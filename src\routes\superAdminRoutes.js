const express = require('express');
const router = express.Router();
const multer = require('multer');
const SuperAdminShopController = require('../controllers/superAdminShopController');
const paymentTransactionController = require('../controllers/superAdmin/paymentTransactionController');
const dashboardController = require('../controllers/superAdmin/dashboardController');
const { authenticate, authorize } = require('../middleware/authMiddleware');
const { validate } = require('../middleware/validationMiddleware');
const { superAdminSchemas, superAdminPaymentSchemas } = require('../validations');
const rateLimit = require('../middleware/rateLimiters');

// Configure Multer for shop logo uploads
const storageConfig = {
  destination: function (req, file, cb) {
    cb(null, 'uploads/shop-logos/');
  },
  filename: function (req, file, cb) {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, 'shop-logo-' + uniqueSuffix + '-' + file.originalname);
  }
};

const upload = multer({ 
  storage: multer.diskStorage(storageConfig),
  fileFilter: (req, file, cb) => {
    // Accept only image files
    const allowedTypes = ['image/jpeg', 'image/png', 'image/jpg', 'image/webp'];
    if (allowedTypes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new Error('Invalid file type. Only JPG, PNG, and WebP files are allowed.'), false);
    }
  },
  limits: {
    fileSize: 5 * 1024 * 1024, // 5MB limit
  }
});

/**
 * SuperAdmin Dashboard Routes
 * Provides aggregated dashboard data for SuperAdmin overview
 */

console.log('[SuperAdminRoutes] Registering dashboard overview route: GET /dashboard/overview');

// GET - Dashboard overview with aggregated statistics
router.get('/dashboard/overview',
  authenticate,
  authorize(['superAdmin']),
  (req, res, next) => {
    console.log('[SuperAdminRoutes] Dashboard overview route accessed by:', req.user?.email || 'unknown');
    next();
  },
  dashboardController.getDashboardOverview
);

/**
 * Simplified SuperAdmin Shop CRUD Routes
 * Clean, straightforward endpoints without complex logic
 * All routes require SuperAdmin authentication
 */

// CREATE - Create new shop (with optional logo upload)
router.post('/shops', 
  authenticate,
  authorize(['superAdmin']),
  upload.single('shopLogo'),
  validate(superAdminSchemas.createShop),
  SuperAdminShopController.createShop
);

// READ - Get all shops with pagination and filtering
router.get('/shops',
  authenticate, 
  authorize(['superAdmin']),
  SuperAdminShopController.getAllShops
);

// READ - Get shop statistics
router.get('/shops/stats',
  authenticate,
  authorize(['superAdmin']),
  SuperAdminShopController.getShopStats
);

// READ - Get shop by ID
router.get('/shops/:shopId',
  authenticate,
  authorize(['superAdmin']),
  SuperAdminShopController.getShopById
);

// UPDATE - Update shop information
router.put('/shops/:shopId',
  authenticate,
  authorize(['superAdmin']),
  validate(superAdminSchemas.updateShop),
  SuperAdminShopController.updateShop
);

// UPDATE - Update shop logo
router.put('/shops/:shopId/logo',
  authenticate,
  authorize(['superAdmin']),
  upload.single('shopLogo'),
  SuperAdminShopController.updateShopLogo
);

// UPDATE - Change shop status (activate/suspend)
router.put('/shops/:shopId/status',
  authenticate,
  authorize(['superAdmin']),
  validate(superAdminSchemas.changeShopStatus),
  SuperAdminShopController.changeShopStatus
);

// DELETE - Delete shop (soft delete)
router.delete('/shops/:shopId',
  authenticate,
  authorize(['superAdmin']),
  validate(superAdminSchemas.deleteShop),
  SuperAdminShopController.deleteShop
);

/**
 * SuperAdmin Payment Transaction Routes
 * Handles subscription payment transaction management
 * All routes require SuperAdmin authentication and rate limiting
 */

// Apply rate limiting to payment transaction routes
router.use('/payment-transactions*', rateLimit.superAdminLimiter);

// GET - Get all payment transactions with filtering and pagination
router.get('/payment-transactions',
  authenticate,
  authorize(['superAdmin']),
  validate(superAdminPaymentSchemas.getPaymentTransactions),
  paymentTransactionController.getAllPaymentTransactions
);

// GET - Get payment transaction statistics
router.get('/payment-transactions/stats',
  authenticate,
  authorize(['superAdmin']),
  validate(superAdminPaymentSchemas.getPaymentStats),
  paymentTransactionController.getPaymentTransactionStats
);

// GET - Export payment transactions
router.get('/payment-transactions/export',
  authenticate,
  authorize(['superAdmin']),
  validate(superAdminPaymentSchemas.exportPaymentTransactions),
  paymentTransactionController.exportPaymentTransactions
);

// POST - Approve payment transaction
router.post('/payment-transactions/:paymentId/approve',
  authenticate,
  authorize(['superAdmin']),
  validate(superAdminPaymentSchemas.approvePaymentTransaction),
  paymentTransactionController.approvePaymentTransaction
);

// POST - Reject payment transaction
router.post('/payment-transactions/:paymentId/reject',
  authenticate,
  authorize(['superAdmin']),
  validate(superAdminPaymentSchemas.rejectPaymentTransaction),
  paymentTransactionController.rejectPaymentTransaction
);

// GET - Get single payment transaction by ID
// This must come after other specific routes to avoid conflicts
router.get('/payment-transactions/:paymentId',
  authenticate,
  authorize(['superAdmin']),
  validate(superAdminPaymentSchemas.getPaymentTransactionById),
  paymentTransactionController.getPaymentTransactionById
);

module.exports = router; 