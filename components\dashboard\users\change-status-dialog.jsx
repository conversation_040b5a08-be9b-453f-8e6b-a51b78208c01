"use client";

import { useState } from 'react';
import { 
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter
} from '@/components/ui/dialog';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import { toast } from 'sonner';
import { useUsers } from '@/contexts/users-context';
import { AlertTriangle, RefreshCw, Loader2, Mail } from 'lucide-react';
import { Checkbox } from '@/components/ui/checkbox';

export function ChangeStatusDialog({ isOpen, onClose, user, action }) {
  const [reason, setReason] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [sendEmail, setSendEmail] = useState(true); // Default to sending email notification
  
  // Use the context instead of creating a new service instance
  const { changeUserStatus, refreshUserList } = useUsers();
  
  // Determine title, description, and confirmation text based on action
  const getDialogProps = () => {
    if (action === 'suspend') {
      return {
        title: 'Suspend User',
        description: 'Suspending this user will prevent them from accessing the system until reactivated.',
        icon: <AlertTriangle className="h-6 w-6 text-amber-500" />,
        confirmText: 'Suspend User',
        confirmColor: 'amber',
        reasonRequired: true
      };
    } else if (action === 'reactivate') {
      return {
        title: 'Reactivate User',
        description: 'This will restore the user\'s access to the system.',
        icon: <RefreshCw className="h-6 w-6 text-green-500" />,
        confirmText: 'Reactivate User',
        confirmColor: 'green',
        reasonRequired: false
      };
    }
    
    return {
      title: 'Change User Status',
      description: 'Update the user\'s status in the system.',
      icon: null,
      confirmText: 'Confirm',
      confirmColor: 'primary',
      reasonRequired: false
    };
  };
  
  const dialogProps = getDialogProps();
  
  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();
    
    // Validate reason if required
    if (dialogProps.reasonRequired && !reason.trim()) {
      toast.error('Please provide a reason for this action');
      return;
    }
    
    try {
      setIsSubmitting(true);
      
      const newStatus = action === 'suspend' ? 'suspended' : 'active';
      
      console.log(`Changing user status to ${newStatus} with email notification: ${sendEmail}`);
      
      // Use the context method instead of service directly - pass the sendEmail parameter
      const updatedUser = await changeUserStatus(user.userId, newStatus, reason, sendEmail);
      
      if (updatedUser) {
        // Status change was successful
        toast.success(`User ${newStatus === 'active' ? 'reactivated' : 'suspended'} successfully`);
        
        // Immediately refresh the user list to update the UI
        await refreshUserList();
        
        // Close dialog
        onClose(true);
        
        // Show confirmation of email notification if applicable
        if (sendEmail) {
          toast.success(
            `Email notification ${newStatus === 'active' ? 'for reactivation' : 'about suspension'} has been sent to the user`, 
            { duration: 4000 }
          );
        }
      } else {
        // Something went wrong but no error was thrown
        toast.error('Failed to update user status. Please try again.');
      }
    } catch (error) {
      console.error('Error changing user status:', error);
      // Toast is already handled by context
    } finally {
      setIsSubmitting(false);
    }
  };
  
  return (
    <Dialog open={isOpen} onOpenChange={(open) => !isSubmitting && onClose(false)}>
      <DialogContent className="sm:max-w-[450px]">
        <DialogHeader>
          <div className="flex items-center gap-3">
            {dialogProps.icon}
            <DialogTitle>{dialogProps.title}</DialogTitle>
          </div>
          <DialogDescription>
            {dialogProps.description}
          </DialogDescription>
        </DialogHeader>
        
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="bg-muted p-3 rounded-md">
            <div className="font-medium">User Information:</div>
            <div className="grid grid-cols-2 gap-2 mt-2 text-sm">
              <div>
                <span className="text-muted-foreground">Name:</span>
                <span className="ml-1 font-medium">{user.fullName}</span>
              </div>
              <div>
                <span className="text-muted-foreground">Role:</span>
                <span className="ml-1 font-medium capitalize">{user.role}</span>
              </div>
              <div>
                <span className="text-muted-foreground">Email:</span>
                <span className="ml-1">{user.email}</span>
              </div>
              <div>
                <span className="text-muted-foreground">Current Status:</span>
                <span className="ml-1 capitalize">{user.status}</span>
              </div>
            </div>
          </div>
          
          {(dialogProps.reasonRequired || action === 'reactivate') && (
            <div className="space-y-2">
              <label htmlFor="reason" className="text-sm font-medium">
                {action === 'suspend' ? 'Reason for suspension' : 'Notes (optional)'}:
              </label>
              <Textarea
                id="reason"
                value={reason}
                onChange={(e) => setReason(e.target.value)}
                placeholder={
                  action === 'suspend' 
                    ? 'Provide a detailed reason for suspending this user...' 
                    : 'Add any notes about this reactivation (optional)...'
                }
                className="min-h-[100px]"
                required={dialogProps.reasonRequired}
              />
              {action === 'suspend' && (
                <p className="text-xs text-muted-foreground">
                  This reason will be stored in the system and may be visible to other administrators.
                </p>
              )}
            </div>
          )}
          
          {/* Email notification option */}
          <div className="flex items-center space-x-2">
            <Checkbox 
              id="send-email" 
              checked={sendEmail} 
              onCheckedChange={setSendEmail}
            />
            <div className="grid gap-1.5 leading-none">
              <label
                htmlFor="send-email"
                className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 flex items-center"
              >
                <Mail className="h-4 w-4 mr-2 text-muted-foreground" />
                Send email notification
              </label>
              <p className="text-xs text-muted-foreground">
                {action === 'suspend' 
                  ? 'Notify user about their account suspension via email'
                  : 'Notify user about their account reactivation via email'}
              </p>
            </div>
          </div>
          
          <DialogFooter className="gap-2 sm:gap-0">
            <Button variant="outline" type="button" onClick={() => onClose(false)} disabled={isSubmitting}>
              Cancel
            </Button>
            <Button 
              type="submit" 
              disabled={isSubmitting}
              variant={dialogProps.confirmColor}
              className={dialogProps.confirmColor === 'amber' ? 'bg-amber-500 hover:bg-amber-600' : 
                        dialogProps.confirmColor === 'green' ? 'bg-green-500 hover:bg-green-600' : ''}
            >
              {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              {dialogProps.confirmText}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
