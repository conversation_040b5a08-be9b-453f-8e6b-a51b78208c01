/**
 * Settings Routes
 * Handles routes related to system settings
 */
const express = require('express');
const router = express.Router();
const settingsController = require('../controllers/settingsController');
const { authenticate, authorize } = require('../middleware/authMiddleware');
const { validate } = require('../middleware/validationMiddleware');
const { settingsSchemas } = require('../validations');

/**
 * Settings Routes
 * Base path: /api/settings
 */

// Public route to get available payment methods
// This can be accessed without authentication for registration
router.get('/payment-methods', settingsController.getPaymentMethods);

// Public route to get superAdmin contact information
// This can be accessed without authentication for mobile app support
router.get('/contact-info', settingsController.getContactInfo);

// Admin-only routes to manage settings
router.get('/', authenticate, authorize(['admin', 'superAdmin']), settingsController.getSettings);

// EVC credentials management routes
// Global credentials require superAdmin, shop credentials can be managed by shop admins
router.get('/evc-credentials', 
  authenticate, 
  authorize(['superAdmin', 'admin']), 
  settingsController.getEVCCredentials
);

router.post('/evc-credentials', 
  authenticate, 
  authorize(['superAdmin', 'admin']), 
  validate(settingsSchemas.evcCredentialsSchema),
  settingsController.setEVCCredentials
);

router.post('/test-evc-credentials', 
  authenticate, 
  authorize(['superAdmin', 'admin']), 
  validate(settingsSchemas.testEVCCredentialsSchema),
  settingsController.testEVCCredentials
);

// Only superAdmin can manage global payment method settings
router.put('/payment-methods', 
  authenticate, 
  authorize(['superAdmin']), 
  validate(settingsSchemas.updatePaymentMethodsSchema),
  settingsController.updatePaymentMethods
);

// Only superAdmin can manage security settings
router.put('/', 
  authenticate, 
  authorize(['superAdmin']), 
  settingsController.updateSecuritySettings
);

// Update a specific setting by key
// Admins can update settings with accessLevel 'admin' for their shop
// SuperAdmins can update any setting
router.patch('/:key', 
  authenticate, 
  authorize(['admin', 'superAdmin']), 
  validate(settingsSchemas.updateSettingByKeySchema), // We will define this schema next
  settingsController.updateSettingByKey
);

module.exports = router;
