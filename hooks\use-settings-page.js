"use client";

/**
 * Custom hook for settings pages
 * Provides simplified access to settings context with additional utilities
 * Optimized with caching and request deduplication
 * 
 * @module hooks/useSettingsPage
 */
import { useState, useCallback, useEffect, useMemo, useRef } from "react";
import { useSettings } from "@/contexts/settings";
import { useAuth } from "@/contexts/auth-context";
import { useRouter } from "next/navigation";
import { toast } from "sonner";

/**
 * Settings cache configuration
 * Uses a shared cache instance for all hook instances
 * This implementation is designed to be eventually replaced by SWR or React Query
 */
const settingsCache = {
  _cache: new Map(),
  _timeouts: new Map(),
  
  fetchWithCache: async (key, fetcher, forceRefresh = false) => {
    if (!forceRefresh && settingsCache._cache.has(key)) {
      return settingsCache._cache.get(key);
    }
    
    const data = await fetcher();
    settingsCache._cache.set(key, data);
    
    // Clear any existing timeout
    if (settingsCache._timeouts.has(key)) {
      clearTimeout(settingsCache._timeouts.get(key));
    }
    
    // Set cache expiry
    settingsCache._timeouts.set(key, setTimeout(() => {
      settingsCache._cache.delete(key);
      settingsCache._timeouts.delete(key);
    }, 5 * 60 * 1000)); // 5 minutes
    
    return data;
  },
  
  invalidate: (key) => {
    settingsCache._cache.delete(key);
    if (settingsCache._timeouts.has(key)) {
      clearTimeout(settingsCache._timeouts.get(key));
      settingsCache._timeouts.delete(key);
    }
  }
};

/**
 * @param {Object} options - Configuration options
 * @param {string} options.category - Settings category (general, users, etc.)
 * @param {string} options.pageTitle - Page title for display and logging
 * @param {boolean} [options.requireSuperAdmin=false] - Whether this page requires superadmin privileges
 * @returns {Object} Settings page state, data, and actions
 */
export function useSettingsPage({
  category = "all",
  pageTitle = "System Settings",
  requireSuperAdmin = true
} = {}) {
  const router = useRouter();
  const [isReady, setIsReady] = useState(false);
  const [error, setError] = useState(null);
  const settings = useSettings();
  const { isSuperAdmin, user } = useAuth();
  
  // Track the latest settings request to avoid stale data issues
  const latestRequestRef = useRef(null);
  
  /**
   * Handles access control and redirects if needed
   * Extracted as a separate function to improve readability
   */
  const checkAccess = useCallback(() => {
    if (requireSuperAdmin && !isSuperAdmin()) {
      toast.error("You don't have permission to access this page");
      router.push("/dashboard");
      return false;
    }
    return true;
  }, [requireSuperAdmin, isSuperAdmin, router]);
  
  /**
   * Fetches settings data for a specific category with caching
   * @param {string} categoryName - Category to fetch
   * @param {boolean} forceRefresh - Whether to bypass cache
   * @returns {Promise} Promise resolving to settings data
   */
  const fetchCategorySettings = useCallback((categoryName, forceRefresh = false) => {
    if (categoryName === "all") {
      return Promise.resolve(null);
    }
    
    // Create a unique request ID to track this specific request
    const requestId = Date.now();
    latestRequestRef.current = requestId;
    
    return settingsCache.fetchWithCache(
      categoryName,
      () => {
        // Only continue if this is still the latest request
        if (latestRequestRef.current !== requestId) {
          return Promise.reject(new Error('Request superseded by newer request'));
        }
        
        return settings.fetchSettings(categoryName)
          .catch(error => {
            // Handle partial failures with retry logic
            console.error(`Error fetching ${categoryName} settings:`, error);
            setError(`Failed to load ${categoryName} settings. ${error.message}`);
            
            // Show error to user for critical categories
            if (['payment', 'user', 'security'].includes(categoryName)) {
              toast.error(`Error loading ${categoryName} settings. Retrying...`);
              
              // Auto-retry once after a short delay for critical settings
              return new Promise(resolve => {
                setTimeout(() => {
                  // Only retry if this is still the latest request
                  if (latestRequestRef.current === requestId) {
                    resolve(settings.fetchSettings(categoryName));
                  } else {
                    resolve(null);
                  }
                }, 2000);
              });
            }
            
            // Re-throw the error for non-critical categories
            throw error;
          });
      },
      forceRefresh
    );
  }, [settings]);
  
  // Fetch settings data on mount and handle access control
  useEffect(() => {
    // Skip if user doesn't have required permissions
    if (!checkAccess()) return;
    
    // No need to fetch for 'all' category
    if (category === "all") {
      setIsReady(true);
      return;
    }
    
    // Handle payment category specially to ensure we have both settings and methods
    if (category === 'payment') {
      // Fetch both payment settings and payment methods in parallel
      Promise.all([
        fetchCategorySettings('payment'),
        settings.fetchPaymentMethods().catch(err => {
          console.error('Failed to fetch payment methods:', err);
          toast.error('Could not load payment methods. Please try again.');
          return null;
        })
      ])
      .then(() => {
        setIsReady(true);
        setError(null);
      })
      .catch(err => {
        console.error('Failed to initialize payment settings:', err);
        setError('Failed to initialize payment settings. Please refresh the page.');
        setIsReady(true); // Still mark as ready so UI can show error state
      });
    } else {
      // Standard fetch for other categories
      fetchCategorySettings(category)
        .then(() => {
          setIsReady(true);
          setError(null);
        })
        .catch(err => {
          console.error(`Failed to initialize ${category} settings:`, err);
          setError(`Failed to initialize ${category} settings. Please refresh the page.`);
          setIsReady(true); // Still mark as ready so UI can show error state
        });
    }
    
    // Cleanup function
    return () => {
      latestRequestRef.current = null;
    };
  }, [category, checkAccess, fetchCategorySettings, settings]);

  /**
   * Get settings for a specific category with memoization
   * @param {string} categoryName - Category to filter by
   * @returns {Array} Settings for the category
   */
  const getSettingsByCategory = useCallback((categoryName) => {
    return settings.settingsByCategory[categoryName] || [];
  }, [settings.settingsByCategory]);
  
  /**
   * Memoized settings by category for current category
   */
  const currentCategorySettings = useMemo(() => {
    return getSettingsByCategory(category);
  }, [getSettingsByCategory, category]);

  /**
   * Get a specific setting by key with memoization
   * @param {string} key - Setting key
   * @returns {Object|null} Setting object or null if not found
   */
  const getSettingByKey = useCallback((key) => {
    return settings.settings.find(setting => setting.key === key) || null;
  }, [settings.settings]);
  
  /**
   * Optimized version that creates a key-based lookup map
   * @returns {Object} Map of setting keys to setting objects
   */
  const settingsMap = useMemo(() => {
    // Only log in development to avoid console spam
    if (process.env.NODE_ENV === 'development') {
      console.debug('[useSettingsPage] Processing settings data');
    }
    
    // Handle different data structures that might come from the API or cache
    // The API might return {success: true, data: Array} or directly Array
    const settingsArray = Array.isArray(settings.settings) 
      ? settings.settings 
      : (settings.settings?.data && Array.isArray(settings.settings.data)) 
        ? settings.settings.data 
        : [];
    
    // Create the mapping of setting keys to values
    return settingsArray.reduce((map, setting) => {
      if (setting && setting.key) {
        map[setting.key] = setting;
      }
      return map;
    }, {});
  }, [settings.settings]);
  
  /**
   * Optimized version of getSettingByKey using the memoized map
   * @param {string} key - Setting key
   * @returns {Object|null} Setting object or null if not found
   */
  const getSettingByKeyFast = useCallback((key) => {
    return settingsMap[key] || null;
  }, [settingsMap]);

  /**
   * Helper to determine if a setting should be editable by current user
   * @param {Object} setting - Setting object
   * @returns {boolean} Whether setting is editable
   */
  const canEditSetting = useCallback((setting) => {
    if (!setting || !setting.editable) return false;
    
    switch (setting.accessLevel) {
      case 'superAdmin': return isSuperAdmin();
      case 'admin': return isSuperAdmin() || user?.role?.toLowerCase() === 'admin';
      case 'all': return true;
      default: return false;
    }
  }, [isSuperAdmin, user]);
  
  // Memoize the current user's permissions for optimization
  const userPermissions = useMemo(() => {
    return {
      isSuperAdmin: isSuperAdmin(),
      isAdmin: user?.role?.toLowerCase() === 'admin',
      role: user?.role
    };
  }, [isSuperAdmin, user]);

  /**
 * Force refetch a specific category
 * Bypasses caching to get fresh data from the server
 * Provides optimistic UI updates with error handling
 * 
 * @param {string} categoryToRefetch - Category to refresh (defaults to current category)
 * @returns {Promise} Promise resolving when refresh completes
 */
const forceRefetch = useCallback((categoryToRefetch = category) => {
  // Set loading state (if needed)
  setIsReady(false);
  
  // For payment category, handle special case with payment methods
  if (categoryToRefetch === 'payment') {
    // Show loading toast for better UX
    const toastId = toast.loading('Refreshing payment settings...');
    
    // Invalidate cache for both settings and methods
    settingsCache.invalidate(categoryToRefetch);
    
    // Fetch both in parallel for efficiency
    return Promise.all([
      fetchCategorySettings(categoryToRefetch, true),
      settings.fetchPaymentMethods().catch(err => {
        console.error('Failed to refresh payment methods:', err);
        return null; // Continue even if this part fails
      })
    ])
    .then(([settingsResult, methodsResult]) => {
      // Success - both parts completed
      toast.success('Payment settings refreshed successfully', { id: toastId });
      setIsReady(true);
      setError(null);
      return { settings: settingsResult, methods: methodsResult };
    })
    .catch(err => {
      // Handle failures with informative messaging
      console.error('Failed to refresh payment settings:', err);
      toast.error('Could not refresh payment settings. Please try again.', { id: toastId });
      setError('Failed to refresh payment settings. Please try again.');
      setIsReady(true); // Still mark as ready so UI can show error state
      throw err; // Re-throw for promise chaining
    });
  }
  
  // Standard case for non-payment categories
  return fetchCategorySettings(categoryToRefetch, true)
    .then(result => {
      setIsReady(true);
      setError(null);
      return result;
    })
    .catch(err => {
      console.error(`Failed to refresh ${categoryToRefetch} settings:`, err);
      toast.error(`Could not refresh ${categoryToRefetch} settings. Please try again.`);
      setError(`Failed to refresh ${categoryToRefetch} settings. Please try again.`);
      setIsReady(true); // Still mark as ready so UI can show error state
      throw err;
    });
}, [category, fetchCategorySettings, settings]);
  
  /**
   * Builds a comprehensive settings state object
   * @returns {Object} Comprehensive settings state
   */
  const buildSettingsState = useCallback(() => {
    // Get active settings and their loading states
    const activeSettings = {
      // Current settings data
      currentCategory: category,
      categorySettings: currentCategorySettings,
      // Loading and error states
      isReady,
      hasError: !!error,
      errorMessage: error,
      // UI helpers
      pageTitle,
      // Access control
      canAccess: checkAccess(),
      userPermissions,
    };
    
    return activeSettings;
  }, [category, checkAccess, currentCategorySettings, error, isReady, pageTitle, userPermissions]);
  
  // Memoize the returned object to prevent unnecessary re-renders
  const hookReturn = useMemo(() => ({
    // State data
    ...settings,
    ...buildSettingsState(),
    
    // Utility functions
    getSettingsByCategory,
    getSettingByKey,
    getSettingByKeyFast, // Optimized version - preferred for performance
    canEditSetting,
    forceRefetch,
    
    // Pre-computed data
    currentCategorySettings,
    settingsMap,
    
    // Access control helpers
    userPermissions,
    isSuperAdmin: userPermissions.isSuperAdmin
  }), [
    settings,
    buildSettingsState,
    getSettingsByCategory,
    getSettingByKey,
    getSettingByKeyFast,
    canEditSetting,
    forceRefetch,
    currentCategorySettings,
    settingsMap,
    userPermissions
  ]);
  
  return hookReturn;
}

/**
 * This is a migration path to eventually move to SWR or React Query
 * When you're ready to migrate, replace implementation with one of these libraries
 * The exported interface should remain the same to minimize impact on consumers
 */
export default useSettingsPage;
