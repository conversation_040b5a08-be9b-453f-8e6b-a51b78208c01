/**
 * Fetch Settings Service
 * Matches backend: settingsController.getSettings
 * GET /api/settings
 */

import { getSettings } from '../../api/modules/settings';
import { handleError, logApiCall } from '../baseService';

/**
 * Fetch all system settings
 * @param {string} [category='all'] - Optional category filter ('all', 'system', 'notification', 'payment', 'security', 'ml')
 * @returns {Promise<Object>} Settings data
 */
const fetchSettings = async (category = 'all') => {
  const context = `fetchSettings(category: ${category})`;
  
  try {
    logApiCall(context, 'GET /api/settings', { category });
    
    const response = await getSettings(category);
    
    if (response?.success) {
      console.log(`[${context}] Success:`, {
        settingsCount: response.data?.length || 0,
        category
      });
      
      return {
        success: true,
        data: response.data || []
      };
    } else {
      console.warn(`[${context}] API returned success=false:`, response);
      return {
        success: false,
        data: [],
        message: response?.message || 'Failed to fetch settings'
      };
    }
    
  } catch (error) {
    console.error(`[${context}] Error:`, error);
    handleError(error, context, false);
    
    return {
      success: false,
      data: [],
      message: error.message || 'Failed to fetch settings',
      error: error
    };
  }
};

export default fetchSettings;
