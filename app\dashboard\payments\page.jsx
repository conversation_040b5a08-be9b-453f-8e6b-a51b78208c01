"use client";

import { useState, useEffect } from "react";
import { useAuth } from "@/contexts/auth-context";
import { useRouter } from "next/navigation";
import { ResponsiveContainer } from "@/components/layout/responsive-container";
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { toast } from "sonner";
import { Wallet, Search, X } from "lucide-react";

// Import refactored components
import { PaymentCard } from "@/components/dashboard/payments/payment-card";
import { VerificationDialog } from "@/components/dashboard/payments/verification-dialog";
import { ProofPreview } from "@/components/dashboard/payments/proof-preview";
import { PaymentService } from "@/components/dashboard/payments/payment-service";

export default function PaymentsPage() {
  const [activeTab, setActiveTab] = useState("pending");
  const [payments, setPayments] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");
  const [imagePreviewUrl, setImagePreviewUrl] = useState(null);
  const [showPreview, setShowPreview] = useState(false);
  const [currentPayment, setCurrentPayment] = useState(null);
  const [verificationDialog, setVerificationDialog] = useState(false);
  const [verificationAction, setVerificationAction] = useState("approve");

  const { user, isSuperAdmin } = useAuth();
  const router = useRouter();

  // Redirect if not a superadmin
  useEffect(() => {
    if (!isSuperAdmin) {
      router.push("/unauthorized");
    }
  }, [isSuperAdmin, router]);

  // Fetch payments
  useEffect(() => {
    const fetchPayments = async () => {
      try {
        setLoading(true);
        const response = await PaymentService.fetchOfflinePayments();
        setPayments(response.data);
      } catch (error) {
        console.error("Error fetching payments:", error);
        toast.error("Failed to load payment data");
      } finally {
        setLoading(false);
      }
    };

    if (isSuperAdmin) {
      fetchPayments();
    }
  }, [isSuperAdmin]);

  // Filter payments based on active tab and search query
  const filteredPayments = payments.filter((payment) => {
    // Filter by status based on active tab
    if (activeTab === "pending" && payment.status !== "pending") {
      return false;
    }
    if (activeTab === "verified" && payment.status !== "verified") {
      return false;
    }
    if (activeTab === "rejected" && payment.status !== "rejected") {
      return false;
    }

    // Filter by search query (shop name, payment ID, etc.)
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      return (
        payment.shopName?.toLowerCase().includes(query) ||
        payment.paymentId?.toLowerCase().includes(query) ||
        payment.subscriptionId?.toLowerCase().includes(query) ||
        payment.amount?.toString().includes(query)
      );
    }

    return true;
  });

  // Handle payment verification
  const handleVerifyPayment = async (note) => {
    if (!currentPayment) return;

    try {
      setLoading(true);
      
      // Call the API endpoint for verification
      await PaymentService.verifyPayment(
        currentPayment.paymentId, 
        verificationAction, 
        note
      );

      // Update the local payments data
      setPayments(
        payments.map((payment) =>
          payment.paymentId === currentPayment.paymentId
            ? { ...payment, status: verificationAction, verificationNote: note }
            : payment
        )
      );

      toast.success(
        verificationAction === "approve"
          ? "Payment verified successfully"
          : "Payment rejected successfully"
      );

      // Close the dialog
      setVerificationDialog(false);
      setCurrentPayment(null);
    } catch (error) {
      console.error("Error verifying payment:", error);
      toast.error(
        verificationAction === "approve"
          ? "Failed to verify payment"
          : "Failed to reject payment"
      );
    } finally {
      setLoading(false);
    }
  };

  // Open the verification dialog
  const openVerificationDialog = (payment, action) => {
    setCurrentPayment(payment);
    setVerificationAction(action);
    setVerificationDialog(true);
  };

  // View payment proof
  const viewPaymentProof = async (payment) => {
    try {
      // Get the payment proof image
      const blob = await PaymentService.getPaymentProof(payment.proofFileId);
      
      // Create a URL for the image blob
      const imageUrl = URL.createObjectURL(blob);
      setImagePreviewUrl(imageUrl);
      setShowPreview(true);
      setCurrentPayment(payment);
    } catch (error) {
      console.error("Error fetching payment proof:", error);
      toast.error("Failed to load payment proof");
    }
  };

  // Download payment proof
  const downloadPaymentProof = async (payment) => {
    try {
      // Get the payment proof image
      const blob = await PaymentService.getPaymentProof(payment.proofFileId);
      
      // Create a download link
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', `payment-proof-${payment.paymentId}.jpg`);
      document.body.appendChild(link);
      link.click();
      
      // Clean up
      link.parentNode.removeChild(link);
      window.URL.revokeObjectURL(url);
      
      toast.success("Payment proof downloaded successfully");
    } catch (error) {
      console.error("Error downloading payment proof:", error);
      toast.error("Failed to download payment proof");
    }
  };

  if (!isSuperAdmin) {
    return null; // Don't render anything while redirecting
  }

  return (
    <ResponsiveContainer>
      <div className="flex flex-col gap-6 pb-8">
        <div>
          <h1 className="text-2xl md:text-3xl font-bold tracking-tight mb-1">
            Payment Verification
          </h1>
          <p className="text-muted-foreground">
            Verify offline subscription payments submitted by shops
          </p>
        </div>

        {/* Filters and Search */}
        <div className="flex flex-col sm:flex-row gap-4 justify-between">
          <div className="relative max-w-md">
            <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
            <Input
              placeholder="Search by shop, payment ID..."
              className="pl-10 pr-10"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
            {searchQuery && (
              <X
                className="absolute right-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground cursor-pointer"
                onClick={() => setSearchQuery("")}
              />
            )}
          </div>
        </div>

        {/* Payment Tabs */}
        <Tabs defaultValue="pending" value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-3 mb-6">
            <TabsTrigger value="pending" className="flex items-center gap-2 py-3">
              <span className="hidden sm:inline">Pending</span>
              <span className="sm:hidden">Pending</span>
              <Badge variant="secondary" className="ml-1">
                {payments.filter(p => p.status === "pending").length}
              </Badge>
            </TabsTrigger>
            <TabsTrigger value="verified" className="py-3">
              <span className="hidden sm:inline">Verified</span>
              <span className="sm:hidden">Verified</span>
              <Badge variant="secondary" className="ml-1">
                {payments.filter(p => p.status === "verified").length}
              </Badge>
            </TabsTrigger>
            <TabsTrigger value="rejected" className="py-3">
              <span className="hidden sm:inline">Rejected</span>
              <span className="sm:hidden">Rejected</span>
              <Badge variant="secondary" className="ml-1">
                {payments.filter(p => p.status === "rejected").length}
              </Badge>
            </TabsTrigger>
          </TabsList>

          {/* Loading State */}
          {loading ? (
            <div className="flex justify-center py-12">
              <div className="flex flex-col items-center gap-2">
                <div className="h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent"></div>
                <p className="text-sm text-muted-foreground">Loading payments...</p>
              </div>
            </div>
          ) : (
            <div>
              {/* No Results State */}
              {filteredPayments.length === 0 && (
                <div className="flex flex-col items-center justify-center py-12 text-center">
                  <div className="rounded-full bg-muted p-4 mb-4">
                    <Wallet className="h-8 w-8 text-muted-foreground" />
                  </div>
                  <h3 className="text-lg font-medium">No payments found</h3>
                  <p className="text-sm text-muted-foreground mt-2 max-w-md px-4">
                    {searchQuery
                      ? "No payments match your search criteria. Try a different search."
                      : activeTab === "pending"
                      ? "There are no pending payments that require verification."
                      : activeTab === "verified"
                      ? "No payments have been verified yet."
                      : "No payments have been rejected yet."}
                  </p>
                </div>
              )}

              {/* Payment List */}
              <div className="space-y-6">
                {filteredPayments.map((payment) => (
                  <PaymentCard
                    key={payment.paymentId}
                    payment={payment}
                    formatCurrency={PaymentService.formatCurrency}
                    formatDate={PaymentService.formatDate}
                    onViewProof={viewPaymentProof}
                    onDownload={downloadPaymentProof}
                    onVerify={(payment) => openVerificationDialog(payment, "approve")}
                    onReject={(payment) => openVerificationDialog(payment, "reject")}
                  />
                ))}
              </div>
            </div>
          )}
        </Tabs>
      </div>

      {/* Verification Dialog */}
      <VerificationDialog
        open={verificationDialog}
        onOpenChange={setVerificationDialog}
        action={verificationAction}
        onConfirm={handleVerifyPayment}
      />

      {/* Image Preview */}
      <ProofPreview
        open={showPreview}
        onOpenChange={(open) => {
          setShowPreview(open);
          if (!open && imagePreviewUrl) {
            URL.revokeObjectURL(imagePreviewUrl);
            setImagePreviewUrl(null);
          }
        }}
        imageUrl={imagePreviewUrl}
        payment={currentPayment}
        onVerify={(payment) => {
          setShowPreview(false);
          openVerificationDialog(payment, "approve");
        }}
        onReject={(payment) => {
          setShowPreview(false);
          openVerificationDialog(payment, "reject");
        }}
        onDownload={downloadPaymentProof}
      />
    </ResponsiveContainer>
  );
}
