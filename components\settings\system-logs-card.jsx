"use client";

import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON>er, CardContent, CardTitle, CardDescription, CardFooter } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { AlertTriangle, Clock, Search, Download, RefreshCw, Shield, UserCog, Database, CreditCard, AlertCircle } from "lucide-react";

// Log type icons
const LOG_TYPE_ICONS = {
  auth: <Shield className="h-4 w-4" />,
  user: <UserCog className="h-4 w-4" />,
  system: <Database className="h-4 w-4" />,
  payment: <CreditCard className="h-4 w-4" />,
  error: <AlertCircle className="h-4 w-4" />
};

// Log level badges
const LOG_LEVEL_BADGES = {
  info: "bg-blue-100 text-blue-800",
  warning: "bg-yellow-100 text-yellow-800",
  error: "bg-red-100 text-red-800",
  critical: "bg-red-600 text-white",
  debug: "bg-gray-100 text-gray-800"
};

/**
 * System Logs Card Component
 * Displays filterable system logs for SuperAdmin
 */
export function SystemLogsCard({ 
  logs = [],
  fetchLogs,
  exportLogs,
  loading,
  totalCount = 0
}) {
  const [filters, setFilters] = useState({
    type: "all",
    level: "all",
    startDate: "",
    endDate: "",
    search: "",
    page: 1,
    limit: 50
  });

  // Handle filter changes
  const handleFilterChange = (field, value) => {
    setFilters(prev => ({
      ...prev,
      [field]: value,
      // Reset to page 1 when changing filters
      page: field === 'page' ? value : 1
    }));
  };

  // Apply filters and fetch logs
  const handleApplyFilters = () => {
    fetchLogs(filters);
  };

  // Reset filters
  const handleResetFilters = () => {
    setFilters({
      type: "all",
      level: "all",
      startDate: "",
      endDate: "",
      search: "",
      page: 1,
      limit: 50
    });
    fetchLogs({
      type: "all",
      level: "all",
      page: 1,
      limit: 50
    });
  };

  // Export logs with current filters
  const handleExportLogs = () => {
    exportLogs(filters);
  };

  // Format date for display
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'short',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: false
    }).format(date);
  };

  // Get total pages
  const totalPages = Math.ceil(totalCount / filters.limit);

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span className="flex items-center">
            <Clock className="mr-2 h-5 w-5" />
            System Logs
          </span>
          <div className="flex items-center space-x-2">
            <Button 
              variant="outline" 
              size="sm"
              onClick={handleResetFilters}
              disabled={loading}
            >
              Reset
            </Button>
            <Button 
              variant="outline" 
              size="sm"
              onClick={handleExportLogs}
              disabled={loading}
            >
              <Download className="mr-1 h-4 w-4" />
              Export
            </Button>
          </div>
        </CardTitle>
        <CardDescription>
          View and filter system activity logs
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Filters */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="space-y-2">
            <label className="text-sm font-medium">Log Type</label>
            <Select 
              value={filters.type} 
              onValueChange={(value) => handleFilterChange('type', value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="All Types" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Types</SelectItem>
                <SelectItem value="auth">Authentication</SelectItem>
                <SelectItem value="user">User Activity</SelectItem>
                <SelectItem value="system">System</SelectItem>
                <SelectItem value="payment">Payment</SelectItem>
                <SelectItem value="error">Errors</SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          <div className="space-y-2">
            <label className="text-sm font-medium">Log Level</label>
            <Select 
              value={filters.level} 
              onValueChange={(value) => handleFilterChange('level', value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="All Levels" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Levels</SelectItem>
                <SelectItem value="info">Info</SelectItem>
                <SelectItem value="warning">Warning</SelectItem>
                <SelectItem value="error">Error</SelectItem>
                <SelectItem value="critical">Critical</SelectItem>
                <SelectItem value="debug">Debug</SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          <div className="space-y-2">
            <label className="text-sm font-medium">Start Date</label>
            <Input
              type="date"
              value={filters.startDate}
              onChange={(e) => handleFilterChange('startDate', e.target.value)}
            />
          </div>
          
          <div className="space-y-2">
            <label className="text-sm font-medium">End Date</label>
            <Input
              type="date"
              value={filters.endDate}
              onChange={(e) => handleFilterChange('endDate', e.target.value)}
            />
          </div>
        </div>
        
        <div className="flex space-x-2">
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search logs..."
                className="pl-8"
                value={filters.search}
                onChange={(e) => handleFilterChange('search', e.target.value)}
              />
            </div>
          </div>
          <Button 
            onClick={handleApplyFilters}
            disabled={loading}
          >
            {loading ? (
              <>
                <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                Loading...
              </>
            ) : (
              "Apply Filters"
            )}
          </Button>
        </div>
        
        {/* System Logs Table */}
        <div className="border rounded-md">
          <div className="min-w-full divide-y divide-gray-200">
            <div className="bg-gray-50">
              <div className="grid grid-cols-12 gap-2 px-4 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider">
                <div className="col-span-1">Type</div>
                <div className="col-span-1">Level</div>
                <div className="col-span-2">Timestamp</div>
                <div className="col-span-2">User/Source</div>
                <div className="col-span-6">Message</div>
              </div>
            </div>
            
            <div className="bg-white divide-y divide-gray-200">
              {logs.length > 0 ? (
                logs.map((log, index) => (
                  <div key={log._id || index} className="grid grid-cols-12 gap-2 px-4 py-3 text-sm">
                    <div className="col-span-1 flex items-center">
                      {LOG_TYPE_ICONS[log.type] || <Database className="h-4 w-4" />}
                      <span className="ml-1">{log.type}</span>
                    </div>
                    <div className="col-span-1">
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${LOG_LEVEL_BADGES[log.level] || 'bg-gray-100'}`}>
                        {log.level}
                      </span>
                    </div>
                    <div className="col-span-2 text-gray-500">
                      {formatDate(log.timestamp)}
                    </div>
                    <div className="col-span-2 truncate" title={log.user || log.source}>
                      {log.user || log.source || 'System'}
                    </div>
                    <div className="col-span-6 break-words">
                      {log.message}
                      {log.details && (
                        <button 
                          className="ml-2 text-blue-600 hover:text-blue-800 text-xs"
                          onClick={() => window.alert(JSON.stringify(log.details, null, 2))}
                        >
                          [Details]
                        </button>
                      )}
                    </div>
                  </div>
                ))
              ) : (
                <div className="px-4 py-8 text-center text-gray-500">
                  {loading ? (
                    <div className="flex justify-center items-center">
                      <RefreshCw className="animate-spin h-5 w-5 mr-2" />
                      Loading logs...
                    </div>
                  ) : (
                    <div className="flex flex-col items-center">
                      <AlertTriangle className="h-8 w-8 text-yellow-500 mb-2" />
                      <p>No logs found matching your filters.</p>
                      <p className="text-sm mt-1">Try adjusting your search criteria.</p>
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>
        
        {/* Pagination */}
        {totalPages > 1 && (
          <div className="flex items-center justify-between">
            <div className="text-sm text-gray-500">
              Showing {(filters.page - 1) * filters.limit + 1}-
              {Math.min(filters.page * filters.limit, totalCount)} of {totalCount} logs
            </div>
            <div className="flex space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleFilterChange('page', Math.max(1, filters.page - 1))}
                disabled={filters.page <= 1 || loading}
              >
                Previous
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleFilterChange('page', Math.min(totalPages, filters.page + 1))}
                disabled={filters.page >= totalPages || loading}
              >
                Next
              </Button>
            </div>
          </div>
        )}
        
        {/* Security Info */}
        <div className="p-4 bg-blue-50 rounded-md flex items-start space-x-3 text-blue-800">
          <Shield className="h-5 w-5 mt-0.5 text-blue-500" />
          <div>
            <p className="font-medium">Security Information</p>
            <p className="text-sm">
              System logs contain sensitive security information and are only accessible to SuperAdmin users.
              Logs are retained according to the system's data retention policy.
            </p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
