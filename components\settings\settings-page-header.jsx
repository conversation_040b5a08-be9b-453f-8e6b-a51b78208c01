"use client";

import { But<PERSON> } from "@/components/ui/button";
import { useRouter } from "next/navigation";
import { memo, useCallback } from "react";

/**
 * Settings Page Header Component
 * Reusable header for all settings pages
 * Memoized to prevent unnecessary re-renders
 */
function SettingsPageHeaderComponent({ 
  title, 
  description, 
  showBackButton = true 
}) {
  const router = useRouter();
  
  // Memoize the navigation callback
  const handleBackClick = useCallback(() => {
    router.push("/dashboard/settings");
  }, [router]);
  
  return (
    <div className="flex items-center justify-between mb-8">
      <div>
        <h1 className="text-3xl font-bold mb-2">{title}</h1>
        {description && (
          <p className="text-muted-foreground">{description}</p>
        )}
      </div>
      {showBackButton && (
        <Button 
          variant="outline" 
          onClick={handleBackClick}
        >
          Back to Settings
        </Button>
      )}
    </div>
  );
}

// Export a memoized version of the component
export const SettingsPageHeader = memo(SettingsPageHeaderComponent);
