const User = require('../models/user.model');
const { logInfo, logError } = require('../utils');
const { validationResult } = require('express-validator');

/**
 * FCM Token Controller
 * Handles FCM token registration and management for mobile app users
 */
class FCMTokenController {

  /**
   * Register or update FCM token for authenticated user
   * POST /api/fcm/register
   */
  static async registerToken(req, res) {
    try {
      // Validate request
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: errors.array()
        });
      }

      const { token, deviceInfo } = req.body;
      const userId = req.user.userId;

      logInfo(`Registering FCM token for user ${userId}`, 'FCMTokenController');

      // Find user
      const user = await User.findOne({ userId, isDeleted: false });
      if (!user) {
        return res.status(404).json({
          success: false,
          message: 'User not found'
        });
      }

      // 🔍 DEBUG: Log complete user data to diagnose query mismatch
      logInfo(`DEBUG - FCM Registration User Data:`, 'FCMTokenController');
      logInfo(`  userId: ${user.userId}`, 'FCMTokenController');
      logInfo(`  role: ${user.role}`, 'FCMTokenController');
      logInfo(`  shopId: ${user.shopId}`, 'FCMTokenController');
      logInfo(`  isActive: ${user.isActive}`, 'FCMTokenController');
      logInfo(`  isDeleted: ${user.isDeleted}`, 'FCMTokenController');
      logInfo(`  existing FCM tokens: ${user.fcmTokens ? user.fcmTokens.length : 0}`, 'FCMTokenController');

      // Only allow admin roles to register tokens
      if (!['superAdmin', 'admin', 'employee'].includes(user.role)) {
        return res.status(403).json({
          success: false,
          message: 'Only admin users can register for push notifications'
        });
      }

      // Add or update FCM token
      await user.addFCMToken({
        token: token,
        deviceInfo: deviceInfo || {}
      });

      logInfo(`FCM token registered successfully for user ${userId}`, 'FCMTokenController');

      res.status(200).json({
        success: true,
        message: 'FCM token registered successfully',
        data: {
          userId: user.userId,
          tokenCount: user.fcmTokens.length
        }
      });
    } catch (error) {
      logError(`Failed to register FCM token: ${error.message}`, 'FCMTokenController', error);
      res.status(500).json({
        success: false,
        message: 'Failed to register FCM token',
        error: error.message
      });
    }
  }

  /**
   * Remove FCM token for authenticated user
   * DELETE /api/fcm/unregister
   */
  static async unregisterToken(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: errors.array()
        });
      }

      const { token } = req.body;
      const userId = req.user.userId;

      logInfo(`Unregistering FCM token for user ${userId}`, 'FCMTokenController');

      // Find user
      const user = await User.findOne({ userId, isDeleted: false });
      if (!user) {
        return res.status(404).json({
          success: false,
          message: 'User not found'
        });
      }

      // Remove FCM token
      await user.removeFCMToken(token);

      logInfo(`FCM token unregistered successfully for user ${userId}`, 'FCMTokenController');

      res.status(200).json({
        success: true,
        message: 'FCM token unregistered successfully',
        data: {
          userId: user.userId,
          tokenCount: user.fcmTokens.length
        }
      });
    } catch (error) {
      logError(`Failed to unregister FCM token: ${error.message}`, 'FCMTokenController', error);
      res.status(500).json({
        success: false,
        message: 'Failed to unregister FCM token',
        error: error.message
      });
    }
  }

  /**
   * Update FCM token usage (called when notification is received)
   * PUT /api/fcm/usage
   */
  static async updateTokenUsage(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: errors.array()
        });
      }

      const { token } = req.body;
      const userId = req.user.userId;

      // Find user and update token usage
      const user = await User.findOne({ userId, isDeleted: false });
      if (!user) {
        return res.status(404).json({
          success: false,
          message: 'User not found'
        });
      }

      await user.updateFCMTokenUsage(token);

      res.status(200).json({
        success: true,
        message: 'Token usage updated successfully'
      });
    } catch (error) {
      logError(`Failed to update token usage: ${error.message}`, 'FCMTokenController', error);
      res.status(500).json({
        success: false,
        message: 'Failed to update token usage',
        error: error.message
      });
    }
  }

  /**
   * Get user's FCM tokens
   * GET /api/fcm/tokens
   */
  static async getUserTokens(req, res) {
    try {
      const userId = req.user.userId;

      const user = await User.findOne({ userId, isDeleted: false })
        .select('fcmTokens');

      if (!user) {
        return res.status(404).json({
          success: false,
          message: 'User not found'
        });
      }

      // Return tokens without sensitive token values
      const tokenInfo = user.fcmTokens.map(t => ({
        deviceInfo: t.deviceInfo,
        registeredAt: t.registeredAt,
        lastUsed: t.lastUsed,
        isActive: t.lastUsed > new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
      }));

      res.status(200).json({
        success: true,
        message: 'FCM tokens retrieved successfully',
        data: {
          totalTokens: tokenInfo.length,
          activeTokens: tokenInfo.filter(t => t.isActive).length,
          tokens: tokenInfo
        }
      });
    } catch (error) {
      logError(`Failed to get user tokens: ${error.message}`, 'FCMTokenController', error);
      res.status(500).json({
        success: false,
        message: 'Failed to retrieve FCM tokens',
        error: error.message
      });
    }
  }

  /**
   * Test push notification to user's devices
   * POST /api/fcm/test
   */
  static async testNotification(req, res) {
    try {
      const userId = req.user.userId;

      const user = await User.findOne({ userId, isDeleted: false });
      if (!user) {
        return res.status(404).json({
          success: false,
          message: 'User not found'
        });
      }

      const activeTokens = user.getActiveFCMTokens();
      if (activeTokens.length === 0) {
        return res.status(400).json({
          success: false,
          message: 'No active FCM tokens found for this user'
        });
      }

      // Send test notification using Firebase service
      const FirebaseService = require('../services/firebaseService');
      const result = await FirebaseService.sendPushNotification({
        recipientTokens: activeTokens,
        title: 'DeynCare Test Notification',
        message: `Hello ${user.fullName}! This is a test notification to verify your push notification setup.`,
        data: {
          type: 'test',
          userId: userId
        }
      });

      if (result.success) {
        res.status(200).json({
          success: true,
          message: 'Test notification sent successfully',
          data: {
            totalSent: result.totalSent,
            totalFailed: result.totalFailed,
            totalRecipients: result.totalRecipients
          }
        });
      } else {
        res.status(500).json({
          success: false,
          message: 'Failed to send test notification',
          error: result.error
        });
      }
    } catch (error) {
      logError(`Failed to send test notification: ${error.message}`, 'FCMTokenController', error);
      res.status(500).json({
        success: false,
        message: 'Failed to send test notification',
        error: error.message
      });
    }
  }

  /**
   * Clean up expired tokens (admin only)
   * DELETE /api/fcm/cleanup
   */
  static async cleanupExpiredTokens(req, res) {
    try {
      const { days = 30 } = req.query;
      const cutoffDate = new Date(Date.now() - parseInt(days) * 24 * 60 * 60 * 1000);

      // Remove expired tokens from all users
      const result = await User.updateMany(
        {},
        {
          $pull: {
            fcmTokens: {
              lastUsed: { $lt: cutoffDate }
            }
          }
        }
      );

      logInfo(`Cleaned up expired FCM tokens older than ${days} days`, 'FCMTokenController');

      res.status(200).json({
        success: true,
        message: `Cleaned up expired FCM tokens older than ${days} days`,
        data: {
          modifiedUsers: result.modifiedCount
        }
      });
    } catch (error) {
      logError(`Failed to cleanup expired tokens: ${error.message}`, 'FCMTokenController', error);
      res.status(500).json({
        success: false,
        message: 'Failed to cleanup expired tokens',
        error: error.message
      });
    }
  }
}

module.exports = FCMTokenController; 