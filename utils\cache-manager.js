/**
 * Cache Manager Utility
 * 
 * A flexible cache management system that provides:
 * - TTL-based caching with category-specific overrides
 * - Request deduplication via pending request tracking
 * - Fine-grained cache invalidation
 * - Comprehensive logging for debugging
 * 
 * This is designed as a bridge solution until the app can be migrated to
 * more robust data fetching libraries like SWR or React Query.
 *
 * @module utils/cache-manager
 */

/**
 * Creates a new cache manager instance with configurable options
 * @param {Object} options - Configuration options
 * @param {number} options.defaultTTL - Default time-to-live in milliseconds
 * @param {Object} options.ttlOverrides - Category-specific TTL overrides
 * @param {boolean} options.enableLogging - Whether to enable detailed logging
 * @param {string} options.name - Name for this cache instance (for logging)
 * @returns {Object} Cache manager instance
 */
export function createCacheManager({
  defaultTTL = 5 * 60 * 1000, // 5 minutes default
  ttlOverrides = {},
  enableLogging = true,
  name = 'cache'
} = {}) {
  // Internal cache storage
  const cache = {
    // Track which keys have been requested
    requested: new Set(),
    // Track in-flight requests to deduplicate concurrent calls
    pending: new Map(),
    // Store timestamps for TTL calculation
    timestamps: {},
    // Store actual cached data
    data: {}
  };

  /**
   * Logs a message if logging is enabled
   * @param {string} message - Log message
   * @param {*} [data] - Optional data to log
   */
  const log = (message, data) => {
    if (!enableLogging) return;
    
    if (data !== undefined) {
      console.log(`[${name}] ${message}`, data);
    } else {
      console.log(`[${name}] ${message}`);
    }
  };

  /**
   * Determines if the cache for a key is still valid
   * @param {string} key - Cache key
   * @returns {boolean} Whether the cache entry is valid
   */
  const isValid = (key) => {
    if (!cache.timestamps[key]) return false;
    
    const now = Date.now();
    const timestamp = cache.timestamps[key];
    const ttl = ttlOverrides[key] || defaultTTL;
    const age = now - timestamp;
    const valid = age < ttl;
    
    log(`Cache entry for '${key}' age: ${age}ms, TTL: ${ttl}ms, valid: ${valid}`);
    return valid;
  };

  /**
   * Gets a value from the cache
   * @param {string} key - Cache key
   * @returns {*} Cached value or undefined if not in cache
   */
  const get = (key) => {
    if (!cache.requested.has(key) || !isValid(key)) {
      log(`Cache miss for '${key}'`);
      return undefined;
    }
    
    log(`Cache hit for '${key}'`);
    return cache.data[key];
  };

  /**
   * Sets a value in the cache
   * @param {string} key - Cache key
   * @param {*} value - Value to cache
   */
  const set = (key, value) => {
    cache.requested.add(key);
    cache.data[key] = value;
    cache.timestamps[key] = Date.now();
    log(`Set cache for '${key}'`, value);
  };

  /**
   * Checks if a request is already pending for this key
   * @param {string} key - Cache key
   * @returns {boolean} Whether a request is pending
   */
  const hasPending = (key) => cache.pending.has(key);

  /**
   * Gets a pending request promise for a key
   * @param {string} key - Cache key
   * @returns {Promise|null} Pending promise or null
   */
  const getPending = (key) => cache.pending.get(key) || null;

  /**
   * Sets a pending request promise for a key
   * @param {string} key - Cache key
   * @param {Promise} promise - Promise to track
   */
  const setPending = (key, promise) => {
    cache.pending.set(key, promise);
    log(`Set pending request for '${key}'`);
    
    // Auto-cleanup when promise resolves/rejects
    promise.finally(() => {
      if (cache.pending.get(key) === promise) {
        cache.pending.delete(key);
        log(`Cleared pending request for '${key}'`);
      }
    });
  };

  /**
   * Checks if the cache should be bypassed based on validation and forced refresh
   * @param {string} key - Cache key
   * @param {boolean} forceRefresh - Whether to force refresh
   * @returns {boolean} Whether to bypass cache
   */
  const shouldBypassCache = (key, forceRefresh = false) => {
    if (forceRefresh) {
      log(`Force refreshing '${key}'`);
      return true;
    }
    
    if (!cache.requested.has(key)) {
      log(`First request for '${key}'`);
      return true;
    }
    
    if (!isValid(key)) {
      log(`Cache expired for '${key}'`);
      return true;
    }
    
    return false;
  };

  /**
   * Invalidates the cache for a specific key
   * @param {string} key - Cache key to invalidate
   */
  const invalidate = (key) => {
    log(`Invalidating cache for '${key}'`);
    cache.requested.delete(key);
    delete cache.timestamps[key];
    delete cache.data[key];
  };

  /**
   * Invalidates all cache entries
   */
  const invalidateAll = () => {
    log('Invalidating all cache entries');
    cache.requested.clear();
    cache.timestamps = {};
    cache.data = {};
  };

  /**
   * Handles a fetch operation with caching
   * @param {string} key - Cache key
   * @param {Function} fetchFn - Function that returns a promise with data
   * @param {boolean} forceRefresh - Whether to force refresh
   * @returns {Promise} Promise that resolves with data
   */
  const fetchWithCache = async (key, fetchFn, forceRefresh = false) => {
    // Handle forced refresh
    if (forceRefresh) {
      invalidate(key);
    }
    
    // Check for pending request to avoid duplicates
    if (hasPending(key)) {
      log(`Reusing pending request for '${key}'`);
      return getPending(key);
    }
    
    // Check if we can use the cache
    if (!shouldBypassCache(key, forceRefresh)) {
      const cachedData = get(key);
      if (cachedData !== undefined) {
        return Promise.resolve(cachedData);
      }
    }
    
    // Need to fetch fresh data
    log(`Initiating fetch for '${key}'`);
    const fetchPromise = fetchFn().then(result => {
      log(`Fetch successful for '${key}'`);
      set(key, result);
      return result;
    }).catch(error => {
      log(`Fetch failed for '${key}':`, error);
      throw error;
    });
    
    // Track this promise to deduplicate concurrent requests
    setPending(key, fetchPromise);
    
    return fetchPromise;
  };

  // Return the public API
  return {
    get,
    set,
    invalidate,
    invalidateAll,
    fetchWithCache,
    isValid,
    hasPending
  };
}

/**
 * Clear all API caches
 */
export function clearAllApiCaches() {
  if (typeof window !== 'undefined') {
    // Clear localStorage caches
    Object.keys(localStorage).forEach(key => {
      if (key.startsWith('cache_') || key.startsWith('api_cache_')) {
        localStorage.removeItem(key);
        console.log(`[Cache Manager] Cleared cache: ${key}`);
      }
    });

    // Clear sessionStorage caches
    Object.keys(sessionStorage).forEach(key => {
      if (key.startsWith('cache_') || key.startsWith('api_cache_')) {
        sessionStorage.removeItem(key);
        console.log(`[Cache Manager] Cleared session cache: ${key}`);
      }
    });

    console.log('[Cache Manager] All API caches cleared');
  }
}

/**
 * Clear specific cache by key pattern
 */
export function clearCacheByPattern(pattern) {
  if (typeof window !== 'undefined') {
    // Clear localStorage
    Object.keys(localStorage).forEach(key => {
      if (key.includes(pattern)) {
        localStorage.removeItem(key);
        console.log(`[Cache Manager] Cleared cache: ${key}`);
      }
    });

    // Clear sessionStorage  
    Object.keys(sessionStorage).forEach(key => {
      if (key.includes(pattern)) {
        sessionStorage.removeItem(key);
        console.log(`[Cache Manager] Cleared session cache: ${key}`);
      }
    });
  }
}

/**
 * Force fresh API call by clearing related caches
 */
export function forceFreshApiCall(endpoint) {
  clearCacheByPattern(endpoint);
  console.log(`[Cache Manager] Forced fresh API call for: ${endpoint}`);
}
