/**
 * Migration Script: Standardize Subscription IDs
 * 
 * This script migrates all non-standard subscription IDs to the proper SUB### format.
 * It handles:
 * - Timestamp-based IDs: SUB_1753935872730_LU4TT4 → SUB001
 * - Trial format IDs: TRIAL-SHOP035-1753118413083 → SUB002  
 * - Invalid IDs: SUBNaN → SUB003
 * - Updates all references in related collections
 */

const mongoose = require('mongoose');
const { Subscription, Shop, Payment } = require('../models');
const { idGenerator, logInfo, logSuccess, logError, logWarning } = require('../utils');
const { connectDatabase } = require('../config/database');

/**
 * Find all subscriptions with non-standard IDs
 */
async function findNonStandardSubscriptions() {
  try {
    const nonStandardSubs = await Subscription.find({
      subscriptionId: { 
        $not: /^SUB\d{3,}$/ // Not matching SUB### format
      },
      isDeleted: { $ne: true }
    }).lean();

    logInfo(`Found ${nonStandardSubs.length} subscriptions with non-standard IDs`, 'SubscriptionMigration');
    
    // Group by ID format for reporting
    const formatGroups = {
      timestamp: [],
      trial: [],
      invalid: [],
      other: []
    };

    nonStandardSubs.forEach(sub => {
      const id = sub.subscriptionId;
      if (id.startsWith('SUB_') && id.includes('_')) {
        formatGroups.timestamp.push(sub);
      } else if (id.startsWith('TRIAL-')) {
        formatGroups.trial.push(sub);
      } else if (id === 'SUBNaN' || id.includes('NaN')) {
        formatGroups.invalid.push(sub);
      } else {
        formatGroups.other.push(sub);
      }
    });

    logInfo(`Breakdown: Timestamp(${formatGroups.timestamp.length}), Trial(${formatGroups.trial.length}), Invalid(${formatGroups.invalid.length}), Other(${formatGroups.other.length})`, 'SubscriptionMigration');
    
    return nonStandardSubs;
  } catch (error) {
    logError('Failed to find non-standard subscriptions', 'SubscriptionMigration', error);
    throw error;
  }
}

/**
 * Update all references to old subscription ID
 */
async function updateReferences(oldId, newId, session) {
  const updates = [];
  
  try {
    // Update Shop references
    const shopUpdate = await Shop.updateMany(
      { currentSubscriptionId: oldId },
      { currentSubscriptionId: newId },
      { session }
    );
    if (shopUpdate.modifiedCount > 0) {
      updates.push(`Updated ${shopUpdate.modifiedCount} shop references`);
    }

    // Update Payment references
    const paymentUpdate = await Payment.updateMany(
      { subscriptionId: oldId },
      { subscriptionId: newId },
      { session }
    );
    if (paymentUpdate.modifiedCount > 0) {
      updates.push(`Updated ${paymentUpdate.modifiedCount} payment references`);
    }

    // Check for any other collections that might reference subscription IDs
    // Add more collections here as needed
    
    return updates;
  } catch (error) {
    logError(`Failed to update references for ${oldId} → ${newId}`, 'SubscriptionMigration', error);
    throw error;
  }
}

/**
 * Migrate a single subscription ID
 */
async function migrateSingleSubscription(subscription, session) {
  try {
    const oldId = subscription.subscriptionId;
    
    // Generate new standard ID
    const newId = await idGenerator.generateSubscriptionId(Subscription);
    
    // Update the subscription itself
    await Subscription.updateOne(
      { _id: subscription._id },
      { 
        subscriptionId: newId,
        $push: {
          history: {
            action: 'id_migration',
            date: new Date(),
            performedBy: 'system',
            details: {
              oldId: oldId,
              newId: newId,
              migrationVersion: '1.0',
              reason: 'Standardize subscription ID format'
            }
          }
        }
      },
      { session }
    );

    // Update all references
    const referenceUpdates = await updateReferences(oldId, newId, session);
    
    logSuccess(`Migrated ${oldId} → ${newId}`, 'SubscriptionMigration');
    if (referenceUpdates.length > 0) {
      logInfo(`Reference updates: ${referenceUpdates.join(', ')}`, 'SubscriptionMigration');
    }
    
    return {
      oldId,
      newId,
      referenceUpdates: referenceUpdates.length
    };
  } catch (error) {
    logError(`Failed to migrate subscription ${subscription.subscriptionId}`, 'SubscriptionMigration', error);
    throw error;
  }
}

/**
 * Main migration function
 */
async function runMigration(options = {}) {
  const { dryRun = false, batchSize = 10 } = options;
  
  let session;
  let migrationStats = {
    total: 0,
    success: 0,
    failed: 0,
    referenceUpdates: 0,
    startTime: new Date()
  };

  try {
    logInfo(`Starting subscription ID migration (${dryRun ? 'DRY RUN' : 'LIVE'})`, 'SubscriptionMigration');
    
    // Find all non-standard subscriptions
    const nonStandardSubs = await findNonStandardSubscriptions();
    migrationStats.total = nonStandardSubs.length;
    
    if (nonStandardSubs.length === 0) {
      logSuccess('No non-standard subscription IDs found. Migration not needed.', 'SubscriptionMigration');
      return migrationStats;
    }

    if (dryRun) {
      logWarning('DRY RUN MODE: No changes will be made', 'SubscriptionMigration');
      nonStandardSubs.forEach(sub => {
        logInfo(`Would migrate: ${sub.subscriptionId} (Shop: ${sub.shopId})`, 'SubscriptionMigration');
      });
      return migrationStats;
    }

    // Process in batches with transactions
    for (let i = 0; i < nonStandardSubs.length; i += batchSize) {
      const batch = nonStandardSubs.slice(i, i + batchSize);
      
      session = await mongoose.startSession();
      session.startTransaction();
      
      try {
        logInfo(`Processing batch ${Math.floor(i / batchSize) + 1}/${Math.ceil(nonStandardSubs.length / batchSize)} (${batch.length} subscriptions)`, 'SubscriptionMigration');
        
        for (const subscription of batch) {
          const result = await migrateSingleSubscription(subscription, session);
          migrationStats.success++;
          migrationStats.referenceUpdates += result.referenceUpdates;
        }
        
        await session.commitTransaction();
        logSuccess(`Batch completed successfully`, 'SubscriptionMigration');
        
      } catch (error) {
        await session.abortTransaction();
        migrationStats.failed += batch.length;
        logError(`Batch failed, rolling back ${batch.length} subscriptions`, 'SubscriptionMigration', error);
      } finally {
        await session.endSession();
      }
    }

    migrationStats.endTime = new Date();
    migrationStats.duration = migrationStats.endTime - migrationStats.startTime;
    
    logSuccess(`Migration completed. Success: ${migrationStats.success}, Failed: ${migrationStats.failed}, Reference Updates: ${migrationStats.referenceUpdates}`, 'SubscriptionMigration');
    
    return migrationStats;
    
  } catch (error) {
    if (session) {
      await session.abortTransaction();
      await session.endSession();
    }
    logError('Migration failed', 'SubscriptionMigration', error);
    throw error;
  }
}

/**
 * Verify migration results
 */
async function verifyMigration() {
  try {
    logInfo('Verifying migration results...', 'SubscriptionMigration');
    
    // Check for remaining non-standard IDs
    const remainingNonStandard = await Subscription.find({
      subscriptionId: { $not: /^SUB\d{3,}$/ },
      isDeleted: { $ne: true }
    }).lean();
    
    if (remainingNonStandard.length > 0) {
      logWarning(`Found ${remainingNonStandard.length} remaining non-standard IDs:`, 'SubscriptionMigration');
      remainingNonStandard.forEach(sub => {
        logWarning(`  - ${sub.subscriptionId} (Shop: ${sub.shopId})`, 'SubscriptionMigration');
      });
      return false;
    }
    
    // Check reference integrity
    const shopsWithInvalidRefs = await Shop.find({
      currentSubscriptionId: { $exists: true, $ne: null }
    }).lean();
    
    let brokenReferences = 0;
    for (const shop of shopsWithInvalidRefs) {
      const subscription = await Subscription.findOne({ 
        subscriptionId: shop.currentSubscriptionId 
      }).lean();
      if (!subscription) {
        logWarning(`Shop ${shop.shopId} references non-existent subscription ${shop.currentSubscriptionId}`, 'SubscriptionMigration');
        brokenReferences++;
      }
    }
    
    if (brokenReferences > 0) {
      logWarning(`Found ${brokenReferences} broken subscription references`, 'SubscriptionMigration');
      return false;
    }
    
    logSuccess('Migration verification passed!', 'SubscriptionMigration');
    return true;
    
  } catch (error) {
    logError('Migration verification failed', 'SubscriptionMigration', error);
    return false;
  }
}

/**
 * CLI execution
 */
async function main() {
  try {
    const args = process.argv.slice(2);
    const dryRun = args.includes('--dry-run');
    const verify = args.includes('--verify');
    const batchSize = parseInt(args.find(arg => arg.startsWith('--batch-size='))?.split('=')[1]) || 10;
    
    // Connect to database
    await connectDatabase();
    
    if (verify) {
      const isValid = await verifyMigration();
      process.exit(isValid ? 0 : 1);
    } else {
      const stats = await runMigration({ dryRun, batchSize });
      
      if (stats.failed > 0) {
        logError(`Migration completed with ${stats.failed} failures`, 'SubscriptionMigration');
        process.exit(1);
      } else {
        logSuccess('Migration completed successfully', 'SubscriptionMigration');
        process.exit(0);
      }
    }
    
  } catch (error) {
    logError('Migration script failed', 'SubscriptionMigration', error);
    process.exit(1);
  } finally {
    await mongoose.disconnect();
  }
}

// Export functions for testing
module.exports = {
  findNonStandardSubscriptions,
  migrateSingleSubscription,
  runMigration,
  verifyMigration
};

// Run if called directly
if (require.main === module) {
  main();
}
