/**
 * Create a new pricing plan
 * Allows SuperAdmin to dynamically control features
 * POST /api/plans
 */
const PlanService = require('../../services/PlanService');
const { logInfo, logSuccess, logError } = require('../../utils');

const createPlan = async (req, res, next) => {
  try {
    const planData = req.body;
    logInfo(`Creating new pricing plan: ${planData.name}`, 'PlanController');
    
    // Only apply default features if no features are provided by SuperAdmin
    if (!planData.features) {
      planData.features = {
        debtTracking: true,
        customerPayments: true,
        smsReminders: true,
        smartRiskScore: true,
        businessDashboard: true,
        exportReports: true,
        customerProfiles: true,
        offlineSupport: true
      };
      logInfo('Applied default features (all enabled)', 'PlanController');
    } else {
      logInfo(`Using SuperAdmin defined features: ${Object.keys(planData.features).filter(key => planData.features[key]).join(', ')}`, 'PlanController');
    }
    
    const plan = await PlanService.createPlan(planData, {
      actorId: req.user.userId,
      actorRole: req.user.role
    });
    
    logSuccess(`Created new pricing plan: ${plan.planId}`, 'PlanController');
    
    return res.status(201).json({
      success: true,
      message: 'Plan created successfully',
      data: plan
    });
  } catch (error) {
    logError('Failed to create pricing plan', 'PlanController', error);
    return next(error);
  }
};

module.exports = createPlan;
