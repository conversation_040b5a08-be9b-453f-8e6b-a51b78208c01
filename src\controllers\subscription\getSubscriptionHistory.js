/**
 * Get Subscription History Controller
 * Handles retrieving subscription history for a shop
 */
const { SubscriptionService } = require('../../services');
const { AppError, logError } = require('../../utils');

/**
 * Get subscription history for the current shop
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
const getSubscriptionHistory = async (req, res, next) => {
  try {
    const { shopId } = req.user;
    
    if (!shopId) {
      return next(new AppError('Shop association required', 400, 'shop_required'));
    }
    
    // Get subscription history from service
    const history = await SubscriptionService.getSubscriptionHistory(shopId);
    
    return res.status(200).json({
      success: true,
      message: 'Subscription history retrieved successfully',
      data: {
        history
      }
    });
  } catch (error) {
    logError('Failed to get subscription history', 'SubscriptionController', error);
    return next(error);
  }
};

module.exports = getSubscriptionHistory;
