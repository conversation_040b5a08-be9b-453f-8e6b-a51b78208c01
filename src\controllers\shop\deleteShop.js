const ShopService = require('../../services/shopService');
const { AppError } = require('../../utils');
const { User } = require('../../models');
const ShopEmailService = require('../../services/email/shopEmailService');
const { logInfo, logError } = require('../../utils/logger');

/**
 * Delete shop (HARD DELETE ONLY with cascade)
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 * @returns {Promise<void>}
 */
const deleteShop = async (req, res, next) => {
  try {
    const { shopId } = req.params;
    
    if (!shopId) {
      return next(new AppError('Shop ID is required', 400, 'missing_shop_id'));
    }
    
    // Get reason from request body
    const { reason } = req.body;

    // Only SuperAdmin can delete shops (hard delete only)
    if (req.user?.role !== 'superAdmin') {
      return next(new AppError('Shop deletion requires SuperAdmin privileges', 403, 'insufficient_privileges'));
    }
    
    // Call service with actor information (always hard delete with cascade)
    const result = await ShopService.deleteShop(shopId, {
      actorId: req.user?.userId || 'system',
      actorRole: req.user?.role || 'system',
      reason: reason || 'Deleted by SuperAdmin'
    });
    
    // Send shop deletion notification email
    try {
      // NOTE: In hard delete, users are also deleted, so we can't send email notifications
      // This is expected behavior for hard delete with cascade
      logInfo(`Hard delete completed - no email notification sent as users were also deleted for shop ${shopId}`, 'deleteShop');
    } catch (emailError) {
      // Don't fail the shop deletion if email sending fails
      logError(`Note: Hard delete completed, email notification not applicable: ${emailError.message}`, 'deleteShop', emailError);
    }
    
    // Return success response with deletion details
    const cascadeStats = result.cascadeStats;
    const totalRecordsDeleted = Object.values(cascadeStats).reduce((sum, count) => sum + count, 0);
    
    const responseMessage = `Shop permanently deleted with complete data cascade: ${totalRecordsDeleted} total records deleted (Users: ${cascadeStats.usersDeleted}, Customers: ${cascadeStats.customersDeleted}, Debts: ${cascadeStats.debtsDeleted}, Payments: ${cascadeStats.paymentsDeleted}, Subscriptions: ${cascadeStats.subscriptionsDeleted}, Files: ${cascadeStats.filesDeleted}, and ${Object.keys(cascadeStats).length - 6} other entity types)`;

    const responseData = {
      shopId: result.shopId,
      deletedAt: result.deletedAt,
      deletionType: result.type,
      totalRecordsDeleted,
      cascadeStats: result.cascadeStats
    };

    res.status(200).json({
      success: true,
      message: responseMessage,
      data: responseData
    });
  } catch (error) {
    next(error);
  }
};

module.exports = deleteShop;
