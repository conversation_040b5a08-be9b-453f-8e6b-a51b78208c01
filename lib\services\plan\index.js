/**
 * Plan Service Index
 * 
 * Main aggregator for all plan-related services
 * UPDATED: Now includes all backend functionality
 */

import getPlans from './getPlans';
import getPlanById from './getPlanById';
import createPlan from './createPlan';
import updatePlan from './updatePlan';
import deletePlan from './deletePlan';
import getPlanStats from './getPlanStats';

import apiBridge from '@/lib/api/bridge';
import { ENDPOINTS } from '@/lib/api/contract';
import { handleError, processApiResponse, logApiCall } from '../baseService';

/**
 * Get plan by type (e.g., 'trial', 'monthly', 'yearly')
 * Finds the first active plan of the specified type
 * @param {string} planType - Plan type to find
 * @returns {Promise<Object>} API response with plan or null
 */
async function getPlanByType(planType) {
  try {
    if (!planType) {
      throw new Error('Plan type is required');
    }

    if (!['trial', 'monthly', 'yearly'].includes(planType)) {
      throw new Error('Plan type must be one of: trial, monthly, yearly');
    }

    logApiCall('PlanService.getPlanByType', ENDPOINTS.PLANS.BASE, { type: planType });

    // Get all plans and find the matching one
    const response = await getPlans({ includeInactive: false });
    
    if (!response.success) {
      throw new Error('Failed to retrieve plans');
    }

    const plans = response.data || [];
    const matchingPlan = plans.find(plan => 
      plan.type === planType && 
      plan.isActive && 
      !plan.isDeleted
    );

    return {
      success: true,
      data: matchingPlan || null
    };
  } catch (error) {
    handleError(error, 'PlanService.getPlanByType', true);
    throw error;
  }
}

/**
 * Toggle plan status (SuperAdmin only)
 * Activates or deactivates a plan
 * @param {string} planId - Plan ID
 * @param {boolean} isActive - New status
 * @returns {Promise<Object>} API response
 */
async function togglePlanStatus(planId, isActive) {
  try {
    if (!planId) {
      throw new Error('Plan ID is required');
    }
    if (typeof isActive !== 'boolean') {
      throw new Error('isActive must be a boolean');
    }

    logApiCall('PlanService.togglePlanStatus', ENDPOINTS.PLANS.TOGGLE_STATUS(planId), { isActive });

    const response = await apiBridge.patch(ENDPOINTS.PLANS.TOGGLE_STATUS(planId), {
      isActive
    }, {
      skipCache: true
    });

    const result = processApiResponse(response, `Plan ${isActive ? 'activated' : 'deactivated'} successfully`);

    // Clear related caches
    if (typeof window !== 'undefined') {
      localStorage.removeItem('cache_plans-list');
      localStorage.removeItem(`cache_plan-${planId}`);
      localStorage.removeItem('cache_plan-stats');
    }

    return result;
  } catch (error) {
    handleError(error, 'PlanService.togglePlanStatus', true);
    throw error;
  }
}

/**
 * Duplicate plan (SuperAdmin only)
 * Creates a copy of existing plan with modified name
 * @param {string} planId - Source plan ID
 * @param {string} newName - New plan name
 * @param {Object} overrides - Optional field overrides
 * @returns {Promise<Object>} API response with duplicated plan
 */
async function duplicatePlan(planId, newName, overrides = {}) {
  try {
    if (!planId) {
      throw new Error('Plan ID is required');
    }
    if (!newName) {
      throw new Error('New plan name is required');
    }

    logApiCall('PlanService.duplicatePlan', `duplicate-${planId}`, { newName, overrides });

    // Get original plan first
    const originalResponse = await getPlanById(planId);
    
    if (!originalResponse.success) {
      throw new Error('Failed to retrieve original plan');
    }

    const originalPlan = originalResponse.data;

    // Create duplicate with new name and any overrides
    const duplicateData = {
      ...originalPlan,
      name: newName.toLowerCase().replace(/\s+/g, '_'),
      displayName: newName,
      // Apply any overrides
      ...overrides,
      // Remove backend-generated fields
      planId: undefined,
      _id: undefined,
      createdAt: undefined,
      updatedAt: undefined,
      createdBy: undefined,
      // Keep original features as-is (SuperAdmin controls individual features)
    };

    return await createPlan(duplicateData);
  } catch (error) {
    handleError(error, 'PlanService.duplicatePlan', true);
    throw error;
  }
}

/**
 * Get active plans only
 * Returns only plans that are active and not deleted
 * @returns {Promise<Object>} API response with active plans
 */
async function getActivePlans() {
  try {
    logApiCall('PlanService.getActivePlans', ENDPOINTS.PLANS.BASE, { active: true });

    const response = await getPlans({ includeInactive: false });
    
    if (!response.success) {
      throw new Error('Failed to retrieve plans');
    }

    const allPlans = response.data || [];
    const activePlans = allPlans.filter(plan => 
      plan.isActive && 
      !plan.isDeleted
    );

    return {
      success: true,
      data: activePlans
    };
  } catch (error) {
    handleError(error, 'PlanService.getActivePlans', true);
    throw error;
  }
}

/**
 * Get plans for selection dropdown
 * Returns only active plans formatted for UI selection
 * @returns {Promise<Object>} API response with formatted plan options
 */
async function getPlansForSelection() {
  try {
    logApiCall('PlanService.getPlansForSelection', ENDPOINTS.PLANS.BASE, { forSelection: true });

    const response = await getActivePlans();
    
    if (!response.success) {
      throw new Error('Failed to retrieve plans');
    }

    const plans = response.data || [];
    const formattedPlans = plans
      .sort((a, b) => (a.displayOrder || 1) - (b.displayOrder || 1))
      .map(plan => ({
        value: plan.planId || plan._id,
        label: plan.displayName || plan.name,
        type: plan.type,
        price: plan.pricing?.basePrice || 0,
        currency: plan.pricing?.currency || 'USD',
        billingCycle: plan.pricing?.billingCycle || 'monthly',
        isRecommended: plan.metadata?.isRecommended || false,
        description: plan.description || '',
        features: plan.features || {},
        limits: plan.limits || {}
      }));

    return {
      success: true,
      data: formattedPlans
    };
  } catch (error) {
    handleError(error, 'PlanService.getPlansForSelection', true);
    throw error;
  }
}

/**
 * Initialize default plans
 * Creates default plans if none exist (matches backend logic)
 * @returns {Promise<Object>} API response
 */
async function initializeDefaultPlans() {
  try {
    logApiCall('PlanService.initializeDefaultPlans', 'initialize-defaults');

    // Check if any plans exist
    const existingPlans = await getPlans();
    
    if (!existingPlans.success) {
      throw new Error('Failed to check existing plans');
    }

    // If plans already exist, don't create defaults
    if (existingPlans.data && existingPlans.data.length > 0) {
      return {
        success: true,
        message: 'Default plans already exist',
        data: existingPlans.data
      };
    }

    // Create default plans that match backend defaults
    const defaultPlans = [
      {
        name: 'trial',
        type: 'trial',
        displayName: 'Free Trial',
        description: 'Try all features free for 14 days',
        pricing: {
          basePrice: 0,
          currency: 'USD',
          billingCycle: 'one-time',
          trialDays: 14,
          setupFee: 0
        },
        displayOrder: 1,
        metadata: {
          isRecommended: false,
          tags: ['trial', 'free']
        }
      },
      {
        name: 'monthly',
        type: 'monthly',
        displayName: 'Monthly Plan',
        description: 'Full access to all features with monthly billing',
        pricing: {
          basePrice: 10,
          currency: 'USD',
          billingCycle: 'monthly',
          trialDays: 0,
          setupFee: 0
        },
        displayOrder: 2,
        metadata: {
          isRecommended: true,
          tags: ['monthly', 'standard']
        }
      },
      {
        name: 'yearly',
        type: 'yearly',
        displayName: 'Annual Plan',
        description: 'Save 20% with yearly billing',
        pricing: {
          basePrice: 96,  // Match backend pricing - $96/year (20% discount from $120)
          currency: 'USD',
          billingCycle: 'yearly',
          trialDays: 0,
          setupFee: 0
        },
        displayOrder: 3,
        metadata: {
          isRecommended: false,
          tags: ['yearly', 'discounted']
        }
      }
    ];

    // Create each default plan
    const createdPlans = [];
    for (const planData of defaultPlans) {
      try {
        const result = await createPlan(planData);
        if (result.success) {
          createdPlans.push(result.data);
        }
      } catch (error) {
        console.warn(`Failed to create default plan ${planData.name}:`, error.message);
      }
    }

    return {
      success: true,
      message: `Created ${createdPlans.length} default plans`,
      data: createdPlans
    };
  } catch (error) {
    handleError(error, 'PlanService.initializeDefaultPlans', true);
    throw error;
  }
}

/**
 * Get plan by ID
 * @param {string} planId - Plan ID
 * @param {Object} options - Additional options
 * @returns {Promise<Object>} Plan details
 */
async function getPlanByIdWrapper(planId, options = {}) {
  try {
    
    if (!planId) {
      throw new Error('Plan ID is required');
    }

    // Call the individual service with options
    const plan = await getPlanById(planId, options);
    
    return {
      success: true,
      data: plan,
      message: 'Plan retrieved successfully'
    };
  } catch (error) {
    handleError(error, 'PlanService.getPlanById', true);
    
    return {
      success: false,
      data: null,
      message: error.message || 'Failed to get plan'
    };
  }
}

// Export all services
export {
  getPlans,
  getPlanById,
  createPlan,
  updatePlan,
  deletePlan,
  getPlanStats,
  getPlanByType,
  togglePlanStatus,
  duplicatePlan,
  getActivePlans,
  getPlansForSelection,
  initializeDefaultPlans
};

export default {
  getPlans,
  getPlanById: getPlanByIdWrapper,
  createPlan,
  updatePlan,
  deletePlan,
  getPlanStats,
  getPlanByType,
  togglePlanStatus,
  duplicatePlan,
  getActivePlans,
  getPlansForSelection,
  initializeDefaultPlans
};