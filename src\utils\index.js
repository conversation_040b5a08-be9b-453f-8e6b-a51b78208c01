/**
 * Utilities Index
 * Centralizes all utility exports for cleaner imports throughout the application
 * 
 * Usage example:
 * import { App<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Response<PERSON>elper, TokenHelper } from '../utils';
 */

// Export error handling utilities
const AppError = require('./core/AppError');
const ErrorHandler = require('./core/errorHandler');
const ErrorResponse = require('./core/errorResponse');

// Export code and ID generation utilities
const { generateVerificationCode, generateToken, calculateExpiry } = require('./generators/generateCode');
const idGenerator = require('./generators/idGenerator');

// Export new helper utilities
const TokenHelper = require('./helpers/tokenHelper');
const ResponseHelper = require('./helpers/responseHelper');
const DebugHelper = require('./helpers/debugHelper');
const LogHelper = require('./helpers/logHelper');
const UserHelper = require('./helpers/userHelper');
const ShopHelper = require('./helpers/shopHelper');
const SubscriptionHelper = require('./helpers/subscriptionHelper');
const PaginationHelper = require('./helpers/paginationHelper');
const ValidationHelper = require('./helpers/validationHelper');
const SettingsHelper = require('./helpers/settingsHelper');
const TransactionHelper = require('./helpers/TransactionHelper');

// Export logging utilities
const logger = require('./logger.js');
const { 
  logInfo, 
  logSuccess, 
  logWarning, 
  logError, 
  logDebug, 
  logAuth, 
  logDatabase, 
  logApi, 
  logPerformance,
  logSession,
  logValidation,
  timer
} = require('./logger.js');

// Legacy response helper for backward compatibility
const successResponse = (res, { message, data, statusCode = 200 }) => {
  return ResponseHelper.success(res, message, data, statusCode);
};

/**
 * Clean export object - no circular dependencies
 */
module.exports = {
  // Error utilities
  AppError,
  ErrorHandler,
  ErrorResponse,
  
  // Code generation
  generateVerificationCode,
  generateToken,
  calculateExpiry,
  idGenerator,
  
  // Helper utilities
  TokenHelper,
  ResponseHelper,
  DebugHelper,
  LogHelper,
  UserHelper,
  ShopHelper,
  SubscriptionHelper,
  PaginationHelper,
  ValidationHelper,
  SettingsHelper,
  TransactionHelper,
  
  // Response utilities
  successResponse,
  
  // Logging
  logger,
  logInfo,
  logSuccess,
  logWarning,
  logError,
  logDebug,
  logAuth,
  logDatabase,
  logApi,
  logPerformance,
  logSession,
  logValidation,
  timer
};
