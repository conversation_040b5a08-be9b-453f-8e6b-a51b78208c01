/**
 * Get Expired Active Subscriptions Service
 * Finds subscriptions that have expired but are still marked as active
 */
const { Subscription } = require('../../models');
const { logError } = require('../../utils');

/**
 * Get all subscriptions that have expired but are still active
 * @returns {Promise<Array>} Array of expired active subscriptions
 */
const getExpiredActiveSubscriptions = async () => {
  try {
    const now = new Date();
    
    // Find subscriptions where:
    // 1. Status is 'active' or 'trial'
    // 2. End date has passed
    // 3. Not deleted
    const expiredSubscriptions = await Subscription.find({
      status: { $in: ['active', 'trial'] },
      'dates.endDate': { $lt: now },
      isDeleted: false
    }).lean();

    return expiredSubscriptions;
  } catch (error) {
    logError('Failed to get expired active subscriptions', 'SubscriptionService', error);
    throw error;
  }
};

module.exports = getExpiredActiveSubscriptions; 