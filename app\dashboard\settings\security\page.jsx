"use client";

import { useEffect } from "react";
import { useRouter } from "next/navigation";
import { useSettings } from "@/contexts/settings";
import { useAuth } from "@/contexts/auth-context";
import { toast } from "sonner";

// Import modular components
import { SettingsPageHeader } from "@/components/settings/settings-page-header";
import { SecuritySettingsCard } from "@/components/settings/security-settings-card";

/**
 * Security Settings Page
 * Allows SuperAdmin to configure global security settings
 * Strict access control - SuperAdmin only
 */
export default function SecuritySettingsPage() {
  const router = useRouter();
  const { 
    securitySettings,
    loading, 
    error, 
    fetchSecuritySettings, 
    updateSecuritySettings 
  } = useSettings();
  const { isSuperAdmin, user } = useAuth();

  // Fetch security settings on mount with strict SuperAdmin check
  useEffect(() => {
    const loadSecuritySettings = async () => {
      if (isSuperAdmin()) {
        try {
          await fetchSecuritySettings();
        } catch (err) {
          console.error("Error fetching security settings:", err);
          toast.error("Failed to load security settings. Please try again later.");
        }
      } else {
        toast.error("Only SuperAdmin users can access security settings");
        router.push("/dashboard");
      }
    };
    
    loadSecuritySettings();
  }, [isSuperAdmin, fetchSecuritySettings, router]);

  // Save security settings
  const handleSaveSecuritySettings = async (updatedSettings) => {
    try {
      // Double-check that user is SuperAdmin before saving
      if (!isSuperAdmin()) {
        toast.error("Permission denied: Only SuperAdmin users can modify security settings");
        return { success: false, error: "Permission denied" };
      }
      
      const result = await updateSecuritySettings(updatedSettings);
      
      if (result.success) {
        toast.success("Security settings updated successfully");
        // Log sensitive operation
        console.log(`Security settings updated by SuperAdmin: ${user?.email}`);
      }
      return result;
    } catch (err) {
      toast.error("Failed to update security settings");
      console.error("Security settings update error:", err);
      return { success: false, error: err.message };
    }
  };

  // Strict rendering guard - SuperAdmin only
  if (!isSuperAdmin()) {
    return null;
  }

  return (
    <div className="container py-8">
      <SettingsPageHeader 
        title="Security Settings" 
        description="Configure global security policies and protections"
      />
      
      <div className="mt-8 max-w-4xl mx-auto">
        <SecuritySettingsCard
          initialSettings={securitySettings}
          onSave={handleSaveSecuritySettings}
          loading={loading}
        />
        
        {/* Information about backend security features */}
        <div className="mt-8 p-6 bg-slate-50 rounded-lg border border-slate-200">
          <h3 className="text-lg font-medium mb-4">Advanced Security Features</h3>
          <p className="mb-3 text-sm text-slate-700">
            DeynCare implements several advanced security mechanisms that are configured on the backend:
          </p>
          <ul className="space-y-2 text-sm text-slate-700 list-disc pl-5">
            <li>
              <span className="font-medium">CSRF Protection:</span> Double-submit cookie pattern with token rotation
            </li>
            <li>
              <span className="font-medium">Content Security Policy:</span> Nonce-based CSP with environment-specific policies
            </li>
            <li>
              <span className="font-medium">Secure Cookies:</span> Centralized security configuration with Redis session store
            </li>
            <li>
              <span className="font-medium">Rate Limiting:</span> Endpoint-specific rate limits with Redis support
            </li>
            <li>
              <span className="font-medium">Context-aware Protection:</span> Special handling for login/logout flows and sensitive operations
            </li>
          </ul>
          <p className="mt-4 text-sm text-slate-600 italic">
            These features are configured via environment variables and backend code. Contact system administrators for changes.
          </p>
        </div>
      </div>
    </div>
  );
}
