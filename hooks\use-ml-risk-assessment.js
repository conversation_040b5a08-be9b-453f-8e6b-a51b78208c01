import { useState, useCallback } from 'react';
import { assessSingleRisk, formatDebtForML } from '../lib/services/ml/riskAssessment';

/**
 * Custom hook for ML risk assessment
 */
export function useMLRiskAssessment() {
  const [isAssessing, setIsAssessing] = useState(false);
  const [lastAssessment, setLastAssessment] = useState(null);
  const [error, setError] = useState(null);

  const assessRisk = useCallback(async (debtData) => {
    setIsAssessing(true);
    setError(null);

    try {
      // Format debt data for ML API if needed
      const mlData = typeof debtData.DebtPaidRatio !== 'undefined' 
        ? debtData 
        : formatDebtForML(debtData);

      const result = await assessSingleRisk(mlData);
      
      if (result.success) {
        setLastAssessment(result.data);
        return result.data;
      } else {
        setError(result.error);
        return null;
      }
    } catch (err) {
      setError(err.message);
      return null;
    } finally {
      setIsAssessing(false);
    }
  }, []);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  const clearAssessment = useCallback(() => {
    setLastAssessment(null);
    setError(null);
  }, []);

  return {
    assessRisk,
    isAssessing,
    lastAssessment,
    error,
    clearError,
    clearAssessment,
  };
} 