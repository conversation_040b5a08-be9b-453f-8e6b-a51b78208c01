import React from 'react';
import { RefreshCw, Download, CreditCard, CheckCircle, XCircle, FileText } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';

/**
 * PaymentTransactionsHeader Component
 * Header section for the payment transactions dashboard with actions and title
 * 
 * @param {Object} props - Component props
 * @param {Function} props.onRefresh - Callback for refresh action
 * @param {Function} props.onExportClick - Callback for export action
 * @param {boolean} props.isRefreshing - Whether data is currently refreshing
 * @param {boolean} props.isSuperAdmin - Whether user is SuperAdmin
 * @param {Object} props.stats - Payment transaction statistics
 * @returns {JSX.Element} Rendered component
 */
const PaymentTransactionsHeader = ({
  onRefresh,
  onExportClick,
  isRefreshing = false,
  isSuperAdmin = false,
  stats = null
}) => {
  return (
    <div className="flex flex-col gap-4 md:flex-row md:items-start md:justify-between">
      {/* Title Section */}
      <div className="flex items-start gap-4">
        <div className="p-3 bg-blue-100 dark:bg-blue-900/20 rounded-xl">
          <CreditCard className="h-6 w-6 text-blue-600 dark:text-blue-400" />
        </div>
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Payment Transaction Management</h1>
          <p className="text-muted-foreground mt-1">
            Review and manage subscription payment transactions
          </p>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex flex-wrap gap-2">
        <Button
          onClick={onRefresh}
          disabled={isRefreshing}
          variant="outline"
          size="sm"
        >
          <RefreshCw className={`h-4 w-4 mr-2 ${isRefreshing ? 'animate-spin' : ''}`} />
          {isRefreshing ? 'Refreshing...' : 'Refresh'}
        </Button>

        <Button
          onClick={onExportClick}
          variant="outline"
          size="sm"
          disabled={isRefreshing}
        >
          <Download className="h-4 w-4 mr-2" />
          Export
        </Button>
      </div>
    </div>
  );
};

export default PaymentTransactionsHeader; 