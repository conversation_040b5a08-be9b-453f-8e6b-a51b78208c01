"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { useSettings } from "@/contexts/settings";
import { useAuth } from "@/contexts/auth-context";
import { toast } from "sonner";
import { saveAs } from 'file-saver';

// Import modular components
import { SettingsPageHeader } from "@/components/settings/settings-page-header";
import { SystemLogsCard } from "@/components/settings/system-logs-card";

/**
 * System Logs Page
 * Allows SuperAdmin users to view and filter system logs
 * Strict access control - SuperAdmin only
 */
export default function SystemLogsPage() {
  const router = useRouter();
  const { 
    fetchSystemLogs,
    exportSystemLogs 
  } = useSettings();
  const { isSuperAdmin, user } = useAuth();
  
  const [logs, setLogs] = useState([]);
  const [loading, setLoading] = useState(false);
  const [totalCount, setTotalCount] = useState(0);

  // Verify SuperAdmin access
  useEffect(() => {
    if (!isSuperAdmin()) {
      toast.error("Only SuperAdmin users can access system logs");
      router.push("/dashboard");
    }
  }, [isSuperAdmin, router]);

  // Fetch system logs
  const handleFetchLogs = async (filters) => {
    if (!isSuperAdmin()) return;
    
    try {
      setLoading(true);
      const result = await fetchSystemLogs(filters);
      
      if (result.success) {
        setLogs(result.data.logs || []);
        setTotalCount(result.data.totalCount || 0);
      } else {
        toast.error("Failed to fetch system logs");
      }
    } catch (err) {
      console.error("Error fetching logs:", err);
      toast.error("An error occurred while fetching logs");
    } finally {
      setLoading(false);
    }
  };

  // Export logs as CSV or JSON
  const handleExportLogs = async (filters) => {
    if (!isSuperAdmin()) return;
    
    try {
      setLoading(true);
      toast.info("Preparing logs export...");
      
      const result = await exportSystemLogs({
        ...filters,
        format: "csv" // or "json"
      });
      
      if (result.success && result.data) {
        // Create a Blob from the data
        const blob = new Blob([result.data], { 
          type: filters.format === "json" 
            ? "application/json" 
            : "text/csv;charset=utf-8" 
        });
        
        // Generate filename with current date
        const date = new Date().toISOString().split('T')[0];
        const filename = `deyncare_system_logs_${date}.${filters.format === "json" ? "json" : "csv"}`;
        
        // Save the file
        saveAs(blob, filename);
        toast.success("Logs exported successfully");
      } else {
        toast.error("Failed to export logs");
      }
    } catch (err) {
      console.error("Error exporting logs:", err);
      toast.error("An error occurred while exporting logs");
    } finally {
      setLoading(false);
    }
  };

  // Initial fetch on component mount
  useEffect(() => {
    if (isSuperAdmin()) {
      handleFetchLogs({
        type: "all",
        level: "all",
        page: 1,
        limit: 50
      });
    }
  }, [isSuperAdmin]);

  // Strict rendering guard - SuperAdmin only
  if (!isSuperAdmin()) {
    return null;
  }

  return (
    <div className="container py-8">
      <SettingsPageHeader 
        title="System Logs" 
        description="View and search system activity logs"
      />
      
      <div className="mt-8 max-w-6xl mx-auto">
        <SystemLogsCard
          logs={logs}
          fetchLogs={handleFetchLogs}
          exportLogs={handleExportLogs}
          loading={loading}
          totalCount={totalCount}
        />
        
        {/* Log retention policy info */}
        <div className="mt-8 p-6 bg-slate-50 rounded-lg border border-slate-200">
          <h3 className="text-lg font-medium mb-4">Log Retention Policy</h3>
          <p className="mb-3 text-sm text-slate-700">
            DeynCare implements the following log retention policies:
          </p>
          <ul className="space-y-2 text-sm text-slate-700 list-disc pl-5">
            <li>
              <span className="font-medium">Authentication logs:</span> Retained for 90 days
            </li>
            <li>
              <span className="font-medium">System operation logs:</span> Retained for 30 days
            </li>
            <li>
              <span className="font-medium">Payment transaction logs:</span> Retained for 7 years (regulatory requirement)
            </li>
            <li>
              <span className="font-medium">Error logs:</span> Retained for 60 days
            </li>
            <li>
              <span className="font-medium">Security event logs:</span> Retained for 1 year
            </li>
          </ul>
          <p className="mt-4 text-sm text-slate-600">
            <strong>Note:</strong> Logs containing personally identifiable information (PII) are automatically redacted 
            or anonymized according to applicable data protection regulations after their retention period expires.
          </p>
        </div>
      </div>
    </div>
  );
}
