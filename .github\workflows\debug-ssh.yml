name: 🔍 Debug SSH Connection

on:
  workflow_dispatch:  # Manual trigger only

jobs:
  debug:
    runs-on: ubuntu-latest
    
    steps:
      - name: 📥 Checkout Code
        uses: actions/checkout@v3

      - name: 🧪 Environment Debug
        run: |
          echo "=== Environment Information ==="
          echo "Operating System: $(uname -a)"
          echo "SSH Version: $(ssh -V)"
          echo "OpenSSL Version: $(openssl version)"
          echo "Current User: $(whoami)"
          echo "Current Directory: $(pwd)"
          echo "Home Directory: $HOME"
          echo ""
          
          echo "=== SSH Client Configuration ==="
          ssh -F /dev/null -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null -o LogLevel=DEBUG3 -T ************** 2>&1 | head -10 || true
          echo ""

      - name: 🔐 SSH Key Debug
        run: |
          echo "=== SSH Key Information ==="
          mkdir -p ~/.ssh
          echo "${{ secrets.SSH_PRIVATE_KEY }}" > ~/.ssh/debug_key
          chmod 600 ~/.ssh/debug_key
          
          echo "SSH Key file created"
          ls -la ~/.ssh/debug_key
          
          echo "SSH Key fingerprint:"
          ssh-keygen -l -f ~/.ssh/debug_key || echo "Failed to get fingerprint"
          
          echo "SSH Key type and length:"
          ssh-keygen -l -f ~/.ssh/debug_key -E md5 || echo "Failed to get key info"

      - name: 🌐 Network Debug
        run: |
          echo "=== Network Connection Tests ==="
          echo "Testing connection to ${{ secrets.SSH_HOST }}..."
          
          # Test basic connectivity
          ping -c 3 ${{ secrets.SSH_HOST }} || echo "Ping failed"
          
          # Test SSH port
          nc -zv ${{ secrets.SSH_HOST }} 22 || echo "Port 22 not accessible"
          
          # Test SSH key scan
          ssh-keyscan -H ${{ secrets.SSH_HOST }} || echo "SSH keyscan failed"

      - name: 🔍 SSH Connection Debug
        run: |
          echo "=== SSH Connection Debug ==="
          ssh-keyscan -H ${{ secrets.SSH_HOST }} >> ~/.ssh/known_hosts
          
          echo "Attempting SSH connection with debug..."
          ssh -i ~/.ssh/debug_key -v -o StrictHostKeyChecking=no -o ConnectTimeout=10 ${{ secrets.SSH_USER }}@${{ secrets.SSH_HOST }} 'echo "SSH connection successful"' 2>&1 || echo "SSH connection failed"

      - name: 🧹 Cleanup
        run: |
          rm -f ~/.ssh/debug_key
          echo "Debug key removed" 