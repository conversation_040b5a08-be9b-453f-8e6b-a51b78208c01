import { useState, useCallback, useEffect } from 'react';
import NotificationService from '@/lib/services/notification';

/**
 * Custom hook for notification history management
 * Handles pagination, filtering, and data fetching
 */
export function useNotificationHistory() {
  const [notifications, setNotifications] = useState([]);
  const [pagination, setPagination] = useState({
    total: 0,
    limit: 20,
    offset: 0,
    hasMore: false
  });
  const [filters, setFilters] = useState({
    search: '',
    type: 'all',
    status: 'all',
    shopId: null
  });
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);

  /**
   * Fetch notification history from the backend
   */
  const fetchHistory = useCallback(async (params = {}) => {
    setIsLoading(true);
    setError(null);

    try {
      const queryParams = {
        limit: pagination.limit,
        offset: pagination.offset,
        ...params
      };

      // Apply filters
      if (filters.status && filters.status !== 'all') {
        queryParams.status = filters.status;
      }
      if (filters.type && filters.type !== 'all') {
        queryParams.type = filters.type;
      }
      if (filters.shopId) {
        queryParams.shopId = filters.shopId;
      }

      const response = await NotificationService.getNotificationHistory(queryParams);

      if (response.success) {
        const { notifications: newNotifications, pagination: newPagination } = response.data;
        
        // If offset is 0, replace notifications (fresh load), otherwise append (pagination)
        if (queryParams.offset === 0) {
          setNotifications(newNotifications);
        } else {
          setNotifications(prev => [...prev, ...newNotifications]);
        }
        
        setPagination(newPagination);
      } else {
        setError(response.message || 'Failed to fetch notification history');
      }
    } catch (err) {
      setError(err.message || 'An error occurred while fetching notification history');
    } finally {
      setIsLoading(false);
    }
  }, [pagination.limit, pagination.offset, filters]);

  /**
   * Load more notifications (pagination)
   */
  const loadMore = useCallback(() => {
    if (!isLoading && pagination.hasMore) {
      setPagination(prev => ({ ...prev, offset: prev.offset + prev.limit }));
    }
  }, [isLoading, pagination.hasMore]);

  /**
   * Refresh notifications (reset to first page)
   */
  const refresh = useCallback(() => {
    setPagination(prev => ({ ...prev, offset: 0 }));
    fetchHistory({ offset: 0 });
  }, [fetchHistory]);

  /**
   * Update filters and reset pagination
   */
  const updateFilters = useCallback((newFilters) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
    setPagination(prev => ({ ...prev, offset: 0 }));
  }, []);

  /**
   * Apply search filter
   */
  const search = useCallback((searchTerm) => {
    // For search, we'll filter locally for now since backend doesn't have search yet
    // In the future, this could be moved to backend
    const filteredNotifications = notifications.filter(notification => 
      notification.title?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      notification.message?.toLowerCase().includes(searchTerm.toLowerCase())
    );
    
    // For now, just update the search term in filters
    setFilters(prev => ({ ...prev, search: searchTerm }));
    
    return filteredNotifications;
  }, [notifications]);

  /**
   * Get filtered notifications based on current search term
   */
  const getFilteredNotifications = useCallback(() => {
    if (!filters.search) return notifications;
    
    return notifications.filter(notification => 
      notification.title?.toLowerCase().includes(filters.search.toLowerCase()) ||
      notification.message?.toLowerCase().includes(filters.search.toLowerCase())
    );
  }, [notifications, filters.search]);

  // Fetch data when pagination offset changes or filters change
  useEffect(() => {
    fetchHistory();
  }, [pagination.offset, filters.status, filters.type, filters.shopId]);

  return {
    notifications: getFilteredNotifications(),
    pagination,
    filters,
    isLoading,
    error,
    
    // Actions
    fetchHistory,
    loadMore,
    refresh,
    updateFilters,
    search,
    
    // Utilities
    hasMore: pagination.hasMore,
    totalCount: pagination.total
  };
} 