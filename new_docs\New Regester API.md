# New Modular Registration APIs for DeynCare Backend

This document outlines the new modular API endpoints for user registration in the DeynCare backend. These APIs are designed to provide a multi-step registration process, allowing for more granular control, better error handling, and improved frontend orchestration.

**Important:** These new APIs exist alongside the original monolithic `/api/auth/register` endpoint, which remains unchanged.

---

## 1. `/api/register/init` (POST)

**Description:** This endpoint handles the initial creation of a new user account and their associated shop. It sets up the foundational user and shop records but **does not** send email verification codes or initiate any payments.

**Endpoint:** `POST /api/register/init`

**Request Body (JSON, or multipart/form-data for shopLogo):**

```json
{
  "fullName": "<PERSON>e",
  "email": "<EMAIL>",
  "phone": "+************",
  "password": "StrongPassword123!",
  "shopName": "John's Super Shop",
  "shopAddress": "123 Main Street, Mogadishu",
  "planType": "monthly",    // "trial", "monthly", "yearly"
  "paymentMethod": "offline", // "offline", "EVC Plus"
  "initialPaid": false,     // Typically false for this step, true if frontend assumes payment is immediate
  "discountCode": "KARSHE01", // Optional discount code
  "paymentDetails": {
    "phoneNumber": "+************" // Optional, for EVC Plus if initialPaid is true. Not used for actual payment in this step.
  }
}
```

**Note on `shopLogo`:** If `shopLogo` is provided, the request should be `multipart/form-data`. The `shopLogo` field should contain the file itself.

**Expected Success Response (201 Created):**

```json
{
  "success": true,
  "message": "Registration initiated successfully. Please verify your email.",
  "data": {
    "user": {
      "userId": "USR001",
      "fullName": "John Doe",
      "email": "<EMAIL>",
      "phone": "+************",
      "role": "admin",
      "shopId": "SHOP001",
      "status": "pending_email_verification",
      "verified": false,
      "emailVerified": false,
      "isPaid": false,
      "isActivated": false,
      "profilePicture": null,
      "lastLoginAt": null,
      "createdAt": "2024-01-01T10:00:00.000Z",
      "updatedAt": "2024-01-01T10:00:00.000Z",
      "preferences": {
        "theme": "system",
        "language": "en"
      }
    },
    "shop": {
      "id": "SHOP001",
      "name": "John's Super Shop"
    },
    "nextStep": "verify_email_required"
  },
  "statusCode": 201
}
```

**Expected Error Responses:**

*   **400 Bad Request:** If required fields are missing or invalid (`"message": "Validation error"`, `"type": "validation_error"`).
*   **409 Conflict:** If the `email` is already registered (`"message": "Email already exists"`, `"type": "duplicate_key"`, `"errorCode": "email_conflict"`).
*   **400 Bad Request:** If `discountCode` is invalid (`"message": "Invalid discount code"`, `"type": "invalid_discount_code"`).
*   **500 Internal Server Error:** For unexpected server issues (`"message": "Error during registration initialization"`, `"type": "registration_init_error"`).

---

## 2. `/api/register/verify-email` (POST)

**Description:** This endpoint is used to verify the user's email address by providing the email and the verification code sent to them. Upon successful verification, the user's `emailVerified` status is updated. If payment was already made, `isActivated` is also set to true.

**Endpoint:** `POST /api/register/verify-email`

**Request Body (JSON):**

```json
{
  "email": "<EMAIL>",
  "verificationCode": "123456" // The 6-digit code sent to the user's email
}
```

**Expected Success Response (200 OK):**

```json
{
  "success": true,
  "message": "Email verified successfully",
  "data": {
    "user": {
      "userId": "USR001",
      "fullName": "John Doe",
      "email": "<EMAIL>",
      "phone": "+************",
      "role": "admin",
      "shopId": "SHOP001",
      "status": "pending_email_verification", // Could still be this or active if paid already
      "verified": true,
      "emailVerified": true,
      "isPaid": false, // Depends on whether payment was done previously
      "isActivated": false, // True if both emailVerified and isPaid are true
      "profilePicture": null,
      "lastLoginAt": null,
      "createdAt": "2024-01-01T10:00:00.000Z",
      "updatedAt": "2024-01-01T10:01:00.000Z",
      "preferences": {
        "theme": "system",
        "language": "en"
      }
    },
    "nextStep": "payment_required" // Or "registration_complete" if already paid
  },
  "statusCode": 200
}
```

**Expected Error Responses:**

*   **400 Bad Request:** If `email` or `verificationCode` are missing, or if the code is `invalid` or `expired` (`"message": "Invalid verification code"`, `"type": "invalid_verification_code"` or `"message": "Verification code has expired."`, `"type": "verification_code_expired"`).
*   **404 Not Found:** If `user` not found (`"message": "User not found"`, `"type": "user_not_found"`).
*   **500 Internal Server Error:** For unexpected server issues (`"message": "Error during email verification"`, `"type": "email_verification_error"`).

---

## 3. `/api/register/pay` (POST)

**Description:** This endpoint handles the payment initiation for a user's subscription plan. It requires the user to be **authenticated** (via JWT token in `Authorization` header) and their **email must be verified**. Upon successful payment, the user's and shop's `isPaid` and `isActivated` statuses are updated.

**Endpoint:** `POST /api/register/pay`

**Authentication:** Requires a valid JWT `Authorization` header.

**Request Body (JSON):**

```json
{
  "planType": "monthly",    // "trial", "monthly", "yearly" (must match user's selected plan)
  "paymentMethod": "EVC Plus", // "offline", "EVC Plus"
  "paymentDetails": {
    "phoneNumber": "+************" // Required for EVC Plus
  },
  "discountCode": "KARSHE01" // Optional discount code
}
```

**Expected Success Response (200 OK):**

```json
{
  "success": true,
  "message": "Payment successful and account activated!",
  "data": {
    "user": {
      "userId": "USR001",
      "fullName": "John Doe",
      "email": "<EMAIL>",
      "phone": "+************",
      "role": "admin",
      "shopId": "SHOP001",
      "status": "active", // User status becomes active
      "verified": true,
      "emailVerified": true,
      "isPaid": true,
      "isActivated": true,
      "profilePicture": null,
      "lastLoginAt": null,
      "createdAt": "2024-01-01T10:00:00.000Z",
      "updatedAt": "2024-01-01T10:02:00.000Z",
      "preferences": {
        "theme": "system",
        "language": "en"
      }
    },
    "nextStep": "registration_complete"
  },
  "statusCode": 200
}
```

**Expected Success Response (200 OK) for Offline Payment:**

```json
{
  "success": true,
  "message": "Payment skipped. Complete payment later.",
  "data": {
    "user": {
      "userId": "USR001",
      "fullName": "John Doe",
      "email": "<EMAIL>",
      "phone": "+************",
      "role": "admin",
      "shopId": "SHOP001",
      "status": "pending_email_verification", // Or 'active' if email was already verified
      "verified": true,
      "emailVerified": true,
      "isPaid": false, // Remains false for offline
      "isActivated": false, // Remains false for offline
      // ... other user fields
    },
    "nextStep": "registration_complete_offline_payment_pending"
  },
  "statusCode": 200
}
```

**Expected Error Responses:**

*   **401 Unauthorized:** If no valid JWT token is provided.
*   **403 Forbidden:** If the authenticated user's email is not verified (`"message": "Email not verified. Please verify your email before making a payment."`, `"type": "email_not_verified"`).
*   **404 Not Found:** If the associated user or shop cannot be found (`"message": "User not found"` or `"message": "Associated shop not found"`).
*   **400 Bad Request:** If `planType`, `paymentMethod`, or `paymentDetails` are missing/invalid, or if `discountCode` is invalid.
*   **402 Payment Required:** If EVC Plus payment fails at the gateway (e.g., invalid PIN, insufficient balance) (`"message": "EVC Plus payment failed"`, `"type": "evc_payment_declined"`).
*   **502 Bad Gateway:** If there's an issue communicating with the payment provider (e.g., timeout, network issue) (`"message": "Payment processing failed: timeout of 30000ms exceeded"`, `"type": "evc_payment_error"`).
*   **500 Internal Server Error:** For unexpected server issues during payment processing.

--- 