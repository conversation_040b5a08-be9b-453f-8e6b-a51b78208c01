/**
 * Debt Export Controller
 * Handles export requests for debt management data (Admin role)
 */
const BaseExportController = require('./baseExportController');
const DebtService = require('../../services/debtService');
const { logError } = require('../../utils');

class DebtExportController extends BaseExportController {
  /**
   * Get debt export fields
   * @param {Object} req - Express request object
   * @returns {Array} Field configurations
   */
  static async getExportFields(req) {
    return [
      {
        label: 'Debt ID',
        key: 'debtId',
        type: 'string'
      },
      {
        label: 'Customer Name',
        key: 'customer.fullName',
        type: 'string'
      },
      {
        label: 'Customer Phone',
        key: 'customer.phone',
        type: 'string'
      },
      {
        label: 'Customer Email',
        key: 'customer.email',
        type: 'string'
      },
      {
        label: 'Amount',
        key: 'amount',
        type: 'currency',
        currency: 'USD'
      },
      {
        label: 'Interest Rate',
        key: 'interestRate',
        type: 'number'
      },
      {
        label: 'Due Date',
        key: 'dueDate',
        type: 'date'
      },
      {
        label: 'Status',
        key: 'status',
        type: 'string'
      },
      {
        label: 'Priority',
        key: 'priority',
        type: 'string'
      },
      {
        label: 'Risk Level',
        key: 'riskLevel',
        type: 'string'
      },
      {
        label: 'Risk Score',
        key: 'riskScore',
        type: 'number'
      },
      {
        label: 'Balance',
        key: 'balance',
        type: 'currency',
        currency: 'USD'
      },
      {
        label: 'Amount Paid',
        key: 'amountPaid',
        type: 'currency',
        currency: 'USD'
      },
      {
        label: 'Days Overdue',
        key: 'daysOverdue',
        type: 'number'
      },
      {
        label: 'Payment Count',
        key: 'paymentCount',
        type: 'number'
      },
      {
        label: 'Last Payment Date',
        key: 'lastPaymentDate',
        type: 'date'
      },
      {
        label: 'Last Payment Amount',
        key: 'lastPaymentAmount',
        type: 'currency',
        currency: 'USD'
      },
      {
        label: 'Description',
        key: 'description',
        type: 'string'
      },
      {
        label: 'Created At',
        key: 'createdAt',
        type: 'datetime'
      },
      {
        label: 'Updated At',
        key: 'updatedAt',
        type: 'datetime'
      }
    ];
  }

  /**
   * Get debt data for export
   * @param {Object} req - Express request object
   * @returns {Array} Debt data
   */
  static async getExportData(req) {
    try {
      const { 
        status, 
        priority,
        riskLevel,
        customerId,
        startDate,
        endDate,
        dueStartDate,
        dueEndDate
      } = req.query;
      
      const { shopId } = req.user; // Admin can only export their shop's debts
      
      const filters = { shopId };
      if (status) filters.status = status;
      if (priority) filters.priority = priority;
      if (riskLevel) filters.riskLevel = riskLevel;
      if (customerId) filters.customerId = customerId;
      
      if (startDate || endDate) {
        filters.createdAt = {};
        if (startDate) filters.createdAt.$gte = new Date(startDate);
        if (endDate) filters.createdAt.$lte = new Date(endDate);
      }
      
      if (dueStartDate || dueEndDate) {
        filters.dueDate = {};
        if (dueStartDate) filters.dueDate.$gte = new Date(dueStartDate);
        if (dueEndDate) filters.dueDate.$lte = new Date(dueEndDate);
      }

      // Get all debts for this shop (no limit for export)
      const result = await DebtService.getAllDebts(filters, 1, 0);
      
      return result.docs;
    } catch (error) {
      logError('Failed to get debt data for export', 'DebtExportController', error);
      throw error;
    }
  }

  /**
   * Export debts to CSV
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next middleware function
   */
  static async exportToCSV(req, res, next) {
    req.exportConfig = {
      module: 'debts',
      getFields: this.getExportFields,
      getData: this.getExportData,
      filename: req.query.filename || 'debt_management_export'
    };

    await this.prepareExport(req, res, next);
    return super.exportToCSV(req, res, next);
  }

  /**
   * Export debts to Excel
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next middleware function
   */
  static async exportToExcel(req, res, next) {
    req.exportConfig = {
      module: 'debts',
      getFields: this.getExportFields,
      getData: this.getExportData,
      filename: req.query.filename || 'debt_management_export',
      options: {
        sheetName: 'Debt Management',
        styling: {
          header: true,
          columns: {
            'customer.fullName': { width: 20 },
            'customer.phone': { width: 15 },
            'customer.email': { width: 25 },
            'amount': { width: 15, alignment: { horizontal: 'right' } },
            'balance': { width: 15, alignment: { horizontal: 'right' } },
            'amountPaid': { width: 15, alignment: { horizontal: 'right' } },
            'lastPaymentAmount': { width: 15, alignment: { horizontal: 'right' } },
            'dueDate': { width: 15 },
            'lastPaymentDate': { width: 15 },
            'description': { width: 30 },
            'createdAt': { width: 20 },
            'updatedAt': { width: 20 }
          }
        }
      }
    };

    await this.prepareExport(req, res, next);
    return super.exportToExcel(req, res, next);
  }
}

module.exports = DebtExportController; 