/**
 * Get All Subscriptions Controller
 * Handles retrieving all subscriptions with filtering, sorting, and pagination (admin only)
 */
const { SubscriptionService } = require('../../services');
const { logError } = require('../../utils');

/**
 * Get all subscriptions (admin only)
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
const getAllSubscriptions = async (req, res, next) => {
  try {
    // Get query parameters for filtering and pagination
    const { 
      page = 1, 
      limit = 10, 
      status, 
      planType, 
      sortBy = 'createdAt', 
      sortOrder = 'desc' 
    } = req.query;
    
    // Create filter object
    const filter = {};
    
    // Only apply filters if the value is not "all" (which means no filter)
    if (status && status !== 'all') filter.status = status;
    if (planType && planType !== 'all') filter['plan.type'] = planType;
    
    // Create options object for pagination and sorting
    const options = {
      page: parseInt(page),
      limit: parseInt(limit),
      sort: { [sortBy]: sortOrder === 'desc' ? -1 : 1 }
    };
    
    // Log the filter and options for debugging
    console.log('Subscription filter:', JSON.stringify(filter));
    console.log('Subscription options:', JSON.stringify(options));
    
    // Get subscriptions through service
    const result = await SubscriptionService.getAllSubscriptions(filter, options);
    
    console.log('Subscription result summary:', {
      totalSubscriptions: result.subscriptions?.length || 0,
      totalItems: result.pagination?.totalItems || 0
    });
    
    return res.status(200).json({
      success: true,
      message: 'Subscriptions retrieved successfully',
      data: result
    });
  } catch (error) {
    logError('Failed to get all subscriptions', 'SubscriptionController', error);
    return next(error);
  }
};

module.exports = getAllSubscriptions;
