<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Your DeynCare Shop Has Been Activated</title>
  <style>
    /* Inlined styles for email client compatibility */
    :root {
      --primary-color: #2e86de;
      --secondary-color: #0abde3;
      --accent-color: #10ac84;
      --success-color: #20bf6b;
      --text-color: #333333;
      --text-muted: #666666;
      --bg-light: #f7f7f7;
      --bg-white: #ffffff;
      --border-color: #eeeeee;
    }
    body {
      font-family: 'Poppins', 'Segoe UI', Arial, sans-serif;
      line-height: 1.6;
      color: #333333;
      margin: 0;
      padding: 0;
      background-color: #f7f7f7;
    }
    .email-container {
      max-width: 600px;
      margin: 20px auto;
      background-color: #ffffff;
      border-radius: 8px;
      overflow: hidden;
      box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
    }
    .email-header {
      background-color: #2e86de;
      background-image: linear-gradient(to right, #2e86de, #0abde3);
      color: white;
      padding: 24px 20px;
      text-align: center;
    }
    .email-header h1 {
      margin: 15px 0 0;
      font-size: 28px;
      font-weight: 600;
    }
    .email-content {
      padding: 32px 24px;
      background-color: #ffffff;
    }
    .email-greeting {
      font-size: 18px;
      margin-bottom: 15px;
      color: #333333;
    }
    .email-message {
      font-size: 16px;
      margin-bottom: 24px;
      color: #333333;
    }
    .success-banner {
      background-color: #ecf9f2;
      border-radius: 8px;
      padding: 20px;
      margin: 25px 0;
      text-align: center;
      border: 1px solid #d1f0de;
    }
    .success-icon {
      font-size: 48px;
      color: #20bf6b;
      margin-bottom: 10px;
    }
    .success-title {
      font-size: 20px;
      font-weight: 600;
      color: #20bf6b;
      margin-bottom: 10px;
    }
    .btn-container {
      text-align: center;
      margin: 30px 0;
    }
    .btn-primary {
      display: inline-block;
      background-color: #2e86de;
      color: white !important;
      text-decoration: none;
      padding: 12px 30px;
      border-radius: 50px;
      font-weight: 600;
      text-align: center;
      transition: all 0.3s ease;
    }
    .shop-details {
      background-color: #f7f7f7;
      padding: 20px;
      border-radius: 8px;
      margin-top: 20px;
    }
    .shop-details h3 {
      color: #2e86de;
      margin-top: 0;
    }
    .shop-details table {
      width: 100%;
      border-collapse: collapse;
    }
    .shop-details table td {
      padding: 8px 0;
    }
    .shop-details table td:first-child {
      font-weight: 600;
      width: 40%;
    }
    .next-steps {
      margin: 25px 0;
    }
    .step-item {
      display: flex;
      margin-bottom: 15px;
      align-items: flex-start;
    }
    .step-number {
      width: 24px;
      height: 24px;
      margin-right: 10px;
      background-color: #2e86de;
      border-radius: 50%;
      color: white;
      text-align: center;
      line-height: 24px;
      flex-shrink: 0;
      font-weight: 600;
    }
    .step-text {
      flex: 1;
    }
    .divider {
      height: 1px;
      background-color: #eeeeee;
      margin: 25px 0;
    }
    .email-footer {
      background-color: #f7f7f7;
      padding: 20px;
      text-align: center;
      font-size: 13px;
      color: #999999;
      border-top: 1px solid #eeeeee;
    }
    .social-links {
      margin: 15px 0;
    }
    .footer-text {
      margin: 10px 0;
    }
    @media only screen and (max-width: 480px) {
      .email-header h1 {
        font-size: 24px;
      }
      .email-content {
        padding: 25px 15px;
      }
    }
  </style>
</head>
<body>
  <div class="email-container">
    <div class="email-header">
      <h1>DeynCare</h1>
    </div>
    <div class="email-content">
      <div class="email-greeting">
        Hello {{fullName}},
      </div>
      <div class="email-message">
        <p>Great news! Your DeynCare shop has been activated and is now ready for business.</p>
      </div>
      
      <div class="success-banner">
        <div class="success-icon">✓</div>
        <div class="success-title">Shop Successfully Activated</div>
        <p>Your payment has been received and your subscription is now active.</p>
      </div>
      
      <div class="shop-details">
        <h3>Your Shop Details</h3>
        <table>
          <tr>
            <td>Shop Name:</td>
            <td>{{shopName}}</td>
          </tr>
          <tr>
            <td>Shop ID:</td>
            <td>{{shopId}}</td>
          </tr>
          <tr>
            <td>Subscription:</td>
            <td>{{planType}} Plan</td>
          </tr>
        </table>
      </div>
      
      <div class="shop-details">
        <h3>🔐 Your Login Credentials</h3>
        <table>
          <tr>
            <td>Email:</td>
            <td><strong>{{email}}</strong></td>
          </tr>
          <tr>
            <td>Password:</td>
            <td><strong>{{password}}</strong></td>
          </tr>
          <tr>
            <td>Login URL:</td>
            <td><a href="{{loginUrl}}" style="color: #2e86de;">{{loginUrl}}</a></td>
          </tr>
        </table>
        <p style="font-size: 12px; color: #666; margin-top: 10px;">
          <strong>Important:</strong> Please change your password after your first login for security purposes.
        </p>
      </div>
      
      <div class="next-steps">
        <h3>Your Next Steps:</h3>
        <div class="step-item">
          <div class="step-number">1</div>
          <div class="step-text">
            <strong>Login to Your Account:</strong> Use the credentials above to access your shop dashboard.
          </div>
        </div>
        <div class="step-item">
          <div class="step-number">2</div>
          <div class="step-text">
            <strong>Change Your Password:</strong> For security, please update your password after your first login.
          </div>
        </div>
        <div class="step-item">
          <div class="step-number">3</div>
          <div class="step-text">
            <strong>Add Your Customers:</strong> Start by adding your customers to the system.
          </div>
        </div>
        <div class="step-item">
          <div class="step-number">4</div>
          <div class="step-text">
            <strong>Import Customers:</strong> Add your existing customers to start tracking their debts.
          </div>
        </div>
      </div>
      
      <div class="btn-container">
        <a href="{{loginUrl}}" class="btn-primary">Login to Dashboard</a>
      </div>
      
      <div class="divider"></div>
      
      <div class="email-message">
        <p>We're excited to have you on board! If you need any assistance getting started, our support team is available to help.</p>
        <p>Best regards,<br>The DeynCare Team</p>
      </div>
    </div>
    <div class="email-footer">
      <div class="social-links">
        <a href="#">Facebook</a> | 
        <a href="#">Twitter</a> | 
        <a href="#">Instagram</a>
      </div>
      <div class="footer-text">
        © {{year}} DeynCare. All rights reserved.
      </div>
      <div class="footer-text">
        DeynCare - Your Debt Management Solution
      </div>
    </div>
  </div>
</body>
</html>
