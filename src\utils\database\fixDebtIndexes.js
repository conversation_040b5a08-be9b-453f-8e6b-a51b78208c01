/**
 * Fix MongoDB Index Mismatch for Debt Collection
 * 
 * Problem: Existing index uses 'DebtID' but model uses 'debtId'
 * Solution: Drop old index and create correct one
 */

const mongoose = require('mongoose');
require('dotenv').config();

async function fixDebtIndexes() {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI || process.env.DATABASE_URL);
    console.log('✅ Connected to MongoDB');

    const db = mongoose.connection.db;
    const collection = db.collection('debts');

    // Step 1: Check existing indexes
    console.log('\n📋 Current indexes:');
    const indexes = await collection.indexes();
    indexes.forEach(index => {
      console.log(`  - ${JSON.stringify(index.key)} (${index.name})`);
    });

    // Step 2: Drop problematic index if it exists
    try {
      await collection.dropIndex({ DebtID: 1 });
      console.log('\n✅ Dropped old DebtID index');
    } catch (error) {
      if (error.codeName === 'IndexNotFound') {
        console.log('\n⚠️  DebtID index not found (already removed)');
      } else {
        console.log('\n❌ Error dropping DebtID index:', error.message);
      }
    }

    // Step 3: Create correct index
    try {
      await collection.createIndex({ debtId: 1 }, { unique: true, name: 'debtId_1' });
      console.log('✅ Created new debtId index');
    } catch (error) {
      if (error.code === 11000) {
        console.log('⚠️  debtId index already exists');
      } else {
        console.log('❌ Error creating debtId index:', error.message);
      }
    }

    // Step 4: Verify the fix
    console.log('\n📋 Updated indexes:');
    const newIndexes = await collection.indexes();
    newIndexes.forEach(index => {
      console.log(`  - ${JSON.stringify(index.key)} (${index.name})`);
    });

    // Step 5: Check for any documents with null debtId
    const nullDebtIdCount = await collection.countDocuments({ debtId: null });
    const missingDebtIdCount = await collection.countDocuments({ debtId: { $exists: false } });
    
    console.log(`\n📊 Data integrity check:`);
    console.log(`  - Documents with null debtId: ${nullDebtIdCount}`);
    console.log(`  - Documents missing debtId field: ${missingDebtIdCount}`);

    if (nullDebtIdCount > 0 || missingDebtIdCount > 0) {
      console.log('\n⚠️  WARNING: Found documents with missing/null debtId');
      console.log('   These need to be fixed before creating new debts');
      console.log('   Run: node fixOrphanedDebts.js');
    }

    console.log('\n🎉 Index fix completed successfully!');

  } catch (error) {
    console.error('❌ Error fixing indexes:', error);
  } finally {
    await mongoose.disconnect();
    console.log('📤 Disconnected from MongoDB');
  }
}

// Run the fix
if (require.main === module) {
  fixDebtIndexes();
}

module.exports = fixDebtIndexes; 