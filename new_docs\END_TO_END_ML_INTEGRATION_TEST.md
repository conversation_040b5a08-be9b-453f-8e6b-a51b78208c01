# End-to-End ML Integration Test Journey

## Overview
This document provides a complete testing workflow for the DeynCare ML integration system, from initial setup to ML prediction validation.

## Prerequisites
- DeynCare backend running on `http://localhost:3000`
- Postman or similar API testing tool
- SuperAdmin account credentials
- Ad<PERSON> (shop owner) account credentials

---

## Currently Available Endpoints

### **Settings Endpoints:**
- `GET /api/settings` - Get all settings (with optional category filter)
- `PATCH /api/settings/:key` - Update specific setting by key (SuperAdmin only for global settings)
- `GET /api/settings/payment-methods` - Get available payment methods (public)

### **Customer Endpoints:**
- `POST /api/customers` - Create new customer
- `GET /api/customers` - Get all customers
- `GET /api/customers/:customerId` - Get customer by ID
- `PUT /api/customers/:customerId` - Update customer
- `DELETE /api/customers/:customerId` - Delete customer
- `GET /api/customers/stats` - Get customer statistics

### **Debt Endpoints:**
- `POST /api/debts` - Create new debt
- `GET /api/debts` - Get all debts  
- `GET /api/debts/:debtId` - Get debt by ID
- `PUT /api/debts/:debtId` - Update debt
- `DELETE /api/debts/:debtId` - Delete debt
- `GET /api/debts/stats` - Get debt statistics
- `POST /api/debts/:debtId/payments` - Add payment to debt
- `GET /api/debts/:debtId/payments` - Get payment history

---

## Test Journey Flow

### **Step 1: SuperAdmin Authentication**
### **Step 2: Check Current ML Settings**
### **Step 3: Admin Authentication & Customer/Debt Operations**
### **Step 4: ML Integration Testing**

---

## **STEP 1: SuperAdmin Authentication**

### 1.1 SuperAdmin Login
**Endpoint:** `POST /api/auth/login`
**Auth:** No Bearer Required (Login Endpoint)

```json
{
  "email": "<EMAIL>",
  "password": "your_superadmin_password"
}
```

**Expected Response:**
```json
{
  "success": true,
  "message": "Login successful",
  "data": {
    "user": {
      "userId": "SA123456789",
      "email": "<EMAIL>",
      "role": "superAdmin"
    },
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "csrfToken": "csrf_token_here"
  }
}
```

---

## **STEP 2: Check Current ML Settings**

### 2.1 Get ML Settings
**Endpoint:** `GET /api/settings?category=ml`
**Auth:** Bearer Token Required (SuperAdmin)
**Headers:** 
- `Authorization: Bearer {superadmin_token}`

**Expected Response:**
```json
{
  "success": true,
  "data": [
    {
      "key": "ml_enabled",
      "category": "ml",
      "displayName": "ML Enabled",
      "description": "Enable/disable ML risk evaluation globally.",
      "value": false,
      "dataType": "boolean",
      "isEditable": true,
      "accessLevel": "superAdmin"
    },
    {
      "key": "ml_api_base_url",
      "category": "ml",
      "displayName": "ML API Base URL",
      "description": "Base URL for ML service",
      "value": "http://localhost:8000",
      "dataType": "string",
      "isEditable": true,
      "accessLevel": "superAdmin"
    },
    {
      "key": "ml_api_key",
      "category": "ml",
      "displayName": "ML API Key",
      "description": "API key for ML service (optional - not enforced by FastAPI)",
      "value": "",
      "dataType": "string",
      "isEditable": true,
      "accessLevel": "superAdmin"
    }
  ]
}
```

### 2.2 Enable ML (if needed)
**Endpoint:** `PATCH /api/settings/ml_enabled`
**Auth:** Bearer Token Required (SuperAdmin)
**Headers:** 
- `Authorization: Bearer {superadmin_token}`
- `X-CSRF-Token: {csrf_token}`

```json
{
  "value": true
}
```

### 2.3 Set ML API URL (if needed)
**Endpoint:** `PATCH /api/settings/ml_api_base_url`
**Auth:** Bearer Token Required (SuperAdmin)
**Headers:** 
- `Authorization: Bearer {superadmin_token}`
- `X-CSRF-Token: {csrf_token}`

```json
{
  "value": "https://deyncare-ml.onrender.com"
}
```

---

## **STEP 3: Admin Authentication & Operations**

### 3.1 Admin Login
**Endpoint:** `POST /api/auth/login`
**Auth:** No Bearer Required (Login Endpoint)

```json
{
  "email": "<EMAIL>",
  "password": "admin_password"
}
```

**Expected Response:**
```json
{
  "success": true,
  "message": "Login successful",
  "data": {
    "user": {
      "userId": "U123456789",
      "email": "<EMAIL>",
      "role": "admin",
      "shopId": "S123456789"
    },
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "csrfToken": "csrf_token_here"
  }
}
```

### 3.2 Create Customer
**Endpoint:** `POST /api/customers`
**Auth:** Bearer Token Required (Admin)
**Headers:** 
- `Authorization: Bearer {admin_token}`
- `X-CSRF-Token: {csrf_token}`

```json
{
  "CustomerName": "John Doe",
  "phone": "+1234567890",
  "CustomerType": "New"
}
```

**Expected Response:**
```json
{
  "success": true,
  "message": "Customer created successfully",
  "data": {
    "customerId": "C_S123456789_1642248000000",
    "customerName": "John Doe",
    "phone": "+1234567890",
    "customerType": "New",
    "riskProfile": {
      "currentRiskLevel": "Not Assessed",
      "riskScore": 0,
      "lastAssessment": null,
      "assessmentCount": 0
    },
    "createdAt": "2024-01-15T10:30:00.000Z"
  }
}
```

### 3.3 Create Debt with ML Integration
**Endpoint:** `POST /api/debts`
**Auth:** Bearer Token Required (Admin)
**Headers:** 
- `Authorization: Bearer {admin_token}`
- `X-CSRF-Token: {csrf_token}`

```json
{
  "customerId": "C_S123456789_1642248000000",
  "CustomerName": "John Doe",
  "customerPhone": "+1234567890",
  "CustomerType": "New",
  "DebtAmount": 1000,
  "DueDate": "2024-01-30T00:00:00.000Z",
  "notes": "First loan for electronics purchase"
}
```

**Expected Response (When ML is Enabled):**
```json
{
  "success": true,
  "message": "Debt created successfully",
  "data": {
    "debtId": "D_S123456789_1642248000001",
    "customerId": "C_S123456789_1642248000000",
    "customerName": "John Doe",
    "customerType": "New",
    "debtAmount": 1000,
    "outstandingDebt": 1000,
    "paidAmount": 0,
    "debtPaidRatio": 0,
    "dueDate": "2024-01-30T00:00:00.000Z",
    "riskLevel": "Active Debt",
    "paymentDelay": 0,
    "repaymentTime": 0,
    "isOnTime": true,
    "mlPayload": {
      "DebtPaidRatio": 0,
      "PaymentDelay": 0,
      "OutstandingDebt": 1000,
      "DebtAmount": 1000
    },
    "createdAt": "2024-01-15T10:35:00.000Z"
  }
}
```

### 3.4 Add Payment (Triggers ML Re-evaluation)
**Endpoint:** `POST /api/debts/{debtId}/payments`
**Auth:** Bearer Token Required (Admin)
**Headers:** 
- `Authorization: Bearer {admin_token}`
- `X-CSRF-Token: {csrf_token}`

```json
{
  "amount": 300,
  "paymentMethod": "cash",
  "notes": "First partial payment",
  "paymentDate": "2024-02-05T10:00:00.000Z"
}
```

**Expected Response (When ML is Enabled):**
```json
{
  "success": true,
  "message": "Payment added successfully",
  "data": {
    "payment": {
      "paymentId": "P_S123456789_1642248000002",
      "debtId": "D_S123456789_1642248000001",
      "amount": 300,
      "paymentDate": "2024-02-05T10:00:00.000Z",
      "paymentMethod": "cash",
      "isOnTime": false,
      "paymentDelay": 6
    },
    "updatedDebt": {
      "debtId": "D_S123456789_1642248000001",
      "outstandingDebt": 700,
      "paidAmount": 300,
      "debtPaidRatio": 0.3,
      "riskLevel": "Medium Risk",
      "paymentDelay": 6,
      "repaymentTime": 21,
      "isOnTime": false
    },
    "mlEvaluation": {
      "riskLevel": "Medium",
      "riskScore": 45,
      "confidence": 85,
      "source": "ml_api",
      "factors": [
        "Payment delay detected",
        "Partial payment only"
      ]
    }
  }
}
```

---

## **STEP 4: Verify ML Integration**

### 4.1 Get Payment History with ML Data
**Endpoint:** `GET /api/debts/{debtId}/payments`
**Auth:** Bearer Token Required (Admin)
**Headers:** 
- `Authorization: Bearer {admin_token}`

**Expected Response:**
```json
{
  "success": true,
  "message": "Payment history retrieved successfully",
  "data": {
    "debtInfo": {
      "debtId": "D_S123456789_1642248000001",
      "customerName": "John Doe",
      "debtAmount": 1000,
      "outstandingDebt": 700,
      "riskLevel": "Medium Risk"
    },
    "payments": [
      {
        "paymentId": "P_S123456789_1642248000002",
        "amount": 300,
        "paymentDate": "2024-02-05T10:00:00.000Z",
        "paymentMethod": "cash",
        "isOnTime": false,
        "paymentDelay": 6
      }
    ],
    "statistics": {
      "totalPayments": 1,
      "totalAmount": 300,
      "onTimePaymentRate": 0,
      "paymentProgress": 30
    }
  }
}
```

### 4.2 Get Customer Statistics
**Endpoint:** `GET /api/customers/stats`
**Auth:** Bearer Token Required (Admin)
**Headers:** 
- `Authorization: Bearer {admin_token}`

### 4.3 Get Debt Statistics  
**Endpoint:** `GET /api/debts/stats`
**Auth:** Bearer Token Required (Admin)
**Headers:** 
- `Authorization: Bearer {admin_token}`

---

## **ML Payload Format**

The system sends this simplified payload to FastAPI:

```json
{
  "DebtPaidRatio": 0.3,
  "PaymentDelay": 6,
  "OutstandingDebt": 700,
  "DebtAmount": 1000
}
```

**FastAPI Response:**
```json
{
  "RiskScore": 0.425,
  "PredictedRiskLevel": "Medium Risk"
}
```

---

## **Known Issues & Solutions**

### Issue: "Cannot find /api/settings/ml_api_base_url"
**Solution:** Use the correct endpoint: `PATCH /api/settings/ml_api_base_url`

### Issue: ML not working
**Solution:** 
1. Check ML is enabled: `GET /api/settings?category=ml`
2. Verify FastAPI URL is correct
3. Check server logs for ML API errors

### Issue: Authentication errors
**Solution:** Ensure you include both `Authorization` and `X-CSRF-Token` headers for authenticated requests

---

## **Validation Checklist**

### ✅ **Basic Functionality:**
- [ ] SuperAdmin can view ML settings
- [ ] SuperAdmin can update ML settings
- [ ] Admin can create customers and debts
- [ ] Payments can be recorded

### ✅ **ML Integration (when enabled):**
- [ ] ML payloads are generated correctly
- [ ] FastAPI receives 4-field format
- [ ] Risk levels update based on ML predictions
- [ ] Payment timing analysis works
- [ ] Statistics reflect ML-driven insights

This completes the **realistic** end-to-end ML integration testing workflow! 🚀 