import apiBridge from '@/lib/api/bridge';
import BaseService from '../baseService';

const getPaymentRetryHistory = async (subscriptionId) => {
  try {
    const response = await apiBridge.get(
      ENDPOINTS.subscription.paymentRetryHistory.replace(':subscriptionId', subscriptionId)
    );
    
    return BaseService.processApiResponse(response);
  } catch (error) {
    throw BaseService.handleApiError(error);
  }
};

export default getPaymentRetryHistory;
