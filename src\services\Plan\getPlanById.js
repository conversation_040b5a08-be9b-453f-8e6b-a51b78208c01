/**
 * Get plan by ID
 * Retrieves a plan by its unique ID
 */
const { Plan } = require('../../models');
const { AppError, logError } = require('../../utils');

/**
 * Get plan by ID
 * @param {string} planId - ID of the plan
 * @returns {Promise<Object>} Plan
 */
const getPlanById = async (planId) => {
  try {
    const plan = await Plan.findOne({ 
      planId, 
      isDeleted: false 
    });
    
    if (!plan) {
      throw new AppError('Plan not found', 404, 'plan_not_found');
    }
    
    return plan;
  } catch (error) {
    logError(`Failed to get plan: ${planId}`, 'PlanService', error);
    throw error;
  }
};

module.exports = getPlanById;
