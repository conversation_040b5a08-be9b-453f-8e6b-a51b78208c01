/**
 * Plan Validation Schemas
 * Defines validation rules for plan management operations
 */
const Joi = require('joi');
const { validationPatterns } = require('../validationPatterns');

// Plan validation schemas
const planSchemas = {
  /**
   * Schema for creating a new pricing plan
   */
  createPlan: {
    body: Joi.object({
      name: Joi.string().required().trim().max(50),
      type: Joi.string().valid('trial', 'monthly', 'yearly').required(),
      displayName: Joi.string().required().trim().max(100),
      description: Joi.string().allow('', null).max(500),
      pricing: Joi.object({
        basePrice: Joi.number().min(0).required(),
        currency: Joi.string().default('USD').max(3),
        billingCycle: Joi.string().valid('one-time', 'monthly', 'yearly').required(),
        trialDays: Joi.number().min(0).default(0),
        setupFee: Joi.number().min(0).default(0)
      }).required(),
      features: Joi.object({
        debtTracking: Joi.boolean().default(true),
        customerPayments: Joi.boolean().default(true),
        smsReminders: Joi.boolean().default(true),
        smartRiskScore: Joi.boolean().default(true),
        businessDashboard: Joi.boolean().default(true),
        exportReports: Joi.boolean().default(true),
        customerProfiles: Joi.boolean().default(true),
        offlineSupport: Joi.boolean().default(true)
      }),
      limits: Joi.object({
        maxEmployees: Joi.number().integer().min(1),
        maxStorageMB: Joi.number().integer().min(1),
        maxCustomers: Joi.number().integer().min(1),
        maxDailyTransactions: Joi.number().integer().min(1)
      }),
      isActive: Joi.boolean().default(true),
      displayOrder: Joi.number().integer().default(1),
      metadata: Joi.object({
        isRecommended: Joi.boolean().default(false),
        tags: Joi.array().items(Joi.string()),
        customFields: Joi.object().unknown(true)
      }).default({})
    })
  },

  /**
   * Schema for updating an existing pricing plan
   */
  updatePlan: {
    params: Joi.object({
      planId: Joi.string().required()
    }),
    body: Joi.object({
      name: Joi.string().trim().max(50),
      displayName: Joi.string().trim().max(100),
      description: Joi.string().allow('', null).max(500),
      pricing: Joi.object({
        basePrice: Joi.number().min(0),
        currency: Joi.string().max(3),
        billingCycle: Joi.string().valid('one-time', 'monthly', 'yearly'),
        trialDays: Joi.number().min(0),
        setupFee: Joi.number().min(0)
      }),
      features: Joi.object({
        debtTracking: Joi.boolean(),
        customerPayments: Joi.boolean(),
        smsReminders: Joi.boolean(),
        smartRiskScore: Joi.boolean(),
        businessDashboard: Joi.boolean(),
        exportReports: Joi.boolean(),
        customerProfiles: Joi.boolean(),
        offlineSupport: Joi.boolean()
      }),
      limits: Joi.object({
        maxEmployees: Joi.number().integer().min(1),
        maxStorageMB: Joi.number().integer().min(1),
        maxCustomers: Joi.number().integer().min(1),
        maxDailyTransactions: Joi.number().integer().min(1)
      }),
      isActive: Joi.boolean(),
      displayOrder: Joi.number().integer(),
      metadata: Joi.object({
        isRecommended: Joi.boolean(),
        tags: Joi.array().items(Joi.string()),
        customFields: Joi.object().unknown(true)
      })
    }).min(1) // At least one field must be provided for update
  },

  /**
   * Schema for updating plan features
   */
  updatePlanFeatures: {
    params: Joi.object({
      planId: Joi.string().required()
    }),
    body: Joi.object({
      features: Joi.object({
        debtTracking: Joi.boolean(),
        customerPayments: Joi.boolean(),
        smsReminders: Joi.boolean(),
        smartRiskScore: Joi.boolean(),
        businessDashboard: Joi.boolean(),
        exportReports: Joi.boolean(),
        customerProfiles: Joi.boolean(),
        offlineSupport: Joi.boolean()
      }).required().min(1) // At least one feature must be provided
    })
  },

  /**
   * Schema for toggling plan status
   */
  togglePlanStatus: {
    params: Joi.object({
      planId: Joi.string().required()
        .messages({
          'any.required': 'Plan ID is required',
          'string.base': 'Plan ID must be a string'
        })
    }),
    body: Joi.object({
      isActive: Joi.boolean().required()
        .messages({
          'any.required': 'isActive status is required',
          'boolean.base': 'isActive must be a boolean value'
        })
    })
  }
};

module.exports = planSchemas;
