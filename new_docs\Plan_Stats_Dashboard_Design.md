# Plan Stats Dashboard Design Documentation

## Overview
This document outlines the design specifications for the Plan Stats Dashboard, a SuperAdmin interface for monitoring and managing subscription plans.

## Layout Structure

### 1. Header Section
- **Title**: "Plan Statistics Dashboard"
- **Refresh Button**: Manual refresh button in top-right corner
- **Last Updated**: Timestamp showing last data refresh

### 2. KPI Cards Section (Top Row)
Four metric cards displaying key statistics:

#### Card 1: Active Plans
- **Title**: "Active Plans"
- **Value**: Large number display
- **Icon**: Checkmark icon
- **Theme**: Success state from design system
- **Subtext**: "Currently active subscription plans"

#### Card 2: Pending Plans
- **Title**: "Pending Plans"
- **Value**: Large number display
- **Icon**: Clock icon
- **Theme**: Warning state from design system
- **Subtext**: "Plans awaiting activation"

#### Card 3: Most Used Plan
- **Title**: "Most Popular Plan"
- **Value**: Plan name
- **Icon**: Star icon
- **Theme**: Primary state from design system
- **Subtext**: "Based on active subscriptions"

#### Card 4: Total Plans
- **Title**: "Total Plans"
- **Value**: Large number display
- **Icon**: Database icon
- **Theme**: Secondary state from design system
- **Subtext**: "All plans in system"

### 3. Plans Table Section
A comprehensive table showing all plans with the following columns:

#### Table Columns
1. **Plan Name**
   - Display name of the plan
   - Clickable to view details

2. **Type**
   - Badge showing: Trial/Monthly/Yearly
   - Using application badge styles

3. **Status**
   - Active/Inactive toggle
   - Using application status indicators

4. **Subscribers**
   - Number of active subscriptions
   - Progress bar using application progress component

5. **Price**
   - Base price
   - Currency symbol
   - Billing cycle indicator

6. **Created Date**
   - Formatted date
   - Time since creation

7. **Actions**
   - Edit button
   - Delete button
   - View details button

### 4. Table Features
- **Pagination**: 10 items per page
- **Sorting**: All columns sortable
- **Filtering**: 
  - By plan type
  - By status
  - By price range
- **Search**: Global search across all fields

## Design System Integration
- Using existing application color scheme
- Following application typography system
- Consistent with application component library
- Maintaining application spacing and layout patterns

## Interactive Elements

### Buttons
1. **Refresh Button**
   - Icon: Refresh
   - Position: Top-right
   - Action: Fetches latest data
   - Using application button styles

2. **Action Buttons**
   - Edit: Pencil icon
   - Delete: Trash icon
   - View: Eye icon
   - Using application icon button styles

### Status Indicators
- Using application status indicator components
- Following application state color conventions

## Responsive Design
- **Desktop**: Full layout
- **Tablet**: 
  - KPI cards: 2x2 grid
  - Table: Scrollable horizontally
- **Mobile**:
  - KPI cards: Stacked
  - Table: Scrollable both directions

## Loading States
- Using application loading components
- **Initial Load**: Full page spinner
- **Refresh**: Subtle loading indicator
- **Table Loading**: Skeleton loading

## Error States
- Using application error components
- **API Error**: Error message with retry button
- **No Data**: Empty state with illustration
- **Failed Actions**: Toast notifications

## Accessibility
- ARIA labels for all interactive elements
- Keyboard navigation support
- High contrast mode support
- Screen reader friendly

## Performance Considerations
- Lazy loading for table data
- Caching of statistics
- Debounced search
- Optimized image loading

## API Integration Points
1. **Stats Endpoint**: `/api/plans/stats`
2. **Plans List**: `/api/plans`
3. **Plan Actions**: 
   - Update: PUT `/api/plans/:planId`
   - Delete: DELETE `/api/plans/:planId`
   - Create: POST `/api/plans`

## Future Enhancements
1. Real-time updates using WebSocket
2. Export functionality for table data
3. Advanced filtering options
4. Custom date range selection
5. Plan comparison feature

---

*Note: This design document follows the existing application design system. All components and styles should be consistent with the current application UI/UX patterns.* 