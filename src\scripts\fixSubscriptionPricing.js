/**
 * Fix Subscription Pricing Migration Script
 * Updates existing subscriptions to use correct Plan model pricing instead of hardcoded defaults
 */

const mongoose = require('mongoose');
const { Subscription, Plan } = require('../models');
const { logInfo, logError, logSuccess } = require('../utils');

/**
 * Find subscriptions with incorrect pricing
 */
const findIncorrectPricingSubscriptions = async () => {
  try {
    logInfo('Finding subscriptions with incorrect pricing...', 'PricingMigration');
    
    // Find subscriptions that likely have hardcoded pricing
    const subscriptions = await Subscription.find({
      isDeleted: { $ne: true },
      $or: [
        // Monthly subscriptions with $10 hardcoded pricing
        { 
          'plan.type': 'monthly',
          'pricing.basePrice': 10
        },
        // Yearly subscriptions with $8 hardcoded pricing  
        {
          'plan.type': 'yearly',
          'pricing.basePrice': 8
        },
        // Subscriptions missing planId (can't verify correct pricing)
        {
          planId: { $exists: false }
        },
        // Subscriptions with planId but potentially wrong pricing
        {
          planId: { $exists: true, $ne: null }
        }
      ]
    }).populate('planId');

    logInfo(`Found ${subscriptions.length} subscriptions to analyze`, 'PricingMigration');
    return subscriptions;
  } catch (error) {
    logError(`Error finding incorrect pricing subscriptions: ${error.message}`, 'PricingMigration', error);
    throw error;
  }
};

/**
 * Get correct pricing from Plan model (dynamic pricing system)
 */
const getCorrectPricing = async (planId, planType) => {
  try {
    // First priority: Use specific planId if provided
    if (planId) {
      const plan = await Plan.findOne({ planId });
      if (plan && plan.pricing) {
        logInfo(`Found specific plan pricing for ${planId}: $${plan.pricing.basePrice}`, 'PricingMigration');
        return {
          basePrice: plan.pricing.basePrice,
          currency: plan.pricing.currency || 'USD',
          billingCycle: plan.pricing.billingCycle,
          trialDays: plan.pricing.trialDays || 0,
          setupFee: plan.pricing.setupFee || 0
        };
      } else {
        logInfo(`Plan ${planId} not found, falling back to type-based pricing`, 'PricingMigration');
      }
    }

    // Second priority: Find any active plan of the same type
    const typePlans = await Plan.find({
      type: planType,
      isActive: true,
      isDeleted: { $ne: true }
    }).sort({ displayOrder: 1 });

    if (typePlans.length > 0) {
      const plan = typePlans[0]; // Use first active plan of this type
      logInfo(`Using type-based pricing for ${planType}: $${plan.pricing.basePrice}`, 'PricingMigration');
      return {
        basePrice: plan.pricing.basePrice,
        currency: plan.pricing.currency || 'USD',
        billingCycle: plan.pricing.billingCycle,
        trialDays: plan.pricing.trialDays || 0,
        setupFee: plan.pricing.setupFee || 0
      };
    }

    // Final fallback: Use reasonable defaults based on plan type
    const fallbackPricing = {
      trial: { basePrice: 0, billingCycle: 'one-time', trialDays: 14 },
      monthly: { basePrice: 10, billingCycle: 'monthly', trialDays: 0 },
      yearly: { basePrice: 96, billingCycle: 'yearly', trialDays: 0 }
    };

    const pricing = fallbackPricing[planType];
    if (pricing) {
      logInfo(`Using fallback pricing for ${planType}: $${pricing.basePrice}`, 'PricingMigration');
      return {
        basePrice: pricing.basePrice,
        currency: 'USD',
        billingCycle: pricing.billingCycle,
        trialDays: pricing.trialDays,
        setupFee: 0
      };
    }

    throw new Error(`No pricing configuration found for planId: ${planId}, planType: ${planType}`);
  } catch (error) {
    logError(`Error getting correct pricing: ${error.message}`, 'PricingMigration', error);
    throw error;
  }
};

/**
 * Update single subscription pricing
 */
const updateSubscriptionPricing = async (subscription, session = null) => {
  try {
    const correctPricing = await getCorrectPricing(subscription.planId, subscription.plan.type);
    
    // Check if pricing needs updating
    const needsUpdate = 
      subscription.pricing.basePrice !== correctPricing.basePrice ||
      subscription.pricing.currency !== correctPricing.currency ||
      subscription.pricing.billingCycle !== correctPricing.billingCycle;

    if (!needsUpdate) {
      return { updated: false, reason: 'Pricing already correct' };
    }

    // Preserve existing discount information
    const updatedPricing = {
      ...correctPricing,
      discount: subscription.pricing.discount // Preserve existing discounts
    };

    // Update subscription
    const updateResult = await Subscription.updateOne(
      { _id: subscription._id },
      { 
        $set: { 
          pricing: updatedPricing,
          updatedAt: new Date()
        }
      },
      { session }
    );

    if (updateResult.modifiedCount > 0) {
      logInfo(`Updated pricing for subscription ${subscription.subscriptionId}: $${subscription.pricing.basePrice} → $${correctPricing.basePrice}`, 'PricingMigration');
      return { 
        updated: true, 
        oldPrice: subscription.pricing.basePrice, 
        newPrice: correctPricing.basePrice 
      };
    }

    return { updated: false, reason: 'No changes made' };
  } catch (error) {
    logError(`Error updating subscription ${subscription.subscriptionId}: ${error.message}`, 'PricingMigration', error);
    throw error;
  }
};

/**
 * Run the pricing migration
 */
const runPricingMigration = async (options = {}) => {
  const { dryRun = false, batchSize = 50 } = options;
  
  let session = null;
  let totalProcessed = 0;
  let totalUpdated = 0;
  let errors = [];

  try {
    logInfo(`Starting pricing migration (${dryRun ? 'DRY RUN' : 'LIVE'})...`, 'PricingMigration');

    // Find subscriptions needing updates
    const subscriptions = await findIncorrectPricingSubscriptions();
    
    if (subscriptions.length === 0) {
      logSuccess('No subscriptions need pricing updates', 'PricingMigration');
      return { totalProcessed: 0, totalUpdated: 0, errors: [] };
    }

    // Process in batches with transactions
    for (let i = 0; i < subscriptions.length; i += batchSize) {
      const batch = subscriptions.slice(i, i + batchSize);
      
      if (!dryRun) {
        session = await mongoose.startSession();
        session.startTransaction();
      }

      try {
        for (const subscription of batch) {
          const result = await updateSubscriptionPricing(subscription, session);
          totalProcessed++;
          
          if (result.updated) {
            totalUpdated++;
          }
          
          if (dryRun) {
            logInfo(`[DRY RUN] Would update ${subscription.subscriptionId}: $${result.oldPrice || subscription.pricing.basePrice} → $${result.newPrice || 'N/A'}`, 'PricingMigration');
          }
        }

        if (!dryRun && session) {
          await session.commitTransaction();
        }
        
        logInfo(`Processed batch ${Math.floor(i / batchSize) + 1}: ${batch.length} subscriptions`, 'PricingMigration');
      } catch (error) {
        if (!dryRun && session) {
          await session.abortTransaction();
        }
        errors.push(`Batch ${Math.floor(i / batchSize) + 1}: ${error.message}`);
        logError(`Error processing batch: ${error.message}`, 'PricingMigration', error);
      } finally {
        if (session) {
          session.endSession();
          session = null;
        }
      }
    }

    const summary = {
      totalProcessed,
      totalUpdated,
      errors,
      dryRun
    };

    logSuccess(`Pricing migration completed: ${totalUpdated}/${totalProcessed} subscriptions updated`, 'PricingMigration');
    return summary;

  } catch (error) {
    if (session) {
      await session.abortTransaction();
      session.endSession();
    }
    logError(`Pricing migration failed: ${error.message}`, 'PricingMigration', error);
    throw error;
  }
};

/**
 * Verify migration results
 */
const verifyPricingMigration = async () => {
  try {
    logInfo('Verifying pricing migration results...', 'PricingMigration');

    // Check for remaining incorrect pricing
    const remainingIssues = await Subscription.aggregate([
      {
        $match: {
          isDeleted: { $ne: true }
        }
      },
      {
        $lookup: {
          from: 'plans',
          localField: 'planId',
          foreignField: 'planId',
          as: 'planDetails'
        }
      },
      {
        $addFields: {
          planPricing: { $arrayElemAt: ['$planDetails.pricing.basePrice', 0] },
          pricingMismatch: {
            $ne: ['$pricing.basePrice', { $arrayElemAt: ['$planDetails.pricing.basePrice', 0] }]
          }
        }
      },
      {
        $match: {
          pricingMismatch: true,
          planPricing: { $exists: true }
        }
      },
      {
        $project: {
          subscriptionId: 1,
          'plan.type': 1,
          'pricing.basePrice': 1,
          planPricing: 1
        }
      }
    ]);

    if (remainingIssues.length > 0) {
      logError(`Found ${remainingIssues.length} subscriptions with pricing mismatches`, 'PricingMigration');
      remainingIssues.forEach(issue => {
        logError(`Mismatch: ${issue.subscriptionId} has $${issue.pricing.basePrice} but plan has $${issue.planPricing}`, 'PricingMigration');
      });
      return false;
    }

    logSuccess('All subscription pricing verified successfully', 'PricingMigration');
    return true;
  } catch (error) {
    logError(`Error verifying pricing migration: ${error.message}`, 'PricingMigration', error);
    return false;
  }
};

// CLI interface
if (require.main === module) {
  const args = process.argv.slice(2);
  const dryRun = args.includes('--dry-run');
  const verify = args.includes('--verify');
  const batchSize = parseInt(args.find(arg => arg.startsWith('--batch-size='))?.split('=')[1]) || 50;

  (async () => {
    try {
      await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/deyncare');
      
      if (verify) {
        await verifyPricingMigration();
      } else {
        const result = await runPricingMigration({ dryRun, batchSize });
        console.log('\nMigration Summary:', JSON.stringify(result, null, 2));
        
        if (!dryRun) {
          console.log('\nRunning verification...');
          await verifyPricingMigration();
        }
      }
    } catch (error) {
      console.error('Migration failed:', error);
      process.exit(1);
    } finally {
      await mongoose.disconnect();
      process.exit(0);
    }
  })();
}

module.exports = {
  runPricingMigration,
  verifyPricingMigration,
  findIncorrectPricingSubscriptions,
  updateSubscriptionPricing
};
