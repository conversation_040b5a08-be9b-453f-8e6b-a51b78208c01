/**
 * Login Protection Middleware
 * 
 * @module loginProtection
 * @description Handles account lockout based on failed login attempts
 * @version 1.0.0
 * @since 2025-06-03
 */

const { User, Setting } = require('../models');
const { AppError } = require('../utils');

/**
 * Get login protection settings from database
 * Fallback to default settings if not found
 * @returns {Object} Login protection settings
 */
async function getLoginProtectionSettings() {
  try {
    // Try to get login protection settings from database
    const maxAttemptsSettings = await Setting.findOne({
      category: 'security',
      key: 'login.maxAttempts'
    });
    
    const lockoutDurationSettings = await Setting.findOne({
      category: 'security',
      key: 'login.lockoutDuration'
    });
    
    return {
      maxAttempts: maxAttemptsSettings ? parseInt(maxAttemptsSettings.value) || 5 : 5,
      lockoutDuration: lockoutDurationSettings ? parseInt(lockoutDurationSettings.value) || 15 : 15
    };
  } catch (error) {
    console.error('Error loading login protection settings:', error);
    // Fallback to secure defaults
    return {
      maxAttempts: 5,
      lockoutDuration: 15
    };
  }
}

/**
 * Check if a user account is currently locked out
 * @param {Object} user - User document
 * @param {Object} settings - Login protection settings
 * @returns {Object} Lockout status
 */
async function checkAccountLockout(user, settings) {
  if (!user || !user.loginHistory || !Array.isArray(user.loginHistory)) {
    return { isLocked: false };
  }
  
  // Calculate lockout window based on settings
  const lockoutWindowMinutes = settings.lockoutDuration;
  const lockoutWindowMs = lockoutWindowMinutes * 60 * 1000;
  
  // Get timestamps for failed login attempts within detection window (30 minutes)
  const detectionWindowMs = 30 * 60 * 1000; // 30 minutes detection window
  const now = Date.now();
  const detectionWindowStart = now - detectionWindowMs;
  
  // Find failed login attempts in the detection window
  const recentFailedAttempts = user.loginHistory
    .filter(login => login.status === 'failed' && login.timestamp > detectionWindowStart)
    .sort((a, b) => b.timestamp - a.timestamp); // Sort by most recent first
  
  // If not enough failed attempts to trigger lockout
  if (recentFailedAttempts.length < settings.maxAttempts) {
    return { isLocked: false };
  }
  
  // Get the timestamp of the most recent failed attempt
  const mostRecentFailedAttempt = recentFailedAttempts[0];
  
  if (!mostRecentFailedAttempt) {
    return { isLocked: false };
  }
  
  // Calculate if we're still in the lockout period
  const lockoutEndsAt = new Date(mostRecentFailedAttempt.timestamp.getTime() + lockoutWindowMs);
  const isLocked = lockoutEndsAt > now;
  
  return {
    isLocked,
    lockedUntil: isLocked ? lockoutEndsAt : null,
    remainingMs: isLocked ? lockoutEndsAt - now : 0,
    failedAttempts: recentFailedAttempts.length
  };
}

/**
 * Middleware to check for account lockout before login
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware
 */
async function checkLoginLockoutMiddleware(req, res, next) {
  try {
    // Only check on login attempts
    if (req.path !== '/api/auth/login' || req.method !== 'POST') {
      return next();
    }
    
    const { email } = req.body;
    
    if (!email) {
      return next();
    }
    
    // Get user by email
    const user = await User.findOne({ email: email.toLowerCase() });
    
    // If user doesn't exist, let the login handler deal with it
    if (!user) {
      return next();
    }
    
    // Get login protection settings
    const settings = await getLoginProtectionSettings();
    
    // Check if account is locked
    const lockoutStatus = await checkAccountLockout(user, settings);
    
    if (lockoutStatus.isLocked) {
      // Calculate remaining time in minutes (rounded up)
      const remainingMinutes = Math.ceil(lockoutStatus.remainingMs / (60 * 1000));
      
      // Return lockout error
      throw new AppError(
        `Account temporarily locked due to too many failed login attempts. Try again in ${remainingMinutes} minutes.`,
        423, // Locked
        'account_locked',
        {
          lockedUntil: lockoutStatus.lockedUntil,
          remainingMinutes
        }
      );
    }
    
    next();
  } catch (error) {
    next(error);
  }
}

module.exports = {
  checkLoginLockoutMiddleware,
  getLoginProtectionSettings,
  checkAccountLockout
};
