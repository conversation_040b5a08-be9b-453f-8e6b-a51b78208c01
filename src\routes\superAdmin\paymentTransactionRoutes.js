/**
 * SuperAdmin Payment Transaction Routes
 * Handles all routes related to payment transaction management for SuperAdmin
 */
const express = require('express');
const router = express.Router();
const paymentTransactionController = require('../../controllers/superAdmin/paymentTransactionController');
const { authenticate, authorize } = require('../../middleware/authMiddleware');
const { validate, validateQuery, validateParams } = require('../../middleware/validationMiddleware');
const superAdminPaymentSchemas = require('../../validations/schemas/superAdminPaymentSchemas');
const rateLimit = require('../../middleware/rateLimiters');

// Apply authentication to all routes
router.use(authenticate);

// Apply SuperAdmin authorization to all routes
router.use(authorize(['superAdmin']));

// Apply rate limiting to all routes
router.use(rateLimit.superAdminLimiter);

/**
 * Get all payment transactions with filtering and pagination
 * GET /api/superadmin/payment-transactions
 */
router.get(
  '/',
  validateQuery(superAdminPaymentSchemas.getPaymentTransactions),
  paymentTransactionController.getAllPaymentTransactions
);

/**
 * Get payment transaction statistics
 * GET /api/superadmin/payment-transactions/stats
 */
router.get(
  '/stats',
  validateQuery(superAdminPaymentSchemas.getPaymentStats),
  paymentTransactionController.getPaymentTransactionStats
);

/**
 * Export payment transactions
 * GET /api/superadmin/payment-transactions/export
 */
router.get(
  '/export',
  validateQuery(superAdminPaymentSchemas.exportPaymentTransactions),
  paymentTransactionController.exportPaymentTransactions
);

/**
 * Approve payment transaction
 * POST /api/superadmin/payment-transactions/:paymentId/approve
 */
router.post(
  '/:paymentId/approve',
  validateParams(superAdminPaymentSchemas.approvePaymentTransactionParams),
  validate(superAdminPaymentSchemas.approvePaymentTransaction),
  paymentTransactionController.approvePaymentTransaction
);

/**
 * Reject payment transaction
 * POST /api/superadmin/payment-transactions/:paymentId/reject
 */
router.post(
  '/:paymentId/reject',
  validateParams(superAdminPaymentSchemas.rejectPaymentTransactionParams),
  validate(superAdminPaymentSchemas.rejectPaymentTransaction),
  paymentTransactionController.rejectPaymentTransaction
);

/**
 * Get single payment transaction by ID
 * GET /api/superadmin/payment-transactions/:paymentId
 * This must come after other specific routes to avoid conflicts
 */
router.get(
  '/:paymentId',
  validateParams(superAdminPaymentSchemas.getPaymentTransactionById),
  paymentTransactionController.getPaymentTransactionById
);

module.exports = router; 