const mongoose = require('mongoose');
const UserService = require('../../services/userService');
const ShopService = require('../../services/shopService');
const TokenService = require('../../services/tokenService');
const EmailService = require('../../services/emailService');
const AuthEmailService = require('../../services/email/authEmailService'); // For welcome email

const {
  AppError,
  ResponseHelper,
  UserHelper,
  LogHelper,
  generateVerificationCode,
  logInfo,
  logSuccess,
  logWarning,
  logError,
  TransactionHelper
} = require('../../utils');

/**
 * Resend verification email for pending users
 * POST /api/register/resend-verification
 */
const resendVerificationEmail = async (req, res, next) => {
  try {
    const { email } = req.validatedData || req.body;

    if (!email) {
      return next(new AppError('Email is required', 400, 'validation_error'));
    }

    const normalizedEmail = email.toLowerCase().trim();

    // Find user with pending verification
    const user = await UserService.getUserByEmail(normalizedEmail, {
      includeInactive: true
    });

    if (!user) {
      return next(new AppError('User not found', 404, 'user_not_found'));
    }

    if (user.emailVerified) {
      return next(new AppError('Email is already verified', 400, 'email_already_verified'));
    }

    if (user.status !== 'pending_email_verification') {
      return next(new AppError('User is not in pending verification status', 400, 'invalid_status'));
    }

    // Generate new verification code
    const newVerificationCode = generateVerificationCode(6);
    user.verificationCode = newVerificationCode;
    user.verificationCodeExpires = new Date(Date.now() + 15 * 60 * 1000); // 15 minutes
    await user.save();

    // Send verification email
    await EmailService.auth.sendVerificationEmail(user, newVerificationCode);
    logSuccess(`Verification email resent to ${normalizedEmail}`, 'verifyEmailController');

    return ResponseHelper.success(
      res,
      'Verification email sent successfully',
      {
        email: normalizedEmail,
        nextStep: 'verify_email_required'
      }
    );
  } catch (error) {
    logError(`Failed to resend verification email: ${error.message}`, 'verifyEmailController', error);
    return next(new AppError(
      'Error sending verification email',
      500,
      'email_send_error'
    ));
  }
};

/**
 * Verify user email with verification code.
 * POST /api/register/verify-email
 */
const verifyEmail = async (req, res, next) => {
  try {
    const { email, verificationCode } = req.validatedData || req.body;

    if (!email || !verificationCode) {
      return next(new AppError('Email and verification code are required', 400, 'validation_error'));
    }

    const normalizedEmail = email.toLowerCase().trim();

    // Use a transaction for atomic updates
    const result = await TransactionHelper.withTransaction(async (session) => {
      // Find the user by email, ensuring they are pending email verification
      const user = await UserService.getUserByEmail(normalizedEmail, {
        includeInactive: true, // Need to find pending users
        session
      });

      if (!user) {
        throw new AppError('User not found', 404, 'user_not_found');
      }

      // Check if email is already verified
      if (user.emailVerified) {
        logWarning(`Attempt to verify already verified email for user ${user.userId}`, 'verifyEmailController');
        
        // Determine next step for already verified user
        let nextStep;
        if (user.isActivated) {
          nextStep = 'registration_complete';
        } else {
          // Check if trial plan to determine next step
          let hasTrial = false;
          if (user.shopId) {
            try {
              const { Subscription } = require('../../models');
              const subscription = await Subscription.findOne({ shopId: user.shopId });
              if (subscription && subscription.plan.type === 'trial') {
                hasTrial = true;
                nextStep = 'registration_complete'; // Trial users should be complete
              }
            } catch (subscriptionError) {
              logWarning(`Could not check subscription for already verified user: ${subscriptionError.message}`, 'verifyEmailController');
            }
          }
          
          if (!hasTrial) {
            nextStep = user.isPaid ? 'email_verified_but_needs_payment' : 'needs_payment';
          }
        }
        
        return {
          user: UserHelper.sanitizeUser(user),
          nextStep: nextStep
        }; // Already verified, return current status
      }

      // Validate the verification code and its expiry
      if (user.verificationCode !== verificationCode) {
        throw new AppError('Invalid verification code', 400, 'invalid_verification_code');
      }

      if (user.verificationCodeExpires && new Date() > user.verificationCodeExpires) {
        throw new AppError('Verification code has expired. Please request a new one.', 400, 'verification_code_expired');
      }

      // Update user status
      user.emailVerified = true;
      user.verified = true; // Mark overall user as verified
      user.verificationCode = undefined; // Clear code
      user.verificationCodeExpires = undefined; // Clear expiry
      user.verifiedAt = new Date();

      // Determine the user's status based on email verification and payment
      let justActivated = false;
      let hasPaidOrTrial = user.isPaid;
      
      // Check if user has trial plan (trial plans should be auto-activated after email verification)
      if (!hasPaidOrTrial && user.shopId) {
        try {
          const { Subscription } = require('../../models');
          const subscription = await Subscription.findOne({ shopId: user.shopId }).session(session);
          if (subscription && subscription.plan.type === 'trial') {
            hasPaidOrTrial = true; // Treat trial plans as "paid" for activation purposes
            logInfo(`User ${user.userId} has trial plan - treating as paid for activation`, 'verifyEmailController');
          }
        } catch (subscriptionError) {
          logWarning(`Could not check subscription for user ${user.userId}: ${subscriptionError.message}`, 'verifyEmailController');
        }
      }
      
      if (hasPaidOrTrial) {
        // If already paid (or trial), and now email is verified, then fully activated
        if (!user.isActivated) {
          justActivated = true;
        }
        user.isActivated = true;
        user.status = 'active'; // Fully active
        // Also update the shop's activation status if user is activated
        let shop = null;
        if (user.shopId) {
          shop = await ShopService.findShopById(user.shopId, { session, includeInactive: true });
          if (shop) {
            shop.access.isActivated = true;
            shop.status = 'active'; // Set shop status to active
            
            // Enable email notifications for system emails (welcome, receipts, etc.)
            if (!shop.notifications.emailEnabled) {
              shop.notifications.emailEnabled = true;
              logInfo(`Email notifications enabled for shop ${shop.shopId} during activation`, 'verifyEmailController');
            }
            
            await shop.save({ session });
            logInfo(`Shop ${user.shopId} activated due to user ${user.userId} email verification and payment status`, 'verifyEmailController');
          }
        }
        // Send welcome email and billing receipt if user just became activated
        if (justActivated) {
          try {
            // Get subscription data if available
            let subscription = null;
            if (shop && shop.currentSubscriptionId) {
              const { Subscription } = require('../../models');
              subscription = await Subscription.findOne({ subscriptionId: shop.currentSubscriptionId }).session(session);
            }
            
            // Send welcome email
            await AuthEmailService.sendWelcomeEmail(user, shop, subscription);
            logSuccess(`Welcome email sent to ${user.email}`, 'verifyEmailController');
            
            // Send billing receipt if payment was made
            if (shop && subscription && subscription.payment.verified) {
              const paymentData = {
                transactionId: subscription.payment.paymentDetails?.transactionId || `TXN-${Date.now()}`,
                amount: subscription.pricing.basePrice?.toString() || '0.00',
                method: subscription.payment.method === 'evc_plus' ? 'EVC Plus' : subscription.payment.method
              };
              
              await AuthEmailService.sendRegistrationReceiptEmail(user, shop, paymentData, subscription);
              logSuccess(`Registration receipt email sent to ${user.email}`, 'verifyEmailController');
            }
          } catch (emailError) {
            logError(`Failed to send welcome/receipt emails to ${user.email}`, 'verifyEmailController', emailError);
            // Do not throw; registration should not fail if email fails
          }
        }
      } else {
        // Email verified, but payment is still pending
        user.isActivated = false; // Not fully activated yet
        user.status = 'email_verified_pending_payment'; // New status
      }
      console.log('User status before save:', user.status); // DEBUG LOG
      await user.save({ session }); // Save user changes within the transaction
      console.log('User status after save:', user.status); // DEBUG LOG
      


      // Log the verification event
      await LogHelper.createUserLog(
        user.userId,
        'email_verified',
        { email: normalizedEmail },
        session
      );

      logSuccess(`Email verified for user ${user.userId}`, 'verifyEmailController');

      // Generate authentication tokens for the newly verified user
      const tokenPayload = { userId: user.userId, role: user.role, shopId: user.shopId || null, email: user.email };
      const accessToken = TokenService.generateAccessToken(tokenPayload);
      const refreshTokenData = await TokenService.generateRefreshToken(user); // Pass full user object
      const refreshToken = refreshTokenData.token; // Extract the token string

      // Determine next step based on user's activation status
      let nextStep;
      if (user.isActivated) {
        nextStep = 'registration_complete';
      } else {
        // Check if user has trial plan before requiring payment
        let requiresPayment = true;
        if (user.shopId) {
          try {
            const { Subscription } = require('../../models');
            const subscription = await Subscription.findOne({ shopId: user.shopId }).session(session);
            if (subscription && subscription.plan.type === 'trial') {
              requiresPayment = false; // Trial plans don't require payment
              nextStep = 'registration_complete'; // Trial users should be complete after email verification
              logInfo(`User ${user.userId} with trial plan - skipping payment requirement`, 'verifyEmailController');
            }
          } catch (subscriptionError) {
            logWarning(`Could not check subscription for next step determination: ${subscriptionError.message}`, 'verifyEmailController');
          }
        }
        
        if (requiresPayment) {
          nextStep = 'payment_required'; // User is verified, but payment is still needed
        }
      }
      const sanitizedUser = UserHelper.sanitizeUser(user);
      console.log('Sanitized user status before response:', sanitizedUser.status); // DEBUG LOG
      return { user: sanitizedUser, nextStep, accessToken, refreshToken };
    }, { name: 'VerifyEmail' });

    // Return success response with user status and next step
    return ResponseHelper.success(
      res,
      'Email verified successfully',
      {
        user: result.user,
        nextStep: result.nextStep,
        accessToken: result.accessToken,
        refreshToken: result.refreshToken
      }
    );
  } catch (error) {
    logError(`Email verification failed: ${error.message}`, 'verifyEmailController', error);

    // Handle specific errors
    if (error instanceof AppError) {
      return next(error); // Re-throw AppError for custom handling
    } else if (error.name === 'MongoServerError' && error.code === 11000) {
      return next(new AppError('Database conflict during verification', 409, 'database_conflict'));
    }

    return next(new AppError(
      error.message || 'Error during email verification',
      error.statusCode || 500,
      error.type || 'email_verification_error'
    ));
  }
};

module.exports = {
  verifyEmail,
  resendVerificationEmail,
}; 