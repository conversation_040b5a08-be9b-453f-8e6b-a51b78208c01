/**
 * Format raw settings data for UI display
 * 
 * Transforms API settings data into a grouped format suitable for UI rendering
 * Handles different input types (array, object with data property, single object)
 */

/**
 * Format raw settings data for UI display
 * @param {Array|Object} settings - Raw settings data from API
 * @returns {Object} Settings grouped by category
 */
const formatSettingsForDisplay = (settings) => {
  const groupedSettings = {};
  
  // Handle different input types
  if (!settings) {
    return groupedSettings;
  }
  
  // Convert to array if necessary
  const settingsArray = Array.isArray(settings) ? settings : 
                       (settings.data && Array.isArray(settings.data)) ? settings.data : 
                       Object.keys(settings).length > 0 ? [settings] : [];
  
  // Process each setting
  settingsArray.forEach(setting => {
    if (!setting) return;
    
    const category = setting.category || 'other';
    
    if (!groupedSettings[category]) {
      groupedSettings[category] = [];
    }
    
    groupedSettings[category].push({
      ...setting,
      canEdit: setting.editable && setting.accessLevel !== 'all', // Only editable for admin/superAdmin
      value: setting.value, // This might need type conversion based on dataType
    });
  });
  
  return groupedSettings;
};

export default formatSettingsForDisplay;
