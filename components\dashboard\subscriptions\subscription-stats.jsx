import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { KpiCard } from '@/components/dashboard/common/kpi-card';
import { Skeleton } from '@/components/ui/skeleton';
import { TrendingUp, TrendingDown, Users, DollarSign, Calendar, Activity } from 'lucide-react';

/**
 * SubscriptionStats Component
 * Displays detailed subscription statistics and analytics
 * 
 * @param {Object} props - Component props
 * @param {Object} props.stats - Statistics data
 * @param {boolean} props.loading - Loading state
 * @returns {JSX.Element} Rendered component
 */
const SubscriptionStats = ({ stats, loading = false }) => {
  const formatCurrency = (value, currency = 'USD') => {
    if (!value) return '$0.00';
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency
    }).format(value);
  };

  const formatNumber = (value) => {
    if (!value) return '0';
    return new Intl.NumberFormat('en-US').format(value);
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          {[...Array(4)].map((_, i) => (
            <Card key={i}>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <Skeleton className="h-4 w-24" />
                <Skeleton className="h-4 w-4" />
              </CardHeader>
              <CardContent>
                <Skeleton className="h-8 w-16 mb-2" />
                <Skeleton className="h-4 w-20" />
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Revenue Statistics */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <KpiCard
          title="Total Revenue"
          value={formatCurrency(stats?.revenue?.total || 0)}
          description="All-time revenue"
          icon="dollar"
          trend={stats?.growth?.totalRevenue > 0 ? "up" : stats?.growth?.totalRevenue < 0 ? "down" : "neutral"}
          trendValue={`${stats?.growth?.totalRevenue || 0}%`}
          trendLabel="from last period"
        />
        
        <KpiCard
          title="Monthly Revenue"
          value={formatCurrency(stats?.revenue?.monthly || 0)}
          description="This month's revenue"
          icon="dollar"
          trend={stats?.growth?.monthlyRevenue > 0 ? "up" : stats?.growth?.monthlyRevenue < 0 ? "down" : "neutral"}
          trendValue={`${stats?.growth?.monthlyRevenue || 0}%`}
          trendLabel="from last month"
        />
        
        <KpiCard
          title="Average Revenue Per User"
          value={formatCurrency(stats?.revenue?.arpu || 0)}
          description="ARPU"
          icon="users"
          trend={stats?.growth?.arpu > 0 ? "up" : stats?.growth?.arpu < 0 ? "down" : "neutral"}
          trendValue={`${stats?.growth?.arpu || 0}%`}
          trendLabel="from last month"
        />
        
        <KpiCard
          title="Lifetime Value"
          value={formatCurrency(stats?.revenue?.ltv || 0)}
          description="Average LTV"
          icon="activity"
          trend={stats?.growth?.ltv > 0 ? "up" : stats?.growth?.ltv < 0 ? "down" : "neutral"}
          trendValue={`${stats?.growth?.ltv || 0}%`}
          trendLabel="from last month"
        />
      </div>

      {/* Subscription Breakdown */}
      <div className="grid gap-4 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>Subscription by Status</CardTitle>
            <CardDescription>Distribution of subscription statuses</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {stats?.byStatus && Object.entries(stats.byStatus).map(([status, count]) => (
                <div key={status} className="flex items-center justify-between">
                  <span className="capitalize">{status}</span>
                  <span className="font-medium">{formatNumber(count)}</span>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Subscription by Plan</CardTitle>
            <CardDescription>Distribution across different plans</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {stats?.byPlan && Object.entries(stats.byPlan).map(([plan, count]) => (
                <div key={plan} className="flex items-center justify-between">
                  <span>{plan}</span>
                  <span className="font-medium">{formatNumber(count)}</span>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Monthly Trends */}
      <Card>
        <CardHeader>
          <CardTitle>Monthly Trends</CardTitle>
          <CardDescription>Subscription and revenue trends over time</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-3">
            <div className="text-center">
              <div className="text-2xl font-bold">{formatNumber(stats?.trends?.newSubscriptions || 0)}</div>
              <div className="text-sm text-muted-foreground">New This Month</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold">{formatNumber(stats?.trends?.renewals || 0)}</div>
              <div className="text-sm text-muted-foreground">Renewals</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold">{formatNumber(stats?.trends?.cancellations || 0)}</div>
              <div className="text-sm text-muted-foreground">Cancellations</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Payment Methods */}
      <Card>
        <CardHeader>
          <CardTitle>Payment Methods</CardTitle>
          <CardDescription>Distribution of payment methods used</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {stats?.byPaymentMethod && Object.entries(stats.byPaymentMethod).map(([method, count]) => (
              <div key={method} className="flex items-center justify-between">
                <span className="capitalize">{method.replace('_', ' ')}</span>
                <span className="font-medium">{formatNumber(count)}</span>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default SubscriptionStats; 