/**
 * Discount Service
 * 
 * This module serves as the main entry point for discount-related operations.
 * Each operation has been refactored into its own file in the discount/ directory
 * for better code organization, maintainability, and testing.
 */

// Import all operations from the discount/ directory
const {
  createDiscountCode,
  getDiscountById,
  getDiscountByCode,
  getDiscountCodes,
  validateAndCalculateDiscount,
  applyDiscountCode,
  verifyDiscountCode
} = require('./discount');

// Re-export all discount service functions
const DiscountService = {
  /**
   * Get discount stats for dashboard analytics
   * @returns {Promise<Object>} Stats object with activeCount, inactiveCount, mostUsed, totalCount
   */
  getDiscountStats: async () => {
    const DiscountCode = require('../models/discountCode.model');
    // Get all codes
    const now = new Date();
    // Add breakdown by scope for future dashboard needs
    const [activeCount, inactiveCount, mostUsed, totalCount, globalCount, shopCount] = await Promise.all([
      DiscountCode.countDocuments({ startDate: { $lte: now }, expiryDate: { $gte: now } }),
      DiscountCode.countDocuments({ $or: [ { startDate: { $gt: now } }, { expiryDate: { $lt: now } } ] }),
      DiscountCode.findOne().sort({ usageCount: -1 }).select('code usageCount').lean(),
      DiscountCode.countDocuments({}),
      DiscountCode.countDocuments({ scope: 'global' }),
      DiscountCode.countDocuments({ scope: 'shop' })
    ]);
    return {
      activeCount,
      inactiveCount,
      mostUsed: mostUsed ? { code: mostUsed.code, usageCount: mostUsed.usageCount } : null,
      totalCount,
      globalCount,
      shopCount
    };
  },
  createDiscountCode,
  getDiscountById,
  getDiscountByCode,
  getDiscountCodes,
  validateAndCalculateDiscount,
  applyDiscountCode,
  verifyDiscountCode
};

module.exports = DiscountService;