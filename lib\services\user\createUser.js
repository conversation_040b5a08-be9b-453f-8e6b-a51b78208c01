import apiBridge from '@/lib/api/bridge';
import { ENDPOINTS } from '@/lib/api/contract';
import { handleError, processApiResponse } from '../baseService';

/**
 * Create a new user
 * @param {Object} userData - User data including role and shop association
 * @returns {Promise<Object>} Created user object
 */
async function createUser(userData) {
  try {
    // Validate superAdmin role requires a shop association
    if (userData.role === 'superAdmin' && (!userData.shopId || userData.shopId === 'no-shop')) {
      handleError({ message: 'SuperAdmin users must be associated with a shop' }, 'UserService.createUser', true);
      throw new Error('SuperAdmin users must be associated with a shop');
    }
    
    // Make API request using the bridge
    const response = await apiBridge.post(ENDPOINTS.USERS.BASE, userData, {
      clearCacheEndpoint: ENDPOINTS.USERS.BASE
    });
    
    // Process response using utility
    const result = processApiResponse(response, 'User created successfully');
    return result.user || result;
  } catch (error) {
    handleError(error, 'UserService.createUser', true);
    throw error;
  }
}

export default createUser;
