'use client';

import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { toast } from 'sonner';
import { 
  CreditCard, 
  RefreshCw, 
  AlertCircle, 
  Send
} from 'lucide-react';
import SubscriptionService from '@/lib/services/subscription';

const RetryPaymentModal = ({ isOpen, onClose, subscription, onSuccess }) => {
  const [formData, setFormData] = useState({
    retryType: 'immediate',
    scheduledDate: '',
    maxRetries: 3,
    retryInterval: 24,
    notifyCustomer: true,
    notes: ''
  });
  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState({});

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: null
      }));
    }
  };

  const validateForm = () => {
    const newErrors = {};

    if (formData.retryType === 'scheduled' && !formData.scheduledDate) {
      newErrors.scheduledDate = 'Please select a scheduled date';
    }

    if (formData.retryType === 'scheduled') {
      const selectedDate = new Date(formData.scheduledDate);
      const now = new Date();
      if (selectedDate <= now) {
        newErrors.scheduledDate = 'Scheduled date must be in the future';
      }
    }

    if (formData.maxRetries < 1 || formData.maxRetries > 10) {
      newErrors.maxRetries = 'Max retries must be between 1 and 10';
    }

    if (formData.retryInterval < 1 || formData.retryInterval > 168) {
      newErrors.retryInterval = 'Retry interval must be between 1 and 168 hours';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm()) {
      toast.error('Please correct the errors before submitting');
      return;
    }

    setIsLoading(true);
    try {
      const retryData = {
        retryType: formData.retryType,
        scheduledDate: formData.retryType === 'scheduled' ? formData.scheduledDate : undefined,
        maxRetries: parseInt(formData.maxRetries),
        retryInterval: parseInt(formData.retryInterval),
        notifyCustomer: formData.notifyCustomer,
        notes: formData.notes || undefined
      };

      const endpoint = formData.retryType === 'immediate' 
        ? 'retryPaymentNow' 
        : 'schedulePaymentRetry';

      await SubscriptionService[endpoint](subscription.subscriptionId, retryData);
      
      toast.success(
        formData.retryType === 'immediate' 
          ? 'Payment retry initiated successfully' 
          : 'Payment retry scheduled successfully'
      );
      
      onSuccess && onSuccess();
      onClose();
    } catch (error) {
      console.error('Error processing payment retry:', error);
      toast.error(error.message || 'Failed to process payment retry');
    } finally {
      setIsLoading(false);
    }
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const formatCurrency = (amount, currency = 'USD') => {
    if (amount === null || amount === undefined) return 'N/A';
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency
    }).format(amount);
  };

  if (!subscription) return null;

  const failedPayments = subscription.payment?.failedPayments || 0;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="dialog-content max-w-2xl">
        <DialogHeader className="dialog-header">
          <DialogTitle className="flex items-center gap-2">
            <RefreshCw className="h-5 w-5" />
            Retry Payment - Subscription #{subscription.subscriptionId}
          </DialogTitle>
        </DialogHeader>
        <div className="dialog-body">

        <div className="space-y-6">
          {/* Current Payment Status */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <CreditCard className="h-4 w-4" />
                Current Payment Status
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <p className="text-sm text-gray-500">Payment Status</p>
                  <Badge className="bg-red-100 text-red-800">
                    {subscription.payment?.status || 'Failed'}
                  </Badge>
                </div>
                <div>
                  <p className="text-sm text-gray-500">Amount Due</p>
                  <p className="font-medium text-lg">
                    {formatCurrency(subscription.pricing?.basePrice, subscription.pricing?.currency)}
                  </p>
                </div>
                <div>
                  <p className="text-sm text-gray-500">Failed Attempts</p>
                  <p className="font-medium text-red-600">{failedPayments}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-500">Next Payment Due</p>
                  <p className="font-medium">{formatDate(subscription.payment?.nextPaymentDate)}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Retry Configuration */}
          <Card>
            <CardHeader>
              <CardTitle>Retry Configuration</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label>Retry Type</Label>
                <Select
                  value={formData.retryType}
                  onValueChange={(value) => handleInputChange('retryType', value)}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="immediate">Retry Immediately</SelectItem>
                    <SelectItem value="scheduled">Schedule Retry</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {formData.retryType === 'scheduled' && (
                <div>
                  <Label htmlFor="scheduledDate">Scheduled Date</Label>
                  <Input
                    id="scheduledDate"
                    type="datetime-local"
                    value={formData.scheduledDate}
                    onChange={(e) => handleInputChange('scheduledDate', e.target.value)}
                    min={new Date(Date.now() + 60000).toISOString().slice(0, 16)}
                  />
                  {errors.scheduledDate && (
                    <p className="text-sm text-red-500 mt-1">{errors.scheduledDate}</p>
                  )}
                </div>
              )}

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="maxRetries">Max Retry Attempts</Label>
                  <Input
                    id="maxRetries"
                    type="number"
                    min="1"
                    max="10"
                    value={formData.maxRetries}
                    onChange={(e) => handleInputChange('maxRetries', e.target.value)}
                  />
                  {errors.maxRetries && (
                    <p className="text-sm text-red-500 mt-1">{errors.maxRetries}</p>
                  )}
                </div>

                <div>
                  <Label htmlFor="retryInterval">Retry Interval (hours)</Label>
                  <Input
                    id="retryInterval"
                    type="number"
                    min="1"
                    max="168"
                    value={formData.retryInterval}
                    onChange={(e) => handleInputChange('retryInterval', e.target.value)}
                  />
                  {errors.retryInterval && (
                    <p className="text-sm text-red-500 mt-1">{errors.retryInterval}</p>
                  )}
                  <p className="text-xs text-gray-500 mt-1">
                    Time between retry attempts (1-168 hours)
                  </p>
                </div>
              </div>

              <div className="flex items-center space-x-2">
                <Switch
                  id="notifyCustomer"
                  checked={formData.notifyCustomer}
                  onCheckedChange={(checked) => handleInputChange('notifyCustomer', checked)}
                />
                <Label htmlFor="notifyCustomer">Notify Customer</Label>
                <p className="text-sm text-gray-500">
                  Send payment retry notification to customer
                </p>
              </div>

              <div>
                <Label htmlFor="notes">Notes (Optional)</Label>
                <Textarea
                  id="notes"
                  value={formData.notes}
                  onChange={(e) => handleInputChange('notes', e.target.value)}
                  placeholder="Add any notes about this payment retry..."
                  rows={2}
                />
              </div>
            </CardContent>
          </Card>

          {/* Summary */}
          <Card className="border-blue-200 bg-blue-50">
            <CardContent className="pt-4">
              <div className="flex items-start gap-3">
                <AlertCircle className="h-5 w-5 text-blue-600 mt-0.5" />
                <div className="space-y-1">
                  <p className="font-medium text-blue-800">Payment Retry Summary</p>
                  <div className="text-sm text-blue-700 space-y-1">
                    <p>
                      • {formData.retryType === 'immediate' ? 'Immediate retry' : `Scheduled for ${formatDate(formData.scheduledDate)}`}
                    </p>
                    <p>
                      • Maximum {formData.maxRetries} attempts with {formData.retryInterval}h intervals
                    </p>
                    <p>
                      • Customer {formData.notifyCustomer ? 'will be' : 'will not be'} notified
                    </p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onClose} disabled={isLoading}>
            Cancel
          </Button>
          <Button onClick={handleSubmit} disabled={isLoading}>
            {isLoading ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Processing...
              </>
            ) : (
              <>
                <Send className="h-4 w-4 mr-2" />
                {formData.retryType === 'immediate' ? 'Retry Now' : 'Schedule Retry'}
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default RetryPaymentModal;
