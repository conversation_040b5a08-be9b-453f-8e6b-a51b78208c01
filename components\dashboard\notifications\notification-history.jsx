"use client";

import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { KpiCard } from "@/components/dashboard/common/kpi-card";
import { DataTable } from "@/components/dashboard/common/data-table";
import { Clock, Search, Filter, RefreshCw, CheckCircle, XCircle, Building2, Users, MessageSquare, Loader2, AlertCircle } from "lucide-react";
import { format } from "date-fns";
import { useNotificationHistory } from "@/hooks/use-notification-history";
import { Alert, AlertDescription } from "@/components/ui/alert";

/**
 * Notification History Component
 * Displays and manages notification history with advanced filtering and analytics
 */
export function NotificationHistory({ stats, loadStats, isLoading: statsLoading = false }) {
  const {
    notifications,
    pagination,
    filters,
    isLoading,
    error,
    refresh,
    updateFilters,
    search,
    loadMore,
    hasMore,
    totalCount
  } = useNotificationHistory();

  const [localFilters, setLocalFilters] = useState({
    search: "",
    type: "all",
    status: "all"
  });

  // Handle filter changes with debouncing
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      updateFilters({
        type: localFilters.type !== 'all' ? localFilters.type : null,
        status: localFilters.status !== 'all' ? localFilters.status : null
      });
      if (localFilters.search) {
        search(localFilters.search);
      }
    }, 300);

    return () => clearTimeout(timeoutId);
  }, [localFilters, updateFilters, search]);

  const columns = [
    {
      accessorKey: "type",
      header: "Type",
      cell: ({ row }) => {
        const type = row.original.type;
        const icons = { 
          shops: Building2, 
          broadcast: Users, 
          debt_reminders: MessageSquare,
          push: MessageSquare,
          notification: MessageSquare
        };
        const Icon = icons[type] || MessageSquare;
        return (
          <Badge variant="secondary" className="flex items-center gap-1">
            <Icon className="h-3 w-3" />
            {type}
          </Badge>
        );
      }
    },
    {
      accessorKey: "title",
      header: "Title",
      cell: ({ row }) => (
        <div>
          <p className="font-medium">{row.original.title}</p>
          <p className="text-sm text-muted-foreground truncate max-w-[200px]">
            {row.original.message}
          </p>
        </div>
      )
    },
    {
      accessorKey: "status",
      header: "Status",
      cell: ({ row }) => {
        const status = row.original.status;
        const statusConfig = {
          delivered: { variant: "default", icon: CheckCircle, color: "text-green-600" },
          sent: { variant: "secondary", icon: CheckCircle, color: "text-blue-600" },
          failed: { variant: "destructive", icon: XCircle, color: "text-red-600" },
          pending: { variant: "secondary", icon: Clock, color: "text-yellow-600" },
          expired: { variant: "outline", icon: XCircle, color: "text-gray-600" }
        };
        const config = statusConfig[status] || statusConfig.pending;
        const Icon = config.icon;
        
        return (
          <Badge variant={config.variant}>
            <Icon className={`h-3 w-3 mr-1 ${config.color}`} />
            {status}
          </Badge>
        );
      }
    },
    {
      accessorKey: "recipient",
      header: "Recipient",
      cell: ({ row }) => (
        <div className="text-sm">
          <div className="font-medium">{row.original.recipientType || 'Unknown'}</div>
          {row.original.shopId && (
            <div className="text-muted-foreground">Shop: {row.original.shopId}</div>
          )}
        </div>
      )
    },
    {
      accessorKey: "sentAt",
      header: "Sent Date",
      cell: ({ row }) => {
        const date = new Date(row.original.sentAt);
        return (
          <div className="text-sm">
            <div>{format(date, "MMM dd, yyyy")}</div>
            <div className="text-muted-foreground">{format(date, "HH:mm")}</div>
          </div>
        );
      }
    },
    {
      accessorKey: "priority",
      header: "Priority",
      cell: ({ row }) => {
        const priority = row.original.priority || 'normal';
        const priorityConfig = {
          high: { variant: "destructive", color: "text-red-600" },
          normal: { variant: "secondary", color: "text-blue-600" },
          low: { variant: "outline", color: "text-gray-600" }
        };
        const config = priorityConfig[priority] || priorityConfig.normal;
        
        return (
          <Badge variant={config.variant}>
            {priority}
          </Badge>
        );
      }
    }
  ];

  // Calculate analytics from current notifications
  const analytics = {
    total: totalCount || notifications.length,
    delivered: notifications.filter(n => n.status === 'delivered').length,
    sent: notifications.filter(n => n.status === 'sent').length,
    failed: notifications.filter(n => n.status === 'failed').length,
    pending: notifications.filter(n => n.status === 'pending').length
  };

  const deliveryRate = analytics.total > 0 ? 
    ((analytics.delivered + analytics.sent) / analytics.total * 100) : 0;

  return (
    <div className="space-y-6">
      {/* Analytics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <KpiCard
          title="Total Notifications"
          value={analytics.total}
          description="All notifications"
          icon="activity"
          loading={isLoading || statsLoading}
        />
        <KpiCard
          title="Delivery Rate"
          value={`${deliveryRate.toFixed(1)}%`}
          description="Success rate"
          icon="credit"
          loading={isLoading || statsLoading}
        />
        <KpiCard
          title="Delivered"
          value={analytics.delivered}
          description="Successfully delivered"
          icon="check"
          loading={isLoading || statsLoading}
        />
        <KpiCard
          title="Failed"
          value={analytics.failed}
          description="Failed to deliver"
          icon="x"
          loading={isLoading || statsLoading}
        />
      </div>

      {/* Error Alert */}
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            {error}
          </AlertDescription>
        </Alert>
      )}

      {/* Filters */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <Filter className="h-5 w-5" />
              Filters
            </CardTitle>
            <Button 
              variant="outline" 
              size="sm" 
              onClick={() => {
                refresh();
                if (loadStats) loadStats();
              }} 
              disabled={isLoading || statsLoading}
            >
              <RefreshCw className={`h-4 w-4 mr-2 ${(isLoading || statsLoading) ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
              <Input
                placeholder="Search notifications..."
                value={localFilters.search}
                onChange={(e) => setLocalFilters(prev => ({ ...prev, search: e.target.value }))}
                className="pl-10"
              />
            </div>
            <Select 
              value={localFilters.type} 
              onValueChange={(value) => setLocalFilters(prev => ({ ...prev, type: value }))}
            >
              <SelectTrigger>
                <SelectValue placeholder="All types" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Types</SelectItem>
                <SelectItem value="shops">Shops</SelectItem>
                <SelectItem value="broadcast">Broadcast</SelectItem>
                <SelectItem value="debt_reminders">Debt Reminders</SelectItem>
                <SelectItem value="push">Push Notifications</SelectItem>
              </SelectContent>
            </Select>
            <Select 
              value={localFilters.status} 
              onValueChange={(value) => setLocalFilters(prev => ({ ...prev, status: value }))}
            >
              <SelectTrigger>
                <SelectValue placeholder="All statuses" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Statuses</SelectItem>
                <SelectItem value="delivered">Delivered</SelectItem>
                <SelectItem value="sent">Sent</SelectItem>
                <SelectItem value="pending">Pending</SelectItem>
                <SelectItem value="failed">Failed</SelectItem>
                <SelectItem value="expired">Expired</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Notifications Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Clock className="h-5 w-5" />
            Notification History
          </CardTitle>
          <CardDescription>
            {totalCount > 0 ? (
              `Showing ${notifications.length} of ${totalCount} notifications`
            ) : (
              'No notifications found'
            )}
          </CardDescription>
        </CardHeader>
        <CardContent>
          {isLoading && notifications.length === 0 ? (
            <div className="flex items-center justify-center py-8">
              <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
            </div>
          ) : notifications.length === 0 ? (
            <div className="text-center py-8">
              <Clock className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="font-medium text-muted-foreground">No Notifications Found</h3>
              <p className="text-sm text-muted-foreground mt-2">
                {localFilters.search || localFilters.type !== 'all' || localFilters.status !== 'all' 
                  ? 'Try adjusting your filters to see more results'
                  : 'Notification history will appear here once you start sending notifications'
                }
              </p>
            </div>
          ) : (
            <>
              <DataTable 
                columns={columns} 
                data={notifications}
                searchValue={localFilters.search}
                onSearchChange={(value) => setLocalFilters(prev => ({ ...prev, search: value }))}
              />
              
              {/* Load More Button */}
              {hasMore && (
                <div className="flex justify-center mt-6">
                  <Button 
                    variant="outline" 
                    onClick={loadMore}
                    disabled={isLoading}
                  >
                    {isLoading ? (
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    ) : null}
                    Load More Notifications
                  </Button>
                </div>
              )}
            </>
          )}
        </CardContent>
      </Card>
    </div>
  );
} 