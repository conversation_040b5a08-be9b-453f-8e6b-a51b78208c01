const { Customer, Debt, Payment } = require('../../models');
const { AppError, logError } = require('../../utils');

/**
 * Get Customer Statistics and Risk Distribution
 * GET /api/customers/stats
 */
const getCustomerStats = async (req, res, next) => {
  try {
    const shopId = req.user.shopId;

    // Basic customer counts
    const totalCustomers = await Customer.countDocuments({ shopId, isDeleted: false });
    const newCustomers = await Customer.countDocuments({ shopId, CustomerType: 'New', isDeleted: false });
    const returningCustomers = await Customer.countDocuments({ shopId, CustomerType: 'Returning', isDeleted: false });

    // Risk distribution
    const riskDistribution = await Customer.aggregate([
      { $match: { shopId, isDeleted: false } },
      {
        $group: {
          _id: '$riskProfile.currentRiskLevel',
          count: { $sum: 1 }
        }
      }
    ]);

    // Customer type analysis
    const customerTypeAnalysis = await Customer.aggregate([
      { $match: { shopId, isDeleted: false } },
      {
        $lookup: {
          from: 'debts',
          localField: 'customerId',
          foreignField: 'customerId',
          as: 'debts',
          pipeline: [{ $match: { isDeleted: false } }]
        }
      },
      {
        $group: {
          _id: '$CustomerType',
          count: { $sum: 1 },
          totalDebtsCreated: { $sum: { $size: '$debts' } },
          averageDebtsPerCustomer: { $avg: { $size: '$debts' } },
          totalDebtAmount: { $sum: { $sum: '$debts.DebtAmount' } },
          totalOutstanding: { $sum: { $sum: '$debts.OutstandingDebt' } }
        }
      }
    ]);

    // Recent customers (last 30 days)
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    const recentCustomers = await Customer.countDocuments({
      shopId,
      isDeleted: false,
      createdAt: { $gte: thirtyDaysAgo }
    });

    // Format the response
    const stats = {
      overview: {
        totalCustomers,
        newCustomers,
        returningCustomers,
        newCustomerPercentage: totalCustomers > 0 ? Math.round((newCustomers / totalCustomers) * 100) : 0,
        recentCustomersLast30Days: recentCustomers
      },

      riskDistribution: riskDistribution.reduce((acc, item) => {
        const riskLevel = item._id || 'Not Assessed';
        acc[riskLevel] = {
          count: item.count,
          percentage: totalCustomers > 0 ? Math.round((item.count / totalCustomers) * 100) : 0
        };
        return acc;
      }, {}),

      customerTypes: customerTypeAnalysis.reduce((acc, item) => {
        acc[item._id] = {
          count: item.count,
          percentage: totalCustomers > 0 ? Math.round((item.count / totalCustomers) * 100) : 0,
          totalDebtsCreated: item.totalDebtsCreated,
          averageDebtsPerCustomer: Math.round((item.averageDebtsPerCustomer || 0) * 10) / 10,
          totalDebtAmount: item.totalDebtAmount || 0,
          totalOutstanding: item.totalOutstanding || 0
        };
        return acc;
      }, {})
    };

    res.json({
      success: true,
      message: 'Customer statistics retrieved successfully',
      data: stats,
      generatedAt: new Date()
    });

  } catch (error) {
    logError('Failed to get customer statistics', 'GetCustomerStats', error);
    return next(new AppError('Failed to retrieve customer statistics', 500));
  }
};

module.exports = getCustomerStats; 