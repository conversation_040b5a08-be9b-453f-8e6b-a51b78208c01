/**
 * Plan Routes
 * Handles all routes related to plan management
 */
const express = require('express');
const planController = require('../controllers/plan');
const authMiddleware = require('../middleware/authMiddleware');
const { validate } = require('../middleware/validationMiddleware');
const { planSchemas } = require('../validations');

const router = express.Router();

// Require authentication for all plan routes
router.use(authMiddleware.authenticate);

// Get all plans
router.get('/', planController.getAllPlans);

// Get plan statistics (must come before /:planId to avoid conflict)
router.get('/stats', authMiddleware.authorize('superAdmin'), planController.getPlanStats);

// Get plan by ID
router.get('/:planId', planController.getPlanById);

// Create new plan (SuperAdmin only)
router.post(
  '/',
  authMiddleware.authorize('superAdmin'),
  validate(planSchemas.createPlan),
  planController.createPlan
);

// Update plan (SuperAdmin only)
router.put(
  '/:planId',
  authMiddleware.authorize('superAdmin'),
  validate(planSchemas.updatePlan),
  planController.updatePlan
);

// Toggle plan status (SuperAdmin only) - ADDED
router.patch(
  '/:planId/toggle-status',
  authMiddleware.authorize('superAdmin'),
  validate(planSchemas.togglePlanStatus),
  planController.togglePlanStatus
);

// Delete plan (SuperAdmin only)
router.delete(
  '/:planId',
  authMiddleware.authorize('superAdmin'),
  planController.deletePlan
);

// Plan Features Management (SuperAdmin only)
router.get('/features/all', authMiddleware.authorize('superAdmin'), planController.getAllFeatures);
router.get('/:planId/features', authMiddleware.authorize('superAdmin'), planController.getPlanFeatures);
router.patch(
  '/:planId/features',
  authMiddleware.authorize('superAdmin'),
  validate(planSchemas.updatePlanFeatures),
  planController.updatePlanFeatures
);

module.exports = router;
