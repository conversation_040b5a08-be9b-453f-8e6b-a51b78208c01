import apiBridge from '@/lib/api/bridge';
import { ENDPOINTS } from '@/lib/api/contract';;
import { handleError, processApiResponse } from '../baseService';

/**
 * Get subscription history for current shop/admin
 * @returns {Promise<Array>} Array of subscription history records
 */
async function getSubscriptionHistory() {
  try {
    const response = await apiBridge.get(`${ENDPOINTS.SUBSCRIPTIONS.BASE}/history`);
    
    const result = processApiResponse(response);
    return result.history || result;
  } catch (error) {
    handleError(error, 'SubscriptionService.getSubscriptionHistory', true);
    throw error;
  }
}

export default getSubscriptionHistory; 
