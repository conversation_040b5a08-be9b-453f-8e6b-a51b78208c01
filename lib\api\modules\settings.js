/**
 * Settings API Module
 * Handles all settings-related API calls with exact backend matching
 * Based on backend routes: /api/settings/*
 */

import api from '../index';

/**
 * Get all system settings
 * GET /api/settings
 * Requires: admin+ authentication
 * Backend: settingsController.getSettings
 */
export const getSettings = async (category = 'all') => {
  const params = {};
  if (category !== 'all') {
    params.category = category;
  }
  
  const response = await api.get('/api/settings', { params });
  return response.data;
};

/**
 * Get available payment methods
 * GET /api/settings/payment-methods
 * Public route - no authentication required
 * Backend: settingsController.getPaymentMethods
 */
export const getPaymentMethods = async (context = 'general') => {
  const params = { context };
  
  const response = await api.get('/api/settings/payment-methods', { params });
  return response.data;
};

/**
 * Update payment method settings
 * PUT /api/settings/payment-methods
 * Requires: superadmin authentication
 * Backend: settingsController.updatePaymentMethods
 */
export const updatePaymentMethods = async (data) => {
  // Exact backend expected format:
  // { enableOnline, enableOffline, context, methods, shopId }
  const payload = {
    enableOnline: data.enableOnline,
    enableOffline: data.enableOffline,
    context: data.context || 'general',
    methods: data.methods || [],
    shopId: data.shopId || null
  };
  
  const response = await api.put('/api/settings/payment-methods', payload);
  return response.data;
};

/**
 * Get EVC payment credentials
 * GET /api/settings/evc-credentials
 * Requires: admin+ authentication
 * Backend: settingsController.getEVCCredentials
 */
export const getEvcCredentials = async (shopId = null) => {
  const params = {};
  if (shopId) {
    params.shopId = shopId;
  }
  
  const response = await api.get('/api/settings/evc-credentials', { params });
  return response.data;
};

/**
 * Set/Update EVC payment credentials
 * POST /api/settings/evc-credentials
 * Requires: admin+ authentication
 * Backend: settingsController.setEVCCredentials
 */
export const setEvcCredentials = async (credentials) => {
  // Exact backend expected format:
  // { merchantUId, apiUserId, apiKey, merchantNo, url, shopId }
  const payload = {
    merchantUId: credentials.merchantUId,
    apiUserId: credentials.apiUserId,
    apiKey: credentials.apiKey,
    merchantNo: credentials.merchantNo,
    url: credentials.url || 'https://api.waafipay.net/asm',
    shopId: credentials.shopId || null
  };
  
  const response = await api.post('/api/settings/evc-credentials', payload);
  return response.data;
};

/**
 * Test EVC payment credentials
 * POST /api/settings/test-evc-credentials
 * Requires: admin+ authentication
 * Backend: settingsController.testEVCCredentials
 */
export const testEvcCredentials = async (data = {}) => {
  // Backend expects: { shopId, phone?, amount? }
  const payload = {
    shopId: data.shopId || null
  };
  
  // Optional: for real payment test
  if (data.phone) payload.phone = data.phone;
  if (data.amount) payload.amount = data.amount;
  
  const response = await api.post('/api/settings/test-evc-credentials', payload);
  return response.data;
};

/**
 * Update security settings
 * PUT /api/settings
 * Requires: superadmin authentication
 * Backend: settingsController.updateSecuritySettings
 */
export const updateSecuritySettings = async (settings) => {
  // Exact backend expected format:
  // { passwordPolicy, sessionSettings, securityHeaders }
  const payload = {};
  
  if (settings.passwordPolicy) {
    payload.passwordPolicy = {
      minLength: settings.passwordPolicy.minLength,
      requireUppercase: settings.passwordPolicy.requireUppercase,
      requireLowercase: settings.passwordPolicy.requireLowercase,
      requireNumbers: settings.passwordPolicy.requireNumbers,
      requireSpecialChars: settings.passwordPolicy.requireSpecialChars,
      passwordExpiryDays: settings.passwordPolicy.passwordExpiryDays
    };
  }
  
  if (settings.sessionSettings) {
    payload.sessionSettings = {
      sessionTimeout: settings.sessionSettings.sessionTimeout,
      maxLoginAttempts: settings.sessionSettings.maxLoginAttempts,
      lockoutDuration: settings.sessionSettings.lockoutDuration
    };
  }
  
  if (settings.securityHeaders) {
    payload.securityHeaders = {
      enableCSP: settings.securityHeaders.enableCSP,
      enableHSTS: settings.securityHeaders.enableHSTS,
      enableXFrameOptions: settings.securityHeaders.enableXFrameOptions,
      enableXSSProtection: settings.securityHeaders.enableXSSProtection
    };
  }
  
  const response = await api.put('/api/settings', payload);
  return response.data;
};

/**
 * Update a specific setting by key
 * PATCH /api/settings/:key
 * Requires: admin+ authentication
 * Backend: settingsController.updateSettingByKey
 */
export const updateSettingByKey = async (key, value, shopId = null) => {
  // Exact backend expected format:
  // { value, shopId }
  const payload = {
    value: value
  };
  
  if (shopId !== null) {
    payload.shopId = shopId;
  }
  
  const response = await api.patch(`/api/settings/${encodeURIComponent(key)}`, payload);
  return response.data;
};

// Default export with all functions
const settingsAPI = {
  getSettings,
  getPaymentMethods,
  updatePaymentMethods,
  getEvcCredentials,
  setEvcCredentials,
  testEvcCredentials,
  updateSecuritySettings,
  updateSettingByKey
};

export default settingsAPI;
