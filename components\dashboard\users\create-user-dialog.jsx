"use client";

import { useState, useEffect } from 'react';
import { 
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter
} from '@/components/ui/dialog';
import { 
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from '@/components/ui/form';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { toast } from 'sonner';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import * as z from 'zod';
import UserService from './user-service';
import { Loader2 } from 'lucide-react';

// Create form schema with zod
const formSchema = z.object({
  fullName: z.string().min(3, {
    message: 'Full name must be at least 3 characters',
  }),
  email: z.string()
    .min(1, 'Email address is required')
    .email({
      message: 'Please enter a valid email address',
    }),
  phone: z.string().min(8, {
    message: 'Phone number must be valid',
  }),
  password: z.string().min(8, {
    message: 'Password must be at least 8 characters',
  }),
  role: z.enum(['admin', 'employee', 'superAdmin'], {
    required_error: 'Please select a role',
  }),
  shopId: z.string().optional(),
});

export function CreateUserDialog({ isOpen, onClose }) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [shops, setShops] = useState([
    { shopId: 'shop_123456', name: 'Smartphone Store' },
    { shopId: 'shop_234567', name: 'Fashion Boutique' },
    { shopId: 'shop_345678', name: 'Grocery Shop' },
    { shopId: 'shop_456789', name: 'Electronics Store' },
    { shopId: 'shop_567890', name: 'Pharmacy Plus' },
  ]);
  
  const userService = new UserService();
  
  // Initialize form
  const form = useForm({
    resolver: zodResolver(formSchema),
    defaultValues: {
      fullName: '',
      email: '',
      phone: '',
      password: '',
      role: 'employee',
      shopId: '',
    },
  });
  
  // Watch the role field to conditionally require shopId
  const role = form.watch('role');
  
  // Load shops when dialog opens (in a real app, this would call an API)
  useEffect(() => {
    // In a real implementation, you would fetch shops from the backend
    // For now, we're using the mock data defined above
  }, [isOpen]);
  
  // Handle form submission
  const onSubmit = async (data) => {
    try {
      setIsSubmitting(true);
      
      // If role is superAdmin, remove shopId
      if (data.role === 'superAdmin') {
        data.shopId = null;
      }
      
      // For standard roles, make sure shopId is provided
      if (data.role !== 'superAdmin' && !data.shopId) {
        toast.error('Shop is required for admin and employee roles');
        return;
      }
      
      // Add shop name for display purposes
      if (data.shopId) {
        const shop = shops.find(s => s.shopId === data.shopId);
        if (shop) {
          data.shopName = shop.name;
        }
      }
      
      // Create user
      await userService.createUser(data);
      
      // Close dialog and refresh parent
      onClose(true);
      
    } catch (error) {
      console.error('Error creating user:', error);
      toast.error(error.message || 'Failed to create user');
    } finally {
      setIsSubmitting(false);
    }
  };
  
  return (
    <Dialog open={isOpen} onOpenChange={(open) => !isSubmitting && onClose(false)}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Create New User</DialogTitle>
          <DialogDescription>
            Add a new user to the system. Users can be administrators or employees.
          </DialogDescription>
        </DialogHeader>
        
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-5">
            <FormField
              control={form.control}
              name="fullName"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Full Name</FormLabel>
                  <FormControl>
                    <Input placeholder="John Doe" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-5">
              <FormField
                control={form.control}
                name="email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Email</FormLabel>
                    <FormControl>
                      <Input placeholder="<EMAIL>" type="email" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <FormField
                control={form.control}
                name="phone"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Phone Number</FormLabel>
                    <FormControl>
                      <Input placeholder="+252612345678" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            
            <FormField
              control={form.control}
              name="password"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Password</FormLabel>
                  <FormControl>
                    <Input placeholder="Minimum 8 characters" type="password" {...field} />
                  </FormControl>
                  <FormDescription>
                    Password must be at least 8 characters and contain letters and numbers.
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-5">
              <FormField
                control={form.control}
                name="role"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Role</FormLabel>
                    <Select 
                      onValueChange={field.onChange} 
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select role" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="superAdmin">Super Admin</SelectItem>
                        <SelectItem value="admin">Admin</SelectItem>
                        <SelectItem value="employee">Employee</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormDescription>
                      Determines user permissions
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              {role !== 'superAdmin' && (
                <FormField
                  control={form.control}
                  name="shopId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Shop</FormLabel>
                      <Select 
                        onValueChange={field.onChange} 
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select shop" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {shops.map((shop) => (
                            <SelectItem key={shop.shopId} value={shop.shopId}>
                              {shop.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormDescription>
                        {role === 'superAdmin' ? 'Not required for Super Admin' : 'Required for this role'}
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              )}
            </div>
            
            <DialogFooter>
              <Button variant="outline" type="button" onClick={() => onClose(false)} disabled={isSubmitting}>
                Cancel
              </Button>
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                Create User
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
