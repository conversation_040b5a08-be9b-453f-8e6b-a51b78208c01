"use client";

import { useState, useEffect } from 'react';
import { 
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import {
  Ta<PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  Ta<PERSON><PERSON>ist,
  TabsTrigger
} from '@/components/ui/tabs';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  User,
  UserCog,
  Shield,
  Calendar,
  Phone,
  Mail,
  Building,
  Clock,
  AlertCircle,
  CheckCircle,
  History,
  Settings,
  Key
} from 'lucide-react';
import { formatDistanceToNow, format } from 'date-fns';
import { toast } from 'sonner';
import UserService from './user-service';

export function UserDetailsDialog({ isOpen, onClose, user }) {
  const [isLoading, setIsLoading] = useState(false);
  const [detailedUser, setDetailedUser] = useState(null);
  const [error, setError] = useState(null);
  const [currentTab, setCurrentTab] = useState('overview');
  
  // Initialize service once
  const userService = new UserService();
  
  // Load detailed user data with proper shop information
  useEffect(() => {
    // Reset state when dialog opens/closes or user changes
    if (!isOpen) {
      return;
    }
    
    const fetchUserDetails = async () => {
      // Don't attempt to fetch if no user or no userId
      if (!user || !user.userId) {
        setError('No user selected');
        return;
      }
      
      setIsLoading(true);
      setError(null);
      
      try {
        console.log(`Fetching details for user: ${user.userId}`);
        
        // Get detailed user from service
        const details = await userService.getUserById(user.userId);
        
        // Validate the response
        if (!details) {
          throw new Error('User details not found');
        }
        
        // Add additional check for shop information
        if (details.shopId && !details.shopName) {
          console.log(`Shop name missing for shop ID: ${details.shopId}, using fallback value`);
          // Use a fallback shop name
          details.shopName = `Shop ${details.shopId}`;
        }
        
        setDetailedUser(details);
      } catch (error) {
        console.error('Error fetching user details:', error);
        setError(error.message || 'Failed to load user details');
        
        // Ensure we have a fallback user object to prevent UI errors
        if (user) {
          setDetailedUser({
            ...user,
            // Add fallback values for essential properties
            fullName: user.fullName || 'Unknown User',
            email: user.email || 'No email available',
            role: user.role || 'Unknown',
            status: user.status || 'Unknown',
            // Add more fallback properties as needed
          });
        }
      } finally {
        setIsLoading(false);
      }
    };
    
    fetchUserDetails();
  }, [isOpen, user]);
  
  // Safe access helper for user properties
  const safeUserProperty = (property, fallback = 'N/A') => {
    // First try from detailedUser, then from user prop, then fallback
    return detailedUser?.[property] || user?.[property] || fallback;
  };
  
  // Format values safely
  const safeFormatDateTime = (dateValue) => {
    try {
      if (!dateValue) return 'N/A';
      return format(new Date(dateValue), 'PPpp');
    } catch (e) {
      console.error('Error formatting date:', e);
      return 'Invalid date';
    }
  };
  
  const safeFormatLastActive = (dateValue) => {
    try {
      if (!dateValue) return 'Never';
      return formatDistanceToNow(new Date(dateValue), { addSuffix: true });
    } catch (e) {
      console.error('Error formatting date:', e);
      return 'Unknown';
    }
  };
  
  const getRoleBadge = (role) => {
    switch (role) {
      case 'superAdmin':
        return <Badge variant="default" className="bg-purple-500">Super Admin</Badge>;
      case 'admin':
        return <Badge variant="default" className="bg-blue-500">Admin</Badge>;
      case 'employee':
        return <Badge variant="outline">Employee</Badge>;
      default:
        return <Badge variant="outline">{role}</Badge>;
    }
  };
  
  const getStatusBadge = (status) => {
    switch (status) {
      case 'active':
        return <Badge variant="outline" className="bg-green-500/10 text-green-500 border-green-500/20">Active</Badge>;
      case 'inactive':
        return <Badge variant="outline" className="bg-amber-500/10 text-amber-500 border-amber-500/20">Inactive</Badge>;
      case 'suspended':
        return <Badge variant="outline" className="bg-red-500/10 text-red-500 border-red-500/20">Suspended</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };
  
  const getRoleIcon = (role) => {
    switch (role) {
      case 'superAdmin':
        return <Shield className="h-5 w-5" />;
      case 'admin':
        return <UserCog className="h-5 w-5" />;
      default:
        return <User className="h-5 w-5" />;
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="dialog-content sm:max-w-[700px]">
        <DialogHeader className="dialog-header">
          <DialogTitle>{detailedUser ? `User Details: ${detailedUser.fullName}` : 'User Details'}</DialogTitle>
          <DialogDescription>View detailed information about this user.</DialogDescription>
        </DialogHeader>
        <div className="dialog-body">
        {isLoading ? (
          <div className="flex flex-col items-center justify-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mb-4"></div>
            <p className="text-muted-foreground">Loading user details...</p>
          </div>
        ) : error && !detailedUser ? (
          <div className="flex flex-col items-center justify-center py-12 text-center">
            <AlertCircle className="h-12 w-12 text-destructive mb-4" />
            <h3 className="text-lg font-medium mb-2">Error Loading User Details</h3>
            <p className="text-muted-foreground mb-4">{error}</p>
            <Button variant="outline" onClick={onClose}>Close</Button>
          </div>
        ) : (
          <>
            <DialogHeader>
              <DialogTitle className="flex items-center gap-2">
                <div className="w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center text-primary">
                  {getRoleIcon(safeUserProperty('role', 'employee'))}
                </div>
                <span>{safeUserProperty('fullName', 'User')}</span>
                {getRoleBadge(safeUserProperty('role', 'employee'))}
              </DialogTitle>
              <DialogDescription>
                {safeUserProperty('email', 'No email available')}
              </DialogDescription>
            </DialogHeader>
            
            <Tabs value={currentTab} onValueChange={setCurrentTab} className="w-full">
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="overview">Overview</TabsTrigger>
                <TabsTrigger value="details">Details</TabsTrigger>
              </TabsList>
              
              <TabsContent value="overview" className="space-y-4 mt-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Card>
                    <CardHeader>
                      <CardTitle>Contact Information</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-2">
                      <div className="flex items-center gap-2">
                        <Mail className="h-4 w-4 text-muted-foreground" />
                        <span className="font-medium">Email:</span>
                        <span>{safeUserProperty('email', 'No email available')}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Phone className="h-4 w-4 text-muted-foreground" />
                        <span className="font-medium">Phone:</span>
                        <span>{safeUserProperty('phone', 'No phone available')}</span>
                      </div>
                    </CardContent>
                  </Card>
                  
                  <Card>
                    <CardHeader>
                      <CardTitle>Status</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="flex items-center gap-2 mb-4">
                        <span className="font-medium">Current status:</span>
                        {getStatusBadge(safeUserProperty('status', 'unknown'))}
                      </div>
                      
                      {safeUserProperty('status') === 'suspended' && (
                        <div className="p-3 bg-red-50 dark:bg-red-950/20 rounded-md border border-red-200 dark:border-red-800">
                          <div className="flex items-center gap-2 text-red-700 dark:text-red-300">
                            <AlertCircle className="h-4 w-4" />
                            <span className="font-medium">Suspension Reason</span>
                          </div>
                          <p className="text-sm text-red-600 dark:text-red-300 mt-1">
                            {safeUserProperty('suspensionReason', 'No reason provided')}
                          </p>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                </div>
                
                <Card>
                  <CardHeader>
                    <CardTitle>Shop Assignment</CardTitle>
                  </CardHeader>
                  <CardContent>
                    {safeUserProperty('shopId') ? (
                      <div className="p-3 bg-blue-50 dark:bg-blue-950/20 rounded-md border border-blue-200 dark:border-blue-800">
                        <div className="flex items-center gap-2 text-blue-700 dark:text-blue-300">
                          <Building className="h-4 w-4" />
                          <span className="font-medium">{safeUserProperty('shopName', `Shop ${safeUserProperty('shopId')}`)}</span>
                        </div>
                        <div className="mt-1 text-sm text-blue-600 dark:text-blue-300">
                          Shop ID: <code className="bg-blue-100 dark:bg-blue-900/30 px-1 py-0.5 rounded text-xs">
                            {safeUserProperty('shopId', 'Unknown')}
                          </code>
                        </div>
                        <div className="mt-2 pt-2 border-t text-xs text-muted-foreground">
                          {safeUserProperty('role') === 'admin' ? (
                            <div className="flex items-center gap-1">
                              <Shield className="h-3 w-3" />
                              <span>Admin access to this shop</span>
                            </div>
                          ) : safeUserProperty('role') === 'employee' ? (
                            <div className="flex items-center gap-1">
                              <User className="h-3 w-3" />
                              <span>Employee access to this shop</span>
                            </div>
                          ) : null}
                        </div>
                      </div>
                    ) : safeUserProperty('role') === 'superAdmin' ? (
                      <div className="p-3 bg-purple-50 dark:bg-purple-950/20 rounded-md border border-purple-200 dark:border-purple-800">
                        <div className="flex items-center gap-2 text-purple-700 dark:text-purple-300">
                          <Shield className="h-4 w-4" />
                          <span className="font-medium">Global Access</span>
                        </div>
                        <p className="text-sm text-purple-600 dark:text-purple-300 mt-1">
                          Super Admin users have access to all shops in the system
                        </p>
                      </div>
                    ) : (
                      <div className="text-muted-foreground p-3 bg-amber-50 dark:bg-amber-950/20 rounded-md border border-amber-200 dark:border-amber-800">
                        <div className="flex items-center gap-2 text-amber-700 dark:text-amber-300">
                          <AlertCircle className="h-4 w-4" />
                          <span className="font-medium">No Shop Assigned</span>
                        </div>
                        <p className="text-sm text-amber-600 dark:text-amber-300 mt-1">
                          This user is not associated with any shop
                        </p>
                      </div>
                    )}
                  </CardContent>
                </Card>
                
                <Card>
                  <CardHeader>
                    <CardTitle>Account Dates</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-2">
                    <div className="flex items-center gap-2">
                      <Calendar className="h-4 w-4 text-muted-foreground" />
                      <span className="font-medium">Created:</span>
                      <span>{safeFormatDateTime(safeUserProperty('createdAt'))}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Clock className="h-4 w-4 text-muted-foreground" />
                      <span className="font-medium">Last Active:</span>
                      <span>{safeFormatLastActive(safeUserProperty('lastLoginAt'))}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <CheckCircle className="h-4 w-4 text-muted-foreground" />
                      <span className="font-medium">Verified:</span>
                      <span>{safeUserProperty('verified', false) ? 'Yes' : 'No'}</span>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
              
              <TabsContent value="details" className="space-y-4 mt-4">
                <Card>
                  <CardHeader>
                    <CardTitle>User Details</CardTitle>
                    <CardDescription>Complete information about this user</CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="space-y-2">
                      <h4 className="text-sm font-medium">User Identifier</h4>
                      <div className="flex gap-2 items-center">
                        <Key className="h-4 w-4 text-muted-foreground" />
                        <code className="bg-secondary px-2 py-1 rounded text-xs">
                          {safeUserProperty('userId', safeUserProperty('_id', 'Unknown ID'))}
                        </code>
                      </div>
                    </div>
                    
                    <div className="space-y-2">
                      <h4 className="text-sm font-medium">Authentication</h4>
                      <div className="grid grid-cols-2 gap-2">
                        <div className="flex items-center justify-between rounded border p-2">
                          <span className="text-sm">Email Verified</span>
                          <Badge variant={safeUserProperty('emailVerified') ? "default" : "outline"}>
                            {safeUserProperty('emailVerified') ? 'Yes' : 'No'}
                          </Badge>
                        </div>
                        <div className="flex items-center justify-between rounded border p-2">
                          <span className="text-sm">Account Verified</span>
                          <Badge variant={safeUserProperty('verified') ? "default" : "outline"}>
                            {safeUserProperty('verified') ? 'Yes' : 'No'}
                          </Badge>
                        </div>
                      </div>
                    </div>
                    
                    {safeUserProperty('updatedAt') && (
                      <div className="border-t pt-4 text-sm text-muted-foreground">
                        <p>Last updated: {safeFormatDateTime(safeUserProperty('updatedAt'))}</p>
                      </div>
                    )}
                  </CardContent>
                </Card>
                
                {error && (
                  <Card className="border-amber-200 dark:border-amber-800">
                    <CardHeader className="bg-amber-50 dark:bg-amber-950/20">
                      <CardTitle className="text-amber-700 dark:text-amber-300 flex items-center gap-2">
                        <AlertCircle className="h-4 w-4" />
                        Warning
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="pt-4">
                      <p className="text-sm text-amber-600 dark:text-amber-300">
                        There was a problem loading the complete user details: {error}
                      </p>
                      <p className="text-sm text-amber-600 dark:text-amber-300 mt-2">
                        Some information may be incomplete or missing.
                      </p>
                    </CardContent>
                    <CardFooter>
                      <Button 
                        variant="outline" 
                        size="sm" 
                        className="text-amber-700 dark:text-amber-300 border-amber-200 dark:border-amber-800"
                        onClick={() => {
                          // Retry fetching user details
                          if (user?.userId) {
                            setIsLoading(true);
                            setError(null);
                            userService.getUserById(user.userId)
                              .then(details => {
                                setDetailedUser(details);
                                toast.success("User details refreshed successfully");
                              })
                              .catch(err => {
                                setError(err.message || "Failed to refresh user details");
                                toast.error("Failed to refresh user details");
                              })
                              .finally(() => {
                                setIsLoading(false);
                              });
                          }
                        }}
                      >
                        Retry
                      </Button>
                    </CardFooter>
                  </Card>
                )}
              </TabsContent>
            </Tabs>
            
            <DialogFooter className="pt-4 border-t mt-6">
              <Button variant="outline" onClick={onClose}>Close</Button>
            </DialogFooter>
          </>
        )}
        </div>
      </DialogContent>
    </Dialog>
  );
}
