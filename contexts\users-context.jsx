"use client";

// This file is a compatibility layer to maintain imports from @/contexts/users-context
// It re-exports everything from user-context.jsx to avoid breaking existing code

import { UserProvider, useUserContext } from './user-context';
import { useUsersEnhanced } from '@/hooks/use-users-enhanced';

// Re-export with the expected names
export const UsersProvider = UserProvider;

// Export useUsers with the enhanced version for better compatibility
export function useUsers() {
  return useUsersEnhanced();
}

// Export default for compatibility
export default { UsersProvider, useUsers };
