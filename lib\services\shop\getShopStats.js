import apiBridge from '@/lib/api/bridge';
import { ENDPOINTS } from '@/lib/api/contract';
import { toast } from 'sonner';
import BaseService from '@/lib/services/baseService';

/**
 * Get shop statistics for SuperAdmin dashboard analytics
 * @param {Object} options - Statistics options
 * @param {string} options.startDate - Start date for statistics (ISO format: YYYY-MM-DD)
 * @param {string} options.endDate - End date for statistics (ISO format: YYYY-MM-DD)
 * @param {string} options.groupBy - Grouping option: 'day', 'week', 'month'
 * @param {string} options.status - Filter by shop status
 * @param {boolean} options.verified - Filter by verification status
 * @returns {Promise<Object>} Shop statistics data
 */
async function getShopStats(options = {}) {
  try {
    // Prepare query parameters for SuperAdmin stats endpoint
    const params = {};
    
    if (options.startDate) {
      params.startDate = options.startDate;
    }
    
    if (options.endDate) {
      params.endDate = options.endDate;
    }
    
    if (options.groupBy) {
      params.groupBy = options.groupBy;
    }
    
    if (options.status) {
      params.status = options.status;
    }
    
    if (typeof options.verified === 'boolean') {
      params.verified = options.verified;
    }
    
    // Fetching shop statistics
    
    // Make API request using SuperAdmin stats endpoint
    const response = await apiBridge.get(ENDPOINTS.SHOPS.STATS, { 
      params,
      useCache: false // Stats should be fresh
    });
    
    // Process response - Match backend format exactly
    if (response.data && response.data.success) {
      const statsData = response.data.data;
      // Statistics retrieved successfully
      
      // Simple stats processing matching backend response format:
      // { _id: null, totalShops: 18, activeShops: 15, pendingShops: 2, suspendedShops: 0 }
              const processedStats = {
          totalShops: statsData.totalShops || 0,
          activeShops: statsData.activeShops || 0,
          pendingShops: statsData.pendingShops || 0,
          suspendedShops: statsData.suspendedShops || 0,
          inactiveShops: statsData.inactiveShops || 0
        };
      
      return processedStats;
    }
    
    // Handle unexpected response
    console.error('[SuperAdminShopStatsService] Unexpected API response format:', response.data);
    toast.error('Failed to load shop statistics: Unexpected response format');
    return null;
  } catch (error) {
    console.error('[SuperAdminShopStatsService] Error fetching shop statistics:', error);
    
    // Enhanced error handling for SuperAdmin operations
    if (error.response?.status === 401) {
      BaseService.handleError({ message: 'Unauthorized: SuperAdmin access required' }, 'SuperAdminShopStatsService.getShopStats', true);
    } else if (error.response?.status === 403) {
      BaseService.handleError({ message: 'Forbidden: Only SuperAdmins can view shop statistics' }, 'SuperAdminShopStatsService.getShopStats', true);
    } else {
      BaseService.handleError(error, 'SuperAdminShopStatsService.getShopStats', true);
    }
    
    throw error;
  }
}

export default getShopStats; 