/**
 * Test script to validate subscription creation fixes
 * Run this script to test the subscription ID generation and creation flow
 */

const mongoose = require('mongoose');
require('dotenv').config();

// Import models and services
const { Subscription, Plan, Shop, User } = require('./src/models');
const idGenerator = require('./src/utils/generators/idGenerator');
const createSubscription = require('./src/services/Subscription/createSubscription');

async function connectDB() {
  try {
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/deyncare', {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    console.log('✅ Connected to MongoDB');
  } catch (error) {
    console.error('❌ MongoDB connection error:', error);
    process.exit(1);
  }
}

async function testSubscriptionIdGeneration() {
  console.log('\n🧪 Testing Subscription ID Generation...');
  
  try {
    // Test generating multiple subscription IDs
    const ids = [];
    for (let i = 0; i < 5; i++) {
      const id = await idGenerator.generateSubscriptionId(Subscription);
      ids.push(id);
      console.log(`Generated ID ${i + 1}: ${id}`);
    }
    
    // Validate format
    const validFormat = ids.every(id => /^SUB\d{3,}$/.test(id));
    console.log(`✅ All IDs match format SUB###: ${validFormat}`);
    
    // Check for sequential nature
    const numericParts = ids.map(id => parseInt(id.replace('SUB', '')));
    const isSequential = numericParts.every((num, index) => {
      if (index === 0) return true;
      return num === numericParts[index - 1] + 1;
    });
    console.log(`✅ IDs are sequential: ${isSequential}`);
    
    return { success: true, ids, isSequential };
  } catch (error) {
    console.error('❌ Subscription ID generation test failed:', error.message);
    return { success: false, error: error.message };
  }
}

async function testSubscriptionCreation() {
  console.log('\n🧪 Testing Subscription Creation Service...');
  
  try {
    // Find or create a test plan
    let testPlan = await Plan.findOne({ type: 'monthly' });
    if (!testPlan) {
      console.log('Creating test plan...');
      testPlan = new Plan({
        planId: 'PLAN_TEST_001',
        name: 'Test Monthly Plan',
        displayName: 'Test Monthly Plan',
        type: 'monthly',
        pricing: {
          basePrice: 29.99,
          currency: 'USD',
          billingCycle: 'monthly'
        },
        features: ['basic_features'],
        limits: { products: 100 }
      });
      await testPlan.save();
    }
    
    // Test subscription creation
    const testShopId = 'SHOP_TEST_001';
    const subscriptionData = {
      shopId: testShopId,
      planType: 'monthly',
      planId: testPlan.planId,
      planName: testPlan.displayName,
      pricing: {
        basePrice: testPlan.pricing.basePrice,
        currency: testPlan.pricing.currency
      },
      paymentMethod: 'admin_created',
      paymentDetails: {
        verified: true,
        approvedBy: 'test_script',
        approvedAt: new Date(),
        approvalNotes: 'Test subscription creation'
      }
    };
    
    const createdSubscription = await createSubscription(subscriptionData, {
      actorId: 'test_script',
      actorRole: 'superAdmin'
    });
    
    console.log(`✅ Subscription created successfully: ${createdSubscription.subscriptionId}`);
    console.log(`   - Shop ID: ${createdSubscription.shopId}`);
    console.log(`   - Plan Type: ${createdSubscription.plan.type}`);
    console.log(`   - Status: ${createdSubscription.status}`);
    console.log(`   - Payment Method: ${createdSubscription.paymentMethod}`);
    
    // Validate the created subscription
    const isValidFormat = /^SUB\d{3,}$/.test(createdSubscription.subscriptionId);
    console.log(`✅ Subscription ID format valid: ${isValidFormat}`);
    
    // Clean up test subscription
    await Subscription.deleteOne({ subscriptionId: createdSubscription.subscriptionId });
    console.log('🧹 Test subscription cleaned up');
    
    return { success: true, subscription: createdSubscription };
  } catch (error) {
    console.error('❌ Subscription creation test failed:', error.message);
    return { success: false, error: error.message };
  }
}

async function testExistingSubscriptions() {
  console.log('\n🧪 Analyzing Existing Subscriptions...');
  
  try {
    const subscriptions = await Subscription.find({}, { subscriptionId: 1 }).sort({ createdAt: 1 }).limit(20);
    
    console.log(`Found ${subscriptions.length} existing subscriptions:`);
    
    const validIds = [];
    const invalidIds = [];
    
    subscriptions.forEach(sub => {
      if (/^SUB\d{3,}$/.test(sub.subscriptionId)) {
        validIds.push(sub.subscriptionId);
      } else {
        invalidIds.push(sub.subscriptionId);
      }
    });
    
    console.log(`✅ Valid format IDs (${validIds.length}):`, validIds.slice(0, 10));
    if (validIds.length > 10) console.log(`   ... and ${validIds.length - 10} more`);
    
    if (invalidIds.length > 0) {
      console.log(`❌ Invalid format IDs (${invalidIds.length}):`, invalidIds);
    }
    
    return { success: true, validCount: validIds.length, invalidCount: invalidIds.length };
  } catch (error) {
    console.error('❌ Existing subscriptions analysis failed:', error.message);
    return { success: false, error: error.message };
  }
}

async function runTests() {
  console.log('🚀 Starting Subscription Creation Tests...');
  
  await connectDB();
  
  const results = {
    idGeneration: await testSubscriptionIdGeneration(),
    subscriptionCreation: await testSubscriptionCreation(),
    existingAnalysis: await testExistingSubscriptions()
  };
  
  console.log('\n📊 Test Results Summary:');
  console.log('========================');
  console.log(`ID Generation: ${results.idGeneration.success ? '✅ PASSED' : '❌ FAILED'}`);
  console.log(`Subscription Creation: ${results.subscriptionCreation.success ? '✅ PASSED' : '❌ FAILED'}`);
  console.log(`Existing Analysis: ${results.existingAnalysis.success ? '✅ PASSED' : '❌ FAILED'}`);
  
  if (results.existingAnalysis.success) {
    console.log(`\nExisting Subscriptions: ${results.existingAnalysis.validCount} valid, ${results.existingAnalysis.invalidCount} invalid`);
  }
  
  await mongoose.disconnect();
  console.log('\n🔌 Disconnected from MongoDB');
  
  const allPassed = Object.values(results).every(result => result.success);
  if (allPassed) {
    console.log('\n🎉 All tests PASSED! Subscription fixes are working correctly.');
  } else {
    console.log('\n⚠️  Some tests FAILED. Please review the errors above.');
  }
  
  process.exit(allPassed ? 0 : 1);
}

// Run tests if this file is executed directly
if (require.main === module) {
  runTests().catch(error => {
    console.error('❌ Test execution failed:', error);
    process.exit(1);
  });
}

module.exports = { runTests, testSubscriptionIdGeneration, testSubscriptionCreation };
