"use client";

import { useState, useEffect, useCallback, useMemo } from 'react';
import { useAuth } from '@/contexts/auth-context';
import ReportService from '@/components/dashboard/reports/report-service';
import { DataTable } from '@/components/dashboard/common/data-table';
import { ReportDetailsDialog } from '@/components/dashboard/reports/report-details';
import { GenerateReportDialog } from '@/components/dashboard/reports/generate-report-dialog';
import { EmailReportDialog } from '@/components/dashboard/reports/email-report-dialog';
import { DeleteReportDialog } from '@/components/dashboard/reports/delete-report-dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { 
  Tabs, 
  TabsContent, 
  TabsList, 
  TabsTrigger 
} from '@/components/ui/tabs';
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator
} from '@/components/ui/dropdown-menu';
import { 
  Select, 
  SelectTrigger, 
  SelectValue, 
  SelectContent, 
  SelectItem 
} from '@/components/ui/select';
import { toast } from 'sonner';
import { 
  Search, 
  Plus, 
  MoreVertical, 
  Eye, 
  Send, 
  FileText, 
  Trash2, 
  ArrowDownToLine, 
  BarChart3, 
  Calendar
} from 'lucide-react';

export default function ReportsPage() {
  const { user } = useAuth();
  
  // State for reports and filtering
  const [reports, setReports] = useState([]);
  const [isLoadingReports, setIsLoadingReports] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [typeFilter, setTypeFilter] = useState('all');
  const [formatFilter, setFormatFilter] = useState('all');
  const [shopFilter, setShopFilter] = useState('all');
  const [shops, setShops] = useState([]);
  
  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [totalReports, setTotalReports] = useState(0);
  const [totalPages, setTotalPages] = useState(1);
  
  // Dialog state
  const [selectedReport, setSelectedReport] = useState(null);
  const [showDetailsDialog, setShowDetailsDialog] = useState(false);
  const [showGenerateDialog, setShowGenerateDialog] = useState(false);
  const [showEmailDialog, setShowEmailDialog] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  
  // Service instance
  const reportService = useMemo(() => new ReportService(), []);
  
  // Fetch reports data from API
  const fetchReports = useCallback(async () => {
    try {
      setIsLoadingReports(true);
      
      const filters = {
        type: typeFilter !== 'all' ? typeFilter : undefined,
        format: formatFilter !== 'all' ? formatFilter : undefined,
        shopId: shopFilter !== 'all' ? shopFilter : undefined,
        search: searchQuery || undefined
      };
      
      const result = await reportService.getAllReports(filters, currentPage, pageSize);
      
      setReports(result.reports);
      setTotalReports(result.pagination.totalDocs);
      setTotalPages(result.pagination.totalPages);
    } catch (error) {
      console.error('Error fetching reports:', error);
      toast.error('Failed to load reports');
    } finally {
      setIsLoadingReports(false);
    }
  }, [reportService, typeFilter, formatFilter, shopFilter, searchQuery, currentPage, pageSize]);
  
  // Load initial shops list for filtering (SuperAdmin only)
  useEffect(() => {
    if (user?.role === 'superAdmin') {
      // In real implementation, fetch shops from API
      // For now, use mock data
      setShops([
        { shopId: '1', name: 'Shop 1' },
        { shopId: '2', name: 'Shop 2' },
        { shopId: '3', name: 'Shop 3' },
        { shopId: '4', name: 'Shop 4' },
        { shopId: '5', name: 'Shop 5' }
      ]);
    }
  }, [user]);
  
  // Load reports on initial render and when filters change
  useEffect(() => {
    fetchReports();
  }, [fetchReports]);
  
  // Handle search input
  const handleSearch = (event) => {
    setSearchQuery(event.target.value);
    setCurrentPage(1); // Reset to first page when searching
  };
  
  // Handle type filter change
  const handleTypeFilterChange = (type) => {
    setTypeFilter(type);
    setCurrentPage(1); // Reset to first page when filtering
  };
  
  // Handle format filter change
  const handleFormatFilterChange = (format) => {
    setFormatFilter(format);
    setCurrentPage(1); // Reset to first page when filtering
  };
  
  // Handle shop filter change (SuperAdmin only)
  const handleShopFilterChange = (shop) => {
    setShopFilter(shop);
    setCurrentPage(1); // Reset to first page when filtering
  };
  
  // Handle page change
  const handlePageChange = (page) => {
    setCurrentPage(page);
  };
  
  // Handle page size change
  const handlePageSizeChange = (size) => {
    setPageSize(size);
    setCurrentPage(1); // Reset to first page when changing page size
  };
  
  // Handle view report details
  const handleViewReportDetails = (report) => {
    setSelectedReport(report);
    setShowDetailsDialog(true);
  };
  
  // Handle generate report
  const handleGenerateReport = () => {
    setShowGenerateDialog(true);
  };
  
  // Handle email report
  const handleEmailReport = (report) => {
    setSelectedReport(report);
    setShowEmailDialog(true);
  };
  
  // Handle delete report
  const handleDeleteReport = (report) => {
    setSelectedReport(report);
    setShowDeleteDialog(true);
  };
  
  // Handle dialog close
  const handleDialogClose = (refresh = false) => {
    setShowDetailsDialog(false);
    setShowGenerateDialog(false);
    setShowEmailDialog(false);
    setShowDeleteDialog(false);
    
    // Refresh reports list if requested
    if (refresh) {
      fetchReports();
    }
  };
  
  // Handle report download
  const handleDownloadReport = (report) => {
    // In a real implementation, this would download the actual file
    const url = report?.url;
    
    toast.success('Downloading report...');
    
    // Mock download for development
    setTimeout(() => {
      window.open(url, '_blank');
    }, 500);
  };
  
  // Define table columns
  const columns = useMemo(() => [
    {
      header: 'Title',
      accessorKey: 'title',
      cell: ({ row }) => {
        const report = row.original;
        if (!report) return null;
        
        return (
          <div className="flex flex-col">
            <span className="font-medium">{report.title}</span>
            <span className="text-xs text-muted-foreground">
              {report.reportId}
            </span>
          </div>
        );
      }
    },
    {
      header: 'Type',
      accessorKey: 'type',
      cell: ({ row }) => {
        const report = row.original;
        if (!report) return null;
        
        const colors = {
          'debt': 'bg-blue-50 text-blue-600 hover:bg-blue-100',
          'sales': 'bg-green-50 text-green-600 hover:bg-green-100',
          'ml-risk': 'bg-purple-50 text-purple-600 hover:bg-purple-100',
          'pos-profit': 'bg-amber-50 text-amber-600 hover:bg-amber-100'
        };
        
        const labels = {
          'debt': 'Debt Report',
          'sales': 'Sales Report',
          'ml-risk': 'Risk Analysis',
          'pos-profit': 'POS Profit'
        };
        
        return (
          <Badge variant="outline" className={colors[report.type] || ''}>
            {labels[report.type] || report.type}
          </Badge>
        );
      }
    },
    {
      header: 'Format',
      accessorKey: 'format',
      cell: ({ row }) => {
        const report = row.original;
        if (!report) return null;
        
        const colors = {
          'pdf': 'bg-red-50 text-red-600 hover:bg-red-100',
          'csv': 'bg-emerald-50 text-emerald-600 hover:bg-emerald-100',
          'excel': 'bg-green-50 text-green-600 hover:bg-green-100'
        };
        
        const labels = {
          'pdf': 'PDF',
          'csv': 'CSV',
          'excel': 'Excel'
        };
        
        return (
          <Badge variant="outline" className={colors[report.format] || ''}>
            {labels[report.format] || report.format}
          </Badge>
        );
      }
    },
    {
      header: 'Date Generated',
      accessorKey: 'generatedAt',
      cell: ({ row }) => {
        const report = row.original;
        if (!report || !report.generatedAt) return <span>N/A</span>;
        
        try {
          return reportService.formatDate(report.generatedAt);
        } catch (error) {
          return <span>Invalid date</span>;
        }
      }
    },
    {
      header: 'Shop',
      accessorKey: 'shopId',
      cell: ({ row }) => {
        const report = row.original;
        if (!report) return null;
        
        if (report.shopId === 'system') {
          return (
            <Badge variant="outline" className="bg-indigo-50 text-indigo-600">
              System-wide
            </Badge>
          );
        }
        
        // In real implementation, fetch shop name from API or use a map
        const shopName = report.shopId.replace('shop_', 'Shop ').trim();
        
        return <span>{shopName}</span>;
      }
    },
    {
      header: '',
      id: 'actions',
      cell: ({ row }) => {
        const report = row.original;
        if (!report) return null;
        
        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="p-0 h-8 w-8">
                <MoreVertical className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="bg-background border border-border">
              <DropdownMenuItem onClick={() => handleViewReportDetails(report)} className="bg-background hover:bg-muted">
                <Eye className="mr-2 h-4 w-4" />
                View Details
              </DropdownMenuItem>
              
              <DropdownMenuItem onClick={() => handleDownloadReport(report)} className="bg-background hover:bg-muted">
                <ArrowDownToLine className="mr-2 h-4 w-4" />
                Download
              </DropdownMenuItem>
              
              <DropdownMenuItem onClick={() => handleEmailReport(report)} className="bg-background hover:bg-muted">
                <Send className="mr-2 h-4 w-4" />
                Email Report
              </DropdownMenuItem>
              
              <DropdownMenuSeparator />
              
              <DropdownMenuItem
                onClick={() => handleDeleteReport(report)}
                className="text-red-600 bg-background hover:bg-red-50"
              >
                <Trash2 className="mr-2 h-4 w-4" />
                Delete Report
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        );
      }
    }
  ], [reportService]);

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold tracking-tight">Reports Management</h1>
        
        <Button onClick={handleGenerateReport}>
          <Plus className="mr-2 h-4 w-4" /> Generate Report
        </Button>
      </div>
      
      <div className="flex flex-col space-y-4">
        <div className="flex flex-col sm:flex-row gap-4 items-end">
          <div className="w-full max-w-sm">
            <div className="flex space-x-2">
              <div className="flex-1 relative">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  type="search"
                  placeholder="Search reports..."
                  className="pl-8"
                  value={searchQuery}
                  onChange={handleSearch}
                />
              </div>
            </div>
          </div>
          
          <div className="flex flex-wrap gap-4">
            <div className="min-w-[150px]">
              <Select
                value={typeFilter}
                onValueChange={handleTypeFilterChange}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Filter by type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Types</SelectItem>
                  <SelectItem value="sales">Sales Reports</SelectItem>
                  <SelectItem value="debt">Debt Reports</SelectItem>
                  <SelectItem value="ml-risk">Risk Analysis</SelectItem>
                  <SelectItem value="pos-profit">POS Profit</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="min-w-[150px]">
              <Select
                value={formatFilter}
                onValueChange={handleFormatFilterChange}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Filter by format" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Formats</SelectItem>
                  <SelectItem value="pdf">PDF</SelectItem>
                  <SelectItem value="excel">Excel</SelectItem>
                  <SelectItem value="csv">CSV</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            {user?.role === 'superAdmin' && (
              <div className="min-w-[150px]">
                <Select
                  value={shopFilter}
                  onValueChange={handleShopFilterChange}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Filter by shop" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Shops</SelectItem>
                    <SelectItem value="system">System-wide Only</SelectItem>
                    {shops.map((shop) => (
                      <SelectItem key={shop.shopId} value={shop.shopId}>
                        {shop.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            )}
          </div>
        </div>
        
        <DataTable
          columns={columns}
          data={reports}
          isLoading={isLoadingReports}
          pagination={{
            currentPage,
            pageSize,
            totalPages,
            totalItems: totalReports,
            onPageChange: handlePageChange,
            onPageSizeChange: handlePageSizeChange
          }}
          emptyState={
            <div className="flex flex-col items-center justify-center py-8">
              <FileText className="h-12 w-12 text-muted-foreground mb-2" />
              <h3 className="text-lg font-medium">No reports found</h3>
              <p className="text-sm text-muted-foreground mb-4">
                {searchQuery || typeFilter !== 'all' || formatFilter !== 'all' || (user?.role === 'superAdmin' && shopFilter !== 'all')
                  ? 'Try adjusting your filters'
                  : 'Get started by generating your first report'}
              </p>
              <Button onClick={handleGenerateReport}>
                <Plus className="mr-2 h-4 w-4" />
                Generate Report
              </Button>
            </div>
          }
        />
      </div>
      
      {/* Dialogs */}
      {showDetailsDialog && selectedReport && (
        <ReportDetailsDialog
          isOpen={showDetailsDialog}
          onClose={() => handleDialogClose()}
          report={selectedReport}
          onEmailReport={handleEmailReport}
        />
      )}
      
      {showGenerateDialog && (
        <GenerateReportDialog
          isOpen={showGenerateDialog}
          onClose={(refresh) => handleDialogClose(refresh)}
          shops={shops}
        />
      )}
      
      {showEmailDialog && selectedReport && (
        <EmailReportDialog
          isOpen={showEmailDialog}
          onClose={(refresh) => handleDialogClose(refresh)}
          report={selectedReport}
        />
      )}
      
      {showDeleteDialog && selectedReport && (
        <DeleteReportDialog
          isOpen={showDeleteDialog}
          onClose={(refresh) => handleDialogClose(refresh)}
          report={selectedReport}
        />
      )}
    </div>
  );
}
