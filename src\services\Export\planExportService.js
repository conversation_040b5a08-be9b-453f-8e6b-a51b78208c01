/**
 * Plan Export Service
 * Handles export functionality for plans
 */
const ExportService = require('./exportService');
const PlanService = require('../PlanService');
const { logError } = require('../../utils');

class PlanExportService {
  /**
   * Get fields configuration for plan exports
   * @returns {Array} Array of field configurations
   */
  static getExportFields() {
    return [
      { label: 'Plan Name', key: 'name' },
      { label: 'Display Name', key: 'displayName' },
      { label: 'Type', key: 'type' },
      { label: 'Base Price', key: 'pricing.basePrice' },
      { label: 'Currency', key: 'pricing.currency' },
      { label: 'Billing Cycle', key: 'pricing.billingCycle' },
      { label: 'Trial Days', key: 'pricing.trialDays' },
      { label: 'Status', key: 'isActive' },
      { label: 'Created At', key: 'createdAt' },
      { label: 'Updated At', key: 'updatedAt' }
    ];
  }

  /**
   * Export plans to CSV
   * @param {Object} filter - Filter criteria
   * @returns {String} CSV string
   */
  static async exportToCSV(filter = {}) {
    try {
      const plans = await PlanService.getAllPlans(true, filter);
      const fields = this.getExportFields();
      return await ExportService.toCSV(plans, fields);
    } catch (error) {
      logError('Failed to export plans to CSV', 'PlanExportService', error);
      throw error;
    }
  }

  /**
   * Export plans to Excel
   * @param {Object} filter - Filter criteria
   * @returns {Buffer} Excel file buffer
   */
  static async exportToExcel(filter = {}) {
    try {
      const plans = await PlanService.getAllPlans(true, filter);
      const fields = this.getExportFields();
      return await ExportService.toExcel(plans, fields);
    } catch (error) {
      logError('Failed to export plans to Excel', 'PlanExportService', error);
      throw error;
    }
  }
}

module.exports = PlanExportService; 