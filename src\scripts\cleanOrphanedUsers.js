const mongoose = require('mongoose');
const { User } = require('../models');
const { logInfo, logSuccess, logError } = require('../utils');

/**
 * Clean up orphaned users from failed registration transactions
 * These are users with status 'pending_email_verification' but no associated shop or incomplete registration
 */
const cleanOrphanedUsers = async () => {
  try {
    await mongoose.connect(process.env.MONGODB_URI);
    logInfo('Connected to database for cleanup', 'CleanOrphanedUsers');

    // Find users with pending email verification that are more than 1 hour old
    const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);
    
    const orphanedUsers = await User.find({
      status: 'pending_email_verification',
      createdAt: { $lt: oneHourAgo },
      isDeleted: false
    });

    if (orphanedUsers.length === 0) {
      logInfo('No orphaned users found', 'CleanOrphanedUsers');
      return;
    }

    logInfo(`Found ${orphanedUsers.length} orphaned users to clean up`, 'CleanOrphanedUsers');

    // Soft delete orphaned users
    const result = await User.updateMany(
      {
        status: 'pending_email_verification',
        createdAt: { $lt: oneHourAgo },
        isDeleted: false
      },
      {
        $set: {
          isDeleted: true,
          deletedAt: new Date(),
          status: 'deleted_orphaned'
        }
      }
    );

    logSuccess(`Successfully cleaned up ${result.modifiedCount} orphaned users`, 'CleanOrphanedUsers');
    
    // Log the emails that were cleaned up for debugging
    orphanedUsers.forEach(user => {
      logInfo(`Cleaned orphaned user: ${user.email} (${user.userId})`, 'CleanOrphanedUsers');
    });

  } catch (error) {
    logError('Error cleaning orphaned users', 'CleanOrphanedUsers', error);
  } finally {
    await mongoose.disconnect();
  }
};

// Run the cleanup if this script is executed directly
if (require.main === module) {
  cleanOrphanedUsers()
    .then(() => {
      console.log('Cleanup completed');
      process.exit(0);
    })
    .catch(error => {
      console.error('Cleanup failed:', error);
      process.exit(1);
    });
}

module.exports = cleanOrphanedUsers; 