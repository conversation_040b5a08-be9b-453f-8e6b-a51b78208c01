const { Subscription, Plan } = require('../../models');
const { AppError, idGenerator, logSuccess, logError } = require('../../utils');

/**
 * Create a subscription record for a shop
 * @param {Object} subscriptionData - Subscription data
 * @param {Object} options - Additional options
 * @returns {Promise<Object>} Created subscription
 */
const createSubscription = async (subscriptionData, options = {}) => {
  try {
    const {
      shopId,
      planType = 'trial',
      planId = null,
      planName = null,
      pricing = {},
      paymentMethod = 'free',
      paymentDetails = null,
      discountDetails = null,
      session = null
    } = subscriptionData;

    if (!shopId) {
      throw new AppError('Shop ID is required to create subscription', 400, 'missing_shop_id');
    }

    // Fetch plan details and pricing (dynamic pricing system)
    let finalPlanName = planName;
    let finalPlanType = planType;
    let finalPricing = pricing;

    // Try to get pricing from Plan model (either by planId or planType)
    try {
      let plan = null;

      // First priority: Use specific planId if provided
      if (planId) {
        plan = await Plan.findOne({ planId: planId });
        if (plan) {
          finalPlanName = plan.displayName || plan.name;
          finalPlanType = plan.type || planType;
        } else {
          logError(`Plan with planId ${planId} not found, falling back to type-based lookup`, 'SubscriptionService');
        }
      }

      // Second priority: Find active plan by type if no specific plan found
      if (!plan) {
        const typePlans = await Plan.find({
          type: finalPlanType,
          isActive: true,
          isDeleted: { $ne: true }
        }).sort({ displayOrder: 1 });

        if (typePlans.length > 0) {
          plan = typePlans[0];
          finalPlanName = finalPlanName || plan.displayName || plan.name;
        }
      }

      // Use Plan model pricing if found
      if (plan && plan.pricing) {
        finalPricing = {
          basePrice: plan.pricing.basePrice,
          currency: plan.pricing.currency || 'USD',
          billingCycle: plan.pricing.billingCycle,
          trialDays: plan.pricing.trialDays || 0,
          setupFee: plan.pricing.setupFee || 0,
          // Preserve any discount details passed in
          discount: pricing.discount
        };
      } else {
        // Fallback pricing if no plan found
        const fallbackPricing = {
          trial: { basePrice: 0, billingCycle: 'one-time' },
          monthly: { basePrice: 10, billingCycle: 'monthly' },
          yearly: { basePrice: 96, billingCycle: 'yearly' }
        };

        const fallback = fallbackPricing[finalPlanType];
        if (fallback) {
          finalPricing = {
            basePrice: fallback.basePrice,
            currency: 'USD',
            billingCycle: fallback.billingCycle,
            trialDays: finalPlanType === 'trial' ? 14 : 0,
            setupFee: 0,
            // Preserve any discount details passed in
            discount: pricing.discount
          };
          logError(`Using fallback pricing for ${finalPlanType}: $${fallback.basePrice}`, 'SubscriptionService');
        }
      }
    } catch (error) {
      logError(`Error fetching plan pricing: ${error.message}`, 'SubscriptionService', error);
    }

    // Set default plan name if still not available
    if (!finalPlanName) {
      finalPlanName = finalPlanType === 'trial' ? 'Trial Plan' :
                     finalPlanType === 'monthly' ? 'Monthly Plan' :
                     finalPlanType === 'yearly' ? 'Yearly Plan' : 'Default Plan';
    }

    // Generate unique subscription ID using proper sequential generator
    const subscriptionId = await idGenerator.generateSubscriptionId(Subscription);

    // Determine subscription status based on payment method and plan type
    let subscriptionStatus = 'active';
    if (finalPlanType === 'trial') {
      subscriptionStatus = 'trial';
    } else if (paymentMethod === 'offline' && !paymentDetails?.verified) {
      subscriptionStatus = 'pending_payment';
    } else if (paymentMethod === 'admin_created' && paymentDetails?.verified) {
      subscriptionStatus = 'active'; // SuperAdmin created subscriptions are immediately active
    }

    // Log subscription creation details for debugging
    console.log(`Creating subscription: ID will be generated, Shop: ${shopId}, Plan: ${finalPlanType}, Status: ${subscriptionStatus}, Payment Method: ${paymentMethod}`);
    if (paymentDetails?.verified) {
      console.log(`Payment pre-verified by: ${paymentDetails.approvedBy || 'system'}`);
    }

    // Create subscription object with required plan fields
    const subscription = new Subscription({
      subscriptionId,
      shopId,
      planId,
      plan: {
        name: finalPlanName,
        type: finalPlanType
      },
      pricing: finalPricing,
      paymentMethod,
      paymentDetails,
      discountDetails,
      status: subscriptionStatus,
      createdAt: new Date()
    });

    // Save with session if provided
    if (session) {
      await subscription.save({ session });
    } else {
      await subscription.save();
    }

    logSuccess(`Subscription created: ${subscriptionId} for shop: ${shopId}`, 'SubscriptionService');
    return subscription;

  } catch (error) {
    if (error instanceof AppError) {
      throw error;
    }
    
    logError(`Error creating subscription: ${error.message}`, 'SubscriptionService', error);
    throw new AppError('Failed to create subscription', 500, 'subscription_creation_error');
  }
};

module.exports = createSubscription; 
