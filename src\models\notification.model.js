const mongoose = require('mongoose');
const { generateCode } = require('../utils/generators/generateCode');

const notificationSchema = new mongoose.Schema({
  notificationId: {
    type: String,
    required: true,
    unique: true,
    trim: true,
    default: () => generateCode('NOT', 8)
  },
  shopId: {
    type: String,
    required: true,
    trim: true,
    index: true
  },
  recipient: {
    type: String,
    required: true,
    trim: true,
    index: true
  },
  // Enhanced: Recipient type with expanded options
  recipientType: {
    type: String,
    enum: ['user', 'customer', 'admin', 'superAdmin', 'system', 'shopOwner', 'broadcast', 'role'],
    default: 'user'
  },
  // Enhanced: Recipient name for better readability in logs
  recipientName: {
    type: String,
    trim: true
  },
  // Enhanced: Notification delivery type
  type: {
    type: String,
    enum: ['SMS', 'Push', 'Email', 'InApp', 'WhatsApp'],
    required: true
  },
  // Enhanced: Priority level affects delivery order
  priority: {
    type: String,
    enum: ['low', 'normal', 'medium', 'high', 'urgent'],
    default: 'medium',
    index: true
  },
  // Enhanced: Category for comprehensive filtering
  category: {
    type: String,
    enum: [
      'transactional', 'promotional', 'reminder', 'alert', 'system',
      'subscription', 'debt', 'payment', 'welcome', 'security',
      'maintenance', 'announcement', 'verification', 'system_broadcast', 'admin_communication'
    ],
    default: 'transactional',
    index: true
  },
  // Enhanced: Message content with length validation
  message: {
    type: String,
    required: true,
    trim: true,
    maxlength: [2000, 'Message cannot exceed 2000 characters']
  },
  // Enhanced: Title/subject with length validation
  title: {
    type: String,
    trim: true,
    maxlength: [200, 'Title cannot exceed 200 characters']
  },
  // Enhanced: Template system integration
  templateId: {
    type: String,
    trim: true
  },
  templateData: {
    type: mongoose.Schema.Types.Mixed,
    default: {}
  },
  // Enhanced: Comprehensive status tracking
  status: {
    type: String,
    enum: ['pending', 'processing', 'sent', 'delivered', 'read', 'failed', 'expired', 'cancelled'],
    default: 'pending',
    index: true
  },
  // Enhanced: Detailed failure tracking
  failureReason: {
    type: String,
    trim: true
  },
  errorCode: {
    type: String,
    trim: true
  },
  // Enhanced: Retry mechanism
  deliveryAttempts: {
    type: Number,
    default: 0,
    min: 0
  },
  maxRetries: {
    type: Number,
    default: 3,
    min: 0,
    max: 10
  },
  nextRetryAt: {
    type: Date,
    index: true
  },
  // Enhanced: Provider tracking with cost
  provider: {
    name: {
      type: String,
      trim: true
    },
    messageId: {
      type: String,
      trim: true
    },
    cost: {
      type: Number,
      min: 0
    },
    currency: {
      type: String,
      default: 'USD',
      uppercase: true
    }
  },
  // Enhanced: Delivery tracking
  sentAt: {
    type: Date
  },
  deliveredAt: {
    type: Date
  },
  readAt: {
    type: Date
  },
  // Enhanced: Context and relationships
  relatedEntity: {
    type: {
      type: String,
      trim: true,
      enum: ['subscription', 'debt', 'payment', 'user', 'shop', 'order', 'customer', 'system']
    },
    id: {
      type: String,
      trim: true
    }
  },
  // Enhanced: Action capabilities (supports web URLs and mobile deep links)
  actionUrl: {
    type: String,
    trim: true,
    validate: {
      validator: function(v) {
        if (!v) return true;
        // Allow HTTP/HTTPS URLs and mobile deep links (deyncare://)
        return /^(https?|deyncare):\/\/.+/.test(v);
      },
      message: 'Action URL must be a valid HTTP/HTTPS URL or mobile deep link (deyncare://)'
    }
  },
  actionLabel: {
    type: String,
    trim: true,
    maxlength: [50, 'Action label cannot exceed 50 characters']
  },
  // Enhanced: Scheduling and expiration
  scheduledAt: {
    type: Date
  },
  expiresAt: {
    type: Date,
    index: true
  },
  // Enhanced: User interaction tracking
  clickedAt: {
    type: Date
  },
  clickCount: {
    type: Number,
    default: 0,
    min: 0
  },
  // Enhanced: Metadata and context
  metadata: {
    type: mongoose.Schema.Types.Mixed,
    default: {}
  },
  tags: [{
    type: String,
    trim: true
  }],
  // Enhanced: Soft delete with audit trail
  isDeleted: {
    type: Boolean,
    default: false,
    index: true
  },
  deletedAt: {
    type: Date,
    default: null
  },
  deletedBy: {
    type: String,
    trim: true
  },
  // Enhanced: Audit fields
  createdBy: {
    type: String,
    trim: true,
    required: true
  },
  updatedBy: {
    type: String,
    trim: true
  }
}, { 
  timestamps: true,
  versionKey: false
});

// Enhanced indexing strategy for optimal performance
notificationSchema.index({ shopId: 1, createdAt: -1 });
notificationSchema.index({ recipient: 1, status: 1 });
notificationSchema.index({ status: 1, priority: -1, createdAt: 1 }); // For processing queue
notificationSchema.index({ category: 1, shopId: 1 });
notificationSchema.index({ nextRetryAt: 1, status: 1 }); // For retry processing
notificationSchema.index({ expiresAt: 1, status: 1 }); // For expiry cleanup
notificationSchema.index({ 'relatedEntity.type': 1, 'relatedEntity.id': 1 });
notificationSchema.index({ scheduledAt: 1, status: 1 }); // For scheduled notifications
notificationSchema.index({ tags: 1 }); // For tag-based queries
notificationSchema.index({ createdBy: 1 }); // For audit queries

// Enhanced virtual properties
notificationSchema.virtual('isExpired').get(function() {
  if (!this.expiresAt) return false;
  return new Date() > this.expiresAt;
});

notificationSchema.virtual('isRetryable').get(function() {
  return this.status === 'failed' && this.deliveryAttempts < this.maxRetries;
});

notificationSchema.virtual('isScheduled').get(function() {
  if (!this.scheduledAt) return false;
  return new Date() < this.scheduledAt;
});

notificationSchema.virtual('processingTime').get(function() {
  if (!this.sentAt) return null;
  return this.sentAt - this.createdAt;
});

// Enhanced notification lifecycle methods
notificationSchema.methods.incrementAttempts = function(errorReason = null, errorCode = null) {
  this.deliveryAttempts += 1;
  this.updatedBy = 'system';
  
  if (errorReason) this.failureReason = errorReason;
  if (errorCode) this.errorCode = errorCode;
  
  if (this.deliveryAttempts >= this.maxRetries) {
    this.status = 'failed';
    this.failureReason = this.failureReason || 'Maximum retry attempts reached';
  } else {
    // Enhanced exponential backoff with jitter: 2min, 6min, 18min, 54min
    const baseDelay = Math.pow(3, this.deliveryAttempts) * 2;
    const jitter = Math.random() * 0.3; // Add 0-30% jitter
    const delayMinutes = baseDelay * (1 + jitter);
    this.nextRetryAt = new Date(Date.now() + delayMinutes * 60 * 1000);
    this.status = 'pending';
  }
  
  return this.save();
};

notificationSchema.methods.markSent = function(providerId = null, cost = null) {
  this.status = 'sent';
  this.sentAt = new Date();
  this.updatedBy = 'system';
  
  if (providerId) {
    this.provider = this.provider || {};
    this.provider.messageId = providerId;
  }
  
  if (cost !== null) {
    this.provider = this.provider || {};
    this.provider.cost = cost;
  }
  
  return this.save();
};

notificationSchema.methods.markDelivered = function(deliveryTimestamp = null) {
  this.status = 'delivered';
  this.deliveredAt = deliveryTimestamp || new Date();
  this.updatedBy = 'system';
  return this.save();
};

notificationSchema.methods.markRead = function(readTimestamp = null) {
  this.status = 'read';
  this.readAt = readTimestamp || new Date();
  this.updatedBy = 'system';
  return this.save();
};

notificationSchema.methods.markClicked = function() {
  this.clickedAt = new Date();
  this.clickCount = (this.clickCount || 0) + 1;
  this.updatedBy = 'system';
  return this.save();
};

notificationSchema.methods.markExpired = function() {
  this.status = 'expired';
  this.updatedBy = 'system';
  return this.save();
};

notificationSchema.methods.cancel = function(cancelledBy = 'system') {
  this.status = 'cancelled';
  this.updatedBy = cancelledBy;
  return this.save();
};

notificationSchema.methods.softDelete = function(deletedBy = 'system') {
  this.isDeleted = true;
  this.deletedAt = new Date();
  this.deletedBy = deletedBy;
  return this.save();
};

// Enhanced static query methods
notificationSchema.statics.findPendingForProcessing = function(limit = 100) {
  const now = new Date();
  return this.find({
    status: 'pending',
    isDeleted: false,
    $or: [
      { scheduledAt: { $exists: false } },
      { scheduledAt: { $lte: now } }
    ],
    $or: [
      { nextRetryAt: { $exists: false } },
      { nextRetryAt: { $lte: now } }
    ],
    $or: [
      { expiresAt: { $exists: false } },
      { expiresAt: { $gt: now } }
    ]
  })
  .sort({ priority: -1, createdAt: 1 })
  .limit(limit);
};

notificationSchema.statics.findExpiredNotifications = function() {
  return this.find({
    expiresAt: { $lt: new Date() },
    status: { $nin: ['expired', 'cancelled'] },
    isDeleted: false
  });
};

notificationSchema.statics.findFailedNotifications = function(shopId = null, hours = 24) {
  const query = { 
    status: 'failed',
    isDeleted: false,
    createdAt: { $gte: new Date(Date.now() - hours * 60 * 60 * 1000) }
  };
  
  if (shopId) query.shopId = shopId;
  
  return this.find(query).sort({ createdAt: -1 });
};

notificationSchema.statics.findByRecipient = function(recipient, options = {}) {
  const query = { recipient, isDeleted: false };
  
  if (options.status) query.status = options.status;
  if (options.category) query.category = options.category;
  if (options.type) query.type = options.type;
  if (options.priority) query.priority = options.priority;
  if (options.tags) query.tags = { $in: options.tags };
  
  const limit = options.limit || 20;
  const skip = options.skip || 0;
  
  return this.find(query)
    .sort({ createdAt: -1 })
    .skip(skip)
    .limit(limit);
};

notificationSchema.statics.getNotificationStats = function(shopId = null, days = 30) {
  const startDate = new Date(Date.now() - days * 24 * 60 * 60 * 1000);
  const matchQuery = { 
    createdAt: { $gte: startDate },
    isDeleted: false 
  };
  
  if (shopId) matchQuery.shopId = shopId;
  
  return this.aggregate([
    { $match: matchQuery },
    {
      $group: {
        _id: {
          status: '$status',
          type: '$type',
          category: '$category'
        },
        count: { $sum: 1 },
        totalCost: { $sum: '$provider.cost' },
        avgDeliveryTime: {
          $avg: {
            $subtract: ['$deliveredAt', '$sentAt']
          }
        }
      }
    },
    {
      $group: {
        _id: null,
        totalNotifications: { $sum: '$count' },
        totalCost: { $sum: '$totalCost' },
        byStatus: {
          $push: {
            status: '$_id.status',
            type: '$_id.type',
            category: '$_id.category',
            count: '$count',
            cost: '$totalCost',
            avgDeliveryTime: '$avgDeliveryTime'
          }
        }
      }
    }
  ]);
};

notificationSchema.statics.findRelatedNotifications = function(entityType, entityId, limit = 10) {
  return this.find({
    'relatedEntity.type': entityType,
    'relatedEntity.id': entityId,
    isDeleted: false
  })
  .sort({ createdAt: -1 })
  .limit(limit);
};

const Notification = mongoose.model('Notification', notificationSchema);

module.exports = Notification;
