"use client";

import { useState, useEffect } from 'react';
import { toast } from 'sonner';
import { Eye, Package, DollarSign, Settings, Users, Edit, Trash2, ToggleLeft, ToggleRight, X } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Skeleton } from '@/components/ui/skeleton';
import { usePlan } from '@/hooks/usePlan-index';
import { 
  formatCurrency, 
  formatPlanStatus, 
  formatPlanFeatures, 
  formatPlanLimits, 
  formatPlanTags,
  formatCreatedDate,
  formatPricingDisplay 
} from './utils/planFormatters';

export function PlanDetailsDialog({ open, onClose, planId, onEdit, onDelete }) {
  const { plan, loading, error, fetchPlanById } = usePlan();
  const [localLoading, setLocalLoading] = useState(false);
  const [hasAttemptedFetch, setHasAttemptedFetch] = useState(false);

  useEffect(() => {
    if (open && planId) {
      console.log('[PlanDetailsDialog] Opening dialog with planId:', planId);
      console.log('[PlanDetailsDialog] planId type:', typeof planId);
      console.log('[PlanDetailsDialog] planId length:', planId?.length);
      
      // Reset state when opening
      setHasAttemptedFetch(false);
      
      // Validate planId format - should be our custom format, not MongoDB ObjectId
      if (!planId || planId.length < 5) {
        console.error('[PlanDetailsDialog] Invalid planId format:', planId);
        toast.error('Invalid plan ID');
        onClose();
        return;
      }
      
      if (planId.length === 24 && /^[0-9a-fA-F]{24}$/.test(planId)) {
        console.warn('[PlanDetailsDialog] Warning: Received MongoDB ObjectId instead of planId:', planId);
        toast.error('Plan not available');
        onClose();
        return;
      }
      
      // FIXED: Clear any cached plan data to prevent stale "Plan not found" responses
      if (typeof window !== 'undefined') {
        localStorage.removeItem(`cache_plan-${planId}`);
        console.log('[PlanDetailsDialog] Cleared cache for plan:', planId);
      }
      
      console.log('[PlanDetailsDialog] Starting fetchPlanById...');
      setHasAttemptedFetch(true);
      fetchPlanById(planId).catch(err => {
        console.error('[PlanDetailsDialog] Fetch error:', err);
        toast.error('Failed to load plan details');
        // Auto-close dialog on error to prevent persistent popup
        setTimeout(() => onClose(), 1500);
      });
    }
  }, [open, planId, fetchPlanById, onClose]);

  // Auto-close dialog if error persists
  useEffect(() => {
    if (error && hasAttemptedFetch && open) {
      console.log('[PlanDetailsDialog] Auto-closing due to error:', error);
      toast.error('Plan not found');
      setTimeout(() => onClose(), 1000);
    }
  }, [error, hasAttemptedFetch, open, onClose]);

  const handleEdit = () => {
    if (plan) {
      onEdit?.(plan);
      onClose();
    }
  };

  const handleDelete = () => {
    if (plan) {
      onDelete?.(plan);
      onClose();
    }
  };

  if (!open) return null;

  if (loading || localLoading) {
    return (
      <Dialog open={open} onOpenChange={onClose}>
        <DialogContent className="dialog-content max-w-4xl">
          <DialogHeader className="dialog-header">
            <DialogTitle className="flex items-center gap-2">
              <Eye className="h-5 w-5" />
              Plan Details
            </DialogTitle>
            <DialogDescription>
              Loading plan information...
            </DialogDescription>
          </DialogHeader>
          <div className="dialog-body">
            <div className="space-y-4">
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-3/4" />
              <Skeleton className="h-32 w-full" />
            </div>
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  if (error || !plan) {
    return (
      <Dialog open={open} onOpenChange={onClose}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Eye className="h-5 w-5" />
              Plan Details
            </DialogTitle>
            <DialogDescription>
              Unable to load plan information
            </DialogDescription>
          </DialogHeader>
          <div className="text-center py-8">
            <p className="text-muted-foreground">
              {error || 'Plan not found'}
            </p>
            <Button onClick={onClose} className="mt-4">
              Close
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  const features = formatPlanFeatures(plan.features || {});
  const limits = formatPlanLimits(plan.limits || {});
  const tags = formatPlanTags(plan.metadata || {});

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="dialog-content max-w-4xl">
        <DialogHeader className="dialog-header">
          <div className="flex items-center justify-between">
            <div>
              <DialogTitle className="flex items-center gap-2">
                <Package className="h-5 w-5" />
                {plan.displayName || plan.name}
              </DialogTitle>
              <DialogDescription>
                Plan ID: {plan.planId} • Created {formatCreatedDate(plan.createdAt)}
              </DialogDescription>
            </div>
            <div className="flex items-center gap-2">
              <Badge variant={plan.isActive ? 'default' : 'secondary'}>
                {formatPlanStatus(plan.isActive).label}
              </Badge>
              <Button variant="outline" size="sm" onClick={handleEdit}>
                <Edit className="h-4 w-4 mr-2" />
                Edit
              </Button>
              <Button variant="outline" size="sm" onClick={handleDelete}>
                <Trash2 className="h-4 w-4 mr-2" />
                Delete
              </Button>
            </div>
          </div>
        </DialogHeader>

        <div className="dialog-body">
          <Tabs defaultValue="overview" className="space-y-4">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="pricing">Pricing</TabsTrigger>
            <TabsTrigger value="limits">Limits</TabsTrigger>
            <TabsTrigger value="features">Features</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Basic Information */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-sm font-medium">Basic Information</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Plan Name:</span>
                    <span className="text-sm font-medium">{plan.name}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Display Name:</span>
                    <span className="text-sm font-medium">{plan.displayName}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Type:</span>
                    <span className="text-sm capitalize">{plan.type}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Status:</span>
                    <Badge variant={plan.isActive ? 'default' : 'secondary'} className="text-xs">
                      {formatPlanStatus(plan.isActive).label}
                    </Badge>
                  </div>
                  {plan.description && (
                    <div>
                      <span className="text-sm text-muted-foreground">Description:</span>
                      <p className="text-sm mt-1">{plan.description}</p>
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* Statistics */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-sm font-medium">Statistics</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-muted-foreground">Active Subscribers:</span>
                    <div className="flex items-center gap-2">
                      <Users className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm font-medium">{plan.subscriberCount || 0}</span>
                    </div>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-muted-foreground">Total Revenue:</span>
                    <div className="flex items-center gap-2">
                      <DollarSign className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm font-medium">
                        {formatCurrency(plan.totalRevenue || 0, 'USD')}
                      </span>
                    </div>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Created:</span>
                    <span className="text-sm">{formatCreatedDate(plan.createdAt)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Last Updated:</span>
                    <span className="text-sm">{formatCreatedDate(plan.updatedAt)}</span>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Tags */}
            {tags.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle className="text-sm font-medium">Tags</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex flex-wrap gap-2">
                    {tags.map((tag, index) => (
                      <Badge key={index} variant="outline" className="text-xs">
                        {tag.label}
                      </Badge>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          <TabsContent value="pricing" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <DollarSign className="h-4 w-4" />
                  Pricing Details
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="text-center p-6 bg-muted/50 rounded-lg">
                  <div className="text-3xl font-bold">
                    {formatPricingDisplay(plan.pricing)}
                  </div>
                  <div className="text-sm text-muted-foreground mt-1">
                    {plan.pricing?.billingCycle && `per ${plan.pricing.billingCycle}`}
                  </div>
                </div>
                
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <span className="text-sm font-medium">Base Price:</span>
                    <p className="text-sm text-muted-foreground">
                      {plan.pricing ? formatCurrency(plan.pricing.basePrice, plan.pricing.currency) : 'Free'}
                    </p>
                  </div>
                  <div className="space-y-2">
                    <span className="text-sm font-medium">Currency:</span>
                    <p className="text-sm text-muted-foreground">
                      {plan.pricing?.currency || 'USD'}
                    </p>
                  </div>
                  <div className="space-y-2">
                    <span className="text-sm font-medium">Billing Cycle:</span>
                    <p className="text-sm text-muted-foreground capitalize">
                      {plan.pricing?.billingCycle || 'N/A'}
                    </p>
                  </div>
                  <div className="space-y-2">
                    <span className="text-sm font-medium">Plan Type:</span>
                    <p className="text-sm text-muted-foreground capitalize">
                      {plan.type}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="limits" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Settings className="h-4 w-4" />
                  Resource Limits
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {limits.map((limit, index) => (
                    <div key={index} className="flex justify-between p-3 border rounded-lg">
                      <span className="text-sm font-medium">{limit.label}:</span>
                      <span className="text-sm text-muted-foreground">{limit.formatted}</span>
                    </div>
                  ))}
                </div>
                {limits.length === 0 && (
                  <p className="text-center text-muted-foreground py-8">
                    No resource limits configured
                  </p>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="features" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Package className="h-4 w-4" />
                  Plan Features
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  {features.map((feature, index) => (
                    <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                      <span className="text-sm">{feature.label}</span>
                      <Badge variant={feature.enabled ? 'default' : 'secondary'} className="text-xs">
                        {feature.enabled ? 'Enabled' : 'Disabled'}
                      </Badge>
                    </div>
                  ))}
                </div>
                {features.length === 0 && (
                  <p className="text-center text-muted-foreground py-8">
                    No features configured
                  </p>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

          <div className="flex justify-end">
            <Button onClick={onClose}>
              Close
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
} 