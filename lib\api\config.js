/**
 * API configuration settings
 * Sets up baseURL, headers, and credentials
 */

// Define API base URL from environment variables or use default
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'https://deyncare-backend.khanciye.com';

// Default headers for all requests
const DEFAULT_HEADERS = {
  'Content-Type': 'application/json',
  'Accept': 'application/json',
};

// Axios instance configuration
const CONFIG = {
  baseURL: API_BASE_URL,
  headers: DEFAULT_HEADERS,
  withCredentials: true, // Important for cookies
};

// Log the base URL for debugging purposes
console.log(`[API] Initialized with baseURL: ${API_BASE_URL}`);

export { API_BASE_URL, DEFAULT_HEADERS, CONFIG };
