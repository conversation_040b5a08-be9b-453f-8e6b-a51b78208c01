/**
 * Upgrade from Trial Service
 * Converts a trial subscription to a paid plan
 */
const { Plan } = require('../../models');
const { AppError, logError, logSuccess } = require('../../utils');
const getSubscriptionById = require('./getSubscriptionById');

/**
 * Upgrade subscription from trial to paid plan
 * @param {string} subscriptionId - ID of the subscription
 * @param {Object} upgradeData - Upgrade details
 * @param {Object} options - Additional options
 * @returns {Promise<Object>} Updated subscription
 */
const upgradeFromTrial = async (subscriptionId, upgradeData, options = {}) => {
  try {
    const { planId, planType = 'monthly', paymentMethod = 'offline' } = upgradeData;
    
    // Get the subscription
    const subscription = await getSubscriptionById(subscriptionId);
    
    // Verify it's a trial subscription
    if (subscription.status !== 'trial' && subscription.plan.type !== 'trial') {
      throw new AppError('Subscription is not in trial status', 400, 'not_trial_subscription');
    }
    
    // Get plan from Plan model
    let plan;
    if (planId) {
      plan = await Plan.findOne({ planId, isDeleted: false });
      if (!plan) {
        throw new AppError('Plan not found', 404, 'plan_not_found');
      }
    } else {
      plan = await Plan.findOne({ type: planType, isDefault: true, isDeleted: false });
      if (!plan) {
        throw new AppError(`No default ${planType} plan found`, 404, 'plan_not_found');
      }
    }
    
    if (plan.type === 'trial') {
      throw new AppError('Cannot upgrade to another trial plan', 400, 'invalid_plan_type');
    }
    
    // Record trial details for history
    const trialDetails = {
      trialPlanId: subscription.planId,
      trialDuration: subscription.daysUsed || 0
    };
    
    // Calculate new end date based on plan type
    const now = new Date();
    const newEndDate = new Date();
    if (plan.type === 'monthly') {
      newEndDate.setMonth(newEndDate.getMonth() + 1);
    } else if (plan.type === 'yearly') {
      newEndDate.setFullYear(newEndDate.getFullYear() + 1);
    }
    
    // Update subscription with new plan details
    subscription.planId = plan.planId;
    subscription.plan.name = plan.name;
    subscription.plan.type = plan.type;
    subscription.plan.features = { ...plan.features };
    subscription.plan.limits = { ...plan.limits };
    
    // Update pricing
    subscription.pricing.basePrice = plan.pricing.basePrice;
    subscription.pricing.currency = plan.pricing.currency;
    subscription.pricing.billingCycle = plan.type === 'yearly' ? 'yearly' : 'monthly';
    
    // Update status and dates
    subscription.status = 'active';
    subscription.payment.method = paymentMethod;
    subscription.payment.verified = (paymentMethod === 'free');
    subscription.dates.trialEndsAt = now;
    subscription.dates.endDate = newEndDate;
    
    // Add to history
    subscription.history.push({
      action: 'trial_converted',
      date: now,
      performedBy: options.actorId || 'system',
      details: {
        trialDetails,
        newPlan: {
          planId: plan.planId,
          name: plan.name,
          type: plan.type
        },
        paymentMethod,
        actorRole: options.actorRole || 'system'
      }
    });
    
    // Save changes
    const updatedSubscription = await subscription.save();
    logSuccess(`Upgraded subscription ${subscriptionId} from trial to ${plan.name} (${plan.type})`, 'SubscriptionService');
    
    return updatedSubscription;
  } catch (error) {
    logError(`Failed to upgrade subscription ${subscriptionId} from trial`, 'SubscriptionService', error);
    throw error;
  }
};

module.exports = upgradeFromTrial;
