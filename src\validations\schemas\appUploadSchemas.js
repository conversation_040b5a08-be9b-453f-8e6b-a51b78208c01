/**
 * App Upload Validation Schemas
 * Defines validation rules for app upload management operations
 */
const Joi = require('joi');
const { validationPatterns } = require('../validationPatterns');

// App upload validation schemas
const appUploadSchemas = {
  /**
   * Schema for uploading a new app file (simplified)
   */
  uploadApp: {
    body: Joi.object({
      appName: Joi.string().trim().max(100).default('DeynCare Mobile App'),
      platform: Joi.string().valid('android').default('android'),
      fileType: Joi.string().valid('apk').default('apk')
    })
  },

  /**
   * Schema for updating app upload metadata
   */
  updateApp: {
    params: Joi.object({
      uploadId: Joi.string().required().trim()
    }),
    body: Joi.object({
      appName: Joi.string().trim().max(100),
      version: Joi.string().trim().max(20).pattern(/^\d+\.\d+\.\d+$/),
      buildNumber: Joi.number().integer().min(1),
      platform: Joi.string().valid('android', 'ios', 'windows', 'mac', 'web'),
      releaseNotes: Joi.string().allow('', null).max(2000),
      isLatest: Joi.boolean(),
      isActive: Joi.boolean()
    }).min(1)
  },

  /**
   * Schema for getting app upload by ID
   */
  getAppById: {
    params: Joi.object({
      uploadId: Joi.string().required().trim()
    })
  },

  /**
   * Schema for downloading app by ID
   */
  downloadApp: {
    params: Joi.object({
      uploadId: Joi.string().required().trim()
    })
  },

  /**
   * Schema for getting apps list with filters
   */
  getApps: {
    query: Joi.object({
      page: Joi.number().integer().min(1).default(1),
      limit: Joi.number().integer().min(1).max(100).default(10),
      platform: Joi.string().valid('android', 'ios', 'windows', 'mac', 'web'),
      isLatest: Joi.boolean(),
      isActive: Joi.boolean(),
      version: Joi.string().trim(),
      search: Joi.string().trim().max(100),
      sortBy: Joi.string().valid('createdAt', 'version', 'buildNumber', 'downloadCount').default('createdAt'),
      sortOrder: Joi.string().valid('asc', 'desc').default('desc')
    })
  },

  /**
   * Schema for deleting app upload
   */
  deleteApp: {
    params: Joi.object({
      uploadId: Joi.string().required().trim()
    })
  },

  /**
   * Schema for setting latest version
   */
  setLatestVersion: {
    body: Joi.object({
      platform: Joi.string().valid('android', 'ios', 'web').optional()
    }),
    params: Joi.object({
      uploadId: Joi.string().required()
    })
  },

  /**
   * Schema for getting latest app by platform
   */
  getLatestByPlatform: {
    params: Joi.object({
      platform: Joi.string().valid('android', 'ios', 'windows', 'mac', 'web').required()
    })
  },

  /**
   * Schema for getting app download statistics
   */
  getAppStats: {
    query: Joi.object({
      platform: Joi.string().valid('android', 'ios', 'windows', 'mac', 'web'),
      startDate: Joi.date().iso(),
      endDate: Joi.date().iso().min(Joi.ref('startDate')),
      groupBy: Joi.string().valid('day', 'week', 'month').default('day')
    })
  }
};

module.exports = appUploadSchemas; 