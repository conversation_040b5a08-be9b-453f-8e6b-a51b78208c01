/**
 * Get Subscription by ID Controller
 * Handles retrieving a specific subscription by ID
 */
const { SubscriptionService } = require('../../services');
const { successResponse } = require('../../utils');

/**
 * Get a subscription by its ID
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
const getSubscriptionById = async (req, res, next) => {
  try {
    const { subscriptionId } = req.params;
    
    // Get subscription through service
    const subscription = await SubscriptionService.getSubscriptionById(subscriptionId);
    
    // Return successful response
    return successResponse(res, {
      message: 'Subscription retrieved successfully',
      data: subscription,
    });
  } catch (error) {
    next(error);
  }
};

module.exports = getSubscriptionById;
