/**
 * Shop Export Controller
 * Handles export requests for shop management data (SuperAdmin only)
 */
const BaseExportController = require('./baseExportController');
const ShopService = require('../../services/shopService');
const { logError } = require('../../utils');

class ShopExportController extends BaseExportController {
  /**
   * Get shop export fields
   * @param {Object} req - Express request object
   * @returns {Array} Field configurations
   */
  static async getExportFields(req) {
    return [
      {
        label: 'Shop ID',
        key: 'shopId',
        type: 'string'
      },
      {
        label: 'Shop Name',
        key: 'shopName',
        type: 'string'
      },
      {
        label: 'Business Type',
        key: 'businessDetails.type',
        type: 'string'
      },
      {
        label: 'Owner Name',
        key: 'ownerName',
        type: 'string'
      },
      {
        label: 'Owner Email',
        key: 'email',
        type: 'string'
      },
      {
        label: 'Owner Phone',
        key: 'phone',
        type: 'string'
      },
      {
        label: 'Address',
        key: 'address',
        type: 'string'
      },
      {
        label: 'Status',
        key: 'status',
        type: 'string'
      },
      {
        label: 'Is Active',
        key: 'isActive',
        type: 'boolean'
      },
      {
        label: 'Payment Status',
        key: 'paymentStatus',
        type: 'string'
      },
      {
        label: 'Total Users',
        key: 'totalUsers',
        type: 'number'
      },
      {
        label: 'Created At',
        key: 'createdAt',
        type: 'datetime'
      }
    ];
  }

  /**
   * Get shop data for export
   * @param {Object} req - Express request object
   * @returns {Array} Shop data
   */
  static async getExportData(req) {
    try {
      const { 
        status, 
        isActive, 
        paymentStatus,
        businessType,
        startDate,
        endDate
      } = req.query;
      
      const filters = {};
      if (status) filters.status = status;
      if (isActive !== undefined) filters.isActive = isActive === 'true';
      if (paymentStatus) filters.paymentStatus = paymentStatus;
      if (businessType) filters['businessDetails.type'] = businessType;
      
      if (startDate || endDate) {
        filters.createdAt = {};
        if (startDate) filters.createdAt.$gte = new Date(startDate);
        if (endDate) filters.createdAt.$lte = new Date(endDate);
      }

      // Get all shops (no limit for export)
      const result = await ShopService.getAllShops(filters, 1, 999999);
      
      return result.docs;
    } catch (error) {
      logError('Failed to get shop data for export', 'ShopExportController', error);
      throw error;
    }
  }

  /**
   * Export shops to CSV
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next middleware function
   */
  static async exportToCSV(req, res, next) {
    req.exportConfig = {
      module: 'shops',
      getFields: this.getExportFields,
      getData: this.getExportData,
      filename: req.query.filename || 'shop_management_export'
    };

    await this.prepareExport(req, res, next);
    return super.exportToCSV(req, res, next);
  }

  /**
   * Export shops to Excel
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next middleware function
   */
  static async exportToExcel(req, res, next) {
    req.exportConfig = {
      module: 'shops',
      getFields: this.getExportFields,
      getData: this.getExportData,
      filename: req.query.filename || 'shop_management_export',
      options: {
        sheetName: 'Shop Management',
        styling: {
          header: true,
          columns: {
            'shopName': { width: 20 },
            'businessDetails.type': { width: 15 },
            'ownerName': { width: 18 },
            'email': { width: 25 },
            'phone': { width: 15 },
            'address': { width: 30 },
            'createdAt': { width: 20 }
          }
        }
      }
    };

    await this.prepareExport(req, res, next);
    return super.exportToExcel(req, res, next);
  }
}

module.exports = ShopExportController; 