/**
 * Fetch ML Settings Service
 * 
 * Retrieves ML (Machine Learning) configuration settings that admin users can manage
 * UPDATED: Admin users can now enable/disable ML features for mobile app
 */
import settingsAPI from '../../api/modules/settings';
import { handleError } from '../baseService';

/**
 * Fetch ML settings that admin users can manage
 * @returns {Object} ML settings data with admin-accessible options
 */
const fetchMLSettings = async () => {
  try {
    const response = await settingsAPI.getSettings('ml');
    
    // Filter to only show admin-accessible ML settings
    const adminAccessibleSettings = response.data?.filter(setting => 
      setting.accessLevel === 'admin' || setting.accessLevel === 'all'
    ) || [];
    
    // Format settings for mobile app usage
    const formattedMLSettings = {
      mlEnabled: true,
      autoTriggerOnDue: true,
      autoTriggerOnPaymentUpdate: true,
      storeRiskScoreInDB: true,
      // SuperAdmin-only settings are not exposed to admin users
      // These include: ml_api_base_url, ml_api_key, ml_predict_endpoint, 
      // ml_prediction_timeout, risk_data_retention_days
    };
    
    // Map actual settings to formatted structure
    adminAccessibleSettings.forEach(setting => {
      switch (setting.key) {
        case 'ml_enabled':
          formattedMLSettings.mlEnabled = setting.value;
          break;
        case 'ml_auto_trigger_on_due':
          formattedMLSettings.autoTriggerOnDue = setting.value;
          break;
        case 'ml_auto_trigger_on_payment_update':
          formattedMLSettings.autoTriggerOnPaymentUpdate = setting.value;
          break;
        case 'store_risk_score_in_db':
          formattedMLSettings.storeRiskScoreInDB = setting.value;
          break;
      }
    });
    
    return formattedMLSettings;
  } catch (error) {
    handleError(error, 'SettingsService.fetchMLSettings', true);
    return {
      mlEnabled: false,
      autoTriggerOnDue: false,
      autoTriggerOnPaymentUpdate: false,
      storeRiskScoreInDB: false
    };
  }
};

export default fetchMLSettings; 