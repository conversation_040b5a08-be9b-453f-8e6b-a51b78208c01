"use client";

import { useState, useEffect, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/auth-context';
import { usePaymentTransactions } from '@/hooks/use-payment-transactions';
import { toast } from 'sonner';
import { ResponsiveContainer } from '@/components/layout/responsive-container';
import { KpiCard } from '@/components/dashboard/common/kpi-card';
import { ExportDialog } from '@/components/dashboard/common/export-dialog';

// Import payment transaction components
import PaymentTransactionsHeader from '@/components/dashboard/payment-transactions/payment-transactions-header';
import PaymentTransactionsFilters from '@/components/dashboard/payment-transactions/payment-transactions-filters';
import PaymentTransactionsTable from '@/components/dashboard/payment-transactions/payment-transactions-table';
import ApproveTransactionDialog from '@/components/dashboard/payment-transactions/approve-transaction-dialog';
import RejectTransactionDialog from '@/components/dashboard/payment-transactions/reject-transaction-dialog';
import TransactionDetailsDialog from '@/components/dashboard/payment-transactions/transaction-details-dialog';

export default function PaymentTransactionsPage() {
  const router = useRouter();
  const { isAuthenticated, isLoading, isSuperAdmin } = useAuth();
  
  // Authentication state tracking
  const [authReady, setAuthReady] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);
  
  // Dialog state
  const [showExportDialog, setShowExportDialog] = useState(false);
  const [showApproveDialog, setShowApproveDialog] = useState(false);
  const [showRejectDialog, setShowRejectDialog] = useState(false);
  const [showDetailsDialog, setShowDetailsDialog] = useState(false);
  const [selectedTransaction, setSelectedTransaction] = useState(null);
  const [actionLoading, setActionLoading] = useState(false);

  // Payment transactions management hook
  const {
    transactions,
    transactionStats,
    loading: transactionsLoading,
    pagination,
    filters,
    fetchTransactions,
    fetchTransactionStats,
    approveTransaction,
    rejectTransaction,
    exportTransactions,
    applyFilters,
    changePage,
    changePageSize,
    refreshData
  } = usePaymentTransactions();

  // Wait for authentication to be fully ready
  useEffect(() => {
    if (isLoading) return;
    
    if (!isAuthenticated) {
      router.push('/login');
      return;
    }
    
    // Only SuperAdmin access for payment transaction management
    if (!isSuperAdmin) {
      router.push('/unauthorized');
      return;
    }

    // Check if we have access token before proceeding
    const hasToken = typeof window !== 'undefined' && localStorage.getItem('accessToken');
    
    if (hasToken) {
      setAuthReady(true);
    } else {
      // Retry after a short delay
      setTimeout(() => {
        const retryHasToken = typeof window !== 'undefined' && localStorage.getItem('accessToken');
        if (retryHasToken) {
          setAuthReady(true);
        }
      }, 1000);
    }
  }, [isAuthenticated, isLoading, isSuperAdmin, router]);

  // Handle refresh
  const handleRefresh = async () => {
    if (isRefreshing) return;
    
    setIsRefreshing(true);
    
    try {
      await refreshData();
      toast.success("Payment transaction data refreshed successfully");
    } catch (error) {
      console.error('[PaymentTransactionsPage] Refresh error:', error);
      toast.error(`Failed to refresh payment transactions: ${error.message}`);
    } finally {
      setTimeout(() => {
        setIsRefreshing(false);
      }, 300);
    }
  };

  // Handle export
  const handleExport = () => {
    setShowExportDialog(true);
  };

  // Handle transaction approval
  const handleApproveTransaction = useCallback(async (transaction, notes) => {
    setActionLoading(true);
    try {
      await approveTransaction(transaction.paymentId, notes);
      setShowApproveDialog(false);
      setSelectedTransaction(null);
    } catch (error) {
      toast.error('Failed to approve transaction');
    } finally {
      setActionLoading(false);
    }
  }, [approveTransaction]);

  // Handle transaction rejection
  const handleRejectTransaction = useCallback(async (transaction, reason) => {
    setActionLoading(true);
    try {
      await rejectTransaction(transaction.paymentId, reason);
      setShowRejectDialog(false);
      setSelectedTransaction(null);
    } catch (error) {
      toast.error('Failed to reject transaction');
    } finally {
      setActionLoading(false);
    }
  }, [rejectTransaction]);

  // Handle view transaction details
  const handleViewDetails = useCallback((transaction) => {
    setSelectedTransaction(transaction);
    setShowDetailsDialog(true);
  }, []);

  // Handle approve action from table
  const handleApproveAction = useCallback((transaction) => {
    setSelectedTransaction(transaction);
    setShowApproveDialog(true);
  }, []);

  // Handle reject action from table
  const handleRejectAction = useCallback((transaction) => {
    setSelectedTransaction(transaction);
    setShowRejectDialog(true);
  }, []);

  // Handle approve from details dialog
  const handleApproveFromDetails = useCallback((transaction) => {
    setShowDetailsDialog(false);
    setTimeout(() => {
      setSelectedTransaction(transaction);
      setShowApproveDialog(true);
    }, 100);
  }, []);

  // Handle reject from details dialog
  const handleRejectFromDetails = useCallback((transaction) => {
    setShowDetailsDialog(false);
    setTimeout(() => {
      setSelectedTransaction(transaction);
      setShowRejectDialog(true);
    }, 100);
  }, []);

  // Filter handlers
  const handleSearchChange = useCallback((search) => {
    applyFilters({ search });
  }, [applyFilters]);

  const handleStatusFilterChange = useCallback((status) => {
    applyFilters({ status });
  }, [applyFilters]);

  const handlePaymentMethodFilterChange = useCallback((paymentMethod) => {
    applyFilters({ paymentMethod });
  }, [applyFilters]);

  const handleDateRangeFilterChange = useCallback((dateRange) => {
    applyFilters({ dateRange });
  }, [applyFilters]);

  const handleClearFilters = useCallback(() => {
    applyFilters({
      search: '',
      status: 'all',
      paymentMethod: 'all',
      dateRange: 'all'
    });
  }, [applyFilters]);

  // Format currency values
  const formatCurrency = (value, currency = 'USD') => {
    if (!value) return '$0.00';
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency
    }).format(value);
  };

  // Calculate approval rate
  const calculateApprovalRate = () => {
    const total = transactionStats?.summary?.totalTransactions || 0;
    const approved = transactionStats?.summary?.approvedTransactions || 0;
    if (total === 0) return 0;
    return ((approved / total) * 100).toFixed(1);
  };

  // Show loading state while authentication is being checked
  if (isLoading || !authReady) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-2 border-primary border-t-transparent" />
      </div>
    );
  }

  return (
    <ResponsiveContainer>
      <div className="space-y-6">
        {/* Header */}
        <PaymentTransactionsHeader 
          onRefresh={handleRefresh}
          onExportClick={handleExport}
          isRefreshing={isRefreshing}
          isSuperAdmin={isSuperAdmin}
          stats={transactionStats}
        />

        {/* KPI Cards */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <KpiCard
            title="Total Transactions"
            value={(transactionStats?.summary?.totalTransactions || 0).toLocaleString()}
            description="All payment transactions"
            icon="credit"
            loading={transactionsLoading}
          />
          
          <KpiCard
            title="Pending Approval"
            value={(transactionStats?.summary?.pendingTransactions || 0).toLocaleString()}
            description="Awaiting review"
            icon="clock"
            trend={transactionStats?.summary?.pendingTransactions > 0 ? "down" : "neutral"}
            trendValue={`${transactionStats?.summary?.pendingTransactions || 0}`}
            trendLabel="need attention"
            loading={transactionsLoading}
          />
          
          <KpiCard
            title="Approved Transactions"
            value={(transactionStats?.summary?.approvedTransactions || 0).toLocaleString()}
            description="Successfully approved"
            icon="activity"
            trend="up"
            trendValue={`${calculateApprovalRate()}%`}
            trendLabel="approval rate"
            loading={transactionsLoading}
          />
          
          <KpiCard
            title="Total Value"
            value={formatCurrency(transactionStats?.summary?.totalValue || 0)}
            description="All transaction value"
            icon="dollar"
            trend="up"
            trendValue={formatCurrency(transactionStats?.summary?.approvedValue || 0)}
            trendLabel="approved value"
            loading={transactionsLoading}
          />
        </div>

        {/* Filters */}
        <PaymentTransactionsFilters
          searchQuery={filters?.search || ''}
          onSearchChange={handleSearchChange}
          statusFilter={filters?.status || 'all'}
          onStatusFilterChange={handleStatusFilterChange}
          paymentMethodFilter={filters?.paymentMethod || 'all'}
          onPaymentMethodFilterChange={handlePaymentMethodFilterChange}
          dateRangeFilter={filters?.dateRange || 'all'}
          onDateRangeFilterChange={handleDateRangeFilterChange}
          onClearFilters={handleClearFilters}
        />

        {/* Transactions Table */}
        <PaymentTransactionsTable 
          transactions={transactions}
          loading={transactionsLoading}
          pagination={pagination}
          onPageChange={changePage}
          onPageSizeChange={changePageSize}
          onViewDetails={handleViewDetails}
          onApprove={handleApproveAction}
          onReject={handleRejectAction}
        />
        
        {/* Export Dialog */}
        <ExportDialog
          open={showExportDialog}
          onOpenChange={setShowExportDialog}
          exportType="payment-transactions"
          title="Export Payment Transactions"
          description="Export payment transaction data to CSV format"
        />

        {/* Approve Transaction Dialog */}
        <ApproveTransactionDialog
          open={showApproveDialog}
          onOpenChange={setShowApproveDialog}
          transaction={selectedTransaction}
          onConfirm={handleApproveTransaction}
          loading={actionLoading}
        />

        {/* Reject Transaction Dialog */}
        <RejectTransactionDialog
          open={showRejectDialog}
          onOpenChange={setShowRejectDialog}
          transaction={selectedTransaction}
          onConfirm={handleRejectTransaction}
          loading={actionLoading}
        />

        {/* Transaction Details Dialog */}
        <TransactionDetailsDialog
          open={showDetailsDialog}
          onOpenChange={setShowDetailsDialog}
          transaction={selectedTransaction}
          onApprove={handleApproveFromDetails}
          onReject={handleRejectFromDetails}
          loading={actionLoading}
        />
      </div>
    </ResponsiveContainer>
  );
} 