const BaseEmailService = require('./baseEmailService');
const { logInfo, logError, logSuccess } = require('../../utils/logger');
const AppError = require('../../utils/core/AppError');

/**
 * Email service for sending subscription-related emails
 */
class SubscriptionEmailService extends BaseEmailService {
  /**
   * Send trial ending reminder email
   * @param {Object} data - Email data containing email, shopName, trialEndsAt, daysLeft
   * @returns {Promise<boolean>} - Success status
   */
  async sendTrialEndingReminderEmail(data) {
    try {
      if (!this.initialized) {
        throw new AppError('Email service not initialized', 500, 'email_service_error');
      }

      const { email, shopName, trialEndsAt, daysLeft } = data;
      
      // Format date for display
      const formattedEndDate = new Date(trialEndsAt).toLocaleDateString('en-US', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });
      
      // Prepare template data
      const templateData = {
        shopName,
        trialEndsAt: formattedEndDate,
        daysLeft,
        features: data.features || {},
        upgradeUrl: `${process.env.FRONTEND_URL || 'https://deyncare.cajiibcreative.com'}/subscription/upgrade`,
        upgradeMonthlyUrl: `${process.env.FRONTEND_URL || 'https://deyncare.cajiibcreative.com'}/subscription/upgrade?plan=monthly`,
        upgradeYearlyUrl: `${process.env.FRONTEND_URL || 'https://deyncare.cajiibcreative.com'}/subscription/upgrade?plan=yearly`
      };

      // Send the email using the general sendEmail method
      return await this.sendEmail({
        to: email,
        subject: `Your DeynCare trial ends in ${daysLeft} days - Upgrade now`,
        template: 'Subscription/trial-ending',
        data: templateData
      });
    } catch (error) {
      logError('Failed to send trial ending reminder email', 'SubscriptionEmailService', error);
      throw error;
    }
  }

  /**
   * Send subscription upgrade confirmation email
   * @param {Object} data - Email data containing email, shopName, planType, endDate, price, currency
   * @returns {Promise<boolean>} - Success status
   */
  async sendSubscriptionUpgradedEmail(data) {
    try {
      const { email, shopName, planType, endDate, price, currency } = data;
      
      // Format date for display
      const formattedEndDate = new Date(endDate).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });
      
      // Determine billing cycle based on plan type
      const billingCycle = planType === 'yearly' ? 'year' : 'month';
      
      // Prepare template data
      const templateData = {
        shopName,
        planType: planType.charAt(0).toUpperCase() + planType.slice(1), // Capitalize
        endDate: formattedEndDate,
        price,
        currency: currency || 'USD',
        billingCycle,
        paymentMethod: data.paymentMethod || 'Credit Card'
      };

      return await this.sendEmail({
        to: email,
        subject: 'Your DeynCare subscription has been upgraded',
        template: 'Subscription/subscription-upgraded',
        data: templateData
      });
    } catch (error) {
      logError('Failed to send subscription upgraded email', 'SubscriptionEmailService', error);
      throw error;
    }
  }

  /**
   * Send subscription canceled email
   * @param {Object} data - Email data containing email, shopName, endDate, immediateEffect
   * @returns {Promise<boolean>} - Success status
   */
  async sendSubscriptionCanceledEmail(data) {
    try {
      const { email, shopName, endDate, immediateEffect } = data;
      
      // Format date for display
      const formattedEndDate = new Date(endDate).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });
      
      // Prepare template data
      const templateData = {
        shopName,
        endDate: formattedEndDate,
        immediateEffect: immediateEffect === true,
        planType: data.planType || 'Standard'
      };

      return await this.sendEmail({
        to: email,
        subject: 'Your DeynCare subscription has been canceled',
        template: 'Subscription/subscription-canceled',
        data: templateData
      });
    } catch (error) {
      logError('Failed to send subscription canceled email', 'SubscriptionEmailService', error);
      throw error;
    }
  }

  /**
   * Send subscription expiry reminder email
   * @param {Object} data - Email data containing email, shopName, endDate, daysLeft, planType, autoRenew
   * @returns {Promise<boolean>} - Success status
   */
  async sendSubscriptionExpiryReminderEmail(data) {
    try {
      const { email, shopName, endDate, daysLeft, planType, autoRenew } = data;
      
      // Format date for display
      const formattedEndDate = new Date(endDate).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });
      
      // Prepare template data
      const templateData = {
        shopName,
        endDate: formattedEndDate,
        daysLeft: daysLeft || 0,
        planType: planType ? planType.charAt(0).toUpperCase() + planType.slice(1) : 'Standard',
        autoRenew: autoRenew === true,
        urgencyLevel: daysLeft <= 1 ? 'urgent' : daysLeft <= 3 ? 'high' : 'medium',
        currentYear: new Date().getFullYear(),
        renewUrl: `${process.env.FRONTEND_URL || 'https://deyncare.cajiibcreative.com'}/subscription/renew`,
        dashboardUrl: `${process.env.FRONTEND_URL || 'https://deyncare.cajiibcreative.com'}/dashboard`,
        termsUrl: `${process.env.FRONTEND_URL || 'https://deyncare.cajiibcreative.com'}/terms`,
        privacyUrl: `${process.env.FRONTEND_URL || 'https://deyncare.cajiibcreative.com'}/privacy`,
        supportUrl: `${process.env.FRONTEND_URL || 'https://deyncare.cajiibcreative.com'}/support`
      };

      const subject = daysLeft <= 1 
        ? `Urgent: Your DeynCare subscription expires ${daysLeft === 0 ? 'today' : 'tomorrow'}!`
        : `Reminder: Your DeynCare subscription expires in ${daysLeft} days`;

      return await this.sendEmail({
        to: email,
        subject,
        template: 'Subscription/subscription-expiry-reminder',
        data: templateData
      });
    } catch (error) {
      logError('Failed to send subscription expiry reminder email', 'SubscriptionEmailService', error);
      throw error;
    }
  }

  /**
   * Send subscription expired email
   * @param {Object} data - Email data containing email, shopName, endDate, planType, gracePeriodDays
   * @returns {Promise<boolean>} - Success status
   */
  async sendSubscriptionExpiredEmail(data) {
    try {
      const { email, shopName, endDate, planType, gracePeriodDays } = data;
      
      // Format date for display
      const formattedEndDate = new Date(endDate).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });
      
      // Prepare template data
      const templateData = {
        shopName,
        endDate: formattedEndDate,
        planType: planType.charAt(0).toUpperCase() + planType.slice(1), // Capitalize
        gracePeriodDays: gracePeriodDays || 30,
        currentYear: new Date().getFullYear(),
        termsUrl: `${process.env.FRONTEND_URL || 'https://deyncare.cajiibcreative.com'}/terms`,
        privacyUrl: `${process.env.FRONTEND_URL || 'https://deyncare.cajiibcreative.com'}/privacy`,
        supportUrl: `${process.env.FRONTEND_URL || 'https://deyncare.cajiibcreative.com'}/support`
      };

      return await this.sendEmail({
        to: email,
        subject: 'Your DeynCare subscription has expired',
        template: 'Subscription/subscription-expired',
        data: templateData
      });
    } catch (error) {
      logError('Failed to send subscription expired email', 'SubscriptionEmailService', error);
      throw error;
    }
  }

  /**
   * Send payment failed with retry information email
   * @param {Object} data - Email data containing necessary info for the retry email
   * @returns {Promise<boolean>} - Success status
   */
  async sendPaymentFailedRetryEmail(data) {
    try {
      const {
        email,
        shopName,
        planType,
        amount,
        currency = 'USD',
        paymentMethod,
        failureReason,
        nextRetryDate,
        hoursUntilNextRetry,
        remainingAttempts,
        gracePeriodDays = 7,
        additionalFeatures = 'Premium support'
      } = data;

      // Format retry date for display
      const formattedRetryDate = new Date(nextRetryDate).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });

      // Prepare template data with secure URLs
      const templateData = {
        shopName,
        planType: planType.charAt(0).toUpperCase() + planType.slice(1), // Capitalize
        amount,
        currency,
        paymentMethod,
        failureReason,
        nextRetryDate: formattedRetryDate,
        hoursUntilNextRetry,
        remainingAttempts,
        gracePeriodDays,
        additionalFeatures,
        currentYear: new Date().getFullYear(),
        updatePaymentUrl: `${process.env.FRONTEND_URL || 'https://deyncare.cajiibcreative.com'}/subscription/payment-methods`,
        dashboardUrl: `${process.env.FRONTEND_URL || 'https://deyncare.cajiibcreative.com'}/dashboard`,
        termsUrl: `${process.env.FRONTEND_URL || 'https://deyncare.cajiibcreative.com'}/terms`,
        privacyUrl: `${process.env.FRONTEND_URL || 'https://deyncare.cajiibcreative.com'}/privacy`,
        supportUrl: `${process.env.FRONTEND_URL || 'https://deyncare.cajiibcreative.com'}/support`
      };

      return await this.sendEmail({
        to: email,
        subject: `Payment Failed - Action Required (Retry in ${hoursUntilNextRetry} hours)`,
        template: 'Subscription/payment-failed-retry',
        data: templateData
      });
    } catch (error) {
      logError('Failed to send payment failed retry email', 'SubscriptionEmailService', error);
      throw error;
    }
  }

  /**
   * Send auto-renewal failed email
   * @param {Object} data - Email data containing necessary info for the auto-renewal failed email
   * @returns {Promise<boolean>} - Success status
   */
  async sendAutoRenewalFailedEmail(data) {
    try {
      const {
        email,
        shopName,
        planType,
        amount,
        currency = 'USD',
        expiryDate,
        daysRemaining,
        remainingAttempts
      } = data;

      // Format expiry date for display
      const formattedExpiryDate = new Date(expiryDate).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });

      // Prepare template data with secure URLs
      const templateData = {
        shopName,
        planType: planType.charAt(0).toUpperCase() + planType.slice(1), // Capitalize
        amount,
        currency,
        expiryDate: formattedExpiryDate,
        daysRemaining,
        remainingAttempts,
        currentYear: new Date().getFullYear(),
        renewNowUrl: `${process.env.FRONTEND_URL || 'https://deyncare.cajiibcreative.com'}/subscription/renew`,
        updatePaymentUrl: `${process.env.FRONTEND_URL || 'https://deyncare.cajiibcreative.com'}/subscription/payment-methods`,
        termsUrl: `${process.env.FRONTEND_URL || 'https://deyncare.cajiibcreative.com'}/terms`,
        privacyUrl: `${process.env.FRONTEND_URL || 'https://deyncare.cajiibcreative.com'}/privacy`,
        supportUrl: `${process.env.FRONTEND_URL || 'https://deyncare.cajiibcreative.com'}/support`
      };

      return await this.sendEmail({
        to: email,
        subject: `Auto-Renewal Failed - Action Required (${daysRemaining} days remaining)`,
        template: 'Subscription/auto-renewal-failed',
        data: templateData
      });
    } catch (error) {
      logError('Failed to send auto-renewal failed email', 'SubscriptionEmailService', error);
      throw error;
    }
  }

  /**
   * Send admin notification about subscription expiry
   * @param {Object} data - Email data containing adminEmail, expiredSubscriptions
   * @returns {Promise<boolean>} - Success status
   */
  async sendAdminExpiryNotificationEmail(data) {
    try {
      const { adminEmail, expiredSubscriptions } = data;
      
      if (!expiredSubscriptions || expiredSubscriptions.length === 0) {
        return false;
      }
      
      // Prepare summary data
      const totalExpired = expiredSubscriptions.length;
      const totalRevenueLoss = expiredSubscriptions.reduce((sum, sub) => {
        const price = sub.plan?.price || (sub.plan?.type === 'yearly' ? 96 : 10);
        return sum + price;
      }, 0);
      
      // Group by plan type
      const planBreakdown = expiredSubscriptions.reduce((acc, sub) => {
        const planType = sub.plan?.type || 'unknown';
        acc[planType] = (acc[planType] || 0) + 1;
        return acc;
      }, {});
      
      // Prepare template data
      const templateData = {
        totalExpired,
        totalRevenueLoss,
        planBreakdown,
        expiredToday: expiredSubscriptions.filter(sub => {
          const expiredDate = new Date(sub.dates?.endDate || sub.endDate);
          const today = new Date();
          return expiredDate.toDateString() === today.toDateString();
        }).length,
        subscriptions: expiredSubscriptions.map(sub => ({
          shopName: sub.shopName || 'Unknown Shop',
          planType: sub.plan?.type || 'Unknown',
          expiredOn: new Date(sub.dates?.endDate || sub.endDate).toLocaleDateString(),
          shopId: sub.shopId
        })),
        currentDate: new Date().toLocaleDateString('en-US', {
          year: 'numeric',
          month: 'long',
          day: 'numeric'
        }),
        dashboardUrl: `${process.env.FRONTEND_URL || 'https://deyncare.cajiibcreative.com'}/admin/subscriptions`,
        reportsUrl: `${process.env.FRONTEND_URL || 'https://deyncare.cajiibcreative.com'}/admin/reports`
      };

      const subject = `DeynCare Admin Alert: ${totalExpired} Subscription${totalExpired !== 1 ? 's' : ''} Expired Today`;

      return await this.sendEmail({
        to: adminEmail,
        subject,
        template: 'Admin/subscription-expiry-admin-alert',
        data: templateData
      });
    } catch (error) {
      logError('Failed to send admin expiry notification email', 'SubscriptionEmailService', error);
      throw error;
    }
  }

  /**
   * Send subscription upgrade request email to SuperAdmin
   * @param {Object} data - Email data containing upgrade request information
   */
  async sendUpgradeRequestEmail(data) {
    try {
      const {
        shopId,
        shopName,
        shopEmail,
        ownerName,
        currentPlan,
        requestedPlan,
        requestMessage,
        requestedBy,
        requestedAt
      } = data;

      // Get SuperAdmin email from environment or use default
      const superAdminEmail = process.env.SUPERADMIN_EMAIL || '<EMAIL>';

      const emailData = {
        to: superAdminEmail,
        subject: `🔄 Subscription Upgrade Request - ${shopName}`,
        template: 'Subscription/upgrade-request',
        data: {
          shopId,
          shopName,
          shopEmail,
          ownerName,
          currentPlan: currentPlan.toUpperCase(),
          requestedPlan: requestedPlan.toUpperCase(),
          requestMessage: requestMessage || 'No additional message provided',
          requestedBy,
          requestedAt: requestedAt.toLocaleDateString(),
          dashboardUrl: process.env.ADMIN_DASHBOARD_URL || 'https://admin.deyncare.com'
        }
      };

      await this.sendEmail(emailData);
      logSuccess(`Upgrade request email sent to SuperAdmin for shop ${shopId}`, 'SubscriptionEmailService');
    } catch (error) {
      logError('Failed to send upgrade request email', 'SubscriptionEmailService', error);
      throw error;
    }
  }
}

module.exports = new SubscriptionEmailService();
