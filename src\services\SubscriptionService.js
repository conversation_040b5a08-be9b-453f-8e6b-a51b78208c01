/**
 * Subscription Service
 * Main entry point for all subscription-related functionality
 * Follows modular design pattern similar to Plan service
 */

// Import individual service functions
const getSubscriptionById = require('./Subscription/getSubscriptionById');
const getCurrentSubscriptionByShop = require('./Subscription/getCurrentSubscriptionByShop');
const getSubscriptionHistory = require('./Subscription/getSubscriptionHistory');
const getAllSubscriptions = require('./Subscription/getAllSubscriptions');
const upgradeFromTrial = require('./Subscription/upgradeFromTrial');
const changePlan = require('./Subscription/changePlan');
const updateSubscription = require('./Subscription/updateSubscription');
const renewSubscription = require('./Subscription/renewSubscription');
const cancelSubscription = require('./Subscription/cancelSubscription');

// Import expiry-related service functions
const getExpiredActiveSubscriptions = require('./Subscription/getExpiredActiveSubscriptions');
const deactivateExpiredSubscription = require('./Subscription/deactivateExpiredSubscription');
const getExpiringSubscriptions = require('./Subscription/getExpiringSubscriptions');
const getTrialsEndingSoon = require('./Subscription/getTrialsEndingSoon');
const getSubscriptionsForRenewal = require('./Subscription/getSubscriptionsForRenewal');

// Import grace period service functions
const gracePeriodService = require('./Subscription/handleGracePeriod');







// Import SuperAdmin service functions
const getSubscriptionStats = require('./Subscription/getSubscriptionStats');
const bulkOperations = require('./Subscription/bulkOperations');

// Import payment retry service
const paymentRetryService = require('./Subscription/paymentRetryService');

/**
 * Consolidated SubscriptionService object with modular service functions
 * This maintains the same API interface while using a more modular internal structure
 */
const SubscriptionService = {
  // Admin/SuperAdmin operations only
  getSubscriptionById,
  getCurrentSubscriptionByShop,
  getSubscriptionHistory,
  getAllSubscriptions,
  
  // Plan-related operations
  upgradeFromTrial,
  changePlan,
  updateSubscription,
  renewSubscription,
  cancelSubscription,
  
  // SuperAdmin stats and bulk operations
  getSubscriptionStats,
  bulkUpdateStatus: bulkOperations.bulkUpdateStatus,
  bulkChangePlan: bulkOperations.bulkChangePlan,
  bulkApplyDiscount: bulkOperations.bulkApplyDiscount,
  bulkExtendExpiration: bulkOperations.bulkExtendExpiration,
  countSubscriptionsByPlanId: bulkOperations.countSubscriptionsByPlanId,
  getPlanRevenue: bulkOperations.getPlanRevenue,
  
  // Expiry and renewal management
  getExpiredActiveSubscriptions,
  deactivateExpiredSubscription,
  getExpiringSubscriptions,
  getTrialsEndingSoon,
  getSubscriptionsForRenewal,
  
  // Grace period management
  getGracePeriodStatus: gracePeriodService.getGracePeriodStatus,
  applyGracePeriod: gracePeriodService.applyGracePeriod,
  getGracePeriodEndingSoon: gracePeriodService.getGracePeriodEndingSoon,
  finalizeExpiredGracePeriods: gracePeriodService.finalizeExpiredGracePeriods,
  
  // Methods have been added as needed
  
  // Payment retry functionality
  recordFailedPayment: paymentRetryService.recordFailedPayment,
  processPendingRetries: paymentRetryService.processPendingRetries,
  getRetryStatus: paymentRetryService.getRetryStatus,
  cancelScheduledRetries: paymentRetryService.cancelScheduledRetries,
  triggerManualRetry: paymentRetryService.triggerManualRetry,
  PAYMENT_RETRY_CONFIG: paymentRetryService.RETRY_CONFIG
};

module.exports = SubscriptionService;
