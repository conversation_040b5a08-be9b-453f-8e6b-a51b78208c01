# Dependencies
node_modules
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Next.js build output
.next/
out/

# Production build
build/
dist/

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Testing
coverage/
.nyc_output

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Git
.git
.gitignore

# Docker
Dockerfile*
docker-compose*
.dockerignore

# Documentation
README.md
*.md
docs/

# Logs
logs
*.log

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage

# ESLint cache
.eslintcache

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# next.js build output
.next

# nuxt.js build output
.nuxt

# vuepress build output
.vuepress/dist

# Serverless directories
.serverless

# FuseBox cache
.fusebox/

# DynamoDB Local files
.dynamodb/

# Backup files
*.backup
*.bak

# Temporary files
tmp/
temp/

# Development and testing files
cypress/
__tests__/
*.test.js
*.spec.js

# Build scripts that shouldn't be in production
scripts/
tools/

# CI/CD files
.github/
.gitlab-ci.yml
.travis.yml
azure-pipelines.yml

# Package manager files
package-lock.json
yarn.lock
pnpm-lock.yaml

# Local development files
.env.example
.env.*.example

# SSL certificates (should be mounted as volumes)
ssl/
certs/ 