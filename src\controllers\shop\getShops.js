const { Shop, Subscription } = require('../../models');
const { AppError } = require('../../utils');

/**
 * Get all shops with pagination and filtering
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 * @returns {Promise<void>}
 */
const getShops = async (req, res, next) => {
  try {
    // Extract query parameters
    const {
      page = 1,
      limit = 20,
      status,
      verified,
      subscriptionStatus,
      search
    } = req.query;
    
    // Parse pagination parameters
    const pageNum = parseInt(page, 10);
    const limitNum = parseInt(limit, 10);
    const skip = (pageNum - 1) * limitNum;
    
    // Build query
    const query = { isDeleted: { $ne: true } }; // Exclude soft-deleted shops
    
    // Add status filter if provided
    if (status && ['active', 'pending', 'suspended', 'deleted'].includes(status)) {
      query.status = status;
    }
    
    // Add verification filter if provided
    if (verified !== undefined) {
      query.verified = verified === 'true';
    }
    
    // Add search filter if provided
    if (search) {
      query.$or = [
        { shopName: { $regex: search, $options: 'i' } },
        { ownerName: { $regex: search, $options: 'i' } },
        { email: { $regex: search, $options: 'i' } }
      ];
    }
    
    // Execute count query for pagination
    const total = await Shop.countDocuments(query);
    
    // Execute find query with pagination
    const shops = await Shop.find(query)
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limitNum)
      .lean();
    
    // Get subscription data for all shops
    const shopIds = shops.map(shop => shop.shopId);
    const subscriptions = await Subscription.find({ 
      shopId: { $in: shopIds },
      isDeleted: { $ne: true }
    }).lean();
    
    // Create subscription lookup map
    const subscriptionMap = new Map();
    subscriptions.forEach(sub => {
      subscriptionMap.set(sub.shopId, sub);
    });
    
    // Calculate pagination metadata
    const pages = Math.ceil(total / limitNum);
    
    // Helper function to determine subscription status
    const getSubscriptionStatus = (subscription) => {
      if (!subscription) return 'no_plan';
      
      const now = new Date();
      const endDate = subscription.endDate ? new Date(subscription.endDate) : null;
      
      if (subscription.status === 'cancelled') return 'cancelled';
      if (subscription.status === 'trial') {
        return endDate && endDate > now ? 'trial' : 'trial_expired';
      }
      
      return endDate && endDate > now ? 'active' : 'expired';
    };
    
    // Helper function to calculate days remaining
    const getDaysRemaining = (endDate) => {
      if (!endDate) return 0;
      const now = new Date();
      const end = new Date(endDate);
      const diffTime = end - now;
      return diffTime > 0 ? Math.ceil(diffTime / (1000 * 60 * 60 * 24)) : 0;
    };
    
    // Sanitize shop data for response with proper subscription information
    const sanitizedShops = shops.map(shop => {
      const subscription = subscriptionMap.get(shop.shopId);
      const subscriptionStatus = getSubscriptionStatus(subscription);
      const daysRemaining = subscription ? getDaysRemaining(subscription.endDate) : 0;
      
      return {
        _id: shop._id,
        shopId: shop.shopId,
        shopName: shop.shopName,
        ownerName: shop.ownerName,
        email: shop.email,
        phone: shop.phone,
        address: shop.address,
        logoUrl: shop.logoUrl || '',
        status: shop.status,
        verified: shop.verified || false,
        subscription: subscription ? {
          subscriptionId: subscription.subscriptionId,
          plan: subscription.plan?.name || subscription.plan?.type || 'Unknown',
          planType: subscription.plan?.type || 'trial',
          status: subscriptionStatus,
          startDate: subscription.startDate,
          endDate: subscription.endDate,
          expiresAt: subscription.endDate, // For backward compatibility
          daysLeft: daysRemaining,
          isActive: subscriptionStatus === 'active',
          // Additional useful fields
          pricing: subscription.pricing || {},
          features: subscription.plan?.features || {}
        } : {
          plan: 'No plan',
          planType: null,
          status: 'no_plan',
          startDate: null,
          endDate: null,
          expiresAt: null,
          daysLeft: 0,
          isActive: false
        },
        createdAt: shop.createdAt,
        updatedAt: shop.updatedAt
      };
    });
    
    // Apply subscription status filter after processing
    let filteredShops = sanitizedShops;
    if (subscriptionStatus) {
      filteredShops = sanitizedShops.filter(shop => {
        switch (subscriptionStatus) {
          case 'active':
            return shop.subscription.status === 'active';
          case 'trial':
            return shop.subscription.status === 'trial';
          case 'expired':
            return shop.subscription.status === 'expired' || shop.subscription.status === 'trial_expired';
          case 'no_plan':
            return shop.subscription.status === 'no_plan';
          default:
            return true;
        }
      });
    }
    
    // Return success response with pagination
    res.status(200).json({
      success: true,
      message: 'Shops retrieved successfully',
      data: {
        shops: filteredShops,
        pagination: {
          total,
          page: pageNum,
          limit: limitNum,
          pages,
          filtered: filteredShops.length !== sanitizedShops.length ? filteredShops.length : undefined
        }
      }
    });
  } catch (error) {
    next(error);
  }
};

module.exports = getShops;
