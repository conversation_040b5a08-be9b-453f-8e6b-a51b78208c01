const mongoose = require('mongoose');

const customerSchema = new mongoose.Schema({
  // System IDs
  customerId: {
    type: String,
    required: true,
    unique: true,
    trim: true
  },
  shopId: {
    type: String,
    required: true,
    trim: true
  },

  CustomerName: {
    type: String,
    required: true,
    trim: true
  },
  CustomerType: {
    type: String,
    enum: ['New', 'Returning'],
    required: true
  },
  
  // Mobile App Input Fields
  phone: {
    type: String,
    required: true,
    trim: true
  },
  email: {
    type: String,
    trim: true,
    lowercase: true
  },
  address: {
    type: String,
    trim: true
  },
  
  category: {
    type: String,
    trim: true,
    minlength: [2, 'Category must be at least 2 characters'],
    maxlength: [50, 'Category cannot exceed 50 characters'],
    default: 'regular'
  },
  notes: {
    type: String,
    trim: true
  },
  
  // ML Risk Profile
  riskProfile: {
    currentRiskLevel: {
      type: String,
      enum: ['Low Risk', 'Medium Risk', 'High Risk', 'Active Debt'],
      default: 'Active Debt'
    },
    riskScore: {
      type: Number,
      min: 0,
      max: 100,
      default: 0
    },
    lastAssessment: {
      type: Date,
      default: null
    },
    assessmentCount: {
      type: Number,
      default: 0
    },
    mlSource: {
      type: String,
      enum: ['manual', 'ml_api', 'system'],
      default: 'system'
    }
  },
  
  // System fields
  isDeleted: {
    type: Boolean,
    default: false
  }
}, { 
  timestamps: true 
});



// Method to get CSV payload format (ML only)
customerSchema.methods.getCSVPayload = function() {
  return {
    CustomerID: this.customerId, // Use customerId instead
    CustomerName: this.CustomerName,
    CustomerType: this.CustomerType
  };
};



// Indexes for performance optimization
customerSchema.index({ shopId: 1, phone: 1 });
customerSchema.index({ customerId: 1 }); // Changed from CustomerID to customerId
customerSchema.index({ shopId: 1, isDeleted: 1 }); // For report queries
customerSchema.index({ shopId: 1, createdAt: -1 }); // For date-based reports
customerSchema.index({ shopId: 1, 'riskProfile.currentRiskLevel': 1 }); // For risk reports
customerSchema.index({ shopId: 1, CustomerType: 1 }); // For customer type filtering

const Customer = mongoose.model('Customer', customerSchema);
module.exports = Customer;
