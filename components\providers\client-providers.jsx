"use client"

import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { ThemeProvider } from "@/components/theme-provider"
import { AuthProvider } from "@/contexts/auth-context"
import { UserProvider as UsersProvider } from "@/contexts/user-context"
import { ShopProvider } from "@/contexts/shop-context"
import { Toaster } from "@/components/ui/sonner"

// Create a stable query client instance
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 5 * 60 * 1000,
      refetchOnWindowFocus: false,
      retry: 1,
      gcTime: 10 * 60 * 1000,
    },
    mutations: {
      retry: false,
    },
  },
})

export function ClientProviders({ children }) {
  return (
    <QueryClientProvider client={queryClient}>
      <ThemeProvider 
        attribute="class" 
        defaultTheme="system" 
        enableSystem
        disableTransitionOnChange
      >
        <AuthProvider>
          <UsersProvider>
            <ShopProvider>
              {children}
              <Toaster />
            </ShopProvider>
          </UsersProvider>
        </AuthProvider>
      </ThemeProvider>
    </QueryClientProvider>
  )
} 