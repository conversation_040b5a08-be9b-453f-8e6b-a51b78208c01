# DeynCare Dashboard Overview & Cards Design

## 🎯 Dashboard Layout Structure

### Main Dashboard Overview
The dashboard displays a comprehensive overview of all core features in a structured layout:

```
┌─────────────────────────────────────────────────────────────────┐
│                    DASHBOARD HEADER                             │
│  Welcome back, SuperAdmin         Today: Dec 15, 2024          │
│  Here's your DeynCare overview                                 │
└─────────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────────┐
│                  CRITICAL ACTIONS BAR                          │
│  ⚠️ Action Required: 3 payments need verification              │
│  🔔 5 new shop registrations pending approval                   │
└─────────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────────┐
│                    KPI CARDS SECTION                           │
│  [Users] [Shops] [Plans] [Subscriptions] [Payments] [Revenue]  │
└─────────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────────┐
│                    CHARTS SECTION                              │
│  [Growth Chart]    [Revenue Chart]    [Distribution Chart]     │
└─────────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────────┐
│  [Recent Activity]           [Quick Actions]                   │
└─────────────────────────────────────────────────────────────────┘
```

## 📊 KPI Cards Design

### 1. User Management Card
```jsx
<KpiCard
  title="Total Users"
  value="5,241"
  icon="users"
  trend="up"
  trendValue="12.3%"
  trendLabel="from last month"
  description="Active users across all shops"
>
  <div className="mt-2 text-xs">
    <span className="text-blue-600">2,890 Admin</span> • 
    <span className="text-green-600">2,351 Employee</span>
  </div>
</KpiCard>
```

**Data Points:**
- Total Users: 5,241
- Active Users: 4,983 (95.1%)
- New Users (30 days): +142 (+12.3%)
- Role Distribution: Admin (55.2%), Employee (44.8%)
- User Status: Active, Inactive, Suspended

### 2. Shop Management Card
```jsx
<KpiCard
  title="Total Shops"
  value="368"
  icon="cart"
  trend="up"
  trendValue="8.7%"
  trendLabel="from last month"
  description="Registered healthcare shops"
>
  <div className="mt-2 text-xs">
    <span className="text-green-600">340 Active</span> • 
    <span className="text-yellow-600">15 Pending</span> • 
    <span className="text-red-600">13 Suspended</span>
  </div>
</KpiCard>
```

**Data Points:**
- Total Shops: 368
- Active Shops: 340 (92.4%)
- Pending Approval: 15 (4.1%)
- Suspended: 13 (3.5%)
- New Registrations (30 days): +29 (+8.7%)
- Shop Categories: Clinic, Pharmacy, Hospital, etc.

### 3. Plan Management Card
```jsx
<KpiCard
  title="Active Plans"
  value="12"
  icon="calendar"
  trend="neutral"
  trendValue="0%"
  trendLabel="no change"
  description="Available subscription plans"
>
  <div className="mt-2 text-xs">
    <span className="text-blue-600">5 Basic</span> • 
    <span className="text-purple-600">4 Premium</span> • 
    <span className="text-gold-600">3 Enterprise</span>
  </div>
</KpiCard>
```

**Data Points:**
- Total Plans: 12
- Active Plans: 12 (100%)
- Plan Distribution: Basic (5), Premium (4), Enterprise (3)
- Most Popular: Premium Plan (45% of subscriptions)
- Plan Utilization Rate: 87.3%

### 4. Subscription Management Card
```jsx
<KpiCard
  title="Active Subscriptions"
  value="324"
  icon="activity"
  trend="up"
  trendValue="15.2%"
  trendLabel="from last month"
  description="Current active subscriptions"
>
  <div className="mt-2 text-xs">
    <span className="text-green-600">298 Paid</span> • 
    <span className="text-yellow-600">18 Trial</span> • 
    <span className="text-red-600">8 Overdue</span>
  </div>
</KpiCard>
```

**Data Points:**
- Total Subscriptions: 324
- Active Subscriptions: 298 (92.0%)
- Trial Subscriptions: 18 (5.6%)
- Overdue Subscriptions: 8 (2.5%)
- Renewal Rate: 89.4%
- Churn Rate: 3.2%

### 5. Payment Management Card
```jsx
<KpiCard
  title="Payment Verification"
  value="23"
  icon="credit"
  trend="down"
  trendValue="18.2%"
  trendLabel="from last week"
  description="Payments pending verification"
>
  <div className="mt-2 text-xs">
    <span className="text-red-600">23 Pending</span> • 
    <span className="text-green-600">156 Verified Today</span>
  </div>
</KpiCard>
```

**Data Points:**
- Pending Verification: 23
- Verified Today: 156
- Payment Success Rate: 94.7%
- Average Verification Time: 2.4 hours
- Payment Methods: EVC Plus (78%), Offline (22%)

### 6. Revenue Management Card
```jsx
<KpiCard
  title="Monthly Revenue"
  value="$24,590"
  icon="dollar"
  trend="up"
  trendValue="23.8%"
  trendLabel="from last month"
  description="Total revenue this month"
>
  <div className="mt-2 text-xs">
    <span className="text-green-600">$19,650 Collected</span> • 
    <span className="text-yellow-600">$4,940 Pending</span>
  </div>
</KpiCard>
```

**Data Points:**
- Monthly Revenue: $24,590
- Collected Revenue: $19,650 (79.9%)
- Pending Revenue: $4,940 (20.1%)
- Revenue Growth: +23.8%
- Average Revenue per Shop: $67.38

## 📈 Charts Section Design

### 1. Growth Trends Chart (Line Chart)
```jsx
<ChartLineLabel
  title="Platform Growth Trends"
  description="Users and Shops growth over 12 months"
  data={[
    { month: "Jan", users: 4680, shops: 312 },
    { month: "Feb", users: 4820, shops: 325 },
    { month: "Mar", users: 4950, shops: 338 },
    { month: "Apr", users: 5120, shops: 351 },
    { month: "May", users: 5241, shops: 368 },
    // ... more data
  ]}
  config={{
    users: { label: "Users", color: "hsl(var(--primary))" },
    shops: { label: "Shops", color: "hsl(var(--secondary))" }
  }}
/>
```

**Shows:**
- Monthly user growth
- Monthly shop registrations
- Growth rate trends
- Seasonal patterns

### 2. Revenue Analytics Chart (Line Chart)
```jsx
<ChartLineLabel
  title="Revenue Analytics"
  description="Monthly revenue and payment trends"
  data={[
    { month: "Jan", revenue: 18500, payments: 287 },
    { month: "Feb", revenue: 19200, payments: 298 },
    { month: "Mar", revenue: 21100, payments: 312 },
    { month: "Apr", revenue: 22800, payments: 341 },
    { month: "May", revenue: 24590, payments: 368 },
    // ... more data
  ]}
  config={{
    revenue: { label: "Revenue ($)", color: "hsl(var(--chart-1))" },
    payments: { label: "Payments", color: "hsl(var(--chart-2))" }
  }}
/>
```

**Shows:**
- Monthly revenue trends
- Payment volume
- Revenue growth rate
- Payment success patterns

### 3. Distribution Charts (Interactive Pie Chart)
```jsx
<ChartPieInteractive
  title="Subscription Distribution"
  description="Distribution by plan type"
  data={[
    { plan: "Basic", count: 145, percentage: 44.8, fill: "var(--color-basic)" },
    { plan: "Premium", count: 132, percentage: 40.7, fill: "var(--color-premium)" },
    { plan: "Enterprise", count: 47, percentage: 14.5, fill: "var(--color-enterprise)" }
  ]}
  config={{
    basic: { label: "Basic Plan", color: "hsl(var(--chart-1))" },
    premium: { label: "Premium Plan", color: "hsl(var(--chart-2))" },
    enterprise: { label: "Enterprise Plan", color: "hsl(var(--chart-3))" }
  }}
/>
```

**Shows:**
- Plan distribution
- Market share by plan
- Revenue contribution by plan
- Customer preferences

### 4. System Health Radar Chart
```jsx
<ChartRadarLabel
  title="System Performance"
  description="Key system metrics overview"
  data={[
    { metric: "User Satisfaction", score: 85 },
    { metric: "Payment Success", score: 94 },
    { metric: "System Uptime", score: 99 },
    { metric: "Support Response", score: 78 },
    { metric: "Data Accuracy", score: 91 },
    { metric: "Security Score", score: 88 }
  ]}
  config={{
    score: { label: "Score", color: "hsl(var(--primary))" }
  }}
/>
```

**Shows:**
- System performance metrics
- Service quality indicators
- Operational efficiency
- Areas for improvement

## 🔔 Real-time Updates Section

### Critical Actions Bar
```jsx
<Card className="border-l-4 border-l-red-500">
  <CardContent className="p-4">
    <div className="flex items-center justify-between">
      <div className="flex items-center gap-3">
        <AlertCircle className="h-5 w-5 text-red-500" />
        <div>
          <h4 className="font-medium">Payment Verification Required</h4>
          <p className="text-sm text-muted-foreground">
            23 offline payments need verification
          </p>
        </div>
      </div>
      <Button variant="outline" size="sm">
        Review Now
      </Button>
    </div>
  </CardContent>
</Card>
```

### Recent Activity Feed
```jsx
<Card>
  <CardHeader>
    <CardTitle>Recent Activity</CardTitle>
  </CardHeader>
  <CardContent>
    <div className="space-y-3">
      {[
        {
          action: "New Shop Registration",
          shop: "Hargeisa Medical Center",
          time: "5 minutes ago",
          status: "pending"
        },
        {
          action: "Payment Verified",
          shop: "Borama Pharmacy",
          amount: "$89.00",
          time: "12 minutes ago",
          status: "success"
        },
        {
          action: "Subscription Renewed",
          shop: "Burao Clinic",
          plan: "Premium",
          time: "25 minutes ago",
          status: "success"
        }
      ].map((activity, index) => (
        <div key={index} className="flex items-center gap-3 p-2 rounded-lg bg-muted/50">
          <div className="flex-1">
            <p className="font-medium text-sm">{activity.action}</p>
            <p className="text-xs text-muted-foreground">
              {activity.shop} • {activity.time}
            </p>
          </div>
          <Badge variant={activity.status === 'success' ? 'default' : 'secondary'}>
            {activity.status}
          </Badge>
        </div>
      ))}
    </div>
  </CardContent>
</Card>
```

## 🎯 Quick Actions Panel

### Core Feature Quick Access
```jsx
<div className="grid grid-cols-2 md:grid-cols-3 gap-3">
  {[
    {
      title: "User Management",
      icon: <Users className="h-5 w-5" />,
      href: "/dashboard/users",
      count: "5,241 users",
      color: "bg-blue-100 text-blue-600"
    },
    {
      title: "Shop Management",
      icon: <Building2 className="h-5 w-5" />,
      href: "/dashboard/shops",
      count: "368 shops",
      color: "bg-purple-100 text-purple-600"
    },
    {
      title: "Subscriptions",
      icon: <Calendar className="h-5 w-5" />,
      href: "/dashboard/subscriptions",
      count: "324 active",
      color: "bg-green-100 text-green-600"
    },
    {
      title: "Payment Verification",
      icon: <CreditCard className="h-5 w-5" />,
      href: "/dashboard/payments",
      count: "23 pending",
      color: "bg-red-100 text-red-600"
    },
    {
      title: "Reports",
      icon: <FileText className="h-5 w-5" />,
      href: "/dashboard/reports",
      count: "Generate",
      color: "bg-amber-100 text-amber-600"
    },
    {
      title: "Settings",
      icon: <Settings className="h-5 w-5" />,
      href: "/dashboard/settings",
      count: "Configure",
      color: "bg-gray-100 text-gray-600"
    }
  ].map((item, index) => (
    <Card key={index} className="cursor-pointer hover:shadow-md transition-shadow">
      <CardContent className="p-4">
        <div className="flex items-center gap-3">
          <div className={`p-2 rounded-lg ${item.color}`}>
            {item.icon}
          </div>
          <div>
            <h4 className="font-medium text-sm">{item.title}</h4>
            <p className="text-xs text-muted-foreground">{item.count}</p>
          </div>
        </div>
      </CardContent>
    </Card>
  ))}
</div>
```

## 📊 Data Sources & Real-time Updates

### API Endpoints for Dashboard Data
```javascript
const dashboardEndpoints = {
  users: '/api/users/stats',
  shops: '/api/shops/stats',
  plans: '/api/plans/stats',
  subscriptions: '/api/subscriptions/stats',
  payments: '/api/payments/stats',
  revenue: '/api/revenue/stats',
  recentActivity: '/api/activity/recent',
  systemHealth: '/api/system/health'
}
```

### Real-time Update Strategy
```javascript
// Dashboard data fetching with real-time updates
const useDashboardData = () => {
  const [data, setData] = useState(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    // Initial load
    fetchDashboardData()
    
    // Set up real-time updates every 30 seconds
    const interval = setInterval(fetchDashboardData, 30000)
    
    // WebSocket for critical updates
    const ws = new WebSocket('/ws/dashboard')
    ws.onmessage = (event) => {
      const update = JSON.parse(event.data)
      updateDashboardData(update)
    }
    
    return () => {
      clearInterval(interval)
      ws.close()
    }
  }, [])

  return { data, loading }
}
```

## 🎨 Visual Design Specifications

### Color Scheme
- **Primary**: Blue (#3B82F6) - Users, System
- **Secondary**: Purple (#8B5CF6) - Shops, Management
- **Success**: Green (#10B981) - Revenue, Success metrics
- **Warning**: Amber (#F59E0B) - Pending, Warnings
- **Danger**: Red (#EF4444) - Critical, Failed
- **Neutral**: Gray (#6B7280) - General info

### Card Layout
- **Padding**: 16px internal padding
- **Border Radius**: 8px
- **Shadow**: Subtle shadow with hover elevation
- **Spacing**: 16px between cards
- **Responsive**: 1 column mobile, 2-3 columns tablet, 3-4 columns desktop

### Typography
- **Card Title**: 14px, medium weight
- **KPI Value**: 24px, bold
- **Trend Text**: 12px, regular
- **Description**: 12px, muted

This dashboard design provides a comprehensive overview of all your core features with actionable insights and real-time updates, making it easy for SuperAdmins to monitor and manage the entire DeynCare platform efficiently. 