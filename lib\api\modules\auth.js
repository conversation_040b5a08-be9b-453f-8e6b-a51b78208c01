/**
 * Auth API module
 * Handles authentication related API calls
 */
import api from '../index';

const authAPI = {
  /**
   * Log in a user with email and password
   * @param {string} email - User email
   * @param {string} password - User password
   * @returns {Promise} API response
   */
  login: (email, password) => api.post('/api/auth/login', { email, password }),

  /**
   * Log out the current user
   * @returns {Promise} API response
   */
  logout: () => api.post('/api/auth/logout'),

  /**
   * Log out from all devices
   * @returns {Promise} API response
   */
  logoutAll: () => api.post('/api/auth/logout-all'),

  /**
   * Request a password reset email
   * @param {string} email - User email
   * @returns {Promise} API response
   */
  forgotPassword: (email) => api.post('/api/auth/forgot-password', { email }),

  /**
   * Reset password using token from email
   * @param {string} token - Reset token
   * @param {string} newPassword - New password
   * @param {string} confirmPassword - Confirm new password
   * @returns {Promise} API response
   */
  resetPassword: (token, newPassword, confirmPassword) => 
    api.post('/api/auth/reset-password', { token, newPassword, confirmPassword }),

  /**
   * Change password while logged in
   * @param {string} currentPassword - Current password
   * @param {string} newPassword - New password
   * @returns {Promise} API response
   */
  changePassword: (currentPassword, newPassword) => 
    api.post('/api/auth/change-password', { currentPassword, newPassword }),

  /**
   * Get current user profile
   * @returns {Promise} API response with user data
   */
  getProfile: () => api.get('/api/auth/me')
};

export default authAPI;
