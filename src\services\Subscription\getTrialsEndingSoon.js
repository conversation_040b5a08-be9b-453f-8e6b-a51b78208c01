/**
 * Get Trials Ending Soon Service
 * Finds trial subscriptions that are ending within a specified number of days
 */
const { Subscription } = require('../../models');
const { logError } = require('../../utils');

/**
 * Get trial subscriptions ending within specified days
 * @param {Number} daysAhead - Number of days to look ahead (default: 2)
 * @returns {Promise<Array>} Array of trial subscriptions ending soon
 */
const getTrialsEndingSoon = async (daysAhead = 2) => {
  try {
    const now = new Date();
    const futureDate = new Date();
    futureDate.setDate(now.getDate() + daysAhead);
    
    // Find trial subscriptions where:
    // 1. Status is 'trial'
    // 2. End date is between now and future date
    // 3. Not deleted
    // 4. Trial ending reminder not already sent for this period
    const endingTrials = await Subscription.find({
      status: 'trial',
      'dates.endDate': {
        $gte: now,
        $lte: futureDate
      },
      isDeleted: false,
      $or: [
        { 'notifications.trialEndingReminderSent': { $exists: false } },
        { 'notifications.trialEndingReminderSent': false },
        // Also include if reminder was sent more than 12 hours ago (for trials, more frequent reminders)
        { 'notifications.trialEndingReminderSentAt': { $lt: new Date(Date.now() - 12 * 60 * 60 * 1000) } }
      ]
    }).lean();

    return endingTrials;
  } catch (error) {
    logError('Failed to get trials ending soon', 'SubscriptionService', error);
    throw error;
  }
};

module.exports = getTrialsEndingSoon; 