[{"_id": {"$oid": "6850991dd48ee8758e21552d"}, "paymentId": "PAY001", "shopId": "SHOP012", "customerId": "USR017", "customerName": "FITAH", "paymentContext": "subscription", "subscriptionId": "SUB015", "amount": 0.01, "originalAmount": 0.01, "discountAmount": 0, "paymentDate": {"$date": "2025-06-16T22:22:21.464Z"}, "method": "offline", "paymentType": "offline", "referenceNumber": "PAY001", "isConfirmed": true, "status": "approved", "notes": "monthly subscription payment - Registration", "proofFileId": null, "refund": {"amount": 0}, "recordedBy": "USR017", "recordedFromIp": "127.0.0.1", "gatewayInfo": {"gatewayFee": 0}, "integrationStatus": "not_applicable", "sessionType": "online", "syncStatus": "synced", "isDeleted": false, "deletedAt": null, "verificationAttempts": [], "syncedAt": {"$date": "2025-06-16T22:22:21.465Z"}, "createdAt": {"$date": "2025-06-16T22:22:21.466Z"}, "updatedAt": {"$date": "2025-07-17T15:15:45.572Z"}, "__v": 1, "IsOnTime": true, "PaymentDelay": 0, "approvalNotes": "", "approvedAt": {"$date": "2025-07-17T15:15:45.561Z"}, "approvedBy": "USR001", "paidAtReal": {"$date": "2025-07-17T15:15:45.559Z"}, "statusHistory": [{"status": "approved", "changedAt": {"$date": "2025-07-17T15:15:45.562Z"}, "changedBy": "USR001", "notes": "Payment approved by SuperAdmin", "_id": {"$oid": "687913a1d7b19af97249fe96"}}, {"status": "approved", "changedAt": {"$date": "2025-07-17T15:15:45.572Z"}, "changedBy": "USR017", "notes": "monthly subscription payment - Registration", "_id": {"$oid": "687913a1d7b19af97249fe97"}}]}, {"_id": {"$oid": "68557755b31d8cc6e0d1cbfa"}, "paymentId": "PAY002", "debtId": "DEBT001", "customerId": "CUST001", "shopId": "SHOP001", "paymentContext": "debt", "amount": 500, "paymentDate": {"$date": "2025-06-20T10:30:00.000Z"}, "PaymentDelay": 2, "IsOnTime": false, "paymentMethod": "cash", "notes": "First payment for customer debt", "isDeleted": false, "createdAt": {"$date": "2025-06-20T14:59:33.762Z"}, "updatedAt": {"$date": "2025-06-20T14:59:33.762Z"}, "PaidAmount": 500, "PaidDate": {"$date": "2025-06-20T10:30:00.000Z"}, "__v": 0}, {"_id": {"$oid": "685e46a619dfad25787c1992"}, "paymentId": "PAY003", "debtId": "DEBT014", "customerId": "CUST012", "shopId": "SHOP001", "paymentContext": "debt", "amount": 900, "paymentDate": {"$date": "2025-06-27T07:21:48.664Z"}, "PaymentDelay": -30, "IsOnTime": true, "paymentMethod": "cash", "notes": "Done", "isDeleted": false, "createdAt": {"$date": "2025-06-27T07:22:14.860Z"}, "updatedAt": {"$date": "2025-06-27T07:22:14.860Z"}, "PaidAmount": 900, "PaidDate": {"$date": "2025-06-27T07:21:48.664Z"}, "__v": 0}, {"_id": {"$oid": "685f5e9ac8b5294bb29d7fc9"}, "paymentId": "PAY004", "debtId": "DEBT005", "customerId": "CUST009", "shopId": "SHOP001", "paymentContext": "debt", "amount": 500, "paymentDate": {"$date": "2025-06-28T03:16:42.277Z"}, "paidAtReal": {"$date": "2025-06-28T03:16:42.277Z"}, "PaymentDelay": -31, "IsOnTime": true, "paymentMethod": "cash", "notes": "Early payment test", "isDeleted": false, "createdAt": {"$date": "2025-06-28T03:16:42.287Z"}, "updatedAt": {"$date": "2025-06-28T03:16:42.287Z"}, "PaidAmount": 500, "PaidDate": {"$date": "2025-06-28T03:16:42.277Z"}, "__v": 0}, {"_id": {"$oid": "685f625767b517f383182452"}, "paymentId": "PAY005", "debtId": "DEBT005", "customerId": "CUST009", "shopId": "SHOP001", "paymentContext": "debt", "amount": 300, "paymentDate": {"$date": "2025-06-28T03:32:39.028Z"}, "paidAtReal": {"$date": "2025-06-28T03:32:39.028Z"}, "PaymentDelay": -31, "IsOnTime": true, "paymentMethod": "cash", "notes": "Early payment test", "isDeleted": false, "createdAt": {"$date": "2025-06-28T03:32:39.029Z"}, "updatedAt": {"$date": "2025-06-28T03:32:39.029Z"}, "PaidAmount": 300, "PaidDate": {"$date": "2025-06-28T03:32:39.028Z"}, "__v": 0}, {"_id": {"$oid": "685f636a8dc0256f0658c582"}, "paymentId": "PAY006", "debtId": "DEBT007", "customerId": "CUST003", "shopId": "SHOP001", "paymentContext": "debt", "amount": 300, "paymentDate": {"$date": "2025-06-28T03:37:14.271Z"}, "paidAtReal": {"$date": "2025-06-28T03:37:14.271Z"}, "PaymentDelay": -29, "IsOnTime": true, "paymentMethod": "cash", "notes": "Early payment test", "isDeleted": false, "createdAt": {"$date": "2025-06-28T03:37:14.278Z"}, "updatedAt": {"$date": "2025-06-28T03:37:14.278Z"}, "PaidAmount": 300, "PaidDate": {"$date": "2025-06-28T03:37:14.271Z"}, "__v": 0}, {"_id": {"$oid": "685f9325c618c307b59ba6e7"}, "paymentId": "PAY007", "debtId": "DEBT007", "customerId": "CUST003", "shopId": "SHOP001", "paymentContext": "debt", "amount": 20, "paymentDate": {"$date": "2025-06-28T07:00:53.902Z"}, "paidAtReal": {"$date": "2025-06-28T07:00:53.902Z"}, "PaymentDelay": -29, "IsOnTime": true, "paymentMethod": "cash", "notes": "test", "isDeleted": false, "createdAt": {"$date": "2025-06-28T07:00:53.905Z"}, "updatedAt": {"$date": "2025-06-28T07:00:53.905Z"}, "PaidAmount": 20, "PaidDate": {"$date": "2025-06-28T07:00:53.902Z"}, "__v": 0}, {"_id": {"$oid": "686184f07726d968b2fd52e9"}, "paymentId": "PAY008", "debtId": "DEBT015", "customerId": "CUST012", "shopId": "SHOP001", "paymentContext": "debt", "amount": 250, "paymentDate": {"$date": "2025-06-29T18:24:48.472Z"}, "paidAtReal": {"$date": "2025-06-29T18:24:48.472Z"}, "PaymentDelay": 1, "IsOnTime": false, "paymentMethod": "cash", "notes": "test", "isDeleted": false, "createdAt": {"$date": "2025-06-29T18:24:48.478Z"}, "updatedAt": {"$date": "2025-06-29T18:24:48.478Z"}, "PaidAmount": 250, "PaidDate": {"$date": "2025-06-29T18:24:48.472Z"}, "__v": 0}, {"_id": {"$oid": "68638a4b8e74d1cc223c54bd"}, "paymentId": "PAY009", "debtId": "DEBT022", "customerId": "CUST015", "shopId": "SHOP001", "paymentContext": "debt", "amount": 1000, "paymentDate": {"$date": "2025-07-01T07:12:11.378Z"}, "paidAtReal": {"$date": "2025-07-01T07:12:11.378Z"}, "PaymentDelay": {"$numberDouble": "-0.0"}, "IsOnTime": true, "paymentMethod": "cash", "isDeleted": false, "createdAt": {"$date": "2025-07-01T07:12:11.381Z"}, "updatedAt": {"$date": "2025-07-01T07:12:11.381Z"}, "PaidAmount": 1000, "PaidDate": {"$date": "2025-07-01T07:12:11.378Z"}, "__v": 0}, {"_id": {"$oid": "6863d21dce8df64a07f5acff"}, "paymentId": "PAY010", "debtId": "DEBT023", "customerId": "CUST016", "shopId": "SHOP001", "paymentContext": "debt", "amount": 256, "paymentDate": {"$date": "2025-07-01T12:18:37.886Z"}, "paidAtReal": {"$date": "2025-07-01T12:18:37.886Z"}, "PaymentDelay": -1, "IsOnTime": true, "paymentMethod": "cash", "isDeleted": false, "createdAt": {"$date": "2025-07-01T12:18:37.890Z"}, "updatedAt": {"$date": "2025-07-01T12:18:37.890Z"}, "PaidAmount": 256, "PaidDate": {"$date": "2025-07-01T12:18:37.886Z"}, "__v": 0}, {"_id": {"$oid": "68662e6c1d05812d51b09ceb"}, "paymentId": "PAY011", "debtId": "DEBT024", "customerId": "CUST017", "shopId": "SHOP001", "paymentContext": "debt", "amount": 63, "paymentDate": {"$date": "2025-07-03T07:17:00.316Z"}, "paidAtReal": {"$date": "2025-07-03T07:17:00.316Z"}, "PaymentDelay": 1, "IsOnTime": false, "paymentMethod": "cash", "isDeleted": false, "createdAt": {"$date": "2025-07-03T07:17:00.318Z"}, "updatedAt": {"$date": "2025-07-03T07:17:00.318Z"}, "PaidAmount": 63, "PaidDate": {"$date": "2025-07-03T07:17:00.316Z"}, "__v": 0}, {"_id": {"$oid": "6869112e0cc432277e6df8d5"}, "paymentId": "PAY012", "debtId": "DEBT021", "customerId": "CUST014", "shopId": "SHOP001", "paymentContext": "debt", "amount": 350, "paymentDate": {"$date": "2025-07-05T11:49:02.413Z"}, "paidAtReal": {"$date": "2025-07-05T11:49:02.413Z"}, "PaymentDelay": 3, "IsOnTime": false, "paymentMethod": "cash", "notes": "Late", "isDeleted": false, "createdAt": {"$date": "2025-07-05T11:49:02.415Z"}, "updatedAt": {"$date": "2025-07-05T11:49:02.415Z"}, "PaidAmount": 350, "PaidDate": {"$date": "2025-07-05T11:49:02.413Z"}, "__v": 0}, {"_id": {"$oid": "6869127e2902019ddbcedc0c"}, "paymentId": "PAY013", "debtId": "DEBT020", "customerId": "CUST013", "shopId": "SHOP001", "paymentContext": "debt", "amount": 50, "paymentDate": {"$date": "2025-07-05T11:54:38.261Z"}, "paidAtReal": {"$date": "2025-07-05T11:54:38.261Z"}, "PaymentDelay": 1, "IsOnTime": false, "paymentMethod": "cash", "notes": "test", "isDeleted": false, "createdAt": {"$date": "2025-07-05T11:54:38.271Z"}, "updatedAt": {"$date": "2025-07-05T11:54:38.271Z"}, "PaidAmount": 50, "PaidDate": {"$date": "2025-07-05T11:54:38.261Z"}, "__v": 0}, {"_id": {"$oid": "686912812902019ddbcedc1a"}, "paymentId": "PAY014", "debtId": "DEBT020", "customerId": "CUST013", "shopId": "SHOP001", "paymentContext": "debt", "amount": 50, "paymentDate": {"$date": "2025-07-05T11:54:41.501Z"}, "paidAtReal": {"$date": "2025-07-05T11:54:41.501Z"}, "PaymentDelay": 1, "IsOnTime": false, "paymentMethod": "cash", "notes": "test", "isDeleted": false, "createdAt": {"$date": "2025-07-05T11:54:41.502Z"}, "updatedAt": {"$date": "2025-07-05T11:54:41.502Z"}, "PaidAmount": 50, "PaidDate": {"$date": "2025-07-05T11:54:41.501Z"}, "__v": 0}, {"_id": {"$oid": "686912842902019ddbcedc27"}, "paymentId": "PAY015", "debtId": "DEBT020", "customerId": "CUST013", "shopId": "SHOP001", "paymentContext": "debt", "amount": 50, "paymentDate": {"$date": "2025-07-05T11:54:44.074Z"}, "paidAtReal": {"$date": "2025-07-05T11:54:44.074Z"}, "PaymentDelay": 1, "IsOnTime": false, "paymentMethod": "cash", "notes": "test", "isDeleted": false, "createdAt": {"$date": "2025-07-05T11:54:44.076Z"}, "updatedAt": {"$date": "2025-07-05T11:54:44.076Z"}, "PaidAmount": 50, "PaidDate": {"$date": "2025-07-05T11:54:44.074Z"}, "__v": 0}, {"_id": {"$oid": "6869168f51a37146f686f94a"}, "paymentId": "PAY016", "debtId": "DEBT022", "customerId": "CUST015", "shopId": "SHOP001", "paymentContext": "debt", "amount": 300, "paymentDate": {"$date": "2025-07-05T12:11:59.863Z"}, "paidAtReal": {"$date": "2025-07-05T12:11:59.863Z"}, "PaymentDelay": 4, "IsOnTime": false, "paymentMethod": "cash", "isDeleted": false, "createdAt": {"$date": "2025-07-05T12:11:59.865Z"}, "updatedAt": {"$date": "2025-07-05T12:11:59.865Z"}, "PaidAmount": 300, "PaidDate": {"$date": "2025-07-05T12:11:59.863Z"}, "__v": 0}, {"_id": {"$oid": "6869170651a37146f686f9e2"}, "paymentId": "PAY017", "debtId": "DEBT025", "customerId": "CUST018", "shopId": "SHOP001", "paymentContext": "debt", "amount": 100, "paymentDate": {"$date": "2025-07-05T12:13:58.014Z"}, "paidAtReal": {"$date": "2025-07-05T12:13:58.014Z"}, "PaymentDelay": 1, "IsOnTime": false, "paymentMethod": "cash", "isDeleted": false, "createdAt": {"$date": "2025-07-05T12:13:58.015Z"}, "updatedAt": {"$date": "2025-07-05T12:13:58.015Z"}, "PaidAmount": 100, "PaidDate": {"$date": "2025-07-05T12:13:58.014Z"}, "__v": 0}, {"_id": {"$oid": "6869174e51a37146f686fa83"}, "paymentId": "PAY018", "debtId": "DEBT025", "customerId": "CUST018", "shopId": "SHOP001", "paymentContext": "debt", "amount": 850, "paymentDate": {"$date": "2025-07-05T12:15:10.394Z"}, "paidAtReal": {"$date": "2025-07-05T12:15:10.394Z"}, "PaymentDelay": 1, "IsOnTime": false, "paymentMethod": "cash", "isDeleted": false, "createdAt": {"$date": "2025-07-05T12:15:10.396Z"}, "updatedAt": {"$date": "2025-07-05T12:15:10.396Z"}, "PaidAmount": 850, "PaidDate": {"$date": "2025-07-05T12:15:10.394Z"}, "__v": 0}, {"_id": {"$oid": "6869181a17161128be2abc88"}, "paymentId": "PAY019", "debtId": "DEBT022", "customerId": "CUST015", "shopId": "SHOP001", "paymentContext": "debt", "amount": 200, "paymentDate": {"$date": "2025-07-05T12:18:34.592Z"}, "paidAtReal": {"$date": "2025-07-05T12:18:34.592Z"}, "PaymentDelay": 4, "IsOnTime": false, "paymentMethod": "cash", "isDeleted": false, "createdAt": {"$date": "2025-07-05T12:18:34.612Z"}, "updatedAt": {"$date": "2025-07-05T12:18:34.612Z"}, "PaidAmount": 200, "PaidDate": {"$date": "2025-07-05T12:18:34.592Z"}, "__v": 0}, {"_id": {"$oid": "6869182a17161128be2abc9d"}, "paymentId": "PAY020", "debtId": "DEBT022", "customerId": "CUST015", "shopId": "SHOP001", "paymentContext": "debt", "amount": 200, "paymentDate": {"$date": "2025-07-05T12:18:50.172Z"}, "paidAtReal": {"$date": "2025-07-05T12:18:50.172Z"}, "PaymentDelay": 4, "IsOnTime": false, "paymentMethod": "cash", "isDeleted": false, "createdAt": {"$date": "2025-07-05T12:18:50.174Z"}, "updatedAt": {"$date": "2025-07-05T12:18:50.174Z"}, "PaidAmount": 200, "PaidDate": {"$date": "2025-07-05T12:18:50.172Z"}, "__v": 0}, {"_id": {"$oid": "68691a637b51af45daf4e9f6"}, "paymentId": "PAY021", "debtId": "DEBT019", "customerId": "CUST001", "shopId": "SHOP001", "paymentContext": "debt", "amount": 198, "paymentDate": {"$date": "2025-07-05T12:28:19.257Z"}, "paidAtReal": {"$date": "2025-07-05T12:28:19.257Z"}, "PaymentDelay": 4, "IsOnTime": false, "paymentMethod": "cash", "isDeleted": false, "createdAt": {"$date": "2025-07-05T12:28:19.411Z"}, "updatedAt": {"$date": "2025-07-05T12:28:19.411Z"}, "PaidAmount": 198, "PaidDate": {"$date": "2025-07-05T12:28:19.257Z"}, "__v": 0}, {"_id": {"$oid": "68691cfc9e43f54c15d292f9"}, "paymentId": "PAY022", "debtId": "DEBT015", "customerId": "CUST012", "shopId": "SHOP001", "paymentContext": "debt", "amount": 250, "paymentDate": {"$date": "2025-07-05T12:39:24.197Z"}, "paidAtReal": {"$date": "2025-07-05T12:39:24.197Z"}, "PaymentDelay": 7, "IsOnTime": false, "paymentMethod": "cash", "isDeleted": false, "createdAt": {"$date": "2025-07-05T12:39:24.402Z"}, "updatedAt": {"$date": "2025-07-05T12:39:24.402Z"}, "PaidAmount": 250, "PaidDate": {"$date": "2025-07-05T12:39:24.197Z"}, "__v": 0}, {"_id": {"$oid": "687097aa8296872c47fb8b3c"}, "paymentId": "PAY023", "debtId": "DEBT026", "customerId": "CUST019", "shopId": "SHOP001", "paymentContext": "debt", "amount": 1000, "paymentDate": {"$date": "2025-07-11T04:48:42.219Z"}, "paidAtReal": {"$date": "2025-07-11T04:48:42.219Z"}, "PaymentDelay": 1, "IsOnTime": false, "paymentMethod": "cash", "isDeleted": false, "createdAt": {"$date": "2025-07-11T04:48:42.474Z"}, "updatedAt": {"$date": "2025-07-11T04:48:42.474Z"}, "PaidAmount": 1000, "PaidDate": {"$date": "2025-07-11T04:48:42.219Z"}, "__v": 0}, {"_id": {"$oid": "6870a5808296872c47fb8c8a"}, "paymentId": "PAY024", "debtId": "DEBT019", "customerId": "CUST001", "shopId": "SHOP001", "paymentContext": "debt", "amount": 45, "paymentDate": {"$date": "2025-07-11T05:47:44.561Z"}, "paidAtReal": {"$date": "2025-07-11T05:47:44.561Z"}, "PaymentDelay": 10, "IsOnTime": false, "paymentMethod": "cash", "isDeleted": false, "createdAt": {"$date": "2025-07-11T05:47:44.868Z"}, "updatedAt": {"$date": "2025-07-11T05:47:44.868Z"}, "PaidAmount": 45, "PaidDate": {"$date": "2025-07-11T05:47:44.561Z"}, "__v": 0}, {"_id": {"$oid": "68728fe36ea01665f0f8ca0f"}, "paymentId": "PAY025", "debtId": "DEBT029", "customerId": "CUST021", "shopId": "SHOP001", "paymentContext": "debt", "amount": 700, "paymentDate": {"$date": "2025-07-12T16:40:03.542Z"}, "paidAtReal": {"$date": "2025-07-12T16:40:03.542Z"}, "PaymentDelay": {"$numberDouble": "-0.0"}, "IsOnTime": true, "paymentMethod": "cash", "notes": "Early payment test", "isDeleted": false, "createdAt": {"$date": "2025-07-12T16:40:03.796Z"}, "updatedAt": {"$date": "2025-07-12T16:40:03.796Z"}, "PaidAmount": 700, "PaidDate": {"$date": "2025-07-12T16:40:03.542Z"}, "__v": 0}, {"_id": {"$oid": "6872928d6ea01665f0f8cb84"}, "paymentId": "PAY026", "debtId": "DEBT028", "customerId": "CUST021", "shopId": "SHOP001", "paymentContext": "debt", "amount": 1000, "paymentDate": {"$date": "2025-07-12T16:51:25.937Z"}, "paidAtReal": {"$date": "2025-07-12T16:51:25.937Z"}, "PaymentDelay": {"$numberDouble": "-0.0"}, "IsOnTime": true, "paymentMethod": "cash", "isDeleted": false, "createdAt": {"$date": "2025-07-12T16:51:26.229Z"}, "updatedAt": {"$date": "2025-07-12T16:51:26.229Z"}, "PaidAmount": 1000, "PaidDate": {"$date": "2025-07-12T16:51:25.937Z"}, "__v": 0}, {"_id": {"$oid": "687292aa6ea01665f0f8cbb7"}, "paymentId": "PAY027", "debtId": "DEBT029", "customerId": "CUST021", "shopId": "SHOP001", "paymentContext": "debt", "amount": 300, "paymentDate": {"$date": "2025-07-12T16:51:54.811Z"}, "paidAtReal": {"$date": "2025-07-12T16:51:54.811Z"}, "PaymentDelay": {"$numberDouble": "-0.0"}, "IsOnTime": true, "paymentMethod": "cash", "isDeleted": false, "createdAt": {"$date": "2025-07-12T16:51:55.119Z"}, "updatedAt": {"$date": "2025-07-12T16:51:55.119Z"}, "PaidAmount": 300, "PaidDate": {"$date": "2025-07-12T16:51:54.811Z"}, "__v": 0}, {"_id": {"$oid": "6873506cff3d23517ccac060"}, "paymentId": "PAY028", "debtId": "DEBT030", "customerId": "CUST022", "shopId": "SHOP001", "paymentContext": "debt", "amount": 50, "paymentDate": {"$date": "2025-07-13T06:21:32.178Z"}, "paidAtReal": {"$date": "2025-07-13T06:21:32.178Z"}, "PaymentDelay": {"$numberDouble": "-0.0"}, "IsOnTime": true, "paymentMethod": "cash", "isDeleted": false, "createdAt": {"$date": "2025-07-13T06:21:32.303Z"}, "updatedAt": {"$date": "2025-07-13T06:21:32.303Z"}, "PaidAmount": 50, "PaidDate": {"$date": "2025-07-13T06:21:32.178Z"}, "__v": 0}, {"_id": {"$oid": "68738e0ef35cafdd65d0c6bb"}, "paymentId": "PAY029", "debtId": "DEBT027", "customerId": "CUST020", "shopId": "SHOP001", "paymentContext": "debt", "amount": 250, "paymentDate": {"$date": "2025-07-13T10:44:30.478Z"}, "paidAtReal": {"$date": "2025-07-13T10:44:30.478Z"}, "PaymentDelay": 1, "IsOnTime": false, "paymentMethod": "cash", "isDeleted": false, "createdAt": {"$date": "2025-07-13T10:44:30.608Z"}, "updatedAt": {"$date": "2025-07-13T10:44:30.608Z"}, "PaidAmount": 250, "PaidDate": {"$date": "2025-07-13T10:44:30.478Z"}, "__v": 0}, {"_id": {"$oid": "6874da010c0776cda8a84230"}, "paymentId": "PAY030", "debtId": "DEBT030", "customerId": "CUST022", "shopId": "SHOP001", "paymentContext": "debt", "amount": 50, "paymentDate": {"$date": "2025-07-14T10:20:49.182Z"}, "paidAtReal": {"$date": "2025-07-14T10:20:49.182Z"}, "PaymentDelay": 1, "IsOnTime": false, "paymentMethod": "cash", "isDeleted": false, "createdAt": {"$date": "2025-07-14T10:20:49.300Z"}, "updatedAt": {"$date": "2025-07-14T10:20:49.300Z"}, "PaidAmount": 50, "PaidDate": {"$date": "2025-07-14T10:20:49.182Z"}, "__v": 0}, {"_id": {"$oid": "6874eb0f0c0776cda8a8441e"}, "paymentId": "PAY031", "debtId": "DEBT031", "customerId": "CUST023", "shopId": "SHOP029", "paymentContext": "debt", "amount": 900, "paymentDate": {"$date": "2025-07-14T11:33:35.403Z"}, "paidAtReal": {"$date": "2025-07-14T11:33:35.403Z"}, "PaymentDelay": 1, "IsOnTime": false, "paymentMethod": "cash", "isDeleted": false, "createdAt": {"$date": "2025-07-14T11:33:35.528Z"}, "updatedAt": {"$date": "2025-07-14T11:33:35.528Z"}, "PaidAmount": 900, "PaidDate": {"$date": "2025-07-14T11:33:35.403Z"}, "__v": 0}, {"_id": {"$oid": "687638368459c32d708e4fb0"}, "paymentId": "PAY032", "debtId": "DEBT032", "customerId": "CUST024", "shopId": "SHOP031", "paymentContext": "debt", "amount": 200, "paymentDate": {"$date": "2025-07-15T11:15:02.492Z"}, "paidAtReal": {"$date": "2025-07-15T11:15:02.492Z"}, "PaymentDelay": {"$numberDouble": "-0.0"}, "IsOnTime": true, "paymentMethod": "cash", "notes": "paid", "isDeleted": false, "createdAt": {"$date": "2025-07-15T11:15:02.623Z"}, "updatedAt": {"$date": "2025-07-15T11:15:02.623Z"}, "PaidAmount": 200, "PaidDate": {"$date": "2025-07-15T11:15:02.492Z"}, "__v": 0}, {"_id": {"$oid": "687fbcb95d848acffa616beb"}, "paymentId": "PAY033", "debtId": "DEBT018", "customerId": "CUST003", "shopId": "SHOP001", "paymentContext": "debt", "amount": 100, "discountAmount": 0, "paymentDate": {"$date": "2025-07-22T16:30:49.716Z"}, "paidAtReal": {"$date": "2025-07-22T16:30:49.717Z"}, "PaymentDelay": 21, "IsOnTime": false, "method": "cash", "paymentType": "offline", "status": "pending", "isConfirmed": false, "refund": {"amount": 0}, "gatewayInfo": {"gatewayFee": 0}, "integrationStatus": "not_applicable", "sessionType": "online", "syncStatus": "synced", "isDeleted": false, "syncedAt": {"$date": "2025-07-22T16:30:49.717Z"}, "verificationAttempts": [], "statusHistory": [], "createdAt": {"$date": "2025-07-22T16:30:49.846Z"}, "updatedAt": {"$date": "2025-07-22T16:30:49.846Z"}, "PaidAmount": 100, "PaidDate": {"$date": "2025-07-22T16:30:49.716Z"}, "__v": 0}, {"_id": {"$oid": "6883307654237c32f76894d3"}, "paymentId": "PAY034", "debtId": "DEBT034", "customerId": "CUST021", "shopId": "SHOP001", "paymentContext": "debt", "amount": 550, "discountAmount": 0, "paymentDate": {"$date": "2025-07-25T07:21:26.673Z"}, "paidAtReal": {"$date": "2025-07-25T07:21:26.673Z"}, "PaymentDelay": 2, "IsOnTime": false, "method": "cash", "paymentType": "offline", "status": "pending", "isConfirmed": false, "refund": {"amount": 0}, "gatewayInfo": {"gatewayFee": 0}, "integrationStatus": "not_applicable", "sessionType": "online", "syncStatus": "synced", "isDeleted": false, "syncedAt": {"$date": "2025-07-25T07:21:26.673Z"}, "verificationAttempts": [], "statusHistory": [], "createdAt": {"$date": "2025-07-25T07:21:26.793Z"}, "updatedAt": {"$date": "2025-07-25T07:21:26.793Z"}, "PaidAmount": 550, "PaidDate": {"$date": "2025-07-25T07:21:26.673Z"}, "__v": 0}]