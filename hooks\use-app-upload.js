/**
 * App Upload Hooks
 * React hooks for app upload functionality
 */
import { useState, useEffect, useCallback } from 'react';
import { appUploadAPI } from '@/lib/api/modules/appUpload';
import { useAuth } from '@/contexts/auth-context';

/**
 * Hook for uploading app files
 */
export const useAppUpload = () => {
  const [uploading, setUploading] = useState(false);
  const [progress, setProgress] = useState(0);
  const [error, setError] = useState(null);
  const { user } = useAuth();

  const uploadApp = useCallback(async (fileData, metadata) => {
    if (!user || !user.role || user.role !== 'superAdmin') {
      throw new Error('Only SuperAdmin can upload apps');
    }

    setUploading(true);
    setProgress(0);
    setError(null);

    try {
      // Create FormData
      const formData = new FormData();
      formData.append('appFile', fileData);
      
      // Add metadata
      Object.keys(metadata).forEach(key => {
        if (metadata[key] !== undefined && metadata[key] !== null) {
          formData.append(key, metadata[key]);
        }
      });

      // Simulate progress (since we can't track real upload progress with FormData)
      const progressInterval = setInterval(() => {
        setProgress(prev => Math.min(prev + 10, 90));
      }, 200);

      const result = await appUploadAPI.uploadApp(formData);
      
      clearInterval(progressInterval);
      setProgress(100);
      
      return result;
    } catch (error) {
      setError(error.message);
      throw error;
    } finally {
      setUploading(false);
      setTimeout(() => setProgress(0), 1000);
    }
  }, [user]);

  const resetUpload = useCallback(() => {
    setProgress(0);
    setError(null);
    setUploading(false);
  }, []);

  return {
    uploading,
    progress,
    error,
    uploadApp,
    resetUpload
  };
};

/**
 * Hook for managing app uploads list
 */
export const useAppsList = (initialFilters = {}) => {
  const [apps, setApps] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
    totalPages: 1,
    totalDocs: 0
  });
  const [filters, setFilters] = useState(initialFilters);
  const { user } = useAuth();

  const fetchApps = useCallback(async (newFilters = {}) => {
    if (!user || !user.role || user.role !== 'superAdmin') {
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const queryFilters = { ...filters, ...newFilters };
      const result = await appUploadAPI.getApps(queryFilters);
      
      if (result.success) {
        setApps(result.data.docs || []);
        setPagination({
          page: result.data.page || 1,
          limit: result.data.limit || 10,
          totalPages: result.data.totalPages || 1,
          totalDocs: result.data.totalDocs || 0
        });
      }
    } catch (error) {
      setError(error.message);
      console.error('Error fetching apps:', error);
    } finally {
      setLoading(false);
    }
  }, [filters, user]);

  const updateFilters = useCallback((newFilters) => {
    setFilters(prev => ({ ...prev, ...newFilters, page: 1 }));
  }, []);

  const changePage = useCallback((page) => {
    setFilters(prev => ({ ...prev, page }));
  }, []);

  const refreshApps = useCallback(() => {
    fetchApps();
  }, [fetchApps]);

  useEffect(() => {
    fetchApps();
  }, [fetchApps]);

  return {
    apps,
    loading,
    error,
    pagination,
    filters,
    fetchApps,
    updateFilters,
    changePage,
    refreshApps
  };
};

/**
 * Hook for managing individual app operations
 */
export const useAppManagement = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const { user } = useAuth();

  const updateApp = useCallback(async (uploadId, updateData) => {
    if (!user || !user.role || user.role !== 'superAdmin') {
      throw new Error('Only SuperAdmin can update apps');
    }

    setLoading(true);
    setError(null);

    try {
      const result = await appUploadAPI.updateApp(uploadId, updateData);
      return result;
    } catch (error) {
      setError(error.message);
      throw error;
    } finally {
      setLoading(false);
    }
  }, [user]);

  const deleteApp = useCallback(async (uploadId) => {
    if (!user || !user.role || user.role !== 'superAdmin') {
      throw new Error('Only SuperAdmin can delete apps');
    }

    setLoading(true);
    setError(null);

    try {
      const result = await appUploadAPI.deleteApp(uploadId);
      return result;
    } catch (error) {
      setError(error.message);
      throw error;
    } finally {
      setLoading(false);
    }
  }, [user]);

  const setLatestVersion = useCallback(async (uploadId, platform) => {
    if (!user || !user.role || user.role !== 'superAdmin') {
      throw new Error('Only SuperAdmin can set latest version');
    }

    setLoading(true);
    setError(null);

    try {
      const result = await appUploadAPI.setLatestVersion(uploadId, platform);
      return result;
    } catch (error) {
      setError(error.message);
      throw error;
    } finally {
      setLoading(false);
    }
  }, [user]);

  const downloadApp = useCallback(async (uploadId) => {
    setError(null);
    try {
      await appUploadAPI.downloadApp(uploadId);
    } catch (error) {
      setError(error.message);
      throw error;
    }
  }, []);

  return {
    loading,
    error,
    updateApp,
    deleteApp,
    setLatestVersion,
    downloadApp
  };
};

/**
 * Hook for app download functionality (public)
 */
export const useAppDownload = () => {
  const [downloading, setDownloading] = useState(false);
  const [error, setError] = useState(null);

  const downloadLatest = useCallback(async (platform = 'android') => {
    setDownloading(true);
    setError(null);

    try {
      await appUploadAPI.downloadLatest(platform);
    } catch (error) {
      setError(error.message);
      throw error;
    } finally {
      setDownloading(false);
    }
  }, []);

  const downloadById = useCallback(async (uploadId) => {
    setDownloading(true);
    setError(null);

    try {
      await appUploadAPI.downloadApp(uploadId);
    } catch (error) {
      setError(error.message);
      throw error;
    } finally {
      setDownloading(false);
    }
  }, []);

  return {
    downloading,
    error,
    downloadLatest,
    downloadById
  };
};

/**
 * Hook for app statistics
 */
export const useAppStats = () => {
  const [stats, setStats] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const { user } = useAuth();

  const fetchStats = useCallback(async (filters = {}) => {
    if (!user || !user.role || user.role !== 'superAdmin') {
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const result = await appUploadAPI.getStats(filters);
      
      if (result.success) {
        setStats(result.data || []);
      }
    } catch (error) {
      setError(error.message);
      console.error('Error fetching app stats:', error);
    } finally {
      setLoading(false);
    }
  }, [user]);

  useEffect(() => {
    fetchStats();
  }, [fetchStats]);

  return {
    stats,
    loading,
    error,
    fetchStats
  };
};

/**
 * Hook for getting latest app by platform
 */
export const useLatestApp = (platform) => {
  const [app, setApp] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const fetchLatestApp = useCallback(async () => {
    if (!platform) return;

    setLoading(true);
    setError(null);

    try {
      const result = await appUploadAPI.getLatestByPlatform(platform);
      
      if (result.success) {
        setApp(result.data);
      }
    } catch (error) {
      setError(error.message);
      console.error('Error fetching latest app:', error);
    } finally {
      setLoading(false);
    }
  }, [platform]);

  useEffect(() => {
    fetchLatestApp();
  }, [fetchLatestApp]);

  return {
    app,
    loading,
    error,
    refetch: fetchLatestApp
  };
}; 