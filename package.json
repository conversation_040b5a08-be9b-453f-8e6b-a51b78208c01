{"name": "deyncare-backend", "version": "1.0.0", "main": "server.js", "type": "commonjs", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "create-admin": "node src/scripts/createSuperAdmin.js", "test": "echo \"Error: no test specified\" && exit 1", "cron": "node src/cron/subscriptionTasks.js", "cron:trials": "node src/cron/subscriptionTasks.js trialReminders", "cron:expiry": "node src/cron/subscriptionTasks.js expiryReminders", "cron:renewals": "node src/cron/subscriptionTasks.js autoRenewals", "cron:deactivate": "node src/cron/subscriptionTasks.js deactivateExpired"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"bcrypt": "^5.1.1", "bcryptjs": "^2.4.3", "compression": "^1.7.4", "cookie-parser": "^1.4.6", "cors": "^2.8.5", "dotenv": "^16.3.1", "evc-plus": "^1.0.6", "exceljs": "^4.4.0", "express": "^4.17.1", "express-rate-limit": "^6.7.1", "express-session": "^1.18.1", "express-validator": "^7.0.1", "firebase": "^11.9.1", "firebase-admin": "^13.4.0", "helmet": "^7.0.0", "joi": "^17.13.3", "json2csv": "^6.0.0-alpha.2", "jsonwebtoken": "^9.0.1", "mongoose": "^7.4.2", "mongoose-paginate-v2": "^1.9.0", "morgan": "^1.10.0", "multer": "^1.4.5-lts.2", "node-cron": "^3.0.3", "nodemailer": "^6.9.4", "path-to-regexp": "^6.2.1"}, "devDependencies": {"nodemon": "^3.0.1"}}