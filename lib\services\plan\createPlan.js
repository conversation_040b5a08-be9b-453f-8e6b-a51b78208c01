/**
 * Create Plan Service
 * 
 * Creates a new pricing plan with backend validation
 * UPDATED: Now matches backend implementation exactly
 */

import apiBridge from '@/lib/api/bridge';
import { ENDPOINTS } from '@/lib/api/contract';
import { handleError, processApiResponse, logApiCall } from '../baseService';

/**
 * Create a new plan (SuperAdmin only)
 * Backend automatically enables all features regardless of input
 * @param {Object} planData - Plan creation data
 * @returns {Promise<Object>} API response with created plan
 */
async function createPlan(planData) {
  try {
    if (!planData) {
      throw new Error('Plan data is required');
    }

    // Validate required fields
    const requiredFields = ['name', 'type', 'displayName', 'pricing'];
    for (const field of requiredFields) {
      if (!planData[field]) {
        throw new Error(`${field} is required`);
      }
    }

    // Ensure pricing has required sub-fields
    if (!planData.pricing.basePrice && planData.pricing.basePrice !== 0) {
      throw new Error('pricing.basePrice is required');
    }
    if (!planData.pricing.billingCycle) {
      throw new Error('pricing.billingCycle is required');
    }

    // Process plan data to match backend expectations
    const processedData = {
      ...planData,
      // Ensure all features are set to true (backend enforces this anyway)
      features: {
        debtTracking: true,
        customerPayments: true,
        smsReminders: true,
        smartRiskScore: true,
            businessDashboard: true,
    exportReports: true,
    customerProfiles: true,
        offlineSupport: true,
        // Override any passed features to ensure they're all true
        ...(planData.features || {})
      },
      // Set defaults that match backend model
      pricing: {
        currency: 'USD',
        trialDays: 0,
        setupFee: 0,
        ...planData.pricing
      },
      limits: {
        maxProducts: 1000,
        maxEmployees: 10,
        maxStorageMB: 500,
        maxCustomers: 1000,
        maxDailyTransactions: 500,
        ...planData.limits
      },
      isActive: planData.isActive !== undefined ? planData.isActive : true,
      displayOrder: planData.displayOrder || 1,
      metadata: {
        isRecommended: false,
        tags: [],
        customFields: {},
        ...(planData.metadata || {})
      }
    };

    logApiCall('PlanService.createPlan', ENDPOINTS.PLANS.BASE, processedData);

    const response = await apiBridge.post(ENDPOINTS.PLANS.BASE, processedData, {
      skipCache: true
    });

    const result = processApiResponse(response, 'Plan created successfully');

    // Clear related caches
    if (typeof window !== 'undefined') {
      // Clear plans cache in browser
      localStorage.removeItem('cache_plans-list');
      localStorage.removeItem('cache_plan-stats');
    }

    return result;
  } catch (error) {
    handleError(error, 'PlanService.createPlan', true);
    throw error;
  }
}

export default createPlan;