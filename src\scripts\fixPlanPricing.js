/**
 * Fix Plan Pricing Script
 * Updates existing plan pricing to match expected values
 * Also ensures dynamic plan creation works correctly
 */

const mongoose = require('mongoose');
const { Plan } = require('../models');
const { logInfo, logError, logSuccess } = require('../utils');

/**
 * Fix plan pricing in database and ensure dynamic creation works
 */
async function fixPlanPricing() {
  try {
    logInfo('Starting plan pricing fix and verification...', 'FixPlanPricing');

    // Check if plans exist
    const planCount = await Plan.countDocuments({});
    
    if (planCount === 0) {
      logInfo('No plans found. Creating default plans with correct pricing...', 'FixPlanPricing');
      await Plan.createDefaultPlans();
      logSuccess('Default plans created successfully', 'FixPlanPricing');
    } else {
      logInfo(`Found ${planCount} existing plans. Updating pricing...`, 'FixPlanPricing');
      
      // Update monthly plan to $0.01
      const monthlyUpdate = await Plan.findOneAndUpdate(
        { type: 'monthly' },
        { 
          $set: { 
            'pricing.basePrice': 0.01,
            'displayName': 'Monthly Plan',
            'description': 'Full access to all features with monthly billing'
          }
        },
        { new: true }
      );

      if (monthlyUpdate) {
        logSuccess(`Monthly plan updated to $${monthlyUpdate.pricing.basePrice}`, 'FixPlanPricing');
      } else {
        logInfo('Monthly plan not found in existing plans', 'FixPlanPricing');
      }

      // Update yearly plan to $90
      const yearlyUpdate = await Plan.findOneAndUpdate(
        { type: 'yearly' },
        { 
          $set: { 
            'pricing.basePrice': 90,
            'displayName': 'Annual Plan',
            'description': 'Save with yearly billing'
          }
        },
        { new: true }
      );

      if (yearlyUpdate) {
        logSuccess(`Yearly plan updated to $${yearlyUpdate.pricing.basePrice}`, 'FixPlanPricing');
      } else {
        logInfo('Yearly plan not found in existing plans', 'FixPlanPricing');
      }
    }

    // List all plans to verify
    const allPlans = await Plan.find({}).sort({ displayOrder: 1 });
    
    logInfo('✅ Current plan pricing after fix:', 'FixPlanPricing');
    allPlans.forEach(plan => {
      console.log(`   - ${plan.displayName} (${plan.type}): $${plan.pricing.basePrice}`);
    });

    // Verify the pricing matches expected values
    const monthlyPlan = allPlans.find(p => p.type === 'monthly');
    const yearlyPlan = allPlans.find(p => p.type === 'yearly');
    
    const monthlyCorrect = monthlyPlan && monthlyPlan.pricing.basePrice === 0.01;
    const yearlyCorrect = yearlyPlan && yearlyPlan.pricing.basePrice === 90;
    
    if (monthlyCorrect && yearlyCorrect) {
      logSuccess('✅ All plan pricing is correct!', 'FixPlanPricing');
    } else {
      logError('❌ Some plan pricing is still incorrect', 'FixPlanPricing');
      if (!monthlyCorrect) console.log(`   Monthly plan: Expected $0.01, Got $${monthlyPlan?.pricing.basePrice || 'Not found'}`);
      if (!yearlyCorrect) console.log(`   Yearly plan: Expected $90, Got $${yearlyPlan?.pricing.basePrice || 'Not found'}`);
    }

    logSuccess('Plan pricing fix completed successfully', 'FixPlanPricing');
    return { success: true, monthlyCorrect, yearlyCorrect, planCount: allPlans.length };

  } catch (error) {
    logError('Failed to fix plan pricing', 'FixPlanPricing', error);
    throw error;
  }
}

// Run the script if called directly
if (require.main === module) {
  const runFix = async () => {
    try {
      await mongoose.connect(process.env.DB_URL);
      await fixPlanPricing();
      process.exit(0);
    } catch (error) {
      console.error('Script failed:', error);
      process.exit(1);
    }
  };

  runFix();
}

module.exports = { fixPlanPricing }; 