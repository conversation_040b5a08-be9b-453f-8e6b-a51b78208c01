/**
 * Plan Service
 * Handles all business logic related to subscription plans
 * 
 * This module serves as the main entry point for plan-related service operations.
 * Each operation has been refactored into its own file in the Plan/ directory
 * for better code organization, maintainability, and testing.
 * 
 * All plans (trial, monthly, yearly) will have full access to features without limitations.
 */

// Import all services from the Plan/ directory
const {
  initializePlans,
  getAllPlans,
  getPlanByType,
  getPlanById,
  createPlan,
  updatePlan,
  deletePlan,
  getPricing,
  getPlanStats
} = require('./Plan');

// Re-export all plan services
const PlanService = {
  initializePlans,
  getAllPlans,
  getPlanByType,
  getPlanById,
  createPlan,
  updatePlan,
  deletePlan,
  getPricing,
  getPlanStats
};

module.exports = PlanService;
