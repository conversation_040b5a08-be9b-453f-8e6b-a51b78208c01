/**
 * Update Payment Methods Service
 * Matches backend: settingsController.updatePaymentMethods
 * PUT /api/settings/payment-methods
 */

import { updatePaymentMethods as updatePaymentMethodsAPI } from '../../api/modules/settings';
import { handleError, logApiCall } from '../baseService';

/**
 * Update payment method settings
 * @param {Object} data - Payment method settings
 * @param {boolean} [data.enableOnline] - Enable online payments
 * @param {boolean} [data.enableOffline] - Enable offline payments
 * @param {string} [data.context='general'] - Context for payment methods ('general', 'subscription', 'pos')
 * @param {Array} [data.methods] - Array of payment method names
 * @param {string|null} [data.shopId] - Shop ID for shop-specific settings, null for global
 * @returns {Promise<Object>} Update result
 */
const updatePaymentMethods = async (data) => {
  const context = `updatePaymentMethods(${data.context || 'general'}${data.shopId ? `, shopId: ${data.shopId}` : ', global'})`;
  
  try {
    // Prepare payload in exact backend expected format:
    // { enableOnline, enableOffline, context, methods, shopId }
    const payload = {
      context: data.context || 'general',
      shopId: data.shopId || null
    };
    
    // Only include boolean values if they are explicitly provided
    if (typeof data.enableOnline === 'boolean') {
      payload.enableOnline = data.enableOnline;
    }
    
    if (typeof data.enableOffline === 'boolean') {
      payload.enableOffline = data.enableOffline;
    }
    
    // Include methods array if provided
    if (Array.isArray(data.methods)) {
      payload.methods = data.methods;
    }
    
    logApiCall(context, 'PUT /api/settings/payment-methods', payload);
    
    const response = await updatePaymentMethodsAPI(payload);
    
    if (response?.success) {
      console.log(`[${context}] Success:`, {
        message: response.message,
        context: response.context,
        shopId: response.shopId
      });
      
      return {
        success: true,
        message: response.message || 'Payment methods updated successfully',
        context: response.context,
        shopId: response.shopId
      };
    } else {
      console.warn(`[${context}] API returned success=false:`, response);
      return {
        success: false,
        message: response?.message || 'Failed to update payment methods'
      };
    }
    
  } catch (error) {
    console.error(`[${context}] Error:`, error);
    handleError(error, context, false);
    
    return {
      success: false,
      message: error.message || 'Failed to update payment methods',
      error: error
    };
  }
};

export default updatePaymentMethods; 