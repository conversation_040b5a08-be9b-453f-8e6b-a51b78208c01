const mongoose = require('mongoose');
const { User } = require('../models');

// Quick cleanup for specific problematic user
const cleanSpecificUser = async () => {
  try {
    // Connect to database using environment variable
    const mongoUri = process.env.MONGODB_URI || 'mongodb://localhost:27017/Deyncare';
    await mongoose.connect(mongoUri);
    console.log('✅ Connected to database');

    // Find and clean the specific problematic user
    const email = '<EMAIL>';
    
    const user = await User.findOne({ email });
    if (!user) {
      console.log(`❌ User not found: ${email}`);
      return;
    }

    console.log(`🔍 Found user: ${email} (Status: ${user.status}, ID: ${user.userId})`);
    
    if (user.status === 'pending_email_verification') {
      // Soft delete the orphaned user
      user.isDeleted = true;
      user.deletedAt = new Date();
      user.status = 'deleted_orphaned';
      await user.save();
      
      console.log(`✅ Successfully cleaned orphaned user: ${email}`);
    } else {
      console.log(`ℹ️ User status is ${user.status}, not cleaning`);
    }

  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    await mongoose.disconnect();
    console.log('📤 Disconnected from database');
  }
};

// Run the cleanup if this script is executed directly
if (require.main === module) {
  cleanSpecificUser();
}

module.exports = cleanSpecificUser; 