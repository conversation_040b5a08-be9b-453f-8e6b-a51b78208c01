require('dotenv').config();
const mongoose = require('mongoose');
const app = require('./src/app');
const { logInfo, logDatabase, logError, logSuccess } = require('./src/utils/logger');
const { bootstrap } = require('./src/config/bootstrap');

// Server configuration
const PORT = process.env.PORT || 5000;
const MONGODB_URI = process.env.MONGODB_URI;

// MongoDB Connection Options - Optimized and compatible with current MongoDB driver
const mongoOptions = {
  useNewUrlParser: true,
  useUnifiedTopology: true,
  autoIndex: true,
  connectTimeoutMS: 6000, // 6 seconds for connection timeout
  socketTimeoutMS: 25000, // 25 seconds for socket timeout
  family: 4, // Use IPv4, skip trying IPv6
  maxPoolSize: 10, // Maintain up to 10 socket connections
  serverSelectionTimeoutMS: 8000, // 8 seconds for server selection
  heartbeatFrequencyMS: 10000, // Check connection health every 10 seconds
  // Removed bufferMaxEntries as it's not supported in current MongoDB driver versions
};

// Application state management
const appState = {
  dbConnected: false,
  bootstrapCompleted: false,
  criticalSystemsReady: false,
  startTime: new Date(),
  errors: []
};

// Display startup message
console.log('\n🚀 Starting DeynCare Backend Server (Optimized)...');

// Use a simplified server if needed due to path-to-regexp issues
const useSimplifiedServer = process.env.USE_SIMPLIFIED_SERVER === 'true';

if (useSimplifiedServer) {
  // Create a simple standalone Express server
  console.log('Using simplified server due to path-to-regexp configuration issues...');
  
  const express = require('express');
  const cors = require('cors');
  const cookieParser = require('cookie-parser');
  const authRoutes = require('./src/routes/authRoutes');
  
  const simpleApp = express();
  
  // Basic middleware
  simpleApp.use(express.json());
  simpleApp.use(express.urlencoded({ extended: true }));
  simpleApp.use(cookieParser());
  simpleApp.use(cors({ origin: process.env.CORS_ORIGIN || '*', credentials: true }));
  
  // Auth routes
  simpleApp.use('/api/auth', authRoutes);
  
  // Enhanced health check with system status
  simpleApp.get('/api/health', (req, res) => {
    res.status(200).json({ 
      status: 'success', 
      message: 'DeynCare API is running in simplified mode',
      timestamp: new Date().toISOString(),
      uptime: Math.floor((Date.now() - appState.startTime) / 1000),
      environment: process.env.NODE_ENV || 'development'
    });
  });
  
  // Default route
  simpleApp.get('/', (req, res) => {
    res.status(200).json({
      name: 'DeynCare API (Simplified)',
      version: '1.0.0',
      description: 'Debt management system API',
      endpoints: {
        auth: '/api/auth',
        health: '/api/health'
      }
    });
  });
  
  // Connect to MongoDB and start server
  mongoose.connect(MONGODB_URI, mongoOptions)
    .then(async () => {
      appState.dbConnected = true;
      logSuccess('Connected to MongoDB Atlas successfully!', 'MongoDB');
      
      // Start server immediately, run bootstrap in background
      const server = simpleApp.listen(PORT, '0.0.0.0', () => {
        logSuccess(`✅ DeynCare API Server (Simplified) running on port ${PORT}`, 'Server');
        logInfo(`Environment: ${process.env.NODE_ENV || 'development'}`, 'Server');
        logInfo(`MongoDB Connection: Active`, 'Server');
      });
      
      // Run bootstrap in background - don't block server startup
      bootstrap().then(result => {
        appState.bootstrapCompleted = true;
        appState.criticalSystemsReady = result.success;
        if (result.success) {
          logSuccess('Bootstrap completed successfully in background', 'Bootstrap');
        } else {
          appState.errors.push(result.error);
          logError(`Bootstrap failed but server continues: ${result.error}`, 'Bootstrap');
        }
      }).catch(error => {
        appState.errors.push(error.message);
        logError(`Bootstrap failed but server continues: ${error.message}`, 'Bootstrap');
      });
      
      // Handle graceful shutdown
      const gracefulShutdown = (signal) => {
        logInfo(`${signal} received. Starting graceful shutdown...`, 'Server');
        server.close(() => {
          mongoose.connection.close(false, () => {
            logInfo('Process terminated.', 'Server');
            process.exit(0);
          });
          
          setTimeout(() => {
            logError('Forcing process termination', 'Server');
            process.exit(1);
          }, 10000);
        });
      };
      
      process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
      process.on('SIGINT', () => gracefulShutdown('SIGINT'));
    })
    .catch((err) => {
      logError(`MongoDB connection failed: ${err.message}`, 'MongoDB', {
        error: err.toString(),
        stack: err.stack
      });
      process.exit(1);
    });
} else {
  // Use the full app with all features
  // Connect to MongoDB
  mongoose.connect(MONGODB_URI, mongoOptions)
    .then(async () => {
      appState.dbConnected = true;
      logSuccess('Connected to MongoDB Atlas successfully!', 'MongoDB');
      
      // Start the server IMMEDIATELY after database connection
      // Don't wait for bootstrap to complete
      const server = app.listen(PORT, '0.0.0.0', () => {
        logSuccess(`✅ DeynCare API Server running on port ${PORT}`, 'Server');
        logInfo(`Environment: ${process.env.NODE_ENV || 'development'}`, 'Server');
        logInfo(`MongoDB Connection: Active`, 'Server');
        logInfo(`Server Time: ${new Date().toLocaleString()}`, 'Server');
        
        // Display access URLs
        console.log('\n📡 API Access Points:');
        console.log(`➡️ Local:   http://localhost:${PORT}/api`);
        console.log(`➡️ Network: http://<your-ip-address>:${PORT}/api`);
        console.log(`➡️ Health:  http://localhost:${PORT}/api/health\n`);
      });
      
      // Add enhanced health check route to main app
      app.get('/api/health', (req, res) => {
        const uptime = Math.floor((Date.now() - appState.startTime) / 1000);
        const healthStatus = {
          status: appState.dbConnected ? 'healthy' : 'unhealthy',
          timestamp: new Date().toISOString(),
          uptime: `${uptime}s`,
          environment: process.env.NODE_ENV || 'development',
          database: {
            connected: appState.dbConnected,
            readyState: mongoose.connection.readyState
          },
          bootstrap: {
            completed: appState.bootstrapCompleted,
            criticalSystemsReady: appState.criticalSystemsReady
          },
          version: '1.0.0'
        };
        
        if (appState.errors.length > 0) {
          healthStatus.warnings = appState.errors;
        }
        
        const statusCode = appState.dbConnected ? 200 : 503;
        res.status(statusCode).json(healthStatus);
      });
      
      // Run bootstrap process in background - don't block server startup
      bootstrap().then(result => {
        appState.bootstrapCompleted = true;
        appState.criticalSystemsReady = result.success;
        if (result.success) {
          logSuccess(`Bootstrap completed successfully in ${result.totalTime.toFixed(2)}ms`, 'Bootstrap');
        } else {
          appState.errors.push(result.error);
          logError(`Bootstrap failed but server continues: ${result.error}`, 'Bootstrap');
        }
      }).catch(error => {
        appState.errors.push(error.message);
        logError(`Bootstrap failed but server continues: ${error.message}`, 'Bootstrap');
      });
      
      // Handle graceful shutdown
      const gracefulShutdown = (signal) => {
        logInfo(`${signal} received. Starting graceful shutdown...`, 'Server');
        server.close(() => {
          logInfo('HTTP server closed.', 'Server');
          
          // Close database connection
          mongoose.connection.close(false, () => {
            logInfo('MongoDB connection closed.', 'Server');
            logInfo('Process terminated.', 'Server');
            process.exit(0);
          });
          
          // Force close after 10 seconds
          setTimeout(() => {
            logError('Forcing process termination after timeout', 'Server');
            process.exit(1);
          }, 10000);
        });
      };
      
      // Listen for termination signals
      process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
      process.on('SIGINT', () => gracefulShutdown('SIGINT'));
    })
    .catch((err) => {
      logError(`MongoDB connection failed: ${err.message}`, 'MongoDB', {
        error: err.toString(),
        stack: err.stack
      });
      
      console.error('\n❌ Database Connection Error:');
      console.error(`→ Message: ${err.message}`);
      console.error('→ Make sure your MongoDB connection string is correct in .env file');
      console.error('→ Check if MongoDB Atlas IP whitelist includes your current IP\n');
      
      process.exit(1);
    });
}

// Handle uncaught exceptions
process.on('uncaughtException', (err) => {
  logError(`Uncaught Exception: ${err.message}`, 'Process', {
    stack: err.stack
  });
  process.exit(1);
});

// Handle unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  logError(`Unhandled Rejection at: ${promise}, reason: ${reason}`, 'Process');
  // Don't exit on unhandled rejections in production to maintain service availability
  if (process.env.NODE_ENV !== 'production') {
    process.exit(1);
  }
});