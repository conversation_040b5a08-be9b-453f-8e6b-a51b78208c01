/**
 * DeynCare ML API Integration Service
 * Handles risk assessment API calls
 */

const ML_API_BASE_URL = process.env.NEXT_PUBLIC_ML_API_URL || 'https://deyncare-ml.onrender.com';

/**
 * Single customer risk assessment
 * @param {Object} debtData - Debt information
 * @param {number} debtData.DebtPaidRatio - Ratio of paid amount (0-1)
 * @param {number} debtData.PaymentDelay - Days of delay (negative = early)
 * @param {number} debtData.OutstandingDebt - Remaining debt amount
 * @param {number} debtData.DebtAmount - Total debt amount
 * @returns {Promise<Object>} Risk assessment result
 */
export async function assessSingleRisk(debtData) {
  try {
    const response = await fetch(`${ML_API_BASE_URL}/predict_single/`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(debtData),
    });

    if (!response.ok) {
      throw new Error(`ML API Error: ${response.status} ${response.statusText}`);
    }

    const result = await response.json();
    return {
      success: true,
      data: result,
    };
  } catch (error) {
    console.error('[ML API] Risk assessment failed:', error);
    return {
      success: false,
      error: error.message,
    };
  }
}

/**
 * Bulk CSV risk assessment
 * @param {File} csvFile - CSV file with customer data
 * @returns {Promise<Object>} Bulk assessment results
 */
export async function assessBulkRisk(csvFile) {
  try {
    const formData = new FormData();
    formData.append('file', csvFile);

    const response = await fetch(`${ML_API_BASE_URL}/upload_csv/`, {
      method: 'POST',
      body: formData,
    });

    if (!response.ok) {
      throw new Error(`ML API Error: ${response.status} ${response.statusText}`);
    }

    const result = await response.json();
    return {
      success: true,
      data: result,
    };
  } catch (error) {
    console.error('[ML API] Bulk assessment failed:', error);
    return {
      success: false,
      error: error.message,
    };
  }
}

/**
 * Download sample CSV template
 * @returns {Promise<Blob>} CSV file blob
 */
export async function downloadSampleCSV() {
  try {
    const response = await fetch(`${ML_API_BASE_URL}/sample_data.csv`);
    
    if (!response.ok) {
      throw new Error(`Sample download failed: ${response.status}`);
    }

    return await response.blob();
  } catch (error) {
    console.error('[ML API] Sample download failed:', error);
    throw error;
  }
}

/**
 * Convert debt record to ML API format
 * @param {Object} debt - Debt record from database
 * @returns {Object} Formatted data for ML API
 */
export function formatDebtForML(debt) {
  const debtAmount = debt.debtAmount || debt.amount || 0;
  const paidAmount = debt.paidAmount || 0;
  const dueDate = new Date(debt.dueDate);
  const currentDate = new Date();
  
  // Calculate payment delay (positive = late, negative = early)
  const paymentDelay = Math.floor((currentDate - dueDate) / (1000 * 60 * 60 * 24));
  
  return {
    DebtPaidRatio: debtAmount > 0 ? paidAmount / debtAmount : 0,
    PaymentDelay: paymentDelay,
    OutstandingDebt: Math.max(0, debtAmount - paidAmount),
    DebtAmount: debtAmount,
  };
}

/**
 * Get risk level color for UI display
 * @param {string} riskLevel - Risk level from ML API
 * @returns {string} Tailwind CSS color class
 */
export function getRiskLevelColor(riskLevel) {
  switch (riskLevel) {
    case 'Low Risk':
      return 'text-green-600 bg-green-50 border-green-200';
    case 'Medium Risk':
      return 'text-yellow-600 bg-yellow-50 border-yellow-200';
    case 'High Risk':
      return 'text-red-600 bg-red-50 border-red-200';
    default:
      return 'text-gray-600 bg-gray-50 border-gray-200';
  }
}

/**
 * Calculate fallback risk score when ML API is unavailable
 * @param {Object} debtData - Debt information
 * @returns {number} Fallback risk score
 */
export function calculateFallbackRisk(debtData) {
  const { DebtPaidRatio, PaymentDelay, OutstandingDebt, DebtAmount } = debtData;
  
  // Use the same FairRisk algorithm as the ML API
  const riskScore = (1 - DebtPaidRatio) + (PaymentDelay / 10) + (OutstandingDebt / DebtAmount);
  
  return Math.min(Math.max(riskScore, 0), 1); // Clamp between 0 and 1
}

/**
 * Robust error handling with fallback
 * @param {Object} debtData - Debt information
 * @returns {Promise<Object>} Risk assessment result with fallback
 */
export async function assessRiskWithFallback(debtData) {
  try {
    const result = await assessSingleRisk(debtData);
    return result;
  } catch (error) {
    console.warn('[ML API] Primary endpoint failed, using fallback logic');
    
    // Fallback risk calculation
    const fallbackScore = calculateFallbackRisk(debtData);
    return {
      success: true,
      data: {
        RiskScore: fallbackScore,
        PredictedRiskLevel: fallbackScore > 0.6 ? 'High Risk' : 
                           fallbackScore > 0.3 ? 'Medium Risk' : 'Low Risk',
        source: 'fallback'
      }
    };
  }
}

/**
 * Test ML API connectivity
 * @returns {Promise<Object>} Connection test result
 */
export async function testMLConnection() {
  try {
    const response = await fetch(`${ML_API_BASE_URL}/`);
    
    if (!response.ok) {
      throw new Error(`Connection test failed: ${response.status}`);
    }
    
    const result = await response.json();
    return {
      success: true,
      message: result.message || 'ML API is accessible',
      url: ML_API_BASE_URL
    };
  } catch (error) {
    return {
      success: false,
      error: error.message,
      url: ML_API_BASE_URL
    };
  }
} 