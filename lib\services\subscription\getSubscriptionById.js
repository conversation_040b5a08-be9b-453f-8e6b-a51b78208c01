import apiBridge from '@/lib/api/bridge';
import { ENDPOINTS } from '@/lib/api/contract';;
import { handleError, processApiResponse } from '../baseService';

/**
 * Get subscription by ID
 * @param {string} subscriptionId - Subscription ID
 * @returns {Promise<Object>} Subscription details
 */
async function getSubscriptionById(subscriptionId) {
  try {
    if (!subscriptionId) {
      throw new Error('Subscription ID is required');
    }

    const response = await apiBridge.get(`${ENDPOINTS.SUBSCRIPTIONS.BASE}/${subscriptionId}`);
    
    const result = processApiResponse(response);
    return result.subscription || result;
  } catch (error) {
    handleError(error, 'SubscriptionService.getSubscriptionById', true);
    throw error;
  }
}

export default getSubscriptionById; 
