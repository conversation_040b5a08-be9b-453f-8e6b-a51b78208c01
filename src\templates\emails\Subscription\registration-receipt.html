<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Registration Receipt - DeynCare</title>
    <style>
        /* DeynCare Design System */
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333333;
            margin: 0;
            padding: 20px;
            background-color: #f8f9fa;
        }
        
        .receipt-container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #ffffff;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
        
        .receipt-header {
            background: linear-gradient(135deg, #2e86de 0%, #0abde3 100%);
            color: white;
            padding: 30px 20px;
            text-align: center;
        }
        
        .logo {
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 8px;
        }
        
        .receipt-title {
            font-size: 18px;
            font-weight: 400;
            opacity: 0.9;
        }
        
        .success-badge {
            background-color: #27ae60;
            color: white;
            padding: 12px 24px;
            border-radius: 50px;
            text-align: center;
            font-weight: 600;
            margin: 25px 20px 0;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }
        
        .receipt-body {
            padding: 30px 25px;
        }
        
        .greeting {
            font-size: 18px;
            margin-bottom: 20px;
            color: #333333;
        }
        
        .receipt-summary {
            background-color: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin: 25px 0;
            border-left: 4px solid #2e86de;
        }
        
        .summary-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #e9ecef;
        }
        
        .summary-row:last-child {
            border-bottom: none;
            font-weight: 600;
            font-size: 18px;
            color: #27ae60;
            margin-top: 10px;
            padding-top: 15px;
            border-top: 2px solid #e9ecef;
        }
        
        .summary-label {
            color: #666666;
            font-weight: 500;
        }
        
        .summary-value {
            font-weight: 600;
            color: #333333;
        }
        
        .receipt-number {
            font-family: 'Courier New', monospace;
            background-color: #e9ecef;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 14px;
            font-weight: bold;
        }
        
        .action-buttons {
            text-align: center;
            margin: 30px 0;
        }
        
        .btn-primary {
            display: inline-block;
            background-color: #2e86de;
            color: white !important;
            text-decoration: none;
            padding: 14px 28px;
            border-radius: 8px;
            font-weight: 600;
            margin: 0 8px;
            transition: all 0.3s ease;
        }
        
        .btn-outline {
            display: inline-block;
            border: 2px solid #2e86de;
            color: #2e86de !important;
            background-color: transparent;
            text-decoration: none;
            padding: 12px 26px;
            border-radius: 8px;
            font-weight: 600;
            margin: 0 8px;
            transition: all 0.3s ease;
        }
        
        .receipt-footer {
            background-color: #f8f9fa;
            padding: 20px;
            text-align: center;
            border-top: 1px solid #e9ecef;
        }
        
        .footer-text {
            font-size: 14px;
            color: #666666;
            margin: 5px 0;
        }
        
        .divider {
            height: 1px;
            background-color: #e9ecef;
            margin: 25px 0;
        }
        
        @media only screen and (max-width: 480px) {
            body {
                padding: 10px;
            }
            
            .receipt-body {
                padding: 20px 15px;
            }
            
            .btn-primary, .btn-outline {
                display: block;
                margin: 8px 0;
                text-align: center;
            }
        }
    </style>
</head>
<body>
    <div class="receipt-container">
        <!-- Header -->
        <div class="receipt-header">
            <div class="logo">DeynCare</div>
            <div class="receipt-title">Registration Receipt</div>
        </div>
        
        <!-- Success Badge -->
        <div class="success-badge">
            <span>✅</span>
            <span>Payment Successful</span>
        </div>
        
        <!-- Body -->
        <div class="receipt-body">
            <div class="greeting">
                Hello {{fullName}},
            </div>
            
            <p>Thank you for registering with DeynCare! Your payment has been processed successfully and your account is now active.</p>
            
            <!-- Receipt Summary -->
            <div class="receipt-summary">
                <div class="summary-row">
                    <span class="summary-label">Shop Name</span>
                    <span class="summary-value">{{shopName}}</span>
                </div>
                <div class="summary-row">
                    <span class="summary-label">Plan</span>
                    <span class="summary-value">{{planType}} ({{billingCycle}})</span>
                </div>
                <div class="summary-row">
                    <span class="summary-label">Payment Method</span>
                    <span class="summary-value">{{paymentMethod}}</span>
                </div>
                <div class="summary-row">
                    <span class="summary-label">Transaction ID</span>
                    <span class="summary-value">
                        <span class="receipt-number">{{transactionId}}</span>
                    </span>
                </div>
                <div class="summary-row">
                    <span class="summary-label">Total Paid</span>
                    <span class="summary-value">${{amount}} USD</span>
                </div>
            </div>
            
            <!-- Action Buttons -->
            <div class="action-buttons">
                <a href="{{dashboardUrl}}" class="btn-primary">Access Dashboard</a>
                <a href="{{billingUrl}}" class="btn-outline">View Billing</a>
            </div>
            
            <div class="divider"></div>
            
            <p style="color: #666666; font-size: 14px;">
                <strong>📄 Receipt #{{receiptNumber}}</strong><br>
                Payment Date: {{paymentDate}}<br>
                Next Billing: {{nextBillingDate}}
            </p>
            
            <p style="font-size: 14px; color: #666666;">
                Questions? Reply to this email or contact our support team. We're here to help!
            </p>
        </div>
        
        <!-- Footer -->
        <div class="receipt-footer">
            <div class="footer-text">© {{year}} DeynCare. All rights reserved.</div>
            <div class="footer-text">This email serves as your official receipt.</div>
        </div>
    </div>
</body>
</html> 