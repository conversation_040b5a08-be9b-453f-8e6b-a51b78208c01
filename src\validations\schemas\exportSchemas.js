/**
 * Export Validation Schemas
 * Defines validation rules for export-related requests
 */
const Joi = require('joi');
const validationPatterns = require('../validationPatterns');

const exportSchemas = {
  /**
   * Base export query parameters schema
   * Common parameters for all export endpoints
   */
  baseExportQuery: Joi.object({
    format: Joi.string()
      .valid('csv', 'excel')
      .default('csv')
      .description('Export file format'),

    filename: Joi.string()
      .pattern(validationPatterns.filename)
      .max(100)
      .description('Custom filename for the export'),

    includeInactive: Joi.boolean()
      .default(false)
      .description('Include inactive records in export'),

    dateRange: Joi.object({
      startDate: Joi.date()
        .iso()
        .description('Start date for filtering records'),
      endDate: Joi.date()
        .iso()
        .min(Joi.ref('startDate'))
        .description('End date for filtering records')
    }).description('Date range for filtering records')
  }),

  /**
   * Plan export query parameters schema
   */
  planExportQuery: Joi.object({
    format: Joi.string()
      .valid('csv', 'excel')
      .default('csv')
      .description('Export file format'),

    filename: Joi.string()
      .pattern(validationPatterns.filename)
      .max(100)
      .description('Custom filename for the export'),

    includeInactive: Joi.boolean()
      .default(false)
      .description('Include inactive plans in export'),

    planType: Joi.string()
      .valid('trial', 'monthly', 'yearly')
      .description('Filter by plan type'),

    minPrice: Joi.number()
      .min(0)
      .description('Minimum plan price'),

    maxPrice: Joi.number()
      .min(Joi.ref('minPrice'))
      .description('Maximum plan price'),

    sortBy: Joi.string()
      .valid('name', 'price', 'createdAt', 'updatedAt')
      .default('createdAt')
      .description('Field to sort by'),

    sortOrder: Joi.string()
      .valid('asc', 'desc')
      .default('desc')
      .description('Sort order')
  }),

  /**
   * Subscription export query parameters schema
   */
  subscriptionExportQuery: Joi.object({
    format: Joi.string()
      .valid('csv', 'excel')
      .default('csv')
      .description('Export file format'),

    filename: Joi.string()
      .pattern(validationPatterns.filename)
      .max(100)
      .description('Custom filename for the export'),

    includeInactive: Joi.boolean()
      .default(false)
      .description('Include inactive subscriptions in export'),

    status: Joi.string()
      .valid('active', 'expired', 'cancelled', 'pending')
      .description('Filter by subscription status'),

    planType: Joi.string()
      .valid('trial', 'monthly', 'yearly')
      .description('Filter by plan type'),

    dateRange: Joi.object({
      startDate: Joi.date()
        .iso()
        .description('Start date for filtering subscriptions'),
      endDate: Joi.date()
        .iso()
        .min(Joi.ref('startDate'))
        .description('End date for filtering subscriptions')
    }).description('Date range for filtering subscriptions'),

    paymentMethod: Joi.string()
      .valid('offline', 'evc_plus', 'free')
      .description('Filter by payment method'),

    autoRenew: Joi.boolean()
      .description('Filter by auto-renewal status'),

    sortBy: Joi.string()
      .valid('startDate', 'endDate', 'createdAt', 'updatedAt', 'lastPayment')
      .default('createdAt')
      .description('Field to sort by'),

    sortOrder: Joi.string()
      .valid('asc', 'desc')
      .default('desc')
      .description('Sort order')
  }),

  /**
   * User export query parameters schema (SuperAdmin only)
   */
  userExportQuery: Joi.object({
    format: Joi.string()
      .valid('csv', 'excel')
      .default('csv')
      .description('Export file format'),

    filename: Joi.string()
      .pattern(validationPatterns.filename)
      .max(100)
      .description('Custom filename for the export'),

    role: Joi.string()
      .valid('superAdmin', 'admin', 'employee')
      .description('Filter by user role'),

    status: Joi.string()
      .valid('active', 'inactive', 'suspended', 'email_verified_pending_payment')
      .description('Filter by user status'),

    shopId: Joi.string()
      .description('Filter by shop ID'),

    emailVerified: Joi.boolean()
      .description('Filter by email verification status'),

    isPaid: Joi.boolean()
      .description('Filter by payment status'),

    isSuspended: Joi.boolean()
      .description('Filter by suspension status'),

    startDate: Joi.date()
      .iso()
      .description('Start date for filtering users'),

    endDate: Joi.date()
      .iso()
      .min(Joi.ref('startDate'))
      .description('End date for filtering users')
  }),

  /**
   * Shop export query parameters schema (SuperAdmin only)
   */
  shopExportQuery: Joi.object({
    format: Joi.string()
      .valid('csv', 'excel')
      .default('csv')
      .description('Export file format'),

    filename: Joi.string()
      .pattern(validationPatterns.filename)
      .max(100)
      .description('Custom filename for the export'),

    status: Joi.string()
      .valid('active', 'inactive', 'suspended', 'pending')
      .description('Filter by shop status'),

    isActive: Joi.boolean()
      .description('Filter by active status'),

    registrationStatus: Joi.string()
      .valid('pending', 'approved', 'rejected', 'completed')
      .description('Filter by registration status'),

    paymentStatus: Joi.string()
      .valid('pending', 'paid', 'failed', 'refunded')
      .description('Filter by payment status'),

    businessType: Joi.string()
      .description('Filter by business type'),

    city: Joi.string()
      .description('Filter by city'),

    region: Joi.string()
      .description('Filter by region'),

    country: Joi.string()
      .description('Filter by country'),

    startDate: Joi.date()
      .iso()
      .description('Start date for filtering shops'),

    endDate: Joi.date()
      .iso()
      .min(Joi.ref('startDate'))
      .description('End date for filtering shops')
  }),

  /**
   * Customer export query parameters schema (Admin only)
   */
  customerExportQuery: Joi.object({
    format: Joi.string()
      .valid('csv', 'excel')
      .default('csv')
      .description('Export file format'),

    filename: Joi.string()
      .pattern(validationPatterns.filename)
      .max(100)
      .description('Custom filename for the export'),

    status: Joi.string()
      .valid('active', 'inactive', 'suspended')
      .description('Filter by customer status'),

    isActive: Joi.boolean()
      .description('Filter by active status'),

    riskLevel: Joi.string()
      .valid('Low Risk', 'Medium Risk', 'High Risk')
      .description('Filter by risk level'),

    city: Joi.string()
      .description('Filter by city'),

    region: Joi.string()
      .description('Filter by region'),

    country: Joi.string()
      .description('Filter by country'),

    startDate: Joi.date()
      .iso()
      .description('Start date for filtering customers'),

    endDate: Joi.date()
      .iso()
      .min(Joi.ref('startDate'))
      .description('End date for filtering customers')
  }),

  /**
   * Debt export query parameters schema (Admin only)
   */
  debtExportQuery: Joi.object({
    format: Joi.string()
      .valid('csv', 'excel')
      .default('csv')
      .description('Export file format'),

    filename: Joi.string()
      .pattern(validationPatterns.filename)
      .max(100)
      .description('Custom filename for the export'),

    status: Joi.string()
      .valid('active', 'paid', 'overdue', 'cancelled')
      .description('Filter by debt status'),

    priority: Joi.string()
      .valid('low', 'medium', 'high', 'urgent')
      .description('Filter by priority'),

    riskLevel: Joi.string()
      .valid('Low Risk', 'Medium Risk', 'High Risk')
      .description('Filter by risk level'),

    customerId: Joi.string()
      .description('Filter by customer ID'),

    startDate: Joi.date()
      .iso()
      .description('Start date for filtering debts'),

    endDate: Joi.date()
      .iso()
      .min(Joi.ref('startDate'))
      .description('End date for filtering debts'),

    dueStartDate: Joi.date()
      .iso()
      .description('Due date start for filtering debts'),

    dueEndDate: Joi.date()
      .iso()
      .min(Joi.ref('dueStartDate'))
      .description('Due date end for filtering debts')
  }),

  /**
   * ML Risk export query parameters schema (Admin only)
   */
  mlRiskExportQuery: Joi.object({
    format: Joi.string()
      .valid('csv', 'excel')
      .default('csv')
      .description('Export file format'),

    filename: Joi.string()
      .pattern(validationPatterns.filename)
      .max(100)
      .description('Custom filename for the export'),

    riskLevel: Joi.string()
      .valid('Low Risk', 'Medium Risk', 'High Risk')
      .description('Filter by risk level'),

    minRiskScore: Joi.number()
      .min(0)
      .max(100)
      .description('Minimum risk score'),

    maxRiskScore: Joi.number()
      .min(Joi.ref('minRiskScore'))
      .max(100)
      .description('Maximum risk score'),

    customerId: Joi.string()
      .description('Filter by customer ID'),

    startDate: Joi.date()
      .iso()
      .description('Start date for filtering risk assessments'),

    endDate: Joi.date()
      .iso()
      .min(Joi.ref('startDate'))
      .description('End date for filtering risk assessments')
  })
};

module.exports = exportSchemas; 