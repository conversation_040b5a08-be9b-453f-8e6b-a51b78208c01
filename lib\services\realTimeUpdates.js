/**
 * Real-Time Updates Service
 * Handles automatic data refreshing, cache invalidation, and state synchronization
 */

class RealTimeUpdatesService {
  constructor() {
    this.subscribers = new Map();
    this.updateQueue = new Set();
    this.isProcessingQueue = false;
    this.lastUpdateTime = new Map();
    this.debounceTime = 500;
    
    if (typeof window !== 'undefined') {
      window.addEventListener('storage', this.handleStorageChange.bind(this));
      window.addEventListener('focus', this.handleWindowFocus.bind(this));
    }
  }

  subscribe(dataType, callback, options = {}) {
    if (!this.subscribers.has(dataType)) {
      this.subscribers.set(dataType, new Set());
    }
    
    const subscription = {
      callback,
      options,
      id: Date.now() + Math.random()
    };
    
    this.subscribers.get(dataType).add(subscription);
    
    return () => {
      this.subscribers.get(dataType)?.delete(subscription);
    };
  }

  async triggerUpdate(dataType, data = null, options = {}) {
    const now = Date.now();
    const lastUpdate = this.lastUpdateTime.get(dataType) || 0;
    
    if (now - lastUpdate < this.debounceTime && !options.force) {
      this.updateQueue.add({ dataType, data, options, timestamp: now });
      this.scheduleQueueProcessing();
      return;
    }
    
    this.lastUpdateTime.set(dataType, now);
    
    const subscribers = this.subscribers.get(dataType);
    if (subscribers) {
      for (const subscription of subscribers) {
        try {
          await subscription.callback(data, options);
        } catch (error) {
          console.error(`Error in ${dataType} subscriber:`, error);
        }
      }
    }
    
    this.broadcastUpdate(dataType, data, options);
  }

  scheduleQueueProcessing() {
    if (this.isProcessingQueue) return;
    
    setTimeout(() => {
      this.processUpdateQueue();
    }, this.debounceTime);
  }

  async processUpdateQueue() {
    if (this.isProcessingQueue || this.updateQueue.size === 0) return;
    
    this.isProcessingQueue = true;
    
    const latestUpdates = new Map();
    
    for (const update of this.updateQueue) {
      const existing = latestUpdates.get(update.dataType);
      if (!existing || update.timestamp > existing.timestamp) {
        latestUpdates.set(update.dataType, update);
      }
    }
    
    this.updateQueue.clear();
    
    for (const update of latestUpdates.values()) {
      await this.triggerUpdate(update.dataType, update.data, { ...update.options, force: true });
    }
    
    this.isProcessingQueue = false;
  }

  broadcastUpdate(dataType, data, options) {
    if (typeof window === 'undefined') return;
    
    const updateEvent = {
      type: 'REAL_TIME_UPDATE',
      dataType,
      timestamp: Date.now(),
      data: data ? JSON.stringify(data) : null,
      options
    };
    
    localStorage.setItem('lastUpdate', JSON.stringify(updateEvent));
    
    window.dispatchEvent(new CustomEvent('realTimeUpdate', {
      detail: updateEvent
    }));
  }

  handleStorageChange(event) {
    if (event.key === 'lastUpdate' && event.newValue) {
      try {
        const updateEvent = JSON.parse(event.newValue);
        if (updateEvent.type === 'REAL_TIME_UPDATE') {
          const data = updateEvent.data ? JSON.parse(updateEvent.data) : null;
          this.triggerUpdate(updateEvent.dataType, data, { 
            ...updateEvent.options, 
            crossTab: true 
          });
        }
      } catch (error) {
        console.error('Error handling storage change:', error);
      }
    }
  }

  handleWindowFocus() {
    for (const [dataType, lastUpdate] of this.lastUpdateTime.entries()) {
      const timeSinceUpdate = Date.now() - lastUpdate;
      if (timeSinceUpdate > 60000) {
        this.triggerUpdate(dataType, null, { 
          force: true, 
          reason: 'window_focus' 
        });
      }
    }
  }

  invalidateCache(dataTypes) {
    const types = Array.isArray(dataTypes) ? dataTypes : [dataTypes];
    
    for (const dataType of types) {
      this.triggerUpdate(dataType, null, { 
        force: true, 
        invalidateCache: true 
      });
    }
  }

  getLastUpdateTime(dataType) {
    return this.lastUpdateTime.get(dataType);
  }

  isDataStale(dataType, maxAge = 300000) {
    const lastUpdate = this.lastUpdateTime.get(dataType);
    if (!lastUpdate) return true;
    
    return Date.now() - lastUpdate > maxAge;
  }

  destroy() {
    this.subscribers.clear();
    this.updateQueue.clear();
    this.lastUpdateTime.clear();
    
    if (typeof window !== 'undefined') {
      window.removeEventListener('storage', this.handleStorageChange.bind(this));
      window.removeEventListener('focus', this.handleWindowFocus.bind(this));
    }
  }
}

const realTimeUpdatesService = new RealTimeUpdatesService();

export { realTimeUpdatesService };
export default realTimeUpdatesService; 