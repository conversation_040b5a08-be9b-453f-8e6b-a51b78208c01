module.exports = {
  apps: [
    {
      name: 'deyncare',
      script: 'npm',
      args: 'start',
      cwd: '/var/www/deyncare.cajiibcreative.com',
      instances: 2, // Use 2 instances for better stability (adjust based on your VPS specs)
      exec_mode: 'cluster', // Enable cluster mode for better performance
      
      // Environment configuration
      env: {
        NODE_ENV: 'development',
        PORT: 3000,
      },
      env_production: {
        NODE_ENV: 'production',
        PORT: 3000,
      },
      
      // Process management
      autorestart: true,
      watch: false, // Disable in production
      max_memory_restart: '1G',
      
      // Logging configuration
      log_file: '/var/log/deyncare/combined.log',
      out_file: '/var/log/deyncare/out.log',
      error_file: '/var/log/deyncare/error.log',
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      merge_logs: true,
      
      // Advanced PM2 features
      min_uptime: '10s',
      max_restarts: 10,
      restart_delay: 4000,
      
      // Health monitoring
      kill_timeout: 5000,
      listen_timeout: 3000,
      
      // Source control (optional - for PM2 Plus monitoring)
      source_map_support: true,
      
      // Process behavior
      ignore_watch: [
        'node_modules',
        '.next',
        'logs',
        '.git'
      ],
      
      // Environment variables from file
      env_file: '.env.production',
      
      // Graceful shutdown
      kill_retry_time: 100,
      
      // Deployment configuration (if using PM2 deploy feature)
      // Uncomment and configure if you want to use PM2's deployment features
      /*
      deploy: {
        production: {
          user: 'ubuntu',
          host: 'your-vps-ip',
          ref: 'origin/main',
          repo: 'https://github.com/yourusername/deyncare-frontend.git',
          path: '/var/www/deyncare.cajiibcreative.com',
          'pre-deploy-local': '',
          'post-deploy': 'npm install && npm run build && pm2 reload ecosystem.config.js --env production',
          'pre-setup': '',
          env: {
            NODE_ENV: 'production'
          }
        }
      }
      */
    }
  ],

  // Optional: Deploy configuration for multiple environments
  deploy: {
    production: {
      user: 'ubuntu', 
      host: ['deyncare.cajiibcreative.com'], // Your actual frontend domain
      ref: 'origin/main',
      repo: 'https://github.com/yourusername/deyncare-frontend.git', // Update with actual repo URL
      path: '/var/www/deyncare.cajiibcreative.com',
      'post-deploy': 'npm ci && npm run build && pm2 reload ecosystem.config.js --env production && pm2 save',
      env: {
        NODE_ENV: 'production',
        PORT: 3000
      }
    },
    staging: {
      user: 'ubuntu',
      host: 'staging-server-ip',
      ref: 'origin/develop',
      repo: 'https://github.com/yourusername/deyncare-frontend.git',
      path: '/var/www/deyncare.cajiibcreative.com-staging',
      'post-deploy': 'npm ci && npm run build && pm2 reload ecosystem.config.js --env staging && pm2 save',
      env: {
        NODE_ENV: 'staging',
        PORT: 3001
      }
    }
  }
}; 