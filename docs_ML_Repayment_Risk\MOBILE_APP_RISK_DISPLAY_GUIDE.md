# Mobile App Risk Level Display Guide

## 🎯 **System Logic Overview**

### **Complete Debt & Risk Evaluation Flow**

```mermaid
graph TD
    A[Customer Takes Loan] --> B[Debt Created]
    B --> C[Status: Active Debt]
    C --> D{Payment Before Due Date?}
    
    D -->|Yes| E[Record Payment]
    E --> F[Status: Active Debt]
    F --> G[ML Evaluation: SKIPPED]
    
    D -->|No| H[Due Date Passes]
    H --> I[Cron Job Triggers]
    I --> J[ML Risk Evaluation]
    J --> K[Risk Level Updated]
    
    E --> L{Significant Payment ≥50%?}
    L -->|Yes| M[ML Evaluation Triggered]
    L -->|No| N[Stay Active Debt]
    
    M --> O[Risk Level: Low/Medium/High]
    K --> O
```

## 📱 **Mobile App Risk Display**

### **1. Risk Level Categories**

| Risk Level | Status | Color | Emoji | Description |
|------------|--------|-------|-------|-------------|
| **Low Risk** | Excellent | 🟢 Green | ✅ | Great payment history |
| **Medium Risk** | Good | 🟡 Yellow | ⚠️ | Generally reliable |
| **High Risk** | Needs Attention | 🔴 Red | 🚨 | Payment delays detected |
| **Active Debt** | Monitoring | ⚪ Gray | 📊 | New debt, evaluation pending |

### **2. Enhanced Mobile App API Endpoint**

**✅ Using Existing Endpoint (No Duplication):**
```javascript
GET /api/customers/:customerId?mobile=true
```

**Alternative Mobile Detection:**
- Add `?mobile=true` query parameter
- Include `User-Agent: Flutter` header  
- Include `X-Platform: mobile` header

**Enhanced Response Format:**
```json
{
  "success": true,
  "data": {
    "customer": {
      "customerId": "CUST_001",
      "customerName": "John Doe",
      "customerType": "Returning",
      "phone": "+1234567890"
    },
    "riskProfile": {
      "currentRiskLevel": "Low Risk",
      "riskScore": 25,
      "lastAssessment": "2024-01-15T10:30:00Z"
    },
    
    // 📱 MOBILE-SPECIFIC ENHANCEMENTS
    "riskInterpretation": {
      "status": "Excellent",
      "emoji": "✅",
      "color": "#10B981",
      "backgroundColor": "#ECFDF5",
      "message": "Great payment history! Keep it up.",
      "description": "This customer has shown excellent payment behavior...",
      "actionable": "Consider offering credit limit increases...",
      "trustLevel": "High Trust",
      "recommendation": "Approve new debts up to higher limits"
    },
    
    "uiHints": {
      "showRiskBadge": true,
      "allowNewDebt": true,
      "requireApproval": false,
      "suggestedCreditLimit": "Increase",
      "notificationPriority": "normal",
      "enableAutoApproval": true,
      "maxRecommendedDebt": 1500
    },
    
    "quickSummary": {
      "riskLevel": "Low Risk",
      "riskScore": 25,
      "trustLevel": "High Trust",
      "recommendation": "Approve new debts up to higher limits",
      "totalOutstanding": 500,
      "paymentBehaviorScore": 90,
      "activeDebts": 1
    },
    
    "mobileOptimized": true,
    
    // Standard fields (existing)
    "statistics": { /* existing stats */ },
    "debtHistory": { /* existing debt history */ },
    "paymentHistory": { /* existing payment history */ }
  }
}
```

## 🔄 **System Logic Breakdown**

### **Phase 1: Debt Creation**
```javascript
// When customer takes a loan
const debt = new Debt({
  customerId: "CUST_001",
  DebtAmount: 1000,
  DueDate: "2024-02-15",
  RiskLevel: "Active Debt", // ✅ Default status
  status: "active"
});

// Customer risk profile
customer.riskProfile.currentRiskLevel = "Active Debt";
customer.riskProfile.riskScore = 75;
customer.riskProfile.mlSource = "system";
```

**Mobile App Display:**
- 📊 **Status**: Monitoring
- 🔍 **Message**: "New debt created. Evaluation in progress."
- 🎯 **Action**: "Monitor payment behavior. No action needed yet."

### **Phase 2: Payment Before Due Date**
```javascript
// Customer pays before due date
if (paymentDate < dueDate) {
  debt.addPayment(amount, paymentDate);
  
  // ✅ NO ML EVALUATION TRIGGERED
  debt.RiskLevel = "Active Debt"; // Remains unchanged
  
  console.log("Payment recorded - ML evaluation pending until due date");
}
```

**Mobile App Display:**
- 📊 **Status**: Monitoring
- 💰 **Message**: "Payment recorded - evaluation pending"
- ⏰ **Next Evaluation**: "After due date"

### **Phase 3: Due Date Passes (Cron Job)**
```javascript
// Cron job runs every hour
const overdueDebts = await Debt.find({
  DueDate: { $lt: new Date() },
  RiskLevel: "Active Debt" // Not yet evaluated
});

for (const debt of overdueDebts) {
  // 🤖 ML EVALUATION TRIGGERED
  const evaluation = await mlService.evaluateRisk(debt, customer);
  
  debt.RiskLevel = evaluation.riskLevel; // "Low Risk", "Medium Risk", "High Risk"
  customer.riskProfile.currentRiskLevel = evaluation.riskLevel;
  customer.riskProfile.riskScore = evaluation.riskScore;
}
```

**Mobile App Display:**
- ✅ **Status**: Excellent (if Low Risk)
- 🎉 **Message**: "Risk evaluation completed!"
- 📈 **Score**: 25/100 (Low Risk)

### **Phase 4: Significant Payment (≥50%)**
```javascript
// Customer makes significant payment
if (debt.DebtPaidRatio >= 0.5) {
  // 🤖 ML EVALUATION TRIGGERED IMMEDIATELY
  const evaluation = await mlService.evaluateRisk(debt, customer);
  
  debt.RiskLevel = evaluation.riskLevel;
  console.log("Significant payment - ML evaluation triggered");
}
```

## 🎨 **Mobile App UI Components**

### **1. Risk Badge Component**
```dart
// Flutter example
Widget buildRiskBadge(RiskInterpretation riskInfo) {
  return Container(
    padding: EdgeInsets.symmetric(horizontal: 12, vertical: 6),
    decoration: BoxDecoration(
      color: Color(int.parse(riskInfo.color.substring(1), radix: 16) + 0xFF000000),
      borderRadius: BorderRadius.circular(20),
    ),
    child: Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(riskInfo.emoji, style: TextStyle(fontSize: 16)),
        SizedBox(width: 4),
        Text(riskInfo.status, style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold)),
      ],
    ),
  );
}
```

### **2. Enhanced Risk Score Progress Bar**
```dart
Widget buildRiskScoreBar(int riskScore, RiskInterpretation riskInfo) {
  return Container(
    padding: EdgeInsets.all(16),
    decoration: BoxDecoration(
      color: Color(int.parse(riskInfo.backgroundColor.substring(1), radix: 16) + 0xFF000000),
      borderRadius: BorderRadius.circular(12),
      border: Border.all(color: Color(int.parse(riskInfo.color.substring(1), radix: 16) + 0xFF000000), width: 1),
    ),
    child: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text('Risk Score', style: TextStyle(fontWeight: FontWeight.bold)),
            Text('$riskScore/100', style: TextStyle(fontWeight: FontWeight.bold)),
          ],
        ),
        SizedBox(height: 8),
        LinearProgressIndicator(
          value: riskScore / 100,
          backgroundColor: Colors.grey[300],
          valueColor: AlwaysStoppedAnimation<Color>(
            Color(int.parse(riskInfo.color.substring(1), radix: 16) + 0xFF000000)
          ),
        ),
        SizedBox(height: 8),
        Text(riskInfo.message, style: TextStyle(color: Colors.grey[700])),
      ],
    ),
  );
}
```

### **3. Complete Customer Risk Card**
```dart
Widget buildCustomerRiskCard(CustomerProfile profile) {
  final riskInfo = profile.riskInterpretation;
  final uiHints = profile.uiHints;
  
  return Card(
    elevation: 4,
    margin: EdgeInsets.all(16),
    child: Padding(
      padding: EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with name and risk badge
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      profile.customer.customerName,
                      style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                    ),
                    Text(
                      profile.customer.phone,
                      style: TextStyle(color: Colors.grey[600]),
                    ),
                  ],
                ),
              ),
              if (uiHints.showRiskBadge) buildRiskBadge(riskInfo),
            ],
          ),
          SizedBox(height: 16),
          
          // Risk Score Bar
          buildRiskScoreBar(profile.riskProfile.riskScore, riskInfo),
          SizedBox(height: 16),
          
          // Trust Level & Recommendation
          Container(
            padding: EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.grey[100],
              borderRadius: BorderRadius.circular(8),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(Icons.security, color: Colors.blue),
                    SizedBox(width: 8),
                    Text('Trust Level: ${riskInfo.trustLevel}', 
                         style: TextStyle(fontWeight: FontWeight.bold)),
                  ],
                ),
                SizedBox(height: 8),
                Text(riskInfo.description),
              ],
            ),
          ),
          
          // Action Recommendations
          if (riskInfo.actionable.isNotEmpty)
            Container(
              margin: EdgeInsets.only(top: 16),
              padding: EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.blue[50],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.blue[200]!),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(Icons.lightbulb, color: Colors.blue),
                      SizedBox(width: 8),
                      Text('Recommendation', style: TextStyle(fontWeight: FontWeight.bold, color: Colors.blue[800])),
                    ],
                  ),
                  SizedBox(height: 8),
                  Text(riskInfo.recommendation, style: TextStyle(color: Colors.blue[800])),
                ],
              ),
            ),
          
          // Quick Actions
          SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: uiHints.allowNewDebt ? () => _createNewDebt() : null,
                  icon: Icon(Icons.add),
                  label: Text('New Debt'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: uiHints.allowNewDebt ? Colors.green : Colors.grey,
                  ),
                ),
              ),
              SizedBox(width: 12),
              Expanded(
                child: OutlinedButton.icon(
                  onPressed: () => _viewPaymentHistory(),
                  icon: Icon(Icons.history),
                  label: Text('Payment History'),
                ),
              ),
            ],
          ),
        ],
      ),
    ),
  );
}
```

## 📊 **Business Rules Summary**

### **When ML Evaluation is Triggered:**
1. ✅ **Due date passes** (Cron job - every hour)
2. ✅ **Significant payment ≥50%** (Immediate)
3. ✅ **Full payment** (Immediate)
4. ❌ **Payment before due date <50%** (No ML)

### **Risk Level Mapping:**
- **Risk Score 0-30**: Low Risk ✅
- **Risk Score 31-60**: Medium Risk ⚠️
- **Risk Score 61-100**: High Risk 🚨
- **New Debt**: Active Debt 📊

### **Mobile App Actions Based on UI Hints:**
- **Low Risk**: `allowNewDebt: true`, `enableAutoApproval: true`
- **Medium Risk**: `allowNewDebt: true`, `requireApproval: false`
- **High Risk**: `allowNewDebt: false`, `requireApproval: true`
- **Active Debt**: `showRiskBadge: false`, monitor only

## 🔧 **Implementation Usage**

### **For Mobile App Requests:**
```javascript
// Add mobile detection in your Flutter HTTP client
final response = await http.get(
  Uri.parse('$baseUrl/api/customers/$customerId?mobile=true'),
  headers: {
    'Authorization': 'Bearer $token',
    'User-Agent': 'Flutter',
    'X-Platform': 'mobile',
  },
);

// Response will include riskInterpretation and uiHints
final customerData = json.decode(response.body);
if (customerData['data']['mobileOptimized'] == true) {
  // Use enhanced mobile UI
  final riskInfo = customerData['data']['riskInterpretation'];
  final uiHints = customerData['data']['uiHints'];
  // Build UI with mobile-friendly components
}
```

### **For Web Dashboard Requests:**
```javascript
// Regular web request (no mobile parameter)
GET /api/customers/:customerId

// Response includes standard fields only
// No riskInterpretation or uiHints
```

## 🎯 **Next Steps**

1. **✅ Enhanced existing endpoint** (no duplication)
2. **🔄 Test mobile detection** with Flutter app
3. **🔄 Implement UI components** using provided examples
4. **🔄 Add real-time notifications** for risk level changes
5. **🔄 Monitor ML API performance** and accuracy

---

**Enhanced API Endpoint**: `GET /api/customers/:customerId?mobile=true`
**ML API URL**: `https://deyncare-ml-88ga.onrender.com/`
**Mobile Detection**: Query param, User-Agent, or X-Platform header 