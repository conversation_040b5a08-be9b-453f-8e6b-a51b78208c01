/**
 * Event handlers for authentication system
 */
import { toast } from "sonner";
import { getCsrfToken, setCsrfToken } from "@/lib/api/token";

/**
 * Setup event handlers for authentication events
 * @param {Object} options - Configuration options
 * @param {Function} options.setUser - Function to update user state
 * @param {Function} options.setIsAuthenticated - Function to update authentication state
 * @param {Object} options.router - Next.js router
 * @returns {Function} - Cleanup function for event handlers
 */
export const setupAuthEventHandlers = ({ setUser, setIsAuthenticated, router }) => {
  if (typeof window === 'undefined') return () => {};
  
  // Make router navigation function available globally
  window.routerNavigate = (path) => {
    router.push(path);
  };
  
  // Make toast available globally for auth errors
  window.toast = toast;
  
  // Handle inactive account events
  const handleInactiveAccount = (event) => {
    console.log('[Auth] Inactive account event received');
    
    // Check if this is a settings-related error (should be ignored)
    if (event.detail?.isSettingsError || 
        (event.detail?.source === 'settings') || 
        (event.detail?.url && event.detail?.url.includes('/api/settings'))) {
      console.log('[Auth] Ignoring inactive account event from settings page');
      
      // Show a permission error message instead of account deactivation
      toast.error('You don\'t have permission to change these settings.');
      return;
    }
    
    // Only proceed with account deactivation for genuine account issues
    console.log('[Auth] Genuine inactive account detected');
    
    // Update auth state
    setUser(null);
    setIsAuthenticated(false);
    
    // Show message to user
    toast.error(event.detail?.message || 'Your account has been deactivated. Please contact an administrator.');
    
    // Redirect to login
    router.push('/login?status=inactive');
  };
  
  // Handle session expiring events
  const handleSessionExpiring = () => {
    console.log('[Auth] Session expiring soon');
    
    toast.warning('Your session will expire soon.', {
      description: 'You will be logged out in 1 minute.',
      action: {
        label: 'Stay logged in',
        onClick: () => {
          // Manually trigger token refresh
          console.log('[Auth] Manually refreshing token');
          window.dispatchEvent(new CustomEvent('auth:token:refresh:request'));
        }
      },
      duration: 10000
    });
  };
  
  // Clear navigation timestamp on page change
  const handleRouteChange = () => {
    console.log('[Auth] Route changed, clearing redirect prevention timestamp');
    sessionStorage.removeItem('lastAuthRedirect');
  };
  
  // Register event listeners
  window.addEventListener('auth:account:inactive', handleInactiveAccount);
  window.addEventListener('auth:session:expiring', handleSessionExpiring);
  
  // Create a cleanup function
  return () => {
    delete window.routerNavigate;
    delete window.toast;
    window.removeEventListener('auth:account:inactive', handleInactiveAccount);
    window.removeEventListener('auth:session:expiring', handleSessionExpiring);
  };
};

/**
 * Handle token refresh events and requests
 * @param {Object} options - Configuration options
 * @param {Function} options.checkAuth - Function to check authentication status
 * @returns {Object} - Event handlers and cleanup function
 */
export const setupTokenRefreshHandlers = ({ checkAuth }) => {
  if (typeof window === 'undefined') return { cleanup: () => {} };
  
  // Handle token refresh events
  const handleTokenRefresh = (e) => {
    console.log('[Auth] Token refreshed, updating auth state');
    
    // Check for CSRF token in cookies after token refresh
    // Only run in browser environment
    if (typeof window !== 'undefined') {
      setTimeout(() => {
        const csrfToken = getCsrfToken();
        if (!csrfToken) {
          console.log('[Auth] No CSRF token found after token refresh, checking cookies');
          // Check for CSRF cookie and store it as fallback
          try {
            const csrfCookie = document.cookie.match(new RegExp('(^| )XSRF-TOKEN=([^;]+)'));
            if (csrfCookie) {
              console.log('[Auth] CSRF token found in cookies after refresh');
              const token = decodeURIComponent(csrfCookie[2]);
              setCsrfToken(token);
            }
          } catch (error) {
            console.log('[Auth] Error accessing cookies:', error.message);
          }
        }
      }, 100);
    }
    
    // Run auth check when token is refreshed
    checkAuth();
  };
  
  // Handle manual token refresh requests
  const handleTokenRefreshRequest = async () => {
    console.log('[Auth] Manual token refresh requested');
    
    try {
      // Import dynamically to avoid React hook issues
      const { refreshToken: refreshTokenFn } = await import('@/lib/api/token');
      const newToken = await refreshTokenFn();
      
      if (newToken) {
        console.log('[Auth] Manual token refresh successful');
        toast.success('Session extended successfully');
      } else {
        console.error('[Auth] Manual token refresh failed - no token returned');
        toast.error('Unable to extend your session. Please log in again.');
      }
    } catch (error) {
      console.error('[Auth] Manual token refresh error:', error);
      
      // Check if the error is due to inactive account
      if (error.response?.status === 403 && 
          (error.response?.data?.type === 'inactive_account' || 
           error.response?.data?.message?.toLowerCase().includes('inactive'))) {
        
        console.log('[Auth] Token refresh failed due to inactive account');
        
        // Clear tokens and redirect to login
        localStorage.removeItem('accessToken');
        localStorage.removeItem('refreshToken');
        localStorage.setItem('accountStatus', 'inactive');
        
        // Dispatch inactive account event
        window.dispatchEvent(new CustomEvent('auth:account:inactive', {
          detail: { 
            message: 'Your account is inactive. Please contact an administrator.',
            source: 'token_refresh'
          }
        }));
        
        return;
      }
      
      // For other errors, show generic message
      toast.error('Unable to extend your session. Please log in again.');
    }
  };
  
  // Add event listeners
  window.addEventListener('auth:token:refreshed', handleTokenRefresh);
  window.addEventListener('auth:token:refresh:request', handleTokenRefreshRequest);
  
  // Return cleanup function
  const cleanup = () => {
    window.removeEventListener('auth:token:refreshed', handleTokenRefresh);
    window.removeEventListener('auth:token:refresh:request', handleTokenRefreshRequest);
  };
  
  return {
    handleTokenRefresh,
    handleTokenRefreshRequest,
    cleanup
  };
};

/**
 * Check for inactive account status on login page
 */
export const checkInactiveAccountStatus = () => {
  if (typeof window !== 'undefined' && 
      window.location.pathname === '/login') {
    // Check for inactive account status
    const accountStatus = localStorage.getItem('accountStatus');
    const statusParam = new URLSearchParams(window.location.search).get('status');
    
    if (accountStatus === 'inactive' || statusParam === 'inactive') {
      toast.error('Your account has been deactivated. Please contact an administrator.');
      localStorage.removeItem('accountStatus');
    }
  }
};

/**
 * Trigger manual token refresh
 */
export const manualTokenRefresh = () => {
  if (typeof window !== 'undefined') {
    window.dispatchEvent(new CustomEvent('auth:token:refresh:request'));
  }
};
