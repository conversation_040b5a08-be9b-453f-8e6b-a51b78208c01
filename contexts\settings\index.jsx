"use client";

/**
 * Settings Context - Main Exports
 * Centralizes all exports for the settings context
 */

import React, { createContext } from 'react';
import { useSettings } from './hooks';

// Create context with initial null value
export const SettingsContext = createContext(null);

// Import the provider AFTER creating the context to avoid circular dependencies
import { SettingsProvider as ContextProvider } from './provider';

// Re-export the provider
export const SettingsProvider = ContextProvider;

// Re-export the hook
export { useSettings };
