/**
 * Delete Plan Service
 * 
 * Deletes an existing pricing plan (SuperAdmin only)
 */

import apiBridge from '@/lib/api/bridge';
import { ENDPOINTS } from '@/lib/api/contract';
import { handleError, processApiResponse, logApiCall } from '../baseService';

/**
 * Delete an existing plan (SuperAdmin only)
 * @param {string} planId - Plan ID to delete
 * @returns {Promise<Object>} API response confirming deletion
 */
async function deletePlan(planId) {
  try {
    if (!planId) {
      throw new Error('Plan ID is required');
    }

    logApiCall('PlanService.deletePlan', ENDPOINTS.PLANS.DETAIL(planId));

    const response = await apiBridge.delete(ENDPOINTS.PLANS.DETAIL(planId), {
      skipCache: true
    });

    const result = processApiResponse(response, 'Plan deleted successfully');

    // Clear related caches
    if (typeof window !== 'undefined') {
      localStorage.removeItem('cache_plans-list');
      localStorage.removeItem(`cache_plan-${planId}`);
      localStorage.removeItem('cache_plan-stats');
    }

    return result;
  } catch (error) {
    handleError(error, 'PlanService.deletePlan', true);
    throw error;
  }
}

export default deletePlan;