# DeynCare API Documentation: SuperAdmin Subscriptions Module

## Overview
This documentation covers the API endpoints for managing subscriptions from the SuperAdmin perspective. These endpoints provide comprehensive subscription management capabilities including viewing, filtering, searching, and managing subscriptions.

## Base Path
`/api/subscriptions`

## Authentication
All endpoints require SuperAdmin authentication.

## Endpoints

### 1. Get All Subscriptions
- **GET** `/api/subscriptions`
- **Description:** Retrieve all subscriptions with filtering, sorting, and pagination
- **Query Parameters:**
  ```json
  {
    "status": "active | canceled | expired | trial",
    "planType": "trial | monthly | yearly",
    "shopId": "string",
    "page": "number (default: 1)",
    "limit": "number (default: 10, max: 100)",
    "sortBy": "createdAt | endDate | startDate (default: createdAt)",
    "sortOrder": "asc | desc (default: desc)"
  }
  ```
- **Response:**
  ```json
  {
    "success": true,
    "data": {
      "subscriptions": [{
        "subscriptionId": "string",
        "shopId": "string",
        "plan": {
          "name": "string",
          "type": "trial | monthly | yearly",
          "features": {
            "debtTracking": "boolean",
            "customerPayments": "boolean",
            "smsReminders": "boolean",
            "smartRiskScore": "boolean",
            "productSalesSystem": "boolean",
            "businessDashboard": "boolean",
            "exportReports": "boolean",
            "customerProfiles": "boolean",
            "posAndReceipt": "boolean",
            "fullTrialAccess": "boolean",
            "offlineSupport": "boolean"
          },
          "limits": {
            "maxProducts": "number",
            "maxEmployees": "number",
            "maxStorageMB": "number",
            "maxCustomers": "number",
            "maxDailyTransactions": "number"
          }
        },
        "pricing": {
          "basePrice": "number",
          "currency": "string",
          "billingCycle": "monthly | yearly",
          "discount": {
            "active": "boolean",
            "code": "string",
            "amount": "number",
            "type": "fixed | percentage",
            "value": "number"
          }
        },
        "status": "trial | active | past_due | canceled | expired",
        "payment": {
          "method": "offline | evc_plus | free",
          "verified": "boolean",
          "lastPaymentDate": "date",
          "nextPaymentDate": "date",
          "failedPayments": "number"
        },
        "dates": {
          "startDate": "date",
          "endDate": "date",
          "trialEndsAt": "date",
          "canceledAt": "date",
          "lastUpdated": "date"
        },
        "renewalSettings": {
          "autoRenew": "boolean",
          "reminderSent": "boolean",
          "renewalAttempts": "number"
        },
        "displayStatus": "string",
        "totalPrice": "number",
        "durationDays": "number",
        "daysRemaining": "number",
        "percentageUsed": "number"
      }],
      "pagination": {
        "total": "number",
        "page": "number",
        "limit": "number",
        "pages": "number"
      }
    }
  }
  ```

### 2. Get Subscription Statistics
- **GET** `/api/subscriptions/stats`
- **Description:** Get comprehensive subscription statistics
- **Response:**
  ```json
  {
    "success": true,
    "summary": {
      "totalSubscriptions": "number",
      "activeSubscriptions": "number",
      "trialSubscriptions": "number",
      "expiringSubscriptions": "number",
      "averageLifetime": "number",
      "totalRevenue": "number",
      "revenueByPlanType": {
        "monthly": "number",
        "yearly": "number"
      }
    }
  }
  ```

### 3. Bulk Update Subscriptions
- **POST** `/api/subscriptions/bulk`
- **Description:** Perform bulk operations on multiple subscriptions
- **Payload:**
  ```json
  {
    "operation": "extend | cancel | change-plan",
    "subscriptionIds": ["string"],
    "data": {
      // Operation-specific data
      "days": "number", // for extend
      "reason": "string", // for extend/cancel
      "planId": "string", // for change-plan
      "planType": "monthly | yearly" // for change-plan
    }
  }
  ```

### 4. Payment Retry Management

#### 4.1 Get Payment Retry Status
- **GET** `/api/subscriptions/payment-retry/:subscriptionId/status`
- **Description:** Get payment retry status for a subscription

#### 4.2 Trigger Manual Payment Retry
- **POST** `/api/subscriptions/payment-retry/:subscriptionId/trigger`
- **Description:** Manually trigger payment retry for a subscription

#### 4.3 Cancel Scheduled Retries
- **POST** `/api/subscriptions/payment-retry/:subscriptionId/cancel`
- **Description:** Cancel scheduled payment retries for a subscription

#### 4.4 Process All Pending Retries
- **POST** `/api/subscriptions/payment-retry/process-all`
- **Description:** Process all pending payment retries

#### 4.5 Get Retry Configuration
- **GET** `/api/subscriptions/payment-retry/config`
- **Description:** Get current payment retry configuration

### 5. Subscription Management

#### 5.1 Extend Subscription
- **POST** `/api/subscriptions/:subscriptionId/extend`
- **Description:** Extend a subscription's duration
- **Payload:**
  ```json
  {
    "days": "number (required)",
    "reason": "string (max 500)"
  }
  ```

#### 5.2 Verify Offline Payment
- **POST** `/api/subscriptions/verify-payment/:paymentId`
- **Description:** Verify an offline payment submission
- **Payload:**
  ```json
  {
    "status": "approved | rejected",
    "adminNote": "string (max 500)"
  }
  ```

### 6. Export Functionality

#### 6.1 Export Subscriptions to CSV
- **GET** `/api/export/subscriptions/csv`
- **Description:** Export subscriptions to CSV format
- **Query Parameters:**
  ```json
  {
    "format": "csv | excel (default: csv)",
    "filename": "string (max 100 chars)",
    "includeInactive": "boolean (default: false)",
    "status": "active | expired | cancelled | pending",
    "planType": "trial | monthly | yearly",
    "dateRange": {
      "startDate": "date (ISO format)",
      "endDate": "date (ISO format)"
    },
    "paymentMethod": "offline | evc_plus | free",
    "autoRenew": "boolean",
    "sortBy": "startDate | endDate | createdAt | updatedAt | lastPayment (default: createdAt)",
    "sortOrder": "asc | desc (default: desc)"
  }
  ```
- **Response:** CSV file download

#### 6.2 Export Subscriptions to Excel
- **GET** `/api/export/subscriptions/excel`
- **Description:** Export subscriptions to Excel format
- **Query Parameters:** Same as CSV export
- **Response:** Excel file download

#### 6.3 Export Plans to CSV
- **GET** `/api/export/plans/csv`
- **Description:** Export plans to CSV format
- **Query Parameters:**
  ```json
  {
    "format": "csv | excel (default: csv)",
    "filename": "string (max 100 chars)",
    "includeInactive": "boolean (default: false)",
    "planType": "trial | monthly | yearly",
    "minPrice": "number",
    "maxPrice": "number",
    "sortBy": "name | price | createdAt | updatedAt (default: createdAt)",
    "sortOrder": "asc | desc (default: desc)"
  }
  ```
- **Response:** CSV file download

#### 6.4 Export Plans to Excel
- **GET** `/api/export/plans/excel`
- **Description:** Export plans to Excel format
- **Query Parameters:** Same as CSV export
- **Response:** Excel file download

## Notes
- All endpoints require SuperAdmin authentication
- Pagination is implemented for list endpoints with a maximum of 100 items per page
- Dates are returned in ISO 8601 format
- All monetary values are in the specified currency (default: USD)
- The system supports multiple payment methods: offline, EVC Plus, and free (for trials)
- Subscription statuses include: trial, active, past_due, canceled, expired
- The API includes comprehensive error handling and validation

## Export File Contents

### Subscription Export Fields
- Subscription ID
- Shop ID
- Plan Name
- Plan Type
- Status
- Start Date
- End Date
- Trial End Date
- Payment Method
- Last Payment Date
- Next Payment Date
- Auto Renewal Status
- Total Price
- Currency
- Days Remaining
- Created At
- Updated At

### Plan Export Fields
- Plan ID
- Plan Name
- Plan Type
- Base Price
- Currency
- Billing Cycle
- Features
- Limits
- Status
- Created At
- Updated At

## Error Responses
All endpoints may return the following error responses:

```json
{
  "success": false,
  "error": {
    "code": "string",
    "message": "string",
    "details": {} // Optional additional error details
  }
}
```

Common error codes:
- `UNAUTHORIZED`: Authentication required
- `FORBIDDEN`: Insufficient permissions
- `NOT_FOUND`: Resource not found
- `VALIDATION_ERROR`: Invalid input data
- `PAYMENT_ERROR`: Payment processing error
- `SUBSCRIPTION_ERROR`: Subscription management error

---

*Generated by Cascade AI — DeynCare Backend API Documentation (SuperAdmin Subscriptions)* 