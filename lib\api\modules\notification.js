/**
 * Notification API Module
 * 
 * Handles all notification-related API endpoints including:
 * - Push notifications (SuperAdmin only)
 * - FCM token management
 * - Notification statistics and testing
 * 
 * Integrates with the existing notification system backend:
 * - Push Notification Controller (/api/admin/notifications/push/*)
 * - FCM Token Controller (/api/fcm/*)
 * - Notification Service (Email, SMS, Push, InApp, WhatsApp)
 */

import api from '../index';

const NotificationAPI = {
  
  // ===== PUSH NOTIFICATIONS (SuperAdmin Only) =====
  
  /**
   * Send push notification to specific shops
   * @param {Object} notificationData - Notification data
   * @param {string[]} notificationData.shopIds - Array of shop IDs
   * @param {string} notificationData.title - Notification title (1-100 chars)
   * @param {string} notificationData.message - Notification message (1-500 chars)
   * @param {string} [notificationData.priority='normal'] - Priority: low, normal, high
   * @param {string} [notificationData.actionUrl] - Action URL
   * @param {string} [notificationData.actionLabel] - Action button label
   * @param {string} [notificationData.scheduledAt] - ISO 8601 date for scheduling
   * @returns {Promise<Object>} API response with send results
   */
  sendToShops: (notificationData) => 
    api.post('/api/admin/notifications/push/shops', notificationData),

  /**
   * Send broadcast push notification to all admins
   * @param {Object} notificationData - Notification data
   * @param {string} notificationData.title - Notification title (1-100 chars)
   * @param {string} notificationData.message - Notification message (1-500 chars)
   * @param {string} [notificationData.priority='normal'] - Priority: low, normal, high
   * @param {string} [notificationData.actionUrl] - Action URL
   * @param {string} [notificationData.actionLabel] - Action button label
   * @param {string} [notificationData.scheduledAt] - ISO 8601 date for scheduling
   * @returns {Promise<Object>} API response with broadcast result
   */
  sendBroadcast: (notificationData) => 
    api.post('/api/admin/notifications/push/broadcast', notificationData),

  /**
   * Send debt reminder notifications (Backend-matched)
   * @param {Object} reminderData - Reminder configuration
   * @param {string[]} [reminderData.shopIds] - Specific shop IDs (optional)
   * @param {string} [reminderData.reminderType='overdue'] - Type: 7_days, 3_days, overdue (backend values)
   * @param {number} [reminderData.daysOverdue] - Days overdue filter
   * @param {string} [reminderData.priority='normal'] - Priority: low, normal, high
   * @returns {Promise<Object>} API response with reminder results
   */
  sendDebtReminders: (reminderData = {}) => 
    api.post('/api/admin/notifications/push/debt-reminders', reminderData),

  /**
   * Get push notification statistics
   * @param {Object} options - Query options
   * @param {string} [options.shopId] - Filter by specific shop
   * @param {number} [options.days=30] - Number of days to include (1-365)
   * @returns {Promise<Object>} API response with notification statistics
   */
  getNotificationStats: (options = {}) => 
    api.get('/api/admin/notifications/push/stats', { params: options }),

  /**
   * Test Firebase connection
   * @returns {Promise<Object>} API response with connection test result
   */
  testFirebaseConnection: () => 
    api.get('/api/admin/notifications/push/test'),

  /**
   * Get notification targets (shops and users)
   * @returns {Promise<Object>} API response with available targets
   */
  getNotificationTargets: () => 
    api.get('/api/admin/notifications/push/targets'),

  // ===== FCM TOKEN MANAGEMENT (Backend-matched only) =====

  /**
   * Register FCM token for current user
   * @param {Object} tokenData - Token registration data
   * @param {string} tokenData.token - FCM token string
   * @param {Object} [tokenData.deviceInfo] - Device information
   * @param {string} [tokenData.deviceInfo.deviceId] - Unique device identifier
   * @param {string} [tokenData.deviceInfo.platform] - Platform: android, ios (backend only accepts these)
   * @param {string} [tokenData.deviceInfo.appVersion] - App version
   * @param {string} [tokenData.deviceInfo.osVersion] - OS version
   * @returns {Promise<Object>} API response with registration result
   */
  registerFCMToken: (tokenData) => 
    api.post('/api/fcm/register', tokenData),

  /**
   * Send test notification to current user
   * Backend sends a predefined test message to all user's active FCM tokens
   * @returns {Promise<Object>} API response with test result
   */
  sendTestNotification: () => 
    api.post('/api/fcm/test'),

  // Note: The following FCM endpoints are NOT available in backend:
  // - unregisterFCMToken (no DELETE /api/fcm/unregister route)
  // - updateFCMTokenUsage (no PUT /api/fcm/usage route)  
  // - getUserFCMTokens (no GET /api/fcm/tokens route)
  // - cleanupExpiredTokens (no DELETE /api/fcm/cleanup route)

  // ===== UTILITY METHODS =====

  /**
   * Get notification system status
   * Combines Firebase test and basic stats
   * @returns {Promise<Object>} Combined system status
   */
  async getSystemStatus() {
    try {
      const [firebaseTest, stats] = await Promise.allSettled([
        this.testFirebaseConnection(),
        this.getNotificationStats({ days: 1 })
      ]);

      return {
        success: true,
        data: {
          firebase: {
            connected: firebaseTest.status === 'fulfilled' && firebaseTest.value?.data?.success,
            error: firebaseTest.status === 'rejected' ? firebaseTest.reason?.message : null
          },
          todayStats: stats.status === 'fulfilled' ? stats.value?.data : null,
          timestamp: new Date().toISOString()
        }
      };
    } catch (error) {
      console.error('[NotificationAPI.getSystemStatus] Error:', error);
      throw error;
    }
  },

  /**
   * Validate notification data before sending (Backend-matched)
   * @param {Object} data - Notification data to validate
   * @param {string} type - Type of notification: 'shops', 'broadcast', 'debt_reminders'
   * @returns {Object} Validation result
   */
  validateNotificationData(data, type) {
    const errors = [];

    try {
      switch (type) {
        case 'shops':
          if (!data.shopIds || !Array.isArray(data.shopIds) || data.shopIds.length === 0) {
            errors.push('At least one shop ID is required');
          }
          break;
        case 'broadcast':
          // No additional validation needed beyond common checks
          break;
        case 'debt_reminders':
          if (data.reminderType && !['7_days', '3_days', 'overdue'].includes(data.reminderType)) {
            errors.push('Reminder type must be 7_days, 3_days, or overdue');
          }
          break;
      }

      // Common validations (for shops and broadcast, debt_reminders uses different logic)
      if (type !== 'debt_reminders') {
        if (!data.title || data.title.length === 0 || data.title.length > 100) {
          errors.push('Title must be between 1 and 100 characters');
        }
        
        if (!data.message || data.message.length === 0 || data.message.length > 500) {
          errors.push('Message must be between 1 and 500 characters');
        }
      }

      if (data.priority && !['low', 'normal', 'high'].includes(data.priority)) {
        errors.push('Priority must be low, normal, or high');
      }

      return {
        isValid: errors.length === 0,
        errors
      };
    } catch (error) {
      return {
        isValid: false,
        errors: ['Validation error: ' + error.message]
      };
    }
  }
};

export default NotificationAPI; 