import apiBridge from '@/lib/api/bridge';
import { ENDPOINTS } from '@/lib/api/contract';
import { toast } from 'sonner';
import BaseService from '@/lib/services/baseService';

/**
 * Get user statistics for dashboard analytics
 * @param {Object} options - Statistics options
 * @param {string} options.startDate - Start date for statistics (ISO format: YYYY-MM-DD)
 * @param {string} options.endDate - End date for statistics (ISO format: YYYY-MM-DD)
 * @param {string} options.groupBy - Grouping option: 'day', 'week', 'month'
 * @returns {Promise<Object>} User statistics data
 */
async function getUserStats(options = {}) {
  try {
    // Prepare query parameters
    const params = {};
    
    if (options.startDate) {
      params.startDate = options.startDate;
    }
    
    if (options.endDate) {
      params.endDate = options.endDate;
    }
    
    if (options.groupBy) {
      params.groupBy = options.groupBy;
    }
    
    // Make API request using the bridge
    const response = await apiBridge.get(`${ENDPOINTS.USERS.BASE}/stats`, { params });
    
    // Process response
    if (response.data && response.data.success) {
      const statsData = response.data.data;
      
      // Return statistics data
      return statsData;
    }
    
    // Handle unexpected response
    console.error('Unexpected API response format:', response.data);
    toast.error('Failed to load user statistics: Unexpected response format');
    return null;
  } catch (error) {
    console.error('Error fetching user statistics:', error);
    // Use standardized error handling
    BaseService.handleError(error, 'UserStatsService.getUserStats', true);
    throw error;
  }
}

export default getUserStats; 