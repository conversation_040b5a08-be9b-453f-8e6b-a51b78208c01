"use client"

import { useState, useEffect } from "react"
import { Moon, Sun } from "lucide-react"

export function ThemeToggleFixed() {
  const [theme, setTheme] = useState(() => {
    // Default to light if we can't determine
    if (typeof window === 'undefined') return 'light'
    
    // Check localStorage first
    const savedTheme = localStorage.getItem('theme')
    if (savedTheme) return savedTheme
    
    // Check system preference
    if (window.matchMedia('(prefers-color-scheme: dark)').matches) {
      return 'dark'
    }
    
    return 'light'
  })
  
  // Apply theme class to document
  useEffect(() => {
    const root = window.document.documentElement
    
    if (theme === 'dark') {
      root.classList.add('dark')
      localStorage.setItem('theme', 'dark')
    } else {
      root.classList.remove('dark')
      localStorage.setItem('theme', 'light')
    }
  }, [theme])
  
  const toggleTheme = () => {
    setTheme(prevTheme => prevTheme === 'dark' ? 'light' : 'dark')
  }
  
  return (
    <button
      onClick={toggleTheme}
      className="rounded-md p-2 border border-border/30 bg-background hover:bg-muted transition-all duration-200 transform hover:scale-105"
      aria-label={theme === 'dark' ? 'Switch to light theme' : 'Switch to dark theme'}
      type="button"
    >
      {theme === 'dark' ? (
        <Sun className="h-5 w-5" />
      ) : (
        <Moon className="h-5 w-5" />
      )}
    </button>
  )
}
