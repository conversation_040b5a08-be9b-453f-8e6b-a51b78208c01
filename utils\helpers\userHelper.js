const UserHelper = {
  _cache: new Map(),
  _cacheExpiry: new Map(),
  
  _getCachedUser: (key) => {
    if (!UserHelper._cache.has(key)) return null;
    
    // Check if cache has expired
    const expiry = UserHelper._cacheExpiry.get(key);
    if (expiry && expiry < Date.now()) {
      UserHelper._cache.delete(key);
      UserHelper._cacheExpiry.delete(key);
      return null;
    }
    
    return UserHelper._cache.get(key);
  },
  
  _cacheUser: (key, user) => {
    // Only cache serializable data
    const serializedUser = {
      userId: user.userId,
      email: user.email,
      role: user.role,
      status: user.status,
      // Add other serializable fields as needed
    };
    
    UserHelper._cache.set(key, serializedUser);
    UserHelper._cacheExpiry.set(key, Date.now() + (5 * 60 * 1000)); // 5 minutes
  },
  
  // ... rest of the helper methods
}; 