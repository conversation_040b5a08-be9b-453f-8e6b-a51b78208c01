/**
 * Subscription Stats Controller
 * Provides stats data about subscriptions for SuperAdmin dashboard
 * GET /api/subscriptions/stats
 */
const { SubscriptionService } = require('../../services');
const { logInfo, logError } = require('../../utils');

const getSubscriptionStats = async (req, res, next) => {
  try {
    // Only SuperAdmin can access analytics
    if (req.user.role !== 'superAdmin') {
      return res.status(403).json({
        success: false,
        message: 'Access denied. SuperAdmin privileges required.'
      });
    }
    
    logInfo('Fetching subscription stats for SuperAdmin dashboard', 'SubscriptionController');
    
    // Get time range from query params or use defaults
    // Use a much wider default range to capture all historical data
    const { 
      startDate = new Date('2020-01-01'), // Start from 2020 to capture all data
      endDate = new Date(),
      groupBy = 'day' // day, week, month
    } = req.query;
    
    const statsOptions = {
      startDate: new Date(startDate),
      endDate: new Date(endDate),
      groupBy
    };
    
    // Log the date parameters for debugging
    logInfo(`Stats date range: ${statsOptions.startDate.toISOString()} to ${statsOptions.endDate.toISOString()}`, 'SubscriptionController');
    
    // Get stats data
    const stats = await SubscriptionService.getSubscriptionStats(statsOptions);

    logInfo(`Stats result: ${JSON.stringify(stats)}`, 'SubscriptionController');

    return res.status(200).json({
      success: true,
      summary: {
        totalSubscriptions: stats.overview?.totalSubscriptions || 0,
        activeSubscriptions: stats.overview?.activeSubscriptions || 0,
        trialSubscriptions: stats.overview?.trialSubscriptions || 0,
        expiringSubscriptions: stats.overview?.expiringSubscriptions || 0,
        averageLifetime: stats.overview?.averageLifetime || 0,
        totalRevenue: stats.revenue?.total || 0,
        revenueByPlanType: stats.revenue?.byPlanType || {},
        // Add additional data for frontend components
        byStatus: stats.planDistribution?.reduce((acc, item) => {
          acc[item._id] = item.count;
          return acc;
        }, {}) || {},
        byPlan: stats.planDistribution?.reduce((acc, item) => {
          acc[item._id] = item.count;
          return acc;
        }, {}) || {},
        trends: {
          newSubscriptions: stats.trends?.length || 0,
          renewals: 0, // Calculate from trends if needed
          cancellations: 0 // Calculate from trends if needed
        }
      }
      // Add more fields if needed, e.g., data: stats.data
    });
  } catch (error) {
    logError('Failed to fetch subscription analytics', 'SubscriptionController', error);
    return next(error);
  }
};

module.exports = getSubscriptionStats;
