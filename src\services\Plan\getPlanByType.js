/**
 * Get plan by type
 * Retrieves a plan by its type (trial, monthly, yearly)
 */
const { Plan } = require('../../models');
const { AppError, logError } = require('../../utils');

/**
 * Get plan by type
 * @param {string} planType - Type of plan (trial, monthly, yearly)
 * @returns {Promise<Object>} Plan
 */
const getPlanByType = async (planType) => {
  try {
    const plan = await Plan.findPlanByType(planType);
    
    if (!plan) {
      throw new AppError(`Plan type '${planType}' not found`, 404, 'plan_not_found');
    }
    
    return plan;
  } catch (error) {
    logError(`Failed to get plan by type: ${planType}`, 'PlanService', error);
    throw error;
  }
};

module.exports = getPlanByType;
