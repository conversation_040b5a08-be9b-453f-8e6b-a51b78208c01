# DeynCare Environment Variables

## Admin Setup
```
ADMIN_EMAIL=<EMAIL>              # Default admin email address
ADMIN_PASSWORD=Hnajiib12345$$                     # Default admin password
SUPER_ADMIN_NAME=<PERSON><PERSON><PERSON>         # Name for the super admin account
SUPER_ADMIN_PHONE=252 *********                  # Phone number for super admin
```

## Server Configuration
```
NODE_ENV=development                # Application environment (development, staging, production)
PORT=5000                           # Server port number
API_VERSION=v1                      # API version identifier
USE_SIMPLIFIED_SERVER=false         # Toggle simplified server mode
```

## Database
```
MONGODB_URI=mongodb+srv://Karshe:<EMAIL>/Deyncare?retryWrites=true&w=majority  # MongoDB connection string
```

## Authentication
```
JWT_ACCESS_SECRET=your_super_secure_access_token_secret_key_change_in_production  # Secret for signing access tokens
JWT_REFRESH_SECRET=your_super_secure_refresh_token_secret_key_change_in_production # Secret for signing refresh tokens
JWT_ACCESS_EXPIRY=2h                                                             # Access token lifetime
JWT_REFRESH_EXPIRY=30d                                                            # Refresh token lifetime
```

## CORS & URLs
```
CORS_ORIGIN=https://deyncare.cajiibcreative.com              # Allowed origins for CORS
FRONTEND_URL=http://localhost:3000             # URL of the frontend application
```

## Email
```
EMAIL_HOST=smtp.gmail.com                      # SMTP server hostname
EMAIL_PORT=587                                 # SMTP server port
EMAIL_USER=<EMAIL>             # SMTP authentication username
EMAIL_PASS=fiwt spjc krbi hmqy                 # SMTP authentication password
EMAIL_FROM="DeynCare <<EMAIL>>" # From address for sent emails
```

## Security
```
PASSWORD_SALT_ROUNDS=10                         # Bcrypt salt rounds for password hashing
COOKIE_SECRET=GFhPtK8Xn3LbCz5qR9jMsY7vWdE2A6U4  # Secret for signing cookies
SESSION_SECRET=B7tVxZcF3qKpN9mR5dS8gH2jL6wE4yA1  # Secret for express-session
CSRF_SECRET=L9eR7tY5xW3vU1sP8dK6gF4hJ2mN0qB3     # Secret for CSRF token generation
TOKEN_SECRET=T6yR9uI2oP5aS8dF3gH7jK1lZ4xC0vB2    # General purpose token signing secret
```


FAST_STARTUP=true
LOG_LEVEL=2

# Disable heavy bootstrap operations during development
CREATE_SUPER_ADMIN=false
SKIP_BOOTSTRAP_HEAVY_OPS=true

## Rate Limiting
```
# Rate Limiting - Optimized for development and Flutter apps
RATE_LIMIT_WINDOW_MS=900000          # 15 minutes (keep same)
RATE_LIMIT_MAX_REQUESTS=500          # Increased from 100 to 500
LOGIN_RATE_LIMIT_MAX=20              # Increased from 5 to 20 (Flutter testing)
PASSWORD_RESET_RATE_LIMIT_MAX=10     # Increased from 3 to 10
VERIFICATION_RATE_LIMIT_MAX=15       # Increased from 5 to 15
AUTH_RATE_LIMIT_MAX=30               # Increased from 10 to 30
```

## Content Security Policy
```
CSP_CONNECT_SRC="'self' http://localhost:3000"  # Allowed sources for fetch, XHR, WebSocket
CSP_SCRIPT_SRC="'self' 'unsafe-inline'"         # Allowed sources for scripts
CSP_IMG_SRC="'self' data: blob:"                # Allowed sources for images
```

## Logging
```
LOG_LEVEL=debug                # Logging verbosity level
```

## Application Info
```
APP_NAME=DeynCare                      # Application name
APP_DESCRIPTION=Debt management and POS system  # Application description
```

## SuperAdmin Creation
```
CREATE_SUPER_ADMIN=true                  # Auto-create super admin on startup
SUPER_ADMIN_EMAIL=<EMAIL>   # Email for super admin account
SUPER_ADMIN_PASSWORD=Hnajiib12345$       # Password for super admin account
SUPER_ADMIN_NAME=Abdinaib Mohamed Karshe   # Name for super admin account
SUPER_ADMIN_PHONE=252 *********          # Phone for super admin account
```

## Subscription
```
DEFAULT_TRIAL_DAYS=14       # Default trial period length
DATA_RETENTION_DAYS=30      # How long to keep data after subscription ends
```

## Scheduler
```
ENABLE_SCHEDULER=false         # Use in-app scheduler or external CRON
```

## CRON Jobs
```
CRON_TRIAL_REMINDERS=0 8 * * *       # Schedule for trial reminder notifications (8:00 AM daily)
CRON_EXPIRY_REMINDERS=0 9 * * *      # Schedule for expiry reminder notifications (9:00 AM daily)
CRON_AUTO_RENEWALS=0 10 * * *        # Schedule for subscription auto-renewals (10:00 AM daily)
CRON_DEACTIVATE_EXPIRED=0 11 * * *   # Schedule for deactivating expired subscriptions (11:00 AM daily)
```

## Session
```
SESSION_MAX_AGE=********          # Session cookie lifetime (24 hours in milliseconds)
SESSION_SECURE=false              # Require HTTPS for session cookie (set to true in production)
```
#EVC CREDENTIAL
CREDENTIAL_ENCRYPTION_KEY=fb819e79e37c4f68a59f9acb42356b19$8cd379f5fbd04d8bbd2a4f831a712d34

# 🔐 SHOP_KEY_SALT
# This value is used to derive shop-specific encryption keys from the master key.
# It should be long, random, and kept secret. DO NOT change once in production.
SHOP_KEY_SALT=8fbd9a94c3d747e0af7b2f6d129e54fc56dc4af53c31877bfe7f0e56d830cbb2



# WaafiPay/EVC API Credentials (leave blank if using DB-stored encrypted credentials)
WAAFIPAY_MERCHANT_UID=
WAAFIPAY_API_USER_ID=
WAAFIPAY_API_KEY=
WAAFIPAY_MERCHANT_NO=
WAAFIPAY_API_URL=





# =================================
# FIREBASE CONFIGURATION FOR DEYNCARE
# =================================
# Copy this content to your .env file

# Firebase Project Configuration (From your Firebase Console)
FIREBASE_PROJECT_ID=deyncare-47d99
FIREBASE_WEB_API_KEY=AIzaSyCYP-Wb642o1uCI3BmjWU97jE_VWHope5k
FIREBASE_PROJECT_NUMBER=*************
FIREBASE_APP_ID=1:*************:web:1471e000c3152d2812f4b3

# Firebase Admin SDK Service Account
GOOGLE_APPLICATION_CREDENTIALS=C:/Users/<USER>/Desktop/deyncare-backend/deyncare-47d99-firebase-adminsdk-fbsvc-cea556463f.json

# =================================
# NOTIFICATION SYSTEM CONFIGURATION
# =================================

# Enable/Disable Features
PUSH_NOTIFICATIONS_ENABLED=true
DEBT_REMINDERS_ENABLED=true

# Notification Settings
NOTIFICATION_BATCH_SIZE=500
NOTIFICATION_RETRY_ATTEMPTS=3
NOTIFICATION_TIMEOUT_SECONDS=35

# =================================
# DEVELOPMENT/TESTING
# =================================

# Firebase Emulator (Development Only)
FIREBASE_EMULATOR_ENABLED=false
FIREBASE_EMULATOR_HOST=localhost
FIREBASE_EMULATOR_PORT=9099

