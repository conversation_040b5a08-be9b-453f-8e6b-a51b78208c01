# 🧪 Comprehensive Offline Payment Registration Testing Scenarios

## 📋 Overview
This document provides complete testing scenarios for the DeynCare offline payment registration system, including exact API endpoints, payloads, and expected responses based on the codebase analysis.

---

## 🔧 Prerequisites

### Authentication Setup
```bash
# 1. Register a new user first
curl -X POST http://localhost:5000/api/register/init \
  -H "Content-Type: application/json" \
  -d '{
    "fullName": "<PERSON>",
    "email": "<EMAIL>",
    "phone": "+************",
    "password": "SecurePass123!",
    "shopName": "Ahmed Electronics",
    "shopAddress": "Bakara Market, Mogadishu",
    "planType": "monthly"
  }'

# 2. Verify email (get verification code from email/logs)
curl -X POST http://localhost:5000/api/register/verify-email \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "verificationCode": "123456"
  }'

# 3. Login to get access token
curl -X POST http://localhost:5000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "SecurePass123!"
  }'

# Extract the access_token from response for use in subsequent requests
export ACCESS_TOKEN="your_jwt_token_here"
```

---

## 🎯 Test Scenario 

### **Scenario: Offline Payment with Payment Proof Upload**

#### Endpoint: `POST /api/register/pay`
```bash
# Create a test image file first
echo "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==" | base64 -d > test_receipt.png

curl -X POST http://localhost:5000/api/register/pay \
  -H "Authorization: Bearer $ACCESS_TOKEN" \
  -F "planType=monthly" \
  -F "paymentMethod=offline" \
  -F "payerName=Ahmed Hassan" \
  -F "payerPhone=+************" \
  -F "notes=Offline payment to DeynCare account" \
  -F "paymentProof=@test_receipt.png"
```

#### Expected Response:
```json
{
  "success": true,
  "message": "Payment skipped. Complete payment later.",
  "data": {
    "user": {
      "userId": "USR_123456",
      "status": "pending_payment",
      "isPaid": false
    },
    "shop": {
      "shopId": "SHP_789012",
      "status": "pending"
    },
    "subscription": {
      "subscriptionId": "SUB_345678",
      "status": "pending_payment",
      "payment": {
        "method": "offline",
        "verified": false,
        "paymentDetails": {
          "payerName": "Ahmed Hassan",
          "payerPhone": "+************",
          "notes": "Offline payment to DeynCare account",
          "paymentProof": {
            "filename": "SHP_789012_subscription_1703596801234.png",
            "originalName": "test_receipt.png",
            "size": 95
          },
          "submissionMethod": "enhanced_offline"
        }
      }
    },
    "nextStep": "registration_complete_offline_payment_pending"
  }
}
```

---

### **File Upload Validation Tests**

#### Test: Invalid File Type
```bash
echo "This is a text file" > test_file.txt
curl -X POST http://localhost:5000/api/register/pay \
  -H "Authorization: Bearer $ACCESS_TOKEN" \
  -F "planType=monthly" \
  -F "paymentMethod=offline" \
  -F "paymentProof=@test_file.txt"
```

#### Expected Error Response:
```json
{
  "success": false,
  "message": "Invalid file type. Only JPG, PNG, and PDF files are allowed.",
  "error": {
    "code": "invalid_file_type",
    "statusCode": 400
  }
}
```

#### Test: File Too Large (>5MB)
```bash
# Create a large file (>5MB)
dd if=/dev/zero of=large_file.jpg bs=1M count=6

curl -X POST http://localhost:5000/api/register/pay \
  -H "Authorization: Bearer $ACCESS_TOKEN" \
  -F "planType=monthly" \
  -F "paymentMethod=offline" \
  -F "paymentProof=@large_file.jpg"
```

#### Expected Error Response:
```json
{
  "success": false,
  "message": "File too large. Maximum file size is 5MB.",
  "error": {
    "code": "file_too_large",
    "statusCode": 400
  }
}
```

---

## 🚨 Error Scenarios

### **Error 1: Missing Authentication**
```bash
curl -X POST http://localhost:5000/api/register/pay \
  -H "Content-Type: application/json" \
  -d '{
    "planType": "monthly",
    "paymentMethod": "offline"
  }'
```

#### Expected Error:
```json
{
  "success": false,
  "message": "Access token is required",
  "error": {
    "code": "missing_token",
    "statusCode": 401
  }
}
```

### **Error 2: Invalid Payment Method**
```bash
curl -X POST http://localhost:5000/api/register/pay \
  -H "Authorization: Bearer $ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "planType": "monthly",
    "paymentMethod": "InvalidMethod"
  }'
```

#### Expected Error:
```json
{
  "success": false,
  "message": "Validation error",
  "error": {
    "code": "validation_error",
    "statusCode": 400,
    "details": [
      {
        "field": "paymentMethod",
        "message": "paymentMethod must be one of [EVC Plus, Card, Mobile Money, offline]"
      }
    ]
  }
}
```

---

## 📧 Email Notification Verification

After successful offline payment submission, verify that SuperAdmin receives email notification:

### Check Email Logs:
```bash
# Check application logs for email sending confirmation
tail -f logs/app.log | grep "Enhanced SuperAdmin notification sent"
```

### Expected Log Entry:
```
[INFO] Enhanced SuperAdmin notification sent for offline payment order: Ahmed Electronics (enhanced_offline)
```

---

## 🔍 Database Verification

### Check Payment Record:
```javascript
// MongoDB query to verify payment record
db.subscriptions.findOne({
  "payment.method": "offline",
  "payment.verified": false
})
```

### Check Shop Status:
```javascript
// MongoDB query to verify shop status
db.shops.findOne({
  "shopName": "Ahmed Electronics",
  "status": "pending"
})
```

---

## 📝 Notes

1. **File Upload Limits**: Maximum 5MB, supports JPG, PNG, PDF
2. **Authentication Required**: All payment endpoints require valid JWT token
3. **Email Notifications**: SuperAdmin receives notifications for offline payments
4. **Submission Methods**: System tracks "basic_offline" vs "enhanced_offline"
5. **Payment Verification**: All offline payments require SuperAdmin approval
6. **Status Tracking**: Users remain in "pending_payment" status until approval

---

## 🔗 Integration with SuperAdmin Approval

### **SuperAdmin Login for Testing**
```bash
# Login as SuperAdmin to test approval workflow
curl -X POST http://localhost:5000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "your_superadmin_password"
  }'

export SUPERADMIN_TOKEN="superadmin_jwt_token_here"
```

### **Approve Offline Payment**
```bash
# Approve the offline payment submission
curl -X POST http://localhost:5000/api/register/admin/approve-shop/SHP_789012 \
  -H "Authorization: Bearer $SUPERADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "approvalNotes": "Offline payment verified",
    "activateImmediately": true,
    "confirmOfflinePayment": true,
    "offlinePaymentDetails": {
      "receiptNumber": "TXN20241216001",
      "paymentDate": "2024-12-16T10:30:00Z",
      "amount": 50.00,
      "currency": "USD",
      "paymentMethod": "offline",
      "notes": "Payment verified via uploaded proof"
    }
  }'
```

### **Verify Account Activation**
```bash
# Login as the customer to verify account is now active
curl -X POST http://localhost:5000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "SecurePass123!"
  }'

# Expected: User should now have isPaid: true, isActivated: true
```

---

## 📊 File Upload Specifications

### **Supported File Types**:
- **JPG/JPEG**: Image files (receipts, screenshots)
- **PNG**: Image files (receipts, screenshots)
- **PDF**: Document files (bank statements, receipts)

### **File Size Limits**:
- **Maximum**: 5MB (5,242,880 bytes)
- **Minimum**: No minimum limit

### **File Storage Location**:
- **Directory**: `uploads/payment-proofs/`
- **Naming Convention**: `{shopId}_{context}_{timestamp}.{extension}`
- **Example**: `SHP_789012_subscription_1703596801234.png`

### **File Access**:
- **Endpoint**: `GET /api/files/{fileId}`
- **Authentication**: Required (JWT token)
- **Usage**: SuperAdmin can view uploaded payment proofs

---

## 🔍 Advanced Testing Scenarios

### **Additional Testing Scenarios**

#### **Different Plan Types**
```bash
# Test yearly plan offline payment
curl -X POST http://localhost:5000/api/register/pay \
  -H "Authorization: Bearer $ACCESS_TOKEN" \
  -F "planType=yearly" \
  -F "paymentMethod=offline" \
  -F "payerName=Ahmed Hassan" \
  -F "payerPhone=+************" \
  -F "notes=Yearly plan offline payment" \
  -F "paymentProof=@test_receipt.png"
```

#### **Unicode and Special Characters**
```bash
# Test with Arabic/Somali names and special characters
curl -X POST http://localhost:5000/api/register/pay \
  -H "Authorization: Bearer $ACCESS_TOKEN" \
  -F "planType=monthly" \
  -F "paymentMethod=offline" \
  -F "payerName=أحمد حسن" \
  -F "notes=Payment with special chars: $50.00 @DeynCare #receipt" \
  -F "paymentProof=@test_receipt.png"
```

---

## 🎯 Success Criteria

- ✅ User can submit offline payment without file upload
- ✅ User can submit offline payment with file upload
- ✅ System validates file types and sizes correctly
- ✅ SuperAdmin receives email notifications automatically
- ✅ Payment records are created with correct status
- ✅ Shop remains in pending status until approval
- ✅ Error handling works for all invalid inputs
- ✅ File upload and storage works properly
- ✅ SuperAdmin can approve payments successfully
- ✅ Account activation works after approval
- ✅ Email notifications are sent at each step
- ✅ Database records are updated consistently
