/**
 * App Upload Service
 * Handles app file uploads and management for DeynCare application
 */
const fs = require('fs');
const path = require('path');
const crypto = require('crypto');
const util = require('util');
const AppUpload = require('../models/appUpload.model');
const { AppError, logInfo, logError, logSuccess, logWarning, idGenerator } = require('../utils');

// Promisify fs operations
const mkdirAsync = util.promisify(fs.mkdir);
const existsAsync = util.promisify(fs.exists);
const unlinkAsync = util.promisify(fs.unlink);
const statAsync = util.promisify(fs.stat);

/**
 * AppUploadService provides methods for handling app file uploads
 */
const AppUploadService = {
  /**
   * Base directory for app uploads
   */
  baseUploadDir: path.join(process.cwd(), 'uploads', 'app-files'),
  
  /**
   * Initialize app upload directories
   * @returns {Promise<boolean>} Success status
   */
  init: async () => {
    try {
      // Create app-files upload directory if it doesn't exist
      if (!await existsAsync(AppUploadService.baseUploadDir)) {
        await mkdirAsync(AppUploadService.baseUploadDir, { recursive: true });
      }
      
      logSuccess('App upload directories initialized', 'AppUploadService');
      return true;
    } catch (error) {
      logError(`Failed to initialize app upload directories: ${error.message}`, 'AppUploadService', error);
      return false;
    }
  },

  /**
   * Generate unique filename for app upload
   * @param {string} originalFileName - Original file name
   * @param {string} platform - Platform (android, ios, etc.)
   * @param {string} version - App version
   * @returns {string} Generated filename
   */
  generateFileName: (originalFileName, platform, version) => {
    const timestamp = Date.now();
    const randomSuffix = Math.random().toString(36).substring(2, 15);
    const fileExtension = path.extname(originalFileName);
    return `${platform}-${version}-${timestamp}-${randomSuffix}${fileExtension}`;
  },

  /**
   * Calculate file checksum
   * @param {string} filePath - Path to the file
   * @returns {Promise<string>} File checksum
   */
  calculateChecksum: async (filePath) => {
    return new Promise((resolve, reject) => {
      const hash = crypto.createHash('sha256');
      const stream = fs.createReadStream(filePath);
      
      stream.on('data', data => hash.update(data));
      stream.on('end', () => resolve(hash.digest('hex')));
      stream.on('error', reject);
    });
  },

  /**
   * Upload new app file
   * @param {Object} fileData - File data
   * @param {Object} metadata - App metadata
   * @param {Object} user - Uploading user
   * @returns {Promise<Object>} Upload result
   */
  uploadApp: async (fileData, metadata, user) => {
    try {
      // Initialize directories
      await AppUploadService.init();

      // Auto-generate version and build number
      const latestApp = await AppUpload.findOne({ 
        platform: metadata.platform || 'android' 
      }).sort({ createdAt: -1 });

      let version = '1.0.0';
      let buildNumber = 1;

      if (latestApp) {
        // Increment build number
        buildNumber = (latestApp.buildNumber || 0) + 1;
        // Keep same version, increment build
        version = latestApp.version || '1.0.0';
      }

      // Generate unique upload ID and filename
      const uploadId = await idGenerator.generateAppUploadId(AppUpload);
      const fileName = AppUploadService.generateFileName(
        fileData.originalname, 
        metadata.platform || 'android', 
        version
      );
      const filePath = path.join(AppUploadService.baseUploadDir, fileName);

      // Move file to destination
      await fs.promises.rename(fileData.path, filePath);

      // Get file stats
      const stats = await statAsync(filePath);
      const checksum = await AppUploadService.calculateChecksum(filePath);

      // Create app upload record
      const appUpload = new AppUpload({
        uploadId,
        appName: metadata.appName || 'DeynCare Mobile App',
        version,
        buildNumber,
        fileName,
        originalFileName: fileData.originalname,
        filePath,
        fileSize: stats.size,
        fileType: metadata.fileType || 'apk',
        platform: metadata.platform || 'android',
        isLatest: true, // Always set new uploads as latest
        releaseNotes: 'New version uploaded',
        uploadedBy: user._id,
        uploadedByName: user.fullName,
        metadata: {
          checksum,
          mimeType: fileData.mimetype,
          encoding: fileData.encoding
        }
      });

      await appUpload.save();

      // Set this as the latest version (removes latest flag from others)
      await AppUpload.setLatestVersion(uploadId, metadata.platform || 'android');

      logSuccess(`App uploaded successfully: ${fileName}`, 'AppUploadService');
      
      return {
        success: true,
        data: appUpload,
        message: 'App uploaded successfully'
      };

    } catch (error) {
      logError(`Failed to upload app: ${error.message}`, 'AppUploadService', error);
      throw new AppError('Failed to upload app file', 500);
    }
  },

  /**
   * Get all app uploads with pagination and filters
   * @param {Object} filters - Query filters
   * @returns {Promise<Object>} Paginated results
   */
  getApps: async (filters = {}) => {
    try {
      const {
        page = 1,
        limit = 10,
        platform,
        isLatest,
        isActive,
        version,
        search,
        sortBy = 'createdAt',
        sortOrder = 'desc'
      } = filters;

      // Build query
      const query = {};
      
      if (platform) query.platform = platform;
      if (typeof isLatest === 'boolean') query.isLatest = isLatest;
      if (typeof isActive === 'boolean') query.isActive = isActive;
      if (version) query.version = version;
      if (search) {
        query.$or = [
          { appName: { $regex: search, $options: 'i' } },
          { version: { $regex: search, $options: 'i' } },
          { releaseNotes: { $regex: search, $options: 'i' } }
        ];
      }

      // Sort options
      const sort = {};
      sort[sortBy] = sortOrder === 'asc' ? 1 : -1;

      // Pagination options
      const options = {
        page: parseInt(page),
        limit: parseInt(limit),
        sort,
        populate: {
          path: 'uploadedBy',
          select: 'fullName email'
        }
      };

      const result = await AppUpload.paginate(query, options);
      
      return {
        success: true,
        data: result,
        message: 'Apps retrieved successfully'
      };

    } catch (error) {
      logError(`Failed to get apps: ${error.message}`, 'AppUploadService', error);
      throw new AppError('Failed to retrieve apps', 500);
    }
  },

  /**
   * Get app upload by ID
   * @param {string} uploadId - Upload ID
   * @returns {Promise<Object>} App upload data
   */
  getAppById: async (uploadId) => {
    try {
      const appUpload = await AppUpload.findOne({ uploadId, isActive: true })
        .populate('uploadedBy', 'fullName email');

      if (!appUpload) {
        throw new AppError('App not found', 404);
      }

      return {
        success: true,
        data: appUpload,
        message: 'App retrieved successfully'
      };

    } catch (error) {
      if (error instanceof AppError) throw error;
      logError(`Failed to get app by ID: ${error.message}`, 'AppUploadService', error);
      throw new AppError('Failed to retrieve app', 500);
    }
  },

  /**
   * Get latest app by platform
   * @param {string} platform - Platform
   * @returns {Promise<Object>} Latest app for platform
   */
  getLatestByPlatform: async (platform) => {
    try {
      const appUpload = await AppUpload.getLatestByPlatform(platform);

      if (!appUpload) {
        throw new AppError(`No app found for platform: ${platform}`, 404);
      }

      return {
        success: true,
        data: appUpload,
        message: 'Latest app retrieved successfully'
      };

    } catch (error) {
      if (error instanceof AppError) throw error;
      logError(`Failed to get latest app: ${error.message}`, 'AppUploadService', error);
      throw new AppError('Failed to retrieve latest app', 500);
    }
  },

  /**
   * Download app file
   * @param {string} uploadId - Upload ID
   * @returns {Promise<Object>} File path and metadata
   */
  downloadApp: async (uploadId) => {
    try {
      // Get app record
      const appUpload = await AppUpload.findOne({ uploadId, isActive: true });

      if (!appUpload) {
        throw new AppError('App not found', 404);
      }

      // Check if file exists
      if (!await existsAsync(appUpload.filePath)) {
        throw new AppError('App file not found on server', 404);
      }

      // Increment download count
      await AppUpload.incrementDownloadCount(uploadId);

      logInfo(`App downloaded: ${appUpload.fileName}`, 'AppUploadService');

      return {
        success: true,
        data: {
          filePath: appUpload.filePath,
          fileName: appUpload.originalFileName,
          mimeType: appUpload.metadata.mimeType || 'application/octet-stream',
          fileSize: appUpload.fileSize
        },
        message: 'App ready for download'
      };

    } catch (error) {
      if (error instanceof AppError) throw error;
      logError(`Failed to download app: ${error.message}`, 'AppUploadService', error);
      throw new AppError('Failed to download app', 500);
    }
  },

  /**
   * Update app metadata
   * @param {string} uploadId - Upload ID
   * @param {Object} updateData - Update data
   * @returns {Promise<Object>} Updated app data
   */
  updateApp: async (uploadId, updateData) => {
    try {
      const appUpload = await AppUpload.findOneAndUpdate(
        { uploadId, isActive: true },
        { $set: updateData },
        { new: true, runValidators: true }
      ).populate('uploadedBy', 'fullName email');

      if (!appUpload) {
        throw new AppError('App not found', 404);
      }

      // If setting as latest, update other versions
      if (updateData.isLatest === true) {
        await AppUpload.setLatestVersion(uploadId, appUpload.platform);
      }

      logSuccess(`App updated: ${uploadId}`, 'AppUploadService');

      return {
        success: true,
        data: appUpload,
        message: 'App updated successfully'
      };

    } catch (error) {
      if (error instanceof AppError) throw error;
      logError(`Failed to update app: ${error.message}`, 'AppUploadService', error);
      throw new AppError('Failed to update app', 500);
    }
  },

  /**
   * Delete app upload
   * @param {string} uploadId - Upload ID
   * @returns {Promise<Object>} Deletion result
   */
  deleteApp: async (uploadId) => {
    try {
      const appUpload = await AppUpload.findOne({ uploadId, isActive: true });

      if (!appUpload) {
        throw new AppError('App not found', 404);
      }

      // Delete file from filesystem
      if (await existsAsync(appUpload.filePath)) {
        await unlinkAsync(appUpload.filePath);
      }

      // Mark as inactive instead of deleting the record
      await AppUpload.findOneAndUpdate(
        { uploadId },
        { $set: { isActive: false } }
      );

      logSuccess(`App deleted: ${uploadId}`, 'AppUploadService');

      return {
        success: true,
        message: 'App deleted successfully'
      };

    } catch (error) {
      if (error instanceof AppError) throw error;
      logError(`Failed to delete app: ${error.message}`, 'AppUploadService', error);
      throw new AppError('Failed to delete app', 500);
    }
  },

  /**
   * Get download statistics
   * @param {Object} filters - Query filters
   * @returns {Promise<Object>} Statistics data
   */
  getStats: async (filters = {}) => {
    try {
      const { platform, startDate, endDate } = filters;

      // Build match query
      const matchQuery = { isActive: true };
      if (platform) matchQuery.platform = platform;
      if (startDate || endDate) {
        matchQuery.createdAt = {};
        if (startDate) matchQuery.createdAt.$gte = new Date(startDate);
        if (endDate) matchQuery.createdAt.$lte = new Date(endDate);
      }

      // Aggregate statistics
      const stats = await AppUpload.aggregate([
        { $match: matchQuery },
        {
          $group: {
            _id: '$platform',
            totalDownloads: { $sum: '$downloadCount' },
            totalApps: { $sum: 1 },
            latestVersion: { $max: '$version' },
            averageFileSize: { $avg: '$fileSize' }
          }
        }
      ]);

      return {
        success: true,
        data: stats,
        message: 'Statistics retrieved successfully'
      };

    } catch (error) {
      logError(`Failed to get stats: ${error.message}`, 'AppUploadService', error);
      throw new AppError('Failed to retrieve statistics', 500);
    }
  }
};

module.exports = AppUploadService; 