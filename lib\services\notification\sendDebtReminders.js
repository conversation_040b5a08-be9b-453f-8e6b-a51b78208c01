import apiBridge from '@/lib/api/bridge';
import { ENDPOINTS } from '@/lib/api/contract';
import { handleError, processApiResponse } from '../baseService';

/**
 * Send debt reminder notifications (SuperAdmin only)
 * @param {Object} reminderData - Reminder configuration
 * @param {string[]} [reminderData.shopIds] - Specific shop IDs (optional)
 * @param {string} [reminderData.reminderType='overdue'] - Type: overdue, upcoming, custom
 * @param {string} [reminderData.customMessage] - Custom message for type 'custom'
 * @param {number} [reminderData.daysOverdue] - Days overdue filter
 * @param {string} [reminderData.priority='normal'] - Priority: low, normal, high
 * @returns {Promise<Object>} Reminder results
 */
async function sendDebtReminders(reminderData = {}) {
  // Validate reminder type specific requirements
  if (reminderData.reminderType === 'custom' && !reminderData.customMessage) {
    throw new Error('Custom message is required for custom reminder type');
  }

  // Validate custom message length if provided
  if (reminderData.customMessage && reminderData.customMessage.length > 500) {
    throw new Error('Custom message cannot exceed 500 characters');
  }

  // Validate priority if provided
  if (reminderData.priority && !['low', 'normal', 'high'].includes(reminderData.priority)) {
    throw new Error('Priority must be low, normal, or high');
  }

  // Validate reminder type if provided
  if (reminderData.reminderType && !['overdue', 'upcoming', 'custom'].includes(reminderData.reminderType)) {
    throw new Error('Reminder type must be overdue, upcoming, or custom');
  }

  // Validate daysOverdue if provided
  if (reminderData.daysOverdue !== undefined && (typeof reminderData.daysOverdue !== 'number' || reminderData.daysOverdue < 0)) {
    throw new Error('Days overdue must be a non-negative number');
  }

  // Prepare notification data with backend-expected structure
  const backendData = {
    reminderType: reminderData.reminderType || 'overdue',
    ...(reminderData.shopIds && { shopIds: reminderData.shopIds })
  };

  // Make API request using the bridge
  const response = await apiBridge.post(ENDPOINTS.NOTIFICATIONS.PUSH.DEBT_REMINDERS, backendData, {
    skipCache: true
  });

  // Process response using utility
  const result = processApiResponse(response, 'Debt reminder notifications sent successfully');
  return result;
}

export default sendDebtReminders; 