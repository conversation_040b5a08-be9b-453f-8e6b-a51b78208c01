'use client';

import { useState, useCallback, useEffect, createContext, useContext } from 'react';
import { useRouter } from 'next/navigation';
import api from '@/lib/api';
import axios from 'axios';
import { toast } from 'sonner';

// Create auth context
const AuthContext = createContext(null);

/**
 * Provider component for authentication
 */
export function AuthProvider({ children }) {
  const [user, setUser] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const router = useRouter();
  
  // Check authentication status
  const checkAuthStatus = useCallback(async () => {
    setIsLoading(true);
    try {
      // Get current user profile
      const response = await api.get('/auth/me');
      const userData = response.data.data.user;
      
      setUser(userData);
      setIsAuthenticated(true);
    } catch (error) {
      console.error('Authentication check failed:', error);
      setUser(null);
      setIsAuthenticated(false);
      
      // Clear token on auth failure
      if (typeof window !== 'undefined') {
        localStorage.removeItem('accessToken');
      }
    } finally {
      setIsLoading(false);
    }
  }, []);
  
  // Login user
  const login = useCallback(async (email, password) => {
    setIsLoading(true);
    try {
      const response = await api.post('/api/auth/login', { email, password });
      const { accessToken, refreshToken, user: userData } = response.data.data;
      
      // Store tokens
      if (typeof window !== 'undefined') {
        localStorage.setItem('accessToken', accessToken);
        localStorage.setItem('refreshToken', refreshToken);
      }
      
      setUser(userData);
      setIsAuthenticated(true);
      toast.success(`Welcome back, ${userData.fullName}`);
      return userData;
    } catch (error) {
      const errorMessage = error.response?.data?.message || 'Login failed';
      console.error('Login error:', errorMessage);
      toast.error(errorMessage);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, []);
  
  // Logout user
  const logout = useCallback(async () => {
    setIsLoading(true);
    try {
      // Call logout endpoint if authenticated
      if (isAuthenticated && typeof window !== 'undefined') {
        const refreshToken = localStorage.getItem('refreshToken');
        if (refreshToken) {
          await api.post('/api/auth/logout', { refreshToken });
        }
      }
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      // Clear auth state regardless of API success
      setUser(null);
      setIsAuthenticated(false);
      
      // Remove tokens
      if (typeof window !== 'undefined') {
        localStorage.removeItem('accessToken');
        localStorage.removeItem('refreshToken');
      }
      
      setIsLoading(false);
    }
  }, [isAuthenticated]);
  
  // Token refresh handler
  const handleTokenRefresh = useCallback(async (event) => {
    console.log('[Auth] Token expired event received, attempting refresh');
    try {
      // Get refresh token
      const refreshToken = localStorage.getItem('refreshToken');
      if (!refreshToken) {
        console.log('[Auth] No refresh token found');
        window.dispatchEvent(new CustomEvent('auth:token:refresh:failed'));
        return;
      }
      
      // Call refresh endpoint
      const response = await axios.post(
        `${process.env.NEXT_PUBLIC_API_URL || 'https://deyncare-backend.khanciye.com'}/api/auth/refresh-token`,
        { refreshToken },
        { 
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
          }
        }
      );
      
      if (response.data?.data?.accessToken) {
        const { accessToken, refreshToken: newRefreshToken } = response.data.data;
        
        // Update storage
        localStorage.setItem('accessToken', accessToken);
        if (newRefreshToken) {
          localStorage.setItem('refreshToken', newRefreshToken);
        }
        
        // Dispatch success event with new token
        window.dispatchEvent(new CustomEvent('auth:token:refreshed', {
          detail: { newToken: accessToken }
        }));
        
        console.log('[Auth] Token refreshed successfully');
        return;
      }
      
      console.log('[Auth] Token refresh response did not contain a new token');
      window.dispatchEvent(new CustomEvent('auth:token:refresh:failed'));
    } catch (error) {
      console.error('[Auth] Token refresh failed:', error);
      
      // Clear tokens on refresh failure
      localStorage.removeItem('accessToken');
      localStorage.removeItem('refreshToken');
      
      // Update auth state
      setUser(null);
      setIsAuthenticated(false);
      
      // Dispatch failure event
      window.dispatchEvent(new CustomEvent('auth:token:refresh:failed'));
      
      // Handle redirect for failed auth
      if (typeof window !== 'undefined' && window.location.pathname !== '/login') {
        console.log('[Auth] Redirecting to login after failed token refresh');
        setTimeout(() => {
          router.push('/login');
        }, 100);
      }
    }
  }, [router]);
  
  // Set up token refresh listener
  useEffect(() => {
    if (typeof window !== 'undefined') {
      window.addEventListener('auth:token:expired', handleTokenRefresh);
      return () => {
        window.removeEventListener('auth:token:expired', handleTokenRefresh);
      };
    }
  }, [handleTokenRefresh]);
  
  // Check auth status on mount
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const token = localStorage.getItem('accessToken');
      if (token) {
        checkAuthStatus();
      } else {
        setIsLoading(false);
      }
    }
  }, [checkAuthStatus]);
  
  // Computed properties for convenience
  const isSuperAdmin = user?.role === 'superAdmin';
  const isAdmin = user?.role === 'admin';
  const isStaff = user?.role === 'staff';
  
  const value = {
    user,
    isLoading,
    isAuthenticated,
    isSuperAdmin,
    isAdmin,
    isStaff,
    login,
    logout,
    checkAuthStatus
  };
  
  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}

/**
 * Hook for using authentication context
 */
export function useAuth() {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}
