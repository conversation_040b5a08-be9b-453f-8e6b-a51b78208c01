import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { 
  Users, 
  CheckSquare, 
  XSquare, 
  RefreshCw, 
  Calendar, 
  CreditCard,
  AlertTriangle,
  CheckCircle
} from 'lucide-react';
import { useSubscription } from '@/hooks/use-subscription';
import { toast } from 'sonner';

/**
 * BulkOperations Component
 * Provides bulk operation capabilities for subscription management
 * 
 * @param {Object} props - Component props
 * @param {Array} props.subscriptions - Array of subscription objects
 * @param {Function} props.onRefresh - Callback to refresh data after operations
 * @returns {JSX.Element} Rendered component
 */
const BulkOperations = ({ subscriptions = [], onRefresh }) => {
  const [selectedSubscriptions, setSelectedSubscriptions] = useState([]);
  const [operation, setOperation] = useState('');
  const [operationData, setOperationData] = useState({});
  const [isProcessing, setIsProcessing] = useState(false);

  const { bulkUpdateSubscriptions } = useSubscription();

  // Handle subscription selection
  const handleSubscriptionToggle = (subscriptionId) => {
    setSelectedSubscriptions(prev => 
      prev.includes(subscriptionId)
        ? prev.filter(id => id !== subscriptionId)
        : [...prev, subscriptionId]
    );
  };

  // Handle select all
  const handleSelectAll = () => {
    if (selectedSubscriptions.length === subscriptions.length) {
      setSelectedSubscriptions([]);
    } else {
      setSelectedSubscriptions(subscriptions.map(sub => sub.id || sub._id));
    }
  };

  // Handle bulk operation execution
  const handleExecuteOperation = async () => {
    if (!operation || selectedSubscriptions.length === 0) {
      toast.error('Please select an operation and at least one subscription');
      return;
    }

    setIsProcessing(true);
    
    try {
      const bulkData = {
        operation,
        subscriptionIds: selectedSubscriptions,
        ...operationData
      };

      await bulkUpdateSubscriptions(bulkData);
      
      // Reset selections and operation
      setSelectedSubscriptions([]);
      setOperation('');
      setOperationData({});
      
      // Refresh data
      if (onRefresh) {
        onRefresh();
      }
      
      toast.success(`Bulk operation completed for ${selectedSubscriptions.length} subscriptions`);
    } catch (error) {
      console.error('Bulk operation error:', error);
      toast.error('Failed to execute bulk operation');
    } finally {
      setIsProcessing(false);
    }
  };

  // Render operation-specific form fields
  const renderOperationFields = () => {
    switch (operation) {
      case 'extend':
        return (
          <div className="space-y-4">
            <div>
              <Label htmlFor="days">Extension Days</Label>
              <Select
                value={operationData.days?.toString() || ''}
                onValueChange={(value) => setOperationData({ ...operationData, days: parseInt(value) })}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select extension period" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="7">7 days</SelectItem>
                  <SelectItem value="14">14 days</SelectItem>
                  <SelectItem value="30">30 days</SelectItem>
                  <SelectItem value="60">60 days</SelectItem>
                  <SelectItem value="90">90 days</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label htmlFor="reason">Reason for Extension</Label>
              <Textarea
                id="reason"
                placeholder="Enter reason for extending subscriptions..."
                value={operationData.reason || ''}
                onChange={(e) => setOperationData({ ...operationData, reason: e.target.value })}
              />
            </div>
          </div>
        );
      
      case 'cancel':
        return (
          <div className="space-y-4">
            <div>
              <Label htmlFor="reason">Cancellation Reason</Label>
              <Textarea
                id="reason"
                placeholder="Enter reason for cancellation..."
                value={operationData.reason || ''}
                onChange={(e) => setOperationData({ ...operationData, reason: e.target.value })}
                required
              />
            </div>
            <div className="flex items-center space-x-2">
              <Checkbox
                id="immediate"
                checked={operationData.immediateEffect || false}
                onCheckedChange={(checked) => setOperationData({ ...operationData, immediateEffect: checked })}
              />
              <Label htmlFor="immediate">Cancel immediately (otherwise at end of period)</Label>
            </div>
          </div>
        );

      case 'update_auto_renewal':
        return (
          <div>
            <Label>Auto Renewal Setting</Label>
            <Select
              value={operationData.autoRenew?.toString() || ''}
              onValueChange={(value) => setOperationData({ ...operationData, autoRenew: value === 'true' })}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select auto renewal setting" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="true">Enable Auto Renewal</SelectItem>
                <SelectItem value="false">Disable Auto Renewal</SelectItem>
              </SelectContent>
            </Select>
          </div>
        );

      case 'change_status':
        return (
          <div className="space-y-4">
            <div>
              <Label>New Status</Label>
              <Select
                value={operationData.status || ''}
                onValueChange={(value) => setOperationData({ ...operationData, status: value })}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select new status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="active">Active</SelectItem>
                  <SelectItem value="suspended">Suspended</SelectItem>
                  <SelectItem value="cancelled">Cancelled</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label htmlFor="reason">Reason</Label>
              <Textarea
                id="reason"
                placeholder="Enter reason for status change..."
                value={operationData.reason || ''}
                onChange={(e) => setOperationData({ ...operationData, reason: e.target.value })}
              />
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="space-y-6">
      {/* Selection Summary */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            Subscription Selection
          </CardTitle>
          <CardDescription>
            Select subscriptions for bulk operations
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-2">
              <Checkbox
                checked={selectedSubscriptions.length === subscriptions.length && subscriptions.length > 0}
                onCheckedChange={handleSelectAll}
              />
              <Label>Select All ({subscriptions.length} subscriptions)</Label>
            </div>
            <Badge variant="secondary">
              {selectedSubscriptions.length} selected
            </Badge>
          </div>

          {/* Subscription List */}
          <div className="max-h-64 overflow-y-auto space-y-2">
            {subscriptions.map((subscription) => (
              <div 
                key={subscription.id || subscription._id} 
                className="flex items-center justify-between p-2 border rounded"
              >
                <div className="flex items-center gap-2">
                  <Checkbox
                    checked={selectedSubscriptions.includes(subscription.id || subscription._id)}
                    onCheckedChange={() => handleSubscriptionToggle(subscription.id || subscription._id)}
                  />
                  <div className="flex flex-col">
                    <span className="text-sm font-medium">
                      {subscription.shop?.name || subscription.shopName || 'Unknown Shop'}
                    </span>
                    <span className="text-xs text-muted-foreground">
                      {subscription.plan?.name || subscription.planType} • {subscription.status}
                    </span>
                  </div>
                </div>
                <Badge variant={subscription.status === 'active' ? 'success' : 'secondary'}>
                  {subscription.status}
                </Badge>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Bulk Operations */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CheckSquare className="h-5 w-5" />
            Bulk Operations
          </CardTitle>
          <CardDescription>
            Execute operations on selected subscriptions
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Operation Selection */}
          <div>
            <Label>Select Operation</Label>
            <Select value={operation} onValueChange={setOperation}>
              <SelectTrigger>
                <SelectValue placeholder="Choose an operation" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="extend">
                  <div className="flex items-center gap-2">
                    <Calendar className="h-4 w-4" />
                    Extend Subscriptions
                  </div>
                </SelectItem>
                <SelectItem value="cancel">
                  <div className="flex items-center gap-2">
                    <XSquare className="h-4 w-4" />
                    Cancel Subscriptions
                  </div>
                </SelectItem>
                <SelectItem value="update_auto_renewal">
                  <div className="flex items-center gap-2">
                    <RefreshCw className="h-4 w-4" />
                    Update Auto Renewal
                  </div>
                </SelectItem>
                <SelectItem value="change_status">
                  <div className="flex items-center gap-2">
                    <CreditCard className="h-4 w-4" />
                    Change Status
                  </div>
                </SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Operation-specific fields */}
          {operation && (
            <>
              <Separator />
              {renderOperationFields()}
            </>
          )}

          {/* Execute Button */}
          <div className="flex items-center gap-2 pt-4">
            <Button
              onClick={handleExecuteOperation}
              disabled={!operation || selectedSubscriptions.length === 0 || isProcessing}
              className="flex items-center gap-2"
            >
              {isProcessing ? (
                <RefreshCw className="h-4 w-4 animate-spin" />
              ) : (
                <CheckCircle className="h-4 w-4" />
              )}
              {isProcessing ? 'Processing...' : 'Execute Operation'}
            </Button>

            {selectedSubscriptions.length > 0 && (
              <div className="flex items-center gap-1 text-sm text-muted-foreground">
                <AlertTriangle className="h-4 w-4" />
                This will affect {selectedSubscriptions.length} subscription{selectedSubscriptions.length > 1 ? 's' : ''}
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default BulkOperations; 