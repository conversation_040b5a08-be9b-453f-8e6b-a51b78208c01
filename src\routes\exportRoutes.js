/**
 * Export Routes
 * Handles export routes for both web dashboard and mobile application
 */
const express = require('express');
const router = express.Router();
const authMiddleware = require('../middleware/authMiddleware');
const { validate } = require('../middleware/validationMiddleware');
const { exportSchemas } = require('../validations');
const planExportController = require('../controllers/export/planExportController');
const subscriptionExportController = require('../controllers/export/subscriptionExportController');
const userExportController = require('../controllers/export/userExportController');
const shopExportController = require('../controllers/export/shopExportController');
const customerExportController = require('../controllers/export/customerExportController');
const debtExportController = require('../controllers/export/debtExportController');
const mlRiskExportController = require('../controllers/export/mlRiskExportController');
const coreFeatureExportController = require('../controllers/export/coreFeatureExportController');

// All export routes require authentication
router.use(authMiddleware.authenticate);

// =============================================================================
// SUPERADMIN WEB DASHBOARD EXPORT ROUTES
// =============================================================================

// Simplified Core Feature Export Routes (SuperAdmin only)
// GET /api/export/core-feature?feature=users&format=csv
router.get('/core-feature', 
  authMiddleware.authorize('superAdmin'),
  coreFeatureExportController.exportCoreFeature
);

// Get available core features for export
router.get('/core-features', 
  authMiddleware.authorize('superAdmin'),
  coreFeatureExportController.getAvailableCoreFeatures
);

// Get core feature export statistics
router.get('/core-features/stats', 
  authMiddleware.authorize('superAdmin'),
  coreFeatureExportController.getCoreFeatureStats
);

// Legacy Individual Export Routes (kept for backward compatibility)
// User Management export routes (SuperAdmin only)
router.get('/users', 
  authMiddleware.authorize('superAdmin'),
  validate(exportSchemas.userExportQuery),
  (req, res, next) => {
    const format = req.query.format || 'csv';
    if (format === 'excel') {
      return userExportController.exportToExcel(req, res, next);
    }
    return userExportController.exportToCSV(req, res, next);
  }
);

// Shop Management export routes (SuperAdmin only)
router.get('/shops', 
  authMiddleware.authorize('superAdmin'),
  validate(exportSchemas.shopExportQuery),
  (req, res, next) => {
    const format = req.query.format || 'csv';
    if (format === 'excel') {
      return shopExportController.exportToExcel(req, res, next);
    }
    return shopExportController.exportToCSV(req, res, next);
  }
);

// Plan Management export routes (SuperAdmin only)
router.get('/plans', 
  authMiddleware.authorize('superAdmin'),
  validate(exportSchemas.planExportQuery),
  (req, res, next) => {
    const format = req.query.format || 'csv';
    if (format === 'excel') {
      return planExportController.exportToExcel(req, res, next);
    }
    return planExportController.exportToCSV(req, res, next);
  }
);

// Subscription Management export routes (SuperAdmin only)
router.get('/subscriptions',
  authMiddleware.authorize('superAdmin'),
  validate(exportSchemas.subscriptionExportQuery),
  (req, res, next) => {
    const format = req.query.format || 'csv';
    if (format === 'excel') {
      return subscriptionExportController.exportToExcel(req, res, next);
    }
    return subscriptionExportController.exportToCSV(req, res, next);
  }
);

// =============================================================================
// ADMIN MOBILE APP EXPORT ROUTES
// =============================================================================

// Customer export routes (Admin only)
router.get('/customers', 
  authMiddleware.authorize('admin'),
  validate(exportSchemas.customerExportQuery),
  (req, res, next) => {
    const format = req.query.format || 'csv';
    if (format === 'excel') {
      return customerExportController.exportToExcel(req, res, next);
    }
    return customerExportController.exportToCSV(req, res, next);
  }
);

// Debt Management export routes (Admin only)
router.get('/debts', 
  authMiddleware.authorize('admin'),
  validate(exportSchemas.debtExportQuery),
  (req, res, next) => {
    const format = req.query.format || 'csv';
    if (format === 'excel') {
      return debtExportController.exportToExcel(req, res, next);
    }
    return debtExportController.exportToCSV(req, res, next);
  }
);

// ML Risk Assessment export routes (Admin only)
router.get('/ml-risk', 
  authMiddleware.authorize('admin'),
  validate(exportSchemas.mlRiskExportQuery),
  (req, res, next) => {
    const format = req.query.format || 'csv';
    if (format === 'excel') {
      return mlRiskExportController.exportToExcel(req, res, next);
    }
    return mlRiskExportController.exportToCSV(req, res, next);
  }
);

module.exports = router; 