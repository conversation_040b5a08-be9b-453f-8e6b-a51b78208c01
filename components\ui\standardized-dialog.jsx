import { cn } from "@/lib/utils";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";

/**
 * Standardized Dialog Component
 * 
 * This component prevents multiple scroll issues by using a proper flex layout
 * with a single scroll container in the body section.
 * 
 * Usage:
 * <StandardizedDialog
 *   trigger={<Button>Open Dialog</Button>}
 *   title="Dialog Title"
 *   description="Optional description"
 *   size="lg" // sm, md, lg, xl, full
 *   className="additional-classes"
 * >
 *   <StandardizedDialog.Body>
 *     Your content here (this will be scrollable if needed)
 *   </StandardizedDialog.Body>
 *   <StandardizedDialog.Footer>
 *     <Button>Cancel</Button>
 *     <Button>Save</Button>
 *   </StandardizedDialog.Footer>
 * </StandardizedDialog>
 */

const sizeClasses = {
  sm: "max-w-sm",
  md: "max-w-md", 
  lg: "max-w-lg",
  xl: "max-w-xl",
  "2xl": "max-w-2xl",
  "3xl": "max-w-3xl",
  "4xl": "max-w-4xl",
  "5xl": "max-w-5xl",
  full: "max-w-[95vw]"
};

export function StandardizedDialog({ 
  children, 
  trigger, 
  title, 
  description, 
  size = "md",
  className,
  open,
  onOpenChange,
  ...props 
}) {
  return (
    <Dialog open={open} onOpenChange={onOpenChange} {...props}>
      {trigger && <DialogTrigger asChild>{trigger}</DialogTrigger>}
      <DialogContent 
        className={cn(
          "dialog-content", // Uses our standardized CSS class
          sizeClasses[size],
          className
        )}
      >
        {(title || description) && (
          <DialogHeader className="dialog-header px-6 pt-6 pb-4">
            {title && <DialogTitle className="text-lg font-semibold">{title}</DialogTitle>}
            {description && (
              <DialogDescription className="text-sm text-muted-foreground">
                {description}
              </DialogDescription>
            )}
          </DialogHeader>
        )}
        {children}
      </DialogContent>
    </Dialog>
  );
}

// Sub-components for better organization
StandardizedDialog.Body = function DialogBody({ children, className, ...props }) {
  return (
    <div 
      className={cn("dialog-body px-6", className)}
      {...props}
    >
      {children}
    </div>
  );
};

StandardizedDialog.Footer = function DialogFooter({ children, className, ...props }) {
  return (
    <div 
      className={cn(
        "dialog-footer px-6 py-4 flex items-center justify-end gap-2",
        className
      )}
      {...props}
    >
      {children}
    </div>
  );
};

// For cases where you need the raw dialog without standardization
StandardizedDialog.Raw = function RawDialog({ children, size = "md", className, ...props }) {
  return (
    <DialogContent 
      className={cn(
        sizeClasses[size],
        className
      )}
      {...props}
    >
      {children}
    </DialogContent>
  );
};

export default StandardizedDialog; 