/**
 * Authentication operations - login, logout, password reset, etc.
 */
import { toast } from "sonner";
import AuthService from "@/lib/services/auth";
import { errorHandlers } from "@/lib/api/contract";
import { setCsrfToken } from "@/lib/api/token";

/**
 * Login operation
 * @param {Object} options - Configuration options
 * @param {Function} options.setLoading - Function to update loading state
 * @param {Function} options.setError - Function to update error state
 * @param {Function} options.setUser - Function to update user state
 * @param {Function} options.setIsAuthenticated - Function to update authentication state
 * @param {string} email - User email
 * @param {string} password - User password
 * @returns {Promise<Object>} - Login response data
 */
export const login = async ({ setLoading, setError, setUser, setIsAuthenticated }, email, password) => {
  setLoading(true);
  setError(null);
  
  try {
    console.log("[Auth] Attempting login...");
    // Call the login API endpoint
    const data = await AuthService.login(email, password);
    // AuthService already returns the data object directly
    
    // Log only success without exposing sensitive data
    console.log("[Auth] Login successful");
    
    // Save tokens to localStorage with proper validation
    if (data.accessToken) {
      console.log("[Auth] Storing access token");
      localStorage.setItem("accessToken", data.accessToken);
    } else {
      console.error("[Auth] No access token received in login response");
    }
    
    // Store refresh token from response - backend now includes it in both response and secure cookie
    if (data.refreshToken) {
      console.log("[Auth] Storing refresh token from response");
      localStorage.setItem("refreshToken", data.refreshToken);
    } else {
      console.log("[Auth] No refresh token in response - should be in HTTP-only cookie");
    }
    
    // Handle CSRF token from both cookie and response
    // Our backend now sets both a cookie and includes the token in the response
    if (typeof window !== 'undefined') {
      try {
        // First check for CSRF token cookie set by the backend
        const csrfCookie = document.cookie.match(new RegExp('(^| )XSRF-TOKEN=([^;]+)'));
        if (csrfCookie) {
          console.log("[Auth] CSRF token cookie detected");
        }
        
        // Then check for CSRF token in the response data (new approach)
        if (data.csrfToken) {
          console.log("[Auth] Storing CSRF token from response data");
          setCsrfToken(data.csrfToken);
        } else {
          // Fallback to other possible response formats
          const csrfHeader = data.xsrfToken;
          if (csrfHeader) {
            console.log("[Auth] Storing alternate CSRF token from response");
            setCsrfToken(csrfHeader);
          } else if (!csrfCookie) {
            console.log("[Auth] No CSRF token found in cookie or response, secure operations might fail");
          }
        }
      } catch (error) {
        console.log("[Auth] Error handling CSRF tokens:", error.message);
      }
    }
    
    // Set complete user data from backend response
    console.log("[Auth] Setting complete user data from backend");
    
    // Case insensitive role check for superAdmin
    const normalizedRole = data.user.role?.toLowerCase() || '';
    const isSuperAdmin = normalizedRole === 'superadmin';
    // Log role check without revealing specific role information
    console.log(`[Auth] Role verification completed`);
    
    // Store the complete user object from backend
    setUser(data.user);
    setIsAuthenticated(true);
    
    // Show success toast
    toast.success("Login successful");
    
    setLoading(false);
    return data;
  } catch (err) {
    console.error("[Auth] Login error:", err);
    const friendlyErrorMessage = errorHandlers.getErrorMessage(err);
    setError(friendlyErrorMessage);
    toast.error(friendlyErrorMessage);
    setLoading(false);
    throw err;
  }
};

/**
 * Logout operation
 * @param {Object} options - Configuration options
 * @param {Function} options.setLoading - Function to update loading state
 * @param {Function} options.setUser - Function to update user state
 * @param {Function} options.setIsAuthenticated - Function to update authentication state
 * @param {Object} options.router - Next.js router
 * @returns {Promise<void>}
 */
export const logout = async ({ setLoading, setUser, setIsAuthenticated, router }) => {
  setLoading(true);
  try {
    // Call the logout API endpoint
    await AuthService.logout();
    
    // Remove tokens from localStorage
    localStorage.removeItem("accessToken");
    localStorage.removeItem("refreshToken");
    localStorage.removeItem("csrfToken");
    
    // Clear user data and authentication state
    setUser(null);
    setIsAuthenticated(false);
    
    // Show success message
    toast.success("Logged out successfully");
    
    // Redirect to login page
    router.push('/login');
  } catch (err) {
    // Even if the API call fails, we should still log out locally
    localStorage.removeItem("accessToken");
    localStorage.removeItem("refreshToken");
    setUser(null);
    
    // Show friendly error notification
    toast.error("You've been logged out successfully, but we couldn't reach the server to confirm.");
    
    // Still redirect to login
    router.push('/login');
  }
};

/**
 * Forgot password operation
 * @param {Object} options - Configuration options
 * @param {Function} options.setLoading - Function to update loading state
 * @param {Function} options.setError - Function to update error state
 * @param {string} email - User email
 * @returns {Promise<Object>} - Forgot password response data
 */
export const forgotPassword = async ({ setLoading, setError }, email) => {
  setLoading(true);
  setError(null);
  
  try {
    // Call the forgot password API endpoint
    const data = await AuthService.forgotPassword(email);
    // AuthService already returns the data directly
    
    // Show success toast
    toast.success("Password reset instructions sent to your email.");
    
    setLoading(false);
    return data;
  } catch (err) {
    const friendlyErrorMessage = errorHandlers.getErrorMessage(err);
    setError(friendlyErrorMessage);
    toast.error(friendlyErrorMessage);
    setLoading(false);
    throw err;
  }
};

/**
 * Reset password operation
 * @param {Object} options - Configuration options
 * @param {Function} options.setLoading - Function to update loading state
 * @param {Function} options.setError - Function to update error state
 * @param {string} token - Reset password token
 * @param {string} newPassword - New password
 * @param {string} confirmPassword - Confirm password
 * @returns {Promise<Object>} - Reset password response data
 */
export const resetPassword = async ({ setLoading, setError }, token, newPassword, confirmPassword) => {
  setLoading(true);
  setError(null);
  
  try {
    // Call the reset password API endpoint with the correct parameter names
    const data = await AuthService.resetPassword(token, newPassword, confirmPassword);
    // AuthService already returns the data directly
    
    // Show success toast
    toast.success("Password has been reset successfully. Please log in with your new password.");
    
    setLoading(false);
    return data;
  } catch (err) {
    const friendlyErrorMessage = errorHandlers.getErrorMessage(err);
    setError(friendlyErrorMessage);
    toast.error(friendlyErrorMessage);
    setLoading(false);
    throw err;
  }
};

/**
 * Change password operation
 * @param {Object} options - Configuration options
 * @param {Function} options.setLoading - Function to update loading state
 * @param {Function} options.setError - Function to update error state
 * @param {string} currentPassword - Current password
 * @param {string} newPassword - New password
 * @returns {Promise<Object>} - Change password response data
 */
export const changePassword = async ({ setLoading, setError }, currentPassword, newPassword) => {
  setLoading(true);
  setError(null);
  
  try {
    const data = await AuthService.changePassword(currentPassword, newPassword);
    toast.success("Password changed successfully");
    setLoading(false);
    return data;
  } catch (err) {
    const friendlyErrorMessage = errorHandlers.getErrorMessage(err);
    setError(friendlyErrorMessage);
    toast.error(friendlyErrorMessage);
    setLoading(false);
    throw err;
  }
};
