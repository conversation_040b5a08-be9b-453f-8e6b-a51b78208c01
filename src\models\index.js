/**
 * Model Factory for preventing circular dependencies
 * Uses lazy loading to load models only when needed
 */

// Model cache to prevent re-loading
const modelCache = {};

/**
 * Lazy load a model to prevent circular dependencies
 * @param {string} modelName - Name of the model (e.g., 'user', 'shop')
 * @returns {Object} Model instance
 */
const getModel = (modelName) => {
  if (!modelCache[modelName]) {
    try {
      modelCache[modelName] = require(`./${modelName}.model`);
    } catch (error) {
      console.error(`Failed to load model ${modelName}:`, error.message);
      throw new Error(`Model '${modelName}' not found or failed to load`);
    }
  }
  return modelCache[modelName];
};

/**
 * Get all available models (lazy-loaded)
 * @returns {Object} Object with all model instances
 */
const getAllModels = () => {
  const modelNames = [
    'user', 'shop', 'subscription', 'customer', 'debt', 'payment',
    'notification', 'log', 'setting', 'session',
    'file', 'shopSetting', 'financialSnapshot', 'report', 'discountCode', 'Plan'
  ];
  
  const models = {};
  modelNames.forEach(name => {
    Object.defineProperty(models, name.charAt(0).toUpperCase() + name.slice(1), {
      get: () => getModel(name),
      enumerable: true,
      configurable: false
    });
  });
  
  return models;
};

// Export both the factory function and individual models
module.exports = {
  // Factory function for lazy loading
  getModel,
  getAllModels,
  
  // Legacy support - lazy-loaded properties
  get User() { return getModel('user'); },
  get Shop() { return getModel('shop'); },
  get Subscription() { return getModel('subscription'); },
  get Customer() { return getModel('customer'); },
  get Debt() { return getModel('debt'); },
  get Payment() { return getModel('payment'); },
  get Notification() { return getModel('notification'); },
  get Log() { return getModel('log'); },
  get Setting() { return getModel('setting'); },
  get Session() { return getModel('session'); },
  get File() { return getModel('file'); },
  get ShopSetting() { return getModel('shopSetting'); },
  get FinancialSnapshot() { return getModel('financialSnapshot'); },
  get Report() { return getModel('report'); },
  get DiscountCode() { return getModel('discountCode'); },
  get Plan() { return getModel('Plan'); }
};
