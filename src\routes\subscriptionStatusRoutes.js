/**
 * Subscription Status Routes
 * Provides endpoints for mobile apps to check subscription status and access restrictions
 */
const express = require('express');
const router = express.Router();
const authMiddleware = require('../middleware/authMiddleware');
const { getSubscriptionStatus } = require('../middleware/subscriptionMiddleware');

// All subscription status routes require authentication
router.use(authMiddleware.authenticate);

/**
 * GET /api/subscription-status/:shopId
 * Get detailed subscription status for a shop (Mobile App specific)
 */
router.get('/:shopId', 
  authMiddleware.authorize(['admin', ]),
  getSubscriptionStatus
);

/**
 * GET /api/subscription-status/check/current
 * Check current user's subscription status
 */
router.get('/check/current', 
  authMiddleware.authorize(['admin', ]),
  async (req, res, next) => {
    // Use shop ID from authenticated user
    req.params.shopId = req.user.shopId;
    getSubscriptionStatus(req, res, next);
  }
);

module.exports = router; 