import React, { useState, useEffect } from 'react';
import { 
  Eye, 
  CreditCard, 
  Building2, 
  Calendar, 
  User, 
  FileText, 
  DollarSign,
  CheckCircle,
  XCircle,
  Clock,
  AlertTriangle
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogFooter, 
  DialogHeader, 
  DialogTitle 
} from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import PaymentTransactionsService from '@/lib/services/payment-transactions';

/**
 * TransactionDetailsDialog Component
 * Dialog for viewing detailed payment transaction information
 * 
 * @param {Object} props - Component props
 * @param {boolean} props.open - Dialog open state
 * @param {Function} props.onOpenChange - Dialog open state change callback
 * @param {Object} props.transaction - Transaction to display
 * @param {Function} props.onApprove - Approve callback
 * @param {Function} props.onReject - Reject callback
 * @param {boolean} props.loading - Loading state
 * @returns {JSX.Element} Rendered component
 */
const TransactionDetailsDialog = ({
  open = false,
  onOpenChange,
  transaction = null,
  onApprove,
  onReject,
  loading = false
}) => {
  const [detailedTransaction, setDetailedTransaction] = useState(null);
  const [detailsLoading, setDetailsLoading] = useState(false);

  // Fetch detailed transaction data when dialog opens
  useEffect(() => {
    if (open && transaction?.paymentId) {
      fetchTransactionDetails();
    }
  }, [open, transaction?.paymentId]);

  const fetchTransactionDetails = async () => {
    try {
      setDetailsLoading(true);
      const details = await PaymentTransactionsService.getTransactionById(transaction.paymentId);
      setDetailedTransaction(details || transaction);
    } catch (error) {
      console.error('Failed to fetch transaction details:', error);
      setDetailedTransaction(transaction); // Fallback to basic transaction data
    } finally {
      setDetailsLoading(false);
    }
  };

  if (!transaction && !detailedTransaction) return null;

  const displayTransaction = detailedTransaction || transaction;
  const isPending = displayTransaction?.status?.toLowerCase() === 'pending';
  const canApprove = isPending;
  const canReject = isPending;

  const getStatusIcon = (status) => {
    switch (status?.toLowerCase()) {
      case 'approved':
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'rejected':
      case 'failed':
        return <XCircle className="h-4 w-4 text-red-600" />;
      case 'pending':
      case 'processing':
      default:
        return <Clock className="h-4 w-4 text-yellow-600" />;
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-2xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Eye className="h-5 w-5" />
            Payment Transaction Details
          </DialogTitle>
          <DialogDescription>
            Complete information for payment transaction {displayTransaction?.paymentId}
          </DialogDescription>
        </DialogHeader>

        {detailsLoading ? (
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-2 border-primary border-t-transparent" />
            <span className="ml-2">Loading transaction details...</span>
          </div>
        ) : (
          <div className="space-y-6 py-4">
            {/* Transaction Status */}
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-lg flex items-center gap-2">
                  {getStatusIcon(displayTransaction?.status)}
                  Transaction Status
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Current Status:</span>
                  <Badge variant={PaymentTransactionsService.getStatusVariant(displayTransaction?.status)}>
                    {PaymentTransactionsService.formatStatus(displayTransaction?.status)}
                  </Badge>
                </div>
                {displayTransaction?.approvedBy && (
                  <div className="flex items-center justify-between mt-2">
                    <span className="text-sm text-muted-foreground">Approved by:</span>
                    <span className="text-sm">{displayTransaction.approvedBy}</span>
                  </div>
                )}
                {displayTransaction?.approvedAt && (
                  <div className="flex items-center justify-between mt-2">
                    <span className="text-sm text-muted-foreground">Approved on:</span>
                    <span className="text-sm">{PaymentTransactionsService.formatDate(displayTransaction.approvedAt)}</span>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Payment Information */}
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-lg flex items-center gap-2">
                  <CreditCard className="h-5 w-5" />
                  Payment Information
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <span className="text-sm font-medium text-muted-foreground">Payment ID</span>
                    <p className="text-sm">{displayTransaction?.paymentId}</p>
                  </div>
                  <div>
                    <span className="text-sm font-medium text-muted-foreground">Payment Method</span>
                    <p className="text-sm">{PaymentTransactionsService.formatPaymentMethod(displayTransaction?.method)}</p>
                  </div>
                  <div>
                    <span className="text-sm font-medium text-muted-foreground">Amount</span>
                    <p className="text-sm font-semibold">{PaymentTransactionsService.formatCurrency(displayTransaction?.amount)}</p>
                  </div>
                  <div>
                    <span className="text-sm font-medium text-muted-foreground">Currency</span>
                    <p className="text-sm">{displayTransaction?.currency || 'USD'}</p>
                  </div>
                </div>

                {displayTransaction?.originalAmount && displayTransaction?.originalAmount !== displayTransaction?.amount && (
                  <div className="bg-green-50 dark:bg-green-900/10 p-3 rounded-lg">
                    <div className="flex justify-between items-center">
                      <span className="text-sm font-medium text-green-800 dark:text-green-200">Original Amount:</span>
                      <span className="text-sm text-green-700 dark:text-green-300 line-through">
                        {PaymentTransactionsService.formatCurrency(displayTransaction.originalAmount)}
                      </span>
                    </div>
                    {displayTransaction?.discountAmount > 0 && (
                      <div className="flex justify-between items-center mt-1">
                        <span className="text-sm font-medium text-green-800 dark:text-green-200">Discount Applied:</span>
                        <span className="text-sm text-green-700 dark:text-green-300">
                          -{PaymentTransactionsService.formatCurrency(displayTransaction.discountAmount)}
                        </span>
                      </div>
                    )}
                  </div>
                )}

                {displayTransaction?.referenceNumber && (
                  <div>
                    <span className="text-sm font-medium text-muted-foreground">Reference Number</span>
                    <p className="text-sm font-mono">{displayTransaction.referenceNumber}</p>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Shop and Customer Information */}
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-lg flex items-center gap-2">
                  <Building2 className="h-5 w-5" />
                  Shop & Customer
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="grid grid-cols-1 gap-4">
                  <div>
                    <span className="text-sm font-medium text-muted-foreground">Shop Name</span>
                    <p className="text-sm">{displayTransaction?.shopName || 'Unknown Shop'}</p>
                  </div>
                  {displayTransaction?.customerName && (
                    <div>
                      <span className="text-sm font-medium text-muted-foreground">Customer Name</span>
                      <p className="text-sm">{displayTransaction.customerName}</p>
                    </div>
                  )}
                  {displayTransaction?.subscriptionId && (
                    <div>
                      <span className="text-sm font-medium text-muted-foreground">Subscription ID</span>
                      <p className="text-sm font-mono">{displayTransaction.subscriptionId}</p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Timeline */}
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-lg flex items-center gap-2">
                  <Calendar className="h-5 w-5" />
                  Timeline
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="grid grid-cols-1 gap-3">
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium text-muted-foreground">Created</span>
                    <span className="text-sm">{PaymentTransactionsService.formatDate(displayTransaction?.createdAt)}</span>
                  </div>
                  {displayTransaction?.updatedAt && displayTransaction?.updatedAt !== displayTransaction?.createdAt && (
                    <div className="flex justify-between items-center">
                      <span className="text-sm font-medium text-muted-foreground">Last Updated</span>
                      <span className="text-sm">{PaymentTransactionsService.formatDate(displayTransaction.updatedAt)}</span>
                    </div>
                  )}
                  {displayTransaction?.approvedAt && (
                    <div className="flex justify-between items-center">
                      <span className="text-sm font-medium text-green-600">Approved</span>
                      <span className="text-sm text-green-600">{PaymentTransactionsService.formatDate(displayTransaction.approvedAt)}</span>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Notes and Comments */}
            {(displayTransaction?.approvalNotes || displayTransaction?.rejectionReason) && (
              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-lg flex items-center gap-2">
                    <FileText className="h-5 w-5" />
                    Notes & Comments
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  {displayTransaction?.approvalNotes && (
                    <div className="bg-green-50 dark:bg-green-900/10 p-3 rounded-lg">
                      <span className="text-sm font-medium text-green-800 dark:text-green-200">Approval Notes:</span>
                      <p className="text-sm text-green-700 dark:text-green-300 mt-1">{displayTransaction.approvalNotes}</p>
                    </div>
                  )}
                  {displayTransaction?.rejectionReason && (
                    <div className="bg-red-50 dark:bg-red-900/10 p-3 rounded-lg">
                      <span className="text-sm font-medium text-red-800 dark:text-red-200">Rejection Reason:</span>
                      <p className="text-sm text-red-700 dark:text-red-300 mt-1">{displayTransaction.rejectionReason}</p>
                    </div>
                  )}
                </CardContent>
              </Card>
            )}

            {/* Action Required Warning */}
            {isPending && (
              <Card className="border-yellow-200 bg-yellow-50 dark:bg-yellow-900/10">
                <CardContent className="pt-6">
                  <div className="flex items-start gap-3">
                    <AlertTriangle className="h-5 w-5 text-yellow-600 mt-0.5" />
                    <div>
                      <h4 className="font-medium text-yellow-800 dark:text-yellow-200">Action Required</h4>
                      <p className="text-sm text-yellow-700 dark:text-yellow-300 mt-1">
                        This payment transaction is pending approval. Please review the details and either approve or reject the transaction.
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        )}

        <DialogFooter className="flex gap-2">
          <Button
            variant="outline"
            onClick={() => onOpenChange?.(false)}
          >
            Close
          </Button>
          
          {canApprove && (
            <Button
              onClick={() => onApprove?.(displayTransaction)}
              disabled={loading || detailsLoading}
              className="bg-green-600 hover:bg-green-700 text-white"
            >
              <CheckCircle className="h-4 w-4 mr-2" />
              Approve
            </Button>
          )}
          
          {canReject && (
            <Button
              onClick={() => onReject?.(displayTransaction)}
              disabled={loading || detailsLoading}
              variant="destructive"
            >
              <XCircle className="h-4 w-4 mr-2" />
              Reject
            </Button>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default TransactionDetailsDialog; 