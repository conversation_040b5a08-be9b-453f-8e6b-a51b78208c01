import apiBridge from '@/lib/api/bridge';
import { ENDPOINTS } from '@/lib/api/contract';
import { toast } from 'sonner';
import BaseService from '@/lib/services/baseService';

/**
 * Update shop information via SuperAdmin API
 * @param {string} shopId - ID of the shop to update
 * @param {Object} updateData - Data to update
 * @param {string} updateData.shopName - Shop name
 * @param {string} updateData.shopAddress - Shop address
 * @param {string} updateData.ownerName - Owner name
 * @param {string} updateData.email - Email
 * @param {string} updateData.phone - Phone
 * @param {Object} updateData.businessDetails - Business details
 * @param {Object} updateData.location - Location details
 * @returns {Promise<Object>} Updated shop object
 */
async function updateShop(shopId, updateData) {
  try {
    // Transform frontend data to match SuperAdmin backend expectations
    const transformedData = {
      // Shop basic info
      ...(updateData.shopName && { shopName: updateData.shopName }),
      ...(updateData.shopAddress && { address: updateData.shopAddress }), // Map to backend field name
      ...(updateData.ownerName && { ownerName: updateData.ownerName }),
      ...(updateData.email && { email: updateData.email }),
      ...(updateData.phone && { phone: updateData.phone }),
      
      // Additional details
      ...(updateData.businessDetails && { businessDetails: updateData.businessDetails }),
      ...(updateData.location && { location: updateData.location }),
      
      // Any other fields passed through
      ...Object.keys(updateData)
        .filter(key => ![
          'shopName', 'shopAddress', 'ownerName', 'email', 'phone', 
          'businessDetails', 'location'
        ].includes(key))
        .reduce((obj, key) => {
          obj[key] = updateData[key];
          return obj;
        }, {})
    };
    
    // Make API request using SuperAdmin endpoint
    const response = await apiBridge.put(`${ENDPOINTS.SHOPS.BASE}/${shopId}`, transformedData, {
      clearCacheEndpoint: ENDPOINTS.SHOPS.BASE
    });
    
    // Process response
    if (response.data && response.data.success) {
      const updatedShop = response.data.data;
      toast.success('Shop updated successfully');
      
      // Return formatted shop data
      return {
        // Core identifiers
        id: updatedShop.shopId || updatedShop._id,
        shopId: updatedShop.shopId || updatedShop._id,
        _id: updatedShop._id || updatedShop.shopId,
        
        // Basic shop info (mapped from SuperAdmin response)
        shopName: updatedShop.shopName,
        shopAddress: updatedShop.address, // Map from backend field name
        ownerName: updatedShop.ownerName,
        email: updatedShop.email,
        phone: updatedShop.phone,
        
        // Status and verification
        status: updatedShop.status,
        verified: updatedShop.verified,
        registeredBy: updatedShop.registeredBy,
        
        // Logo
        logoUrl: updatedShop.logoUrl || '',
        
        // Additional details
        location: updatedShop.location || {},
        businessDetails: updatedShop.businessDetails || {},
        
        // Dates
        updatedAt: updatedShop.updatedAt,
        createdAt: updatedShop.createdAt,
        
        // Access control
        access: {
          isPaid: updatedShop.access?.isPaid || false,
          isActivated: updatedShop.access?.isActivated || false
        }
      };
    }
    
    // Handle unexpected response
    console.error('[SuperAdminShopService] Unexpected API response format:', response.data);
    toast.error('Failed to update shop: Unexpected response format');
    return null;
  } catch (error) {
    console.error(`[SuperAdminShopService] Error updating shop ${shopId}:`, error);
    
    // Enhanced error handling for SuperAdmin operations
    if (error.response?.status === 400 && error.response?.data?.message) {
      BaseService.handleError({ message: `Validation error: ${error.response.data.message}` }, 'SuperAdminShopService.updateShop', true);
    } else if (error.response?.status === 401) {
      BaseService.handleError({ message: 'Unauthorized: SuperAdmin access required' }, 'SuperAdminShopService.updateShop', true);
    } else if (error.response?.status === 403) {
      BaseService.handleError({ message: 'Forbidden: Only SuperAdmins can update shops' }, 'SuperAdminShopService.updateShop', true);
    } else if (error.response?.status === 404) {
      BaseService.handleError({ message: 'Shop not found' }, 'SuperAdminShopService.updateShop', true);
    } else {
      BaseService.handleError(error, 'SuperAdminShopService.updateShop', true);
    }
    
    return null;
  }
}

export default updateShop;
