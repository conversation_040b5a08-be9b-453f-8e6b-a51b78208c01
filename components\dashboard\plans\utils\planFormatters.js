/**
 * Plan formatting utilities
 * Provides consistent formatting for plan data display
 */

/**
 * Format currency value
 */
export function formatCurrency(amount, currency = 'USD') {
  if (amount === null || amount === undefined) return '$0.00';
  
  const formatter = new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: currency,
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  });
  
  return formatter.format(amount);
}

/**
 * Format plan status with label and variant
 */
export function formatPlanStatus(isActive) {
  return {
    label: isActive ? 'Active' : 'Inactive',
    value: isActive ? 'active' : 'inactive',
    variant: isActive ? 'default' : 'secondary'
  };
}

/**
 * Get status variant for Badge component
 */
export function getStatusVariant(status) {
  switch (status) {
    case 'active':
      return 'default';
    case 'inactive':
      return 'secondary';
    default:
      return 'outline';
  }
}

/**
 * Format plan type display
 */
export function formatPlanType(type) {
  const typeMap = {
    monthly: 'Monthly',
    yearly: 'Yearly',
    trial: 'Trial',
    custom: 'Custom',
    free: 'Free'
  };
  
  return typeMap[type] || type;
}

/**
 * Format billing cycle display
 */
export function formatBillingCycle(cycle) {
  const cycleMap = {
    monthly: 'month',
    yearly: 'year',
    weekly: 'week',
    daily: 'day',
    one_time: 'one-time'
  };
  
  return cycleMap[cycle] || cycle;
}

/**
 * Format date to readable string
 */
export function formatDate(date) {
  if (!date) return 'N/A';
  
  try {
    return new Date(date).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  } catch (error) {
    return 'Invalid Date';
  }
}

/**
 * Format date to short string (MM/DD/YY)
 */
export function formatShortDate(date) {
  if (!date) return 'N/A';
  
  try {
    return new Date(date).toLocaleDateString('en-US', {
      year: '2-digit',
      month: 'numeric',
      day: 'numeric'
    });
  } catch (error) {
    return 'Invalid';
  }
}

/**
 * Format plan features for display
 */
export function formatPlanFeatures(features) {
  if (!features || typeof features !== 'object') return [];
  
  return Object.entries(features)
    .filter(([key, value]) => value === true)
    .map(([key, value]) => ({
      key,
      label: key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase()),
      enabled: value
    }));
}

/**
 * Format plan limits for display
 */
export function formatPlanLimits(limits) {
  if (!limits || typeof limits !== 'object') return [];
  
  const limitLabels = {
    maxProducts: 'Products',
    maxEmployees: 'Employees',
    maxStorage: 'Storage (GB)',
    maxCustomers: 'Customers',
    maxTransactions: 'Transactions/month'
  };
  
  return Object.entries(limits)
    .map(([key, value]) => ({
      key,
      label: limitLabels[key] || key,
      value: value === -1 ? 'Unlimited' : value,
      formatted: value === -1 ? 'Unlimited' : formatLimitValue(key, value)
    }));
}

/**
 * Format individual limit value
 */
function formatLimitValue(key, value) {
  if (value === -1) return 'Unlimited';
  
  switch (key) {
    case 'maxStorage':
      return `${value} GB`;
    case 'maxTransactions':
      return `${value.toLocaleString()}/month`;
    default:
      return value.toLocaleString();
  }
}

/**
 * Format plan metadata tags
 */
export function formatPlanTags(metadata) {
  if (!metadata || !metadata.tags || !Array.isArray(metadata.tags)) return [];
  
  return metadata.tags.map(tag => ({
    label: tag.charAt(0).toUpperCase() + tag.slice(1),
    value: tag
  }));
}

/**
 * Format pricing display with billing cycle
 */
export function formatPricingDisplay(pricing) {
  if (!pricing) return 'Free';
  
  const { basePrice, currency, billingCycle } = pricing;
  const amount = formatCurrency(basePrice, currency);
  const cycle = formatBillingCycle(billingCycle);
  
  if (basePrice === 0) return 'Free';
  
  return `${amount}/${cycle}`;
}

/**
 * Format plan subscriber count
 */
export function formatSubscriberCount(count) {
  if (!count || count === 0) return '0 subscribers';
  if (count === 1) return '1 subscriber';
  
  return `${count.toLocaleString()} subscribers`;
}

/**
 * Format plan revenue
 */
export function formatPlanRevenue(revenue, currency = 'USD') {
  if (!revenue || revenue === 0) return formatCurrency(0, currency);
  
  return formatCurrency(revenue, currency);
}

/**
 * Get plan status color
 */
export function getPlanStatusColor(isActive) {
  return isActive ? 'green' : 'gray';
}

/**
 * Format plan description
 */
export function formatPlanDescription(description, maxLength = 100) {
  if (!description) return 'No description';
  
  if (description.length <= maxLength) return description;
  
  return description.substring(0, maxLength) + '...';
}

/**
 * Format plan creation date
 */
export function formatCreatedDate(createdAt) {
  if (!createdAt) return 'Unknown';
  
  try {
    const date = new Date(createdAt);
    const now = new Date();
    const diffInDays = Math.floor((now - date) / (1000 * 60 * 60 * 24));
    
    if (diffInDays === 0) return 'Today';
    if (diffInDays === 1) return 'Yesterday';
    if (diffInDays < 7) return `${diffInDays} days ago`;
    
    return formatDate(createdAt);
  } catch (error) {
    return 'Invalid Date';
  }
} 