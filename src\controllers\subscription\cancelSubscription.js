/**
 * Cancel Subscription Controller
 * Handles cancellation of subscriptions
 */
const { SubscriptionService } = require('../../services');
const { logError, successResponse } = require('../../utils');

/**
 * Cancel subscription
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
const cancelSubscription = async (req, res, next) => {
  try {
    const { reason, feedback, immediateEffect } = req.body;
    const { shopId } = req.user;
    
    if (!shopId) {
      return res.status(400).json({
        success: false,
        message: 'No shop associated with this user',
        statusCode: 400,
        type: 'shop_not_found'
      });
    }
    
    // Get current subscription for the shop
    const currentSubscription = await SubscriptionService.getCurrentSubscriptionByShop(shopId);
    
    if (!currentSubscription) {
      return res.status(404).json({
        success: false,
        message: 'Subscription not found',
        statusCode: 404,
        type: 'subscription_not_found'
      });
    }
    
    // For security tracking, we pass actor information to service
    const options = {
      actorId: req.user?.userId || 'system',
      actorRole: req.user?.role || 'system'
    };
    
    // Cancel subscription through service
    const updatedSubscription = await SubscriptionService.cancelSubscription(
      currentSubscription.subscriptionId, 
      { reason, feedback, immediateEffect }, 
      options
    );
    
    return successResponse(res, {
      message: 'Subscription canceled successfully',
      data: updatedSubscription,
    });
  } catch (error) {
    logError('Failed to cancel subscription', 'SubscriptionController', error);
    return next(error);
  }
};

module.exports = cancelSubscription;
