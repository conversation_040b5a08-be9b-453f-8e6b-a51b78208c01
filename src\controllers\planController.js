/**
 * Plan Controller
 * 
 * This module serves as the main entry point for plan-related controller operations.
 * Each operation has been refactored into its own file in the plan/ directory
 * for better code organization, maintainability, and testing.
 */

// Import all controllers from the plan/ directory
const {
  getAllPlans,
  getPlanById,
  createPlan,
  updatePlan,
  deletePlan,
  getPlanStats,
  togglePlanStatus
} = require('./plan');

// Re-export all plan controllers
const PlanController = {
  getAllPlans,
  getPlanById,
  createPlan,
  updatePlan,
  deletePlan,
  getPlanStats,
  togglePlanStatus
};

module.exports = PlanController;
