const Debt = require('../../models/debt.model');
const Customer = require('../../models/customer.model');
const Payment = require('../../models/payment.model');
const AppError = require('../../utils/core/AppError');
const mlService = require('../../services/mlRiskService');

/**
 * Get Debt By ID Controller (Admin Role - Shop Owner)
 * - Retrieve single debt with full ML evaluation details
 * - Include payment history and risk analysis
 * - Show ML payload data for transparency
 */
const getDebtById = async (req, res, next) => {
  try {
    const { debtId } = req.params;
    const shopId = req.user.shopId;

    // Find debt record
    const debt = await Debt.findOne({
      debtId,
      shopId,
      isDeleted: false
    }).lean();

    if (!debt) {
      return next(new AppError('Debt record not found', 404));
    }

    // Find customer
    const customer = await Customer.findOne({
      customerId: debt.customerId
    }).lean();

    if (!customer) {
      return next(new AppError('Customer not found', 404));
    }

    // Get payment history for this debt
    const payments = await Payment.find({
      debtId: debt.debtId,
      paymentContext: 'debt'
    }).sort({ paymentDate: -1 }).lean();

    // Calculate timeline information
    const now = new Date();
    const dueDate = new Date(debt.DueDate);
    const createdDate = new Date(debt.DebtCreationDate);
    const daysUntilDue = Math.ceil((dueDate - now) / (1000 * 60 * 60 * 24));
    const daysOverdue = daysUntilDue < 0 ? Math.abs(daysUntilDue) : 0;
    const daysActive = Math.ceil((now - createdDate) / (1000 * 60 * 60 * 24));

    // Check if ML evaluation is available or needed
    const isDueDatePassed = now > dueDate;
    const needsEvaluation = isDueDatePassed && debt.RiskLevel === 'Active Debt';
    
    let mlAnalysis = null;
    if (isDueDatePassed && debt.RiskLevel !== 'Active Debt') {
      // Generate ML payload for transparency
      mlAnalysis = {
        evaluated: true,
        riskLevel: debt.RiskLevel,
        evaluationDate: debt.updatedAt,
        mlPayload: mlService.prepareMLPayload(debt, customer),
        explanation: mlService.identifyRiskFactors(debt)
      };
    } else if (needsEvaluation) {
      mlAnalysis = {
        evaluated: false,
        status: 'Evaluation Pending',
        message: 'ML evaluation will be triggered automatically',
        canTriggerNow: true
      };
    } else {
      mlAnalysis = {
        evaluated: false,
        status: 'Waiting for Due Date',
        message: `ML evaluation will begin after ${debt.DueDate.toLocaleDateString()}`,
        daysUntilEvaluation: daysUntilDue
      };
    }

    // Format payment history
    const paymentHistory = payments.map(payment => ({
      paymentId: payment.paymentId,
      amount: payment.amount,
      date: payment.paymentDate,
      method: payment.paymentMethod,
      timing: {
        isOnTime: payment.IsOnTime,
        paymentDelay: payment.PaymentDelay,
        status: payment.IsOnTime ? 'On Time ✅' : 
                payment.PaymentDelay > 0 ? `${payment.PaymentDelay} days late ⏰` : 
                `${Math.abs(payment.PaymentDelay)} days early ⚡`
      },
      notes: payment.notes,
      createdAt: payment.createdAt
    }));

    // Calculate payment statistics
    const paymentStats = {
      totalPayments: payments.length,
      totalPaid: payments.reduce((sum, p) => sum + p.amount, 0),
      avgPaymentAmount: payments.length > 0 ? 
        Math.round(payments.reduce((sum, p) => sum + p.amount, 0) / payments.length) : 0,
      onTimePayments: payments.filter(p => p.IsOnTime).length,
      latePayments: payments.filter(p => !p.IsOnTime && p.PaymentDelay > 0).length,
      earlyPayments: payments.filter(p => !p.IsOnTime && p.PaymentDelay < 0).length
    };

    // Comprehensive debt information
    const debtDetails = {
      basic: {
        debtId: debt.debtId,
        description: debt.description,
        status: debt.status,
        createdAt: debt.createdAt,
        updatedAt: debt.updatedAt
      },
      customer: {
        customerId: customer.customerId,
        name: customer.CustomerName,
        type: customer.CustomerType,
        phone: customer.phone,
        email: customer.email,
        address: customer.address,
        riskProfile: customer.riskProfile
      },
      financial: {
        debtAmount: debt.DebtAmount,
        outstandingDebt: debt.OutstandingDebt,
        paidAmount: debt.PaidAmount || 0,
        debtPaidRatio: debt.DebtPaidRatio,
        repaymentTime: debt.RepaymentTime,
        paymentProgress: {
          percentage: (debt.DebtPaidRatio * 100).toFixed(1),
          remaining: debt.OutstandingDebt,
          paid: debt.PaidAmount || 0
        }
      },
      timeline: {
        createdDate: debt.DebtCreationDate,
        dueDate: debt.DueDate,
        paidDate: debt.PaidDate,
        daysActive,
        daysUntilDue: daysUntilDue > 0 ? daysUntilDue : 0,
        daysOverdue,
        isOverdue: daysOverdue > 0,
        isDueDatePassed,
        status: daysOverdue > 0 ? 'Overdue ❌' : 
               daysUntilDue === 0 ? 'Due Today ⚠️' :
               daysUntilDue <= 3 ? 'Due Soon ⏰' : 'Active ✅'
      },
      risk: {
        currentLevel: debt.RiskLevel,
        isEvaluated: debt.RiskLevel !== 'Active Debt',
        paymentDelay: debt.PaymentDelay,
        isOnTime: debt.IsOnTime,
        mlAnalysis
      },
      payments: {
        history: paymentHistory,
        statistics: paymentStats
      }
    };

    // Response data
    const responseData = {
      success: true,
      message: 'Debt details retrieved successfully',
      data: debtDetails
    };

    res.status(200).json(responseData);

  } catch (error) {
    console.error('Get debt by ID error:', error);
    return next(new AppError('Failed to retrieve debt details', 500));
  }
};

module.exports = getDebtById; 