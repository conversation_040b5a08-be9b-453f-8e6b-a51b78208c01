/**
 * Get Plan By Type Service
 * 
 * Finds a plan by its type (trial, monthly, yearly)
 * Matches backend getPlanByType.js functionality
 */

import apiBridge from '@/lib/api/bridge';
import { ENDPOINTS } from '@/lib/api/contract';
import { handleError, processApiResponse, logApiCall } from '../baseService';
import getPlans from './getPlans';

/**
 * Get plan by type
 * Finds the first active plan of the specified type
 * @param {string} planType - Plan type ('trial', 'monthly', 'yearly')
 * @returns {Promise<Object>} API response with plan or null
 */
async function getPlanByType(planType) {
  try {
    if (!planType) {
      throw new Error('Plan type is required');
    }

    // Validate plan type
    const validTypes = ['trial', 'monthly', 'yearly'];
    if (!validTypes.includes(planType)) {
      throw new Error(`Plan type must be one of: ${validTypes.join(', ')}`);
    }

    logApiCall('PlanService.getPlanByType', ENDPOINTS.PLANS.BASE, { type: planType });

    // Get all active plans
    const response = await getPlans({ includeInactive: false });
    
    if (!response.success) {
      throw new Error('Failed to retrieve plans');
    }

    const plans = response.data || [];
    
    // Find the matching plan that is active and not deleted
    const matchingPlan = plans.find(plan => 
      plan.type === planType && 
      plan.isActive && 
      !plan.isDeleted
    );

    return {
      success: true,
      data: matchingPlan || null,
      message: matchingPlan 
        ? `Plan of type '${planType}' found`
        : `No active plan found for type '${planType}'`
    };
  } catch (error) {
    handleError(error, 'PlanService.getPlanByType', true);
    throw error;
  }
}

export default getPlanByType; 