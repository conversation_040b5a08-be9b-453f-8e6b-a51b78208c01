# Payment System Fixes Documentation

## Overview

This document outlines the fixes for two critical payment system issues:

1. **Payment Amount Mismatch**: Wrong payment amounts being charged to users
2. **Payment Retry UX**: Poor user experience when payment fails after email verification

## System Architecture

### Dynamic Plan Creation System
Plans are **dynamically created** by the backend and **fetched by Flutter**:

```
Backend: createDefaultPlans() → Database → /api/public/plans → Flutter: FetchPlans
```

- **Backend**: Plans are created via `Plan.createDefaultPlans()` when database is empty
- **Flutter**: Fetches plans from `/api/public/plans` endpoint using `PlanBloc`
- **No Static Pricing**: All pricing is dynamic and managed through the backend

## Issue 1: Payment Amount Mismatch

### Problem Description
- Users selected monthly plan ($0.01 expected) but EVC charged yearly plan amount ($90)
- **Root Cause**: Dynamic plan creation had incorrect default pricing values
- Backend `createDefaultPlans()` had wrong values ($10 monthly, $8 yearly) instead of expected ($0.01 monthly, $90 yearly)

### Root Cause Analysis
1. **Dynamic Plan Creation**: `Plan.model.js` `createDefaultPlans()` method had incorrect default pricing
2. **Fallback Values**: Multiple files had hardcoded fallback pricing that didn't match expected values
3. **Consistency Issue**: No synchronization between plan creation defaults and fallback values

### Solution Implementation

#### 1. Fixed Dynamic Plan Creation
**File**: `deyncare-backend/src/models/Plan.model.js`

Updated `createDefaultPlans()` method:
```javascript
// Monthly plan
pricing: {
  basePrice: 0.01,  // Updated from 10
  currency: 'USD',
  billingCycle: 'monthly'
}

// Yearly plan  
pricing: {
  basePrice: 90,    // Updated from 8
  currency: 'USD',
  billingCycle: 'yearly'
}
```

#### 2. Fixed Fallback Values
**Files Updated**:
- `deyncare-backend/src/services/Subscription/createSubscription.js`
- `deyncare-backend/src/models/subscription.model.js`

Updated fallback pricing:
```javascript
// Monthly fallback
basePrice = pricing.basePrice || 0.01;  // Updated from 10

// Yearly fallback
basePrice = pricing.basePrice || 90;    // Updated from 8
```

#### 3. Database Fix Script
**File**: `deyncare-backend/src/scripts/fixPlanPricing.js`

Enhanced script that:
- Creates default plans if none exist (with correct pricing)
- Updates existing plans to correct pricing
- Verifies all pricing is correct after fix

**Usage**: 
```bash
node src/scripts/fixPlanPricing.js
```

#### 4. Flutter Fallback Sync
**File**: `Deyncare-mobile/deyncare_app/lib/presentation/screens/auth/payment/payment_screen.dart`

Updated Flutter fallback amounts to match backend dynamic creation:
- Monthly: $0.01
- Yearly: $90.00

### Testing Requirements
1. **Dynamic Plan Creation Test**: Verify `createDefaultPlans()` creates plans with correct pricing
2. **API Integration Test**: Test `/api/public/plans` endpoint returns correct plan data
3. **Flutter Integration Test**: Verify Flutter fetches and displays correct plan amounts
4. **Payment Flow Test**: Test complete payment flow with both plan types shows correct amounts
5. **Fallback Test**: Ensure Flutter fallbacks are used correctly when API fails

## Issue 2: Payment Retry UX Problem

### Problem Description
- Users with `email_verified_pending_payment` status couldn't retry payment
- Only option was to create new account with different email
- Poor user experience for failed payments

### Root Cause Analysis
1. **Status Flow**: No mechanism to retry payment for verified users
2. **UX Design**: No clear path for users to retry failed payments
3. **Backend Logic**: Missing payment retry endpoints

### Solution Implementation

#### 1. Payment Retry Controller
**File**: `deyncare-backend/src/controllers/auth/paymentRetryController.js`

**Endpoints**:
- `GET /api/auth/payment-retry/check/:userId` - Check retry eligibility
- `POST /api/auth/payment-retry/:userId` - Retry payment
- `DELETE /api/auth/payment-retry/:userId/reset` - Reset user (admin only)

#### 2. Payment Retry Routes
**File**: `deyncare-backend/src/routes/paymentRetryRoutes.js`

Registered in app.js as: `/api/auth/payment-retry/*`

#### 3. User Status Management
Enhanced user status handling for payment retry scenarios:

**Status Flow**:
```
pending → email_verified_pending_payment → active (after successful payment)
                      ↓
                 (retry option available)
```

### API Usage Examples

#### Check Retry Eligibility
```bash
GET /api/auth/payment-retry/check/USER123
```

Response:
```json
{
  "success": true,
  "eligible": true,
  "reason": "User can retry payment",
  "userStatus": "email_verified_pending_payment",
  "retryInfo": {
    "userId": "USER123",
    "email": "<EMAIL>",
    "planType": "monthly",
    "planAmount": 0.01,
    "shopName": "Test Shop"
  }
}
```

#### Retry Payment
```bash
POST /api/auth/payment-retry/USER123
Content-Type: application/json

{
  "paymentMethod": "EVC Plus",
  "phoneNumber": "+252123456789",
  "discountCode": "SAVE10"
}
```

Response:
```json
{
  "success": true,
  "message": "Payment completed successfully",
  "transactionId": "TXN123456",
  "amount": 0.01,
  "user": {
    "userId": "USER123",
    "email": "<EMAIL>",
    "status": "active",
    "isPaid": true
  }
}
```

#### Reset User (Admin Only)
```bash
DELETE /api/auth/payment-retry/USER123/reset
Content-Type: application/json

{
  "reason": "User requested new registration"
}
```

## Flutter Integration (Recommended)

### 1. Payment Retry Screen
Create a new screen for users with `email_verified_pending_payment` status:

```dart
// lib/presentation/screens/auth/payment_retry_screen.dart
class PaymentRetryScreen extends StatelessWidget {
  final String userId;
  final String email;
  // ... implementation
}
```

### 2. Auth State Updates
Update auth bloc to handle payment retry states:

```dart
// lib/presentation/blocs/auth/auth_event.dart
class PaymentRetryRequested extends AuthEvent {
  final String userId;
  final String paymentMethod;
  final String phoneNumber;
  final String? discountCode;
}

// lib/presentation/blocs/auth/auth_state.dart
class AuthPaymentRetryRequired extends AuthState {
  final User user;
  final PaymentRetryInfo retryInfo;
}
```

### 3. API Integration
Add payment retry methods to auth repository:

```dart
// lib/data/repositories/auth_repository_impl.dart
Future<PaymentRetryEligibility> checkPaymentRetryEligibility(String userId);
Future<PaymentResult> retryPayment(PaymentRetryRequest request);
```

## Testing Checklist

### Backend Tests
- [ ] Dynamic plan creation (`createDefaultPlans()`) works correctly
- [ ] `/api/public/plans` endpoint serves correct plan data
- [ ] Plan pricing fix script works correctly
- [ ] Payment retry eligibility check works
- [ ] EVC payment retry processes successfully
- [ ] Offline payment retry records correctly
- [ ] User status updates properly after successful retry
- [ ] Admin reset functionality works

### Flutter Tests
- [ ] Plan fetching via `FetchPlans` event works correctly
- [ ] Plan display shows correct amounts from backend
- [ ] Fallback amounts match backend dynamic creation values
- [ ] Payment retry screen displays correctly
- [ ] EVC payment retry flow works
- [ ] Error handling for failed retries
- [ ] User status updates in UI

### Integration Tests
- [ ] Complete payment flow with correct amounts (dynamic pricing)
- [ ] Plan creation → API serving → Flutter display chain works
- [ ] Payment retry flow end-to-end
- [ ] User status synchronization between Flutter and backend
- [ ] Admin tools for managing retry users

## Environment Setup

### Backend Environment Variables
```env
# Existing variables plus:
ENABLE_PAYMENT_RETRY=true
PAYMENT_RETRY_MAX_ATTEMPTS=3
```

### Database Updates
Run the enhanced pricing fix script:
```bash
cd deyncare-backend
node src/scripts/fixPlanPricing.js
```

This script will:
- Create default plans with correct pricing if none exist
- Update existing plans to correct pricing
- Verify all pricing is correct after the fix

### Restart Services
After implementing changes:
```bash
# Backend
npm restart

# Flutter
flutter clean
flutter pub get
```

## Monitoring and Logging

### Backend Logs
Payment retry actions are logged with:
- User ID and email
- Payment method and amount
- Success/failure status
- Error details for troubleshooting

### Flutter Logs
Payment retry attempts logged with:
- User session info
- Payment method selected
- API response status
- Error messages

## Future Improvements

1. **Automated Retry**: Implement scheduled retry for failed payments
2. **Payment Method Fallback**: If EVC fails, offer alternative payment methods
3. **User Notifications**: Email/SMS notifications for payment retry opportunities
4. **Admin Dashboard**: View and manage users with payment retry status
5. **Analytics**: Track payment retry success rates and user behavior

## Support and Troubleshooting

### Common Issues

1. **Payment Amount Still Wrong**
   - Check database values with: `db.pricingplans.find({})`
   - Verify Flutter is fetching from `/api/public/plans`, not using fallbacks
   - Test the `/api/public/plans` endpoint directly
   - Ensure `createDefaultPlans()` method has correct pricing values

2. **Payment Retry Not Working**
   - Check user status is `email_verified_pending_payment`
   - Verify EVC Plus integration is working
   - Check logs for specific error messages

3. **User Can't Access Retry**
   - Verify routes are registered in app.js
   - Check auth middleware is working
   - Confirm user has valid session

### Contact Information
For technical support with these fixes, contact the development team with:
- Error logs from both backend and Flutter
- User ID and email of affected users
- Payment method and amount details
- Screenshots of error messages

---

**Last Updated**: [Current Date]
**Version**: 1.0
**Authors**: Development Team 