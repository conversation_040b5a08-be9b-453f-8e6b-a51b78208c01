/**
 * Get plan by ID
 * GET /api/plans/:planId
 */
const PlanService = require('../../services/PlanService');
const { logInfo, logError } = require('../../utils');

const getPlanById = async (req, res, next) => {
  try {
    const { planId } = req.params;
    logInfo(`Fetching plan with ID: ${planId}`, 'PlanController');
    
    const plan = await PlanService.getPlanById(planId);
    
    return res.status(200).json({
      success: true,
      data: plan
    });
  } catch (error) {
    logError(`Failed to fetch plan: ${req.params.planId}`, 'PlanController', error);
    return next(error);
  }
};

module.exports = getPlanById;
