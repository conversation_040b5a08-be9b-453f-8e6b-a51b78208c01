/**
 * Update SuperAdmin Role Script
 * Updates any existing users with 'superadmin' role to 'superAdmin' (camelCase)
 * Run with: node src/scripts/updateSuperAdminRole.js
 */

require('dotenv').config();
const mongoose = require('mongoose');
const { User } = require('../models');
const { logSuccess, logError, logInfo } = require('../utils');

const updateSuperAdminRole = async () => {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI);
    logSuccess('Connected to MongoDB for role update', 'UpdateSuperAdminRole');

    // Find all users with 'superadmin' role (lowercase)
    const usersToUpdate = await User.find({ role: 'superadmin' });
    
    if (usersToUpdate.length === 0) {
      logInfo('No users found with old "superadmin" role', 'UpdateSuperAdminRole');
      return;
    }

    logInfo(`Found ${usersToUpdate.length} users with old "superadmin" role`, 'UpdateSuperAdminRole');

    // Update each user's role to 'superAdmin' (camelCase)
    for (const user of usersToUpdate) {
      user.role = 'superAdmin';
      await user.save();
      logSuccess(`Updated role for user: ${user.email} (${user.fullName})`, 'UpdateSuperAdminRole');
    }

    logSuccess(`Successfully updated ${usersToUpdate.length} users to "superAdmin" role`, 'UpdateSuperAdminRole');

  } catch (error) {
    logError('Error updating SuperAdmin roles', 'UpdateSuperAdminRole', error);
    throw error;
  } finally {
    // Disconnect from MongoDB
    await mongoose.disconnect();
    logInfo('Disconnected from MongoDB', 'UpdateSuperAdminRole');
  }
};

// Run the script
updateSuperAdminRole()
  .then(() => {
    console.log('✅ SuperAdmin role update completed successfully');
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ SuperAdmin role update failed:', error.message);
    process.exit(1);
  }); 