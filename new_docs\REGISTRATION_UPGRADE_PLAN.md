# Registration System Upgrade Plan

## 1. Model Updates

### UserModel Updates
```dart
class UserModel extends User {
  // New fields
  final String registrationStatus; // pending_email_verification, email_verified_pending_payment, active
  final bool isEmailVerified;
  final bool isPaid;
  final DateTime? emailVerifiedAt;
  final DateTime? paymentCompletedAt;
  final String? verificationCode;
  final DateTime? verificationCodeExpiresAt;
  
  // Shop related fields
  final String? shopId;
  final String? shopName;
  final String? shopStatus;
  final bool isShopActive;
}
```

### AuthTokenModel Updates
```dart
class AuthTokenModel extends AuthToken {
  // New fields
  final String tokenType; // access, refresh
  final String scope;
  final String userId;
  final DateTime issuedAt;
  final List<String> permissions;
}
```

### PlanModel Updates
```dart
class PlanModel extends Equatable {
  // New fields
  final int trialPeriodDays;
  final List<String> features;
  final List<String> limitations;
  final List<String> upgradePaths;
  final bool supportsDiscountCodes;
  final double? discountPercentage;
  final DateTime? discountExpiresAt;
}
```

## 2. Repository Implementation Updates

### AuthRepositoryImpl Updates
```dart
class AuthRepositoryImpl implements AuthRepository {
  // New methods
  Future<(User, AuthToken)> initRegistration({
    required String fullName,
    required String email,
    required String phone,
    required String password,
    required String shopName,
    required String shopAddress,
    String planType = 'trial',
    String paymentMethod = 'offline',
    bool initialPaid = false,
    String? discountCode,
  });

  Future<(User, AuthToken)> verifyEmail({
    required String email,
    required String verificationCode,
  });

  Future<(User, AuthToken)> processPayment({
    required String userId,
    required String shopId,
    required String planId,
    required String paymentMethod,
    required Map<String, dynamic> paymentDetails,
    String? discountCode,
  });
}
```

## 3. State Management Updates

### Registration States
```dart
enum RegistrationStatus {
  initial,
  pendingEmailVerification,
  emailVerifiedPendingPayment,
  paymentProcessing,
  registrationComplete,
  error
}

class RegistrationState extends Equatable {
  final RegistrationStatus status;
  final User? user;
  final String? error;
  final String? nextStep;
  final bool isLoading;
  final Map<String, dynamic>? paymentDetails;
}
```

### Registration Events
```dart
abstract class RegistrationEvent extends Equatable {
  const RegistrationEvent();
}

class InitRegistration extends RegistrationEvent {
  final String fullName;
  final String email;
  final String phone;
  final String password;
  final String shopName;
  final String shopAddress;
  final String planType;
  final String paymentMethod;
  final String? discountCode;
}

class VerifyEmail extends RegistrationEvent {
  final String email;
  final String verificationCode;
}

class ProcessPayment extends RegistrationEvent {
  final String userId;
  final String shopId;
  final String planId;
  final String paymentMethod;
  final Map<String, dynamic> paymentDetails;
  final String? discountCode;
}
```

## 4. UI/UX Updates

### Registration Flow Screens
1. Initial Registration Screen
   - User details form
   - Shop details form
   - Plan selection
   - Payment method selection
   - Discount code input

2. Email Verification Screen
   - Verification code input
   - Resend code option
   - Countdown timer
   - Success/error states

3. Payment Processing Screen
   - Payment method specific UI
   - EVC Plus integration
   - Offline payment instructions
   - Payment status tracking
   - Success/error states

### Status Indicators
- Progress bar showing registration steps
- Clear status messages
- Error handling and recovery options
- Loading states and animations

## 5. Implementation Steps

1. **Phase 1: Model Updates**
   - Update existing models
   - Add new fields and methods
   - Update serialization/deserialization

2. **Phase 2: Repository Updates**
   - Implement new registration flow
   - Add email verification
   - Add payment processing
   - Update error handling

3. **Phase 3: State Management**
   - Implement new states
   - Add event handling
   - Update state transitions
   - Add error handling

4. **Phase 4: UI/UX Updates**
   - Create new screens
   - Implement status indicators
   - Add loading states
   - Update error handling

5. **Phase 5: Testing**
   - Unit tests for models
   - Integration tests for repository
   - UI tests for screens
   - End-to-end testing

## 6. Migration Strategy

1. **Backward Compatibility**
   - Maintain old endpoints temporarily
   - Add new endpoints alongside old ones
   - Gradually deprecate old endpoints

2. **Data Migration**
   - Update existing user records
   - Add new fields with default values
   - Migrate payment records

3. **User Communication**
   - Notify users about changes
   - Provide migration guide
   - Offer support during transition

## 7. Security Considerations

1. **Token Management**
   - Secure token storage
   - Token refresh mechanism
   - Token revocation

2. **Payment Security**
   - Secure payment processing
   - Payment data encryption
   - Fraud prevention

3. **Data Protection**
   - User data encryption
   - Secure communication
   - Access control 