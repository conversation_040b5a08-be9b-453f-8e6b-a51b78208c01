## 4.6 System Design

### 4.6.1 Architecture Overview

The DeynCare system follows a microservices architecture with clear separation of concerns across multiple platforms:

- **Backend API**: Node.js/Express.js with MongoDB for core business logic
- **Web Dashboard**: Next.js/React for administrative interfaces
- **Mobile Application**: Flutter for field operations and customer interactions
- **ML Service**: FastAPI/Python for risk assessment and predictive analytics

### 4.6.2 Database Design

#### ******* Database Schema Overview

The DeynCare system uses MongoDB as its primary database, designed with a document-oriented approach that supports the complex, hierarchical nature of business data while maintaining flexibility for future enhancements.

#### ******* Core Entity Models

**1. Shop Collection**
```javascript
{
  _id: ObjectId,
  shopId: String (unique),
  shopName: String,
  ownerName: String,
  email: String,
  phone: String,
  address: String,
  location: {
    street: String,
    city: String,
    district: String,
    state: String,
    country: String (default: "Somalia"),
    coordinates: { latitude: Number, longitude: Number },
    formattedAddress: String
  },
  businessDetails: {
    type: String (enum: retail, wholesale, service, etc.),
    category: String,
    foundedDate: Date,
    registrationNumber: String,
    taxId: String,
    employeeCount: Number,
    operatingHours: Object
  },
  status: String (enum: pending, active, suspended, deleted),
  access: {
    isPaid: Boolean,
    isActivated: Boolean
  },
  verified: Boolean,
  currentSubscriptionId: String,
  statistics: {
    totalProducts: Number,
    totalCustomers: Number,
    totalRevenue: Number,
    totalDebts: Number,
    totalDebtAmount: Number,
    lastUpdated: Date
  },
  createdAt: Date,
  updatedAt: Date
}
```

**2. User Collection**
```javascript
{
  _id: ObjectId,
  userId: String (unique),
  fullName: String,
  email: String (unique),
  phone: String,
  password: String (hashed),
  role: String (enum: superAdmin, admin, employee),
  shopId: String,
  status: String (enum: active, inactive, suspended, pending),
  emailVerified: Boolean,
  isPaid: Boolean,
  isActivated: Boolean,
  lastLoginAt: Date,
  loginHistory: [{
    timestamp: Date,
    ipAddress: String,
    device: String,
    browser: String,
    location: String,
    status: String
  }],
  preferences: {
    theme: String,
    language: String,
    notifications: Object
  },
  createdAt: Date,
  updatedAt: Date
}
```

**3. Customer Collection**
```javascript
{
  _id: ObjectId,
  customerId: String (unique),
  shopId: String,
  fullName: String,
  email: String,
  phone: String,
  alternativePhone: String,
  type: String (enum: new, returning),
  category: String (enum: regular, vip, wholesale, corporate),
  address: String,
  detailedAddress: {
    street: String,
    city: String,
    state: String,
    zipCode: String,
    country: String,
    coordinates: { latitude: Number, longitude: Number }
  },
  outstandingBalance: Number,
  creditLimit: Number,
  paymentTerms: Number,
  riskScore: Number (0-100),
  totalPurchaseAmount: Number,
  totalDebtAmount: Number,
  lastPurchaseDate: Date,
  contactPreferences: {
    allowSMS: Boolean,
    allowEmail: Boolean,
    allowPhoneCall: Boolean
  },
  syncStatus: String,
  createdAt: Date,
  updatedAt: Date
}
```

**4. Debt Collection**
```javascript
{
  _id: ObjectId,
  debtId: String (unique),
  shopId: String,
  customerId: String,
  customerName: String,
  customerPhone: String,
  debtAmount: Number,
  paidAmount: Number,
  isSettled: Boolean,
  dueDate: Date,
  status: String (enum: active, overdue, high-risk, paid, partially-paid),
  riskScore: Number,
  riskLevel: String (enum: High Risk, Medium Risk, Low Risk),
  remindersSent: [{
    type: String,
    sentAt: Date,
    status: String
  }],
  collectionStatus: String,
  shortNote: String,
  createdAt: Date,
  updatedAt: Date
}
```

**5. Payment Collection**
```javascript
{
  _id: ObjectId,
  paymentId: String (unique),
  shopId: String,
  customerId: String,
  paymentContext: String (enum: debt, subscription),
  debtId: String,
  subscriptionId: String,
  amount: Number,
  originalAmount: Number,
  discountAmount: Number,
  method: String (enum: EVC, Cash),
  paymentType: String (enum: online, offline),
  status: String (enum: pending, confirmed, failed, refunded),
  isConfirmed: Boolean,
  confirmedAt: Date,
  confirmedBy: String,
  receiptNumber: String,
  gatewayInfo: {
    gatewayName: String,
    transactionId: String,
    gatewayFee: Number,
    responseCode: String
  },
  verificationAttempts: Array,
  recordedBy: String,
  createdAt: Date,
  updatedAt: Date
}
```

**6. Product Collection**
```javascript
{
  _id: ObjectId,
  productId: String (unique),
  shopId: String,
  name: String,
  description: String,
  price: Number,
  cost: Number,
  unit: String,
  stockQuantity: Number,
  lowStockThreshold: Number,
  categoryId: String,
  tags: [String],
  sku: String,
  status: String (enum: active, outOfStock, discontinued),
  createdAt: Date,
  updatedAt: Date
}
```



**8. Subscription Collection**
```javascript
{
  _id: ObjectId,
  subscriptionId: String (unique),
  shopId: String,
  planId: String,
  plan: {
    name: String,
    type: String (enum: trial, monthly, yearly),
    features: {
      debtTracking: Boolean,
      customerPayments: Boolean,
      smsReminders: Boolean,
      smartRiskScore: Boolean,
      businessDashboard: Boolean,
      exportReports: Boolean
    },
    limits: {
      maxProducts: Number,
      maxEmployees: Number,
      maxStorageMB: Number,
      maxCustomers: Number,
      maxDailyTransactions: Number
    }
  },
  pricing: {
    basePrice: Number,
    currency: String,
    billingCycle: String,
    discount: Object
  },
  status: String (enum: trial, active, past_due, canceled, expired),
  payment: {
    method: String,
    verified: Boolean,
    lastPaymentDate: Date,
    nextPaymentDate: Date,
    failedPayments: Number
  },
  dates: {
    startDate: Date,
    endDate: Date,
    trialEndsAt: Date,
    canceledAt: Date
  },
  history: Array,
  createdAt: Date,
  updatedAt: Date
}
```

#### ******* Supporting Collections

**9. Notification Collection**
```javascript
{
  _id: ObjectId,
  notificationId: String (unique),
  shopId: String,
  recipient: String,
  recipientType: String (enum: user, customer, admin),
  type: String (enum: SMS, Push, Email, InApp),
  priority: String (enum: low, medium, high, urgent),
  category: String (enum: transactional, promotional, reminder, alert),
  message: String,
  title: String,
  status: String (enum: sent, failed, pending, delivered, read),
  deliveryAttempts: Number,
  maxRetries: Number,
  provider: {
    name: String,
    messageId: String,
    cost: Number
  },
  relatedEntity: {
    type: String,
    id: String
  },
  createdAt: Date,
  updatedAt: Date
}
```

**10. Setting Collection**
```javascript
{
  _id: ObjectId,
  key: String (unique),
  category: String (enum: system, notification, payment, security, ml),
  displayName: String,
  description: String,
  value: Mixed,
  dataType: String (enum: string, number, boolean, object, array),
  defaultValue: Mixed,
  validation: Object,
  accessLevel: String (enum: superAdmin, admin, all),
  isEditable: Boolean,
  shopId: String,
  updatedBy: String,
  history: Array,
  updatedAt: Date
}
```

**11. DiscountCode Collection**
```javascript
{
  _id: ObjectId,
  discountId: String (unique),
  code: String (unique, uppercase),
  description: String,
  type: String (enum: fixed, percentage),
  value: Number,
  minimumPurchase: Number,
  maxDiscountAmount: Number,
  startDate: Date,
  expiryDate: Date,
  usageLimit: Number,
  usageCount: Number,
  applicableFor: [String] (enum: subscription, debt, all),
  scope: String (enum: global, shop),
  shopId: String,
  isActive: Boolean,
  createdBy: String,
  createdAt: Date,
  updatedAt: Date
}
```

#### ******* Database Relationships and Indexing Strategy

**Primary Relationships:**
- Shop → Users (1:N) via shopId
- Shop → Customers (1:N) via shopId
- Shop → Products (1:N) via shopId
- Shop → Debts (1:N) via shopId
- Shop → Subscription (1:1) via currentSubscriptionId
- Customer → Debts (1:N) via customerId
- Customer → Payments (1:N) via customerId
- Debt → Payments (1:N) via debtId

**Indexing Strategy:**
```javascript
// Performance-critical indexes
db.shops.createIndex({ "location.coordinates": "2dsphere" }) // Geospatial queries
db.customers.createIndex({ shopId: 1, phone: 1 }) // Customer lookup
db.customers.createIndex({ shopId: 1, outstandingBalance: -1 }) // Debt analysis
db.customers.createIndex({ riskScore: -1 }) // Risk-based queries
db.debts.createIndex({ shopId: 1, customerId: 1 }) // Debt lookup
db.debts.createIndex({ dueDate: 1 }) // Due date queries
db.debts.createIndex({ status: 1 }) // Status filtering
db.payments.createIndex({ shopId: 1, debtId: 1 }) // Payment tracking
db.payments.createIndex({ isConfirmed: 1 }) // Unconfirmed payments

db.products.createIndex({ shopId: 1, name: 1 }) // Product search
db.notifications.createIndex({ shopId: 1, createdAt: -1 }) // Notification history
db.sessions.createIndex({ userId: 1 }) // User sessions
db.logs.createIndex({ timestamp: 1 }, { expireAfterSeconds: 7776000 }) // TTL index (90 days)
```

#### ******* Data Integrity and Validation

**Schema Validation Rules:**
- Email format validation using regex patterns
- Phone number format validation for Somali numbers
- Currency amount validation (non-negative values)
- Date range validation (due dates cannot be in the past)
- Risk score validation (0-100 range)
- Status enum validation across all collections

**Business Logic Constraints:**
- Customers cannot exceed their credit limit
- Payments cannot exceed debt amounts
- Stock quantities cannot go negative
- Subscription end dates must be after start dates
- Risk scores are automatically calculated via ML service

#### ******* Data Security and Privacy

**Security Measures:**
- Password hashing using bcrypt with salt rounds
- Sensitive data encryption at rest
- API key encryption for external services
- Audit trail for all data modifications
- Soft delete implementation to maintain data integrity

**Privacy Compliance:**
- Customer consent tracking for communications
- Data retention policies with automatic cleanup
- Personal data anonymization capabilities
- GDPR-compliant data export functionality

#### ******* Scalability Considerations

**Horizontal Scaling:**
- Shop-based data partitioning for multi-tenancy
- Read replicas for analytics and reporting
- Caching layer for frequently accessed data
- Background job processing for heavy operations

**Performance Optimization:**
- Aggregation pipelines for complex analytics
- Materialized views for dashboard metrics
- Connection pooling for database efficiency
- Query optimization with explain plans

This comprehensive database design supports the full functionality of the DeynCare system while maintaining flexibility for future enhancements and ensuring optimal performance for Somali SME operations.

## 4.8 Conclusion

This chapter has presented a comprehensive analysis and design of the DeynCare debt management system, specifically tailored for Somali small and medium enterprises. The system analysis revealed significant challenges in traditional manual debt tracking methods, including lack of systematic record-keeping, inefficient payment reminders, absence of risk assessment, and limited business insights.

The proposed DeynCare solution addresses these challenges through a multi-platform architecture comprising a Node.js backend API, Next.js web dashboard, Flutter mobile application, and FastAPI machine learning service. The system's design incorporates several key innovations:

**Technical Innovations:**
- AI-powered risk assessment using machine learning algorithms to predict customer payment behavior
- Multi-channel communication system supporting SMS, email, and push notifications
- Offline-first mobile application design for reliable operation in areas with limited connectivity
- Real-time synchronization between web and mobile platforms
- Integration with local payment systems (EVC/WaafiPay) popular in Somalia

**Business Process Improvements:**
- Automated debt tracking and payment reminder systems
- Comprehensive customer profiling with risk scoring

- Advanced analytics and reporting for business insights
- Flexible subscription-based pricing model

**System Architecture Strengths:**
- Microservices architecture ensuring scalability and maintainability
- MongoDB document database providing flexibility for evolving business requirements
- Role-based access control supporting different user types (SuperAdmin, Admin, Employee)
- Comprehensive audit trail and logging for compliance and security
- Multi-tenant design supporting thousands of concurrent shops

The feasibility study confirmed the technical, operational, and economic viability of the system. The 18-month development timeline is realistic, the technology stack is proven and well-supported, and the subscription-based revenue model ensures sustainable operations while remaining affordable for SMEs.

The detailed system design, including use case diagrams, class diagrams, sequence diagrams, and data flow diagrams, provides a clear blueprint for implementation. The database design with 20+ collections supports complex business operations while maintaining data integrity and performance through strategic indexing and relationship management.

The DeynCare system represents a significant advancement in debt management solutions for developing markets, combining modern technology with deep understanding of local business practices and constraints. By addressing the specific needs of Somali SMEs—including support for local languages, currencies, payment methods, and business customs—the system is positioned to make a meaningful impact on business efficiency and financial management in the region.

The next phase of this research will focus on system implementation, testing, and deployment, followed by user acceptance testing and performance evaluation in real-world business environments. The comprehensive design presented in this chapter provides a solid foundation for successful system development and deployment.

---

**Chapter 4 Summary:**
- Analyzed existing debt management challenges in Somali SMEs
- Designed a comprehensive AI-powered solution with multi-platform architecture
- Established technical and business feasibility through detailed analysis
- Created complete system design with UML diagrams and database schema
- Provided implementation roadmap for 18-month development timeline

The DeynCare system design successfully addresses the research objectives of creating an efficient, scalable, and culturally appropriate debt management solution for Somali small and medium enterprises. 