/**
 * Fetch Security Settings Service
 * 
 * Retrieves security-related system settings
 */
import settingsAPI from '../../api/modules/settings';
import { handleError } from '../baseService';

/**
 * Fetch security settings
 * @returns {Object} Security settings data
 */
const fetchSecuritySettings = async () => {
  try {
    const response = await settingsAPI.getSecuritySettings();
    // Map API response to frontend structure or use defaults
    const settings = response.data?.data || [];
    
    // Initialize default structure
    const formattedSettings = {
      passwordPolicy: {
        minLength: 8,
        requireUppercase: true,
        requireLowercase: true,
        requireNumbers: true,
        requireSpecialChars: true,
        passwordExpiryDays: 90
      },
      sessionSettings: {
        sessionTimeout: 30,
        maxLoginAttempts: 5,
        lockoutDuration: 15
      },
      securityHeaders: {
        enableCSP: true,
        enableHSTS: true,
        enableXFrameOptions: true,
        enableXSSProtection: true
      }
    };

    // Map backend settings to frontend format
    if (Array.isArray(settings)) {
      settings.forEach(setting => {
        // Map password policy settings
        if (setting.key.startsWith('password.')) {
          const fieldName = setting.key.split('.')[1];
          if (fieldName === 'minLength') {
            formattedSettings.passwordPolicy.minLength = parseInt(setting.value) || 8;
          } else if (fieldName === 'passwordExpiryDays') {
            formattedSettings.passwordPolicy.passwordExpiryDays = parseInt(setting.value) || 90;
          } else if (['requireUppercase', 'requireLowercase', 'requireNumbers', 'requireSpecialChars'].includes(fieldName)) {
            formattedSettings.passwordPolicy[fieldName] = setting.value !== false;
          }
        }
        
        // Map session settings
        else if (setting.key === 'session.timeout') {
          formattedSettings.sessionSettings.sessionTimeout = parseInt(setting.value) || 30;
        } else if (setting.key === 'login.maxAttempts') {
          formattedSettings.sessionSettings.maxLoginAttempts = parseInt(setting.value) || 5;
        } else if (setting.key === 'login.lockoutDuration') {
          formattedSettings.sessionSettings.lockoutDuration = parseInt(setting.value) || 15;
        }
        
        // Map security header settings
        else if (setting.key.startsWith('headers.')) {
          const headerName = setting.key.split('.')[1];
          if (['enableCSP', 'enableHSTS', 'enableXFrameOptions', 'enableXSSProtection'].includes(headerName)) {
            formattedSettings.securityHeaders[headerName] = setting.value !== false;
          }
        }
      });
    }
    
    return formattedSettings;
  } catch (error) {
    handleError(error, 'SettingsService.fetchSecuritySettings', true);
    return {};
  }
};

export default fetchSecuritySettings;
