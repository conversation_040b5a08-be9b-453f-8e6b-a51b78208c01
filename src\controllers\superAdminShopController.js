const mongoose = require('mongoose');
const { Shop, User, Plan } = require('../models');
const { AppError, TransactionHelper, idGenerator } = require('../utils');
const { generateSecurePassword } = require('../utils/generators/passwordGenerator');
const ShopService = require('../services/shopService');
const UserService = require('../services/userService');
const FileUploadService = require('../services/fileUploadService');
const BaseEmailService = require('../services/email/baseEmailService');
const { logSuccess, logError } = require('../utils/logger');

/**
 * Simplified SuperAdmin Shop CRUD Operations
 * Clean, straightforward operations without complex logic
 */

/**
 * CREATE - Create shop with automatic activation
 * POST /api/admin/shops
 */
const createShop = async (req, res, next) => {
  try {
    console.log('🔍 [SuperAdminController] Received request body:', JSON.stringify(req.body, null, 2));
    console.log('🔍 [SuperAdminController] Received file:', req.file ? { name: req.file.originalname, size: req.file.size } : 'No file');

    // Ensure default plans exist before proceeding
    const planCount = await Plan.countDocuments({ isActive: true, isDeleted: { $ne: true } });
    if (planCount === 0) {
      console.log('🔧 [SuperAdminController] No active plans found, creating default plans...');
      await Plan.createDefaultPlans();
      console.log('✅ [SuperAdminController] Default plans created');
    }
    
    const {
      // User data (SAME field names as public registration)
      fullName,
      email,
      phone,
      // Shop data (SAME field names as public registration)
      shopName,
      shopAddress,
      // Business details (OPTIONAL)
      businessDetails = {},
      // Plan type from API
      planType
    } = req.body;

    // Parse businessDetails if it comes as JSON string (from FormData)
    let parsedBusinessDetails = businessDetails;
    if (typeof businessDetails === 'string') {
      try {
        parsedBusinessDetails = JSON.parse(businessDetails);
        console.log('🔍 [SuperAdminController] Parsed businessDetails from JSON string:', parsedBusinessDetails);
      } catch (parseError) {
        console.error('❌ [SuperAdminController] Failed to parse businessDetails JSON:', parseError);
        parsedBusinessDetails = {};
      }
    }

    console.log('🔍 [SuperAdminController] Extracted data:', {
      fullName,
      email,
      phone,
      shopName,
      shopAddress,
      businessDetails: parsedBusinessDetails,
      planType
    });

    // Validate required fields
    if (!planType) {
      console.error('❌ [SuperAdminController] Missing planType');
      return next(new AppError('Plan type is required', 400, 'missing_plan'));
    }

    // 🔍 VALIDATE PLAN: Fetch actual plan details from database
    console.log(`🔍 [SuperAdminController] Looking for plan with identifier: ${planType}`);

    // Build query conditions - check planId, name, type, and _id
    const queryConditions = [
      { planId: planType },
      { name: planType },
      { type: planType }
    ];

    // Only add _id condition if planType looks like a valid ObjectId (24 hex characters)
    if (mongoose.Types.ObjectId.isValid(planType) && planType.length === 24) {
      queryConditions.push({ _id: planType });
    }

    const selectedPlan = await Plan.findOne({
      $or: queryConditions,
      isActive: true,
      isDeleted: { $ne: true }
    });

    if (!selectedPlan) {
      // Provide detailed error information for debugging
      const allPlans = await Plan.find({ isActive: true, isDeleted: { $ne: true } })
        .select('planId name type displayName')
        .lean();

      console.error('❌ [SuperAdminController] Plan not found:', {
        searchedFor: planType,
        availablePlans: allPlans.map(p => ({
          planId: p.planId,
          name: p.name,
          type: p.type,
          displayName: p.displayName
        }))
      });

      return next(new AppError(
        `Invalid plan selected: ${planType}. Available plans: ${allPlans.map(p => p.planId).join(', ')}`,
        400,
        'invalid_plan'
      ));
    }

    console.log(`📋 Selected plan: ${selectedPlan.displayName} ($${selectedPlan.pricing.basePrice}/${selectedPlan.pricing.billingCycle})`);

    // 🚨 DUPLICATION PREVENTION: Check for existing shop by email
    const existingShop = await Shop.findOne({ 
      email: email.toLowerCase(),
      isDeleted: { $ne: true }
    });

    if (existingShop) {
      return next(new AppError(`Shop with email ${email} already exists (ID: ${existingShop.shopId})`, 409, 'shop_exists'));
    }

    console.log(`🆕 Creating new shop: ${shopName} for ${email}`);

    // 🔐 AUTO-GENERATE SECURE PASSWORD for shop owner
    const generatedPassword = generateSecurePassword(12, false, true, true, true); // 12 chars, no special chars for easier sharing
    console.log(`🔑 Generated secure password for ${email}`);

    // Handle logo upload if provided
    let logoUrl = '';
    if (req.file) {
      try {
        const logoData = await FileUploadService.saveShopLogo(req.file);
        logoUrl = logoData.url;
      } catch (logoError) {
        // Don't fail shop creation if logo upload fails, just log the error
        console.error('Logo upload failed:', logoError.message);
      }
    }

    // SuperAdmin shop creation - Include plan details for payment processing
    const shopData = {
      shopName,
      ownerName: fullName,        // Map fullName to ownerName for shop model
      email,
      phone,
      address: shopAddress,       // Map shopAddress to address for shop model
      // Process business details with validation
      businessDetails: {
        type: parsedBusinessDetails.type || 'retail',
        category: parsedBusinessDetails.category || 'general_store',
        foundedDate: parsedBusinessDetails.foundedDate || null,
        registrationNumber: parsedBusinessDetails.registrationNumber || '',
        taxId: parsedBusinessDetails.taxId || '',
        employeeCount: parsedBusinessDetails.employeeCount || 1
      },
      logoUrl,                    // Include logo URL
      status: 'active',           // SuperAdmin shops are immediately active
      verified: true,             // SuperAdmin shops are auto-verified
      registeredBy: 'superAdmin',
      // Include plan information for payment processing
      subscription: {
        planId: selectedPlan.planId,
        planName: selectedPlan.displayName,
        planType: selectedPlan.type,
        pricing: {
          amount: selectedPlan.pricing.basePrice,
          currency: selectedPlan.pricing.currency,
          billingCycle: selectedPlan.pricing.billingCycle
        },
        status: 'active',         // SuperAdmin shops are immediately active
        startDate: new Date(),
        paidUntil: new Date(Date.now() + (selectedPlan.pricing.billingCycle === 'yearly' ? 365 : 30) * 24 * 60 * 60 * 1000)
      },
      access: {
        isPaid: true,             // SuperAdmin shops are considered paid
        isActivated: true         // SuperAdmin shops are immediately activated
      }
    };

    // Use transaction to create both shop and user (BYPASS ShopService validation for SuperAdmin)
    const result = await TransactionHelper.withTransaction(async (session) => {
      // Create shop directly using model to bypass service validation for SuperAdmin
      // Generate shop ID
      const shopId = await idGenerator.generateShopId(Shop);
      
      // Create shop directly with SuperAdmin privileges
      const shop = new Shop({
        ...shopData,
        shopId,
        createdAt: new Date(),
        updatedAt: new Date()
      });
      
      await shop.save({ session });

      // Create corresponding subscription record using the service
      try {
        const subscriptionData = {
          shopId,
          planType: selectedPlan.type,
          planId: selectedPlan.planId,
          planName: selectedPlan.displayName || selectedPlan.name,
          pricing: {
            basePrice: selectedPlan.pricing.basePrice,
            currency: selectedPlan.pricing.currency,
            billingCycle: selectedPlan.pricing.billingCycle
          },
          paymentMethod: 'offline', // SuperAdmin shops use offline payment
          session
        };

        const createSubscription = require('../services/Subscription/createSubscription');
        await createSubscription(subscriptionData, { 
          actorId: req.user.userId || 'superAdmin',
          actorRole: 'superAdmin'
        });
        logSuccess(`Subscription record created for shop: ${shopId}`, 'SuperAdminShopController');
      } catch (subscriptionError) {
        // Log error but don't fail shop creation
        logError(`Failed to create subscription record for shop ${shopId}: ${subscriptionError.message}`, 'SuperAdminShopController', subscriptionError);
      }

      // Create user for the shop (SAME as public registration)
      const userData = await UserService.createUser({
        fullName,
        email,
        phone,
        password: generatedPassword,    // Use auto-generated password
        role: 'admin',                  // Shop owner is admin
        shopId: shopId,                 // Use the generated shopId
        registeredBy: 'superAdmin',
        status: 'active',               // SuperAdmin created users are immediately active
        verified: true,                 // SuperAdmin created users are pre-verified
        emailVerified: true,            // SuperAdmin created users don't need email verification
        isPaid: true,                   // SuperAdmin created users are pre-paid
        isActivated: true               // SuperAdmin created users are immediately activated
      }, { session });

      // Update shop with owner reference
      shop.ownerId = userData.userId;
      await shop.save({ session });

      return { shop, userData, generatedPassword, selectedPlan };
    }, { name: 'SuperAdminCreateShop' });

    // 📧 SEND WELCOME EMAIL with login credentials
    try {
      const emailService = new BaseEmailService();
      await emailService.initialize();
      
      const emailData = {
        fullName: result.userData.fullName,
        shopName: result.shop.shopName,
        shopId: result.shop.shopId,
        email: result.userData.email,
        password: result.generatedPassword,  // Include generated password in email
        loginUrl: process.env.FRONTEND_URL || 'https://deyncare.cajiibcreative.com',
        planType: result.selectedPlan.displayName
      };

      const htmlContent = emailService.renderTemplate('Shop/shop-activation', emailData);
      await emailService.sendMail(
        result.userData.email,
        'Welcome to DeynCare - Your Shop is Ready!',
        htmlContent
      );
      
      console.log(`✅ Welcome email sent to ${result.userData.email}`);
    } catch (emailError) {
      // Don't fail shop creation if email sending fails, just log the error
      console.error('Failed to send welcome email:', emailError.message);
    }

    // Create safe response object (WITHOUT including the generated password for security)
    const responseData = {
      shop: {
        shopId: result.shop.shopId,
        shopName: result.shop.shopName,
        ownerName: result.shop.ownerName,
        email: result.shop.email,
        phone: result.shop.phone,
        address: result.shop.address,
        logoUrl: result.shop.logoUrl || '',
        status: result.shop.status,
        subscription: result.shop.subscription,
        verified: result.shop.verified,
        registeredBy: result.shop.registeredBy
      },
      owner: {
        userId: result.userData.userId,
        fullName: result.userData.fullName,
        email: result.userData.email,
        role: result.userData.role
      },
      plan: {
        planId: result.selectedPlan.planId,
        name: result.selectedPlan.displayName,
        type: result.selectedPlan.type,
        pricing: result.selectedPlan.pricing
      },
      emailSent: true // Indicate that welcome email was attempted
    };

    res.status(201).json({
      success: true,
      message: 'Shop and owner created successfully. Welcome email sent with login credentials.',
      data: responseData
    });
  } catch (error) {
    next(error);
  }
};

/**
 * READ - Get all shops with pagination
 * GET /api/admin/shops
 */
const getAllShops = async (req, res, next) => {
  try {
    const {
      page = 1,
      limit = 20,
      status,
      search
    } = req.query;

    const query = {
      isDeleted: { $ne: true } // Exclude soft-deleted shops
    };
    
    if (status) query.status = status;
    if (search) {
      query.$or = [
        { shopName: { $regex: search, $options: 'i' } },
        { ownerName: { $regex: search, $options: 'i' } },
        { email: { $regex: search, $options: 'i' } }
      ];
    }

    const shops = await Shop.find(query)
      .sort({ createdAt: -1 })
      .limit(limit * 1)
      .skip((page - 1) * limit)
      .lean();

    const total = await Shop.countDocuments(query);

    // Get subscription data for each shop
    const getCurrentSubscriptionByShop = require('../services/Subscription/getCurrentSubscriptionByShop');
    
    const shopsWithSubscriptions = await Promise.all(
      shops.map(async (shop) => {
        try {
          const subscription = await getCurrentSubscriptionByShop(shop.shopId);
          return {
            ...shop,
            subscription: subscription ? {
              subscriptionId: subscription.subscriptionId,
              planId: subscription.planId,
              plan: subscription.plan?.name || subscription.plan,
              planType: subscription.plan?.type || 'unknown',
              status: subscription.status,
              pricing: subscription.pricing,
              startDate: subscription.dates?.startDate,
              endDate: subscription.dates?.endDate,
              expiresAt: subscription.dates?.endDate,
              daysLeft: subscription.dates?.endDate ? Math.max(0, Math.ceil((new Date(subscription.dates.endDate) - new Date()) / (1000 * 60 * 60 * 24))) : 0,
              isActive: ['active', 'trial'].includes(subscription.status)
            } : {
              plan: 'No plan',
              status: 'no_plan',
              expiresAt: null,
              daysLeft: 0,
              isActive: false
            }
          };
        } catch (error) {
          console.error(`Error fetching subscription for shop ${shop.shopId}:`, error);
          return {
            ...shop,
            subscription: null
          };
        }
      })
    );

    res.json({
      success: true,
      data: {
        shops: shopsWithSubscriptions,
        total,
        currentPage: Number(page),
        totalPages: Math.ceil(total / limit),
        pageSize: Number(limit),
        hasNextPage: Number(page) < Math.ceil(total / limit),
        hasPrevPage: Number(page) > 1,
        // Legacy pagination format for backward compatibility
        pagination: {
          total,
          page: Number(page),
          limit: Number(limit),
          pages: Math.ceil(total / limit)
        }
      }
    });
  } catch (error) {
    next(error);
  }
};

/**
 * READ - Get shop by ID
 * GET /api/admin/shops/:shopId
 */
const getShopById = async (req, res, next) => {
  try {
    const { shopId } = req.params;
    
    const shop = await Shop.findOne({ 
      shopId,
      isDeleted: { $ne: true } // Exclude soft-deleted shops
    }).lean();
    
    if (!shop) {
      return next(new AppError('Shop not found', 404));
    }

    // Get subscription data for the shop
    const getCurrentSubscriptionByShop = require('../services/Subscription/getCurrentSubscriptionByShop');
    
    try {
      const subscription = await getCurrentSubscriptionByShop(shop.shopId);
      const shopWithSubscription = {
        ...shop,
        subscription: subscription ? {
          subscriptionId: subscription.subscriptionId,
          planId: subscription.planId,
          plan: subscription.plan?.name || subscription.plan,
          planType: subscription.plan?.type || 'unknown',
          status: subscription.status,
          pricing: subscription.pricing,
          startDate: subscription.dates?.startDate,
          endDate: subscription.dates?.endDate,
          expiresAt: subscription.dates?.endDate,
          daysLeft: subscription.dates?.endDate ? Math.max(0, Math.ceil((new Date(subscription.dates.endDate) - new Date()) / (1000 * 60 * 60 * 24))) : 0,
          isActive: ['active', 'trial'].includes(subscription.status)
        } : {
          plan: 'No plan',
          status: 'no_plan',
          expiresAt: null,
          daysLeft: 0,
          isActive: false
        }
      };

      res.json({
        success: true,
        data: shopWithSubscription
      });
    } catch (subscriptionError) {
      console.error(`Error fetching subscription for shop ${shop.shopId}:`, subscriptionError);
      // Return shop data without subscription if subscription fetch fails
      res.json({
        success: true,
        data: {
          ...shop,
          subscription: null
        }
      });
    }
  } catch (error) {
    next(error);
  }
};

/**
 * UPDATE - Update shop information
 * PUT /api/admin/shops/:shopId
 */
const updateShop = async (req, res, next) => {
  try {
    const { shopId } = req.params;
    const {
      // User data (SAME field names as public registration)
      fullName,
      email,
      phone,
      // Shop data (SAME field names as public registration)
      shopName,
      shopAddress,
      // Other fields
      status,
      verified
    } = req.body;

    // Map public registration field names to shop model field names
    const updateData = {};
    if (fullName !== undefined) updateData.ownerName = fullName;
    if (email !== undefined) updateData.email = email;
    if (phone !== undefined) updateData.phone = phone;
    if (shopName !== undefined) updateData.shopName = shopName;
    if (shopAddress !== undefined) updateData.address = shopAddress;
    if (status !== undefined) updateData.status = status;
    if (verified !== undefined) updateData.verified = verified;

    // Add update timestamp
    updateData.updatedAt = new Date();

    const shop = await Shop.findOneAndUpdate(
      { shopId },
      { $set: updateData },
      { new: true, runValidators: true }
    );

    if (!shop) {
      return next(new AppError('Shop not found', 404));
    }

    res.json({
      success: true,
      message: 'Shop updated successfully',
      data: shop
    });
  } catch (error) {
    next(error);
  }
};

/**
 * UPDATE - Change shop status with cascading user status changes
 * PUT /api/admin/shops/:shopId/status
 */
const changeShopStatus = async (req, res, next) => {
  try {
    const { shopId } = req.params;
    const { status, reason } = req.body;

    // Start transaction for atomic operation
    const session = await mongoose.startSession();
    
    try {
      let shop, affectedUsers;
      await session.withTransaction(async () => {
        // 1. Update shop status
        shop = await Shop.findOneAndUpdate(
          { shopId },
          { 
            $set: { 
              status,
              statusReason: reason,
              statusChangedAt: new Date(),
              statusChangedBy: req.user.userId
            }
          },
          { new: true, session }
        );

        if (!shop) {
          throw new AppError('Shop not found', 404);
        }

        // 2. CASCADE: Update user statuses based on shop status
        let userStatusUpdate = {};
        let cascadeMessage = '';

        if (status === 'suspended' || status === 'inactive') {
          // Shop suspended/inactive → Deactivate users
          userStatusUpdate = {
            status: 'inactive',
            suspensionReason: `Shop ${status}: ${reason || 'No reason provided'}`,
            suspendedAt: new Date(),
            suspendedBy: req.user.userId
          };
          cascadeMessage = `${status} → users deactivated`;
        } else if (status === 'active') {
          // Shop reactivated → Reactivate users (only those suspended due to shop)
          userStatusUpdate = {
            status: 'active',
            suspensionReason: null,
            suspendedAt: null,
            suspendedBy: null,
            reactivatedAt: new Date(),
            reactivatedBy: req.user.userId
          };
          cascadeMessage = 'reactivated → users reactivated';
        }

        if (Object.keys(userStatusUpdate).length > 0) {
          const cascadeResult = await User.updateMany(
            { 
              shopId: shopId,
              isDeleted: { $ne: true }, // Only affect non-deleted users
              role: { $ne: 'superAdmin' } // Don't affect superAdmins
            },
            { $set: userStatusUpdate },
            { session }
          );

          console.log(`🔄 CASCADE: Shop ${shopId} ${cascadeMessage} → ${cascadeResult.modifiedCount} users affected`);
        }

        // Get affected users for email notifications
        affectedUsers = await User.find({
          shopId: shopId,
          isDeleted: { $ne: true },
          role: { $ne: 'superAdmin' }
        }).select('email fullName').session(session);
      });

      await session.endSession();

      // 📧 SEND EMAIL NOTIFICATIONS after successful transaction
      try {
        const AdminEmailService = require('../services/email/adminEmailService');
        
        if (status === 'suspended' && affectedUsers.length > 0) {
          // Send suspension emails to all affected users
          for (const user of affectedUsers) {
            try {
              await AdminEmailService.sendAccountSuspensionEmail(user.email, {
                name: user.fullName,
                reason: reason || 'Account suspended by administrator',
                contactEmail: '<EMAIL>'
              });
              console.log(`✅ Suspension email sent to ${user.email}`);
            } catch (emailError) {
              console.error(`❌ Failed to send suspension email to ${user.email}:`, emailError.message);
            }
          }
        } else if (status === 'active' && affectedUsers.length > 0) {
          // Send reactivation emails to all affected users
          for (const user of affectedUsers) {
            try {
              await AdminEmailService.sendAccountReactivationEmail(user.email, {
                name: user.fullName,
                contactEmail: '<EMAIL>'
              });
              console.log(`✅ Reactivation email sent to ${user.email}`);
            } catch (emailError) {
              console.error(`❌ Failed to send reactivation email to ${user.email}:`, emailError.message);
            }
          }
        }
      } catch (emailError) {
        // Don't fail the status change if email sending fails
        console.error('Failed to send status change emails:', emailError.message);
      }

      res.json({
        success: true,
        message: `Shop ${status} successfully with cascading user updates and email notifications`,
        data: { 
          shopId, 
          status, 
          updatedAt: new Date(),
          emailsSent: affectedUsers.length 
        }
      });
    } catch (error) {
      await session.endSession();
      throw error;
    }
  } catch (error) {
    next(error);
  }
};

/**
 * DELETE - Soft delete shop with cascading user deactivation
 * DELETE /api/admin/shops/:shopId
 */
const deleteShop = async (req, res, next) => {
  try {
    const { shopId } = req.params;
    const { reason } = req.body;

    console.log(`🗑️ Starting shop deletion: ${shopId}`);

    // Check if shop exists and is not already deleted
    const existingShop = await Shop.findOne({ shopId, isDeleted: { $ne: true } });
    if (!existingShop) {
      return next(new AppError('Shop not found or already deleted', 404));
    }

    console.log(`📋 Found shop to delete: ${existingShop.shopName} (ID: ${shopId})`);

    // Start transaction for atomic operation
    const session = await mongoose.startSession();
    
    try {
      let updatedShop;
      await session.withTransaction(async () => {
        // 1. Soft delete the shop (NOT creating a new one)
        updatedShop = await Shop.findOneAndUpdate(
          { 
            shopId,
            isDeleted: { $ne: true } // Only delete if not already deleted
          },
          { 
            $set: { 
              isDeleted: true,
              deletedAt: new Date(),
              deletedBy: req.user.userId,
              deletionReason: reason || 'No reason provided',
              status: 'deleted' // ✅ CONSISTENCY FIX: Always set status to deleted
            }
          },
          { 
            new: true, 
            session,
            upsert: false, // IMPORTANT: Don't create if not found
            runValidators: false // Skip validation for deletion
          }
        );

        if (!updatedShop) {
          throw new AppError('Shop not found or already deleted', 404);
        }

        console.log(`✅ Shop soft deleted: ${updatedShop.shopName} (isDeleted: ${updatedShop.isDeleted})`);

        // 2. CASCADE: Deactivate all users linked to this shop
        const User = require('../models/user.model');
        const cascadeResult = await User.updateMany(
          { 
            shopId: shopId,
            isDeleted: { $ne: true }, // Only affect non-deleted users
            role: { $ne: 'superAdmin' } // Don't affect superAdmins
          },
          { 
            $set: { 
              status: 'inactive',
              isDeleted: true,
              deletedAt: new Date(),
              deletedBy: req.user.userId,
              deletionReason: `Shop deleted: ${reason || 'No reason provided'}`
            }
          },
          { session }
        );

        console.log(`🔄 CASCADE: Shop ${shopId} deleted → ${cascadeResult.modifiedCount} users deactivated`);
      });

      await session.endSession();

      res.json({
        success: true,
        message: 'Shop and associated users deleted successfully',
        data: { 
          shopId: updatedShop.shopId,
          shopName: updatedShop.shopName,
          deletedAt: updatedShop.deletedAt,
          isDeleted: updatedShop.isDeleted
        }
      });
    } catch (error) {
      await session.endSession();
      throw error;
    }
  } catch (error) {
    console.error(`❌ Shop deletion error:`, error);
    next(error);
  }
};

/**
 * UPDATE - Update shop logo
 * PUT /api/admin/shops/:shopId/logo
 */
const updateShopLogo = async (req, res, next) => {
  try {
    const { shopId } = req.params;

    if (!req.file) {
      return next(new AppError('No logo file provided', 400));
    }

    // Upload the new logo
    const logoData = await FileUploadService.saveShopLogo(req.file, shopId);

    // Update shop with new logo URL
    const shop = await Shop.findOneAndUpdate(
      { shopId },
      { 
        $set: { 
          logoUrl: logoData.url,
          updatedAt: new Date()
        }
      },
      { new: true }
    );

    if (!shop) {
      return next(new AppError('Shop not found', 404));
    }

    res.json({
      success: true,
      message: 'Shop logo updated successfully',
      data: {
        shopId: shop.shopId,
        logoUrl: shop.logoUrl,
        updatedAt: shop.updatedAt
      }
    });
  } catch (error) {
    next(error);
  }
};

/**
 * UTILITY - Get shop statistics
 * GET /api/admin/shops/stats
 */
const getShopStats = async (req, res, next) => {
  try {
    const stats = await Shop.aggregate([
      {
        $match: {
          isDeleted: { $ne: true } // Exclude soft-deleted shops
        }
      },
      {
        $group: {
          _id: null,
          totalShops: { $sum: 1 },
          activeShops: { $sum: { $cond: [{ $eq: ['$status', 'active'] }, 1, 0] } },
          pendingShops: { $sum: { $cond: [{ $eq: ['$status', 'pending'] }, 1, 0] } },
          suspendedShops: { $sum: { $cond: [{ $eq: ['$status', 'suspended'] }, 1, 0] } },
          inactiveShops: { $sum: { $cond: [{ $eq: ['$status', 'inactive'] }, 1, 0] } }
        }
      }
    ]);

    const result = stats[0] || {
      totalShops: 0,
      activeShops: 0,
      pendingShops: 0,
      suspendedShops: 0,
      inactiveShops: 0
    };

    res.json({
      success: true,
      data: result
    });
  } catch (error) {
    next(error);
  }
};

module.exports = {
  createShop,
  getAllShops,
  getShopById,
  updateShop,
  updateShopLogo,
  changeShopStatus,
  deleteShop,
  getShopStats
}; 