/**
 * Core Feature Export Controller
 * Handles export for 4 core features only: User, Shop, Plan, Subscription Management
 * SuperAdmin can choose what core feature to export and get only CSV file
 */
const { logInfo, logError, AppError } = require('../../utils');
const userExportController = require('./userExportController');
const shopExportController = require('./shopExportController');
const planExportController = require('./planExportController');
const subscriptionExportController = require('./subscriptionExportController');

/**
 * Export core feature data based on SuperAdmin choice
 * GET /api/export/core-feature
 * @param {string} feature - Core feature type: 'users', 'shops', 'plans', 'subscriptions'
 * @param {string} format - Export format: 'csv' (only CSV supported for simplicity)
 */
const exportCoreFeature = async (req, res, next) => {
  try {
    const { feature } = req.query;
    
    // Validate core feature selection
    const validFeatures = ['users', 'shops', 'plans', 'subscriptions'];
    if (!feature || !validFeatures.includes(feature)) {
      throw new AppError(
        `Invalid feature. Must be one of: ${validFeatures.join(', ')}`,
        400,
        'invalid_feature'
      );
    }

    // Force CSV format for simplicity
    req.query.format = 'csv';
    
    logInfo(`SuperAdmin exporting core feature: ${feature}`, 'CoreFeatureExportController');

    // Route to appropriate export controller based on feature
    switch (feature) {
      case 'users':
        return await userExportController.exportToCSV(req, res, next);
      
      case 'shops':
        return await shopExportController.exportToCSV(req, res, next);
      
      case 'plans':
        return await planExportController.exportToCSV(req, res, next);
      
      case 'subscriptions':
        return await subscriptionExportController.exportToCSV(req, res, next);
      
      default:
        throw new AppError('Unsupported core feature', 400, 'unsupported_feature');
    }
  } catch (error) {
    logError(`Failed to export core feature: ${error.message}`, 'CoreFeatureExportController', error);
    next(error);
  }
};

/**
 * Get available core features for export
 * GET /api/export/core-features
 */
const getAvailableCoreFeatures = async (req, res, next) => {
  try {
    const coreFeatures = [
      {
        id: 'users',
        name: 'User Management',
        description: 'Export all user accounts, roles, and activity data',
        icon: 'Users',
        endpoint: '/api/export/core-feature?feature=users&format=csv'
      },
      {
        id: 'shops',
        name: 'Shop Management', 
        description: 'Export all shop information, status, and configurations',
        icon: 'Store',
        endpoint: '/api/export/core-feature?feature=shops&format=csv'
      },
      {
        id: 'plans',
        name: 'Plan Management',
        description: 'Export all subscription plans, pricing, and features',
        icon: 'Package',
        endpoint: '/api/export/core-feature?feature=plans&format=csv'
      },
      {
        id: 'subscriptions',
        name: 'Subscription Management',
        description: 'Export all subscription data, payments, and status',
        icon: 'CreditCard',
        endpoint: '/api/export/core-feature?feature=subscriptions&format=csv'
      }
    ];

    res.json({
      success: true,
      message: 'Available core features for export',
      data: {
        coreFeatures,
        totalFeatures: coreFeatures.length,
        supportedFormat: 'csv',
        accessLevel: 'superAdmin'
      }
    });
  } catch (error) {
    logError(`Failed to get core features: ${error.message}`, 'CoreFeatureExportController', error);
    next(error);
  }
};

/**
 * Get core feature export statistics
 * GET /api/export/core-features/stats
 */
const getCoreFeatureStats = async (req, res, next) => {
  try {
    const { User, Shop, Plan, Subscription } = require('../../models');

    // Get counts for each core feature
    const [usersCount, shopsCount, plansCount, subscriptionsCount] = await Promise.all([
      User.countDocuments(),
      Shop.countDocuments(), 
      Plan.countDocuments(),
      Subscription.countDocuments()
    ]);

    const stats = {
      users: {
        name: 'User Management',
        count: usersCount,
        exportable: usersCount > 0
      },
      shops: {
        name: 'Shop Management',
        count: shopsCount,
        exportable: shopsCount > 0
      },
      plans: {
        name: 'Plan Management', 
        count: plansCount,
        exportable: plansCount > 0
      },
      subscriptions: {
        name: 'Subscription Management',
        count: subscriptionsCount,
        exportable: subscriptionsCount > 0
      }
    };

    res.json({
      success: true,
      message: 'Core feature export statistics',
      data: {
        stats,
        totalRecords: usersCount + shopsCount + plansCount + subscriptionsCount,
        lastUpdated: new Date().toISOString()
      }
    });
  } catch (error) {
    logError(`Failed to get core feature stats: ${error.message}`, 'CoreFeatureExportController', error);
    next(error);
  }
};

module.exports = {
  exportCoreFeature,
  getAvailableCoreFeatures,
  getCoreFeatureStats
}; 