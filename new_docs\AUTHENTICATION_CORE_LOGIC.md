# Authentication Core Logic Modules in DeynCare Mobile App

This document outlines the core logic modules involved in the authentication process within the DeynCare mobile application, based on the analyzed files. The architecture follows a layered approach, separating concerns for better maintainability and scalability.

## 1. Data Layer

This layer is responsible for interacting with external data sources, such as the backend API and local secure storage.

*   **`auth_remote_source.dart`**: This is the primary component for direct API calls related to authentication. It handles requests for login, registration, password reset, email verification, and token refreshing. It also includes response validation.
*   **`auth_utils.dart`**: Manages secure storage of user data and other authentication-related utilities locally on the device (e.g., using `flutter_secure_storage`). It handles serialization and deserialization of user data.
*   **`auth_deep_link_handler.dart`**: Specializes in handling incoming deep links related to authentication flows, such as password reset links or email verification links, navigating the user to the appropriate screen within the app.
*   **`auth_token_model.dart` & `user_model.dart`**: These are data models that represent the structure of authentication tokens and user information as received from the API or stored locally. They include `from<PERSON>son` and `to<PERSON>son` methods for data conversion.
*   **`api_client.dart`**: A generalized HTTP client that provides a wrapper around Dio, incorporating common functionalities like token management, connectivity checks, and request retries.
*   **`auth_interceptor.dart`**: A Dio interceptor responsible for adding authentication tokens to outgoing requests and handling token refresh logic when an access token expires (401 error).
*   **`error_interceptor.dart`**: Another Dio interceptor that catches common network and server errors, transforming them into more user-friendly `ApiException` messages.
*   **`error_model.dart`**: Defines the structure for error responses from the API.

## 2. Domain Layer

This layer contains the business logic and entities that are independent of how data is stored or presented. It defines what the application *does*.

*   **`auth_repository.dart` (Interface)**: Defines the contract for authentication operations that the domain layer expects from the data layer. This ensures a clear separation of concerns.
*   **`auth_repository_impl.dart`**: This is the concrete implementation of the `AuthRepository` interface. It acts as a bridge between the domain and data layers, orchestrating calls to `AuthRemoteSource` and `AuthUtils` and mapping data models to domain entities (`UserModel` to `User`, `AuthTokenModel` to `AuthToken`). It also includes connectivity awareness.
*   **`auth_token.dart` & `user.dart`**: These are pure Dart classes representing the core domain entities for authentication tokens and users. They contain business rules and methods (e.g., `isExpired` for `AuthToken`, `hasPermission` for `User`).
*   **Use Cases (e.g., `login_use_case.dart`, `register_use_case.dart`, `forgot_password_use_case.dart`, `verify_email_use_case.dart`, `change_password_use_case.dart`, `logout_use_case.dart`, `refresh_token_use_case.dart`, `reset_password_success_use_case.dart`, `check_auth_status_use_case.dart`)**: These classes encapsulate specific business rules and orchestrate interactions with the `AuthRepository` to perform a single, well-defined task (e.g., logging in a user, changing a password). They represent the "actions" that can be performed in the authentication domain.

## 3. Application Service Layer

This layer acts as an orchestration service, bringing together different data layer components and providing a unified interface to the presentation layer.

*   **`auth_service.dart`**: This class coordinates operations from the `AuthRepositoryImpl` and `AuthDeepLinkHandler`. It serves as the primary entry point for the presentation layer to interact with the authentication logic, abstracting away the underlying repository and deep link handling details.

## 4. Presentation Layer (BLoC)

This layer is responsible for managing the UI state and reacting to user interactions.

*   **`auth_bloc.dart`**: A BLoC (Business Logic Component) that manages the authentication state. It receives events from the UI (e.g., `LoggedIn`, `ForgotPasswordRequested`), processes them using the domain layer's use cases, and emits new states (`AuthAuthenticated`, `AuthForgotPasswordSent`) that the UI can react to. It also handles initial app startup authentication checks and session expiry.
*   **`auth_event.dart`**: Defines all possible events that can be dispatched to the `AuthBloc` (e.g., `LoggedIn`, `LoggedOut`, `ForgotPasswordRequested`).
*   **`auth_state.dart`**: Defines all possible states that the `AuthBloc` can emit, representing different authentication scenarios (e.g., `AuthInitial`, `AuthLoading`, `AuthAuthenticated`, `AuthUnauthenticated`, `AuthFailure`).

## Core Logic Flow

The authentication flow generally follows these steps:

1.  **UI Event**: User interacts with the UI, triggering an `AuthEvent` (e.g., `LoggedIn`).
2.  **BLoC Processing**: The `AuthBloc` receives the event and calls the appropriate domain `UseCase` (e.g., `LoginUseCase`).
3.  **Domain Logic**: The `UseCase` orchestrates operations, typically by calling methods on the `AuthRepository` (e.g., `_repository.login()`).
4.  **Data Retrieval/Manipulation**: The `AuthRepositoryImpl` (via `AuthService`) interacts with `AuthRemoteSource` for API calls and `AuthUtils` for local storage. Network interceptors (`AuthInterceptor`, `ErrorInterceptor`) handle token management and error processing during API calls.
5.  **Response Handling**: Data is transformed from models (`AuthTokenModel`, `UserModel`) into domain entities (`AuthToken`, `User`).
6.  **State Update**: The `AuthBloc` receives results from the use case and emits a new `AuthState` based on the outcome (e.g., `AuthAuthenticated` on success, `AuthFailure` on error).
7.  **UI Reaction**: The UI listens to `AuthState` changes and updates accordingly (e.g., navigates to home screen, shows error message).

This layered architecture ensures clear responsibilities, making the authentication module robust and easy to understand.

```mermaid
graph TD;
    A[User Interaction (UI)] --> B[Dispatch AuthEvent];
    B --> C[AuthBloc Processes Event];
    C --> D[Calls UseCase (Domain Layer)];
    D --> E[UseCase Calls AuthRepository];
    E --> F[AuthRepositoryImpl Interacts with];
    F --> G[AuthRemoteSource (API Calls)];
    F --> H[AuthUtils (Local Storage)];
    G -- Intercepted by --> I[AuthInterceptor & ErrorInterceptor];
    H --> J[Data Models (AuthTokenModel, UserModel)];
    I --> J;
    J --> K[Domain Entities (AuthToken, User)];
    K --> C;
    C --> L[AuthBloc Emits AuthState];
    L --> M[UI Reacts to AuthState];
    M --> A;
``` 

graph TD;A[User Interaction] --> B[Dispatch Event];B --> C[AuthBloc];C --> D[Use Case];D --> E[Auth Repository];E --> F[Remote Source];E --> G[Auth Utils];F --> H[Interceptors];H --> I[Data Models];G --> I;I --> J[Domain Entities];J --> C;C --> K[AuthBloc Emits State];K --> L[UI Reacts];L --> A;