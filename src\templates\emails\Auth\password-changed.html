<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Password Changed Successfully</title>
  <style>
    /* Inlined styles for email client compatibility */
    :root {
      --primary-color: #2e86de;
      --secondary-color: #0abde3;
      --accent-color: #10ac84;
      --text-color: #333333;
      --text-muted: #666666;
      --bg-light: #f7f7f7;
      --bg-white: #ffffff;
      --border-color: #eeeeee;
    }
    body {
      font-family: 'Poppins', 'Segoe UI', Arial, sans-serif;
      line-height: 1.6;
      color: #333333;
      margin: 0;
      padding: 0;
      background-color: #f7f7f7;
    }
    .email-container {
      max-width: 600px;
      margin: 20px auto;
      background-color: #ffffff;
      border-radius: 8px;
      overflow: hidden;
      box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
    }
    .email-header {
      background-color: #2e86de;
      background-image: linear-gradient(to right, #2e86de, #0abde3);
      color: white;
      padding: 24px 20px;
      text-align: center;
    }
    .email-header h1 {
      margin: 15px 0 0;
      font-size: 28px;
      font-weight: 600;
    }
    .email-content {
      padding: 32px 24px;
      background-color: #ffffff;
    }
    .email-greeting {
      font-size: 18px;
      margin-bottom: 15px;
      color: #333333;
    }
    .email-message {
      font-size: 16px;
      margin-bottom: 24px;
      color: #333333;
    }
    .btn-container {
      text-align: center;
      margin: 30px 0;
    }
    .btn-primary {
      display: inline-block;
      background-color: #2e86de;
      color: white !important;
      text-decoration: none;
      padding: 12px 30px;
      border-radius: 50px;
      font-weight: 600;
      text-align: center;
      transition: all 0.3s ease;
    }
    .security-alert {
      background-color: #fff8e1;
      border-left: 4px solid #ffc107;
      padding: 15px;
      margin: 25px 0;
      border-radius: 4px;
    }
    .security-alert p {
      margin: 0;
      color: #856404;
    }
    .divider {
      height: 1px;
      background-color: #eeeeee;
      margin: 25px 0;
    }
    .email-footer {
      background-color: #f7f7f7;
      padding: 20px;
      text-align: center;
      font-size: 13px;
      color: #999999;
      border-top: 1px solid #eeeeee;
    }
    .social-links {
      margin: 15px 0;
    }
    .footer-text {
      margin: 10px 0;
    }
    @media only screen and (max-width: 480px) {
      .email-header h1 {
        font-size: 24px;
      }
      .email-content {
        padding: 25px 15px;
      }
    }
  </style>
</head>
<body>
  <div class="email-container">
    <div class="email-header">
      <h1>DeynCare</h1>
    </div>
    <div class="email-content">
      <div class="email-greeting">
        Hello {{fullName}},
      </div>
      <div class="email-message">
        <p>Your password for your DeynCare account has been successfully changed.</p>
        <p>If you made this change, no further action is required. You can continue to use your account with the new password.</p>
      </div>
      
      <div class="security-alert">
        <p><strong>Security Notice:</strong> If you did not change your password, please contact us immediately or reset your password by clicking the button below.</p>
      </div>
      
      <div class="btn-container">
        <a href="{{loginUrl}}" class="btn-primary">Log In to Your Account</a>
      </div>
      
      <div class="divider"></div>
      
      <p style="font-size: 14px; color: #666666;">
        For security reasons, you have been logged out of all devices. You will need to log in again with your new password.
      </p>
      
      <p style="font-size: 14px; color: #666666;">
        <strong>Time of change:</strong> {{changeTime}}<br>
        <strong>Device:</strong> {{deviceInfo}}<br>
        <strong>Location:</strong> {{location}}
      </p>
    </div>
    <div class="email-footer">
      <p class="footer-text">DeynCare - Debt Management System</p>
      <p class="footer-text">If you have any questions, please contact our support team.</p>
      <p class="footer-text">&copy; 2025 DeynCare. All rights reserved.</p>
    </div>
  </div>
</body>
</html>
