/**
 * Renew Subscription Service
 * Handles automatic and manual subscription renewals
 */

const { Subscription } = require('../../models');
const { AppError, logInfo, logError, logSuccess } = require('../../utils');

/**
 * Renew a subscription automatically or manually
 * @param {String} subscriptionId - ID of the subscription to renew
 * @param {Object} paymentData - Payment information
 * @param {String} paymentData.paymentMethod - Payment method used
 * @param {String} paymentData.transactionId - Transaction ID for payment
 * @param {Object} options - Renewal options
 * @param {String} options.actorId - ID of user/system performing renewal
 * @param {String} options.actorRole - Role of the actor
 * @returns {Object} Renewed subscription data
 */
const renewSubscription = async (subscriptionId, paymentData = {}, options = {}) => {
  try {
    const { paymentMethod = 'auto_renewal', transactionId } = paymentData;
    const { actorId = 'system', actorRole = 'system' } = options;

    // Get the subscription
    const subscription = await Subscription.findOne({ 
      subscriptionId: subscriptionId 
    });

    if (!subscription) {
      throw new AppError('Subscription not found', 404, 'subscription_not_found');
    }

    // Check if subscription is eligible for renewal
    if (subscription.status === 'canceled') {
      throw new AppError('Cannot renew canceled subscription', 400, 'subscription_canceled');
    }

    // Calculate new end date based on billing cycle
    const currentEndDate = new Date(subscription.dates?.endDate || subscription.endDate);
    const newEndDate = new Date(currentEndDate);
    
    if (subscription.pricing?.billingCycle === 'yearly' || subscription.plan?.type === 'yearly') {
      newEndDate.setFullYear(newEndDate.getFullYear() + 1);
    } else {
      // Default to monthly
      newEndDate.setMonth(newEndDate.getMonth() + 1);
    }

    // Calculate next payment date (usually same as end date)
    const nextPaymentDate = new Date(newEndDate);

    // Update subscription data
    const updateData = {
      status: 'active',
      'dates.endDate': newEndDate,
      'payment.method': paymentMethod,
      'payment.lastPaymentDate': new Date(),
      'payment.nextPaymentDate': nextPaymentDate,
      'payment.verified': true,
      'payment.failedPayments': 0, // Reset failed payment count
      'renewalSettings.reminderSent': false, // Reset reminder flag
      'renewalSettings.renewalAttempts': (subscription.renewalSettings?.renewalAttempts || 0) + 1,
      'dates.lastUpdated': new Date()
    };

    // Add payment details if provided
    if (transactionId) {
      updateData['payment.paymentDetails.transactionId'] = transactionId;
    }

    // Add to history
    const historyEntry = {
      action: 'renewed',
      date: new Date(),
      performedBy: actorId,
      performerRole: actorRole,
      details: {
        previousEndDate: currentEndDate,
        newEndDate,
        paymentMethod,
        transactionId: transactionId || 'auto_renewal',
        billingCycle: subscription.pricing?.billingCycle || subscription.plan?.type || 'monthly'
      }
    };

    updateData.$push = { history: historyEntry };

    // Update the subscription
    const renewedSubscription = await Subscription.findOneAndUpdate(
      { subscriptionId: subscriptionId },
      updateData,
      { new: true, runValidators: true }
    );

    if (!renewedSubscription) {
      throw new AppError('Failed to update subscription', 500, 'renewal_failed');
    }

    logSuccess(`Subscription renewed successfully: ${subscriptionId}`, 'SubscriptionService');

    return {
      subscriptionId: renewedSubscription.subscriptionId,
      shopId: renewedSubscription.shopId,
      status: renewedSubscription.status,
      endDate: newEndDate,
      nextPaymentDate,
      planType: renewedSubscription.plan?.type || renewedSubscription.planType,
      renewalCount: renewedSubscription.renewalSettings?.renewalAttempts || 1,
      subscription: renewedSubscription
    };

  } catch (error) {
    logError(`Failed to renew subscription ${subscriptionId}`, 'SubscriptionService', error);
    throw error;
  }
};

module.exports = renewSubscription; 