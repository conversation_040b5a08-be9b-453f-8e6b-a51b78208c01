const mongoose = require('mongoose');
const UserService = require('../../services/userService');
const ShopService = require('../../services/shopService');
const DiscountService = require('../../services/discountService');
const EmailService = require('../../services/emailService'); // Although not used here, useful for context
const {
  AppError,
  generateVerificationCode,
  ResponseHelper,
  UserHelper,
  ShopHelper,
  SubscriptionHelper,
  LogHelper,
  logInfo,
  logSuccess,
  logWarning,
  logError,
  TokenHelper,
  idGenerator,
  TransactionHelper
} = require('../../utils');

/**
 * Initialize Registration: Create user and shop, but do not send email or initiate payment.
 * POST /api/register/init
 */
const initRegistration = async (req, res, next) => {
  try {
    const {
      // User data
      fullName,
      email,
      phone,
      password,
      // Shop data
      shopName,
      shopAddress,
      // Business category data
      businessCategory = 'general_store',
      businessType = 'retail',
      // Subscription data
      planType = 'trial',
      registeredBy = 'self',
      paymentMethod = 'offline',
      initialPaid: userInitialPaid = false,
      // Payment details (not used for direct payment in this endpoint, but passed for shop model)
      paymentDetails: rawPaymentDetails,
      discountCode
    } = req.validatedData || req.body;

    // SMART EMAIL CHECK - Handle existing unverified users gracefully
    const normalizedEmail = email.toLowerCase().trim();
    const { User } = require('../../models');
    const existingUser = await User.findOne({
      email: normalizedEmail,
      isDeleted: { $ne: true }
    });

    if (existingUser) {
      if (existingUser.status === 'pending_email_verification') {
        // SMART HANDLING: Instead of error, resend verification and continue to verification step
        try {
          // Generate new verification code and update expiry
          const newVerificationCode = generateVerificationCode(6);
          existingUser.verificationCode = newVerificationCode;
          existingUser.verificationCodeExpires = new Date(Date.now() + 15 * 60 * 1000); // 15 minutes
          await existingUser.save();

          // Resend verification email
          await EmailService.auth.sendVerificationEmail(existingUser, newVerificationCode);
          logSuccess(`Verification email resent to existing user ${normalizedEmail}`, 'initRegistrationController');

          // Get shop details for existing user
          let shopDetails = { id: existingUser.shopId, name: 'Your Shop' };
          if (existingUser.shopId) {
            try {
              const existingShop = await ShopService.getShopById(existingUser.shopId, { includeInactive: true });
              if (existingShop) {
                shopDetails = {
                  id: existingShop.shopId,
                  name: existingShop.shopName
                };
              }
            } catch (shopError) {
              logWarning(`Could not fetch shop details for user ${existingUser.userId}`, 'initRegistrationController');
            }
          }

          // Return success response directing to verification step
          return ResponseHelper.success(
            res,
            'Registration already initiated. New verification code sent to your email.',
            {
              user: UserHelper.sanitizeUser(existingUser),
              shop: shopDetails,
              nextStep: 'verify_email_required',
              resent: true
            },
            200 // OK - existing registration continued
          );
        } catch (emailError) {
          logError(`Failed to resend verification email to ${normalizedEmail}`, 'initRegistrationController', emailError);
          // Get shop details for existing user (same logic as above)
          let shopDetails = { id: existingUser.shopId, name: 'Your Shop' };
          if (existingUser.shopId) {
            try {
              const existingShop = await ShopService.getShopById(existingUser.shopId, { includeInactive: true });
              if (existingShop) {
                shopDetails = {
                  id: existingShop.shopId,
                  name: existingShop.shopName
                };
              }
            } catch (shopError) {
              logWarning(`Could not fetch shop details for user ${existingUser.userId}`, 'initRegistrationController');
            }
          }

          // Even if email fails, still direct to verification
          return ResponseHelper.success(
            res,
            'Registration already initiated. Please check your email for verification code.',
            {
              user: UserHelper.sanitizeUser(existingUser),
              shop: shopDetails,
              nextStep: 'verify_email_required',
              resent: false
            },
            200
          );
        }
      } else {
        // For active/other status users, still return error
        return next(new AppError(
          'Email is already registered and active. Please login instead.',
          409,
          'email_exists'
        ));
      }
    }

    // Use a mutable variable for initialPaid to align with shop model
    let initialPaid = userInitialPaid; 
    let paymentDetails = rawPaymentDetails || {};

    // Use our new TransactionHelper for end-to-end transaction management
    const result = await TransactionHelper.withTransaction(async (session) => {
      let userData;
      let shopId = null;
      let shopLogoData = null;
      let discountDetails = null;
      let verificationCode = generateVerificationCode(6); // Always defined in transaction scope

      try {
        // UPDATED: Handle upload.fields() instead of upload.single()
        // Process shop logo file if uploaded - supports both 'logo' and 'shopLogo' field names
        if (req.files) {
          const FileUploadService = require('../../services/fileUploadService');
          // Check for 'logo' field first, then 'shopLogo' for backward compatibility
          const logoFile = req.files['logo'] ? req.files['logo'][0] : 
                          req.files['shopLogo'] ? req.files['shopLogo'][0] : null;
          
          if (logoFile) {
            shopLogoData = await FileUploadService.saveShopLogo(logoFile);
          }
        } else if (req.file) {
          // Fallback for single file upload (if any routes still use upload.single)
          const FileUploadService = require('../../services/fileUploadService');
          shopLogoData = await FileUploadService.saveShopLogo(req.file);
        }

        // Check if there's a discount code and verify it
        if (discountCode) {
          discountDetails = await DiscountService.verifyDiscountCode(discountCode);

          if (!discountDetails.valid) {
            throw new AppError(
              discountDetails.message || 'Invalid discount code',
              400,
              'invalid_discount_code'
            );
          }

          // Log discount code usage
          logInfo(`Discount code ${discountCode} applied to shop registration`, 'initRegistrationController');
        }

        // Create shop first with explicit initial statuses
        const shopData = {
          shopName,
          ownerName: fullName,
          email,
          phone,
          address: shopAddress,
          // Business details with category
          businessDetails: {
            type: businessType,
            category: businessCategory
          },
          status: 'pending', // Explicit initial shop status
          access: {
            isPaid: false,     // Access control: not paid initially
            isActivated: false // Access control: not activated initially
          },
          paymentHistory: [], // Initialize payment history
          subscription: {
            planType,
            paymentMethod,
            initialPaid, // Frontend sends this, but actual payment happens later
            paymentDetails,
            discountDetails: discountDetails?.valid ? discountDetails.discountDetails : null,
            status: 'pending_payment' // Explicit initial subscription status
          },
          registeredBy,
          logoUrl: shopLogoData?.url || '', // Set logo URL
          // Using session for transaction consistency
          session
        };

        const createdShop = await ShopService.createShop(shopData);
        shopId = createdShop.shopId;

        // Now create user with reference to shop
        // Create user with explicit initial statuses for new modular flow
        userData = await UserService.createUser({
          fullName,
          email,
          phone,
          password,
          role: 'admin', // Shop owner is always admin
          shopId: shopId,
          registeredBy,
          status: 'pending_email_verification', // New status for email verification step
          verified: false, // Not verified yet
          emailVerified: false, // Explicitly false
          isPaid: false, // Explicitly false
          isActivated: false, // Explicitly false
          verificationCode,
        }, { session }); // Pass session to UserService.createUser

        // Also update the shop with the owner reference
        createdShop.ownerId = userData.userId;
        await createdShop.save({ session }); // Save with session

        logSuccess(`Successfully initialized registration for user ${userData.userId} with shop ${shopId}`, 'initRegistrationController');

        // Return the data - TransactionHelper will handle the commit
        return { userData, shopId, verificationCode };
      } catch (error) {
        // TransactionHelper will handle the abort and logging
        throw error;
      }
    }, { name: 'InitRegistration' });

    // Get result from transaction
    const { userData, shopId, verificationCode } = result;

    // Send verification email after transaction commit (non-blocking for registration init)
    try {
      await EmailService.auth.sendVerificationEmail(userData, verificationCode);
      logSuccess(`Verification email sent to ${email}`, 'initRegistrationController');
    } catch (emailError) {
      logError(`Failed to send verification email to ${email}`, 'initRegistrationController', emailError);
      // Don't fail registration init if email fails, but log it
    }

    // Return success response with next step information
    return ResponseHelper.success(
      res,
      'Registration initiated successfully. Please verify your email.',
      {
        user: UserHelper.sanitizeUser(userData),
        shop: {
          id: shopId,
          name: shopName
        },
        nextStep: 'verify_email_required' // Indicate next step to frontend
      },
      201 // Created
    );
  } catch (error) {
    // Handle specific database errors
    if (error.name === 'MongoServerError' && error.code === 11000) {
      // Duplicate key error
      const field = Object.keys(error.keyValue)[0];
      const errorMessage = field === 'email'
        ? 'Email already exists'
        : `${field.charAt(0).toUpperCase() + field.slice(1)} already exists`;

      return next(new AppError(
        errorMessage,
        409,
        'duplicate_key'
      ));
    }

    // TransactionHelper already handled the abort and logging for transaction errors
    return next(new AppError(
      error.message || 'Error during registration initialization',
      error.statusCode || 500,
      error.type || 'registration_init_error'
    ));
  }
};

module.exports = {
  initRegistration,
}; 