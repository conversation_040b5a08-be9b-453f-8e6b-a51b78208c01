const ReportService = require('../services/reportService');
const ShopService = require('../services/shopService');

// Import models
const { Customer, Debt } = require('../models');

// Import utility modules
const { 
  // Core utilities
  AppError, 
  
  // Helper utilities
  ResponseHelper,
  LogHelper,
  
  // Logger utilities
  logInfo,
  logSuccess,
  logWarning,
  logError
} = require('../utils');

/**
 * Report controller for handling all report-related operations
 */
const ReportController = {
  /**
   * Generate a new report
   * POST /api/reports
   * Requires authentication
   */
  generateReport: async (req, res, next) => {
    try {
      const reportData = req.validatedData || req.body;
      const { userId, role, shopId } = req.user;
      
      // Set default values
      reportData.createdBy = userId;
      
      // If user is not superAdmin, restrict to their shop
      if (role !== 'superAdmin') {
        reportData.shopId = shopId;
      } else if (!reportData.shopId) {
        return next(new AppError('Shop ID is required for report generation', 400, 'missing_shop_id'));
      }
      
      // Verify shop exists
      try {
        const shop = await ShopService.getShopById(reportData.shopId);
        if (!shop) {
          return next(new AppError('Shop not found', 404, 'shop_not_found'));
        }
      } catch (error) {
        return next(new AppError('Failed to verify shop', 500, 'shop_verification_error'));
      }
      
      // Generate the report
      const report = await ReportService.generateReport(reportData);
      
      // Create audit log
      await LogHelper.createAdminLog('report_generated', {
        actorId: userId,
        actorRole: role,
        shopId: reportData.shopId,
        details: {
          reportId: report.reportId,
          reportType: report.type
        }
      });
      
      return ResponseHelper.success(
        res, 
        'Report generated successfully', 
        { report }, 
        201
      );
    } catch (error) {
      logError('Error generating report', 'ReportController', error);
      return next(error);
    }
  },
  
  /**
   * Get report by ID
   * GET /api/reports/:reportId
   * Requires authentication
   */
  getReportById: async (req, res, next) => {
    try {
      const { reportId } = req.params;
      const { userId, role, shopId } = req.user;
      
      // Set authorization options
      const options = {
        role,
        shopId: role !== 'superAdmin' ? shopId : undefined
      };
      
      // Get the report
      const report = await ReportService.getReportById(reportId, options);
      
      // Create audit log
      await LogHelper.createAdminLog('report_viewed', {
        actorId: userId,
        actorRole: role,
        shopId: report.shopId,
        details: {
          reportId: report.reportId,
          reportType: report.type
        }
      });
      
      return ResponseHelper.success(
        res, 
        'Report retrieved successfully', 
        { report }
      );
    } catch (error) {
      logError(`Error retrieving report ${req.params.reportId}`, 'ReportController', error);
      return next(error);
    }
  },
  
  /**
   * Get reports by shop
   * GET /api/reports/shop/:shopId
   * Requires authentication and appropriate authorization
   */
  getReportsByShop: async (req, res, next) => {
    try {
      const { shopId } = req.params;
      const { userId, role, shopId: userShopId } = req.user;
      
      // Check authorization
      if (role !== 'superAdmin' && shopId !== userShopId) {
        return next(new AppError('You do not have permission to view reports for this shop', 403, 'forbidden'));
      }
      
      // Get the reports
      const result = await ReportService.getReportsByShop(shopId, req.query);
      
      // Create audit log
      await LogHelper.createAdminLog('shop_reports_viewed', {
        actorId: userId,
        actorRole: role,
        shopId,
        details: {
          filters: req.query
        }
      });
      
      return ResponseHelper.success(
        res, 
        'Reports retrieved successfully', 
        { 
          reports: result.items,
          pagination: result.pagination
        }
      );
    } catch (error) {
      logError(`Error retrieving reports for shop ${req.params.shopId}`, 'ReportController', error);
      return next(error);
    }
  },
  
  /**
   * Get all reports (SuperAdmin only)
   * GET /api/reports
   * Requires authentication and superAdmin authorization
   */
  getAllReports: async (req, res, next) => {
    try {
      const { userId, role } = req.user;
      
      // Only superAdmin can access this endpoint
      if (role !== 'superAdmin') {
        return next(new AppError('You do not have permission to access this resource', 403, 'forbidden'));
      }
      
      // Get all reports
      const result = await ReportService.getAllReports(req.query);
      
      // Create audit log
      await LogHelper.createAdminLog('all_reports_viewed', {
        actorId: userId,
        actorRole: role,
        details: {
          filters: req.query
        }
      });
      
      return ResponseHelper.success(
        res, 
        'Reports retrieved successfully', 
        { 
          reports: result.items,
          pagination: result.pagination
        }
      );
    } catch (error) {
      logError('Error retrieving all reports', 'ReportController', error);
      return next(error);
    }
  },
  
  /**
   * Delete a report
   * DELETE /api/reports/:reportId
   * Requires authentication and appropriate authorization
   */
  deleteReport: async (req, res, next) => {
    try {
      const { reportId } = req.params;
      const { userId, role, shopId } = req.user;
      
      // Set authorization options
      const options = {
        role,
        shopId: role !== 'superAdmin' ? shopId : undefined
      };
      
      // Get the report first to check authorization
      const report = await ReportService.getReportById(reportId, options);
      
      // Delete the report
      await ReportService.deleteReport(reportId, options);
      
      // Create audit log
      await LogHelper.createAdminLog('report_deleted', {
        actorId: userId,
        actorRole: role,
        shopId: report.shopId,
        details: {
          reportId: report.reportId,
          reportType: report.type
        }
      });
      
      return ResponseHelper.success(
        res, 
        'Report deleted successfully'
      );
    } catch (error) {
      logError(`Error deleting report ${req.params.reportId}`, 'ReportController', error);
      return next(error);
    }
  },
  
  /**
   * Get report statistics (SuperAdmin only)
   * GET /api/reports/statistics
   * Requires authentication and superAdmin authorization
   */
  getReportStatistics: async (req, res, next) => {
    try {
      const { userId, role } = req.user;
      
      // Only superAdmin can access this endpoint
      if (role !== 'superAdmin') {
        return next(new AppError('You do not have permission to access this resource', 403, 'forbidden'));
      }
      
      // Get report statistics
      const statistics = await ReportService.getReportStatistics();
      
      // Create audit log
      await LogHelper.createAdminLog('report_statistics_viewed', {
        actorId: userId,
        actorRole: role
      });
      
      return ResponseHelper.success(
        res, 
        'Report statistics retrieved successfully', 
        { statistics }
      );
    } catch (error) {
      logError('Error retrieving report statistics', 'ReportController', error);
      return next(error);
    }
  },
  
  /**
   * Generate a system-wide report (SuperAdmin only)
   * POST /api/reports/system
   * Requires authentication and superAdmin authorization
   */
  generateSystemReport: async (req, res, next) => {
    try {
      const { userId, role } = req.user;
      
      // Only superAdmin can access this endpoint
      if (role !== 'superAdmin') {
        return next(new AppError('You do not have permission to access this resource', 403, 'forbidden'));
      }
      
      const parameters = req.validatedData || req.body;
      
      // Set created by
      parameters.createdBy = userId;
      
      // Generate the system report
      const report = await ReportService.generateSystemReport(parameters);
      
      // Create audit log
      await LogHelper.createAdminLog('system_report_generated', {
        actorId: userId,
        actorRole: role,
        details: {
          reportId: report.reportId,
          reportType: report.type
        }
      });
      
      return ResponseHelper.success(
        res, 
        'System report generated successfully', 
        { report }, 
        201
      );
    } catch (error) {
      logError('Error generating system report', 'ReportController', error);
      return next(error);
    }
  },
  
  /**
   * Email a report to specified recipients
   * POST /api/reports/:reportId/email
   * Requires authentication
   */
  emailReport: async (req, res, next) => {
    try {
      const { reportId } = req.params;
      const emailData = req.validatedData || req.body;
      const { userId, role, shopId } = req.user;
      
      // Set authorization options
      const options = {
        role,
        shopId: role !== 'superAdmin' ? shopId : undefined
      };
      
      // Verify the report exists and user has access to it
      try {
        await ReportService.getReportById(reportId, options);
      } catch (error) {
        return next(error);
      }
      
      // Send the email
      const result = await ReportService.emailReport(reportId, emailData, options);
      
      // Create audit log
      await LogHelper.createAdminLog('report_emailed', {
        actorId: userId,
        actorRole: role,
        shopId: role !== 'superAdmin' ? shopId : null,
        details: {
          reportId,
          recipients: emailData.recipients,
          subject: emailData.subject
        }
      });
      
      return ResponseHelper.success(
        res, 
        'Report emailed successfully',
        { result }
      );
    } catch (error) {
      logError(`Error emailing report ${req.params.reportId}`, 'ReportController', error);
      return next(error);
    }
  },
  
  /**
   * Schedule periodic report generation and delivery
   * POST /api/reports/schedule
   * Requires authentication and superAdmin role for system-wide reports
   */
  scheduleReportDelivery: async (req, res, next) => {
    try {
      const scheduleData = req.validatedData || req.body;
      const { userId, role, shopId } = req.user;
      
      // Set authorization options
      const options = {
        role,
        shopId: role !== 'superAdmin' ? shopId : undefined
      };
      
      // Schedule the report delivery
      const result = await ReportService.scheduleReportDelivery(scheduleData, options);
      
      // Create audit log
      await LogHelper.createAdminLog('report_delivery_scheduled', {
        actorId: userId,
        actorRole: role,
        shopId: role !== 'superAdmin' ? shopId : scheduleData.shopId,
        details: {
          scheduleId: result.schedule.id,
          frequency: scheduleData.frequency,
          reportType: scheduleData.reportType
        }
      });
      
      return ResponseHelper.success(
        res, 
        'Report delivery scheduled successfully',
        { schedule: result.schedule }
      );
    } catch (error) {
      logError('Error scheduling report delivery', 'ReportController', error);
      return next(error);
    }
  },
  /**
   * Get Customer Report Data (Optimized for PDF generation)
   * GET /api/reports/customers/data
   */
  getCustomerReportData: async (req, res, next) => {
    try {
      const { shopId, role } = req.user;
      const {
        month,
        year,
        startDate,
        endDate,
        dateRange = 'monthly',
        format = 'json',
        includeRiskData = 'true',
        page = 1,
        limit = 100 // Add pagination to prevent memory issues
      } = req.query;

      // Validate pagination parameters
      const pageNum = Math.max(1, parseInt(page));
      const limitNum = Math.min(500, Math.max(10, parseInt(limit))); // Max 500 records per request
      const skip = (pageNum - 1) * limitNum;

      // Build date filter
      let dateFilter = {};
      let periodDescription = 'All Time';
      
      if (month && year) {
        const startOfMonth = new Date(year, month - 1, 1);
        const endOfMonth = new Date(year, month, 0, 23, 59, 59);
        dateFilter.createdAt = { $gte: startOfMonth, $lte: endOfMonth };
        periodDescription = `${new Date(year, month - 1).toLocaleString('default', { month: 'long' })} ${year}`;
      } else if (startDate && endDate) {
        dateFilter.createdAt = { 
          $gte: new Date(startDate), 
          $lte: new Date(endDate) 
        };
        periodDescription = `${new Date(startDate).toLocaleDateString()} - ${new Date(endDate).toLocaleDateString()}`;
      } else if (dateRange === 'daily') {
        const today = new Date();
        const startOfDay = new Date(today.setHours(0, 0, 0, 0));
        const endOfDay = new Date(today.setHours(23, 59, 59, 999));
        dateFilter.createdAt = { $gte: startOfDay, $lte: endOfDay };
        periodDescription = 'Today';
      }

      // Get customers with debt aggregation to calculate financial data
      console.log('🔍 [REPORT DEBUG] Starting customer aggregation for shopId:', shopId);
      console.log('🔍 [REPORT DEBUG] Date filter:', dateFilter);

      // First, let's check if there are any debts in the database for this shop
      const Debt = require('../models/debt.model');
      const totalDebtsInShop = await Debt.countDocuments({ shopId, isDeleted: false });
      console.log('🔍 [REPORT DEBUG] Total debts in shop:', totalDebtsInShop);

      if (totalDebtsInShop > 0) {
        const sampleDebt = await Debt.findOne({ shopId, isDeleted: false }).lean();
        console.log('🔍 [REPORT DEBUG] Sample debt record:', {
          debtId: sampleDebt?.debtId,
          customerId: sampleDebt?.customerId,
          DebtAmount: sampleDebt?.DebtAmount,
          OutstandingDebt: sampleDebt?.OutstandingDebt,
          PaidAmount: sampleDebt?.PaidAmount
        });

        // Check if any customers have matching customerIds
        const sampleCustomer = await Customer.findOne({ shopId, isDeleted: false }).lean();
        console.log('🔍 [REPORT DEBUG] Sample customer record:', {
          customerId: sampleCustomer?.customerId,
          CustomerName: sampleCustomer?.CustomerName
        });

        // Check if there's a match
        const matchingDebt = await Debt.findOne({
          shopId,
          customerId: sampleCustomer?.customerId,
          isDeleted: false
        }).lean();
        console.log('🔍 [REPORT DEBUG] Matching debt for sample customer:', matchingDebt ? 'FOUND' : 'NOT FOUND');

      } else {
        console.log('🔍 [REPORT DEBUG] No debts found in database for this shop');
        console.log('🔍 [REPORT DEBUG] This explains why all debt amounts are $0.00');
      }

      // Test the aggregation step by step
      console.log('🔍 [REPORT DEBUG] Testing aggregation pipeline...');

      // Step 1: Test basic customer query
      const basicCustomers = await Customer.find({
        shopId,
        isDeleted: false,
        ...dateFilter
      }).limit(1).lean();
      console.log('🔍 [REPORT DEBUG] Basic customer query result:', basicCustomers.length);

      // Step 2: Test lookup manually
      if (basicCustomers.length > 0) {
        const testCustomerId = basicCustomers[0].customerId;
        const testDebts = await Debt.find({
          customerId: testCustomerId,
          isDeleted: false
        }).lean();
        console.log('🔍 [REPORT DEBUG] Manual debt lookup for customer', testCustomerId, ':', testDebts.length, 'debts');
        if (testDebts.length > 0) {
          console.log('🔍 [REPORT DEBUG] First debt:', {
            DebtAmount: testDebts[0].DebtAmount,
            OutstandingDebt: testDebts[0].OutstandingDebt,
            PaidAmount: testDebts[0].PaidAmount
          });
        }
      }

      // Get total count for pagination
      const totalCount = await Customer.countDocuments({
        shopId,
        isDeleted: false,
        ...dateFilter
      });

      const customers = await Customer.aggregate([
        {
          $match: {
            shopId,
            isDeleted: false,
            ...dateFilter
          }
        },
        {
          $lookup: {
            from: 'debts',
            localField: 'customerId',
            foreignField: 'customerId',
            as: 'debts',
            pipeline: [
              { $match: { isDeleted: false } },
              { $limit: 50 } // Limit debts per customer to prevent memory issues
            ]
          }
        },
        {
          $addFields: {
            totalDebt: { $sum: '$debts.DebtAmount' },
            outstandingDebt: { $sum: '$debts.OutstandingDebt' },
            paidAmount: { $sum: '$debts.PaidAmount' },
            debtCount: { $size: '$debts' }
          }
        },
        { $sort: { createdAt: -1 } },
        { $skip: skip },
        { $limit: limitNum }
      ]);

      console.log('🔍 [REPORT DEBUG] Found customers:', customers.length);
      if (customers.length > 0) {
        const firstCustomer = customers[0];
        console.log('🔍 [REPORT DEBUG] First customer sample:');
        console.log('  - customerId:', firstCustomer.customerId);
        console.log('  - CustomerName:', firstCustomer.CustomerName);
        console.log('  - debtCount:', firstCustomer.debtCount);
        console.log('  - totalDebt:', firstCustomer.totalDebt);
        console.log('  - outstandingDebt:', firstCustomer.outstandingDebt);
        console.log('  - paidAmount:', firstCustomer.paidAmount);
        console.log('  - debts array length:', firstCustomer.debts?.length || 0);
        if (firstCustomer.debts && firstCustomer.debts.length > 0) {
          console.log('  - first debt sample:', {
            debtId: firstCustomer.debts[0].debtId,
            DebtAmount: firstCustomer.debts[0].DebtAmount,
            OutstandingDebt: firstCustomer.debts[0].OutstandingDebt,
            PaidAmount: firstCustomer.debts[0].PaidAmount
          });
        }
      }

      // Calculate comprehensive summary
      const summary = {
        totalCustomers: customers.length,
        reportPeriod: periodDescription,
        generatedAt: new Date().toISOString(),
        dateRange: { 
          startDate: dateFilter.createdAt?.$gte, 
          endDate: dateFilter.createdAt?.$lte 
        },
        customerTypes: customers.reduce((acc, customer) => {
          acc[customer.CustomerType] = (acc[customer.CustomerType] || 0) + 1;
          return acc;
        }, {}),
        riskDistribution: customers.reduce((acc, customer) => {
          const risk = customer.riskProfile?.currentRiskLevel || 'Not Assessed';
          acc[risk] = (acc[risk] || 0) + 1;
          return acc;
        }, {}),
        totalDebtAmount: customers.reduce((sum, customer) => sum + (customer.totalDebt || 0), 0),
        totalOutstandingDebt: customers.reduce((sum, customer) => sum + (customer.outstandingDebt || 0), 0),
        averageDebtPerCustomer: customers.length > 0 ? 
          customers.reduce((sum, customer) => sum + (customer.totalDebt || 0), 0) / customers.length : 0
      };

      // Format customer data for reports
      console.log('🔍 [REPORT DEBUG] Formatting customers for response...');
      const formattedCustomers = customers.map((customer, index) => {
        const formatted = {
          customerId: customer.customerId,
          CustomerName: customer.CustomerName,
          CustomerType: customer.CustomerType,
          phone: customer.phone,
          email: customer.email || 'N/A',
          address: customer.address || 'N/A',
          riskLevel: customer.riskProfile?.currentRiskLevel || 'Not Assessed',
          createdAt: customer.createdAt,
          totalDebt: customer.totalDebt || 0,
          outstandingDebt: customer.outstandingDebt || 0,
          paidAmount: customer.paidAmount || 0,
          paymentStatus: (customer.outstandingDebt || 0) > 0 ? 'Outstanding' : 'Paid',
          registrationDate: customer.createdAt.toLocaleDateString(),
          daysSinceRegistration: Math.floor((new Date() - customer.createdAt) / (1000 * 60 * 60 * 24))
        };

        if (index === 0) {
          console.log('🔍 [REPORT DEBUG] First formatted customer:');
          console.log('  - totalDebt:', formatted.totalDebt, '(type:', typeof formatted.totalDebt, ')');
          console.log('  - outstandingDebt:', formatted.outstandingDebt, '(type:', typeof formatted.outstandingDebt, ')');
          console.log('  - paidAmount:', formatted.paidAmount, '(type:', typeof formatted.paidAmount, ')');
        }

        return formatted;
      });

      // Create audit log
      await LogHelper.createAdminLog('customer_report_generated', {
        actorId: req.user.userId,
        actorRole: role,
        shopId,
        details: {
          reportType: 'customer_data',
          period: periodDescription,
          totalCustomers: customers.length,
          filters: { month, year, startDate, endDate, dateRange }
        }
      });

      return ResponseHelper.success(res, 'Customer report data generated successfully', {
        customers: formattedCustomers,
        summary,
        pagination: {
          currentPage: pageNum,
          totalPages: Math.ceil(totalCount / limitNum),
          totalRecords: totalCount,
          recordsPerPage: limitNum,
          hasNextPage: pageNum < Math.ceil(totalCount / limitNum),
          hasPreviousPage: pageNum > 1
        },
        metadata: {
          reportType: 'customer_report',
          generatedBy: req.user.userId,
          shopId,
          filters: { month, year, startDate, endDate, dateRange },
          totalRecords: totalCount,
          currentPageRecords: customers.length,
          format
        }
      });

    } catch (error) {
      logError('Error generating customer report data', 'ReportController', error);
      return next(error);
    }
  },
  /**
   * Get Customer Report Statistics
   * GET /api/reports/customers/stats
   */
  getCustomerReportStats: async (req, res, next) => {
    try {
      const { shopId } = req.user;
      const { month, year, startDate, endDate, dateRange = 'monthly' } = req.query;

      // Build date filter (same logic as above)
      let dateFilter = {};
      if (month && year) {
        const startOfMonth = new Date(year, month - 1, 1);
        const endOfMonth = new Date(year, month, 0, 23, 59, 59);
        dateFilter.createdAt = { $gte: startOfMonth, $lte: endOfMonth };
      }


      // Get aggregated statistics
      const stats = await Customer.aggregate([
        { $match: { shopId, isDeleted: false, ...dateFilter } },
        {
          $group: {
            _id: null,
            totalCustomers: { $sum: 1 },
            totalDebt: { $sum: '$totalDebt' },
            totalOutstanding: { $sum: '$outstandingDebt' },
            avgDebtPerCustomer: { $avg: '$totalDebt' },
            customerTypes: {
              $push: '$CustomerType'
            },
            riskLevels: {
              $push: '$riskProfile.currentRiskLevel'
            }
          }
        }
      ]);

      const result = stats[0] || {
        totalCustomers: 0,
        totalDebt: 0,
        totalOutstanding: 0,
        avgDebtPerCustomer: 0,
        customerTypes: [],
        riskLevels: []
      };

      // Process customer types distribution
      const typeDistribution = result.customerTypes.reduce((acc, type) => {
        acc[type] = (acc[type] || 0) + 1;
        return acc;
      }, {});

      // Process risk distribution
      const riskDistribution = result.riskLevels.reduce((acc, risk) => {
        const level = risk || 'Not Assessed';
        acc[level] = (acc[level] || 0) + 1;
        return acc;
      }, {});

      return ResponseHelper.success(res, 'Customer statistics retrieved successfully', {
        statistics: {
          totalCustomers: result.totalCustomers,
          totalDebtAmount: result.totalDebt,
          totalOutstandingDebt: result.totalOutstanding,
          averageDebtPerCustomer: Math.round(result.avgDebtPerCustomer || 0),
          collectionRate: result.totalDebt > 0 ? 
            Math.round(((result.totalDebt - result.totalOutstanding) / result.totalDebt) * 100) : 0,
          customerTypeDistribution: typeDistribution,
          riskDistribution: riskDistribution
        },
        metadata: {
          generatedAt: new Date().toISOString(),
          period: month && year ? `${year}-${month.toString().padStart(2, '0')}` : 'all_time',
          shopId
        }
      });

    } catch (error) {
      logError('Error getting customer report statistics', 'ReportController', error);
      return next(error);
    }
  },

  /**
   * Get Debt Report Data
   * GET /api/reports/debts/data
   */
  getDebtReportData: async (req, res, next) => {
    try {
      console.log('🚀 [DEBT REPORT] Route handler called successfully!');
      console.log('🚀 [DEBT REPORT] Request URL:', req.originalUrl);
      console.log('🚀 [DEBT REPORT] Request method:', req.method);
      console.log('🚀 [DEBT REPORT] Query params:', req.query);

      const { shopId } = req.user;
      const {
        startDate,
        endDate,
        period = 'all',
        page = 1,
        limit = 100 // Add pagination
      } = req.query;

      // Validate pagination parameters
      const pageNum = Math.max(1, parseInt(page));
      const limitNum = Math.min(500, Math.max(10, parseInt(limit)));
      const skip = (pageNum - 1) * limitNum;

      console.log('🔍 [DEBT REPORT DEBUG] Starting debt report for shopId:', shopId);
      console.log('🔍 [DEBT REPORT DEBUG] Period:', period, 'StartDate:', startDate, 'EndDate:', endDate);
      console.log('🔍 [DEBT REPORT DEBUG] Pagination:', { page: pageNum, limit: limitNum, skip });

      // Build date filter
      let dateFilter = {};
      if (period !== 'all' && startDate && endDate) {
        dateFilter.createdAt = {
          $gte: new Date(startDate),
          $lte: new Date(endDate)
        };
      }

      // Get debts with customer information
      const debts = await Debt.aggregate([
        {
          $match: {
            shopId,
            isDeleted: false,
            ...dateFilter
          }
        },
        {
          $lookup: {
            from: 'customers',
            localField: 'customerId',
            foreignField: 'customerId',
            as: 'customer'
          }
        },
        {
          $unwind: {
            path: '$customer',
            preserveNullAndEmptyArrays: true
          }
        },
        {
          $addFields: {
            customerName: '$customer.CustomerName',
            customerType: '$customer.CustomerType',
            overdueDebt: {
              $cond: {
                if: {
                  $and: [
                    { $ne: ['$DueDate', null] },
                    { $lt: ['$DueDate', new Date()] },
                    { $gt: ['$OutstandingDebt', 0] }
                  ]
                },
                then: '$OutstandingDebt',
                else: 0
              }
            },
            daysPastDue: {
              $cond: {
                if: {
                  $and: [
                    { $ne: ['$DueDate', null] },
                    { $lt: ['$DueDate', new Date()] }
                  ]
                },
                then: {
                  $divide: [
                    { $subtract: [new Date(), '$DueDate'] },
                    1000 * 60 * 60 * 24
                  ]
                },
                else: 0
              }
            }
          }
        },
        { $sort: { createdAt: -1 } }
      ]);

      console.log('🔍 [DEBT REPORT DEBUG] Found debts:', debts.length);

      // Format debt data for reports
      const formattedDebts = debts.map(debt => ({
        debtId: debt.debtId,
        customerId: debt.customerId,
        customerName: debt.customerName || 'Unknown Customer',
        customerType: debt.customerType || 'Unknown',
        debtAmount: debt.DebtAmount || 0,
        outstandingDebt: debt.OutstandingDebt || 0,
        paidAmount: debt.PaidAmount || 0,
        debtDate: debt.createdAt,
        dueDate: debt.DueDate,
        overdueDebt: debt.overdueDebt || 0,
        paymentStatus: (debt.OutstandingDebt || 0) > 0 ? 'Outstanding' : 'Paid',
        daysPastDue: Math.floor(debt.daysPastDue || 0)
      }));

      // Calculate summary
      const summary = {
        totalDebts: debts.length,
        totalDebtAmount: debts.reduce((sum, debt) => sum + (debt.DebtAmount || 0), 0),
        totalOutstandingDebt: debts.reduce((sum, debt) => sum + (debt.OutstandingDebt || 0), 0),
        totalPaidAmount: debts.reduce((sum, debt) => sum + (debt.PaidAmount || 0), 0),
        totalOverdueDebt: formattedDebts.reduce((sum, debt) => sum + debt.overdueDebt, 0),
        overdueCount: formattedDebts.filter(debt => debt.overdueDebt > 0).length
      };

      console.log('🔍 [DEBT REPORT DEBUG] Summary:', summary);

      res.status(200).json({
        success: true,
        data: {
          debts: formattedDebts,
          summary,
          period,
          generatedAt: new Date().toISOString()
        }
      });

    } catch (error) {
      console.error('❌ Error generating debt report:', error);
      logError('Error generating debt report data', 'ReportController', error);
      return next(error);
    }
  },

  /**
   * Get Risk Report Data
   * GET /api/reports/risks/data
   */
  getRiskReportData: async (req, res, next) => {
    try {
      console.log('🚀 [RISK REPORT] Route handler called successfully!');
      console.log('🚀 [RISK REPORT] Request URL:', req.originalUrl);
      console.log('🚀 [RISK REPORT] Request method:', req.method);
      console.log('🚀 [RISK REPORT] Query params:', req.query);

      const { shopId } = req.user;
      const { startDate, endDate, period = 'all' } = req.query;

      console.log('🔍 [RISK REPORT DEBUG] Starting risk report for shopId:', shopId);
      console.log('🔍 [RISK REPORT DEBUG] Period:', period, 'StartDate:', startDate, 'EndDate:', endDate);

      // Build date filter
      let dateFilter = {};
      if (period !== 'all' && startDate && endDate) {
        dateFilter.createdAt = {
          $gte: new Date(startDate),
          $lte: new Date(endDate)
        };
      }

      // Get customers with risk assessment data
      const customers = await Customer.aggregate([
        {
          $match: {
            shopId,
            isDeleted: false,
            ...dateFilter
          }
        },
        {
          $lookup: {
            from: 'debts',
            localField: 'customerId',
            foreignField: 'customerId',
            as: 'debts',
            pipeline: [
              { $match: { isDeleted: false } }
            ]
          }
        },
        {
          $lookup: {
            from: 'payments',
            localField: 'customerId',
            foreignField: 'customerId',
            as: 'payments',
            pipeline: [
              { $match: { isDeleted: false } }
            ]
          }
        },
        {
          $addFields: {
            totalDebt: { $sum: '$debts.DebtAmount' },
            outstandingDebt: { $sum: '$debts.OutstandingDebt' },
            paidAmount: { $sum: '$debts.PaidAmount' },
            totalTransactions: { $size: '$payments' },
            latePayments: {
              $size: {
                $filter: {
                  input: '$debts',
                  cond: {
                    $and: [
                      { $ne: ['$$this.DueDate', null] },
                      { $lt: ['$$this.DueDate', new Date()] },
                      { $gt: ['$$this.OutstandingDebt', 0] }
                    ]
                  }
                }
              }
            },
            averagePaymentTime: {
              $avg: {
                $map: {
                  input: '$payments',
                  as: 'payment',
                  in: {
                    $divide: [
                      { $subtract: ['$$payment.updatedAt', '$$payment.createdAt'] },
                      1000 * 60 * 60 * 24
                    ]
                  }
                }
              }
            },
            lastPaymentDate: { $max: '$payments.createdAt' }
          }
        },
        {
          $addFields: {
            paymentDelay: {
              $cond: {
                if: { $ne: ['$lastPaymentDate', null] },
                then: {
                  $divide: [
                    { $subtract: [new Date(), '$lastPaymentDate'] },
                    1000 * 60 * 60 * 24
                  ]
                },
                else: 365 // Default to 1 year if no payments
              }
            },
            riskScore: {
              $multiply: [
                {
                  $add: [
                    {
                      $cond: {
                        if: { $gt: ['$totalTransactions', 0] },
                        then: { $divide: ['$latePayments', '$totalTransactions'] },
                        else: 0
                      }
                    },
                    {
                      $cond: {
                        if: { $gt: ['$totalDebt', 0] },
                        then: { $divide: ['$outstandingDebt', '$totalDebt'] },
                        else: 0
                      }
                    }
                  ]
                },
                50
              ]
            }
          }
        },
        {
          $addFields: {
            riskCategory: {
              $cond: {
                if: { $gte: ['$riskScore', 70] },
                then: 'High Risk',
                else: {
                  $cond: {
                    if: { $gte: ['$riskScore', 40] },
                    then: 'Medium Risk',
                    else: 'Low Risk'
                  }
                }
              }
            }
          }
        },
        { $sort: { riskScore: -1 } }
      ]);

      console.log('🔍 [RISK REPORT DEBUG] Found customers:', customers.length);

      // Format risk data for reports
      const formattedRisks = customers.map(customer => ({
        customerId: customer.customerId,
        customerName: customer.CustomerName || 'Unknown Customer',
        customerType: customer.CustomerType || 'Unknown',
        riskLevel: customer.riskProfile?.currentRiskLevel || customer.riskCategory || 'Not Assessed',
        totalDebt: customer.totalDebt || 0,
        paidAmount: customer.paidAmount || 0,
        paymentDelay: Math.floor(customer.paymentDelay || 0),
        outstandingDebt: customer.outstandingDebt || 0,
        totalTransactions: customer.totalTransactions || 0,
        latePayments: customer.latePayments || 0,
        averagePaymentTime: customer.averagePaymentTime || 0,
        lastPaymentDate: customer.lastPaymentDate || new Date(Date.now() - 365 * 24 * 60 * 60 * 1000),
        riskCategory: customer.riskCategory || 'Low Risk',
        riskScore: customer.riskScore || 0
      }));

      // Calculate summary
      const summary = {
        totalCustomers: customers.length,
        highRiskCount: formattedRisks.filter(c => c.riskScore >= 70).length,
        mediumRiskCount: formattedRisks.filter(c => c.riskScore >= 40 && c.riskScore < 70).length,
        lowRiskCount: formattedRisks.filter(c => c.riskScore < 40).length,
        averageRiskScore: formattedRisks.reduce((sum, c) => sum + c.riskScore, 0) / (formattedRisks.length || 1),
        totalDebtAtRisk: formattedRisks.filter(c => c.riskScore >= 40).reduce((sum, c) => sum + c.totalDebt, 0)
      };

      console.log('🔍 [RISK REPORT DEBUG] Summary:', summary);

      res.status(200).json({
        success: true,
        data: {
          risks: formattedRisks,
          summary,
          period,
          generatedAt: new Date().toISOString()
        }
      });

    } catch (error) {
      console.error('❌ Error generating risk report:', error);
      logError('Error generating risk report data', 'ReportController', error);
      return next(error);
    }
  }
};

module.exports = ReportController;





