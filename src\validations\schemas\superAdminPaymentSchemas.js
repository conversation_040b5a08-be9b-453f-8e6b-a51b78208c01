/**
 * SuperAdmin Payment Transaction Validation Schemas
 * Defines validation rules for SuperAdmin payment transaction endpoints
 */
const Joi = require('joi');

/**
 * Get all payment transactions validation
 */
const getPaymentTransactions = Joi.object({
  page: Joi.number().integer().min(1).default(1)
    .messages({
      'number.base': 'Page must be a number',
      'number.integer': 'Page must be an integer',
      'number.min': 'Page must be at least 1'
    }),
  limit: Joi.number().integer().min(1).max(100).default(10)
    .messages({
      'number.base': 'Limit must be a number',
      'number.integer': 'Limit must be an integer',
      'number.min': 'Limit must be at least 1',
      'number.max': 'Limit cannot exceed 100'
    }),
  status: Joi.string().valid('pending', 'processing', 'success', 'failed', 'approved', 'rejected')
    .messages({
      'any.only': 'Status must be one of: pending, processing, success, failed, approved, rejected'
    }),
  paymentMethod: Joi.string().valid('cash', 'bank_transfer', 'mobile_money', 'card', 'other', 'offline', 'evc_plus')
    .messages({
      'any.only': 'Payment method must be one of: cash, bank_transfer, mobile_money, card, other, offline, evc_plus'
    }),
  startDate: Joi.date().iso()
    .messages({
      'date.base': 'Start date must be a valid date',
      'date.format': 'Start date must be in ISO format'
    }),
  endDate: Joi.date().iso().min(Joi.ref('startDate'))
    .messages({
      'date.base': 'End date must be a valid date',
      'date.format': 'End date must be in ISO format',
      'date.min': 'End date must be after start date'
    }),
  search: Joi.string().trim().max(100)
    .messages({
      'string.max': 'Search term cannot exceed 100 characters'
    }),
  sortBy: Joi.string().valid('createdAt', 'amount', 'customerName', 'shopName', 'status')
    .default('createdAt')
    .messages({
      'any.only': 'Sort by must be one of: createdAt, amount, customerName, shopName, status'
    }),
  sortOrder: Joi.string().valid('asc', 'desc').default('desc')
    .messages({
      'any.only': 'Sort order must be either asc or desc'
    })
});

/**
 * Get single payment transaction by ID validation
 */
const getPaymentTransactionById = Joi.object({
  paymentId: Joi.string().required().trim()
    .messages({
      'string.empty': 'Payment ID cannot be empty',
      'any.required': 'Payment ID is required'
    })
});

/**
 * Approve payment transaction validation
 */
const approvePaymentTransactionParams = Joi.object({
  paymentId: Joi.string().required().trim()
    .messages({
      'string.empty': 'Payment ID cannot be empty',
      'any.required': 'Payment ID is required'
    })
});

const approvePaymentTransaction = Joi.object({
  approvalNotes: Joi.string().trim().max(500)
    .messages({
      'string.max': 'Approval notes cannot exceed 500 characters'
    }),
  activateSubscription: Joi.boolean().default(true)
    .messages({
      'boolean.base': 'Activate subscription must be a boolean value'
    })
});

/**
 * Reject payment transaction validation
 */
const rejectPaymentTransactionParams = Joi.object({
  paymentId: Joi.string().required().trim()
    .messages({
      'string.empty': 'Payment ID cannot be empty',
      'any.required': 'Payment ID is required'
    })
});

const rejectPaymentTransaction = Joi.object({
  rejectionReason: Joi.string().required().trim().max(200)
    .messages({
      'string.empty': 'Rejection reason cannot be empty',
      'string.max': 'Rejection reason cannot exceed 200 characters',
      'any.required': 'Rejection reason is required'
    }),
  rejectionNotes: Joi.string().trim().max(500)
    .messages({
      'string.max': 'Rejection notes cannot exceed 500 characters'
    })
});

/**
 * Export payment transactions validation
 */
const exportPaymentTransactions = Joi.object({
  format: Joi.string().valid('csv', 'pdf').default('csv')
    .messages({
      'any.only': 'Export format must be either csv or pdf'
    }),
  status: Joi.string().valid('pending', 'processing', 'success', 'failed', 'approved', 'rejected')
    .messages({
      'any.only': 'Status must be one of: pending, processing, success, failed, approved, rejected'
    }),
  paymentMethod: Joi.string().valid('cash', 'bank_transfer', 'mobile_money', 'card', 'other', 'offline', 'evc_plus')
    .messages({
      'any.only': 'Payment method must be one of: cash, bank_transfer, mobile_money, card, other, offline, evc_plus'
    }),
  startDate: Joi.date().iso()
    .messages({
      'date.base': 'Start date must be a valid date',
      'date.format': 'Start date must be in ISO format'
    }),
  endDate: Joi.date().iso().min(Joi.ref('startDate'))
    .messages({
      'date.base': 'End date must be a valid date',
      'date.format': 'End date must be in ISO format',
      'date.min': 'End date must be after start date'
    }),
  search: Joi.string().trim().max(100)
    .messages({
      'string.max': 'Search term cannot exceed 100 characters'
    })
});

/**
 * Get payment statistics validation
 */
const getPaymentStats = Joi.object({
  startDate: Joi.date().iso()
    .messages({
      'date.base': 'Start date must be a valid date',
      'date.format': 'Start date must be in ISO format'
    }),
  endDate: Joi.date().iso().min(Joi.ref('startDate'))
    .messages({
      'date.base': 'End date must be a valid date',
      'date.format': 'End date must be in ISO format',
      'date.min': 'End date must be after start date'
    })
});

module.exports = {
  getPaymentTransactions,
  getPaymentTransactionById,
  approvePaymentTransaction,
  approvePaymentTransactionParams,
  rejectPaymentTransaction,
  rejectPaymentTransactionParams,
  exportPaymentTransactions,
  getPaymentStats
}; 