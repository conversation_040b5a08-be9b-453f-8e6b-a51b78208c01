/**
 * Authentication validation and token checking utilities
 */
import { toast } from "sonner";
import AuthService from "@/lib/services/auth";
import { errorHandlers } from "@/lib/api/contract";
import { shouldUseFallbackAuth, getFallbackUserData, storeFallbackUserData, resetAuthFallbackCounters } from "@/lib/api/auth-fallback";

/**
 * Check token expiry and set up warning if needed
 * @param {string} accessToken - JWT access token
 */
export const checkTokenExpiry = (accessToken) => {
  if (!accessToken || typeof window === 'undefined') return;
  
  try {
    // Attempt to decode the token to get expiry time
    const tokenParts = accessToken.split('.');
    if (tokenParts.length === 3) {
      const base64Url = tokenParts[1];
      const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
      const jsonPayload = decodeURIComponent(window.atob(base64).split('').map(function(c) {
        return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
      }).join(''));
      
      const payload = JSON.parse(jsonPayload);
      if (payload.exp) {
        const expiryTime = payload.exp * 1000; // Convert to milliseconds
        const now = Date.now();
        const timeUntilExpiry = Math.max(0, expiryTime - now);
        
        // If token expires within the next 5 minutes, set up a warning
        if (timeUntilExpiry > 0 && timeUntilExpiry < 300000) {
          // Schedule warning 1 minute before expiry
          const warningTime = timeUntilExpiry - 60000;
          
          if (warningTime > 0) {
            console.log(`[Auth] Session expires in ${Math.round(timeUntilExpiry/1000)}s, warning in ${Math.round(warningTime/1000)}s`);
            setTimeout(() => {
              if (typeof window !== 'undefined') {
                window.dispatchEvent(new CustomEvent('auth:session:expiring'));
              }
            }, warningTime);
          }
        }
      }
    }
  } catch (error) {
    console.error('[Auth] Error setting up session warning:', error);
  }
};

/**
 * Check if user is authenticated and retrieve user profile
 * @param {Object} options - Configuration options
 * @param {Object} options.authCheckInProgress - Ref to track if auth check is in progress
 * @param {Object} options.lastAuthCheckTime - Ref to track last auth check time
 * @param {number} options.authCheckInterval - Interval between auth checks
 * @param {Function} options.setUser - Function to update user state
 * @param {Function} options.setIsAuthenticated - Function to update authentication state
 * @param {Function} options.setLoading - Function to update loading state
 * @param {Function} options.setError - Function to update error state
 * @param {Object} options.router - Next.js router
 * @returns {Promise<void>}
 */
export const checkAuth = async ({
  authCheckInProgress,
  lastAuthCheckTime,
  authCheckInterval,
  setUser,
  setIsAuthenticated,
  setLoading,
  setError,
  router,
  userUpdatedManually,
  manualUpdateTimestamp
}) => {
  // Prevent concurrent auth checks
  if (authCheckInProgress.current) {
    console.log("[Auth] Auth check already in progress, skipping");
    return;
  }

  // Throttle auth checks to prevent excessive API calls
  const currentTime = Date.now();
  if (currentTime - lastAuthCheckTime.current < authCheckInterval) {
    console.log("[Auth] Skipping auth check due to throttling");
    return;
  }
  
  // Skip auth check if user was manually updated recently (within last 30 seconds)
  if (userUpdatedManually && userUpdatedManually.current && manualUpdateTimestamp && 
      (currentTime - manualUpdateTimestamp.current < 30000)) {
    console.log("[Auth] Skipping auth check due to recent manual user update");
    setLoading(false);
    return;
  }
  
  try {
    // Don't attempt auth check if we're in a redirect loop
    const lastRedirectTime = parseInt(sessionStorage.getItem('lastAuthRedirect') || '0');
    
    if (currentTime - lastRedirectTime < 3000) {
      console.log("[Auth] Skipping auth check due to recent redirect");
      setLoading(false);
      return;
    }
    
    // Mark auth check as in progress
    authCheckInProgress.current = true;
    lastAuthCheckTime.current = currentTime;
    
    // Check token expiry and set up warning if needed
    const accessToken = localStorage.getItem("accessToken");
    checkTokenExpiry(accessToken);
    
    // Check for tokens in localStorage
    const refreshToken = localStorage.getItem("refreshToken");
    
    if (!accessToken) {
      console.log("[Auth] No access token found");
      
      // Define public pages that don't require authentication
      const publicPages = ['/', '/login', '/contact', '/about', '/pricing'];
      const isPublicPage = publicPages.includes(window.location.pathname) || 
                          window.location.pathname.includes('/reset-password');
      
      // Only redirect to login if not on a public page
      if (!isPublicPage) {
        console.log("[Auth] Redirecting to login due to missing token");
        sessionStorage.setItem('lastAuthRedirect', Date.now().toString());
        router.push('/login');
      }
      
      setLoading(false);
      return;
    }
    
    if (!refreshToken) {
      console.log("[Auth] No refresh token found, auth may fail");
    }
    
    // We'll use a try-catch inside our main try block to isolate API errors
    let userData = null;
    let apiError = null;
    
    // Check for inactive account status - only if we have an access token
    const accountStatus = localStorage.getItem('accountStatus');
    
    // If we have an access token, we should attempt a backend validation regardless
    // The backend response will determine the actual account status
    if (accessToken) {
      console.log('[Auth] Access token found, attempting backend validation');
    
      // Check if we should use fallback authentication due to server issues
      if (shouldUseFallbackAuth()) {
        console.log("[Auth] Using fallback authentication due to server issues");
        
        // Get fallback user data from localStorage
        const fallbackData = getFallbackUserData();
        if (fallbackData) {
          console.log("[Auth] Using cached user data as fallback", fallbackData);
          userData = fallbackData;
          
          // Set user data and authentication state
          setUser(fallbackData);
          setIsAuthenticated(true);
          setLoading(false);
          
          // Show a warning to the user
          toast.warning("Using cached authentication data due to server issues. Some features may be limited.");
          return;
        } else {
          console.warn("[Auth] No fallback user data available");
        }
      }
      
      try {
        console.log("[Auth] Validating token with backend");
        // Set a timeout promise to prevent hanging requests
        const timeoutPromise = new Promise((_, reject) => {
          setTimeout(() => reject(new Error('Auth check timeout')), 15000);
        });
        
        // Use Promise.race to implement a timeout
        userData = await Promise.race([
          AuthService.getProfile(),
          timeoutPromise
        ]);
        
        // Store the user data for fallback
        storeFallbackUserData(userData);
        
        // If we got here, reset the fallback counters
        resetAuthFallbackCounters();
        
        console.log("[Auth] User data from backend:", JSON.stringify(userData, null, 2));
      } catch (profileError) {
        apiError = profileError;
        console.error("[Auth] Error fetching user profile:", profileError);
        
        // Special handling for server errors
        if (profileError.response?.status === 500) {
          console.warn("[Auth] Server error during profile fetch, treating as temporary issue");
          
          // Check for fallback data
          const fallbackData = getFallbackUserData();
          if (fallbackData) {
            console.log("[Auth] Using cached user data after 500 error", fallbackData);
            userData = fallbackData;
            
            // Set user data and authentication state
            setUser(fallbackData);
            setIsAuthenticated(true);
            setLoading(false);
            
            // Show a warning to the user
            toast.warning("Using cached authentication data due to server issues. Some features may be limited.");
            return;
          } else {
            // If the error is 500, we'll assume it's a temporary server error
            // and not immediately invalidate the session
            setLoading(false);
            setError("Server error. Please try again later.");
            return;
          }
        }
      }
    }
    
    // Handle API error scenario
    if (apiError) {
      // For auth errors (401, 403), clear tokens and redirect
      if (apiError.response?.status === 401 || apiError.response?.status === 403) {
        console.log("[Auth] Authentication failed, redirecting to login");
        localStorage.removeItem("accessToken");
        localStorage.removeItem("refreshToken");
        setUser(null);
        setIsAuthenticated(false);
        
        const isLoginPage = window.location.pathname === '/login';
        const isHomePage = window.location.pathname === '/';
        const isContactPage = window.location.pathname === '/contact';
        
        // Only redirect to login if not on a public page
        if (!isLoginPage && !isHomePage && !isContactPage) {
          sessionStorage.setItem('lastAuthRedirect', Date.now().toString());
          router.push('/login');
        }
      }
      
      setLoading(false);
      return;
    }
    
    // Validate that we received proper user data
    if (!userData || typeof userData !== 'object') {
      console.error('[Auth] Invalid user data received:', userData);
      throw new Error('Invalid user data received from server');
    }
    
    // Check account status from backend response
    if (userData.accountStatus === 'inactive') {
      console.log('[Auth] Backend reports account is inactive');
      localStorage.setItem('accountStatus', 'inactive');
      setUser(null);
      setIsAuthenticated(false);
      setLoading(false);
      
      toast.error('Your account has been deactivated by the server. Please contact an administrator.');
      router.push('/login?status=inactive');
      return;
    } else {
      // If account is active, ensure we clear any stale status
      localStorage.removeItem('accountStatus');
    }
    
    console.log("[Auth] User role from backend:", userData.role || 'undefined');
    
    // Store the complete user data from backend instead of a simplified version
    console.log("[Auth] Setting complete user data from backend:", JSON.stringify(userData, null, 2));
    setUser(userData);
    setIsAuthenticated(true);
    
    // Reset any stale inactive flag after successful authentication
    localStorage.removeItem('accountStatus');
    console.log("[Auth] Cleared accountStatus flag after successful authentication");
    
    // Normalize role for case-insensitive comparison
    const normalizedRole = userData.role?.toLowerCase() || '';
    const isSuperAdmin = normalizedRole === 'superadmin';
    
    console.log(`[Auth] User role normalized: ${normalizedRole}`);
    console.log(`[Auth] Is superAdmin: ${isSuperAdmin}`);
    
    // Don't redirect immediately, let role guard handle specific page access
    // This allows the auth context to be more flexible and handle various role combinations
    if (isSuperAdmin) {
      console.log("[Auth] User is a superAdmin, access granted");
    } else {
      console.log("[Auth] User is not a superAdmin, role guard will handle access control");
    }
    
    setLoading(false);
  } catch (err) {
    console.error("[Auth] Authentication error:", err);
    setError(errorHandlers.getErrorMessage(err));
    
    // Clear tokens on auth error
    localStorage.removeItem("accessToken");
    localStorage.removeItem("refreshToken");
    
    // Don't show error notification if already on login page
    const isLoginPage = window.location.pathname === '/login';
    if (!isLoginPage) {
      toast.error(errorHandlers.getErrorMessage(err) || "Authentication error");
    }
    
    setUser(null);
    setIsAuthenticated(false);
    setLoading(false);
  } finally {
    // Always release the lock after completion
    authCheckInProgress.current = false;
  }
};
