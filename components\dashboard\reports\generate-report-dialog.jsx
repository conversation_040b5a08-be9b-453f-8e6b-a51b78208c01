"use client";

import { useState } from 'react';
import { useAuth } from '@/contexts/auth-context';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { toast } from 'sonner';
import { FileText } from 'lucide-react';

// Form validation schema for Core Feature Export
const formSchema = z.object({
  coreFeature: z.enum(['users', 'shops', 'plans', 'subscriptions'], {
    required_error: 'Please select a core feature to export',
  }),
  format: z.enum(['csv'], {
    required_error: 'CSV format is required',
  }),
});

/**
 * Dialog to export core features (SuperAdmin only)
 */
export function GenerateReportDialog({ isOpen, onClose }) {
  const { user } = useAuth();
  const [isExporting, setIsExporting] = useState(false);
  
  // Set up form for core feature export
  const form = useForm({
    resolver: zodResolver(formSchema),
    defaultValues: {
      coreFeature: 'users',
      format: 'csv',
    },
  });

  /**
   * Handle core feature export
   */
  const onSubmit = async (data) => {
    setIsExporting(true);
    
    try {
      // Call core feature export API
      const exportUrl = `/api/export/core-feature?feature=${data.coreFeature}&format=${data.format}`;
      
      // Create download link
      const response = await fetch(exportUrl, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
        },
      });

      if (!response.ok) {
        throw new Error('Export failed');
      }

      // Download the CSV file
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `${data.coreFeature}_export_${new Date().toISOString().split('T')[0]}.csv`;
      document.body.appendChild(link);
      link.click();
      link.remove();
      window.URL.revokeObjectURL(url);
      
      toast.success(`${getCoreFeatureName(data.coreFeature)} exported successfully`);
      onClose(true);
    } catch (error) {
      console.error('Error exporting core feature:', error);
      toast.error('Failed to export core feature');
    } finally {
      setIsExporting(false);
    }
  };

  /**
   * Get core feature name
   */
  const getCoreFeatureName = (feature) => {
    const names = {
      'users': 'User Management',
      'shops': 'Shop Management',
      'plans': 'Plan Management',
      'subscriptions': 'Subscription Management'
    };
    return names[feature] || feature;
  };

  /**
   * Get core feature description
   */
  const getCoreFeatureDescription = (feature) => {
    const descriptions = {
      'users': 'Export all user accounts, roles, and activity data',
      'shops': 'Export all shop information, status, and configurations', 
      'plans': 'Export all subscription plans, pricing, and features',
      'subscriptions': 'Export all subscription data, payments, and status'
    };
    return descriptions[feature] || '';
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Export Core Features</DialogTitle>
          <DialogDescription>
            Choose a core feature to export as CSV file (SuperAdmin only)
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">

            <FormField
              control={form.control}
              name="coreFeature"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Core Feature to Export</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select core feature" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent className="bg-background border border-border">
                      <SelectItem value="users" className="bg-background hover:bg-muted">
                        <div className="flex items-center">
                          <FileText className="mr-2 h-4 w-4" />
                          <span>User Management</span>
                        </div>
                      </SelectItem>
                      <SelectItem value="shops" className="bg-background hover:bg-muted">
                        <div className="flex items-center">
                          <FileText className="mr-2 h-4 w-4" />
                          <span>Shop Management</span>
                        </div>
                      </SelectItem>
                      <SelectItem value="plans" className="bg-background hover:bg-muted">
                        <div className="flex items-center">
                          <FileText className="mr-2 h-4 w-4" />
                          <span>Plan Management</span>
                        </div>
                      </SelectItem>
                      <SelectItem value="subscriptions" className="bg-background hover:bg-muted">
                        <div className="flex items-center">
                          <FileText className="mr-2 h-4 w-4" />
                          <span>Subscription Management</span>
                        </div>
                      </SelectItem>
                    </SelectContent>
                  </Select>
                  <FormDescription>
                    {getCoreFeatureDescription(form.watch('coreFeature'))}
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="format"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Export Format</FormLabel>
                  <FormControl>
                    <div className="flex items-center space-x-2 p-3 border rounded-md bg-muted">
                      <FileText className="h-4 w-4" />
                      <span>CSV Spreadsheet (Only supported format)</span>
                    </div>
                  </FormControl>
                  <FormDescription>
                    Core features are exported as CSV files for easy data analysis
                  </FormDescription>
                </FormItem>
              )}
            />



            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => onClose(false)}
                disabled={isExporting}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={isExporting}>
                {isExporting ? 'Exporting...' : 'Export CSV'}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
