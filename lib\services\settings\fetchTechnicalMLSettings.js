/**
 * Fetch Technical ML Settings Service
 * 
 * Retrieves SuperAdmin-only technical ML configuration settings
 * Maps to backend settings: ml_api_base_url, ml_api_key, ml_predict_endpoint, ml_prediction_timeout, risk_data_retention_days
 */
import { getSettings } from '../../api/modules/settings';
import { handleError } from '../baseService';

/**
 * Fetch technical ML settings that SuperAdmin users can manage
 * @returns {Object} Technical ML settings data with SuperAdmin-only options
 */
const fetchTechnicalMLSettings = async () => {
  try {
    const response = await getSettings('ml');
    
    // Filter to only show SuperAdmin-accessible ML settings
    const superAdminAccessibleSettings = response.data?.filter(setting => 
      setting.accessLevel === 'superAdmin'
    ) || [];
    
    // Format settings for technical configuration
    const formattedTechnicalMLSettings = {
      mlApiBaseUrl: '',
      mlApiKey: '',
      mlPredictEndpoint: '/predict_single/',
      mlPredictionTimeout: 10,
      riskDataRetentionDays: 365
    };
    
    // Map actual settings to formatted structure
    superAdminAccessibleSettings.forEach(setting => {
      switch (setting.key) {
        case 'ml_api_base_url':
          formattedTechnicalMLSettings.mlApiBaseUrl = setting.value || '';
          break;
        case 'ml_api_key':
          formattedTechnicalMLSettings.mlApiKey = setting.value || '';
          break;
        case 'ml_predict_endpoint':
          formattedTechnicalMLSettings.mlPredictEndpoint = setting.value || '/predict_single/';
          break;
        case 'ml_prediction_timeout':
          formattedTechnicalMLSettings.mlPredictionTimeout = setting.value || 10;
          break;
        case 'risk_data_retention_days':
          formattedTechnicalMLSettings.riskDataRetentionDays = setting.value || 365;
          break;
      }
    });
    
    return formattedTechnicalMLSettings;
  } catch (error) {
    handleError(error, 'SettingsService.fetchTechnicalMLSettings', true);
    return {
      mlApiBaseUrl: '',
      mlApiKey: '',
      mlPredictEndpoint: '/predict_single/',
      mlPredictionTimeout: 10,
      riskDataRetentionDays: 365
    };
  }
};

export default fetchTechnicalMLSettings; 