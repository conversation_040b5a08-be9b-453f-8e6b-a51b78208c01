import apiBridge from '@/lib/api/bridge';
import { ENDPOINTS } from '@/lib/api/contract';;
import { handleError, processApiResponse } from '../baseService';

/**
 * Process all pending payment retries (SuperAdmin only)
 * @returns {Promise<Object>} Processing results
 */
async function processAllPendingRetries() {
  try {
    const response = await apiBridge.post(`${ENDPOINTS.SUBSCRIPTIONS.BASE}/payment-retry/process-all`, {}, {
      clearCacheEndpoint: ENDPOINTS.SUBSCRIPTIONS.BASE
    });
    
    const result = processApiResponse(response, 'All pending payment retries processed successfully');
    return result;
  } catch (error) {
    handleError(error, 'SubscriptionService.processAllPendingRetries', true);
    throw error;
  }
}

export default processAllPendingRetries; 
