/**
 * ML Settings Form Component
 * Handles admin-accessible ML settings (for mobile app usage)
 * Matches backend: ml_enabled, ml_auto_trigger_on_due, ml_auto_trigger_on_payment_update, store_risk_score_in_db
 */

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Button } from '../ui/button';
import { Switch } from '../ui/switch';
import { Label } from '../ui/label';
import { Alert, AlertDescription } from '../ui/alert';
import { CheckCircle, XCircle, Loader2, Brain, Database, Clock, CreditCard } from 'lucide-react';
import { useMLSettingsQuery } from '../../hooks/use-ml-settings-query';
import { getErrorMessage } from '../../lib/utils/errorHandler';

/**
 * ML Settings Form Component
 * @param {Object} props - Component props
 * @param {Function} [props.onSave] - Callback when settings are saved successfully
 */
export default function MLSettingsForm({ onSave }) {
  const {
    mlSettings,
    isLoadingMLSettings,
    isSavingMLSettings,
    updateMLSettings,
    mlSettingsError
  } = useMLSettingsQuery();

  // Form state matching backend ML settings structure
  const [formData, setFormData] = useState({
    mlEnabled: false,
    autoTriggerOnDue: false,
    autoTriggerOnPaymentUpdate: false,
    storeRiskScoreInDB: false
  });

  const [saveStatus, setSaveStatus] = useState(null);

  // Load existing settings into form
  useEffect(() => {
    if (!isLoadingMLSettings && mlSettings) {
      console.log('[MLSettingsForm] Loading ML settings:', mlSettings);
      setFormData({
        mlEnabled: mlSettings.mlEnabled || false,
        autoTriggerOnDue: mlSettings.autoTriggerOnDue || false,
        autoTriggerOnPaymentUpdate: mlSettings.autoTriggerOnPaymentUpdate || false,
        storeRiskScoreInDB: mlSettings.storeRiskScoreInDB || false
      });
    }
  }, [isLoadingMLSettings, mlSettings]);

  // Clear status messages after delay
  useEffect(() => {
    if (saveStatus) {
      const timer = setTimeout(() => setSaveStatus(null), 5000);
      return () => clearTimeout(timer);
    }
  }, [saveStatus]);

  const handleSwitchChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    try {
      console.log('[MLSettingsForm] Submitting ML settings:', formData);

      const result = await updateMLSettings(formData);
      
      if (result && result.success) {
        setSaveStatus({ type: 'success', message: 'ML settings updated successfully' });
        onSave?.(result);
      } else {
        setSaveStatus({ type: 'error', message: 'Failed to update ML settings' });
      }
    } catch (error) {
      console.error('[MLSettingsForm] Save error:', error);
      setSaveStatus({ 
        type: 'error', 
        message: getErrorMessage(error, 'Failed to update ML settings')
      });
    }
  };

  if (isLoadingMLSettings) {
    return (
      <Card className="bg-gradient-to-br from-blue-50 to-indigo-100 border-blue-200 shadow-lg">
        <CardContent className="flex items-center justify-center p-8">
          <div className="text-center">
            <Loader2 className="w-8 h-8 animate-spin text-blue-600 mx-auto mb-3" />
            <p className="text-blue-800 font-medium">Loading ML settings...</p>
            <p className="text-blue-600 text-sm mt-1">Fetching your configuration</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Error State */}
      {mlSettingsError && (
        <Alert variant="destructive" className="border-l-4 border-l-red-500 bg-red-50 shadow-md">
          <XCircle className="h-5 w-5 text-red-600" />
          <AlertDescription className="text-red-800">
            Failed to load ML settings: {getErrorMessage(mlSettingsError)}
          </AlertDescription>
        </Alert>
      )}

      {/* Save Status */}
      {saveStatus && (
        <Alert 
          variant={saveStatus.type === 'success' ? 'default' : 'destructive'} 
          className={`border-l-4 shadow-md ${
            saveStatus.type === 'success' 
              ? 'border-l-green-500 bg-green-50' 
              : 'border-l-red-500 bg-red-50'
          }`}
        >
          {saveStatus.type === 'success' ? (
            <CheckCircle className="h-5 w-5 text-green-600" />
          ) : (
            <XCircle className="h-5 w-5 text-red-600" />
          )}
          <AlertDescription className={saveStatus.type === 'success' ? 'text-green-800' : 'text-red-800'}>
            {String(saveStatus.message || 'Status update')}
          </AlertDescription>
        </Alert>
      )}

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* ML Engine Enable/Disable */}
        <div className="group relative bg-gradient-to-r from-blue-50 to-indigo-50 border-2 border-blue-200 hover:border-blue-300 rounded-xl p-6 transition-all duration-200 hover:shadow-lg">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="p-3 bg-blue-100 rounded-xl group-hover:bg-blue-200 transition-colors">
                <Brain className="w-6 h-6 text-blue-700" />
              </div>
              <div>
                <Label htmlFor="mlEnabled" className="text-lg font-semibold text-blue-900 cursor-pointer">
                  Enable ML Engine
                </Label>
                <p className="text-blue-700 mt-1">
                  Master switch for the ML risk assessment engine
                </p>
              </div>
            </div>
            <div className="flex items-center gap-3">
              <div className={`px-3 py-1 rounded-full text-xs font-medium ${
                formData.mlEnabled 
                  ? 'bg-green-100 text-green-800' 
                  : 'bg-gray-100 text-gray-600'
              }`}>
                {formData.mlEnabled ? 'ACTIVE' : 'INACTIVE'}
              </div>
              <Switch
                id="mlEnabled"
                checked={formData.mlEnabled}
                onCheckedChange={(checked) => handleSwitchChange('mlEnabled', checked)}
                className="data-[state=checked]:bg-blue-600"
              />
            </div>
          </div>
        </div>

        {/* Auto Trigger on Due */}
        <div className={`group relative bg-gradient-to-r from-orange-50 to-yellow-50 border-2 rounded-xl p-6 transition-all duration-200 ${
          formData.mlEnabled 
            ? 'border-orange-200 hover:border-orange-300 hover:shadow-lg' 
            : 'border-gray-200 opacity-60'
        }`}>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className={`p-3 rounded-xl transition-colors ${
                formData.mlEnabled 
                  ? 'bg-orange-100 group-hover:bg-orange-200' 
                  : 'bg-gray-100'
              }`}>
                <Clock className={`w-6 h-6 ${formData.mlEnabled ? 'text-orange-700' : 'text-gray-400'}`} />
              </div>
              <div>
                <Label htmlFor="autoTriggerOnDue" className={`text-lg font-semibold cursor-pointer ${
                  formData.mlEnabled ? 'text-orange-900' : 'text-gray-500'
                }`}>
                  Auto Trigger on Due Date
                </Label>
                <p className={`mt-1 ${formData.mlEnabled ? 'text-orange-700' : 'text-gray-400'}`}>
                  Automatically run ML analysis when payment due dates pass
                </p>
              </div>
            </div>
            <div className="flex items-center gap-3">
              <div className={`px-3 py-1 rounded-full text-xs font-medium ${
                formData.autoTriggerOnDue && formData.mlEnabled
                  ? 'bg-green-100 text-green-800' 
                  : 'bg-gray-100 text-gray-600'
              }`}>
                {formData.autoTriggerOnDue && formData.mlEnabled ? 'ENABLED' : 'DISABLED'}
              </div>
              <Switch
                id="autoTriggerOnDue"
                checked={formData.autoTriggerOnDue}
                disabled={!formData.mlEnabled}
                onCheckedChange={(checked) => handleSwitchChange('autoTriggerOnDue', checked)}
                className="data-[state=checked]:bg-orange-600"
              />
            </div>
          </div>
        </div>

        {/* Auto Trigger on Payment Update */}
        <div className={`group relative bg-gradient-to-r from-green-50 to-emerald-50 border-2 rounded-xl p-6 transition-all duration-200 ${
          formData.mlEnabled 
            ? 'border-green-200 hover:border-green-300 hover:shadow-lg' 
            : 'border-gray-200 opacity-60'
        }`}>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className={`p-3 rounded-xl transition-colors ${
                formData.mlEnabled 
                  ? 'bg-green-100 group-hover:bg-green-200' 
                  : 'bg-gray-100'
              }`}>
                <CreditCard className={`w-6 h-6 ${formData.mlEnabled ? 'text-green-700' : 'text-gray-400'}`} />
              </div>
              <div>
                <Label htmlFor="autoTriggerOnPaymentUpdate" className={`text-lg font-semibold cursor-pointer ${
                  formData.mlEnabled ? 'text-green-900' : 'text-gray-500'
                }`}>
                  Auto Trigger on Payment Update
                </Label>
                <p className={`mt-1 ${formData.mlEnabled ? 'text-green-700' : 'text-gray-400'}`}>
                  Automatically run ML analysis when payment records are updated
                </p>
              </div>
            </div>
            <div className="flex items-center gap-3">
              <div className={`px-3 py-1 rounded-full text-xs font-medium ${
                formData.autoTriggerOnPaymentUpdate && formData.mlEnabled
                  ? 'bg-green-100 text-green-800' 
                  : 'bg-gray-100 text-gray-600'
              }`}>
                {formData.autoTriggerOnPaymentUpdate && formData.mlEnabled ? 'ENABLED' : 'DISABLED'}
              </div>
              <Switch
                id="autoTriggerOnPaymentUpdate"
                checked={formData.autoTriggerOnPaymentUpdate}
                disabled={!formData.mlEnabled}
                onCheckedChange={(checked) => handleSwitchChange('autoTriggerOnPaymentUpdate', checked)}
                className="data-[state=checked]:bg-green-600"
              />
            </div>
          </div>
        </div>

        {/* Store Risk Score in Database */}
        <div className={`group relative bg-gradient-to-r from-purple-50 to-violet-50 border-2 rounded-xl p-6 transition-all duration-200 ${
          formData.mlEnabled 
            ? 'border-purple-200 hover:border-purple-300 hover:shadow-lg' 
            : 'border-gray-200 opacity-60'
        }`}>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className={`p-3 rounded-xl transition-colors ${
                formData.mlEnabled 
                  ? 'bg-purple-100 group-hover:bg-purple-200' 
                  : 'bg-gray-100'
              }`}>
                <Database className={`w-6 h-6 ${formData.mlEnabled ? 'text-purple-700' : 'text-gray-400'}`} />
              </div>
              <div>
                <Label htmlFor="storeRiskScoreInDB" className={`text-lg font-semibold cursor-pointer ${
                  formData.mlEnabled ? 'text-purple-900' : 'text-gray-500'
                }`}>
                  Store Risk Scores in Database
                </Label>
                <p className={`mt-1 ${formData.mlEnabled ? 'text-purple-700' : 'text-gray-400'}`}>
                  Save ML risk assessment results for historical analysis
                </p>
              </div>
            </div>
            <div className="flex items-center gap-3">
              <div className={`px-3 py-1 rounded-full text-xs font-medium ${
                formData.storeRiskScoreInDB && formData.mlEnabled
                  ? 'bg-green-100 text-green-800' 
                  : 'bg-gray-100 text-gray-600'
              }`}>
                {formData.storeRiskScoreInDB && formData.mlEnabled ? 'STORING' : 'NOT STORING'}
              </div>
              <Switch
                id="storeRiskScoreInDB"
                checked={formData.storeRiskScoreInDB}
                disabled={!formData.mlEnabled}
                onCheckedChange={(checked) => handleSwitchChange('storeRiskScoreInDB', checked)}
                className="data-[state=checked]:bg-purple-600"
              />
            </div>
          </div>
        </div>

        {/* Submit Button */}
        <div className="pt-8">
          <Button
            type="submit"
            disabled={isSavingMLSettings}
            className="w-full h-14 text-lg font-semibold bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-[1.02]"
          >
            {isSavingMLSettings ? (
              <div className="flex items-center gap-3">
                <Loader2 className="w-6 h-6 animate-spin" />
                <span>Saving ML Settings...</span>
                <div className="flex gap-1">
                  <div className="w-1 h-1 bg-white rounded-full animate-pulse"></div>
                  <div className="w-1 h-1 bg-white rounded-full animate-pulse delay-75"></div>
                  <div className="w-1 h-1 bg-white rounded-full animate-pulse delay-150"></div>
                </div>
              </div>
            ) : (
              <div className="flex items-center gap-3">
                <Brain className="w-6 h-6" />
                <span>Save ML Settings</span>
                <CheckCircle className="w-5 h-5 opacity-70" />
              </div>
            )}
          </Button>
        </div>

        {/* Debug Info (for development) */}
        {process.env.NODE_ENV === 'development' && (
          <div className="mt-8 p-6 bg-gradient-to-br from-gray-50 to-gray-100 rounded-xl border border-gray-200 shadow-sm">
            <details className="group">
              <summary className="cursor-pointer font-semibold text-gray-700 flex items-center gap-2 hover:text-gray-900 transition-colors">
                <div className="w-2 h-2 bg-gray-400 rounded-full group-open:bg-blue-500 transition-colors"></div>
                Debug Information
              </summary>
              <div className="mt-4 bg-gray-800 p-4 rounded-lg">
                <pre className="text-xs text-green-400 font-mono overflow-x-auto">
                  {JSON.stringify({
                    formData,
                    backendData: mlSettings,
                    timestamp: new Date().toISOString()
                  }, null, 2)}
                </pre>
              </div>
            </details>
          </div>
        )}
      </form>
    </div>
  );
} 