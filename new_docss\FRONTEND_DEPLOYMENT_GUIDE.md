# DeynCare Frontend Deployment Guide

## Environment Configuration Template

Create a `.env.template` file in your frontend repository with the following content:

```bash
# DeynCare Frontend Environment Configuration Template
# Copy this file to .env.local for development or .env.production for production

# Application Configuration
NEXT_PUBLIC_APP_NAME=DeynCare

# Environment Mode
# Options: development, production, test
NODE_ENV=development

# API Configuration
# Development API URL (local backend)
NEXT_PUBLIC_API_URL=http://localhost:5000

# Production API URL (uncomment and update for production deployment)
# NEXT_PUBLIC_API_URL=https://deyncare-backend.khanciye.com

# Frontend URL Configuration
# Development URL
NEXT_PUBLIC_FRONTEND_URL=http://localhost:3000

# Production URL (uncomment for production)
# NEXT_PUBLIC_FRONTEND_URL=https://deyncare.cajiibcreative.com

# Security Configuration
# Add any security-related environment variables here if needed

# Optional: Additional Configuration
# NEXT_PUBLIC_APP_VERSION=1.0.0
# NEXT_PUBLIC_ENVIRONMENT_NAME=Production

# For production deployment, also consider:
# NEXT_PUBLIC_SENTRY_DSN=your-sentry-dsn-here
# NEXT_PUBLIC_ANALYTICS_ID=your-analytics-id-here
```

## Production Environment File (.env.production)

For your VPS deployment, create a `.env.production` file with:

```bash
# Production Configuration
NEXT_PUBLIC_APP_NAME=DeynCare
NODE_ENV=production
NEXT_PUBLIC_API_URL=https://deyncare-backend.khanciye.com
NEXT_PUBLIC_FRONTEND_URL=https://deyncare.cajiibcreative.com
NEXT_PUBLIC_APP_VERSION=1.0.0
NEXT_PUBLIC_ENVIRONMENT_NAME=Production
```

## Deployment Steps

### 1. VPS Setup
```bash
# Connect to your VPS
ssh root@your-vps-ip

# Update system
apt update && apt upgrade -y

# Install Node.js (recommended: use nvm)
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash
source ~/.bashrc
nvm install 18
nvm use 18
nvm alias default 18

# Install PM2 for process management
npm install -g pm2

# Install Nginx
apt install nginx -y
```

### 2. Clone and Setup Frontend
```bash
# Navigate to web directory
cd /var/www

# Clone your frontend repository
git clone https://github.com/your-username/deyncare-frontend.git
cd deyncare-frontend

# Install dependencies
npm install

# Create production environment file
cp .env.template .env.production

# Edit the environment file with production values
nano .env.production
```

### 3. Build and Start Application
```bash
# Build the Next.js application
npm run build

# Start with PM2
pm2 start npm --name "deyncare-frontend" -- start

# Save PM2 configuration
pm2 save
pm2 startup
```

### 4. Nginx Configuration

Create `/etc/nginx/sites-available/deyncare-frontend`:

```nginx
server {
    listen 80;
    server_name deyncare.cajiibcreative.com;

    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
```

Enable the site:
```bash
ln -s /etc/nginx/sites-available/deyncare-frontend /etc/nginx/sites-enabled/
nginx -t
systemctl reload nginx
```

### 5. SSL Certificate (Optional but Recommended)
```bash
# Install Certbot
apt install certbot python3-certbot-nginx -y

# Get SSL certificate
certbot --nginx -d deyncare.cajiibcreative.com
```

### 6. Environment Variables for Production

Make sure your `.env.production` file contains:

```bash
NEXT_PUBLIC_APP_NAME=DeynCare
NODE_ENV=production
NEXT_PUBLIC_API_URL=https://deyncare-backend.khanciye.com
NEXT_PUBLIC_FRONTEND_URL=https://deyncare.cajiibcreative.com
```

### 7. Verify Configuration

Make sure all environment variables are properly set and the backend API is accessible.

## Deployment Script

Create a `deploy.sh` script in your frontend repository:

```bash
#!/bin/bash

echo "🚀 Starting DeynCare Frontend Deployment..."

# Pull latest changes
git pull origin main

# Install dependencies
npm install

# Build application
npm run build

# Restart PM2 process
pm2 restart deyncare-frontend

# Reload Nginx
sudo systemctl reload nginx

echo "✅ Deployment completed successfully!"
echo "🌐 Frontend URL: https://deyncare.cajiibcreative.com"
echo "🔧 Backend URL: https://deyncare-backend.khanciye.com"
```

Make it executable:
```bash
chmod +x deploy.sh
```

## Post-Deployment Verification

1. **Check Application Status:**
   ```bash
   pm2 status
   pm2 logs deyncare-frontend
   ```

2. **Test Frontend Access:**
   - Visit: https://deyncare.cajiibcreative.com
   - Verify API connectivity to backend

3. **Monitor Logs:**
   ```bash
   pm2 logs deyncare-frontend --lines 50
   ```

## Common Issues and Solutions

### Issue: API calls failing
- **Solution:** Verify `NEXT_PUBLIC_API_URL` is correct and backend is running

### Issue: 404 errors on refresh
- **Solution:** Ensure Next.js is built with proper routing configuration

### Issue: Environment variables not working
- **Solution:** Make sure `.env.production` is in the root directory and variables start with `NEXT_PUBLIC_`

## Maintenance Commands

```bash
# View application logs
pm2 logs deyncare-frontend

# Restart application
pm2 restart deyncare-frontend

# Stop application
pm2 stop deyncare-frontend

# Check system resources
pm2 monit

# Update application
cd /var/www/deyncare-frontend && ./deploy.sh
```

## Security Checklist

- [ ] Use HTTPS with SSL certificate
- [ ] Verify environment variables are correct
- [ ] Configure proper CORS on backend
- [ ] Set up firewall rules
- [ ] Regular security updates
- [ ] Monitor application logs
- [ ] Backup strategy in place

## Support

If you encounter issues during deployment:
1. Check PM2 logs: `pm2 logs deyncare-frontend`
2. Check Nginx logs: `tail -f /var/log/nginx/error.log`
3. Verify environment variables are correctly set
4. Ensure backend API is accessible from VPS 