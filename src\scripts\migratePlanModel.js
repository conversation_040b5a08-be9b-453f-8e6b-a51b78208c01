/**
 * Subscription Plan Migration Script
 * 
 * This script migrates existing subscriptions to use the new Plan model
 * by adding planId references while maintaining backward compatibility.
 */
require('dotenv').config();
const mongoose = require('mongoose');
const Subscription = require('../models/subscription.model');
const Plan = require('../models/Plan.model');
const { logInfo, logSuccess, logError } = require('../utils/logger');

async function connectDatabase() {
  try {
    // MongoDB Connection Options - Same as in server.js
    const mongoOptions = {
      useNewUrlParser: true,
      useUnifiedTopology: true,
      autoIndex: true,
      connectTimeoutMS: 10000,
      socketTimeoutMS: 45000,
      family: 4,
      maxPoolSize: 10,
      serverSelectionTimeoutMS: 15000,
      heartbeatFrequencyMS: 10000,
    };

    console.log('Connecting to MongoDB at:', process.env.MONGODB_URI ? 'URI is defined' : 'URI is undefined');
    await mongoose.connect(process.env.MONGODB_URI, mongoOptions);
    logInfo('Database connection established', 'PlanMigration');
    console.log('Database connection successful');
  } catch (error) {
    console.error('Database connection error:', error);
    logError(`Failed to connect to database: ${error.message}`, 'PlanMigration', error);
    process.exit(1);
  }
}

async function ensureDefaultPlans() {
  try {
    const plansCount = await Plan.countDocuments({});
    
    if (plansCount === 0) {
      logInfo('No plans found. Creating default plans...', 'PlanMigration');
      await Plan.createDefaultPlans();
      logSuccess('Default plans created successfully', 'PlanMigration');
    } else {
      logInfo(`Found ${plansCount} existing plans`, 'PlanMigration');
    }
    
    // Return all plans for use in migration
    return await Plan.find({}).lean();
  } catch (error) {
    logError(`Failed to ensure default plans: ${error.message}`, 'PlanMigration', error);
    process.exit(1);
  }
}

async function migrateSubscriptions(plans) {
  try {
    // Create a map for quick lookup of plans by type
    const planMap = plans.reduce((map, plan) => {
      map[plan.type] = plan;
      return map;
    }, {});
    
    // Get all subscriptions that don't have planId
    const subscriptions = await Subscription.find({ planId: { $exists: false } });
    
    logInfo(`Found ${subscriptions.length} subscriptions to migrate`, 'PlanMigration');
    
    let successCount = 0;
    let errorCount = 0;
    
    for (const subscription of subscriptions) {
      try {
        const planType = subscription.plan?.type;
        
        if (!planType) {
          logError(`Subscription ${subscription.subscriptionId} has no plan type`, 'PlanMigration');
          errorCount++;
          continue;
        }
        
        const matchingPlan = planMap[planType];
        
        if (!matchingPlan) {
          logError(`No matching plan found for type: ${planType} in subscription ${subscription.subscriptionId}`, 'PlanMigration');
          errorCount++;
          continue;
        }
        
        // Add planId reference but keep embedded data for backward compatibility
        subscription.planId = matchingPlan.planId;
        
        // Add to metadata for tracking
        if (!subscription.metadata) {
          subscription.metadata = {};
        }
        
        if (!subscription.metadata.migrationHistory) {
          subscription.metadata.migrationHistory = [];
        }
        
        subscription.metadata.migrationHistory.push({
          action: 'plan_model_migration',
          date: new Date(),
          details: {
            fromPlanType: planType,
            toPlanId: matchingPlan.planId,
            migrationVersion: '1.0'
          }
        });
        
        await subscription.save();
        logSuccess(`Updated subscription ${subscription.subscriptionId} with planId ${matchingPlan.planId}`, 'PlanMigration');
        successCount++;
      } catch (subError) {
        logError(`Error migrating subscription ${subscription.subscriptionId}: ${subError.message}`, 'PlanMigration', subError);
        errorCount++;
      }
    }
    
    return { total: subscriptions.length, success: successCount, error: errorCount };
  } catch (error) {
    logError(`Failed during subscription migration: ${error.message}`, 'PlanMigration', error);
    return { total: 0, success: 0, error: 0 };
  }
}

async function runMigration() {
  try {
    logInfo('Starting Plan model migration', 'PlanMigration');
    
    // Connect to database
    await connectDatabase();
    
    // Ensure plans exist
    const plans = await ensureDefaultPlans();
    
    // Migrate subscriptions
    const migrationStats = await migrateSubscriptions(plans);
    
    logInfo(`Migration completed. Total: ${migrationStats.total}, Success: ${migrationStats.success}, Errors: ${migrationStats.error}`, 'PlanMigration');
    
    // Disconnect from database
    await mongoose.disconnect();
    
    if (migrationStats.error > 0) {
      logError(`Migration completed with ${migrationStats.error} errors`, 'PlanMigration');
      process.exit(1);
    } else {
      logSuccess('Migration completed successfully', 'PlanMigration');
      process.exit(0);
    }
  } catch (error) {
    logError(`Migration failed: ${error.message}`, 'PlanMigration', error);
    process.exit(1);
  }
}

// Run the migration when file is executed directly
if (require.main === module) {
  runMigration();
} else {
  // Export for use in other files
  module.exports = { runMigration };
}
