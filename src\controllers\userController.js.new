/**
 * User Controller
 * 
 * This controller has been refactored into a modular structure for better maintainability.
 * Each operation is now in its own file under the src/controllers/user/ directory.
 * 
 * This file re-exports all controller functions for backward compatibility while providing
 * the benefits of a more modular codebase.
 */

// Re-export all user controller functions from the modular structure
const UserController = require('./user');

// For debugging purposes - display all exported functions
console.log('UserController exports:', Object.keys(UserController));

// Export the controller
module.exports = UserController;
