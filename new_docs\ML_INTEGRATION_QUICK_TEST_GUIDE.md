# ML Integration Quick Test Guide

## 🚀 Complete ML Integration Testing Workflow

This guide provides a step-by-step process to test the Machine Learning integration in the DeynCare backend system. The ML system evaluates customer repayment risk using a FairRisk Algorithm.

---

## 📋 Prerequisites

### 1. **Ensure ML Settings are Configured**
Before starting, verify that all ML settings are properly configured:

```http
GET http://localhost:5000/api/settings?category=ml
Authorization: Bearer YOUR_SUPERADMIN_TOKEN
```

**Required Settings:**
- `ml_enabled`: `true`
- `ml_api_base_url`: `https://deyncare-ml.onrender.com/`
- `ml_predict_endpoint`: `/predict_single/`
- `ml_auto_trigger_on_payment_update`: `true`
- `store_risk_score_in_db`: `true`

### 2. **Authentication Required**
You need a valid SuperAdmin or Admin token. Get your token from:
```http
POST http://localhost:5000/api/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "your-password"
}
```

---

## 🧪 Step-by-Step Testing Process

### Recommended Workflow (Best Practice)

**🔄 Proper Database Design Approach:**
1. **Step 1:** Create Customer first
2. **Step 2:** Create Debt referencing Customer ID
3. **Step 3:** Add Payments referencing Debt ID

This approach follows proper database normalization and avoids data duplication.

---

### Step 1: Create a Test Customer

**Purpose:** Create a customer to associate with debt records for ML evaluation.

**Request:**
```http
POST http://localhost:5000/api/customers
Content-Type: application/json
Authorization: Bearer YOUR_TOKEN

{
  "CustomerName": "ML Test Customer",
  "CustomerType": "new",
  "phone": "+252612345678",
  "email": "<EMAIL>",
  "address": "Test Address for ML Integration",
  "category": "regular",
  "notes": "Customer created for ML integration testing"
}
```

**Expected Response:**
```json
{
  "success": true,
  "message": "Customer created successfully",
  "data": {
    "customerId": "CUST-XXXX",
    "CustomerName": "ML Test Customer",
    "CustomerType": "new",
    "phone": "+252612345678",
    "email": "<EMAIL>",
    "address": "Test Address for ML Integration",
    "category": "regular",
    "CustomerID": "C01XXXXXX",
    "shopId": "SHOP_ID"
  }
}
```

**What This Does:**
- Creates a new customer record in MongoDB with proper ML-compatible fields
- Generates a unique `customerId` (e.g., CUST-XXXX) and `CustomerID` for ML
- Associates customer with the current shop (for Admin users)
- Sets up the customer with proper `CustomerType` and `CustomerName` fields required for ML

---

### Step 2: Create a Debt with Short Due Date

**Purpose:** Create a debt record with a short due date to quickly test ML evaluation after payment updates. This approach references the existing customer by ID (best practice).

**Option A: Reference Existing Customer (Recommended) - ⚠️ Requires Enhancement**
```http
POST http://localhost:5000/api/debts
Content-Type: application/json
Authorization: Bearer YOUR_TOKEN

{
  "customerId": "CUST-XXXX",
  "debtAmount": 1000,
  "dueDate": "2025-01-20",
  "description": "Quick ML test debt - short due period",
  "paymentMethod": "cash"
}
```

> **⚠️ Note:** This approach requires backend enhancement. Currently, the validation schema doesn't support `customerId` field. This is the recommended approach for future implementation.

**Option B: Create Debt with New Customer (Current Implementation)**
```http
POST http://localhost:5000/api/debts
Content-Type: application/json
Authorization: Bearer YOUR_TOKEN

{
  "customerName": "ML Test Customer",
  "customerType": "new",
  "phone": "+252612345678",
  "email": "<EMAIL>",
  "address": "Test Address for ML Integration",
  "debtAmount": 1000,
  "dueDate": "2025-01-20",
  "description": "Quick ML test debt - short due period",
  "paymentMethod": "cash"
}
```

**Expected Response:**
```json
{
  "success": true,
  "message": "Debt created successfully",
  "data": {
    "debtId": "DEBT-XXXX",
    "customerId": "CUST-XXXX",
    "customerName": "ML Test Customer",
    "debtAmount": 1000,
    "paidAmount": 0,
    "outstandingDebt": 1000,
    "dueDate": "2025-01-20T00:00:00.000Z",
    "status": "Active Debt",
    "paymentDelay": 0,
    "description": "Quick ML test debt - short due period"
  }
}
```

**What This Does:**
- **Option A (Future Enhancement):** Would reference existing customer by ID, avoiding data duplication
- **Option B (Current Implementation):** Creates debt with embedded customer info or finds existing customer
- Automatically calculates ML-required fields: `outstandingDebt`, `debtPaidRatio`, `paymentDelay`
- Sets debt status to "Active Debt" 
- Links debt to customer record using proper foreign key relationship
- May trigger initial ML evaluation if auto-trigger is enabled

**Best Practice Benefits:**
- ✅ **No Data Duplication:** Customer info stored once in customers table
- ✅ **Data Consistency:** Updates to customer info automatically reflect in all debts
- ✅ **Better Performance:** Smaller debt records, faster queries
- ✅ **Referential Integrity:** Proper database relationships

---

### Step 3: Make a Partial Payment

**Purpose:** Record a partial payment to create realistic data for ML risk evaluation.

**Request:**
```http
POST http://localhost:5000/api/debts/DEBT-XXXX/payments
Content-Type: application/json
Authorization: Bearer YOUR_TOKEN

{
  "amount": 600,
  "paymentDate": "2025-01-18T10:30:00.000Z",
  "paymentMethod": "cash",
  "notes": "Partial payment for ML test - 60% of debt paid"
}
```

**Expected Response:**
```json
{
  "success": true,
  "message": "Payment recorded successfully",
  "data": {
    "paymentId": "PAY-XXXX",
    "debtId": "DEBT-XXXX",
    "amount": 600,
    "paymentMethod": "cash",
    "paymentDate": "2025-01-18T10:30:00.000Z",
    "updatedDebt": {
      "paidAmount": 600,
      "outstandingDebt": 400,
      "debtPaidRatio": 0.6,
      "status": "Partially Paid",
      "paymentDelay": 0
    }
  }
}
```

**What This Does:**
- Records payment in the database
- Updates debt: `paidAmount` = 600, `remainingAmount` = 400
- Changes debt status to "partial"
- **Triggers ML Risk Evaluation** (if `ml_auto_trigger_on_payment_update` is true)

---

### Step 4: Verify ML Risk Evaluation

**Purpose:** Check if the ML system has evaluated the customer's repayment risk.

**Request:**
```http
GET http://localhost:5000/api/debts/DEBT-XXXX
Authorization: Bearer YOUR_TOKEN
```

**Expected Response (with ML data):**
```json
{
  "success": true,
  "data": {
    "debtId": "DEBT-XXXX",
    "customerId": "CUST-XXXX",
    "customerName": "ML Test Customer",
    "debtAmount": 1000,
    "paidAmount": 600,
    "outstandingDebt": 400,
    "debtPaidRatio": 0.6,
    "paymentDelay": 0,
    "status": "Partially Paid",
    "riskLevel": "High Risk",
    "riskScore": 0.8,
    "mlRiskEvaluation": {
      "riskScore": 0.8,
      "riskLevel": "High Risk",
      "evaluatedAt": "2025-01-18T10:30:15.000Z",
      "factors": {
        "debtPaidRatio": 0.6,
        "paymentDelay": 0,
        "outstandingDebt": 400,
        "debtAmount": 1000
      }
    }
  }
}
```

---

## 🧮 Understanding ML Risk Calculation

### FairRisk Algorithm
The ML system uses this formula:
```
Risk_Score = (1 - DebtPaidRatio) + (PaymentDelay ÷ 10) + (OutstandingDebt ÷ DebtAmount)
```

### Example Calculation (from our test):
- **DebtPaidRatio**: 0.6 (60% paid: 600/1000)
- **PaymentDelay**: 0 (paid on time)
- **OutstandingDebt**: 400
- **DebtAmount**: 1000

**Risk Score Calculation:**
```
Risk_Score = (1 - 0.6) + (0 ÷ 10) + (400 ÷ 1000)
Risk_Score = 0.4 + 0 + 0.4 = 0.8
```

### Risk Level Mapping:
- **Low Risk**: Risk Score ≤ 0.3
- **Medium Risk**: 0.3 < Risk Score ≤ 0.6  
- **High Risk**: Risk Score > 0.6

**Our Example Result**: 0.8 = **High Risk** (due to significant outstanding debt)

---

## 🔧 Advanced Testing Scenarios

### Test Case 1: Low Risk Customer
```http
POST http://localhost:5000/api/debts/DEBT-XXXX/payments
Content-Type: application/json
Authorization: Bearer YOUR_TOKEN

{
  "amount": 350,
  "paymentMethod": "mobile_money",
  "notes": "Additional payment - total 95% paid"
}
```
**Expected**: Risk Score ≈ 0.1 (Low Risk)

### Test Case 2: High Risk Customer (Late Payment)
Create debt with due date in the past:
```json
{
  "customerName": "High Risk Test Customer",
  "customerType": "returning",
  "phone": "+252612345679",
  "debtAmount": 2000,
  "dueDate": "2025-01-15",
  "description": "High risk test - overdue payment"
}
```
Pay after due date to create payment delay.

### Test Case 3: Complete Payment
```http
POST http://localhost:5000/api/debts/DEBT-XXXX/payments
Content-Type: application/json
Authorization: Bearer YOUR_TOKEN

{
  "amount": 400,
  "paymentMethod": "mobile_money",
  "notes": "Final payment - debt fully paid"
}
```
**Expected**: Risk Score ≈ 0 (Very Low Risk)

---

## 🐛 Troubleshooting

### Issue 1: ML Evaluation Not Triggered
**Symptoms:** No `mlRiskEvaluation` field in debt response

**Solutions:**
1. Check ML settings:
   ```http
   GET http://localhost:5000/api/settings?category=ml
   ```
2. Verify `ml_auto_trigger_on_payment_update` is `true`
3. Check if ML service is accessible:
   ```http
   GET https://deyncare-ml.onrender.com/
   ```

### Issue 2: ML Service Connection Failed
**Symptoms:** Error messages about ML API connection

**Solutions:**
1. Test ML service directly:
   ```http
   POST https://deyncare-ml.onrender.com/predict_single/
   Content-Type: application/json
   
   {
     "DebtPaidRatio": 0.6,
     "PaymentDelay": 0,
     "OutstandingDebt": 400,
     "DebtAmount": 1000
   }
   ```
2. Check ML API URL setting
3. Verify internet connectivity

### Issue 3: Incorrect Risk Calculation
**Symptoms:** Risk score doesn't match expected calculation

**Solutions:**
1. Verify payment amounts are recorded correctly
2. Check due date vs payment date for delay calculation
3. Review debt amount and remaining amount

---

## 📊 Expected Test Results Summary

| Test Scenario | Paid Amount | Payment Delay | Expected Risk Level | Expected Score Range |
|---------------|-------------|---------------|-------------------|-------------------|
| 60% Paid, On Time | 600/1000 | 0 days | High Risk | ~0.8 |
| 95% Paid, On Time | 950/1000 | 0 days | Low Risk | ~0.1 |
| 100% Paid | 1000/1000 | 0 days | Very Low Risk | ~0.0 |
| 50% Paid, 5 Days Late | 500/1000 | 5 days | High Risk | ~1.0 |

---

## 🎯 Success Criteria

✅ **Integration is successful if:**
1. Customer creation works without errors
2. Debt creation works and generates proper IDs
3. Payment recording updates debt amounts correctly
4. ML evaluation is triggered automatically after payment
5. Risk score calculation matches expected formula
6. Risk level is properly categorized (Low/Medium/High)
7. ML data is stored in database (if `store_risk_score_in_db` is true)

---

## 🔄 Next Steps After Successful Testing

1. **Production Configuration**: Update ML settings for production environment
2. **Monitoring Setup**: Implement logging for ML evaluation failures
3. **Performance Testing**: Test with multiple customers and debts
4. **Cron Job Testing**: Test automated ML evaluation for overdue debts
5. **Dashboard Integration**: Ensure ML risk data appears in admin dashboard

## 🛠️ Recommended Backend Enhancement

### Add Customer ID Reference Support

To implement the proper database design approach, consider adding the following enhancement:

**Update Debt Validation Schema (`src/validations/schemas/debtSchemas.js`):**
```javascript
// Add alternative validation for customer ID reference
createDebtWithCustomerId: {
  body: Joi.object({
    customerId: Joi.string()
      .pattern(/^CUST-[A-Z0-9]+$/)
      .required()
      .messages({
        'string.pattern.base': 'Invalid customer ID format',
        'any.required': 'Customer ID is required'
      }),
    debtAmount: Joi.number().positive().precision(2).required(),
    dueDate: Joi.date().min('now').required(),
    description: Joi.string().max(1000).optional().allow(''),
    paidAmount: Joi.number().min(0).precision(2).optional(),
    paymentMethod: Joi.string().valid('cash', 'bank_transfer', 'mobile_money', 'card', 'other').default('cash').optional()
  })
}
```

**Benefits of This Enhancement:**
- ✅ Follows proper database normalization
- ✅ Reduces data duplication
- ✅ Improves data consistency
- ✅ Better performance with smaller records
- ✅ Proper referential integrity

---

## 📝 Test Data Cleanup

After testing, clean up test data:

```http
DELETE http://localhost:5000/api/debts/DEBT-XXXX
Authorization: Bearer YOUR_TOKEN

DELETE http://localhost:5000/api/customers/CUST-XXXX  
Authorization: Bearer YOUR_TOKEN
```

Or use the database cleanup script:
```bash
node src/scripts/cleanDatabase.js --test-data-only
``` 