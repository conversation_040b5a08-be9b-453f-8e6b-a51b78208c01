import apiBridge from '@/lib/api/bridge';
import { ENDPOINTS } from '@/lib/api/contract';;
import { handleError } from '../baseService';

/**
 * Get subscription statistics (SuperAdmin only)
 * @returns {Promise<Object>} Subscription statistics and analytics
 */
async function getSubscriptionStats() {
  try {
    const response = await apiBridge.get(`${ENDPOINTS.SUBSCRIPTIONS.BASE}/stats`);
    
    // Backend returns { success: true, summary: {...} }
    if (response.data && response.data.success && response.data.summary) {
      return response.data.summary;
    }
    
    console.error('[getSubscriptionStats] Unexpected API response format:', response.data);
    return {};
  } catch (error) {
    handleError(error, 'SubscriptionService.getSubscriptionStats', true);
    throw error;
  }
}

export default getSubscriptionStats; 
