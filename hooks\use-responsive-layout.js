"use client";

import { useState, useEffect, useCallback } from 'react';
import { useResponsive } from './use-responsive';

/**
 * Advanced hook for managing responsive layouts throughout the application
 * 
 * Features:
 * - Responsive sidebar behavior (fixed, collapsible, overlay)
 * - Content area adjustments
 * - Navigation style switching (desktop vs mobile)
 * - Header position handling
 * 
 * @param {Object} options Configuration options
 * @returns {Object} Layout state and controls
 */
export function useResponsiveLayout(options = {}) {
  // Get base responsive information
  const responsive = useResponsive();
  const { isMobile, isTablet, isDesktop } = responsive;
  
  // Default options
  const defaults = {
    sidebarBreakpoint: 'lg', // Breakpoint where sidebar becomes fixed
    headerSticky: true,
    defaultCollapsed: false,
    collapsible: true,
    preserveScroll: true
  };
  
  // Merge default options with provided options
  const config = { ...defaults, ...options };
  
  // Initialize state
  const [state, setState] = useState({
    // Sidebar state
    isSidebarOpen: !isMobile && !config.defaultCollapsed,
    sidebarMode: isDesktop ? 'fixed' : 'overlay',
    
    // Layout measurements
    headerHeight: 64, // Default header height
    contentTop: config.headerSticky ? 64 : 0,
    sidebarWidth: 280, // Default sidebar width
    collapsedSidebarWidth: 80, // Width when collapsed
    
    // Scroll position
    scrollY: 0,
    
    // Mobile nav
    mobileNavVisible: false
  });
  
  // Toggle sidebar
  const toggleSidebar = useCallback(() => {
    setState(prev => ({
      ...prev,
      isSidebarOpen: !prev.isSidebarOpen
    }));
  }, []);
  
  // Set sidebar mode
  const setSidebarMode = useCallback((mode) => {
    setState(prev => ({
      ...prev,
      sidebarMode: mode
    }));
  }, []);
  
  // Toggle mobile nav
  const toggleMobileNav = useCallback(() => {
    setState(prev => ({
      ...prev,
      mobileNavVisible: !prev.mobileNavVisible
    }));
  }, []);
  
  // Update content area measurements when header height changes
  const setHeaderHeight = useCallback((height) => {
    setState(prev => ({
      ...prev,
      headerHeight: height,
      contentTop: config.headerSticky ? height : 0
    }));
  }, [config.headerSticky]);
  
  // Handle scroll events
  const handleScroll = useCallback(() => {
    if (!config.preserveScroll) return;
    
    setState(prev => ({
      ...prev,
      scrollY: window.scrollY
    }));
  }, [config.preserveScroll]);
  
  // Update layout based on responsive breakpoints
  useEffect(() => {
    const sidebarMode = isDesktop ? 'fixed' : isTablet ? 'collapsible' : 'overlay';
    
    setState(prev => ({
      ...prev,
      sidebarMode,
      isSidebarOpen: isDesktop && !config.defaultCollapsed
    }));
  }, [isMobile, isTablet, isDesktop, config.defaultCollapsed]);
  
  // Add scroll event listener
  useEffect(() => {
    if (config.preserveScroll) {
      window.addEventListener('scroll', handleScroll);
      return () => window.removeEventListener('scroll', handleScroll);
    }
  }, [config.preserveScroll, handleScroll]);
  
  // Calculate effective content area
  const contentArea = {
    width: `calc(100% - ${state.isSidebarOpen ? state.sidebarWidth : state.collapsedSidebarWidth}px)`,
    marginLeft: state.sidebarMode === 'fixed' && state.isSidebarOpen ? `${state.sidebarWidth}px` : 
               state.sidebarMode === 'fixed' && !state.isSidebarOpen ? `${state.collapsedSidebarWidth}px` : '0',
    marginTop: `${state.contentTop}px`,
    transition: 'margin 0.3s ease, width 0.3s ease'
  };
  
  // Combine all layout utilities and state
  return {
    // Base responsive information
    responsive,
    
    // Layout state
    layout: {
      ...state,
      contentArea
    },
    
    // Controls
    toggleSidebar,
    setSidebarMode,
    toggleMobileNav,
    setHeaderHeight,
    
    // Helper functions
    isFixedSidebar: state.sidebarMode === 'fixed',
    isOverlaySidebar: state.sidebarMode === 'overlay',
    isCollapsibleSidebar: state.sidebarMode === 'collapsible',
    
    // Styles
    styles: {
      header: {
        position: config.headerSticky ? 'fixed' : 'relative',
        width: '100%',
        zIndex: 40,
        height: `${state.headerHeight}px`,
        transition: 'box-shadow 0.3s ease'
      },
      sidebar: {
        position: state.sidebarMode === 'fixed' ? 'fixed' : 'absolute',
        width: state.isSidebarOpen ? `${state.sidebarWidth}px` : `${state.collapsedSidebarWidth}px`,
        height: '100vh',
        zIndex: state.sidebarMode === 'overlay' ? 50 : 30,
        transform: !state.isSidebarOpen && state.sidebarMode !== 'fixed' ? 
                  `translateX(-${state.sidebarWidth}px)` : 'translateX(0)',
        transition: 'transform 0.3s ease, width 0.3s ease'
      },
      content: contentArea,
      overlay: {
        display: state.sidebarMode === 'overlay' && state.isSidebarOpen ? 'block' : 'none',
        position: 'fixed',
        inset: 0,
        zIndex: 45,
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
        backdropFilter: 'blur(4px)'
      },
      mobileNav: {
        position: 'fixed',
        bottom: 0,
        left: 0,
        right: 0,
        zIndex: 40,
        transform: state.mobileNavVisible ? 'translateY(0)' : 'translateY(100%)',
        transition: 'transform 0.3s ease'
      }
    }
  };
}
