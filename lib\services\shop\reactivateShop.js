import apiBridge from '@/lib/api/bridge';
import { ENDPOINTS } from '@/lib/api/contract';
import { toast } from 'sonner';
import BaseService from '@/lib/services/baseService';

/**
 * Reactivate a suspended shop via SuperAdmin API
 * @param {string} shopId - ID of the shop to reactivate
 * @param {string} reason - Optional reason for reactivation
 * @param {boolean} sendEmail - Whether to send email notification
 * @returns {Promise<Object>} Updated shop status
 */
async function reactivateShop(shopId, reason = 'Reactivated by SuperAdmin', sendEmail = true) {
  try {
    // Prepare request payload for SuperAdmin status change
    const payload = {
      status: 'active',
      reason,
      sendEmail
    };
    
    // Make API request using SuperAdmin status endpoint
    const response = await apiBridge.put(`${ENDPOINTS.SHOPS.BASE}/${shopId}/status`, payload, {
      clearCacheEndpoint: ENDPOINTS.SHOPS.BASE
    });
    
    // Process response
    if (response.data && response.data.success) {
      const updatedShop = response.data.data;
      toast.success('Shop reactivated successfully');
      
      return {
        // Core identifiers
        id: updatedShop.shopId || updatedShop._id,
        shopId: updatedShop.shopId || updatedShop._id,
        _id: updatedShop._id || updatedShop.shopId,
        
        // Basic info
        shopName: updatedShop.shopName,
        status: updatedShop.status,
        
        // Reactivation details
        reactivationDetails: {
          reason,
          reactivatedAt: new Date().toISOString(),
          reactivatedBy: 'superAdmin'
        },
        
        // Dates
        updatedAt: updatedShop.updatedAt || new Date().toISOString()
      };
    }
    
    // Handle unexpected response
    console.error('[SuperAdminShopService] Unexpected API response format:', response.data);
    toast.error('Failed to reactivate shop: Unexpected response format');
    return null;
  } catch (error) {
    console.error(`[SuperAdminShopService] Error reactivating shop ${shopId}:`, error);
    
    // Enhanced error handling for SuperAdmin operations
    if (error.response?.status === 400 && error.response?.data?.message) {
      BaseService.handleError({ message: `Validation error: ${error.response.data.message}` }, 'SuperAdminShopService.reactivateShop', true);
    } else if (error.response?.status === 401) {
      BaseService.handleError({ message: 'Unauthorized: SuperAdmin access required' }, 'SuperAdminShopService.reactivateShop', true);
    } else if (error.response?.status === 403) {
      BaseService.handleError({ message: 'Forbidden: Only SuperAdmins can reactivate shops' }, 'SuperAdminShopService.reactivateShop', true);
    } else if (error.response?.status === 404) {
      BaseService.handleError({ message: 'Shop not found' }, 'SuperAdminShopService.reactivateShop', true);
    } else {
      BaseService.handleError(error, 'SuperAdminShopService.reactivateShop', true);
    }
    
    return null;
  }
}

export default reactivateShop;
