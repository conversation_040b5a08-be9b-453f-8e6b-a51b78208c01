"use client"

import { createContext, useContext, useEffect, useState, useRef } from "react"
import { useRouter } from "next/navigation"
import { toast } from "sonner"
import AuthService from "@/lib/services/auth"
import { errorHandlers } from "@/lib/api/contract"

const AuthContext = createContext({})

export function AuthProvider({ children }) {
  const [user, setUser] = useState(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)
  const [isAuthenticated, setIsAuthenticated] = useState(false)
  const router = useRouter()
  
  // Add refs to prevent concurrent API calls and infinite loops
  const authCheckInProgress = useRef(false)
  const lastAuthCheckTime = useRef(0)
  const authCheckInterval = 60000 // 1 minute interval between auth checks
  
  // Register router navigation for API usage
  useEffect(() => {
    if (typeof window !== 'undefined') {
      // Make the router navigation function available globally
      window.routerNavigate = (path) => {
        router.push(path);
      };
      
      // Clear navigation timestamp on page change
      const handleRouteChange = () => {
        console.log('[Auth] Route changed, clearing redirect prevention timestamp');
        sessionStorage.removeItem('lastAuthRedirect');
      };
      
      // Create a cleanup function
      return () => {
        delete window.routerNavigate;
      };
    }
  }, [router]);

  // Check if user is logged in on initial load and validate role
  useEffect(() => {
    const checkAuth = async () => {
      // Prevent concurrent auth checks
      if (authCheckInProgress.current) {
        console.log("[Auth] Auth check already in progress, skipping");
        return;
      }

      // Throttle auth checks to prevent excessive API calls
      const currentTime = Date.now();
      if (currentTime - lastAuthCheckTime.current < authCheckInterval) {
        console.log("[Auth] Skipping auth check due to throttling");
        return;
      }
      
      try {
        // Don't attempt auth check if we're in a redirect loop
        const lastRedirectTime = parseInt(sessionStorage.getItem('lastAuthRedirect') || '0');
        
        if (currentTime - lastRedirectTime < 3000) {
          console.log("[Auth] Skipping auth check due to recent redirect");
          setLoading(false);
          return;
        }
        
        // Mark auth check as in progress
        authCheckInProgress.current = true;
        lastAuthCheckTime.current = currentTime;
        
        // Check for tokens in localStorage
        const accessToken = localStorage.getItem("accessToken");
        const refreshToken = localStorage.getItem("refreshToken");
        
        if (!accessToken) {
          console.log("[Auth] No access token found");
          
          // If on a protected page but no token, redirect to login
          const isLoginPage = window.location.pathname === '/login';
          if (!isLoginPage) {
            console.log("[Auth] Redirecting to login due to missing token");
            sessionStorage.setItem('lastAuthRedirect', Date.now().toString());
            router.push('/login');
          }
          
          setLoading(false);
          return;
        }
        
        if (!refreshToken) {
          console.log("[Auth] No refresh token found, auth may fail");
        }
        
        try {
          console.log("[Auth] Validating token with backend")
          // Validate user with backend
          const response = await AuthService.getProfile()
          const userData = response.data.data.user || response.data.data
          
          // Debug log user data from backend
          console.log("[Auth] User data from backend:", JSON.stringify(userData, null, 2))
          console.log("[Auth] User role from backend:", userData.role)
          
          // Set user data with role information
          const userInfo = {
            id: userData.userId,
            name: userData.fullName,
            email: userData.email,
            role: userData.role, // Store the original role string exactly as it comes from the backend
            shopId: userData.shopId,
            verified: userData.verified
          }
          
          console.log("[Auth] Processed user info:", JSON.stringify(userInfo, null, 2))
          setUser(userInfo)
          setIsAuthenticated(true)
          
          // Normalize role for case-insensitive comparison
          const normalizedRole = userData.role?.toLowerCase() || ''
          const isSuperAdmin = normalizedRole === 'superadmin'
          
          console.log(`[Auth] User role normalized: ${normalizedRole}`)
          console.log(`[Auth] Is superAdmin: ${isSuperAdmin}`)
          
          // Don't redirect immediately, let role guard handle specific page access
          // This allows the auth context to be more flexible and handle various role combinations
          if (isSuperAdmin) {
            console.log("[Auth] User is a superAdmin, access granted")
          } else {
            console.log("[Auth] User is not a superAdmin, role guard will handle access control")
          }
        } catch (e) {
          // Invalid token or failed validation
          console.error("[Auth] Profile validation error:", e)
          localStorage.removeItem("accessToken")
          localStorage.removeItem("refreshToken")
          setUser(null)
          router.push('/login')
        }
        
        setLoading(false)
      } catch (err) {
        console.error("[Auth] Authentication error:", err)
        setError(errorHandlers.getErrorMessage(err))
        
        // Clear tokens on auth error
        localStorage.removeItem("accessToken");
        localStorage.removeItem("refreshToken");
        
        // Don't show error notification if already on login page
        const isLoginPage = window.location.pathname === '/login';
        if (!isLoginPage) {
          toast.error(errorHandlers.getErrorMessage(err) || "Authentication error");
        }
        
        setUser(null);
        setIsAuthenticated(false);
        setLoading(false);
      } finally {
        // Always release the lock after completion
        authCheckInProgress.current = false;
      }
    }
    
    // Only run on component mount
    if (!authCheckInProgress.current) {
      checkAuth();
    }
    
    // Set up a listener for token refresh events
    const handleTokenRefresh = (e) => {
      console.log("[Auth] Token refreshed, updating auth state");
      // Run auth check when token is refreshed
      if (!authCheckInProgress.current) {
        checkAuth();
      }
    };
    
    window.addEventListener('auth:token:refreshed', handleTokenRefresh);
    
    return () => {
      window.removeEventListener('auth:token:refreshed', handleTokenRefresh);
    };
  }, [])

  // Login function
  const login = async (email, password) => {
    setLoading(true)
    setError(null)
    
    try {
      console.log("[Auth] Attempting login for:", email)
      // Call the login API endpoint
      const response = await AuthService.login(email, password)
      const { data } = response.data
      
      // Debug log login response
      console.log("[Auth] Login response:", JSON.stringify(data, null, 2))
      
      // Save tokens to localStorage with proper validation
      if (data.accessToken) {
        console.log("[Auth] Storing access token:", data.accessToken.substring(0, 15) + "...")
        localStorage.setItem("accessToken", data.accessToken)
      } else {
        console.error("[Auth] No access token received in login response", data)
      }
      
      if (data.refreshToken) {
        localStorage.setItem("refreshToken", data.refreshToken)
      } else {
        console.error("[Auth] No refresh token received in login response", data)
      }
      
      // Set user data
      const userInfo = {
        id: data.user.userId,
        name: data.user.fullName,
        email: data.user.email,
        role: data.user.role,
        shopId: data.user.shopId,
        verified: data.user.verified
      }
      
      console.log("[Auth] Setting user after login:", JSON.stringify(userInfo, null, 2))
      
      // Case insensitive role check for superAdmin
      const normalizedRole = data.user.role?.toLowerCase() || ''
      const isSuperAdmin = normalizedRole === 'superadmin'
      console.log(`[Auth] Is user superAdmin? ${isSuperAdmin}`)
      
      setUser(userInfo)
      setIsAuthenticated(true)
      
      // Show success toast
      toast.success("Login successful")
      
      setLoading(false)
      return data
    } catch (err) {
      console.error("[Auth] Login error:", err)
      const friendlyErrorMessage = errorHandlers.getErrorMessage(err)
      setError(friendlyErrorMessage)
      toast.error(friendlyErrorMessage)
      setLoading(false)
      throw err
    }
  }

  // Logout function
  const logout = async () => {
    setLoading(true)
    try {
      // Call the logout API endpoint
      await AuthService.logout()
      
      // Remove tokens from localStorage
      localStorage.removeItem("accessToken")
      localStorage.removeItem("refreshToken")
      
      // Clear user data and authentication state
      setUser(null)
      setIsAuthenticated(false)
      
      // Show success message
      toast.success("Logged out successfully")
      
      // Redirect to login page
      router.push('/login')
    } catch (err) {
      // Even if the API call fails, we should still log out locally
      localStorage.removeItem("accessToken")
      localStorage.removeItem("refreshToken")
      setUser(null)
      
      // Show friendly error notification
      toast.error("You've been logged out successfully, but we couldn't reach the server to confirm.")
      
      // Still redirect to login
      router.push('/login')
    }
  }

  // Note: Registration functionality removed as it's only for mobile users

  // Forgot password function
  const forgotPassword = async (email) => {
    setLoading(true)
    setError(null)
    
    try {
      // Call the forgot password API endpoint
      const response = await AuthService.forgotPassword(email);
      const { data } = response;
      
      // Show success toast
      toast.success("Password reset instructions sent to your email.");
      
      setLoading(false);
      return data;
    } catch (err) {
      const friendlyErrorMessage = errorHandlers.getErrorMessage(err);
      setError(friendlyErrorMessage);
      toast.error(friendlyErrorMessage);
      setLoading(false);
      throw err;
    }
  }

  // Reset password function
  const resetPassword = async (token, newPassword, confirmPassword) => {
    setLoading(true);
    setError(null);
    
    try {
      // Call the reset password API endpoint with the correct parameter names
      const response = await AuthService.resetPassword(token, newPassword, confirmPassword);
      const { data } = response;
      
      // Show success toast
      toast.success("Password has been reset successfully. Please log in with your new password.");
      
      setLoading(false);
      return data;
    } catch (err) {
      const friendlyErrorMessage = errorHandlers.getErrorMessage(err);
      setError(friendlyErrorMessage);
      toast.error(friendlyErrorMessage);
      setLoading(false);
      throw err;
    }
  }
  
  // Helper methods for role checking (case-insensitive)
  const hasRole = (role) => {
    if (!user || !user.role) return false;
    return user.role.toLowerCase() === role.toLowerCase();
  };

  const isAdmin = () => hasRole('admin');
  const isSuperAdmin = () => hasRole('superadmin');
  const isStaff = () => hasRole('staff');

  // Value provided to the context
  const value = {
    user,
    loading,
    error,
    login,
    logout,
    forgotPassword,
    resetPassword,
    changePassword: async (currentPassword, newPassword) => {
      setLoading(true);
      setError(null);
      
      try {
        const response = await AuthService.changePassword(currentPassword, newPassword);
        toast.success("Password changed successfully");
        setLoading(false);
        return response.data;
      } catch (err) {
        const friendlyErrorMessage = errorHandlers.getErrorMessage(err);
        setError(friendlyErrorMessage);
        toast.error(friendlyErrorMessage);
        setLoading(false);
        throw err;
      }
    },
    // Use the actual authentication state
    isAuthenticated,
    // Use helper methods for role checking
    isSuperAdmin: isSuperAdmin(),
    isAdmin: isAdmin(),
    isStaff: isStaff(),
    // Add a general role checker for flexibility
    hasRole
  }

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>
}

export const useAuth = () => {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider")
  }
  return context
}
