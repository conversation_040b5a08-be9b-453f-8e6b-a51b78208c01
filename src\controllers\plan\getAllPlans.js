

/**
 * Get all pricing plans
 * For SuperAdmin: returns all plans including inactive ones
 * GET /api/plans
 */
const PlanService = require('../../services/PlanService');
const { logInfo, logError } = require('../../utils');

const getAllPlans = async (req, res, next) => {
  try {
    logInfo('Fetching all pricing plans', 'PlanController');
    
    // SuperAdmin sees all plans including inactive
    const includeInactive = req.user.role === 'superAdmin';
    const plans = await PlanService.getAllPlans(includeInactive);
    
    return res.status(200).json({
      success: true,
      count: plans.length,
      data: plans
    });
  } catch (error) {
    logError('Failed to fetch pricing plans', 'PlanController', error);
    return next(error);
  }
};

module.exports = getAllPlans;
