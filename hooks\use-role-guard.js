'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from './use-auth';
import { toast } from 'sonner';

/**
 * Hook to guard routes based on user roles
 * @param {string[]} allowedRoles - Array of allowed roles for this route
 * @param {string} redirectPath - Path to redirect to if role check fails
 * @returns {Object} - Object containing isAuthorized, isLoading, and user
 */
export function useRoleGuard(allowedRoles = ['superAdmin'], redirectPath = '/unauthorized') {
  const router = useRouter();
  const { user, isLoading, isAuthenticated } = useAuth();
  
  useEffect(() => {
    // Wait until auth check completes
    if (isLoading) return;
    
    // Redirect to login if not authenticated
    if (!isAuthenticated) {
      console.log('[RoleGuard] Not authenticated, redirecting to login');
      router.push('/login');
      return;
    }
    
    // Check if user role is allowed to access this route
    const userRole = user?.role || '';
    
    // Normalize roles for comparison to handle case sensitivity
    const normalizedUserRole = userRole.toLowerCase();
    const normalizedAllowedRoles = allowedRoles.map(role => role.toLowerCase());
    
    const isAuthorized = normalizedAllowedRoles.includes(normalizedUserRole);
    
    console.log(`[RoleGuard] User role: ${userRole}, normalized: ${normalizedUserRole}`);
    console.log(`[RoleGuard] Allowed roles: ${allowedRoles}, normalized: ${normalizedAllowedRoles}`);
    console.log(`[RoleGuard] Is authorized: ${isAuthorized}`);
    
    if (!isAuthorized) {
      toast.error(`Access denied. You need ${allowedRoles.join(' or ')} role to access this page.`);
      router.push(redirectPath);
    }
  }, [isLoading, isAuthenticated, user, allowedRoles, redirectPath, router]);
  
  return {
    isAuthorized: isAuthenticated && allowedRoles.map(r => r.toLowerCase()).includes((user?.role || '').toLowerCase()),
    isLoading,
    user
  };
}
