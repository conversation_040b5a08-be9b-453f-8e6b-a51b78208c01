import apiBridge from '@/lib/api/bridge';
import { processApiResponse, handleError } from '@/lib/services/baseService';

/**
 * Get notification history with pagination and filtering
 * 
 * @param {Object} params - Query parameters
 * @param {string} params.shopId - Filter by shop ID (optional)
 * @param {number} params.limit - Number of notifications to fetch (default: 50, max: 100)
 * @param {number} params.offset - Offset for pagination (default: 0)
 * @param {string} params.status - Filter by status: all, sent, delivered, failed, pending, expired (default: all)
 * @param {string} params.type - Filter by type: all, Push, Email, SMS (default: all)
 * @returns {Promise<Object>} API response with notifications and pagination info
 */
export async function getNotificationHistory(params = {}) {
  try {
    const queryParams = new URLSearchParams();
    
    if (params.shopId) queryParams.append('shopId', params.shopId);
    if (params.limit) queryParams.append('limit', params.limit);
    if (params.offset) queryParams.append('offset', params.offset);
    if (params.status && params.status !== 'all') queryParams.append('status', params.status);
    if (params.type && params.type !== 'all') queryParams.append('type', params.type);

    // Make API request using the bridge with minimal caching for history
    const response = await apiBridge.get('/api/admin/notifications/push/history', {
      params: Object.fromEntries(queryParams),
      cacheKey: `notification-history-${params.limit || 20}-${params.offset || 0}`,
      cacheTTL: 60000 // 1 minute cache for history
    });

    return processApiResponse(response, null); // No toast for history retrieval
  } catch (error) {
    console.warn('Failed to load notification history:', error.message);
    // Return default empty response to prevent UI breaking (no toast for history errors)
    return {
      success: true,
      data: {
        notifications: [],
        pagination: {
          total: 0,
          limit: parseInt(params.limit) || 20,
          offset: parseInt(params.offset) || 0,
          hasMore: false
        }
      }
    };
  }
} 