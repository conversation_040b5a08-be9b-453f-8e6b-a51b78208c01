import apiBridge from '@/lib/api/bridge';
import { ENDPOINTS } from '@/lib/api/contract';
import { toast } from 'sonner';
import BaseService from '@/lib/services/baseService';

/**
 * Fetch all shops via SuperAdmin API with optional filtering
 * @param {Object} filters - Optional filters for status, search, etc.
 * @param {number} filters.page - Page number (default: 1)
 * @param {number} filters.limit - Items per page (default: 20)
 * @param {string} filters.status - Filter by status
 * @param {string} filters.search - Search query
 * @returns {Promise<Object>} Object containing shops array and pagination data
 */
async function getShops(filters = {}) {
  try {
    // Extract and prepare query parameters
    const { 
      page = 1, 
      limit = 20, 
      status, 
      search 
    } = filters;
    
    // Build query parameters for SuperAdmin API
    const params = new URLSearchParams();
    if (page) params.append('page', page);
    if (limit) params.append('limit', limit);
    if (status && status !== 'all') params.append('status', status);
    if (search) params.append('search', search);
    
    // Make API request using SuperAdmin endpoint with optimized caching
    const response = await apiBridge.get(ENDPOINTS.SHOPS.BASE, {
      params: Object.fromEntries(params),
      useCache: true,
      cacheKey: `shops-${page}-${limit}-${status || 'all'}-${search || 'none'}` // Custom cache key for better deduplication
    });
    
    // Process response
    if (response.data && response.data.success) {
      // Map backend response to frontend-expected format
      const shopData = response.data.data;

      console.log('[getShops] Raw API response:', {
        success: response.data.success,
        dataKeys: Object.keys(shopData),
        shopsCount: shopData.shops?.length || 0,
        total: shopData.total
      });

      // Process shop data
      const processedShops = [];
      
      if (Array.isArray(shopData.shops)) {
        for (const shop of shopData.shops) {
          // Create a robust shop object with fallbacks for every field
          const shopObject = {
            // Core identifiers (ensure all formats exist for compatibility)
            id: shop._id || shop.id || shop.shopId || `shop-${Date.now()}`,
            _id: shop._id || shop.id || shop.shopId || `shop-${Date.now()}`,
            shopId: shop.shopId || shop._id || shop.id || `shop-${Date.now()}`,
            
            // Basic shop info (mapped from SuperAdmin response)
            shopName: shop.shopName || shop.name || 'Unnamed Shop',
            shopAddress: shop.address || shop.shopAddress || shop.location || '',
            ownerName: shop.ownerName || shop.owner?.name || 'Unknown Owner',
            email: shop.email || shop.owner?.email || '',
            phone: shop.phone || shop.owner?.phone || '',
            
            // Status fields
            status: shop.status || 'unknown',
            verified: typeof shop.verified === 'boolean' ? shop.verified : false,
            registeredBy: shop.registeredBy || 'unknown',
            
            // Logo
            logoUrl: shop.logoUrl || '',
            
            // Dates (with valid date fallbacks)
            createdAt: shop.createdAt || shop.created_at || new Date().toISOString(),
            updatedAt: shop.updatedAt || shop.updated_at || new Date().toISOString(),
            
            // Access control from SuperAdmin shops
            access: {
              isPaid: shop.access?.isPaid || false,
              isActivated: shop.access?.isActivated || false
            },
            
            // Additional data
            features: shop.features || {},
            notifications: shop.notifications || {},
            statistics: shop.statistics || {},
          };
          // Add subscription field from backend if present
          if (shop.subscription) {
            shopObject.subscription = shop.subscription;
          }
          
          // Add to processed shops
          processedShops.push(shopObject);
        }
      }
      
      // Return processed data using the new response format
      const result = {
        shops: processedShops,
        total: shopData.total || shopData.pagination?.total || 0,
        currentPage: shopData.currentPage || shopData.pagination?.page || 1,
        totalPages: shopData.totalPages || shopData.pagination?.pages || 1,
        hasNextPage: shopData.hasNextPage || ((shopData.currentPage || shopData.pagination?.page || 1) < (shopData.totalPages || shopData.pagination?.pages || 1)),
        hasPrevPage: shopData.hasPrevPage || ((shopData.currentPage || shopData.pagination?.page || 1) > 1)
      };

      console.log('[getShops] Processed result:', {
        shopsCount: result.shops.length,
        total: result.total,
        currentPage: result.currentPage,
        totalPages: result.totalPages
      });

      return result;
    }
    
    // Return empty result if response format is unexpected
    console.error('[SuperAdminShopService] Unexpected API response format:', response.data);
    toast.error('Unexpected response format from server');
    return { shops: [], total: 0, currentPage: 1, totalPages: 1 };
  } catch (error) {
    console.error(`[SuperAdminShopService] Error fetching shops:`, error);
    
    // Enhanced error handling for SuperAdmin operations
    if (error.response?.status === 401) {
      BaseService.handleError({ message: 'Unauthorized: SuperAdmin access required' }, 'SuperAdminShopService.getShops', true);
    } else if (error.response?.status === 403) {
      BaseService.handleError({ message: 'Forbidden: Only SuperAdmins can view shops' }, 'SuperAdminShopService.getShops', true);
    } else {
      BaseService.handleError(error, 'SuperAdminShopService.getShops', true);
    }
    
    // Return empty data on error to avoid breaking UI
    return {
      shops: [],
      total: 0,
      currentPage: 1,
      totalPages: 1,
      hasNextPage: false,
      hasPrevPage: false
    };
  }
}

export default getShops;
