const { logInfo, logError } = require('../utils');
const { SettingsHelper } = require('../utils');
const axios = require('axios');

/**
 * SMS Service for sending SMS notifications via Hormuud provider
 * Handles SMS delivery with proper error handling and logging
 */
class SMSService {
  
  /**
   * Get Bearer token from Hormuud API
   * @param {Object} credentials - SMS credentials (username, password)
   * @returns {Promise<string>} Bearer token
   */
  static async getBearerToken(credentials) {
    try {
      const tokenUrl = 'https://smsapi.hormuud.com/token';
      
      const payload = new URLSearchParams({
        username: credentials.username,
        password: credentials.password,
        grant_type: 'password'
      });

      const response = await axios.post(tokenUrl, payload, {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        },
        timeout: 30000
      });

      if (response.data && response.data.access_token) {
        logInfo('Successfully obtained Bearer token from Hormuud API', 'SMSService');
        return response.data.access_token;
      } else {
        throw new Error('No access token received from Hormuud API');
      }
    } catch (error) {
      logError(`Failed to get Bear<PERSON> token: ${error.message}`, 'SMSService', error);
      throw new Error(`Authentication failed: ${error.message}`);
    }
  }

  /**
   * Send SMS using Hormuud provider
   * @param {string} phoneNumber - Recipient phone number
   * @param {string} message - SMS message content
   * @param {string} shopId - Shop ID for credentials (optional)
   * @returns {Promise<Object>} SMS delivery result
   */
  static async sendSMS(phoneNumber, message, shopId = null) {
    try {
      logInfo(`Attempting to send SMS to ${phoneNumber}`, 'SMSService');
      
      // Get SMS credentials
      const credentials = await SettingsHelper.getSMSCredentials(shopId);
      if (!credentials) {
        throw new Error(`No SMS credentials found for ${shopId ? 'shop ' + shopId : 'global system'}`);
      }

      // Validate phone number format (Somali format)
      const cleanPhone = this.formatPhoneNumber(phoneNumber);
      if (!cleanPhone) {
        throw new Error('Invalid phone number format');
      }

      // Validate message content
      if (!message || message.trim().length === 0) {
        throw new Error('Message content is required');
      }

      if (message.length > 160) {
        logInfo(`SMS message truncated from ${message.length} to 160 characters`, 'SMSService');
        message = message.substring(0, 157) + '...';
      }

      // Send SMS via Hormuud API
      const result = await this.sendViaHormuud(credentials, cleanPhone, message);
      
      if (result.success) {
        logInfo(`SMS sent successfully to ${phoneNumber}: ${result.messageId}`, 'SMSService');
        return {
          success: true,
          messageId: result.messageId,
          cost: result.cost || 0,
          provider: 'Hormuud'
        };
      } else {
        throw new Error(result.error || 'SMS delivery failed');
      }
    } catch (error) {
      logError(`Failed to send SMS to ${phoneNumber}: ${error.message}`, 'SMSService', error);
      return {
        success: false,
        error: error.message,
        provider: 'Hormuud'
      };
    }
  }

  /**
   * Send SMS via Hormuud provider API
   * @param {Object} credentials - SMS credentials
   * @param {string} phoneNumber - Formatted phone number
   * @param {string} message - SMS message
   * @returns {Promise<Object>} API response
   */
  static async sendViaHormuud(credentials, phoneNumber, message) {
    try {
      logInfo(`Sending SMS via Hormuud API to ${phoneNumber}`, 'SMSService');
      
      // Get Bearer token
      const bearerToken = await this.getBearerToken(credentials);
      
      // Hormuud SMS API endpoint
      const apiUrl = 'https://smsapi.hormuud.com/api/sms';
      
      const payload = {
        to: phoneNumber,
        message: message,
        from: 'DeynCare' // Replace with your sender ID
      };

      const response = await axios.post(apiUrl, payload, {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${bearerToken}`
        },
        timeout: 30000
      });

      if (response.data && response.data.success) {
        logInfo(`Hormuud API response: ${JSON.stringify(response.data)}`, 'SMSService');
        return {
          success: true,
          messageId: response.data.messageId || response.data.id || `HOR_${Date.now()}`,
          cost: response.data.cost || 0.05,
          status: response.data.status || 'sent'
        };
      } else {
        return {
          success: false,
          error: response.data?.message || response.data?.error || 'SMS delivery failed'
        };
      }
    } catch (error) {
      logError(`Hormuud API error: ${error.message}`, 'SMSService', error);
      return {
        success: false,
        error: `Hormuud API error: ${error.message}`
      };
    }
  }

  /**
   * Test SMS credentials
   * @param {Object} credentials - SMS credentials to test
   * @param {Object} testData - Test message data
   * @returns {Promise<Object>} Test result
   */
  static async testConnection(credentials, testData = {}) {
    try {
      const {
        phone = '**********',
        message = 'DeynCare SMS Integration Test'
      } = testData;

      logInfo('Testing SMS credentials with Hormuud provider', 'SMSService');
      
      const result = await this.sendViaHormuud(credentials, phone, message);
      
      if (result.success) {
        return {
          success: true,
          message: 'SMS credentials test successful',
          messageId: result.messageId,
          provider: 'Hormuud'
        };
      } else {
        return {
          success: false,
          message: `SMS credentials test failed: ${result.error}`,
          provider: 'Hormuud'
        };
      }
    } catch (error) {
      logError(`SMS credentials test failed: ${error.message}`, 'SMSService', error);
      return {
        success: false,
        message: `SMS credentials test failed: ${error.message}`,
        provider: 'Hormuud'
      };
    }
  }

  /**
   * Format phone number to Somali standard
   * @param {string} phoneNumber - Raw phone number
   * @returns {string|null} Formatted phone number or null if invalid
   */
  static formatPhoneNumber(phoneNumber) {
    if (!phoneNumber) return null;
    
    // Remove all non-digit characters
    let cleaned = phoneNumber.replace(/\D/g, '');
    
    // Handle different Somali phone number formats
    if (cleaned.startsWith('252')) {
      // Already in international format (25261xxxx)
      return cleaned;
    } else if (cleaned.startsWith('061') || cleaned.startsWith('062') || cleaned.startsWith('063') || 
               cleaned.startsWith('065') || cleaned.startsWith('066') || cleaned.startsWith('067') ||
               cleaned.startsWith('068') || cleaned.startsWith('069') || cleaned.startsWith('090') ||
               cleaned.startsWith('091') || cleaned.startsWith('092') || cleaned.startsWith('093') ||
               cleaned.startsWith('094') || cleaned.startsWith('095') || cleaned.startsWith('096') ||
               cleaned.startsWith('097') || cleaned.startsWith('098') || cleaned.startsWith('099')) {
      // Somali local format (061xxxxxxx) - remove leading 0 and add country code
      return '252' + cleaned.substring(1);
    } else if (cleaned.length === 9 && (cleaned.startsWith('61') || cleaned.startsWith('62') || 
                                       cleaned.startsWith('63') || cleaned.startsWith('65') || 
                                       cleaned.startsWith('66') || cleaned.startsWith('67') ||
                                       cleaned.startsWith('68') || cleaned.startsWith('69') ||
                                       cleaned.startsWith('90') || cleaned.startsWith('91') ||
                                       cleaned.startsWith('92') || cleaned.startsWith('93') ||
                                       cleaned.startsWith('94') || cleaned.startsWith('95') ||
                                       cleaned.startsWith('96') || cleaned.startsWith('97') ||
                                       cleaned.startsWith('98') || cleaned.startsWith('99'))) {
      // Format without country code and leading 0 (61xxxxxxx)
      return '252' + cleaned;
    }
    
    // If none of the above formats match, return as is for potential international numbers
    return cleaned.length >= 10 ? cleaned : null;
  }

  /**
   * Validate Somali phone number
   * @param {string} phoneNumber - Phone number to validate
   * @returns {boolean} True if valid
   */
  static isValidSomaliPhoneNumber(phoneNumber) {
    const formatted = this.formatPhoneNumber(phoneNumber);
    if (!formatted) return false;
    
    // Check if it matches Somali mobile patterns
    // Somali mobile numbers: 061, 062, 063, 065, 066, 067, 068, 069, 090-099
    const somaliPattern = /^252(6[1-3]|6[5-9]|9[0-9])[0-9]{6}$/;
    return somaliPattern.test(formatted);
  }

  /**
   * Get SMS delivery status
   * @param {string} messageId - Message ID from provider
   * @returns {Promise<Object>} Delivery status
   */
  static async getDeliveryStatus(messageId) {
    try {
      logInfo(`Checking delivery status for message: ${messageId}`, 'SMSService');
      
      // TODO: Implement actual status check with Hormuud API
      // This would typically be a GET request to their delivery report endpoint
      
      // Mock response for now - replace with actual API call
      return {
        messageId,
        status: 'delivered',
        deliveredAt: new Date(),
        provider: 'Hormuud'
      };
    } catch (error) {
      logError(`Failed to get delivery status for ${messageId}: ${error.message}`, 'SMSService', error);
      return {
        messageId,
        status: 'unknown',
        error: error.message,
        provider: 'Hormuud'
      };
    }
  }

  /**
   * Get SMS pricing information
   * @param {string} destination - Destination country/network
   * @returns {Promise<Object>} Pricing information
   */
  static async getPricing(destination = 'somalia') {
    try {
      // TODO: Implement actual pricing API call if available
      return {
        destination,
        price: 0.05,
        currency: 'USD',
        provider: 'Hormuud'
      };
    } catch (error) {
      logError(`Failed to get SMS pricing: ${error.message}`, 'SMSService', error);
      return {
        destination,
        price: 0,
        currency: 'USD',
        error: error.message,
        provider: 'Hormuud'
      };
    }
  }
}

module.exports = SMSService; 