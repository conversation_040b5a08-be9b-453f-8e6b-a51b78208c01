const Debt = require('../../models/debt.model');
const Customer = require('../../models/customer.model');
const AppError = require('../../utils/core/AppError');
const paginationHelper = require('../../utils/helpers/paginationHelper');

/**
 * Get All Debts Controller (Admin Role - Shop Owner)
 * - Retrieve all debts for shop with ML risk levels
 * - Support filtering, sorting, pagination
 * - Include risk distribution analytics
 */
const getAllDebts = async (req, res, next) => {
  try {
    const shopId = req.user.shopId;
    const {
      page = 1,
      limit = 20,
      status,
      riskLevel,
      customerType,
      sortBy = 'createdAt',
      sortOrder = 'desc',
      search
    } = req.query;

    // Build query filter
    const filter = {
      shopId,
      isDeleted: false
    };

    // Apply filters
    if (status) {
      filter.status = status;
    }

    if (riskLevel) {
      filter.RiskLevel = riskLevel;
    }

    if (customerType) {
      filter.CustomerType = customerType;
    }

    // Search functionality
    if (search) {
      filter.$or = [
        { CustomerName: { $regex: search, $options: 'i' } },
        { debtId: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } }
      ];
    }

    // Build sort object
    const sortObj = {};
    sortObj[sortBy] = sortOrder === 'asc' ? 1 : -1;

    // Calculate pagination
    const skip = (parseInt(page) - 1) * parseInt(limit);

    // Get total count for pagination
    const totalCount = await Debt.countDocuments(filter);

    // Fetch debts with pagination
    const debts = await Debt.find(filter)
      .sort(sortObj)
      .skip(skip)
      .limit(parseInt(limit))
      .lean();

    // Get risk distribution analytics
    const riskDistribution = await Debt.aggregate([
      { $match: { shopId, isDeleted: false } },
      {
        $group: {
          _id: '$RiskLevel',
          count: { $sum: 1 },
          totalAmount: { $sum: '$DebtAmount' },
          totalOutstanding: { $sum: '$OutstandingDebt' },
          avgAmount: { $avg: '$DebtAmount' }
        }
      }
    ]);

    // Get status distribution
    const statusDistribution = await Debt.aggregate([
      { $match: { shopId, isDeleted: false } },
      {
        $group: {
          _id: '$status',
          count: { $sum: 1 },
          totalAmount: { $sum: '$DebtAmount' },
          totalOutstanding: { $sum: '$OutstandingDebt' }
        }
      }
    ]);

    // Calculate summary statistics
    const summaryStats = await Debt.aggregate([
      { $match: { shopId, isDeleted: false } },
      {
        $group: {
          _id: null,
          totalDebts: { $sum: 1 },
          totalDebtAmount: { $sum: '$DebtAmount' },
          totalOutstanding: { $sum: '$OutstandingDebt' },
          totalPaid: { $sum: '$PaidAmount' },
          avgDebtAmount: { $avg: '$DebtAmount' },
          avgRiskScore: { $avg: '$riskProfile.riskScore' }
        }
      }
    ]);

    // Calculate collection rate
    const stats = summaryStats[0] || {};
    const collectionRate = stats.totalDebtAmount > 0 ? 
      ((stats.totalPaid / stats.totalDebtAmount) * 100).toFixed(2) : 0;

    // Format debt data with enriched information
    const enrichedDebts = debts.map(debt => {
      const daysUntilDue = Math.ceil((new Date(debt.DueDate) - new Date()) / (1000 * 60 * 60 * 24));
      const daysOverdue = daysUntilDue < 0 ? Math.abs(daysUntilDue) : 0;
      
      return {
        debtId: debt.debtId,
        customer: {
          name: debt.CustomerName,
          type: debt.CustomerType
        },
        debt: {
          amount: debt.DebtAmount,
          outstanding: debt.OutstandingDebt,
          paid: debt.PaidAmount || 0,
          paidRatio: debt.DebtPaidRatio,
          repaymentTime: debt.RepaymentTime
        },
        timeline: {
          createdDate: debt.DebtCreationDate,
          dueDate: debt.DueDate,
          daysUntilDue: daysUntilDue > 0 ? daysUntilDue : 0,
          daysOverdue: daysOverdue,
          isOverdue: daysOverdue > 0,
          paidDate: debt.PaidDate
        },
        risk: {
          level: debt.RiskLevel,
          isEvaluated: debt.RiskLevel !== 'Active Debt',
          needsEvaluation: daysUntilDue < 0 && debt.RiskLevel === 'Active Debt'
        },
        status: debt.status,
        description: debt.description,
        createdAt: debt.createdAt,
        updatedAt: debt.updatedAt
      };
    });

    // Pagination metadata
    const totalPages = Math.ceil(totalCount / parseInt(limit));
    const pagination = {
      currentPage: parseInt(page),
      totalPages,
      totalItems: totalCount,
      itemsPerPage: parseInt(limit),
      hasNextPage: parseInt(page) < totalPages,
      hasPrevPage: parseInt(page) > 1
    };

    // Response data
    const responseData = {
      success: true,
      message: 'Debts retrieved successfully',
      data: {
        debts: enrichedDebts,
        pagination,
        analytics: {
          summary: {
            totalDebts: stats.totalDebts || 0,
            totalDebtAmount: stats.totalDebtAmount || 0,
            totalOutstanding: stats.totalOutstanding || 0,
            totalPaid: stats.totalPaid || 0,
            collectionRate: `${collectionRate}%`,
            avgDebtAmount: Math.round(stats.avgDebtAmount || 0),
            avgRiskScore: Math.round(stats.avgRiskScore || 0)
          },
          riskDistribution: riskDistribution.map(item => ({
            riskLevel: item._id,
            count: item.count,
            totalAmount: Math.round(item.totalAmount),
            totalOutstanding: Math.round(item.totalOutstanding),
            avgAmount: Math.round(item.avgAmount),
            percentage: ((item.count / (stats.totalDebts || 1)) * 100).toFixed(1)
          })),
          statusDistribution: statusDistribution.map(item => ({
            status: item._id,
            count: item.count,
            totalAmount: Math.round(item.totalAmount),
            totalOutstanding: Math.round(item.totalOutstanding),
            percentage: ((item.count / (stats.totalDebts || 1)) * 100).toFixed(1)
          }))
        },
        filters: {
          applied: {
            status,
            riskLevel,
            customerType,
            search
          },
          available: {
            statuses: ['active', 'paid', 'overdue', 'partially_paid'],
            riskLevels: ['Low Risk', 'Medium Risk', 'High Risk', 'Active Debt'],
            customerTypes: ['New', 'Returning']
          }
        }
      }
    };

    res.status(200).json(responseData);

  } catch (error) {
    console.error('Get all debts error:', error);
    return next(new AppError('Failed to retrieve debts', 500));
  }
};

module.exports = getAllDebts; 