"use client";

import { useState, useEffect, useCallback, useMemo } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/auth-context';
import { toast } from 'sonner';
import { Package, Search, Filter, RefreshCw, MoreVertical, Eye, Edit, Trash2, ToggleLeft, ToggleRight, Download, Users, DollarSign, Plus } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from "@/components/ui/badge";

import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { ResponsiveContainer } from '@/components/layout/responsive-container';
import { DataTable } from '@/components/dashboard/common/data-table';
import { KpiCard } from '@/components/dashboard/common/kpi-card';

import { CreatePlanDialog } from '@/components/dashboard/plans/CreatePlanDialog';
import { EditPlanDialog } from '@/components/dashboard/plans/EditPlanDialog';
import { DeletePlanDialog } from '@/components/dashboard/plans/DeletePlanDialog';
import { PlanDetailsDialog } from '@/components/dashboard/plans/PlanDetailsDialog';
import { ExportDialog } from '@/components/dashboard/common/export-dialog';

import { usePlans, usePlanStats } from '@/hooks/usePlan-index';
import { useExport } from '@/hooks/useExport';
import { formatCurrency, formatPlanStatus, getStatusVariant, formatDate } from '@/components/dashboard/plans/utils/planFormatters';

import { Card, CardHeader, CardTitle, CardContent } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { TrendingUp } from "lucide-react";

export default function PlansPage() {
  const router = useRouter();
  const { isAuthenticated, isLoading, isSuperAdmin } = useAuth();
  
  // Authentication state tracking
  const [authReady, setAuthReady] = useState(false);

  // Wait for authentication to be fully ready
  useEffect(() => {
    if (isLoading) {
      return;
    }
    
    if (!isAuthenticated) {
      router.push('/login');
      return;
    }
    
    if (!isSuperAdmin) {
      router.push('/unauthorized');
      return;
    }

    // Check if we have access token before proceeding
    const hasToken = typeof window !== 'undefined' && localStorage.getItem('accessToken');
    
    if (hasToken) {
      setAuthReady(true);
    } else {
      // Retry after a short delay
      setTimeout(() => {
        const retryHasToken = typeof window !== 'undefined' && localStorage.getItem('accessToken');
        if (retryHasToken) {
          setAuthReady(true);
        }
      }, 1000);
    }
  }, [isAuthenticated, isLoading, isSuperAdmin, router]);

  // Custom hooks for data management
  const { 
    plans,
    loading: plansLoading,
    error: plansError,
    filters,
    updateFilters,
    pagination,
    setPagination,
    refreshPlans,
    togglePlanStatus,
    activePlans,
    inactivePlans
  } = usePlans({
    autoFetch: true,
    includeInactive: true,
    showToastMessages: true
  });

  const {
    stats,
    loading: statsLoading,
    error: statsError,
    refreshStats
  } = usePlanStats({
    autoFetch: true,
    showToastMessages: true
  });

  // Comprehensive debug logging
  useEffect(() => {
    
    // Force refresh if stuck loading for too long
    if (statsLoading && !stats) {
      
    }
  }, [stats, statsLoading, statsError, refreshStats]);

  const {
    exportData,
    exportProgress,
    exportStatus,
    cancelExport
  } = useExport();
  
  // Dialog state
  const [selectedPlan, setSelectedPlan] = useState(null);
  const [showDetailsDialog, setShowDetailsDialog] = useState(false);
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [showEditDialog, setShowEditDialog] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [showExportDialog, setShowExportDialog] = useState(false);

  // Dialog handlers
  const handleViewPlan = useCallback((plan) => {
    
    // Validate plan has required planId
    if (!plan?.planId) {
      toast.error('Plan ID missing - cannot view details');
      return;
    }
    
    setSelectedPlan(plan);
    setShowDetailsDialog(true);
  }, []);
  
  const handleEditPlan = useCallback((plan) => {
    setSelectedPlan(plan);
    setShowEditDialog(true);
  }, []);

  const handleDeletePlan = useCallback((plan) => {
    setSelectedPlan(plan);
    setShowDeleteDialog(true);
  }, []);

  const handleToggleStatus = useCallback(async (plan) => {
    try {
      // FIXED: Ensure we have planId, not MongoDB _id
      if (!plan.planId) {
        toast.error('Plan ID missing - cannot update status');
        return;
      }
      
      // FIXED: Remove duplicate toast - the hook already handles notifications
      await togglePlanStatus(plan.planId, !plan.isActive);
    } catch (error) {
      toast.error('Failed to update plan status');
    }
  }, [togglePlanStatus]);

  // Export handler
  const handleExport = useCallback(() => {
    setShowExportDialog(true);
  }, []);
  
  // Table columns definition - FIXED to handle DataTable's row format
  const columns = useMemo(() => [
    {
      key: 'planId',
      header: 'Plan',
      cell: (rowData) => {
        // Handle both direct plan data and wrapped {row: {original: plan}} format
        const plan = rowData?.row?.original || rowData;
        if (!plan) {
          return <div>-</div>;
        }
        return (
          <div className="flex flex-col">
            <span className="font-medium">{plan.displayName || plan.name || 'N/A'}</span>
            <span className="text-xs text-muted-foreground">{plan.planId || plan._id || 'Unknown ID'}</span>
          </div>
        );
      }
    },
    {
      key: 'type',
      header: 'Type',
      cell: (rowData) => {
        const plan = rowData?.row?.original || rowData;
        if (!plan) {
          return (
            <div className="flex flex-col">
              <span className="text-muted-foreground">No plan</span>
            </div>
          );
        }
        
        return (
          <div className="flex flex-col">
            <span className="capitalize">{plan.type || 'Unknown'}</span>
            <span className="text-xs text-muted-foreground">{plan.description || plan.displayName || ''}</span>
          </div>
        );
      }
    },
    {
      key: 'status',
      header: 'Status',
      cell: (rowData) => {
        const plan = rowData?.row?.original || rowData;
        if (!plan) return null;
        
        const getStatusIcon = (isActive) => {
          return isActive ? <ToggleRight className="h-4 w-4" /> : <ToggleLeft className="h-4 w-4" />;
        };
        
        // FIXED: Ensure boolean check
        const isActive = plan.isActive === true;
        
        return (
          <Badge variant={getStatusVariant(isActive ? 'active' : 'inactive')} className="capitalize">
            {getStatusIcon(isActive)}
            <span className="ml-1">{isActive ? 'Active' : 'Inactive'}</span>
          </Badge>
        );
      }
    },
    {
      key: 'pricing',
      header: 'Price',
      cell: (rowData) => {
        const plan = rowData?.row?.original || rowData;
        if (!plan || !plan.pricing) return <span className="text-muted-foreground">-</span>;
        
        const price = plan.pricing.basePrice || 0;
        const currency = plan.pricing.currency || 'USD';
        const cycle = plan.pricing.billingCycle || 'monthly';
        
        return (
          <div className="flex flex-col">
            <span className="font-medium">
              {formatCurrency(price, currency)}
            </span>
            <span className="text-xs text-muted-foreground capitalize">
              {cycle === 'one-time' ? 'one-time' : `per ${cycle}`}
            </span>
          </div>
        );
      }
    },
    {
      key: 'subscribers',
      header: 'Subscribers',
      cell: (rowData) => {
        const plan = rowData?.row?.original || rowData;
        const count = plan?.subscriberCount || 0;
        return (
          <div className="flex items-center gap-2">
            <Users className="h-4 w-4 text-muted-foreground" />
            <span>{count}</span>
          </div>
        );
      }
    },
    {
      key: 'revenue',
      header: 'Revenue',
      cell: (rowData) => {
        const plan = rowData?.row?.original || rowData;
        const revenue = plan?.totalRevenue || 0;
        return (
          <div className="flex items-center gap-2">
            <DollarSign className="h-4 w-4 text-muted-foreground" />
            <span>{formatCurrency(revenue, 'USD')}</span>
          </div>
        );
      }
    },
    {
      key: 'actions',
      header: 'Actions',
      cell: (rowData) => {
        const plan = rowData?.row?.original || rowData;
        return (
          <div className="flex items-center justify-end">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button 
                  variant="ghost" 
                  className="h-8 w-8 p-0"
                  onClick={(e) => e.stopPropagation()}
                >
                  <span className="sr-only">Open menu</span>
                  <MoreVertical className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem onClick={(e) => {
                  e.stopPropagation();
                  handleViewPlan(plan);
                }}>
                  <Eye className="mr-2 h-4 w-4" />
                  View Details
                </DropdownMenuItem>
                <DropdownMenuItem onClick={(e) => {
                  e.stopPropagation();
                  handleEditPlan(plan);
                }}>
                  <Edit className="mr-2 h-4 w-4" />
                  Edit Plan
                </DropdownMenuItem>
                <DropdownMenuItem onClick={(e) => {
                  e.stopPropagation();
                  handleToggleStatus(plan);
                }}>
                  {plan.isActive ? (
                    <>
                      <ToggleLeft className="mr-2 h-4 w-4" />
                      Deactivate
                    </>
                  ) : (
                    <>
                      <ToggleRight className="mr-2 h-4 w-4" />
                      Activate
                    </>
                  )}
                </DropdownMenuItem>
                <DropdownMenuItem onClick={(e) => {
                  e.stopPropagation();
                  handleDeletePlan(plan);
                }} className="text-destructive">
                  <Trash2 className="mr-2 h-4 w-4" />
                  Delete Plan
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        );
      }
    }
  ], [handleViewPlan, handleEditPlan, handleDeletePlan, handleToggleStatus]);
  
  // Empty state component
  const emptyState = (
    <div className="text-center py-12">
      <div className="inline-flex items-center justify-center w-16 h-16 bg-muted rounded-full mb-4">
        <Package className="h-8 w-8 text-muted-foreground" />
      </div>
      <h3 className="text-lg font-medium">No plans found</h3>
      <p className="text-muted-foreground mt-1">
        {filters?.searchQuery || filters?.statusFilter !== 'all'
          ? "Try adjusting your filters or search query"
          : "Create your first plan to get started"}
      </p>
    </div>
  );

  const handleRefresh = async () => {
    try {
      // Clear localStorage cache to force fresh data
      if (typeof window !== 'undefined') {
        localStorage.removeItem('cache_plans-list');
        localStorage.removeItem('cache_plan-stats');
      }
      
      // Show loading toast
      const loadingToast = toast.loading("Refreshing data...");
      
      // Refresh both plans and stats
      await Promise.all([refreshPlans(), refreshStats()]);
      
      // Dismiss loading toast and show success
      toast.dismiss(loadingToast);
      toast.success("Data refreshed successfully");
    } catch (error) {
      toast.error("Failed to refresh data");
    }
  };

  // Filter handlers
  const handleSearchChange = (value) => {
    updateFilters({ searchQuery: value });
  };

  const handleStatusFilterChange = (value) => {
    updateFilters({ statusFilter: value });
  };

  const handleTypeFilterChange = (value) => {
    updateFilters({ typeFilter: value });
  };

  // Test function to debug API call
  const testStatsAPI = async () => {
    try {
      
      // Test direct API call
      const token = localStorage.getItem('accessToken');
      
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000'}/api/plans/stats`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });
      
      if (response.ok) {
        const data = await response.json();
      } else {
        console.error('🧪 [Test] Failed response:', await response.text());
      }
      
      // Test service call
      const serviceResult = await refreshStats();
      
    } catch (error) {
      console.error('🧪 [Test] Error:', error);
    }
  };

  const debugFullDataFlow = async () => {
    
    // 1. Check current hook state
    
    // 2. Check stats structure
    if (stats) {
      
      if (stats.totals) {
      }
    }
    
    // 3. Test direct API call
    try {
      
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000'}/api/plans/stats`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });
      
      if (response.ok) {
        const data = await response.json();
      }
    } catch (error) {
      console.error('🧪 [Full Debug] Direct API Call Failed:', error);
    }
    
    // 4. Test service call
    try {
      const serviceResult = await refreshStats();
    } catch (error) {
      console.error('🧪 [Full Debug] Service Call Failed:', error);
    }
    
    // 5. Test hook refresh
    try {
      await refreshStats();
    } catch (error) {
      console.error('🧪 [Full Debug] Hook refresh failed:', error);
    }
  };

  return (
    <ResponsiveContainer>
      <div className="space-y-6">
        {/* Header Section */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Plans</h1>
            <p className="text-muted-foreground">
              Manage and monitor all subscription plans
            </p>
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleExport}
            >
              <Download className="h-4 w-4 mr-2" />
              Export
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={handleRefresh}
              disabled={plansLoading || statsLoading}
            >
              <RefreshCw className={`h-4 w-4 mr-2 ${(plansLoading || statsLoading) ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
            <Button
              size="sm"
              onClick={() => setShowCreateDialog(true)}
            >
              <Plus className="h-4 w-4 mr-2" />
              Create Plan
            </Button>
          </div>
        </div>

        {/* KPI Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Plans</CardTitle>
              <Package className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {statsLoading ? <Skeleton className="h-8 w-16" /> : (stats?.summary?.totalPlans || 0)}
              </div>
              <p className="text-xs text-muted-foreground">
                Active plans across all tiers
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Active Plans</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {statsLoading ? <Skeleton className="h-8 w-16" /> : (stats?.summary?.activePlans || 0)}
              </div>
              <p className="text-xs text-muted-foreground">
                Plans available for signup
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Subscribers</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {(() => {
                  
                  if (statsLoading) {
                    return <Skeleton className="h-8 w-16" />;
                  }
                  
                  const totalSubs = stats?.totals?.totalSubscriptions;
                  
                  if (totalSubs === undefined || totalSubs === null) {
                    return "No Data";
                  }
                  
                  return totalSubs;
                })()}
              </div>
              <p className="text-xs text-muted-foreground">
                Active subscriptions across all plans
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {(() => {
                  
                  if (statsLoading) {
                    return <Skeleton className="h-8 w-16" />;
                  }
                  
                  const totalRev = stats?.totals?.totalRevenue;
                  
                  if (totalRev === undefined || totalRev === null) {
                    return "No Data";
                  }
                  
                  return formatCurrency(totalRev);
                })()}
              </div>
              <p className="text-xs text-muted-foreground">
                Last 30 days revenue
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Plans Content */}
        <div className="space-y-4">
          {/* Filters Section */}
          <div className="flex items-center gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search by plan name or ID..."
                  className="pl-9"
                  value={filters?.searchQuery || ''}
                  onChange={(e) => handleSearchChange(e.target.value)}
                />
              </div>
            </div>
            <div className="flex gap-2">
              <Select 
                value={filters?.statusFilter || 'all'} 
                onValueChange={handleStatusFilterChange}
              >
                <SelectTrigger className="w-[180px]">
                  <Filter className="h-4 w-4 mr-2" />
                  <SelectValue placeholder="Filter by status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="active">Active</SelectItem>
                  <SelectItem value="inactive">Inactive</SelectItem>
                </SelectContent>
              </Select>
              <Select 
                value={filters?.typeFilter || 'all'} 
                onValueChange={handleTypeFilterChange}
              >
                <SelectTrigger className="w-[180px]">
                  <Package className="h-4 w-4 mr-2" />
                  <SelectValue placeholder="Filter by type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Types</SelectItem>
                  <SelectItem value="monthly">Monthly</SelectItem>
                  <SelectItem value="yearly">Yearly</SelectItem>
                  <SelectItem value="trial">Trial</SelectItem>
                  <SelectItem value="custom">Custom</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Plans Table - FIXED loading logic */}
          <DataTable
            key={`plans-table-${plans?.length || 0}-${plansLoading}`}
            columns={columns}
            data={plans || []}
            pagination={true}
            currentPage={pagination?.currentPage || 1}
            pageSize={pagination?.pageSize || 10}
            totalItems={plans?.length || 0}
            totalPages={Math.ceil((plans?.length || 0) / (pagination?.pageSize || 10))}
            onPageChange={(page) => setPagination && setPagination({ ...pagination, currentPage: page })}
            onPageSizeChange={(size) => setPagination && setPagination({ ...pagination, pageSize: size })}
            isLoading={plansLoading && (!plans || plans.length === 0)}
            emptyState={emptyState}
            onRowClick={handleViewPlan}
          />
        </div>

        {/* Dialogs */}
        {showDetailsDialog && (
          <PlanDetailsDialog
            open={showDetailsDialog}
            onClose={() => setShowDetailsDialog(false)}
            planId={selectedPlan?.planId}
            onEdit={handleEditPlan}
            onDelete={handleDeletePlan}
          />
        )}

        {showCreateDialog && (
          <CreatePlanDialog
            isOpen={showCreateDialog}
            onClose={() => setShowCreateDialog(false)}
            onSuccess={refreshPlans}
          />
        )}

        {showEditDialog && (
          <EditPlanDialog
            isOpen={showEditDialog}
            onClose={() => setShowEditDialog(false)}
            plan={selectedPlan}
            onSuccess={refreshPlans}
          />
        )}

        {showDeleteDialog && (
          <DeletePlanDialog
            isOpen={showDeleteDialog}
            onClose={() => setShowDeleteDialog(false)}
            plan={selectedPlan}
            onSuccess={refreshPlans}
          />
        )}
        
        {/* Export Dialog */}
        <ExportDialog
          open={showExportDialog}
          onOpenChange={setShowExportDialog}
          exportType="plans"
          title="Export Plans"
          description="Export plan data to CSV format"
        />
      </div>
    </ResponsiveContainer>
  );
}