import React, { useMemo } from 'react';
import PropTypes from 'prop-types';
import { format } from 'date-fns';
import { MoreVertical, Eye, Edit, Ban, Power, Trash2 } from 'lucide-react';
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { DataTable } from '@/components/dashboard/common/data-table';
import { Card, CardContent } from "@/components/ui/card";

/**
 * Helper function to format dates with error handling
 * @param {string|Date|null} dateString - The date string to format
 * @returns {string} Formatted date string or fallback text
 */
const formatDate = (dateString) => {
  try {
    if (!dateString) return 'N/A';
    const date = new Date(dateString);
    return isNaN(date.getTime()) ? 'Invalid date' : format(date, 'MMM dd, yyyy');
  } catch (error) {
    console.error('Error formatting date:', error);
    return 'N/A';
  }
};

/**
 * @typedef {Object} Shop
 * @property {string} id - Unique identifier
 * @property {string} [_id] - Alternative identifier 
 * @property {string} [shopId] - Business identifier
 * @property {string} shopName - Name of the shop
 * @property {string} [shopAddress] - Physical address
 * @property {string} [ownerName] - Name of shop owner
 * @property {string} [email] - Contact email
 * @property {string} [phone] - Contact phone number
 * @property {('active'|'pending'|'suspended'|'inactive'|'unknown')} [status] - Current shop status
 * @property {boolean} [verified] - Whether shop is verified
 * @property {string} [createdAt] - Creation timestamp
 * @property {string} [updatedAt] - Last update timestamp
 * @property {Object} [subscription] - Subscription details
 * @property {string} [subscription.plan] - Plan type
 * @property {string} [subscription.status] - Subscription status
 * @property {string} [subscription.startDate] - Start date
 * @property {string} [subscription.expiresAt] - Expiration date
 * @property {number} [subscription.daysLeft] - Days remaining
 */

/**
 * ShopsTable Component - Displays shop data in a table format with actions
 * 
 * @param {Object} props - Component props
 * @param {Array<Shop>} props.shops - Array of shop objects to display
 * @param {boolean} props.isLoading - Whether data is loading
 * @param {Function} props.onView - Callback when view action is triggered
 * @param {Function} props.onEdit - Callback when edit action is triggered
 * @param {Function} props.onDelete - Callback when delete action is triggered
 * @param {Function} props.onSuspend - Callback when suspend action is triggered
 * @param {Function} props.onReactivate - Callback when reactivate action is triggered
 * @param {number} props.currentPage - Current page number for pagination
 * @param {number} props.pageSize - Number of items per page
 * @param {number} props.totalShops - Total number of shops
 * @param {number} props.totalPages - Total number of pages
 * @param {Function} props.onPageChange - Callback when page changes
 * @param {Function} props.onPageSizeChange - Callback when page size changes
 * @returns {JSX.Element} Rendered component
 */
export const ShopsTable = ({ 
  shops = [], 
  isLoading, 
  onView, 
  onEdit, 
  onDelete,
  onSuspend,
  onReactivate,
  currentPage,
  pageSize,
  totalShops,
  totalPages,
  onPageChange,
  onPageSizeChange
}) => {
  // Enhanced debugging for shop data
  if (process.env.NODE_ENV === 'development') {
    console.log('[ShopsTable] Render state:', {
      shopCount: shops?.length || 0,
      isLoading,
      totalShops,
      shopsType: typeof shops,
      isArray: Array.isArray(shops),
      shopsValue: shops,
      firstShop: shops?.[0] ? {
        id: shops[0].id || shops[0].shopId,
        shopName: shops[0].shopName,
        ownerName: shops[0].ownerName
      } : null,
      propsReceived: {
        shopsLength: shops?.length,
        totalShops,
        currentPage,
        pageSize
      }
    });
  }
  // Table columns definition
  const columns = useMemo(() => [
    {
      key: 'shopName',
      header: 'Shop Name',
      cell: (shop) => {
        // Check if we're getting shop directly or as row.original
        const shopData = shop.row?.original || shop;
        
        return (
          <div className="flex flex-col">
            <span className="font-medium">{shopData.shopName}</span>
            <span className="text-xs text-muted-foreground">{shopData.shopAddress}</span>
          </div>
        );
      }
    },
    {
      key: 'ownerName',
      header: 'Owner',
      cell: (shop) => {
        // Check if we're getting shop directly or as row.original
        const shopData = shop.row?.original || shop;
        
        return (
          <div className="flex flex-col">
            <span>{shopData.ownerName}</span>
            <span className="text-xs text-muted-foreground truncate max-w-[200px]">{shopData.email}</span>
          </div>
        );
      }
    },
    {
      key: 'subscription',
      header: 'Subscription',
      cell: (shop) => {
        // Check if we're getting shop directly or as row.original
        const shopData = shop.row?.original || shop;
        
        // Default to empty object if subscription is undefined
        const subscription = shopData?.subscription || {};
        
        // Helper function to get plan name - handle both string and object cases
        const getPlanName = () => {
          if (!subscription || !subscription.status || subscription.status === 'no_plan' || !subscription.plan) {
            return 'No plan';
          }
          // Handle case where plan is an object with name property
          if (typeof subscription.plan === 'object' && subscription.plan?.name) {
            return subscription.plan.name;
          }
          // Handle case where plan is a string
          if (typeof subscription.plan === 'string') {
            return subscription.plan;
          }
          return 'No plan';
        };
        // Helper function to get status display text
        const getStatusDisplay = () => {
          if (!subscription || !subscription.status || subscription.status === 'no_plan') {
            return 'No plan assigned';
          }
          switch (subscription.status) {
            case 'active':
              return `Active until ${formatDate(subscription.expiresAt)}`;
            case 'trial':
              return `Trial ${subscription.daysLeft > 0 ? `(${subscription.daysLeft} days left)` : 'expired'}`;
            case 'trial_expired':
              return 'Trial expired';
            case 'expired':
              return 'Expired';
            case 'cancelled':
              return 'Cancelled';
            default:
              return subscription.status;
          }
        };
        
        // Helper function to get status color
        const getStatusColor = () => {
          switch (subscription.status) {
            case 'active':
              return 'text-green-600';
            case 'trial':
              return subscription.daysLeft > 7 ? 'text-blue-600' : 'text-orange-600';
            case 'trial_expired':
            case 'expired':
              return 'text-red-600';
            case 'cancelled':
              return 'text-gray-600';
            case 'no_plan':
              return 'text-yellow-600';
            default:
              return 'text-muted-foreground';
          }
        };
        
        return (
          <div className="flex flex-col">
            <span className="font-medium">{getPlanName()}</span>
            <span className={`text-xs ${getStatusColor()}`}>
              {getStatusDisplay()}
            </span>
          </div>
        );
      }
    },
    {
      key: 'status',
      header: 'Status',
      cell: (shop) => {
        // Check if we're getting shop directly or as row.original
        const shopData = shop.row?.original || shop;
        
        // Default status if undefined
        const status = shopData?.status || 'unknown';
        
        const getVariant = (status) => {
          switch (status) {
            case 'active': return 'success';
            case 'pending': return 'warning';
            case 'suspended': return 'destructive';
            case 'inactive': return 'outline';
            default: return 'secondary';
          }
        };
        
        // Capitalize first letter safely
        const displayStatus = status.charAt(0).toUpperCase() + status.slice(1);
        
        return (
          <Badge variant={getVariant(status)}>
            {displayStatus}
          </Badge>
        );
      }
    },
    {
      key: 'createdAt',
      header: 'Created At',
      cell: (shop) => {
        // Check if we're getting shop directly or as row.original
        const shopData = shop.row?.original || shop;
        return formatDate(shopData.createdAt);
      }
    },
    {
      key: 'actions',
      header: 'Actions',
      cell: (shop) => {
        // Check if we're getting shop directly or as row.original
        const shopData = shop.row?.original || shop;
        
        return (
          <div className="flex items-center justify-end">
            <Button
              variant="ghost"
              size="icon"
              onClick={(e) => { e.stopPropagation(); onView && onView(shopData); }}
              className="rounded-full"
              title="View details"
            >
              <Eye className="h-4 w-4" />
            </Button>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="icon" className="rounded-full">
                  <MoreVertical className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="bg-popover border border-border shadow-md dark:bg-gray-900 dark:border-gray-800">
                <DropdownMenuItem onClick={() => onEdit && onEdit(shopData)} className="dark:hover:bg-gray-800 dark:text-gray-100">
                  <Edit className="h-4 w-4 mr-2 dark:text-gray-300" />
                  Edit
                </DropdownMenuItem>
                {shopData.status === 'active' ? (
                  <DropdownMenuItem onClick={() => onSuspend && onSuspend(shopData)} className="dark:hover:bg-amber-900/30 dark:text-amber-200">
                    <Ban className="h-4 w-4 mr-2 dark:text-amber-300" />
                    Suspend
                  </DropdownMenuItem>
                ) : shopData.status === 'suspended' ? (
                  <DropdownMenuItem onClick={() => onReactivate && onReactivate(shopData)} className="dark:hover:bg-green-900/30 dark:text-green-200">
                    <Power className="h-4 w-4 mr-2 dark:text-green-300" />
                    Reactivate
                  </DropdownMenuItem>
                ) : null}
                <DropdownMenuItem 
                  onClick={() => onDelete && onDelete(shopData)}
                  className="text-destructive focus:text-destructive dark:hover:bg-red-900/30 dark:text-red-200"
                >
                  <Trash2 className="h-4 w-4 mr-2 dark:text-red-300" />
                  Delete
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        );
      }
    }
  ], [onView, onEdit, onDelete, onSuspend, onReactivate]);

  return (
    <Card>
      <CardContent className="p-0">
        <DataTable
          columns={columns}
          data={shops}
          isLoading={isLoading}
          pagination={true}
          currentPage={currentPage}
          pageSize={pageSize}
          totalItems={totalShops}
          totalPages={totalPages}
          onPageChange={onPageChange}
          onPageSizeChange={onPageSizeChange}
        />
      </CardContent>
    </Card>
  );
};

/**
 * PropTypes for type validation during development
 */
ShopsTable.propTypes = {
  // Data props
  shops: PropTypes.arrayOf(
    PropTypes.shape({
      id: PropTypes.string,
      _id: PropTypes.string,
      shopId: PropTypes.string,
      shopName: PropTypes.string,
      shopAddress: PropTypes.string,
      ownerName: PropTypes.string,
      email: PropTypes.string,
      phone: PropTypes.string,
      status: PropTypes.string,
      verified: PropTypes.bool,
      createdAt: PropTypes.string,
      updatedAt: PropTypes.string,
      subscription: PropTypes.shape({
        plan: PropTypes.oneOfType([PropTypes.string, PropTypes.object]),
        status: PropTypes.string,
        startDate: PropTypes.string,
        expiresAt: PropTypes.string,
        daysLeft: PropTypes.number
      })
    })
  ),
  
  // UI state props
  isLoading: PropTypes.bool,
  
  // Action callbacks
  onView: PropTypes.func,
  onEdit: PropTypes.func,
  onDelete: PropTypes.func,
  onSuspend: PropTypes.func,
  onReactivate: PropTypes.func,
  
  // Pagination props
  currentPage: PropTypes.number,
  pageSize: PropTypes.number,
  totalShops: PropTypes.number,
  totalPages: PropTypes.number,
  onPageChange: PropTypes.func,
  onPageSizeChange: PropTypes.func
};

export default ShopsTable;
