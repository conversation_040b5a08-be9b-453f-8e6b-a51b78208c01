/**
 * Plan Export Controller
 * Handles export requests for plans
 */
const BaseExportController = require('./baseExportController');
const PlanService = require('../../services/PlanService');
const { logError } = require('../../utils');

class PlanExportController extends BaseExportController {
  /**
   * Get plan export fields
   * @param {Object} req - Express request object
   * @returns {Array} Field configurations
   */
  static async getExportFields(req) {
    return [
      { 
        label: 'Plan Name',
        key: 'name',
        type: 'string'
      },
      {
        label: 'Display Name',
        key: 'displayName',
        type: 'string'
      },
      {
        label: 'Type',
        key: 'type',
        type: 'string'
      },
      {
        label: 'Base Price',
        key: 'pricing.basePrice',
        type: 'currency',
        currency: 'USD'
      },
      {
        label: 'Currency',
        key: 'pricing.currency',
        type: 'string'
      },
      {
        label: 'Billing Cycle',
        key: 'pricing.billingCycle',
        type: 'string'
      },
      {
        label: 'Trial Days',
        key: 'pricing.trialDays',
        type: 'number'
      },
      {
        label: 'Status',
        key: 'isActive',
        type: 'boolean'
      },
      {
        label: 'Created At',
        key: 'createdAt',
        type: 'datetime'
      },
      {
        label: 'Updated At',
        key: 'updatedAt',
        type: 'datetime'
      }
    ];
  }

  /**
   * Get plan data for export
   * @param {Object} req - Express request object
   * @returns {Array} Plan data
   */
  static async getExportData(req) {
    try {
      const { includeInactive, planType, minPrice, maxPrice } = req.query;
      
      // Get all plans (including inactive if requested)
      const allPlans = await PlanService.getAllPlans(includeInactive === 'true');
      
      // Apply client-side filtering since the service doesn't support filters
      let filteredPlans = allPlans;
      
      if (planType) {
        filteredPlans = filteredPlans.filter(plan => plan.type === planType);
      }
      
      if (minPrice || maxPrice) {
        filteredPlans = filteredPlans.filter(plan => {
          const price = plan.pricing?.basePrice || 0;
          let matches = true;
          
          if (minPrice && price < parseFloat(minPrice)) {
            matches = false;
          }
          
          if (maxPrice && price > parseFloat(maxPrice)) {
            matches = false;
          }
          
          return matches;
        });
      }

      return filteredPlans;
    } catch (error) {
      logError('Failed to get plan data for export', 'PlanExportController', error);
      throw error;
    }
  }

  /**
   * Export plans to CSV
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next middleware function
   */
  static async exportToCSV(req, res, next) {
    req.exportConfig = {
      module: 'plans',
      getFields: this.getExportFields,
      getData: this.getExportData,
      filename: req.query.filename
    };

    await this.prepareExport(req, res, next);
    return super.exportToCSV(req, res, next);
  }

  /**
   * Export plans to Excel
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next middleware function
   */
  static async exportToExcel(req, res, next) {
    req.exportConfig = {
      module: 'plans',
      getFields: this.getExportFields,
      getData: this.getExportData,
      filename: req.query.filename,
      options: {
        sheetName: 'Plans',
        styling: {
          header: true,
          columns: {
            'pricing.basePrice': { width: 15, alignment: { horizontal: 'right' } },
            'createdAt': { width: 20 },
            'updatedAt': { width: 20 }
          }
        }
      }
    };

    await this.prepareExport(req, res, next);
    return super.exportToExcel(req, res, next);
  }
}

module.exports = PlanExportController; 