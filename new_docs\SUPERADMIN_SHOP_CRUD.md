# 🏪 SuperAdmin Shop CRUD Operations

## Overview
The SuperAdmin Registration system now includes **complete CRUD operations** for shop management, providing SuperAdmins with full shop lifecycle management capabilities within the registration context.

## 🎯 Available Operations

### 1. 📝 CREATE Operations

#### Create Shop with Admin User
```http
POST /api/register/admin/create-shop
Authorization: Bearer <superAdmin_token>
Content-Type: multipart/form-data

{
  "fullName": "John Doe",
  "email": "<EMAIL>", 
  "phone": "+252612345678",
  "password": "SecurePass123!",
  "shopName": "John's Electronics",
  "shopAddress": "123 Main Street, Mogadishu",
  "planType": "monthly",
  "paymentMethod": "admin_created",
  "initialPaid": true,
  "discountCode": "SAVE10"
}
```
**Result**: Creates shop + admin user in single transaction

#### Create Admin for Existing Shop  
```http
POST /api/register/admin/create-admin
Authorization: Bearer <superAdmin_token>

{
  "shopId": "SHP001",
  "adminFullName": "<PERSON>",
  "adminEmail": "<EMAIL>",
  "adminPhone": "+252612345679", 
  "adminPassword": "AdminPass123!",
  "replaceExistingAdmin": false
}
```

### 2. 👀 READ Operations

#### Get All Shops
```http
GET /api/register/admin/shops
Authorization: Bearer <superAdmin_token>
```
**Query Parameters**: `page`, `limit`, `status`, `search`

#### Get Shop by ID
```http
GET /api/register/admin/shops/:shopId
Authorization: Bearer <superAdmin_token>
```

#### Get Shop Statistics
```http
GET /api/register/admin/shops-stats
Authorization: Bearer <superAdmin_token>
```

### 3. ✏️ UPDATE Operations

#### Update Shop Information
```http
PUT /api/register/admin/shops/:shopId
Authorization: Bearer <superAdmin_token>

{
  "name": "Updated Shop Name",
  "address": "New Address",
  "isActive": true
}
```

#### Update Shop Logo
```http
PUT /api/register/admin/shops/:shopId/logo
Authorization: Bearer <superAdmin_token>
Content-Type: multipart/form-data

shopLogo: <file>
```

#### Change Shop Status (Suspend/Activate)
```http
PUT /api/register/admin/shops/:shopId/status
Authorization: Bearer <superAdmin_token>

{
  "status": "suspended",
  "reason": "Violation of terms of service"
}
```

#### Approve Shop Registration
```http
POST /api/register/admin/approve-shop/:shopId
Authorization: Bearer <superAdmin_token>

{
  "approvalNotes": "Shop approved after verification",
  "activateImmediately": true
}
```

#### Verify Shop Payment
```http
PUT /api/register/admin/shops/:shopId/payment
Authorization: Bearer <superAdmin_token>

{
  "paymentVerified": true,
  "transactionId": "TXN123456",
  "paymentMethod": "EVC Plus",
  "amount": 50,
  "notes": "Payment verified manually"
}
```

### 4. 🗑️ DELETE Operations

#### Delete Shop (Soft Delete)
```http
DELETE /api/register/admin/shops/:shopId
Authorization: Bearer <superAdmin_token>

{
  "reason": "Shop requested closure",
  "transferData": false,
  "notifyUsers": true
}
```

## 🎨 Complete API Map

```
SuperAdmin Registration & Shop Management:
├── Registration
│   ├── POST /api/register/admin/create-shop
│   ├── POST /api/register/admin/create-admin  
│   └── POST /api/register/admin/approve-shop/:id
├── Read Operations
│   ├── GET  /api/register/admin/shops
│   ├── GET  /api/register/admin/shops/:id
│   └── GET  /api/register/admin/shops-stats
├── Update Operations
│   ├── PUT  /api/register/admin/shops/:id
│   ├── PUT  /api/register/admin/shops/:id/logo
│   ├── PUT  /api/register/admin/shops/:id/status
│   └── PUT  /api/register/admin/shops/:id/payment
└── Delete Operations
    └── DELETE /api/register/admin/shops/:id
```

## 🔗 Integration Benefits

### 1. **Unified Context**
- All SuperAdmin shop operations in one place
- Consistent `/api/register/admin/*` prefix
- Integrated with registration workflow

### 2. **Complete Shop Lifecycle**
```
Create → Read → Update → Approve → Activate → Manage → Delete
```

### 3. **Enhanced Authorization**
- All operations restricted to SuperAdmin only
- Consistent authentication/authorization patterns
- Proper audit logging for all operations

### 4. **Reused Components**
- Uses existing ShopController functions
- Leverages existing validation schemas
- Maintains transaction safety

## 🚀 Usage Examples

### Complete Shop Onboarding Flow
```javascript
// 1. Create shop with admin
POST /api/register/admin/create-shop
→ Shop: active, User: active

// 2. View created shop
GET /api/register/admin/shops/SHP001
→ Shop details with admin info

// 3. Update shop if needed
PUT /api/register/admin/shops/SHP001
→ Updated shop information

// 4. Monitor via statistics
GET /api/register/admin/shops-stats
→ Dashboard analytics
```

### Shop Management Flow
```javascript
// 1. List all shops
GET /api/register/admin/shops
→ Paginated shop list

// 2. Suspend problematic shop
PUT /api/register/admin/shops/SHP002/status
{ "status": "suspended", "reason": "Policy violation" }

// 3. Verify payment for pending shop
PUT /api/register/admin/shops/SHP003/payment
{ "paymentVerified": true, "transactionId": "TXN123" }

// 4. Delete closed shop
DELETE /api/register/admin/shops/SHP004
{ "reason": "Business closure" }
```

## ✅ Benefits

1. **Complete CRUD**: All shop operations available in SuperAdmin context
2. **Unified Interface**: Single API prefix for all SuperAdmin operations  
3. **Enhanced Control**: Full shop lifecycle management
4. **Consistent Authorization**: SuperAdmin-only access with proper validation
5. **Integrated Workflow**: Registration and management in one place

This provides SuperAdmins with complete shop management capabilities while maintaining the clean separation between registration and general shop operations. 