/**
 * Export System Logs Service
 * 
 * Exports system logs in various formats (CSV, PDF) with optional filtering
 */
import settingsAPI from '../../api/modules/settings';
import { handleError } from '../baseService';

/**
 * Export system logs with filters
 * @param {Object} filters - Log filters including export format
 * @returns {string} Exported logs data
 */
const exportSystemLogs = async (filters = {}) => {
  try {
    const response = await settingsAPI.exportSystemLogs(filters);
    return response.data;
  } catch (error) {
    handleError(error, 'SettingsService.exportSystemLogs', true);
    return null;
  }
};

export default exportSystemLogs;
