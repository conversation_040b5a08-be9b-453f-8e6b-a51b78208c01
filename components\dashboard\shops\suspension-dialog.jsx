"use client";

import { useState } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Textarea } from "@/components/ui/textarea";
import { ExclamationTriangleIcon } from '@radix-ui/react-icons';

export function SuspensionDialog({ 
  open, 
  onOpenChange, 
  shop, 
  onConfirm, 
  mode = 'suspend' // 'suspend' or 'reactivate' 
}) {
  const [reason, setReason] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleClose = () => {
    setReason('');
    setIsSubmitting(false);
    onOpenChange(false);
  };

  const handleConfirm = async () => {
    if (mode === 'suspend' && (!reason.trim() || reason.trim().length < 3)) {
      return; // Require a valid reason for suspension (minimum 3 characters)
    }

    setIsSubmitting(true);
    try {
      await onConfirm(shop, reason);
      handleClose();
    } catch (error) {
      console.error('Action failed:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const isSuspending = mode === 'suspend';
  const title = isSuspending ? 'Suspend Shop' : 'Reactivate Shop';
  const description = isSuspending
    ? `Are you sure you want to suspend "${shop?.shopName}"? This will prevent the shop and all its users from accessing the system.`
    : `Are you sure you want to reactivate "${shop?.shopName}"? This will restore access for the shop and all its users.`;

  return (
    <AlertDialog open={open} onOpenChange={onOpenChange}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle className="flex items-center gap-2">
            {isSuspending && (
              <ExclamationTriangleIcon className="h-5 w-5 text-destructive" />
            )}
            {title}
          </AlertDialogTitle>
          <AlertDialogDescription>
            {description}
          </AlertDialogDescription>
        </AlertDialogHeader>

        {isSuspending && (
          <div className="my-4">
            <label htmlFor="reason" className="block text-sm font-medium mb-2">
              Reason for suspension <span className="text-destructive">*</span>
            </label>
            <Textarea
              id="reason"
              placeholder="Please provide a detailed reason for suspending this shop (minimum 3 characters)"
              value={reason}
              onChange={(e) => setReason(e.target.value)}
              className="w-full min-h-[100px]"
              required
            />
            {isSuspending && reason.trim().length > 0 && reason.trim().length < 3 && (
              <p className="text-sm text-destructive mt-1">
                Reason must be at least 3 characters
              </p>
            )}
            {isSuspending && !reason.trim() && (
              <p className="text-sm text-destructive mt-1">
                A reason is required for suspension
              </p>
            )}
          </div>
        )}

        <AlertDialogFooter>
          <AlertDialogCancel onClick={handleClose}>Cancel</AlertDialogCancel>
          <Button
            variant={isSuspending ? "destructive" : "default"}
            onClick={handleConfirm}
            disabled={(isSuspending && (!reason.trim() || reason.trim().length < 3)) || isSubmitting}
          >
            {isSubmitting
              ? (isSuspending ? "Suspending..." : "Reactivating...")
              : (isSuspending ? "Suspend Shop" : "Reactivate Shop")}
          </Button>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
