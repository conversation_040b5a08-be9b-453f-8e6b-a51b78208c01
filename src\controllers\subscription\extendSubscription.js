/**
 * Extend Subscription Controller
 * Handles extending subscription duration (admin function)
 */
const { SubscriptionService } = require('../../services');
const { AppError, logError } = require('../../utils');

/**
 * Extend a subscription (admin function)
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
const extendSubscription = async (req, res, next) => {
  try {
    const { subscriptionId } = req.params;
    const { days, reason } = req.body;
    
    // Validate days
    if (!days || days <= 0) {
      return next(new AppError('Valid number of days required', 400, 'validation_error'));
    }
    
    // For security tracking, we pass actor information to service
    const options = {
      actorId: req.user?.userId || 'system',
      actorRole: req.user?.role || 'system'
    };
    
    // Extend subscription through service
    const updatedSubscription = await SubscriptionService.extendSubscription(
      subscriptionId, 
      { 
        days, 
        reason: reason || 'Administrative extension', 
        performedBy: req.user.userId 
      }, 
      options
    );
    
    return res.status(200).json({
      success: true,
      message: `Subscription extended by ${days} days`,
      data: {
        subscriptionId: updatedSubscription.subscriptionId,
        status: updatedSubscription.status,
        endDate: updatedSubscription.dates.endDate,
        daysAdded: days
      }
    });
  } catch (error) {
    logError('Failed to extend subscription', 'SubscriptionController', error);
    return next(error);
  }
};

module.exports = extendSubscription;
