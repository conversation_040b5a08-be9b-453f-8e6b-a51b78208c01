const mongoose = require('mongoose');
const UserService = require('../../services/userService');
const ShopService = require('../../services/shopService');
const DiscountService = require('../../services/discountService');
const EmailService = require('../../services/emailService');
const {
  AppError,
  generateVerificationCode,
  ResponseHelper,
  UserHelper,
  ShopHelper,
  LogHelper,
  logInfo,
  logSuccess,
  logWarning,
  logError,
  TransactionHelper
} = require('../../utils');

/**
 * SuperAdmin Registration Operations
 * Allows SuperAdmins to create shops and admin users through the new registration system
 */

/**
 * Create shop with admin user (SuperAdmin only)
 * POST /api/register/admin/create-shop
 * Uses SAME payload structure as public registration but creates shop immediately
 */
const createShopWithAdmin = async (req, res, next) => {
  try {
    // Validate SuperAdmin role
    if (req.user.role !== 'superAdmin') {
      return next(new AppError('Only SuperAdmins can create shops', 403, 'access_denied'));
    }

    // Use SAME payload structure as public registration
    const {
      // User data (same as public registration)
      fullName,
      email,
      phone,
      password,
      // Shop data (same as public registration)
      shopName,
      shopAddress,
      // Business category data (same as public registration)
      businessCategory = 'general_store',
      businessType = 'retail',
      // Subscription data (same as public registration)
      planType = 'monthly', // Default to monthly for SuperAdmin created shops
      registeredBy = 'superAdmin', // Override to indicate SuperAdmin creation
      paymentMethod = 'admin_created',
      initialPaid = true, // SuperAdmin shops are pre-paid
      // Optional (same as public registration)
      paymentDetails: rawPaymentDetails,
      discountCode
    } = req.validatedData || req.body;

    // Use SAME transaction logic as public registration but with different statuses
    let paymentDetails = rawPaymentDetails || {};
    
    const result = await TransactionHelper.withTransaction(async (session) => {
      let userData;
      let shopId = null;
      let shopLogoData = null;
      let discountDetails = null;
      let verificationCode = generateVerificationCode(6); // Same as public registration

      try {
        // Process shop logo file if uploaded (same as public registration)
        if (req.file) {
          const FileUploadService = require('../../services/fileUploadService');
          shopLogoData = await FileUploadService.saveShopLogo(req.file);
        }

        // Check discount code (same as public registration)
        if (discountCode) {
          discountDetails = await DiscountService.verifyDiscountCode(discountCode);

          if (!discountDetails.valid) {
            throw new AppError(
              discountDetails.message || 'Invalid discount code',
              400,
              'invalid_discount_code'
            );
          }

          logInfo(`Discount code ${discountCode} applied to SuperAdmin shop registration`, 'superAdminController');
        }

        // Create shop with SAME structure but different statuses for SuperAdmin
        const shopData = {
          shopName,
          ownerName: fullName, // Use same field names as public registration
          email,
          phone,
          address: shopAddress,
          // Business details with category (same as public registration)
          businessDetails: {
            type: businessType,
            category: businessCategory
          },
          status: 'active', // SuperAdmin shops are immediately active (vs 'pending')
          access: {
            isPaid: true,     // SuperAdmin shops are pre-paid (vs false)
            isActivated: true // SuperAdmin shops are pre-activated (vs false)
          },
          paymentHistory: [], // Same as public registration
          subscription: {
            planType,
            paymentMethod,
            initialPaid, // true for SuperAdmin
            paymentDetails,
            discountDetails: discountDetails?.valid ? discountDetails.discountDetails : null,
            status: 'active' // Active subscription (vs 'pending_payment')
          },
          registeredBy, // 'superAdmin'
          logoUrl: shopLogoData?.url || '', // Same as public registration
          session
        };

        const createdShop = await ShopService.createShop(shopData);
        shopId = createdShop.shopId;

        // Create subscription using the existing service (same as self-registration)
        // This ensures consistent subscription creation and proper linking
        const createSubscription = require('../../services/Subscription/createSubscription');
        const { Plan } = require('../../models');
        
        const plan = await Plan.findOne({ type: planType });
        if (!plan) {
          throw new AppError(`Subscription plan '${planType}' not found.`, 404, 'plan_not_found');
        }
        
        try {
          const subscriptionData = {
            shopId: createdShop.shopId,
            planType: planType,
            planId: plan.planId,
            planName: plan.displayName || plan.name,
            pricing: {
              basePrice: plan.pricing.basePrice,
              currency: plan.pricing.currency
            },
            paymentMethod: paymentMethod,
            paymentDetails: paymentDetails,
            discountDetails: discountDetails?.valid ? discountDetails.discountDetails : null,
            session
          };

          const createdSubscription = await createSubscription(subscriptionData, { 
            actorId: req.user.userId, 
            actorRole: req.user.role 
          });
          
          // Link subscription to shop
          createdShop.currentSubscriptionId = createdSubscription.subscriptionId;
          await createdShop.save({ session });
          
          logSuccess(`Subscription ${createdSubscription.subscriptionId} created and linked to shop ${createdShop.shopId}`, 'superAdminController');
        } catch (subscriptionError) {
          logError(`Failed to create subscription for SuperAdmin shop ${createdShop.shopId}: ${subscriptionError.message}`, 'superAdminController');
          // Don't fail the entire process, but log the error
        }

        // Create user with SAME structure but different statuses for SuperAdmin
        userData = await UserService.createUser({
          fullName,
          email,
          phone,
          password,
          role: 'admin', // Shop owner is always admin (same as public)
          shopId: shopId,
          registeredBy, // 'superAdmin'
          status: 'active', // SuperAdmin created users are immediately active (vs 'pending_email_verification')
          verified: true, // SuperAdmin created users are pre-verified (vs false)
          emailVerified: true, // SuperAdmin created users are pre-verified (vs false)
          isPaid: true, // SuperAdmin created users are pre-paid (vs false)
          isActivated: true, // SuperAdmin created users are pre-activated (vs false)
          verificationCode, // Still generate for consistency but won't be used
        }, { session });

        // Update shop with owner reference (same as public registration)
        createdShop.ownerId = userData.userId;
        await createdShop.save({ session });

        // Log the creation
        await LogHelper.createUserLog(
          'shop_created_by_superadmin',
          {
            actorId: req.user.userId,
            targetId: userData.userId,
            actorRole: req.user.role,
            shopId: shopId,
            details: { shopName, email }
          },
          session
        );

        logSuccess(`SuperAdmin ${req.user.userId} created shop ${shopId} with user ${userData.userId}`, 'superAdminController');

        return { userData, shopId, verificationCode };
      } catch (error) {
        throw error;
      }
    }, { name: 'SuperAdminCreateShop' });

    // Get result from transaction (same as public registration)
    const { userData, shopId, verificationCode } = result;

    // Send welcome email to new admin (outside transaction) - optional since already activated
    try {
      await EmailService.auth.sendWelcomeEmail(userData, {
        invitedBy: req.user.fullName,
        shopName: shopName,
        createdBySuperAdmin: true
      });
      logSuccess(`Welcome email sent to new user ${userData.email}`, 'superAdminController');
    } catch (emailError) {
      logError(`Failed to send welcome email to ${userData.email}`, 'superAdminController', emailError);
      // Don't fail the operation if email fails
    }

    // Return success response (similar structure to public registration)
    return ResponseHelper.success(
      res,
      'Shop and user created successfully by SuperAdmin',
      {
        user: UserHelper.sanitizeUser(userData),
        shop: {
          id: shopId,
          name: shopName
        },
        nextStep: 'registration_complete' // SuperAdmin shops are immediately complete
      },
      201 // Created
    );
  } catch (error) {
    logError(`SuperAdmin shop creation failed: ${error.message}`, 'superAdminController', error);

    if (error instanceof AppError) {
      return next(error);
    }

    return next(new AppError(
      error.message || 'Error creating shop and admin user',
      error.statusCode || 500,
      error.type || 'superadmin_shop_creation_error'
    ));
  }
};

/**
 * Create admin user for existing shop (SuperAdmin only)
 * POST /api/register/admin/create-admin
 */
const createAdminForShop = async (req, res, next) => {
  try {
    // Validate SuperAdmin role
    if (req.user.role !== 'superAdmin') {
      return next(new AppError('Only SuperAdmins can create admin users', 403, 'access_denied'));
    }

    const {
      shopId,
      adminFullName,
      adminEmail,
      adminPhone,
      adminPassword,
      replaceExistingAdmin = false
    } = req.validatedData || req.body;

    // Validate shop exists
    const shop = await ShopService.getShopById(shopId, { includeInactive: true });
    if (!shop) {
      return next(new AppError('Shop not found', 404, 'shop_not_found'));
    }

    // Check if shop already has an admin (if not replacing)
    if (!replaceExistingAdmin && shop.ownerId) {
      const existingAdmin = await UserService.getUserById(shop.ownerId);
      if (existingAdmin && existingAdmin.role === 'admin') {
        return next(new AppError('Shop already has an admin user. Set replaceExistingAdmin=true to replace.', 409, 'admin_already_exists'));
      }
    }

    const result = await TransactionHelper.withTransaction(async (session) => {
      const verificationCode = generateVerificationCode(6);

      // Create admin user
      const adminData = await UserService.createUser({
        fullName: adminFullName,
        email: adminEmail,
        phone: adminPhone,
        password: adminPassword,
        role: 'admin',
        shopId: shopId,
        registeredBy: req.user.userId,
        status: 'pending_email_verification',
        verified: false,
        emailVerified: false,
        isPaid: shop.access.isPaid, // Inherit from shop
        isActivated: false,
        verificationCode,
        createdBy: req.user.userId
      }, { session });

      // Update shop owner reference
      shop.ownerId = adminData.userId;
      await shop.save({ session });

      // Log the creation
      await LogHelper.createUserLog(
        'admin_created_by_superadmin',
        {
          actorId: req.user.userId,
          targetId: adminData.userId,
          actorRole: req.user.role,
          shopId: shopId,
          details: { adminEmail, shopName: shop.shopName }
        },
        session
      );

      return { adminData, verificationCode };
    }, { name: 'SuperAdminCreateAdmin' });

    // Send welcome email
    try {
      await EmailService.auth.sendAdminWelcomeEmail(result.adminData, result.verificationCode, {
        invitedBy: req.user.fullName,
        shopName: shop.shopName,
        createdBySuperAdmin: true
      });
      logSuccess(`Welcome email sent to new admin ${result.adminData.email}`, 'superAdminController');
    } catch (emailError) {
      logError(`Failed to send welcome email to ${result.adminData.email}`, 'superAdminController', emailError);
    }

    return ResponseHelper.success(
      res,
      'Admin user created successfully for shop',
      {
        shop: {
          id: shopId,
          name: shop.shopName
        },
        admin: UserHelper.sanitizeUser(result.adminData),
        nextStep: 'admin_email_verification_required'
      },
      201
    );
  } catch (error) {
    logError(`SuperAdmin admin creation failed: ${error.message}`, 'superAdminController', error);
    return next(new AppError(
      error.message || 'Error creating admin user',
      error.statusCode || 500,
      error.type || 'superadmin_admin_creation_error'
    ));
  }
};

/**
 * Approve pending shop registration (SuperAdmin only)
 * POST /api/register/admin/approve-shop/:shopId
 * 
 * UPDATED: Handles offline payment method specifically
 * - If payment method is "offline", SuperAdmin approval acts as payment confirmation
 * - Automatically activates shop and user when offline payment is approved
 */
const approveShopRegistration = async (req, res, next) => {
  try {
    // Validate SuperAdmin role
    if (req.user.role !== 'superAdmin') {
      return next(new AppError('Only SuperAdmins can approve shop registrations', 403, 'access_denied'));
    }

    const { shopId } = req.params;
    const { 
      approvalNotes, 
      activateImmediately = true,
      // NEW: Offline payment confirmation fields
      confirmOfflinePayment = true,
      offlinePaymentDetails = {}
    } = req.validatedData || req.body;

    const result = await TransactionHelper.withTransaction(async (session) => {
      // Get shop with subscription details
      const shop = await ShopService.getShopById(shopId, { session, includeInactive: true });
      if (!shop) {
        throw new AppError('Shop not found', 404, 'shop_not_found');
      }

      // Get shop's subscription to check payment method
      const Subscription = require('../../models/subscription.model');
      let subscription = null;
      if (shop.currentSubscriptionId) {
        subscription = await Subscription.findOne({ subscriptionId: shop.currentSubscriptionId }).session(session);
      }

      // Check if this is an offline payment shop
      const isOfflinePayment = subscription && 
        (subscription.payment.method === 'offline' || 
         subscription.payment.method === 'cash' ||
         !subscription.payment.verified);

      logInfo(`Shop ${shopId} approval: isOfflinePayment=${isOfflinePayment}, paymentMethod=${subscription?.payment?.method}`, 'superAdminController');

      // Update shop status
      shop.status = 'approved';
      shop.approvedBy = req.user.userId;
      shop.approvedAt = new Date();
      shop.approvalNotes = approvalNotes || 'Approved by SuperAdmin';

      // SPECIAL HANDLING FOR OFFLINE PAYMENTS
      if (isOfflinePayment && confirmOfflinePayment && activateImmediately) {
        // SuperAdmin approval confirms offline payment
        shop.access.isPaid = true;
        shop.access.isActivated = true;
        shop.status = 'active';
        
        // Update subscription payment status
        if (subscription) {
          subscription.payment.verified = true;
          subscription.payment.lastPaymentDate = new Date();
          subscription.payment.paymentDetails = {
            ...subscription.payment.paymentDetails,
            ...offlinePaymentDetails,
            approvedBy: req.user.userId,
            approvedAt: new Date(),
            approvalNotes: 'Offline payment confirmed by SuperAdmin'
          };
          subscription.status = 'active';
          await subscription.save({ session });
          
          logInfo(`Offline payment confirmed for subscription ${subscription.subscriptionId}`, 'superAdminController');
        }
      } else if (activateImmediately && shop.access.isPaid) {
        // Regular activation for already paid shops
        shop.access.isActivated = true;
        shop.status = 'active';
      }

      await shop.save({ session });

      // Update shop owner status if exists
      let shopOwner = null;
      if (shop.ownerId) {
        shopOwner = await UserService.getUserById(shop.ownerId, { session });
        if (shopOwner) {
          // For offline payments, also update user payment status
          if (isOfflinePayment && confirmOfflinePayment && activateImmediately) {
            shopOwner.isPaid = true;
            shopOwner.isActivated = true;
            shopOwner.status = 'active';
            logInfo(`Offline payment confirmed for user ${shopOwner.userId}`, 'superAdminController');
          } else if (activateImmediately && shopOwner.isPaid && shopOwner.emailVerified) {
            shopOwner.isActivated = true;
            shopOwner.status = 'active';
          }
          await shopOwner.save({ session });
        }
      }

      // Log approval with payment details
      await LogHelper.createUserLog(
        'shop_approved_by_superadmin',
        {
          actorId: req.user.userId,
          targetId: shop.ownerId,
          actorRole: req.user.role,
          shopId: shopId,
          details: { 
            approvalNotes, 
            activateImmediately,
            isOfflinePayment,
            offlinePaymentConfirmed: isOfflinePayment && confirmOfflinePayment
          }
        },
        session
      );

      return { shop, shopOwner, subscription, isOfflinePayment };
    }, { name: 'SuperAdminApproveShop' });

    // Send approval email to shop owner
    if (result.shopOwner) {
      try {
        const emailContext = {
          approvedBy: req.user.fullName,
          approvalNotes: approvalNotes,
          isOfflinePayment: result.isOfflinePayment,
          paymentConfirmed: result.isOfflinePayment && confirmOfflinePayment,
          activationStatus: result.shop.access.isActivated
        };
        
        await EmailService.auth.sendShopApprovalEmail(result.shopOwner, result.shop, emailContext);
        logSuccess(`Shop approval email sent to ${result.shopOwner.email}`, 'superAdminController');
      } catch (emailError) {
        logError(`Failed to send approval email to ${result.shopOwner.email}`, 'superAdminController', emailError);
      }
    }

    // Determine response message based on payment method and activation
    let responseMessage = 'Shop registration approved successfully';
    if (result.isOfflinePayment && confirmOfflinePayment) {
      responseMessage = 'Shop registration approved and offline payment confirmed successfully';
    } else if (result.isOfflinePayment && !confirmOfflinePayment) {
      responseMessage = 'Shop registration approved - offline payment still pending confirmation';
    }

    return ResponseHelper.success(
      res,
      responseMessage,
      {
        shop: {
          id: shopId,
          name: result.shop.shopName,
          status: result.shop.status,
          approvedAt: result.shop.approvedAt,
          isActivated: result.shop.access.isActivated,
          isPaid: result.shop.access.isPaid
        },
        owner: result.shopOwner ? UserHelper.sanitizeUser(result.shopOwner) : null,
        payment: {
          method: result.subscription?.payment?.method || 'unknown',
          isOfflinePayment: result.isOfflinePayment,
          paymentConfirmed: result.isOfflinePayment ? confirmOfflinePayment : result.shop.access.isPaid,
          subscriptionStatus: result.subscription?.status || 'unknown'
        }
      }
    );
  } catch (error) {
    logError(`SuperAdmin shop approval failed: ${error.message}`, 'superAdminController', error);
    return next(new AppError(
      error.message || 'Error approving shop registration',
      error.statusCode || 500,
      error.type || 'superadmin_approval_error'
    ));
  }
};

module.exports = {
  createShopWithAdmin,
  createAdminForShop,
  approveShopRegistration
};
