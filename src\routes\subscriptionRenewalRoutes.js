/**
 * Subscription Renewal Routes
 * Integrates with existing payment system for subscription renewals
 */
const express = require('express');
const router = express.Router();
const authMiddleware = require('../middleware/authMiddleware');
const { validate } = require('../middleware/validationMiddleware');
const subscriptionRenewalController = require('../controllers/subscriptionRenewalController');

// All subscription renewal routes require authentication
router.use(authMiddleware.authenticate);

/**
 * POST /api/subscriptions/renew
 * Process subscription renewal payment using existing payment methods
 */
router.post('/renew',
  authMiddleware.authorize(['admin', 'shopOwner']),
  validate({
    body: {
      type: 'object',
      properties: {
        planType: {
          type: 'string',
          enum: ['monthly', 'yearly', 'quarterly'],
          description: 'Subscription plan type'
        },
        paymentMethod: {
          type: 'string',
          enum: ['EVC Plus', 'Cash', 'Bank Transfer', 'Check', 'Other', 'offline'],
          description: 'Payment method to use'
        },
        phoneNumber: {
          type: 'string',
          pattern: '^252[0-9]{8,9}$',
          description: 'Phone number for EVC Plus payments (optional if user has registered phone)'
        }
      },
      required: ['planType', 'paymentMethod'],
      additionalProperties: false
    }
  }),
  subscriptionRenewalController.processRenewalPayment
);

/**
 * GET /api/subscriptions/payment-methods
 * Get available payment methods for subscription renewal
 */
router.get('/payment-methods',
  authMiddleware.authorize(['admin', 'shopOwner']),
  subscriptionRenewalController.getAvailablePaymentMethods
);

/**
 * GET /api/subscriptions/renewal/:paymentId/status
 * Check status of a renewal payment
 */
router.get('/renewal/:paymentId/status',
  authMiddleware.authorize(['admin', 'shopOwner']),
  subscriptionRenewalController.checkRenewalPaymentStatus
);

module.exports = router; 