/**
 * Bulk Update Subscriptions Controller - SuperAdmin Only
 * Allows performing bulk operations on subscriptions:
 * - Change status
 * - Change plans
 * - Apply discounts
 * - Extend expirations
 */
const { SubscriptionService } = require('../../services');
const { logInfo, logSuccess, logError } = require('../../utils');

/**
 * Bulk update subscriptions (SuperAdmin only)
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
const bulkUpdateSubscriptions = async (req, res, next) => {
  try {
    // Only SuperAdmin can perform bulk operations
    if (req.user.role !== 'superAdmin') {
      return res.status(403).json({
        success: false,
        message: 'Access denied. SuperAdmin privileges required.'
      });
    }
    
    const { 
      subscriptionIds, 
      action, 
      data 
    } = req.body;
    
    if (!subscriptionIds || !Array.isArray(subscriptionIds) || subscriptionIds.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'No subscription IDs provided'
      });
    }
    
    if (!action) {
      return res.status(400).json({
        success: false,
        message: 'No action specified'
      });
    }
    
    logInfo(`Performing bulk action: ${action} on ${subscriptionIds.length} subscriptions`, 'SubscriptionController');
    
    let result;
    const options = {
      actorId: req.user.userId,
      actorRole: req.user.role
    };
    
    switch(action) {
      case 'changeStatus':
        if (!data.status) {
          return res.status(400).json({
            success: false,
            message: 'Status is required for changeStatus action'
          });
        }
        result = await SubscriptionService.bulkUpdateStatus(subscriptionIds, data.status, options);
        break;
        
      case 'changePlan':
        if (!data.planId) {
          return res.status(400).json({
            success: false,
            message: 'Plan ID is required for changePlan action'
          });
        }
        result = await SubscriptionService.bulkChangePlan(subscriptionIds, data.planId, options);
        break;
        
      case 'applyDiscount':
        if (!data.discountPercentage && !data.discountAmount) {
          return res.status(400).json({
            success: false,
            message: 'Discount information is required for applyDiscount action'
          });
        }
        result = await SubscriptionService.bulkApplyDiscount(subscriptionIds, data, options);
        break;
        
      case 'extendExpiration':
        if (!data.days) {
          return res.status(400).json({
            success: false,
            message: 'Days to extend is required for extendExpiration action'
          });
        }
        result = await SubscriptionService.bulkExtendExpiration(subscriptionIds, data.days, options);
        break;
        
      default:
        return res.status(400).json({
          success: false,
          message: `Unsupported action: ${action}`
        });
    }
    
    logSuccess(`Bulk ${action} completed successfully on ${result.updatedCount} subscriptions`, 'SubscriptionController');
    
    return res.status(200).json({
      success: true,
      message: `Bulk ${action} operation completed`,
      data: {
        total: subscriptionIds.length,
        updated: result.updatedCount,
        failed: result.failedCount,
        errors: result.errors
      }
    });
  } catch (error) {
    logError(`Failed to perform bulk update on subscriptions: ${error.message}`, 'SubscriptionController', error);
    return next(error);
  }
};

module.exports = bulkUpdateSubscriptions;
