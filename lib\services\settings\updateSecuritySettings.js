/**
 * Update Security Settings Service
 * 
 * Updates security-related system settings for the DeynCare platform
 */
import settingsAPI from '../../api/modules/settings';
import { handleError, handleSuccess } from '../baseService';

/**
 * Update security settings
 * @param {Object} settings - Security settings to update
 * @returns {Object} API response data
 */
const updateSecuritySettings = async (settings) => {
  try {
    const response = await settingsAPI.updateSecuritySettings(settings);
    handleSuccess('Security settings updated successfully');
    return response.data;
  } catch (error) {
    handleError(error, 'SettingsService.updateSecuritySettings', true);
    return { success: false, error: error.message };
  }
};

export default updateSecuritySettings;
