/**
 * Discount Service Index
 * Serves as the centralized export point for all discount-related modules
 */

// Import all discount operations from individual files
const createDiscountCode = require('./createDiscount');
const { getDiscountById, getDiscountByCode } = require('./getDiscount');
const getDiscountCodes = require('./listDiscounts');
const validateAndCalculateDiscount = require('./validateDiscount');
const applyDiscountCode = require('./applyDiscount');
const verifyDiscountCode = require('./verifyDiscount');

// Export all operations as a unified module
module.exports = {
  createDiscountCode,
  getDiscountById,
  getDiscountByCode,
  getDiscountCodes,
  validateAndCalculateDiscount,
  applyDiscountCode,
  verifyDiscountCode
};
