# 🧪 **SuperAdmin Shop CRUD API Testing Guide**

## 📋 **Overview**
Complete testing guide for SuperAdmin Shop CRUD operations with simplified payloads.

---

## 🔐 **Authentication**
All endpoints require SuperAdmin authentication:

```bash
Authorization: Bearer <superAdmin_jwt_token>
Content-Type: application/json (for JSON payloads)
Content-Type: multipart/form-data (for file uploads)
```

---

## 🎯 **API Endpoints Summary**

| **Method** | **Endpoint** | **Description** | **File Upload** |
|------------|-------------|----------------|----------------|
| POST   | `/api/admin/shops` | Create new shop | ✅ Optional Logo |
| GET    | `/api/admin/shops` | Get all shops with pagination | ❌ |
| GET    | `/api/admin/shops/stats` | Get shop statistics | ❌ |
| GET    | `/api/admin/shops/:shopId` | Get shop by ID | ❌ |
| PUT    | `/api/admin/shops/:shopId` | Update shop information | ❌ |
| PUT    | `/api/admin/shops/:shopId/logo` | Update shop logo | ✅ Required Logo |
| PUT    | `/api/admin/shops/:shopId/status` | Change shop status | ❌ |
| DELETE | `/api/admin/shops/:shopId` | Delete shop (soft delete) | ❌ |

---

## 🆕 **CREATE SHOP**

### **Option 1: Create Shop (JSON - No Logo)**
```
POST /api/admin/shops
Content-Type: application/json
```

**Request Body:**
```json
{
  "shopName": "Test Electronics Store",
  "ownerName": "Ahmed Hassan",
  "email": "<EMAIL>",
  "phone": "+252612345678",
  "address": "Hamar Weyne, Mogadishu, Somalia",
  "planType": "monthly"
}
```

**cURL Example:**
```bash
curl -X POST http://localhost:5000/api/admin/shops \
  -H "Authorization: Bearer YOUR_SUPERADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "shopName": "Test Electronics Store",
    "ownerName": "Ahmed Hassan", 
    "email": "<EMAIL>",
    "phone": "+252612345678",
    "address": "Hamar Weyne, Mogadishu, Somalia",
    "planType": "monthly"
  }'
```

### **Option 2: Create Shop with Logo (Multipart)**
```
POST /api/admin/shops
Content-Type: multipart/form-data
```

**cURL Example with Logo:**
```bash
curl -X POST http://localhost:5000/api/admin/shops \
  -H "Authorization: Bearer YOUR_SUPERADMIN_TOKEN" \
  -F "shopName=Test Electronics Store" \
  -F "ownerName=Ahmed Hassan" \
  -F "email=<EMAIL>" \
  -F "phone=+252612345678" \
  -F "address=Hamar Weyne, Mogadishu, Somalia" \
  -F "planType=monthly" \
  -F "shopLogo=@/path/to/logo.png"
```

**Logo Requirements:**
- **File Types:** JPG, PNG, WebP
- **Max Size:** 5MB
- **Field Name:** `shopLogo`

### **Expected Response:**
```json
{
  "success": true,
  "message": "Shop created successfully",
  "data": {
    "shopId": "SHP001234",
    "shopName": "Test Electronics Store",
    "ownerName": "Ahmed Hassan",
    "email": "<EMAIL>",
    "phone": "+252612345678",
    "address": "Hamar Weyne, Mogadishu, Somalia",
    "logoUrl": "http://localhost:5000/api/files/FILE123456",
    "status": "active",
    "verified": true,
    "registeredBy": "superAdmin",
    "access": {
      "isPaid": true,
      "isActivated": true
    },
    "subscription": {
      "planType": "monthly",
      "paymentMethod": "admin_created",
      "initialPaid": true,
      "status": "active"
    },
    "createdAt": "2025-01-23T10:30:00.000Z",
    "updatedAt": "2025-01-23T10:30:00.000Z"
  }
}
```

---

## 📊 **GET SHOP STATISTICS**

### **Endpoint:**
```
GET /api/admin/shops/stats
```

### **cURL Example:**
```bash
curl -X GET http://localhost:5000/api/admin/shops/stats \
  -H "Authorization: Bearer YOUR_SUPERADMIN_TOKEN"
```

### **Expected Response:**
```json
{
  "success": true,
  "data": {
    "totalShops": 45,
    "activeShops": 38,
    "pendingShops": 5,
    "suspendedShops": 2
  }
}
```

---

## 📋 **GET ALL SHOPS**

### **Endpoint:**
```
GET /api/admin/shops
```

### **Query Parameters:**
- `page` (optional): Page number (default: 1)
- `limit` (optional): Items per page (default: 20)
- `status` (optional): Filter by status (active, pending, suspended)
- `search` (optional): Search by shop name, owner name, or email

### **cURL Examples:**

#### **Basic Request:**
```bash
curl -X GET http://localhost:5000/api/admin/shops \
  -H "Authorization: Bearer YOUR_SUPERADMIN_TOKEN"
```

#### **With Pagination:**
```bash
curl -X GET "http://localhost:5000/api/admin/shops?page=2&limit=10" \
  -H "Authorization: Bearer YOUR_SUPERADMIN_TOKEN"
```

#### **With Filters:**
```bash
curl -X GET "http://localhost:5000/api/admin/shops?status=active&search=electronics" \
  -H "Authorization: Bearer YOUR_SUPERADMIN_TOKEN"
```

### **Expected Response:**
```json
{
  "success": true,
  "data": {
    "shops": [
      {
        "shopId": "SHP001234",
        "shopName": "Test Electronics Store",
        "ownerName": "Ahmed Hassan",
        "email": "<EMAIL>",
        "phone": "+252612345678",
        "address": "Hamar Weyne, Mogadishu, Somalia",
        "status": "active",
        "verified": true,
        "createdAt": "2025-01-23T10:30:00.000Z"
      }
    ],
    "pagination": {
      "total": 45,
      "page": 1,
      "limit": 20,
      "pages": 3
    }
  }
}
```

---

## 🔍 **GET SHOP BY ID**

### **Endpoint:**
```
GET /api/admin/shops/:shopId
```

### **cURL Example:**
```bash
curl -X GET http://localhost:5000/api/admin/shops/SHP001234 \
  -H "Authorization: Bearer YOUR_SUPERADMIN_TOKEN"
```

### **Expected Response:**
```json
{
  "success": true,
  "data": {
    "shopId": "SHP001234",
    "shopName": "Test Electronics Store",
    "ownerName": "Ahmed Hassan",
    "email": "<EMAIL>",
    "phone": "+252612345678",
    "address": "Hamar Weyne, Mogadishu, Somalia",
    "status": "active",
    "verified": true,
    "registeredBy": "superAdmin",
    "access": {
      "isPaid": true,
      "isActivated": true
    },
    "subscription": {
      "planType": "monthly",
      "paymentMethod": "admin_created",
      "initialPaid": true,
      "status": "active"
    },
    "statistics": {
      "totalProducts": 0,
      "totalCustomers": 0,
      "totalSales": 0,
      "totalRevenue": 0,
      "totalDebts": 0,
      "totalDebtAmount": 0
    },
    "createdAt": "2025-01-23T10:30:00.000Z",
    "updatedAt": "2025-01-23T10:30:00.000Z"
  }
}
```

---

## ✏️ **UPDATE SHOP**

### **Endpoint:**
```
PUT /api/admin/shops/:shopId
```

### **Request Body:**
```json
{
  "shopName": "Updated Electronics Store",
  "ownerName": "Ahmed Hassan Updated",
  "email": "<EMAIL>",
  "phone": "+252612345679",
  "address": "New Address, Mogadishu, Somalia"
}
```

### **cURL Example:**
```bash
curl -X PUT http://localhost:5000/api/admin/shops/SHP001234 \
  -H "Authorization: Bearer YOUR_SUPERADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "shopName": "Updated Electronics Store",
    "ownerName": "Ahmed Hassan Updated",
    "email": "<EMAIL>",
    "phone": "+252612345679",
    "address": "New Address, Mogadishu, Somalia"
  }'
```

### **Expected Response:**
```json
{
  "success": true,
  "message": "Shop updated successfully",
  "data": {
    "shopId": "SHP001234",
    "shopName": "Updated Electronics Store",
    "ownerName": "Ahmed Hassan Updated",
    "email": "<EMAIL>",
    "phone": "+252612345679",
    "address": "New Address, Mogadishu, Somalia",
    "status": "active",
    "verified": true,
    "updatedAt": "2025-01-23T11:15:00.000Z"
  }
}
```

---

## 🖼️ **UPDATE SHOP LOGO**

### **Endpoint:**
```
PUT /api/admin/shops/:shopId/logo
Content-Type: multipart/form-data
```

### **cURL Example:**
```bash
curl -X PUT http://localhost:5000/api/admin/shops/SHP001234/logo \
  -H "Authorization: Bearer YOUR_SUPERADMIN_TOKEN" \
  -F "shopLogo=@/path/to/new-logo.png"
```

### **Logo Requirements:**
- **File Types:** JPG, PNG, WebP
- **Max Size:** 5MB
- **Field Name:** `shopLogo` (required)

### **Expected Response:**
```json
{
  "success": true,
  "message": "Shop logo updated successfully",
  "data": {
    "shopId": "SHP001234",
    "logoUrl": "http://localhost:5000/api/files/FILE789012",
    "updatedAt": "2025-01-23T11:45:00.000Z"
  }
}
```

---

## 🔄 **CHANGE SHOP STATUS**

### **Endpoint:**
```
PUT /api/admin/shops/:shopId/status
```

### **Request Body:**
```json
{
  "status": "suspended",
  "reason": "Violation of terms of service"
}
```

### **cURL Example:**
```bash
curl -X PUT http://localhost:5000/api/admin/shops/SHP001234/status \
  -H "Authorization: Bearer YOUR_SUPERADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "status": "suspended",
    "reason": "Violation of terms of service"
  }'
```

### **Expected Response:**
```json
{
  "success": true,
  "message": "Shop suspended successfully",
  "data": {
    "shopId": "SHP001234",
    "shopName": "Updated Electronics Store",
    "status": "suspended",
    "statusReason": "Violation of terms of service",
    "statusChangedAt": "2025-01-23T11:30:00.000Z",
    "statusChangedBy": "SUPERADMIN_USER_ID"
  }
}
```

---

## 🗑️ **DELETE SHOP**

### **Endpoint:**
```
DELETE /api/admin/shops/:shopId
```

### **Request Body:**
```json
{
  "reason": "Shop requested closure"
}
```

### **cURL Example:**
```bash
curl -X DELETE http://localhost:5000/api/admin/shops/SHP001234 \
  -H "Authorization: Bearer YOUR_SUPERADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "reason": "Shop requested closure"
  }'
```

### **Expected Response:**
```json
{
  "success": true,
  "message": "Shop deleted successfully",
  "data": {
    "shopId": "SHP001234",
    "deletedAt": "2025-01-23T12:00:00.000Z"
  }
}
```

---

## 🧪 **TESTING SCENARIOS**

### **1. Complete CRUD Flow Test:**
```bash
# 1. Create shop with logo
curl -X POST http://localhost:5000/api/admin/shops \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -F "shopName=Test Shop" \
  -F "ownerName=Test Owner" \
  -F "email=<EMAIL>" \
  -F "phone=+252612345678" \
  -F "address=Test Address" \
  -F "shopLogo=@/path/to/logo.png"

# 2. Get shop stats
curl -X GET http://localhost:5000/api/admin/shops/stats \
  -H "Authorization: Bearer YOUR_TOKEN"

# 3. List all shops
curl -X GET http://localhost:5000/api/admin/shops \
  -H "Authorization: Bearer YOUR_TOKEN"

# 4. Get specific shop
curl -X GET http://localhost:5000/api/admin/shops/SHOP_ID \
  -H "Authorization: Bearer YOUR_TOKEN"

# 5. Update shop
curl -X PUT http://localhost:5000/api/admin/shops/SHOP_ID \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"shopName":"Updated Shop Name"}'

# 6. Update shop logo
curl -X PUT http://localhost:5000/api/admin/shops/SHOP_ID/logo \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -F "shopLogo=@/path/to/new-logo.png"

# 7. Change shop status
curl -X PUT http://localhost:5000/api/admin/shops/SHOP_ID/status \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"status":"suspended","reason":"Test suspension"}'

# 8. Delete shop
curl -X DELETE http://localhost:5000/api/admin/shops/SHOP_ID \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"reason":"Test deletion"}'
```

### **2. Error Testing:**

#### **Invalid Data:**
```bash
curl -X POST http://localhost:5000/api/admin/shops \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"shopName":"","email":"invalid-email"}'
```

#### **Missing Authentication:**
```bash
curl -X GET http://localhost:5000/api/admin/shops
```

#### **Non-existent Shop:**
```bash
curl -X GET http://localhost:5000/api/admin/shops/INVALID_SHOP_ID \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### **3. Logo Upload Error Testing:**

#### **Invalid File Type:**
```bash
curl -X POST http://localhost:5000/api/admin/shops \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -F "shopName=Test Shop" \
  -F "ownerName=Test Owner" \
  -F "email=<EMAIL>" \
  -F "phone=+252612345678" \
  -F "address=Test Address" \
  -F "shopLogo=@/path/to/document.pdf"
```

#### **File Too Large:**
```bash
# Test with file > 5MB
curl -X POST http://localhost:5000/api/admin/shops \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -F "shopName=Test Shop" \
  -F "shopLogo=@/path/to/large-image.jpg"
```

#### **Missing Logo File:**
```bash
curl -X PUT http://localhost:5000/api/admin/shops/SHOP_ID/logo \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

## ⚡ **Quick Test Commands**

### **Environment Setup:**
```bash
# Set your token as environment variable
export SUPERADMIN_TOKEN="your_actual_jwt_token_here"
export API_BASE="http://localhost:5000"

# Test all endpoints quickly
curl -X GET $API_BASE/api/admin/shops/stats -H "Authorization: Bearer $SUPERADMIN_TOKEN"
curl -X GET $API_BASE/api/admin/shops -H "Authorization: Bearer $SUPERADMIN_TOKEN"
```

### **Postman Collection:**
Import this JSON into Postman for easy testing:
```json
{
  "info": {
    "name": "SuperAdmin Shop CRUD",
    "description": "SuperAdmin Shop Management API Testing"
  },
  "auth": {
    "type": "bearer",
    "bearer": [{"key": "token", "value": "{{superAdminToken}}"}]
  },
  "variable": [
    {"key": "baseUrl", "value": "http://localhost:5000"},
    {"key": "superAdminToken", "value": "YOUR_TOKEN_HERE"}
  ]
}
```

---

## 🎯 **Expected Behavior Summary**

### **✅ What Works:**
- SuperAdmin can create shops without verification/payment
- All shops created by SuperAdmin are immediately active
- Full CRUD operations available
- Proper validation and error handling
- Clean, simplified payloads

### **✅ Security Features:**
- Only SuperAdmin role can access endpoints
- JWT authentication required
- Input validation on all payloads
- Proper error responses

### **✅ Performance Features:**
- Pagination support for large datasets
- Search and filtering capabilities
- Efficient database queries
- Proper HTTP status codes

**🚀 Ready for Frontend Integration!** 