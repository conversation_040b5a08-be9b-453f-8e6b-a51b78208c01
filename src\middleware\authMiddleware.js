const { User } = require('../models');
const TokenService = require('../services/tokenService');
const { AppError, logAuth, logWarning, logError } = require('../utils');

// In-memory user cache to reduce database queries
const userCache = new Map();
const CACHE_TTL = 5 * 60 * 1000; // 5 minutes TTL
const MAX_CACHE_SIZE = 1000; // Prevent memory bloat

/**
 * Cache management utilities
 */
const CacheManager = {
  get: (userId) => {
    const cached = userCache.get(userId);
    if (!cached) return null;
    
    // Check if cache entry has expired
    if (Date.now() - cached.timestamp > CACHE_TTL) {
      userCache.delete(userId);
      return null;
    }
    
    return cached.user;
  },
  
  set: (userId, user) => {
    // Cleanup old entries if cache is getting too large
    if (userCache.size >= MAX_CACHE_SIZE) {
      const oldestKey = userCache.keys().next().value;
      userCache.delete(oldestKey);
    }
    
    // Store user data - handle both Mongoose documents and plain objects
    const userData = user && typeof user.toObject === 'function' ? user.toObject() : user;
    
    userCache.set(userId, {
      user: { ...userData }, // Store as plain object
      timestamp: Date.now()
    });
  },
  
  invalidate: (userId) => {
    userCache.delete(userId);
  },
  
  clear: () => {
    userCache.clear();
  }
};

/**
 * Authentication middleware
 * Verifies JWT token and attaches user to request
 */
exports.authenticate = async (req, res, next) => {
  try {
    // Mark that auth middleware was executed
    req.authExecuted = true;
    
    // Get token from Authorization header or cookies
    let token;
    
    if (req.headers.authorization && req.headers.authorization.startsWith('Bearer')) {
      token = req.headers.authorization.split(' ')[1];
    } else if (req.cookies && req.cookies.accessToken) {
      token = req.cookies.accessToken;
    }
    
    if (!token) {
      logError('AUTHENTICATION FAILED: No token found in headers or cookies', 'AuthMiddleware');
      return next(new AppError('Authentication required', 401, 'auth_required'));
    }
    
    // Verify token
    const decoded = TokenService.verifyAccessToken(token);
    console.log(`[AuthMiddleware] Token decoded successfully for user: ${decoded.userId}, role: ${decoded.role}`);

    // Check if this is a settings-related request (more lenient validation)
    const isSettingsRequest = req.originalUrl.includes('/api/settings');
    
    // Try to get user from cache first
    let user = CacheManager.get(decoded.userId);
    
    if (!user) {
      // Get user from database with optimized query
      if (isSettingsRequest) {
        user = await User.findOne({ 
          userId: decoded.userId,
          isDeleted: false
        }).lean(); // Use lean() for better performance
        
      } else {
        // Standard validation for non-settings requests
        if (req.originalUrl === '/api/register/pay') {
          user = await User.findOne({ 
            userId: decoded.userId,
            isDeleted: false,
            isSuspended: { $ne: true },
            $or: [{ status: 'active' }, { status: 'email_verified_pending_payment' }]
          }).lean();
        } else {
          console.log(`[AuthMiddleware] Looking for user: ${decoded.userId} with conditions:`, {
            userId: decoded.userId,
            status: { $in: ['active', 'pending_email_verification'] },
            isDeleted: false,
            isSuspended: { $ne: true }
          });

          user = await User.findOne({
            userId: decoded.userId,
            status: { $in: ['active', 'pending_email_verification', 'email_verified_pending_payment'] }, // Include all valid statuses
            isDeleted: false,
            isSuspended: { $ne: true }
          }).lean();

          console.log(`[AuthMiddleware] User found:`, user ? 'YES' : 'NO');
        }
      }
      
      if (!user) {
        // Enhanced debugging: Check if user exists with different conditions
        const userExists = await User.findOne({ userId: decoded.userId }).lean();
        if (userExists) {
          console.log(`[AuthMiddleware] User found but doesn't meet auth criteria: ${decoded.userId}`);
          console.log(`[AuthMiddleware] User actual data:`, {
            status: userExists.status,
            verified: userExists.verified,
            isDeleted: userExists.isDeleted,
            isSuspended: userExists.isSuspended,
            role: userExists.role
          });
          console.log(`[AuthMiddleware] Query was looking for:`, {
            status: { $in: ['active', 'pending_email_verification'] },
            isDeleted: false,
            isSuspended: { $ne: true }
          });
          logError(`User found but doesn't meet auth criteria: ${decoded.userId}`, 'AuthMiddleware');
        } else {
          console.log(`[AuthMiddleware] User not found in database: ${decoded.userId}`);
          logError(`User not found in database: ${decoded.userId}`, 'AuthMiddleware');
        }
        return next(new AppError('User not found', 404, 'user_not_found'));
      }
      
      // Cache user for future requests
      CacheManager.set(decoded.userId, user);
    }
    
    // Check if user is verified (skip for settings-related requests and allow pending admins)
    const isPendingAdmin = user.role === 'admin' && user.status === 'pending_email_verification';
    if (!isSettingsRequest && !user.verified && !isPendingAdmin) {
      logWarning(`Unverified user attempted access: ${user.userId}`, 'AuthMiddleware');
      return next(new AppError('Account not verified', 403, 'account_not_verified'));
    }

    // Log when pending admin is accessing the system
    if (isPendingAdmin) {
      console.log(`[AuthMiddleware] Pending admin ${user.userId} accessing ${req.method} ${req.path}`);
    }
    
    // All checks passed - add user to request
    req.user = user;
    
    next();
    
  } catch (error) {
    logError(`Authentication failed: ${error.message}`, 'AuthMiddleware');
    
    if (error.type === 'token_expired') {
      return next(new AppError('Session expired, please login again', 401, 'token_expired'));
    }
    
    if (error.name === 'JsonWebTokenError') {
      return next(new AppError('Invalid authentication token', 401, 'invalid_token'));
    }
    
    return next(new AppError('Authentication failed', 401, 'auth_failed'));
  }
};

/**
 * Authorization middleware for role-based access control
 * @param {string[]} roles - Array of allowed roles
 */
exports.authorize = (roles) => {
  return (req, res, next) => {
    try {
      // Ensure user is authenticated
      if (!req.user) {
        return next(new AppError('Not authenticated', 401, 'not_authenticated'));
      }

      // Ensure roles is always an array
      const rolesArray = Array.isArray(roles) ? roles : [roles];
      
      // Get exact user role from model (preserve case)
      const userRole = req.user.role || '';
      
      // Strict, case-sensitive matcher for canonical roles
      const roleMatches = (allowedRole, userRole) => {
        // Convert both to lowercase for case-insensitive comparison
        return allowedRole.toLowerCase() === userRole.toLowerCase();
      };
      
      // Add debug header in non-production environments
      if (process.env.NODE_ENV !== 'production' && process.env.LOG_LEVEL === '4') {
        res.set('X-Debug-Auth', `role=${userRole};allowed=${rolesArray.join(',')}`);
        console.log(`Authorization check: User role '${userRole}' against allowed roles [${rolesArray.join(', ')}]`);
      }
      
      // Check if user has required role
      console.log(`[Authorize] Checking user ${req.user.userId} with role '${userRole}' against required roles [${rolesArray.join(', ')}]`);

      if (rolesArray.some(role => roleMatches(role, userRole))) {
        // Access granted - proceed
        console.log(`[Authorize] Access granted for ${req.user.userId}`);
        return next();
      } else {
        console.log(`[Authorize] Access denied for ${req.user.userId} (${userRole}) - Required roles: ${rolesArray.join(', ')}`);
        logWarning(`Access denied for ${req.user.userId} (${userRole}) - Required roles: ${rolesArray.join(', ')}`, 'AuthMiddleware');
        return next(new AppError(`Insufficient permissions. Required roles: ${rolesArray.join(', ')}`, 403, 'insufficient_permissions'));
      }
      
    } catch (error) {
      logError(`Error in role authorization: ${error.message}`, 'AuthMiddleware', error);
      return next(new AppError('Authorization failed', 500, 'auth_error'));
    }
  };
};

/**
 * Middleware to check if user is verified
 */
exports.isVerified = (req, res, next) => {
  if (!req.user) {
    return next(new AppError('User not authenticated', 401, 'auth_required'));
  }
  
  if (!req.user.verified) {
    logWarning(`Unverified user attempted access: ${req.user.userId}`, 'AuthMiddleware');
    return next(new AppError('Email verification required', 403, 'verification_required'));
  }
  
  next();
};

/**
 * Middleware to ensure shop access
 * Checks if user belongs to the requested shop
 */
exports.hasShopAccess = (req, res, next) => {
  const shopId = req.params.shopId || req.body.shopId || req.query.shopId;
  
  if (!shopId) {
    return next(new AppError('Shop ID is required', 400, 'missing_shop_id'));
  }
  
  // Super admins can access all shops
  if (req.user.role === 'superAdmin') {
    logAuth(`SuperAdmin accessing shop: ${shopId}`, 'AuthMiddleware');
    return next();
  }
  
  // Check if user belongs to this shop
  if (req.user.shopId !== shopId) {
    logWarning(`User ${req.user.userId} attempted to access unauthorized shop: ${shopId}`, 'AuthMiddleware');
    return next(new AppError('You do not have access to this shop', 403, 'forbidden'));
  }
  
  next();
};

/**
 * Middleware to check module-specific permissions
 * Validates if user has permission for specific module actions based on visibility settings
 * @param {string} module - The module name (e.g., 'customerManagement', 'debtManagement', 'reportManagement')
 * @param {string} action - The action name (e.g., 'create', 'view', 'update', 'delete', 'generate')
 */
exports.checkModulePermission = (module, action) => {
  return (req, res, next) => {
    try {
      // Ensure user is authenticated
      if (!req.user) {
        return next(new AppError('Not authenticated', 401, 'not_authenticated'));
      }

      // SuperAdmin has access to everything
      if (req.user.role === 'superAdmin') {
        return next();
      }
      
      // Admin users have full access to their shop's modules
      if (req.user.role === 'admin') {
        return next();
      }

      // For employees, check specific visibility permissions
      if (req.user.role === 'employee') {
        // Check if user has visibility settings
        if (!req.user.visibility || typeof req.user.visibility !== 'object') {
          logWarning(`Employee ${req.user.userId} has no visibility settings`, 'AuthMiddleware');
          return next(new AppError('Access denied - no permissions configured', 403, 'no_permissions'));
        }

        // Check if the module exists in visibility
        if (!req.user.visibility[module]) {
          logWarning(`Employee ${req.user.userId} attempted to access unauthorized module: ${module}`, 'AuthMiddleware');
          return next(new AppError(`Access denied to ${module}`, 403, 'module_access_denied'));
        }

        // Check if the specific action is permitted
        if (!req.user.visibility[module][action]) {
          logWarning(`Employee ${req.user.userId} attempted unauthorized action: ${module}.${action}`, 'AuthMiddleware');
          return next(new AppError(`Permission denied for ${action} on ${module}`, 403, 'action_not_permitted'));
        }

        // Permission granted
        return next();
      }

      // Invalid role
      logWarning(`User ${req.user.userId} has invalid role: ${req.user.role}`, 'AuthMiddleware');
      return next(new AppError('Invalid user role', 403, 'invalid_role'));

    } catch (error) {
      logError(`Error in module permission check: ${error.message}`, 'AuthMiddleware', error);
      return next(new AppError('Permission check failed', 500, 'permission_check_error'));
    }
  };
};

// Export cache management for external use (e.g., when user data changes)
exports.CacheManager = CacheManager;
