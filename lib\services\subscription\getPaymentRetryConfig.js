import apiBridge from '@/lib/api/bridge';
import { ENDPOINTS } from '@/lib/api/contract';;
import { handleError, processApiResponse } from '../baseService';

/**
 * Get payment retry configuration (SuperAdmin only)
 * @returns {Promise<Object>} Payment retry configuration settings
 */
async function getPaymentRetryConfig() {
  try {
    const response = await apiBridge.get(`${ENDPOINTS.SUBSCRIPTIONS.BASE}/payment-retry/config`);
    
    const result = processApiResponse(response);
    return result.config || result;
  } catch (error) {
    handleError(error, 'SubscriptionService.getPaymentRetryConfig', true);
    throw error;
  }
}

export default getPaymentRetryConfig; 
