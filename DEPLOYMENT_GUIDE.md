# DeynCare Frontend Deployment Guide

This guide will help you deploy the DeynCare frontend application to your VPS using PM2 for process management.

## Prerequisites

- VPS running Ubuntu 20.04+ or similar Linux distribution
- Node.js 18+ installed
- Nginx already configured (as mentioned)
- Git installed
- Non-root user with sudo privileges

## Deployment Steps

### 1. Prepare Your VPS

```bash
# Update system packages
sudo apt update && sudo apt upgrade -y

# Install Node.js 18 (if not already installed)
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# Verify installations
node --version  # Should be v18.x.x
npm --version   # Should be 9.x.x or higher
```

### 2. <PERSON><PERSON> and Setup the Application

```bash
# Navigate to web directory
cd /var/www

# Clone the repository (replace with your actual repo URL)
# If using your own repository:
# sudo git clone https://github.com/yourusername/deyncare-frontend.git
# 
# If deploying from local files, you can copy them instead:
# sudo cp -r /path/to/your/deyncare-frontend .
sudo chown -R $USER:$USER deyncare-frontend
cd deyncare-frontend

# Install dependencies
npm ci --production=false
```

### 3. Configure Environment Variables

```bash
# Copy the environment template
cp env.production.example .env.production

# Edit the environment file with your actual values
nano .env.production
```

**Good news!** The environment file is pre-configured with your URLs:
- `NEXT_PUBLIC_API_URL`: https://deyncare-backend.khanciye.com ✅ 
- All other settings are handled by your backend ✅

**Note**: You can simply copy the example file as-is. The frontend only needs the API URL since authentication, sessions, file uploads, Firebase, etc. are all handled by your backend.

### 4. Build the Application

```bash
# Build the Next.js application
npm run build

# Verify build was successful
ls -la .next/
```

### 5. Install and Configure PM2

```bash
# Install PM2 globally
sudo npm install -g pm2

# Start the application using PM2
pm2 start ecosystem.config.js --env production

# Save PM2 process list
pm2 save

# Setup PM2 to start on boot
sudo pm2 startup systemd -u $USER --hp $HOME
```

### 6. Verify Deployment

```bash
# Check PM2 status
pm2 status

# View application logs
pm2 logs deyncare-frontend

# Check if application is responding
curl http://localhost:3000
```

## Using the Deployment Script

You can use the provided automated deployment script:

```bash
# Make the script executable
chmod +x deploy.sh

# Run the deployment script
./deploy.sh
```

### Deployment Script Options

```bash
# Full deployment
./deploy.sh

# Create backup only
./deploy.sh --backup

# Stop application only
./deploy.sh --stop

# Restart application only
./deploy.sh --restart

# Show help
./deploy.sh --help
```

## PM2 Management Commands

### Basic Commands

```bash
# View all processes
pm2 list

# Stop the application
pm2 stop deyncare-frontend

# Start the application
pm2 start deyncare-frontend

# Restart the application
pm2 restart deyncare-frontend

# Reload the application (zero-downtime)
pm2 reload deyncare-frontend

# Delete the application from PM2
pm2 delete deyncare-frontend
```

### Monitoring and Logs

```bash
# View real-time logs
pm2 logs deyncare-frontend

# View logs with lines limit
pm2 logs deyncare-frontend --lines 100

# Monitor CPU and memory usage
pm2 monit

# Show detailed information about the process
pm2 show deyncare-frontend
```

### Advanced PM2 Features

```bash
# Scale to 4 instances
pm2 scale deyncare-frontend 4

# Reload with zero downtime
pm2 reload deyncare-frontend

# Reset restart count
pm2 reset deyncare-frontend

# Save current process list
pm2 save

# Resurrect saved processes
pm2 resurrect
```

## Nginx Configuration

Since you already have nginx set up, make sure it's configured to proxy requests to your PM2 application. Here's a basic nginx configuration example:

```nginx
server {
    listen 80;
    server_name deyncare.cajiibcreative.com;
    
    # Redirect to HTTPS (since you already have SSL configured)
    return 301 https://$host$request_uri;
}

server {
    listen 443 ssl http2;
    server_name deyncare.cajiibcreative.com;
    
    # Your SSL certificates (already configured)
    # ssl_certificate /path/to/your/certificate.crt;
    # ssl_certificate_key /path/to/your/private.key;

    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        proxy_read_timeout 86400;
    }

    # Static files (optional optimization)
    location /_next/static {
        alias /var/www/deyncare-frontend/.next/static;
        expires 365d;
        access_log off;
    }

    location /public {
        alias /var/www/deyncare-frontend/public;
        expires 365d;
        access_log off;
    }
}
```

## Updates and Maintenance

### Updating the Application

```bash
# Navigate to application directory
cd /var/www/deyncare-frontend

# Create backup
pm2 stop deyncare-frontend
cp -r /var/www/deyncare-frontend /var/backups/deyncare-frontend-$(date +%Y%m%d-%H%M%S)

# Pull latest changes
git pull origin main

# Install any new dependencies
npm ci --production=false

# Rebuild the application
npm run build

# Restart with PM2
pm2 restart deyncare-frontend
```

### Log Rotation

PM2 handles log rotation automatically, but you can configure it:

```bash
# Install PM2 log rotate module
pm2 install pm2-logrotate

# Configure log rotation (optional)
pm2 set pm2-logrotate:max_size 10M
pm2 set pm2-logrotate:retain 30
pm2 set pm2-logrotate:compress true
```

## Troubleshooting

### Application Won't Start

```bash
# Check PM2 logs for errors
pm2 logs deyncare-frontend --lines 50

# Check if port 3000 is available
netstat -tulpn | grep :3000

# Verify environment variables
pm2 show deyncare-frontend
```

### High Memory Usage

```bash
# Monitor memory usage
pm2 monit

# Restart application to clear memory
pm2 restart deyncare-frontend

# Reduce number of instances if needed
pm2 scale deyncare-frontend 1
```

### Application Crashes

```bash
# Check error logs
pm2 logs deyncare-frontend --err

# Increase restart limit
pm2 restart deyncare-frontend

# Check system resources
free -h
df -h
```

## Security Considerations

1. **Environment Variables**: Never commit `.env.production` to version control
2. **File Permissions**: Ensure proper ownership of application files
3. **Firewall**: Configure firewall to only allow necessary ports
4. **Updates**: Keep Node.js, npm, and PM2 updated
5. **Monitoring**: Set up monitoring and alerting for your application

## Performance Optimization

1. **PM2 Instances**: Adjust the number of instances based on your VPS specs
2. **Memory Limit**: Set appropriate memory limits in ecosystem.config.js
3. **Nginx Caching**: Configure nginx to cache static files
4. **Gzip Compression**: Enable gzip compression in nginx
5. **Keep-Alive**: Enable keep-alive connections

## Support

If you encounter issues during deployment, check:
1. PM2 logs: `pm2 logs deyncare-frontend`
2. System logs: `sudo journalctl -u nginx`
3. Node.js version compatibility
4. Environment variable configuration
5. Network connectivity to backend API 