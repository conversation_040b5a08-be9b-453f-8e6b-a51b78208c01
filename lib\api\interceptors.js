/**
 * API interceptors module
 * Handles request/response interceptors, token logic, and error handling
 */
import { getAccessToken, refreshToken, subscribeTokenRefresh, isRefreshing, getCsrfToken } from './token';
import { shouldThrottleRequest, createThrottleError } from './throttle';
import { recordAuthFailure, shouldUseFallbackAuth, getFallbackUserData } from './auth-fallback';

/**
 * Configure request interceptors for an axios instance
 * @param {Object} api - Axios instance
 */
export function setupRequestInterceptors(api) {
  // Main request interceptor - handles throttling and token inclusion
  api.interceptors.request.use(
    (config) => {
      // Handle request throttling
      // Make sure config.url exists before calling shouldThrottleRequest
      const url = config.url || '';
      const result = shouldThrottleRequest(config.method, url);
      const shouldThrottle = result.shouldThrottle;
      
      if (shouldThrottle) {
        // Return a promise that will be rejected with a custom error
        return Promise.reject(createThrottleError(config));
      }
      
      // Get token from localStorage in client-side
      if (typeof window !== 'undefined') {
        const token = getAccessToken();
        if (token) {
          // Make sure Authorization header is properly set
          config.headers['Authorization'] = `Bearer ${token}`;
          
          // Only log that token exists, not the actual token value
          console.log(`[API] Setting Authorization header with token (exists)`);
        } else {
          console.log('[API] No access token found in localStorage');
        }
        
        // Special handling for FormData - don't override Content-Type
        if (config.data instanceof FormData) {
          // Remove Content-Type header for FormData uploads to let browser set multipart boundary
          delete config.headers['Content-Type'];
          console.log('[API] FormData detected in interceptor - removing Content-Type header');
        }
        
        // Add CSRF token for non-GET requests
        const method = config.method?.toUpperCase() || '';
        if (!['GET', 'HEAD', 'OPTIONS'].includes(method)) {
          // Get CSRF token - first try cookie, then localstorage
          const csrfToken = getCsrfToken();
          if (csrfToken) {
            config.headers['X-CSRF-Token'] = csrfToken;
            console.log('[API] Adding CSRF token to request');
          } else {
            console.log('[API] No CSRF token found for non-GET request');
          }
        }
      }
      
      // Log the request being made (without exposing full URLs that might contain sensitive data)
      const safeUrl = config.url?.includes('auth') ? '(auth endpoint)' : config.url;
      // Making API request
      return config;
    },
    (error) => {
      // If this was a throttled request, handle it gracefully
      if (error.throttled) {
        console.log('[API] Request was throttled');
        return Promise.reject(error);
      }
      return Promise.reject(error);
    }
  );
}

/**
 * Configure response interceptors for an axios instance
 * @param {Object} api - Axios instance
 */
export function setupResponseInterceptors(api) {
  api.interceptors.response.use(
    (response) => {
      // Log successful responses for debugging without exposing sensitive endpoints
      const safeUrl = response.config.url?.includes('auth') ? '(auth endpoint)' : response.config.url;
      // API request successful
      return response;
    },
    async (error) => {
      // Check for JSON parse errors (happens when server returns HTML instead of JSON)
      if (error.message && (error.message.includes('Unexpected token') || 
          error.message.includes('JSON parse') || 
          error.message.includes('<!DOCTYPE'))) {
        console.error('[API] JSON parse error: Server returned HTML instead of JSON', error);
        
        // Create a more user-friendly error
        return Promise.reject({
          response: {
            status: error.response?.status || 500,
            data: { message: 'The server returned an invalid response format' }
          },
          message: 'Invalid server response format',
          originalError: error
        });
      }
      
      // Handle throttled requests
      if (error.throttled) {
        console.log('[API] Request was throttled to prevent excessive API calls');
        return Promise.reject({
          response: {
            status: 429,
            data: { message: 'Too many requests' }
          },
          message: 'Request throttled to prevent excessive API calls'
        });
      }
      
      const originalRequest = error.config;
      if (!originalRequest) {
        console.error('[API] Error with no config:', error);
        return Promise.reject(error);
      }
      
      // Log detailed error information without exposing full URLs
      const safeUrl = originalRequest?.url?.includes('auth') ? '(auth endpoint)' : originalRequest?.url;
      console.log(`[API] Error ${error.response?.status} from ${safeUrl}:`, 
        error.response?.data?.message || error.message);
      
      // Skip retries for certain request types or endpoints
      const skipRetryEndpoints = ['/api/auth/logout', '/api/auth/refresh-token'];
      const shouldSkipRetry = skipRetryEndpoints.some(endpoint => originalRequest.url.includes(endpoint));
      
      // If error is 401 Unauthorized and not already retrying and not a skip-retry endpoint
      if (error.response?.status === 401 && !originalRequest._retry && !shouldSkipRetry) {
        originalRequest._retry = true;
        console.log('[API] Attempting token refresh due to 401 error');
        
        try {
          // Use our token refresh function
          const newToken = await refreshToken();
          
          if (newToken) {
            // Retry with new token
            console.log('[API] Retrying original request with new token');
            originalRequest.headers['Authorization'] = `Bearer ${newToken}`;
            return api(originalRequest);
          } else {
            // If token refresh failed, redirect to login but do it in a non-blocking way
            console.log('[API] Token refresh failed, redirecting to login');
            if (typeof window !== 'undefined') {
              // Clear tokens
              localStorage.removeItem('accessToken');
              localStorage.removeItem('refreshToken');
              
              // Use setTimeout to make the redirect non-blocking
              setTimeout(() => {
                if (window.routerNavigate) {
                  window.routerNavigate('/login');
                } else {
                  // Fallback if routerNavigate is not available
                  window.location.href = '/login';
                }
              }, 100);
            }
            return Promise.reject(error);
          }
        } catch (refreshError) {
          console.error('[API] Error during token refresh:', refreshError);
          
          // Special handling for 500 errors during refresh
          if (refreshError.response?.status === 500) {
            console.warn('[API] Server error during token refresh, attempting to continue with current request');
            
            // If the server has an issue with token refresh, try to continue with the existing token
            // This is a fallback mechanism to prevent a complete breakdown if token refresh endpoint has issues
            const existingToken = localStorage.getItem('accessToken');
            if (existingToken && !originalRequest.headers['X-Retry-With-Existing-Token']) {
              console.log('[API] Retrying original request with existing token as fallback');
              originalRequest.headers['Authorization'] = `Bearer ${existingToken}`;
              originalRequest.headers['X-Retry-With-Existing-Token'] = 'true';
              return api(originalRequest);
            }
          }
          
          return Promise.reject(error);
        }
      }
      
      // Add subscription to token refresh for queued requests
      if (error.response?.status === 401 && isRefreshing) {
        console.log('[API] Request failed with 401, queuing for token refresh');
        return new Promise((resolve, reject) => {
          subscribeTokenRefresh(newToken => {
            originalRequest.headers['Authorization'] = `Bearer ${newToken}`;
            resolve(api(originalRequest));
          });
        });
      }
      
      // Handle rate limiting (429 errors) more gracefully
      if (error.response?.status === 429) {
        console.log('[API] Rate limited. Consider implementing retry with backoff.');
      }
      
      // Handle specific error types
      if (error.response?.status === 403) {
        // Forbidden - user doesn't have permission
        console.error('[API] Permission denied:', error.response?.data?.message);
        // Do not log authorization headers as they contain tokens
        console.error('[API] Authorization header present:', !!originalRequest?.headers?.Authorization);
        
        // Stricter check for genuine inactive account
        const isAuthEndpoint = originalRequest?.url?.includes('/api/auth') || originalRequest?.url?.includes('/api/user') || originalRequest?.url?.includes('/api/profile');
        const isInactiveAccount =
          error.response?.data?.type === 'inactive_account' ||
          error.response?.data?.code === 'account_inactive' ||
          (
            error.response?.data?.message &&
            error.response?.data?.message.toLowerCase().includes('account is inactive')
          );
        
        if (isAuthEndpoint && isInactiveAccount) {
          console.warn('[API] Request failed due to genuinely inactive account');
          
          if (typeof window !== 'undefined') {
            // Show specific inactive account message using toast
            if (window.toast) {
              window.toast.error('Your account is inactive. Please contact an administrator.');
            } else if (window.showToast) {
              window.showToast({
                type: 'error',
                message: 'Your account is inactive. Please contact an administrator.',
                duration: 5000
              });
            }
            
            // Set the account status
            localStorage.setItem('accountStatus', 'inactive');
            
            // Clear tokens
            localStorage.removeItem('accessToken');
            localStorage.removeItem('refreshToken');
            
            // Dispatch a specific event for inactive accounts with context
            window.dispatchEvent(new CustomEvent('auth:account:inactive', {
              detail: { 
                message: error.response?.data?.message || 'Your account is inactive',
                url: originalRequest?.url || '',
                isSettingsError: originalRequest?.url?.includes('/api/settings'),
                errorCode: error.response?.data?.code || ''
              }
            }));
            
            // Redirect to login with inactive parameter
            setTimeout(() => {
              if (window.routerNavigate) {
                window.routerNavigate('/login?status=inactive');
              } else {
                window.location.href = '/login?status=inactive';
              }
            }, 1500);
            
            // Return a specific error
            return Promise.reject({
              ...error,
              inactiveAccount: true,
              message: 'Your account is inactive. Please contact an administrator.'
            });
          }
        } else if (error.response?.status === 403 && error.config?.url?.includes('/api/settings')) {
          // Special handling for settings-related permission errors
          console.warn('[API] Settings permission error - not treating as inactive account');
          return Promise.reject({
            ...error,
            isSettingsError: true,
            message: error.response?.data?.message || 'You do not have permission to change these settings.'
          });
        }
      } else if (error.response?.status === 404) {
        // Not found
        console.error('[API] Resource not found:', error.response?.data?.message);
      } else if (error.response?.status >= 500) {
        // Server error
        console.error('[API] Server error:', error.response?.data?.message);
        
        // Record server error for auth fallback mechanism
        if (originalRequest?.url?.includes('/api/auth/') || 
            originalRequest?.url?.includes('/api/users/profile')) {
          const thresholdReached = recordAuthFailure(error.response.status);
          console.warn(`[API] Auth-related server error, fallback threshold reached: ${thresholdReached}`);
        }
      }
      
      // For other errors, just reject
      return Promise.reject(error);
    }
  );
}

/**
 * Setup all interceptors for an axios instance
 * @param {Object} api - Axios instance
 */
export function setupInterceptors(api) {
  setupRequestInterceptors(api);
  setupResponseInterceptors(api);
}
