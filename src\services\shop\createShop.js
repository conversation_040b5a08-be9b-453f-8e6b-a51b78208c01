const { Shop, Plan } = require('../../models');

// Import utility modules
const { 
  // Core utilities
  AppError,
  
  // Generator utilities
  idGenerator,
  
  // Helper utilities
  ShopHelper,
  UserHelper,
  SubscriptionHelper,
  LogHelper,
  SettingsHelper,
  
  // Logger utilities
  logSuccess,
  logError,
  logWarning,
  logInfo
} = require('../../utils');

// Import subscription service
const SubscriptionService = require('../SubscriptionService');
const createSubscription = require('../Subscription/createSubscription');

/**
 * Create a new shop
 * @param {Object} shopData - Shop data to create
 * @param {Object} options - Additional options
 * @returns {Promise<Object>} Created shop
 */
const createShop = async (shopData, options = {}) => {
  try {
    const { 
      shopName, 
      ownerName,
      email,
      phone,
      address: shopAddress,
      businessDetails = {},
      logoUrl = '',
      status = 'pending',
      verified = false,
      subscription = {},
      registeredBy = 'self',
      session = null
    } = shopData;

    // Normalize email to lowercase
    const normalizedEmail = email ? email.toLowerCase().trim() : '';
    
    // Process business details with validation
    const validBusinessCategories = [
      'general_store',
      'grocery',
      'electronics',
      'clothing',
      'restaurant',
      'pharmacy',
      'hardware',
      'automotive',
      'beauty_salon',
      'others'
    ];
    
    const processedBusinessDetails = {
      type: businessDetails.type || 'retail',
      category: validBusinessCategories.includes(businessDetails.category) 
        ? businessDetails.category 
        : 'general_store',
      foundedDate: businessDetails.foundedDate || null,
      registrationNumber: businessDetails.registrationNumber || '',
      taxId: businessDetails.taxId || '',
      employeeCount: businessDetails.employeeCount || 1
    };
    
    // Validate shop data
    const validation = ShopHelper.validateShopData({
      ...shopData,
      email: normalizedEmail,
      address: shopAddress
    });
    
    if (!validation.isValid) {
      throw new AppError(
        `Invalid shop data: ${validation.errors.join(', ')}`,
        400,
        'invalid_shop_data'
      );
    }
    
    // Generate shop ID
    const shopId = await idGenerator.generateShopId(Shop);
    
    // Handle logo - only accept file upload objects
    let finalLogoUrl = '';
    if (logoUrl && typeof logoUrl === 'object' && logoUrl.fileId) {
      // This is a file upload result object, extract the URL
      finalLogoUrl = logoUrl.url || '';
    }
    
    // Validate subscription data - ensure we have a valid object to work with
    const subscriptionData = subscription || {};
    const incomingPlanIdentifier = subscriptionData.planId || subscriptionData.planType || 'trial';
    let planType; // This will be the actual type: 'trial', 'monthly', or 'yearly'

    // Check if the identifier is a planId (e.g., 'PLAN2755') or a type string
    if (incomingPlanIdentifier && incomingPlanIdentifier.toUpperCase().startsWith('PLAN')) {
      const plan = await Plan.findOne({ planId: incomingPlanIdentifier });
      if (plan) {
        planType = plan.type; // Use the type from the database, e.g., 'monthly'
      } else {
        logWarning(`Plan with ID '${incomingPlanIdentifier}' not found. Defaulting to trial.`, 'ShopService');
        planType = 'trial'; // Fallback if planId is invalid
      }
    } else {
      // It's a type string like 'trial', 'monthly', etc.
      planType = incomingPlanIdentifier;
    }

    let paymentMethod = subscriptionData.paymentMethod || 'offline';
    const initialPaid = subscriptionData.initialPaid || false;
    const paymentDetails = subscriptionData.paymentDetails || null;
    const discountDetails = subscriptionData.discountDetails || null;
    
    // Map user-friendly payment methods to internal system payment methods BEFORE validation
    const paymentMethodMapping = {
      'Cash': 'offline',
      'EVC Plus': 'evc',
      'Bank Transfer': 'bank',
      'Mobile Money': 'online',
      'Check': 'offline',
      'Card': 'online',
      'Other': 'offline',
      'offline': 'offline'
    };
    
    // Convert payment method to internal format before validation
    const internalPaymentMethod = paymentMethodMapping[paymentMethod] || 'offline';
    if (internalPaymentMethod !== paymentMethod) {
      logInfo(`Mapped payment method '${paymentMethod}' to internal method '${internalPaymentMethod}'`, 'ShopService');
      paymentMethod = internalPaymentMethod;
    }
    
    const subscriptionValidation = SubscriptionHelper.validateSubscription({
      planType,
      paymentMethod, // Now using the mapped internal payment method
      initialPaid,
      paymentDetails
    });
    
    if (!subscriptionValidation.isValid) {
      throw new AppError(
        `Invalid subscription data: ${subscriptionValidation.errors.join(', ')}`,
        400,
        'invalid_subscription_data'
      );
    }
    
    // Ensure we use the properly validated payment method
    // Note: We've already mapped and validated the payment method above
    const validPaymentMethods = ['Cash', 'EVC Plus', 'Bank Transfer', 'Mobile Money', 'Check', 'Card', 'Other', 'offline'];
    
    // For display purposes, keep the original user-friendly payment method name
    let displayPaymentMethod = subscriptionData.paymentMethod || 'offline';
    if (!validPaymentMethods.includes(displayPaymentMethod)) {
      displayPaymentMethod = 'offline';
      logWarning(`Invalid payment method display name, defaulting to 'offline'`, 'ShopService');
    }
    
    // Get default subscription with validated data
    const finalSubscription = SubscriptionHelper.getDefaultSubscription({
      planType: planType || 'trial',
      paymentMethod: internalPaymentMethod, // Use the mapped internal payment method
      initialPaid: initialPaid || false,
      paymentDetails: paymentDetails || null
    });

    // If subscription data contains pricing information (e.g., from SuperAdmin), use it instead of default
    if (subscriptionData.pricing && subscriptionData.pricing.amount !== undefined) {
      finalSubscription.pricing = {
        price: subscriptionData.pricing.amount,
        currency: subscriptionData.pricing.currency || 'USD',
        label: subscriptionData.planName || finalSubscription.pricing.label,
        billingCycle: subscriptionData.pricing.billingCycle || 'monthly'
      };
      logInfo(`Using provided plan pricing: $${subscriptionData.pricing.amount} ${subscriptionData.pricing.currency}`, 'ShopService');
    }

    // Add discount information if provided
    if (discountDetails && initialPaid) {
      finalSubscription.pricing.discount = {
        active: true,
        code: discountDetails.code,
        discountId: discountDetails.discountId,
        amount: discountDetails.discountAmount,
        originalAmount: discountDetails.originalAmount || finalSubscription.pricing.price,
        type: discountDetails.type,
        value: discountDetails.value,
        percentage: discountDetails.type === 'percentage',
        appliedAt: new Date()
      };
      
      logInfo(`Applied discount code ${discountDetails.code} to shop subscription: ${discountDetails.discountAmount}`, 'ShopService');
    }

    // Set shop status based on plan type - trial plans should be immediately active
    // For trial plans, payment method is stored for future use but doesn't affect activation
    const shopPlanType = planType || 'trial';
    let shopStatus = status;
    
    if (shopPlanType === 'trial') {
      // Business rule: Trial plans are ALWAYS active regardless of payment method
      shopStatus = 'active';
      // For trials, mark payment as not required initially
      finalSubscription.initialPaid = true; // Consider trial as already "paid" to avoid payment requirements
    }

    // Create shop with sanitized data
    const shop = new Shop({
      shopId,
      shopName: validation.sanitizedData.shopName,
      ownerName,
      email: normalizedEmail,
      phone: validation.sanitizedData.phone,
      address: shopAddress,
      businessDetails: processedBusinessDetails,
      logoUrl: finalLogoUrl,
      status: shopStatus, // Use the determined status based on plan type
      verified,
      subscription: finalSubscription,
      registeredBy
    });

    // Save shop (with session if provided)
    if (session) {
      await shop.save({ session });
    } else {
      await shop.save();
    }

    // Create corresponding subscription record
    try {
      const subscriptionData = {
        shopId,
        planType: shopPlanType,
        planId: incomingPlanIdentifier && incomingPlanIdentifier.toUpperCase().startsWith('PLAN') ? incomingPlanIdentifier : null,
        pricing: {
          basePrice: finalSubscription.pricing.price,
          currency: finalSubscription.pricing.currency
        },
        paymentMethod: internalPaymentMethod,
        paymentDetails: paymentDetails,
        discountDetails: finalSubscription.pricing.discount,
        session
      };

      await createSubscription(subscriptionData, options);
      logSuccess(`Subscription record created for shop: ${shopId}`, 'ShopService');
    } catch (subscriptionError) {
      // Log error but don't fail shop creation - subscription can be created later
      logError(`Failed to create subscription record for shop ${shopId}: ${subscriptionError.message}`, 'ShopService', subscriptionError);
      logWarning(`Shop ${shopId} created without subscription record - can be added later`, 'ShopService');
      
      // Set a flag to indicate subscription creation failed but continue with shop creation
      shop.subscriptionCreationFailed = true; // Fixed: was ' ohh!', now 'shop'
      // Note: We're not throwing here, so shop creation can still succeed
    }

    // Initialize shop-specific settings
    try {
      // If we're in a transaction, skip this for now as it would require joining the session
      if (!session) {
        await SettingsHelper.createShopSettings(shopId, options.actorId || 'system');
        logSuccess(`Initialized settings for new shop: ${shopId}`, 'ShopService');
      } else {
        // Log that we'll need to create settings after transaction completes
        logInfo(`Shop settings initialization deferred until after transaction for shop: ${shopId}`, 'ShopService');
      }
    } catch (settingsError) {
      // Don't fail shop creation if settings initialization fails
      logError(`Failed to initialize shop settings: ${settingsError.message}`, 'ShopService', settingsError);
    }

    // Log shop creation using LogHelper (defensive)
    try {
      await LogHelper.createShopLog(
        'shop_created', 
        shopId, 
        {
          actorId: options.actorId || 'system',
          actorRole: options.actorRole || 'system'
        }, 
        {
          registeredBy,
          planType: shopPlanType
        }
      );
    } catch (logError) {
      // Don't fail shop creation if logging fails
      logWarning(`Failed to log shop creation for ${shopId}: ${logError.message}`, 'ShopService');
    }

    logSuccess(`Shop created: ${shopId} - ${shopName}`, 'ShopService');
    
    return shop;
  } catch (error) {
    if (error instanceof AppError) {
      throw error;
    }
    
    logError(`Error creating shop: ${error.message}`, 'ShopService', error);
    throw new AppError('Failed to create shop', 500, 'shop_creation_error');
  }
};

module.exports = createShop;

