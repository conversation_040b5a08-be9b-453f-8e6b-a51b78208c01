const Joi = require('joi');
const patterns = require('../validationPatterns');

/**
 * Simplified SuperAdmin CRUD Validation Schemas
 * Clean, minimal payloads without complex verification/payment logic
 */
const superAdminSchemas = {
  /**
   * SIMPLIFIED Schema for SuperAdmin creating shop
   * POST /api/admin/shops
   * NO verification, NO payment complexity - just basic shop info
   * Password is auto-generated by the backend
   */
  createShop: Joi.object({
    // User data (SAME field names as public registration)
    fullName: patterns.string.fullName.required(),
    email: patterns.string.email.required(),
    phone: patterns.string.phone.required(),
    // NO PASSWORD - auto-generated by backend for SuperAdmin registrations

    // Shop data (SAME field names as public registration)
    shopName: patterns.string.shopName.required(),
    shopAddress: patterns.string.shopAddress.required(),

    // Business details (OPTIONAL) - can be object or JSON string
    businessDetails: Joi.alternatives().try(
      // Accept as object (from frontend)
      Joi.object({
        type: Joi.string().valid('retail', 'wholesale', 'service', 'manufacturing').default('retail'),
        category: Joi.string().valid(
          'general_store',
          'grocery',
          'electronics',
          'clothing',
          'restaurant',
          'pharmacy',
          'hardware',
          'automotive',
          'beauty_salon',
          'others'
        ).default('general_store'),
        customCategory: Joi.string().allow('').when('category', {
          is: 'others',
          then: Joi.string().min(2).max(50).required().messages({
            'any.required': 'Custom category is required when "Others" is selected',
            'string.min': 'Custom category must be at least 2 characters',
            'string.max': 'Custom category cannot exceed 50 characters'
          }),
          otherwise: Joi.string().allow('').optional()
        })
      }),
      // Accept as JSON string (from multipart form data)
      Joi.string().custom((value, helpers) => {
        try {
          return JSON.parse(value);
        } catch (error) {
          return helpers.error('any.invalid');
        }
      })
    ).optional().default({
      type: 'retail',
      category: 'general_store'
    }),

    // Plan type (REQUIRED for SuperAdmin registrations) - accepts any plan identifier
    planType: Joi.string().min(1).required().messages({
      'string.empty': 'Plan type is required',
      'any.required': 'Plan type is required'
    })
  }),

  /**
   * Schema for updating shop information
   * PUT /api/admin/shops/:shopId
   */
  updateShop: Joi.object({
    // User data (SAME field names as public registration)
    fullName: patterns.string.fullName.optional(),
    email: patterns.string.email.optional(),
    phone: patterns.string.phone.optional(),
    
    // Shop data (SAME field names as public registration)
    shopName: patterns.string.shopName.optional(),
    shopAddress: patterns.string.shopAddress.optional(),
    
    // Allow updating any shop field without validation complexity
    status: Joi.string().valid('active', 'pending', 'suspended').optional(),
    verified: Joi.boolean().optional()
  }),

  /**
   * Schema for changing shop status
   * PUT /api/admin/shops/:shopId/status
   */
  changeShopStatus: Joi.object({
    status: Joi.string().valid('active', 'suspended', 'pending', 'inactive').required().messages({
      'any.only': 'Status must be active, suspended, pending, or inactive',
      'any.required': 'Status is required'
    }),
    reason: Joi.string().allow('').max(200).optional().messages({
      'string.max': 'Reason cannot exceed 200 characters'
    })
  }),

  /**
   * Schema for deleting shop
   * DELETE /api/admin/shops/:shopId
   */
  deleteShop: Joi.object({
    reason: Joi.alternatives().try(
      Joi.string().allow(''),
      Joi.string().min(3).max(200)
    ).optional().messages({
      'string.min': 'Reason must be at least 3 characters when provided',
      'string.max': 'Reason cannot exceed 200 characters'
    })
  }),

  /**
   * Schema for shop ID parameter validation
   */
  shopIdParam: Joi.object({
    shopId: Joi.string().required().messages({
      'any.required': 'Shop ID is required'
    })
  })
};

module.exports = superAdminSchemas; 