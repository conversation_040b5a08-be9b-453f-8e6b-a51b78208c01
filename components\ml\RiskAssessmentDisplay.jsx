import React from 'react';
import { <PERSON>, TrendingUp, <PERSON><PERSON><PERSON>riangle, CheckCircle } from 'lucide-react';
import { Badge } from '../ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { getRiskLevelColor } from '../../lib/services/ml/riskAssessment';

export default function RiskAssessmentDisplay({ assessment, isLoading, className = '' }) {
  if (isLoading) {
    return (
      <Card className={`${className} animate-pulse`}>
        <CardContent className="flex items-center justify-center p-6">
          <div className="flex items-center gap-3">
            <div className="w-5 h-5 bg-blue-200 rounded animate-spin"></div>
            <span className="text-blue-600">Assessing risk...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!assessment) {
    return (
      <Card className={`${className} border-gray-200`}>
        <CardContent className="flex items-center justify-center p-6 text-gray-500">
          <div className="text-center">
            <Shield className="w-8 h-8 mx-auto mb-2 text-gray-400" />
            <p>No risk assessment available</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  const getRiskIcon = (riskLevel) => {
    switch (riskLevel) {
      case 'Low Risk':
        return <CheckCircle className="w-5 h-5" />;
      case 'Medium Risk':
        return <TrendingUp className="w-5 h-5" />;
      case 'High Risk':
        return <AlertTriangle className="w-5 h-5" />;
      default:
        return <Shield className="w-5 h-5" />;
    }
  };

  const riskColorClass = getRiskLevelColor(assessment.PredictedRiskLevel);

  return (
    <Card className={`${className} shadow-lg`}>
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center gap-2 text-lg">
          <Shield className="w-5 h-5 text-blue-600" />
          Risk Assessment
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {/* Risk Level Badge */}
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium text-gray-600">Risk Level:</span>
            <Badge className={`${riskColorClass} flex items-center gap-1 px-3 py-1`}>
              {getRiskIcon(assessment.PredictedRiskLevel)}
              {assessment.PredictedRiskLevel}
            </Badge>
          </div>

          {/* Risk Score */}
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium text-gray-600">Risk Score:</span>
            <span className="text-lg font-bold text-gray-900">
              {(assessment.RiskScore * 100).toFixed(1)}%
            </span>
          </div>

          {/* Risk Score Bar */}
          <div className="w-full bg-gray-200 rounded-full h-3">
            <div
              className={`h-3 rounded-full transition-all duration-300 ${
                assessment.RiskScore <= 0.3 
                  ? 'bg-green-500' 
                  : assessment.RiskScore <= 0.6 
                  ? 'bg-yellow-500' 
                  : 'bg-red-500'
              }`}
              style={{ width: `${Math.min(assessment.RiskScore * 100, 100)}%` }}
            ></div>
          </div>

          {/* Risk Description */}
          <div className="text-xs text-gray-500 bg-gray-50 p-3 rounded-lg">
            <strong>FairRisk Algorithm:</strong> This score is calculated using debt payment ratio, 
            payment delays, and outstanding amounts to provide transparent risk assessment.
          </div>

          {/* Fallback indicator */}
          {assessment.source === 'fallback' && (
            <div className="text-xs text-orange-600 bg-orange-50 p-2 rounded border border-orange-200">
              ⚠️ Calculated using fallback algorithm (ML API unavailable)
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
} 