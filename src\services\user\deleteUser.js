const mongoose = require('mongoose');
const { App<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, logSuccess, logError } = require('../../utils');
const User = require('../../models/user.model');

/**
 * Lazy load models to prevent circular dependencies
 * @param {string} modelName - Name of the model to load
 * @returns {Object} Model instance
 */
const getModel = (modelName) => {
  try {
    return require(`../../models/${modelName}.model`);
  } catch (error) {
    logError(`Failed to load model ${modelName}: ${error.message}`, 'DeleteUserService');
    throw error;
  }
};

/**
 * Hard delete user with cascading shop impact
 * @param {string} userId - ID of user to delete
 * @param {Object} options - Additional options
 * @param {string} [options.actorId='system'] - ID of actor deleting the user
 * @param {string} [options.actorRole='system'] - Role of actor deleting the user
 * @param {string} [options.reason] - Reason for deletion
 * @param {boolean} [options.anonymize=false] - Whether to anonymize user data (not applicable for hard delete)
 * @returns {Promise<Object>} Success result
 * @throws {AppError} If deletion fails
 */
const deleteUser = async (userId, options = {}) => {
  try {
    // Find active user using UserHelper
    const user = await UserHelper.findActiveUser(userId);

    // Check if user can be deleted
    if (user.role === 'superAdmin' && options.actorRole !== 'superAdmin') {
      throw new AppError('Only superAdmins can delete superAdmin users', 403, 'forbidden_operation');
    }

    // Store user data for logging before deletion
    const userDataForLog = {
      userId: user.userId,
      fullName: user.fullName,
      email: user.email,
      role: user.role,
      shopId: user.shopId
    };

    // Start transaction for cascading operations
    const session = await mongoose.startSession();

    try {
      await session.withTransaction(async () => {
        // 1. HARD DELETE: Remove user from database completely
        await User.findOneAndDelete({ userId: userId }, { session });

        // 2. CASCADE: Handle shop impact if user is shop owner/admin
        if (userDataForLog.shopId && (userDataForLog.role === 'admin' || userDataForLog.role === 'employee')) {
          // Lazy load models to prevent circular dependencies
          const Shop = getModel('shop');

          // Check if this is the shop owner (admin role)
          if (userDataForLog.role === 'admin') {
            // Option A: Suspend the shop when owner is deleted
            await Shop.findOneAndUpdate(
              { shopId: userDataForLog.shopId },
              {
                $set: {
                  status: 'suspended',
                  statusReason: `Shop owner deleted: ${options?.reason || 'No reason provided'}`,
                  statusChangedAt: new Date(),
                  statusChangedBy: options?.actorId || 'system'
                }
              },
              { session }
            );

            console.log(`🔄 REVERSE CASCADE: User ${userId} (shop owner) deleted → Shop ${userDataForLog.shopId} suspended`);
          } else {
            // For employees, just log the impact
            console.log(`🔄 REVERSE CASCADE: User ${userId} (employee) deleted from shop ${userDataForLog.shopId}`);
          }
        }
      });

      await session.endSession();

      // Log the deletion using LogHelper
      await LogHelper.createUserLog(
        'user_deleted',
        userId,
        {
          actorId: options?.actorId || 'system',
          actorRole: options?.actorRole || 'system',
          shopId: userDataForLog.shopId || null
        },
        {
          reason: options?.reason || 'not_specified',
          deletionType: 'hard_delete',
          cascadeAction: userDataForLog.role === 'admin' ? 'shop_suspended' : 'none'
        }
      );

      logSuccess(`User hard deleted with cascading impact: ${userDataForLog.userId}`, 'UserService');
      return {
        success: true,
        message: userDataForLog.role === 'admin'
          ? 'User permanently deleted and associated shop suspended'
          : 'User permanently deleted'
      };
    } catch (error) {
      await session.endSession();
      throw error;
    }
  } catch (error) {
    if (error instanceof AppError) {
      throw error;
    }
    
    logError(`Error deleting user ${userId}: ${error.message}`, 'UserService', error);
    throw new AppError('Failed to delete user', 500, 'user_deletion_error');
  }
};

module.exports = deleteUser;
