"use client"

import * as React from 'react'
import { toast } from 'sonner'
import { useUsers } from '@/contexts/users-context'
import { But<PERSON> } from '@/components/ui/button'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select'
import { Checkbox } from '@/components/ui/checkbox'
import { zodResolver } from '@hookform/resolvers/zod'
import { useForm } from 'react-hook-form'
import * as z from 'zod'
import { Loader2 } from 'lucide-react'

// Form validation schema
const userFormSchema = z.object({
  firstName: z.string().min(2, 'First name must be at least 2 characters'),
  lastName: z.string().min(2, 'Last name must be at least 2 characters'),
  email: z.string()
    .min(1, 'Email address is required')
    .email('Please enter a valid email address'),
  role: z.enum(['employee', 'admin', 'superAdmin'], {
    required_error: 'Please select a role',
  }),
  phone: z.string().optional(),
  shopId: z.string(),
  password: z.string().min(8, 'Password must be at least 8 characters').optional(),
  confirmPassword: z.string().optional(),
  sendWelcomeEmail: z.boolean().default(true),
}).refine((data) => {
  // When creating a new user (password is required)
  if (!data.password && !data.userId) {
    return false;
  }
  return true;
}, {
  message: 'Password is required for new users',
  path: ['password'],
}).refine((data) => {
  // Only validate confirmPassword if password is provided
  if (data.password && data.password !== data.confirmPassword) {
    return false;
  }
  return true;
}, {
  message: 'Passwords do not match',
  path: ['confirmPassword'],
}).refine((data) => {
  // Validate shop selection based on role
  // Only admin and employee roles require a shop association
  if (['admin', 'employee'].includes(data.role)) {
    if (data.shopId === 'none' || !data.shopId || data.shopId === '') {
      return false;
    }
  }
  // SuperAdmin can operate without a shop
  return true;
}, {
  message: 'Admin and employee users must be associated with a shop',
  path: ['shopId'],
});

// Use React.memo to prevent unnecessary re-renders
const UserFormDialog = React.memo(function UserFormDialog({ isOpen, onClose, user = null, shops = [] }) {
  // Use refs for stable references
  const dialogRef = React.useRef(null);
  const initialRender = React.useRef(true);
  
  // State management
  const [submitting, setSubmitting] = React.useState(false);
  const [noShopsWarning, setNoShopsWarning] = React.useState(shops.length === 0);
  
  // Get functions from context
  const { createUser, updateUser, refreshUserList } = useUsers();
  
  // Determine if we're editing or creating
  const isEditMode = React.useMemo(() => !!user, [user]);

  // Get the first available shop for default selection
  // Using a fallback value to ensure we never have an empty string
  const defaultShopId = shops.length > 0 ? shops[0].shopId : 'default';
  
  // Show warning if no shops are available
  React.useEffect(() => {
    setNoShopsWarning(shops.length === 0);
  }, [shops.length]);
  
  // Initialize the form with react-hook-form
  const form = useForm({
    resolver: zodResolver(userFormSchema),
    defaultValues: {
      firstName: user?.firstName || '',
      lastName: user?.lastName || '',
      email: user?.email || '',
      phone: user?.phone || '',
      role: user?.role || 'employee',
      // Always use a valid shop ID instead of 'none'
      shopId: user?.shopId || defaultShopId,
      password: '',
      confirmPassword: '',
      sendWelcomeEmail: true,
    }
  });

  // Extract first and last name from fullName
  const extractNames = (fullName) => {
    if (!fullName) return { firstName: '', lastName: '' };
    
    const nameParts = fullName.split(' ');
    if (nameParts.length === 1) {
      return { firstName: nameParts[0], lastName: '' };
    }
    
    const firstName = nameParts[0];
    const lastName = nameParts.slice(1).join(' ');
    return { firstName, lastName };
  };
  
  // Update form values when editing an existing user
  React.useEffect(() => {
    if (isEditMode && user) {
      // Extract first and last name from fullName for the form
      const { firstName, lastName } = extractNames(user.fullName);
      
      console.log('Populating form with user data:', {
        fullName: user.fullName,
        extractedFirstName: firstName,
        extractedLastName: lastName,
        email: user.email,
        role: user.role
      });
      
      // Reset form with user data
      form.reset({
        firstName,
        lastName,
        email: user.email || '',
        phone: user.phone || '',
        role: user.role || 'employee',
        shopId: user.shopId || defaultShopId,
        password: '',
        confirmPassword: '',
        sendWelcomeEmail: false, // Not needed for edits
      });
    }
  }, [isEditMode, user, form, defaultShopId]);

  // Handle form submission
  const onSubmit = async (data) => {
    try {
      setSubmitting(true);
      
      // Combine form data into format expected by API
      const userData = {
        fullName: `${data.firstName} ${data.lastName}`.trim(),
        email: data.email,
        phone: data.phone || '',
        password: data.password, 
        role: data.role,
        shopId: ['admin', 'employee'].includes(data.role) 
          ? (data.shopId || (shops.length > 0 ? shops[0].shopId : null))
          : null,
        sendWelcomeEmail: data.sendWelcomeEmail
      };
      
      // If this is a superAdmin creating an employee, ensure shopId is set
      if (data.role === 'employee' && userData.shopId === null) {
        toast.error('A shop must be selected for employee users');
        return;
      }
      
      // Log validation details
      console.log('Submitting user data to backend:', userData);

      if (isEditMode) {
        // Update existing user
        const updatedUser = await updateUser(user.userId, userData);
        console.log('User updated successfully:', updatedUser);
        
        // After successful update, explicitly refresh the user list
        // The context might already do this, but this ensures UI consistency
        await refreshUserList();
        
        toast.success('User updated successfully');
        onClose(true);
      } else {
        // Create new user
        const newUser = await createUser(userData);
        console.log('User created successfully:', newUser);
        
        // After successful creation, explicitly refresh the user list
        // The context might already do this, but this ensures UI consistency
        await refreshUserList();
        
        toast.success('User created successfully');
        onClose(true);
      }
    } catch (error) {
      console.error('Error submitting user form:', error);
      
      // Handle specific error codes with user-friendly messages
      if (error.response) {
        const { status, data } = error.response;
        
        switch (status) {
          case 409: // Conflict - Email already exists
            toast.error("This email address is already registered. Please use a different email.");
            form.setError('email', { 
              type: 'manual', 
              message: 'Email already in use' 
            });
            break;
          case 422: // Validation error
            toast.error("Please check your input and try again.");
            break;
          case 404: // Not Found - Shop not found
            toast.error("The selected shop was not found. Please choose a different shop.");
            break;
          default:
            toast.error(data?.message || error.message || 'Failed to save user');
        }
      } else {
        toast.error(error.message || 'Failed to save user');
      }
    } finally {
      setSubmitting(false);
    }
  };

  // Create a safe version of onClose that won't cause React errors
  const handleDialogChange = React.useCallback((open) => {
    if (!submitting) {
      // Only call onClose if it's a function
      if (typeof onClose === 'function') {
        onClose(open ? true : false);
      }
    }
  }, [submitting, onClose]);
  
  // Safe close handler for the cancel button
  const handleCancel = React.useCallback(() => {
    if (!submitting && typeof onClose === 'function') {
      onClose(false);
    }
  }, [submitting, onClose]);

  // Prevent initial render issues
  React.useEffect(() => {
    initialRender.current = false;
  }, []);

  // Only render the dialog if we're open to prevent unnecessary renders
  if (!isOpen && !initialRender.current) {
    return null;
  }

  return (
    <Dialog 
      open={isOpen} 
      onOpenChange={handleDialogChange}>
      <DialogContent className="sm:max-w-[525px]">
        <DialogHeader>
          <DialogTitle>{isEditMode ? 'Edit User' : 'Create New User'}</DialogTitle>
          <DialogDescription>
            {isEditMode 
              ? 'Update user information. Leave password blank to keep it unchanged.'
              : 'Fill in the details to create a new user.'}
          </DialogDescription>
        </DialogHeader>
        
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            {shops.length === 0 && (
              <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-4">
                <div className="flex">
                  <div className="flex-shrink-0">
                    <svg className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M8.485 2.495c.673-1.167 2.357-1.167 3.03 0l6.28 10.875c.673 1.167-.17 2.625-1.516 2.625H3.72c-1.347 0-2.189-1.458-1.515-2.625L8.485 2.495zM10 5a.75.75 0 01.75.75v4.5a.75.75 0 01-1.5 0v-4.5A.75.75 0 0110 5zm0 10a1 1 0 100-2 1 1 0 000 2z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div className="ml-3">
                    <p className="text-sm text-yellow-700">
                      <strong>Warning:</strong> No shops are available. Please create a shop first before creating users.
                    </p>
                  </div>
                </div>
              </div>
            )}
            <div className="grid grid-cols-2 gap-4">
              {/* First Name */}
              <FormField
                control={form.control}
                name="firstName"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>First Name</FormLabel>
                    <FormControl>
                      <Input placeholder="John" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              {/* Last Name */}
              <FormField
                control={form.control}
                name="lastName"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Last Name</FormLabel>
                    <FormControl>
                      <Input placeholder="Doe" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            
            {/* Email */}
            <FormField
              control={form.control}
              name="email"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Email</FormLabel>
                  <FormControl>
                    <Input type="email" placeholder="<EMAIL>" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            
            {/* Phone */}
            <FormField
              control={form.control}
              name="phone"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Phone (Optional)</FormLabel>
                  <FormControl>
                    <Input placeholder="+1234567890" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            
            <div className="grid grid-cols-2 gap-4">
              {/* Role */}
              <FormField
                control={form.control}
                name="role"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Role</FormLabel>
                    <Select 
                      onValueChange={field.onChange} 
                      defaultValue={field.value}
                      value={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select role" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="employee">Employee</SelectItem>
                        <SelectItem value="admin">Admin</SelectItem>
                        <SelectItem value="superAdmin">Super Admin</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              {/* Shop */}
              <FormField
                control={form.control}
                name="shopId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Shop</FormLabel>
                    <Select 
                      onValueChange={field.onChange} 
                      defaultValue={field.value}
                      value={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select shop" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {/* Only show valid shops, not the 'none' option */}
                        {shops.length > 0 ? (
                          shops.map(shop => (
                            <SelectItem key={shop.shopId} value={shop.shopId}>
                              {shop.shopName || shop.name}
                            </SelectItem>
                          ))
                        ) : (
                          <div className="px-2 py-4 text-sm text-muted-foreground">
                            No shops available
                          </div>
                        )}
                      </SelectContent>
                    </Select>
                    <FormDescription>
                      User will be associated with this shop. All users require a shop association.
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            
            {/* Password fields */}
            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="password"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{isEditMode ? 'New Password (Optional)' : 'Password'}</FormLabel>
                    <FormControl>
                      <Input 
                        type="password" 
                        placeholder={isEditMode ? "Leave blank to keep current" : "Enter password"} 
                        {...field} 
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <FormField
                control={form.control}
                name="confirmPassword"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Confirm Password</FormLabel>
                    <FormControl>
                      <Input 
                        type="password" 
                        placeholder="Confirm password" 
                        {...field} 
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            
            {/* Send Welcome Email checkbox - only for new users */}
            {!isEditMode && (
              <FormField
                control={form.control}
                name="sendWelcomeEmail"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                    <FormControl>
                      <Checkbox 
                        checked={field.value} 
                        onCheckedChange={field.onChange} 
                      />
                    </FormControl>
                    <div className="space-y-1 leading-none">
                      <FormLabel>Send welcome email</FormLabel>
                      <FormDescription>
                        User will receive an email with login instructions
                      </FormDescription>
                    </div>
                  </FormItem>
                )}
              />
            )}
            
            {/* Dialog Actions */}
            <DialogFooter>
              <Button 
                type="button" 
                variant="outline" 
                onClick={handleCancel}
                disabled={submitting}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={submitting}>
                {submitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                {isEditMode ? 'Update User' : 'Create User'}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
});

export default UserFormDialog;
