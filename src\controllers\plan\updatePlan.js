/**
 * Update an existing pricing plan
 * Allows SuperAdmin to dynamically control features
 * PUT /api/plans/:planId
 */
const PlanService = require("../../services/PlanService");
const { logInfo, logSuccess, logError } = require("../../utils");

const updatePlan = async (req, res, next) => {
  try {
    const { planId } = req.params;
    const updateData = req.body;
    
    logInfo(`Updating pricing plan: ${planId}`, "PlanController");
    
    // Allow SuperAdmin to control features dynamically
    // No more forcing all features to true
    if (updateData.features) {
      logInfo(`Updating features for plan ${planId}: ${Object.keys(updateData.features).filter(key => updateData.features[key]).join(", ")}`, "PlanController");
    }
    
    const plan = await PlanService.updatePlan(planId, updateData, {
      actorId: req.user.userId,
      actorRole: req.user.role
    });
    
    logSuccess(`Updated pricing plan: ${planId}`, "PlanController");
    
    return res.status(200).json({
      success: true,
      message: "Plan updated successfully",
      data: plan
    });
  } catch (error) {
    logError(`Failed to update pricing plan: ${req.params.planId}`, "PlanController", error);
    return next(error);
  }
};

module.exports = updatePlan;
