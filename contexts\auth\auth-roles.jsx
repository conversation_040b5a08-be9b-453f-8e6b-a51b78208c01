/**
 * Role management utilities for the authentication system
 */

/**
 * Check if a user has a specific role (case-insensitive)
 * @param {Object} user - The user object
 * @param {string} role - The role to check for
 * @returns {boolean} - Whether the user has the specified role
 */
export const hasRole = (user, role) => {
  if (!user || !user.role) return false;
  return user.role.toLowerCase() === role.toLowerCase();
};

/**
 * Check if a user is an admin
 * @param {Object} user - The user object
 * @returns {boolean} - Whether the user is an admin
 */
export const isAdmin = (user) => hasRole(user, 'admin');

/**
 * Check if a user is a super admin
 * @param {Object} user - The user object
 * @returns {boolean} - Whether the user is a super admin
 */
export const isSuperAdmin = (user) => hasRole(user, 'superAdmin');

/**
 * Check if a user is staff
 * @param {Object} user - The user object
 * @returns {boolean} - Whether the user is staff
 */
export const isStaff = (user) => hasRole(user, 'staff');
