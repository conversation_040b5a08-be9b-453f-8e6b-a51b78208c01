import apiBridge from '@/lib/api/bridge';
import BaseService from '../baseService';

const retryPaymentNow = async (subscriptionId, retryData) => {
  try {
    const response = await apiBridge.post(
      ENDPOINTS.subscription.retryPaymentNow.replace(':subscriptionId', subscriptionId),
      retryData
    );
    
    return BaseService.processApiResponse(response);
  } catch (error) {
    throw BaseService.handleApiError(error);
  }
};

export default retryPaymentNow; 
