import apiBridge from '@/lib/api/bridge';
import { ENDPOINTS } from '@/lib/api/contract';
import { handleError, processApiResponse } from '../baseService';

/**
 * Get push notification statistics (SuperAdmin only)
 * @param {Object} options - Query options
 * @param {string} [options.shopId] - Filter by specific shop
 * @param {number} [options.days=30] - Number of days to include (1-365)
 * @returns {Promise<Object>} Notification statistics
 */
async function getNotificationStats(options = {}) {
  try {
    const { shopId, days = 30 } = options;

    // Validate days parameter
    if (days && (typeof days !== 'number' || days < 1 || days > 365)) {
      handleError({ message: 'Days must be between 1 and 365' }, 'NotificationService.getNotificationStats', true);
      throw new Error('Days must be between 1 and 365');
    }

    // Build query parameters
    const params = {};
    if (shopId) params.shopId = shopId;
    if (days !== 30) params.days = days;

    // Make API request using the bridge with caching
    const response = await apiBridge.get(ENDPOINTS.NOTIFICATIONS.PUSH.STATS, {
      params,
      cacheKey: `notification-stats-${shopId || 'all'}-${days}`,
      cacheTTL: 300000 // 5 minutes cache
    });

    // Process response using utility
    const result = processApiResponse(response, null); // No toast for stats retrieval
    return result;
  } catch (error) {
    // Return default empty stats to prevent UI breaking (no toast for stats)
    console.warn('Failed to load notification stats:', error.message);
    return {
      stats: {
        totalSent: 0,
        totalDelivered: 0,
        totalFailed: 0,
        deliveryRate: 0
      },
      details: []
    };
  }
}

export default getNotificationStats; 