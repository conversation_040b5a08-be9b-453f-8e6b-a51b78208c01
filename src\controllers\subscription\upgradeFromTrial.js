/**
 * Upgrade From Trial Controller
 * Handles upgrading a trial subscription to a paid plan
 */
const { SubscriptionService } = require('../../services');
const { successResponse, AppError } = require('../../utils');

/**
 * Upgrade subscription from trial to paid plan
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
const upgradeFromTrial = async (req, res, next) => {
  try {
    const { planId, planType, paymentMethod } = req.body;
    const { shopId } = req.user;
    
    if (!shopId) {
      return res.status(400).json({
        success: false,
        message: 'No shop associated with this user',
        statusCode: 400,
        type: 'shop_not_found'
      });
    }
    
    if (!planId && !planType) {
      throw new AppError('Either Plan ID or Plan Type is required', 400, 'validation_error');
    }
    
    // Get current subscription for the shop
    const currentSubscription = await SubscriptionService.getCurrentSubscriptionByShop(shopId);
    
    if (!currentSubscription) {
      return res.status(404).json({
        success: false,
        message: 'Subscription not found',
        statusCode: 404,
        type: 'subscription_not_found'
      });
    }
    
    // For security tracking, we pass actor information to service
    const options = {
      actorId: req.user?.userId || 'system',
      actorRole: req.user?.role || 'system'
    };
    
    // Upgrade subscription through service
    const updatedSubscription = await SubscriptionService.upgradeFromTrial(
      currentSubscription.subscriptionId, 
      { planId, planType, paymentMethod }, 
      options
    );
    
    // Return successful response
    return successResponse(res, {
      message: 'Subscription upgraded from trial successfully',
      data: updatedSubscription,
    });
  } catch (error) {
    next(error);
  }
};

module.exports = upgradeFromTrial;
