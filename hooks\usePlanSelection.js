"use client";

import { useState, useCallback, useEffect, useMemo, useRef } from 'react';
import { toast } from 'sonner';
import PlanService from '@/lib/services/plan';

/**
 * Plan Selection Hook
 * 
 * Specialized hook for plan selection in forms, dropdowns, and UI components
 * Provides formatted options, filtering, and selection management
 */
export function usePlanSelection(options = {}) {
  const {
    autoFetch = true,
    includeInactive = false, // Only active plans for selection by default
    showToastMessages = false, // Silent by default for selection hooks
    fetchDelay = 300,
    filterTypes = [], // Filter by specific plan types: ['trial', 'monthly', 'yearly']
    excludeTypes = [], // Exclude specific plan types
    sortBy = 'displayOrder', // Sort by: displayOrder, name, price, type
    sortOrder = 'asc', // asc or desc
    includeRecommended = true,
    onSelectionChange,
    onOptionsChange
  } = options;

  // Selection state
  const [plans, setPlans] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [selectedPlan, setSelectedPlan] = useState(null);
  const [lastUpdated, setLastUpdated] = useState(null);

  // Use refs to prevent duplicate requests
  const isLoadingRef = useRef(false);
  const isMountedRef = useRef(true);

  /**
   * Fetch plans for selection
   */
  const fetchPlansForSelection = useCallback(async (showLoadingToast = false) => {
    // Prevent duplicate requests
    if (isLoadingRef.current) {
      console.log('[usePlanSelection] Request already in progress, skipping...');
      return;
    }

    try {
      isLoadingRef.current = true;
      setLoading(true);
      setError(null);

      if (showLoadingToast && showToastMessages) {
        toast.info('Loading plan options...');
      }

      console.log('[usePlanSelection] Fetching plans for selection');

      const response = await PlanService.getPlansForSelection();

      if (!isMountedRef.current) return;

      if (response.success) {
        let planOptions = response.data || [];

        // Apply filtering by plan types
        if (filterTypes.length > 0) {
          planOptions = planOptions.filter(plan => filterTypes.includes(plan.type));
        }

        // Exclude specific types
        if (excludeTypes.length > 0) {
          planOptions = planOptions.filter(plan => !excludeTypes.includes(plan.type));
        }

        // Apply sorting
        planOptions.sort((a, b) => {
          let aValue, bValue;

          switch (sortBy) {
            case 'name':
              aValue = a.label?.toLowerCase() || '';
              bValue = b.label?.toLowerCase() || '';
              break;
            case 'price':
              aValue = a.price || 0;
              bValue = b.price || 0;
              break;
            case 'type':
              aValue = a.type || '';
              bValue = b.type || '';
              break;
            case 'displayOrder':
            default:
              aValue = a.displayOrder || 1;
              bValue = b.displayOrder || 1;
              break;
          }

          if (typeof aValue === 'string') {
            return sortOrder === 'desc' 
              ? bValue.localeCompare(aValue)
              : aValue.localeCompare(bValue);
          } else {
            return sortOrder === 'desc' 
              ? bValue - aValue
              : aValue - bValue;
          }
        });

        setPlans(planOptions);
        setLastUpdated(new Date());
        
        if (showLoadingToast && showToastMessages) {
          toast.success(`Loaded ${planOptions.length} plan options`);
        }

        // Trigger callback if provided
        if (onOptionsChange) {
          onOptionsChange(planOptions);
        }

        console.log('[usePlanSelection] Plan options loaded:', planOptions.length);
      } else {
        throw new Error(response.message || 'Failed to load plan options');
      }
    } catch (err) {
      console.error('[usePlanSelection] Error fetching plan options:', err);
      
      if (!isMountedRef.current) return;

      const errorMessage = err.message || 'Failed to load plan options';
      setError(errorMessage);

      if (showToastMessages && !err.message?.includes('throttled')) {
        toast.error(errorMessage);
      }
    } finally {
      if (isMountedRef.current) {
        setLoading(false);
        isLoadingRef.current = false;
      }
    }
  }, [showToastMessages, filterTypes, excludeTypes, sortBy, sortOrder, onOptionsChange]);

  /**
   * Select a plan by value (planId)
   */
  const selectPlan = useCallback((planValue) => {
    const plan = plans.find(p => p.value === planValue);
    setSelectedPlan(plan || null);
    
    // Trigger callback if provided
    if (onSelectionChange) {
      onSelectionChange(plan || null, planValue);
    }
    
    console.log('[usePlanSelection] Plan selected:', plan);
  }, [plans, onSelectionChange]);

  /**
   * Select plan by type
   */
  const selectPlanByType = useCallback((planType) => {
    const plan = plans.find(p => p.type === planType);
    setSelectedPlan(plan || null);
    
    // Trigger callback if provided
    if (onSelectionChange) {
      onSelectionChange(plan || null, plan?.value);
    }
    
    console.log('[usePlanSelection] Plan selected by type:', plan);
  }, [plans, onSelectionChange]);

  /**
   * Clear selection
   */
  const clearSelection = useCallback(() => {
    setSelectedPlan(null);
    
    // Trigger callback if provided
    if (onSelectionChange) {
      onSelectionChange(null, null);
    }
    
    console.log('[usePlanSelection] Selection cleared');
  }, [onSelectionChange]);

  /**
   * Get plan by value
   */
  const getPlanByValue = useCallback((planValue) => {
    return plans.find(p => p.value === planValue) || null;
  }, [plans]);

  /**
   * Get plan by type
   */
  const getPlanByType = useCallback((planType) => {
    return plans.find(p => p.type === planType) || null;
  }, [plans]);

  /**
   * Refresh plan options
   */
  const refreshOptions = useCallback((showToast = false) => {
    return fetchPlansForSelection(showToast);
  }, [fetchPlansForSelection]);

  // Computed values using useMemo for performance
  const computedValues = useMemo(() => {
    // Group plans by type
    const plansByType = plans.reduce((acc, plan) => {
      if (!acc[plan.type]) {
        acc[plan.type] = [];
      }
      acc[plan.type].push(plan);
      return acc;
    }, {});

    // Get recommended plan
    const recommendedPlan = plans.find(plan => plan.isRecommended) || null;

    // Get cheapest plan
    const cheapestPlan = plans.length > 0
      ? plans.reduce((min, plan) => (plan.price < min.price ? plan : min))
      : null;

    // Get most expensive plan
    const mostExpensivePlan = plans.length > 0
      ? plans.reduce((max, plan) => (plan.price > max.price ? plan : max))
      : null;

    // Format for different UI components
    const selectOptions = plans.map(plan => ({
      value: plan.value,
      label: plan.label,
      disabled: false
    }));

    const radioOptions = plans.map(plan => ({
      value: plan.value,
      label: plan.label,
      description: plan.description,
      price: plan.price,
      currency: plan.currency,
      billingCycle: plan.billingCycle,
      isRecommended: plan.isRecommended
    }));

    const cardOptions = plans.map(plan => ({
      ...plan,
      formatted: {
        price: `${plan.currency} ${plan.price}`,
        billing: plan.billingCycle,
        features: Object.keys(plan.features || {}).filter(key => plan.features[key])
      }
    }));

    return {
      plansByType,
      recommendedPlan,
      cheapestPlan,
      mostExpensivePlan,
      selectOptions,
      radioOptions,
      cardOptions,
      totalOptions: plans.length,
      hasRecommended: !!recommendedPlan,
      availableTypes: Object.keys(plansByType),
      priceRange: plans.length > 0 ? {
        min: Math.min(...plans.map(p => p.price)),
        max: Math.max(...plans.map(p => p.price))
      } : null
    };
  }, [plans]);

  // Auto-fetch on mount with delay to prevent React Strict Mode double calls
  useEffect(() => {
    if (!autoFetch) return;

    const timer = setTimeout(() => {
      fetchPlansForSelection();
    }, fetchDelay);

    return () => clearTimeout(timer);
  }, [autoFetch, fetchDelay, fetchPlansForSelection]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      isMountedRef.current = false;
    };
  }, []);

  return {
    // Basic data
    plans,
    loading,
    error,
    lastUpdated,
    selectedPlan,

    // Computed values
    ...computedValues,

    // Selection management
    selectPlan,
    selectPlanByType,
    clearSelection,
    getPlanByValue,
    getPlanByType,

    // Data management
    refreshOptions,

    // Aliases for compatibility
    refetch: refreshOptions,
    refresh: refreshOptions,
    options: plans,
    selection: selectedPlan,
    setSelection: selectPlan
  };
} 