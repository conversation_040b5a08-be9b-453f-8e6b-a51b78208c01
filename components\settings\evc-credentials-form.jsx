/**
 * EVC Credentials Form Component
 * Matches backend validation schema: settingsSchemas.evcCredentialsSchema
 * Uses exact backend field names and validation rules
 */

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import { Alert, AlertDescription } from '../ui/alert';
import { CheckCircle, XCircle, Loader2, TestTube, Eye, EyeOff } from 'lucide-react';
import { usePaymentSettingsQuery } from '../../hooks/use-payment-settings-query';
import { getErrorMessage } from '../../lib/utils/errorHandler';

/**
 * EVC Credentials Form Component
 * @param {Object} props - Component props
 * @param {string|null} [props.shopId] - Shop ID for shop-specific credentials, null for global
 * @param {Function} [props.onSave] - Callback when credentials are saved successfully
 * @param {Function} [props.onTest] - Callback when credentials are tested successfully
 */
export default function EVCCredentialsForm({ shopId = null, onSave, onTest }) {
  const {
    evcCredentials,
    hasEvcCredentials,
    isLoadingEvcCredentials,
    isSavingEvcCredentials,
    isTestingEvcCredentials,
    saveEvcCredentials,
    testEvcCredentials,
    evcCredentialsError
  } = usePaymentSettingsQuery({ shopId });

  // Form state matching exact backend validation schema
  const [formData, setFormData] = useState({
    merchantUId: '',
    apiUserId: '',
    apiKey: '',
    merchantNo: '',
    url: 'https://api.waafipay.net/asm' // Backend default
  });

  const [showPasswords, setShowPasswords] = useState({
    apiKey: false
  });

  const [errors, setErrors] = useState({});
  const [saveStatus, setSaveStatus] = useState(null);
  const [testStatus, setTestStatus] = useState(null);

  // Load existing credentials into form
  useEffect(() => {
    console.log('[EVCCredentialsForm] Effect triggered:', {
      isLoadingEvcCredentials,
      hasEvcCredentials,
      evcCredentials,
      evcCredentialsKeys: evcCredentials ? Object.keys(evcCredentials) : 'none'
    });
    
    if (!isLoadingEvcCredentials && evcCredentials) {
      // Check if we have any credential data
      const hasAnyCredentialData = evcCredentials.merchantUId || 
                                  evcCredentials.apiUserId || 
                                  evcCredentials.apiKey || 
                                  evcCredentials.merchantNo;
      
      if (hasAnyCredentialData) {
        console.log('[EVCCredentialsForm] Loading existing credentials:', evcCredentials);
        setFormData(prev => ({
          ...prev,
          merchantUId: evcCredentials.merchantUId || '',
          apiUserId: evcCredentials.apiUserId || '',
          apiKey: evcCredentials.apiKey || '',
          merchantNo: evcCredentials.merchantNo || '',
          url: evcCredentials.url || 'https://api.waafipay.net/asm'
        }));
      } else {
        console.log('[EVCCredentialsForm] No existing credentials found, using defaults');
        setFormData(prev => ({
          ...prev,
          url: 'https://api.waafipay.net/asm'
        }));
      }
    }
  }, [isLoadingEvcCredentials, evcCredentials]);

  // Clear status messages after delay
  useEffect(() => {
    if (saveStatus) {
      const timer = setTimeout(() => setSaveStatus(null), 5000);
      return () => clearTimeout(timer);
    }
  }, [saveStatus]);

  useEffect(() => {
    if (testStatus) {
      const timer = setTimeout(() => setTestStatus(null), 5000);
      return () => clearTimeout(timer);
    }
  }, [testStatus]);

  // Validation based on exact backend schema requirements
  const validateField = (name, value) => {
    const rules = {
      merchantUId: { required: true, minLength: 5, maxLength: 100 },
      apiUserId: { required: true, minLength: 5, maxLength: 100 },
      apiKey: { required: true, minLength: 8, maxLength: 500 },
      merchantNo: { required: true, minLength: 4, maxLength: 30 },
      url: { required: false, pattern: /^https?:\/\/.+/ }
    };

    const rule = rules[name];
    if (!rule) return null;

    if (rule.required && (!value || value.trim() === '')) {
      return `${name.replace(/([A-Z])/g, ' $1').trim()} is required`;
    }

    if (value) {
      if (rule.minLength && value.length < rule.minLength) {
        return `Must be at least ${rule.minLength} characters`;
      }
      if (rule.maxLength && value.length > rule.maxLength) {
        return `Cannot exceed ${rule.maxLength} characters`;
      }
      if (rule.pattern && !rule.pattern.test(value)) {
        return 'Invalid format';
      }
    }

    return null;
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
    
    // Clear field error on change
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: null }));
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    // Validate all fields
    const newErrors = {};
    Object.keys(formData).forEach(key => {
      const error = validateField(key, formData[key]);
      if (error) newErrors[key] = error;
    });

    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors);
      return;
    }

    try {
      // Include shopId in credentials payload
      const credentials = {
        ...formData,
        shopId
      };

      const result = await saveEvcCredentials(credentials);
      console.log('[EVCCredentialsForm] Save result:', result);
      
      if (result && result.success) {
        const successMessage = typeof result.message === 'string' 
          ? result.message 
          : 'EVC credentials saved successfully';
        setSaveStatus({ type: 'success', message: successMessage });
        onSave?.(result);
      } else {
        const errorMessage = getErrorMessage(result?.message, 'Failed to save EVC credentials');
        setSaveStatus({ type: 'error', message: errorMessage });
      }
    } catch (error) {
      console.error('[EVCCredentialsForm] Save error:', error);
      setSaveStatus({ type: 'error', message: getErrorMessage(error, 'Failed to save credentials') });
    }
  };

  const handleTest = async () => {
    try {
      const result = await testEvcCredentials({ shopId });
      console.log('[EVCCredentialsForm] Test result:', result);
      
      if (result && result.success) {
        const successMessage = typeof result.message === 'string' 
          ? result.message 
          : 'EVC credentials test successful';
        setTestStatus({ 
          type: 'success', 
          message: successMessage,
          transactionId: result.transactionId
        });
        onTest?.(result);
      } else {
        const errorMessage = getErrorMessage(result?.message, 'Failed to test EVC credentials');
        setTestStatus({ type: 'error', message: errorMessage });
      }
    } catch (error) {
      console.error('[EVCCredentialsForm] Test error:', error);
      setTestStatus({ type: 'error', message: getErrorMessage(error, 'Failed to test credentials') });
    }
  };

  const togglePasswordVisibility = (field) => {
    setShowPasswords(prev => ({ ...prev, [field]: !prev[field] }));
  };

  if (isLoadingEvcCredentials) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center p-6">
          <Loader2 className="w-6 h-6 animate-spin mr-2" />
          Loading EVC credentials...
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          EVC Payment Credentials
          {shopId ? (
            <span className="text-sm font-normal text-gray-500">
              (Shop: {shopId})
            </span>
          ) : (
            <span className="text-sm font-normal text-gray-500">
              (Global)
            </span>
          )}
        </CardTitle>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Error State */}
        {evcCredentialsError && (
          <Alert variant="destructive">
            <XCircle className="h-4 w-4" />
            <AlertDescription>
              Failed to load EVC credentials: {getErrorMessage(evcCredentialsError)}
            </AlertDescription>
          </Alert>
        )}

        {/* Save Status */}
        {saveStatus && (
          <Alert variant={saveStatus.type === 'success' ? 'default' : 'destructive'}>
            {saveStatus.type === 'success' ? (
              <CheckCircle className="h-4 w-4" />
            ) : (
              <XCircle className="h-4 w-4" />
            )}
            <AlertDescription>{saveStatus.message}</AlertDescription>
          </Alert>
        )}

        {/* Test Status */}
        {testStatus && (
          <Alert variant={testStatus.type === 'success' ? 'default' : 'destructive'}>
            {testStatus.type === 'success' ? (
              <CheckCircle className="h-4 w-4" />
            ) : (
              <XCircle className="h-4 w-4" />
            )}
            <AlertDescription>
              {testStatus.message}
              {testStatus.transactionId && (
                <div className="mt-1 text-xs">
                  Transaction ID: {testStatus.transactionId}
                </div>
              )}
            </AlertDescription>
          </Alert>
        )}

        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Merchant UID */}
          <div>
            <Label htmlFor="merchantUId">Merchant UID *</Label>
            <Input
              id="merchantUId"
              name="merchantUId"
              type="text"
              value={formData.merchantUId}
              onChange={handleInputChange}
              placeholder="Enter merchant UID"
              className={errors.merchantUId ? 'border-red-500' : ''}
            />
            {errors.merchantUId && (
              <p className="text-sm text-red-500 mt-1">{errors.merchantUId}</p>
            )}
          </div>

          {/* API User ID */}
          <div>
            <Label htmlFor="apiUserId">API User ID *</Label>
            <Input
              id="apiUserId"
              name="apiUserId"
              type="text"
              value={formData.apiUserId}
              onChange={handleInputChange}
              placeholder="Enter API user ID"
              className={errors.apiUserId ? 'border-red-500' : ''}
            />
            {errors.apiUserId && (
              <p className="text-sm text-red-500 mt-1">{errors.apiUserId}</p>
            )}
          </div>

          {/* API Key */}
          <div>
            <Label htmlFor="apiKey">API Key *</Label>
            <div className="relative">
              <Input
                id="apiKey"
                name="apiKey"
                type={showPasswords.apiKey ? 'text' : 'password'}
                value={formData.apiKey}
                onChange={handleInputChange}
                placeholder="Enter API key"
                className={`pr-10 ${errors.apiKey ? 'border-red-500' : ''}`}
              />
              <Button
                type="button"
                variant="ghost"
                size="sm"
                className="absolute right-0 top-0 h-full px-3"
                onClick={() => togglePasswordVisibility('apiKey')}
              >
                {showPasswords.apiKey ? (
                  <EyeOff className="h-4 w-4" />
                ) : (
                  <Eye className="h-4 w-4" />
                )}
              </Button>
            </div>
            {errors.apiKey && (
              <p className="text-sm text-red-500 mt-1">{errors.apiKey}</p>
            )}
          </div>

          {/* Merchant Number */}
          <div>
            <Label htmlFor="merchantNo">Merchant Number *</Label>
            <Input
              id="merchantNo"
              name="merchantNo"
              type="text"
              value={formData.merchantNo}
              onChange={handleInputChange}
              placeholder="Enter merchant number"
              className={errors.merchantNo ? 'border-red-500' : ''}
            />
            {errors.merchantNo && (
              <p className="text-sm text-red-500 mt-1">{errors.merchantNo}</p>
            )}
          </div>

          {/* API URL */}
          <div>
            <Label htmlFor="url">API URL</Label>
            <Input
              id="url"
              name="url"
              type="url"
              value={formData.url}
              onChange={handleInputChange}
              placeholder="https://api.waafipay.net/asm"
              className={errors.url ? 'border-red-500' : ''}
            />
            {errors.url && (
              <p className="text-sm text-red-500 mt-1">{errors.url}</p>
            )}
          </div>

          {/* Action Buttons */}
          <div className="flex gap-2 pt-4">
            <Button
              type="submit"
              disabled={isSavingEvcCredentials}
              className="flex-1"
            >
              {isSavingEvcCredentials ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  Saving...
                </>
              ) : (
                'Save Credentials'
              )}
            </Button>

            <Button
              type="button"
              variant="outline"
              onClick={handleTest}
              disabled={isTestingEvcCredentials || !hasEvcCredentials}
            >
              {isTestingEvcCredentials ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  Testing...
                </>
              ) : (
                <>
                  <TestTube className="w-4 h-4 mr-2" />
                  Test Connection
                </>
              )}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
} 