/**
 * Utility for generating verification codes
 */

/**
 * Generate a random numeric code
 * @param {number} length - Length of the code (default: 6)
 * @returns {string} - Generated code
 */
const generateVerificationCode = (length = 6) => {
  const min = Math.pow(10, length - 1);
  const max = Math.pow(10, length) - 1;
  const code = Math.floor(min + Math.random() * (max - min + 1));
  return code.toString();
};

/**
 * Generate a random string token
 * @param {number} length - Length of the token (default: 64)
 * @returns {string} - Generated token
 */
const generateToken = (length = 64) => {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let token = '';
  for (let i = 0; i < length; i++) {
    token += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return token;
};

/**
 * Calculate token expiry date
 * @param {number} hours - Hours from now (default: 24)
 * @returns {Date} - Expiry date
 */
const calculateExpiry = (hours = 24) => {
  const expiry = new Date();
  expiry.setHours(expiry.getHours() + hours);
  return expiry;
};

/**
 * Generate a simple ID with prefix and random number
 * Used for controllers that need synchronous ID generation
 * @param {string} prefix - The prefix for the ID (e.g., 'CUST', 'DEBT', 'PAY')
 * @param {number} length - Length of the random number part (default: 4)
 * @returns {string} - Generated ID (e.g., 'CUST-A1B2')
 */
const generateCode = (prefix, length = 4) => {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
  let randomPart = '';
  for (let i = 0; i < length; i++) {
    randomPart += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return `${prefix}-${randomPart}`;
};

module.exports = {
  generateVerificationCode,
  generateToken,
  calculateExpiry,
  generateCode
};
