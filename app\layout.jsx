import { Inter } from "next/font/google";
import "./globals.css";
import { ClientProviders } from "@/components/providers/client-providers";

const inter = Inter({ subsets: ["latin"] });

export const metadata = {
  title: "DeynCare - Business Management Platform",
  description: "DeynCare - Complete debt management, shop management, and customer tracking platform for businesses",
  keywords: "debt management, shop management, customer tracking, business analytics, payment processing, POS system",
  authors: [{ name: "DeynCare Team" }],
  icons: {
    icon: [
      { url: "/images/deyncare_icon.png", sizes: "32x32", type: "image/png" },
      { url: "/images/deyncare_icon.png", sizes: "16x16", type: "image/png" },
    ],
    apple: [
      { url: "/images/deyncare_icon.png", sizes: "180x180", type: "image/png" },
    ],
    shortcut: "/images/deyncare_icon.png",
  },
  manifest: "/manifest.json",
  openGraph: {
    title: "DeynCare - Business Management Platform",
    description: "Complete debt management, shop management, and customer tracking platform for businesses",
    type: "website",
    images: [
      {
        url: "/images/deyncare_logo_black.png",
        width: 1200,
        height: 630,
        alt: "DeynCare Logo",
      },
    ],
  },
  twitter: {
    card: "summary_large_image",
    title: "DeynCare - Business Management Platform",
    description: "Complete debt management, shop management, and customer tracking platform for businesses",
    images: ["/images/deyncare_logo_black.png"],
  },
  themeColor: [
    { media: "(prefers-color-scheme: light)", color: "#ffffff" },
    { media: "(prefers-color-scheme: dark)", color: "#0a0a0a" },
  ],
};

export default function RootLayout({ children }) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={inter.className} suppressHydrationWarning>
        <ClientProviders>
          {children}
        </ClientProviders>
      </body>
    </html>
  );
}
