import apiBridge from '@/lib/api/bridge';
import { ENDPOINTS } from '@/lib/api/contract';;
import { handleError, processApiResponse } from '../baseService';

/**
 * Get all subscriptions with optional filters
 * @param {Object} params - Query parameters (status, planType, shopId, page, limit, sortBy, sortOrder)
 * @returns {Promise<Object>} Subscriptions data with pagination info
 */
async function getAllSubscriptions(params = {}) {
  try {
    const response = await apiBridge.get(ENDPOINTS.SUBSCRIPTIONS.BASE, { params });
    
    const result = processApiResponse(response);
    
    return result;
  } catch (error) {
    handleError(error, 'SubscriptionService.getAllSubscriptions', true);
    throw error;
  }
}

export default getAllSubscriptions; 
