const mongoose = require('mongoose');
const { User, Shop, Subscription } = require('../models');

// Comprehensive cleanup for orphaned records from failed transactions
const cleanupOrphanedRecords = async () => {
  try {
    // Connect to database using environment variable
    const mongoUri = process.env.MONGODB_URI || 'mongodb+srv://Karshe:<EMAIL>/Deyncare?retryWrites=true&w=majority ';
    await mongoose.connect(mongoUri);
    console.log('✅ Connected to database');

    // Find users in pending_email_verification status
    const pendingUsers = await User.find({ 
      status: 'pending_email_verification',
      isDeleted: { $ne: true }
    });

    console.log(`🔍 Found ${pendingUsers.length} users in pending verification status`);

    let cleanedCount = 0;
    for (const user of pendingUsers) {
      try {
        // Check if user has a valid shop
        let hasValidShop = false;
        if (user.shopId) {
          const shop = await Shop.findOne({ shopId: user.shopId });
          hasValidShop = !!shop;
        }

        // If user has no shop or shop doesn't exist, clean up
        if (!hasValidShop) {
          console.log(`🧹 Cleaning orphaned user: ${user.email} (${user.userId}) - no valid shop`);
          
          // Soft delete the orphaned user
          user.isDeleted = true;
          user.deletedAt = new Date();
          user.status = 'deleted_orphaned';
          await user.save();
          
          cleanedCount++;
        } else {
          console.log(`✅ User ${user.email} has valid shop ${user.shopId}`);
        }
      } catch (userError) {
        console.error(`❌ Error processing user ${user.email}:`, userError.message);
      }
    }

    // Find shops without owners
    const orphanedShops = await Shop.find({
      $or: [
        { ownerId: { $exists: false } },
        { ownerId: null },
        { ownerId: '' }
      ]
    });

    console.log(`🔍 Found ${orphanedShops.length} shops without owners`);

    for (const shop of orphanedShops) {
      try {
        // Check if there's a user with this shopId
        const user = await User.findOne({ 
          shopId: shop.shopId,
          isDeleted: { $ne: true }
        });

        if (user) {
          // Link the shop to the user
          shop.ownerId = user.userId;
          await shop.save();
          console.log(`🔗 Linked shop ${shop.shopId} to user ${user.userId}`);
        } else {
          // No user found, mark shop for cleanup
          console.log(`🧹 Marking orphaned shop for cleanup: ${shop.shopName} (${shop.shopId})`);
          shop.status = 'orphaned';
          await shop.save();
        }
      } catch (shopError) {
        console.error(`❌ Error processing shop ${shop.shopId}:`, shopError.message);
      }
    }

    console.log(`✅ Cleanup completed. ${cleanedCount} orphaned users cleaned up.`);

  } catch (error) {
    console.error('❌ Error during cleanup:', error.message);
  } finally {
    await mongoose.disconnect();
    console.log('📤 Disconnected from database');
  }
};

// Run the cleanup if this script is executed directly
if (require.main === module) {
  cleanupOrphanedRecords();
}

module.exports = cleanupOrphanedRecords; 