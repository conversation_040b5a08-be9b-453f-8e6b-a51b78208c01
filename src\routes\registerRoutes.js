const express = require('express');
const multer = require('multer');
const { initRegistration } = require('../controllers/register/initRegistrationController');
const { verifyEmail, resendVerificationEmail } = require('../controllers/register/verifyEmailController');
const { processPayment } = require('../controllers/register/paymentController');
const { createShopWithAdmin, createAdminForShop, approveShopRegistration } = require('../controllers/register/superAdminController');
const { authenticate, authorize } = require('../middleware/authMiddleware');
const { validate } = require('../middleware/validationMiddleware');
const { registerSchemas } = require('../validations');
const ShopController = require('../controllers/shopController');
const { shopSchemas } = require('../validations');

// Configure Multer storage for different file types
const storageConfig = {
  destination: function (req, file, cb) {
    // Choose destination based on field name
    if (file.fieldname === 'paymentProof') {
      cb(null, 'uploads/payment-proofs/');
    } else if (file.fieldname === 'logo' || file.fieldname === 'shopLogo') {
      cb(null, 'uploads/shop-logos/');
    } else {
      cb(null, 'uploads/');
    }
  },
  filename: function (req, file, cb) {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    let prefix = 'file';
    if (file.fieldname === 'paymentProof') {
      prefix = 'payment-proof';
    } else if (file.fieldname === 'logo' || file.fieldname === 'shopLogo') {
      prefix = 'shop-logo';
    }
    cb(null, prefix + '-' + uniqueSuffix + '-' + file.originalname);
  }
};

const upload = multer({
  storage: multer.diskStorage(storageConfig),
  fileFilter: (req, file, cb) => {
    // Accept images and PDFs - consistent with uploadMiddleware
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'application/pdf'];

    // Enhanced logging for debugging
    console.log(`[RegisterRoutes] File validation:`, {
      fieldname: file.fieldname,
      originalname: file.originalname,
      mimetype: file.mimetype,
      size: file.size,
      allowed: allowedTypes.includes(file.mimetype)
    });

    if (allowedTypes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      console.log(`[RegisterRoutes] File rejected - MIME type '${file.mimetype}' not in allowed types:`, allowedTypes);
      cb(new Error('Invalid file type. Only JPG, PNG, and PDF files are allowed.'), false);
    }
  },
  limits: {
    fileSize: 5 * 1024 * 1024, // 5MB limit
  }
});

const router = express.Router();

// Route for initial registration (create user and shop)
// Allow both 'logo' and 'shopLogo' field names for backward compatibility
router.post('/init', upload.fields([
  { name: 'logo', maxCount: 1 },
  { name: 'shopLogo', maxCount: 1 }
]), validate(registerSchemas.initRegistration), initRegistration);

// Route for email verification
router.post('/verify-email', validate(registerSchemas.verifyEmail), verifyEmail);

// Route for resending verification email
router.post('/resend-verification', validate(registerSchemas.resendVerification), resendVerificationEmail);

// Route for payment processing with optional file upload (requires authentication and email verification)
router.post('/pay', authenticate, upload.single('paymentProof'), validate(registerSchemas.processPayment), processPayment);

// ============================================
// SuperAdmin Routes for Registration Management
// ============================================

// Create shop with admin user (SuperAdmin only)
router.post('/admin/create-shop', 
  authenticate, 
  authorize(['superAdmin']), 
  upload.single('shopLogo'),
  validate(registerSchemas.superAdminCreateShop),
  createShopWithAdmin
);

// Create admin user for existing shop (SuperAdmin only)
router.post('/admin/create-admin', 
  authenticate, 
  authorize(['superAdmin']), 
  validate(registerSchemas.superAdminCreateAdmin),
  createAdminForShop
);

// Approve pending shop registration (SuperAdmin only)
router.post('/admin/approve-shop/:shopId', 
  authenticate, 
  authorize(['superAdmin']), 
  validate(registerSchemas.superAdminApproveShop),
  approveShopRegistration
);

// ============================================
// SuperAdmin Shop CRUD Operations (NEW)
// ============================================

// Get all shops (SuperAdmin only)  
router.get('/admin/shops',
  authenticate,
  authorize(['superAdmin']),
  ShopController.getShops
);

// Get shop by ID (SuperAdmin only)
router.get('/admin/shops/:shopId',
  authenticate,
  authorize(['superAdmin']),
  ShopController.getShopById
);

// Get shop statistics (SuperAdmin only)
router.get('/admin/shops-stats',
  authenticate,
  authorize(['superAdmin']),
  ShopController.getShopStats
);

// Update shop information (SuperAdmin only)
router.put('/admin/shops/:shopId',
  authenticate,
  authorize(['superAdmin']),
  validate(shopSchemas.updateShop),
  ShopController.updateShop
);

// Update shop logo (SuperAdmin only)
router.put('/admin/shops/:shopId/logo',
  authenticate,
  authorize(['superAdmin']),
  upload.single('shopLogo'),
  ShopController.uploadShopLogo
);

// Change shop status (suspend/activate) (SuperAdmin only)
router.put('/admin/shops/:shopId/status',
  authenticate,
  authorize(['superAdmin']),
  validate(shopSchemas.changeShopStatus),
  ShopController.changeShopStatus
);

// Verify shop payment (SuperAdmin only)
router.put('/admin/shops/:shopId/payment',
  authenticate,
  authorize(['superAdmin']),
  validate(shopSchemas.verifyPayment),
  ShopController.verifyPayment
);

// Delete shop (SuperAdmin only)
router.delete('/admin/shops/:shopId',
  authenticate,
  authorize(['superAdmin']),
  validate(shopSchemas.deleteShop),
  ShopController.deleteShop
);

module.exports = router; 