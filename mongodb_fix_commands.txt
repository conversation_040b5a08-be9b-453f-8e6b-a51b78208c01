MON<PERSON><PERSON><PERSON> COMMANDS TO FIX PAYMENT METHODS
======================================

PROBLEM ANALYSIS:
-----------------
Your endpoint GET /api/settings/payment-methods returns:
{
  "success": true,
  "data": {
    "paymentMethods": [],  <-- EMPTY ARRAY (This is the problem!)
    "onlinePaymentsEnabled": true,
    "offlinePaymentsEnabled": true,
    "context": "general"
  }
}

The empty paymentMethods array means the 'payment_methods_available' setting is missing from the database.

SOLUTION - RUN THESE MONGODB COMMANDS:
=====================================

1. CONNECT TO YOUR MONGODB DATABASE:
-----------------------------------
mongo "your_mongodb_connection_string"
# OR if using MongoDB Compass, connect to your database

2. SWITCH TO YOUR DATABASE:
--------------------------
use your_database_name

3. CHECK CURRENT SETTINGS:
-------------------------
// Check if payment_methods_available exists
db.settings.findOne({key: "payment_methods_available", shopId: null})

// Check all payment-related settings
db.settings.find({$or: [{category: "payment"}, {key: /payment/i}]})

4. CREATE THE MISSING PAYMENT METHODS SETTING:
----------------------------------------------
db.settings.replaceOne(
  {key: "payment_methods_available", shopId: null},
  {
    key: "payment_methods_available",
    category: "payment",
    displayName: "Available Payment Methods",
    description: "List of all payment methods available in the system",
    value: ["EVC Plus", "Mobile Money", "Card", "offline"],
    dataType: "array",
    defaultValue: ["EVC Plus", "offline"],
    accessLevel: "superAdmin",
    isEditable: true,
    isVisible: true,
    shopId: null,
    updatedBy: "manual_fix",
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {upsert: true}
)

5. CREATE ENABLE_OFFLINE_PAYMENT SETTING:
-----------------------------------------
db.settings.replaceOne(
  {key: "enable_offline_payment", shopId: null},
  {
    key: "enable_offline_payment",
    category: "payment",
    displayName: "Enable Offline Payment",
    description: "Whether offline payment methods are enabled",
    value: true,
    dataType: "boolean",
    defaultValue: true,
    accessLevel: "superAdmin",
    isEditable: true,
    isVisible: true,
    shopId: null,
    updatedBy: "manual_fix",
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {upsert: true}
)

6. CREATE ENABLE_ONLINE_PAYMENT SETTING:
----------------------------------------
db.settings.replaceOne(
  {key: "enable_online_payment", shopId: null},
  {
    key: "enable_online_payment",
    category: "payment",
    displayName: "Enable Online Payment",
    description: "Whether online payment methods are enabled",
    value: true,
    dataType: "boolean",
    defaultValue: true,
    accessLevel: "superAdmin",
    isEditable: true,
    isVisible: true,
    shopId: null,
    updatedBy: "manual_fix",
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {upsert: true}
)

7. VERIFY THE FIX:
-----------------
// Check the payment_methods_available setting
db.settings.findOne({key: "payment_methods_available", shopId: null})

// Should return something like:
{
  "_id": ObjectId("..."),
  "key": "payment_methods_available",
  "category": "payment",
  "value": ["EVC Plus", "Mobile Money", "Card", "offline"],
  "dataType": "array",
  ...
}

// Check all payment settings
db.settings.find({category: "payment", shopId: null})

ALTERNATIVE - USING MONGODB COMPASS:
====================================

1. Open MongoDB Compass
2. Connect to your database
3. Navigate to the 'settings' collection
4. Click "Insert Document"
5. Paste this JSON:

{
  "key": "payment_methods_available",
  "category": "payment",
  "displayName": "Available Payment Methods",
  "description": "List of all payment methods available in the system",
  "value": ["EVC Plus", "Mobile Money", "Card", "offline"],
  "dataType": "array",
  "defaultValue": ["EVC Plus", "offline"],
  "accessLevel": "superAdmin",
  "isEditable": true,
  "isVisible": true,
  "shopId": null,
  "updatedBy": "manual_fix",
  "createdAt": "2025-01-30T12:00:00.000Z",
  "updatedAt": "2025-01-30T12:00:00.000Z"
}

6. Repeat for enable_offline_payment:

{
  "key": "enable_offline_payment",
  "category": "payment",
  "displayName": "Enable Offline Payment",
  "description": "Whether offline payment methods are enabled",
  "value": true,
  "dataType": "boolean",
  "defaultValue": true,
  "accessLevel": "superAdmin",
  "isEditable": true,
  "isVisible": true,
  "shopId": null,
  "updatedBy": "manual_fix",
  "createdAt": "2025-01-30T12:00:00.000Z",
  "updatedAt": "2025-01-30T12:00:00.000Z"
}

7. Repeat for enable_online_payment:

{
  "key": "enable_online_payment",
  "category": "payment",
  "displayName": "Enable Online Payment",
  "description": "Whether online payment methods are enabled",
  "value": true,
  "dataType": "boolean",
  "defaultValue": true,
  "accessLevel": "superAdmin",
  "isEditable": true,
  "isVisible": true,
  "shopId": null,
  "updatedBy": "manual_fix",
  "createdAt": "2025-01-30T12:00:00.000Z",
  "updatedAt": "2025-01-30T12:00:00.000Z"
}

TESTING AFTER FIX:
==================

1. Test the endpoint again:
GET /api/settings/payment-methods

2. Expected result:
{
  "success": true,
  "data": {
    "paymentMethods": ["EVC Plus", "Mobile Money", "Card", "offline"],
    "onlinePaymentsEnabled": true,
    "offlinePaymentsEnabled": true,
    "context": "general"
  }
}

3. Test offline payment registration:
POST /api/register/pay
{
  "planType": "monthly",
  "paymentMethod": "offline",
  "payerName": "Test User",
  "notes": "Test payment"
}

SUMMARY:
========
The issue is that the 'payment_methods_available' setting is missing from your database.
Once you add it with the value ["EVC Plus", "Mobile Money", "Card", "offline"], 
the endpoint will return the correct payment methods and offline payments will work.
