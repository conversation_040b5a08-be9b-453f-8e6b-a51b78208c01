const mongoose = require('mongoose');
const { User } = require('../../models');
const UserService = require('../../services/userService');
const ShopService = require('../../services/shopService');
const SettingsService = require('../../services/settingsService');
const DiscountService = require('../../services/discountService');
const EmailService = require('../../services/emailService');
const TokenService = require('../../services/tokenService');
const authSchemas = require('../../validations/schemas/authSchemas');

// Import utility modules using the new directory structure
const { 
  AppError, 
  generateVerificationCode,
  ResponseHelper,
  UserHelper,
  ShopHelper,
  SubscriptionHelper,
  LogHelper,
  logInfo,
  logSuccess,
  logWarning,
  logError,
  TokenHelper,
  idGenerator,
  TransactionHelper
} = require('../../utils');

/**
 * Register a new user with shop
 * POST /api/auth/register
 */
const register = async (req, res, next) => {
  try {
    const { 
      // User data
      fullName, 
      email, 
      phone, 
      password,
      // Shop data
      shopName, 
      shopAddress,
      // Subscription data
      planType = 'trial',
      registeredBy = 'self',
      paymentMethod = 'offline',
      initialPaid: userInitialPaid = false,
      // Payment details
      paymentDetails: rawPaymentDetails,
      discountCode
    } = req.validatedData || req.body; // Use validated data if available
    
    // Auto-prepare payment details for EVC Plus payments using the registration phone number
    let paymentDetails = rawPaymentDetails || {};
    let initialPaid = userInitialPaid; // Use a mutable variable
    
    // If using EVC Plus and payment details don't include phone number, use the registration phone
    if (paymentMethod === 'EVC Plus' && initialPaid === true) {
      if (!paymentDetails.phoneNumber && phone) {
        paymentDetails = {
          ...paymentDetails,
          phoneNumber: phone  // Use the registration phone number for payment
        };
        logInfo(`Auto-populated EVC payment phone number from registration data: ${phone}`, 'AuthController');
      }
    }

    // Check if the selected payment method is enabled
    const { Setting } = require('../../models');
    const allowedPaymentMethods = await Setting.findOne({ 
      key: 'payment_methods_available',
      shopId: null // Global setting
    });
    
    // If we have settings, validate the payment method
    if (allowedPaymentMethods && allowedPaymentMethods.value) {
      if (!allowedPaymentMethods.value.includes(paymentMethod)) {
        return next(new AppError(
          `Payment method "${paymentMethod}" is not currently available. Allowed methods: ${allowedPaymentMethods.value.join(', ')}`,
          400,
          'invalid_payment_method'
        ));
      }
      
      // Also check if online/offline payment is enabled
      const isOnlineMethod = ['EVC Plus', 'Card', 'Mobile Money'].includes(paymentMethod);
      const isOfflineMethod = ['Cash', 'Bank Transfer', 'Check', 'Other', 'offline'].includes(paymentMethod);
      
      if (isOnlineMethod) {
        const onlineEnabled = await Setting.findOne({ 
          key: 'enable_online_payment',
          shopId: null
        });
        
        if (onlineEnabled && onlineEnabled.value === false) {
          return next(new AppError(
            'Online payment methods are currently disabled',
            400,
            'payment_method_disabled'
          ));
        }
      } else if (isOfflineMethod) {
        const offlineEnabled = await Setting.findOne({ 
          key: 'enable_offline_payment',
          shopId: null
        });
        
        if (offlineEnabled && offlineEnabled.value === false) {
          return next(new AppError(
            'Offline payment methods are currently disabled',
            400,
            'payment_method_disabled'
          ));
        }
      }
    }

    // 2. Register the user
    try {
      // Use our new TransactionHelper for end-to-end transaction management
      const result = await TransactionHelper.withTransaction(async (session) => {
      let userData;
      let shopId = null;
      let shopLogoData = null;
      let discountDetails = null;
      let verificationCode = generateVerificationCode(6); // Always defined in transaction scope
      
      try {
        // Process shop logo file if uploaded
        if (req.file) {
          const FileUploadService = require('../../services/fileUploadService');
          shopLogoData = await FileUploadService.saveShopLogo(req.file);
        }
        
        // Check if there's a discount code and verify it
        if (discountCode) {
          discountDetails = await DiscountService.verifyDiscountCode(discountCode);
          
          if (!discountDetails.valid) {
            throw new AppError(
              discountDetails.message || 'Invalid discount code',
              400,
              'invalid_discount_code'
            );
          }
          
          // Log discount code usage
          logInfo(`Discount code ${discountCode} applied to shop registration`, 'AuthController');
        }
        
        // Create shop first with explicit initial statuses
        const shopData = {
          shopName,
          ownerName: fullName,
          email,
          phone,
          address: shopAddress,
          status: 'pending', // Explicit initial shop status
          access: {
            isPaid: false,     // Access control: not paid
            isActivated: false // Access control: not activated
          },
          paymentHistory: [], // Initialize payment history
          subscription: {
            planType,
            paymentMethod,
            initialPaid,
            paymentDetails,
            discountDetails: discountDetails?.valid ? discountDetails.discountDetails : null,
            status: 'pending_payment' // Explicit initial subscription status
          },
          registeredBy,
          // Using session for transaction consistency
          session
        };
        
        // If using EVC Plus payment and initialPaid is true, trigger payment
        let evcPaymentResult = null;
        if (paymentMethod === 'EVC Plus' && initialPaid === true && paymentDetails && paymentDetails.phoneNumber) {
          try {
            // Import EVC payment service
            const EVCPaymentService = require('../../services/evcPaymentService');
            
            // Get plan price considering any discount
            let planPrice = 0;
            if (discountDetails?.valid && discountDetails.discountDetails) {
              planPrice = discountDetails.discountDetails.discountAmount;
            } else {
              // Get base plan price from subscription service
              const { SubscriptionHelper } = require('../../utils');
              const planInfo = SubscriptionHelper.getPlanPrice(planType);
              planPrice = planInfo.price;
            }
            
            logInfo(`Initiating EVC Plus payment of $${planPrice} for ${paymentDetails.phoneNumber}`, 'AuthController');
            
            // Prepare payment data
            const paymentData = {
              phone: paymentDetails.phoneNumber,
              amount: planPrice,
              reference: `sub_${Date.now()}`, // Generate a unique reference
              description: `${shopName} subscription: ${planType} plan`,
              shopName: shopName || 'New Shop',
              shopId: null // Explicitly use null for shopId during initial registration payment
            };
            
            // Process EVC Plus payment during registration
            try {
              const evcResponse = await EVCPaymentService.retryPayment(paymentData);
              
              if (evcResponse && evcResponse.success) {
                // Update payment details with transaction ID
                paymentDetails.transactionId = evcResponse.transactionId;
                shopData.subscription.paymentDetails = paymentDetails;
                logInfo(`EVC payment successful for ${paymentData.reference}`, 'AuthController');
              } else {
                // Payment failed but we'll continue with registration
                logError(`EVC payment failed but continuing registration: ${evcResponse?.responseMessage || 'Unknown error'}`, 'AuthController', evcResponse);
                
                // Still update payment details for future retry
                if (!shopData.subscription.paymentDetails) {
                  shopData.subscription.paymentDetails = { phoneNumber: paymentDetails.phoneNumber };
                }
              }
            } catch (err) {
              // Handle API authentication errors more gracefully
              if (err.type === 'api_authentication_error') {
                logError(`EVC API credentials error but continuing registration: ${err.message}`, 'AuthController', err);
              } else {
                logError(`Error processing EVC payment but continuing registration: ${err.message || err}`, 'AuthController', err);
              }
              
              // Still update payment details for future retry
              if (!shopData.subscription.paymentDetails) {
                shopData.subscription.paymentDetails = { phoneNumber: paymentDetails.phoneNumber };
              }
            }
          } catch (paymentError) {
            logError(`Error processing EVC payment: ${paymentError.message}`, 'AuthController', paymentError);
            // Continue with registration but mark as not paid
            initialPaid = false;
            shopData.subscription.initialPaid = false;
          }
        }
        
        const createdShop = await ShopService.createShop(shopData);
        shopId = createdShop.shopId;
        
        // Now create user with reference to shop
        // Create user with explicit initial statuses
        userData = await UserService.createUser({
          fullName,
          email,
          phone,
          password,
          role: 'admin', // Shop owner is always admin
          shopId: shopId,
          registeredBy,
          status: 'pending_email_verification', // Explicit initial user status
          verified: false, // Not verified yet
          verificationCode
        }, session);
        
        // Also update the shop with the owner reference
        createdShop.ownerId = userData.userId;
        await createdShop.save({ session });
        
        logSuccess(`Successfully registered user ${userData.userId} with shop ${shopId}`, 'AuthController');
        
        // Return the data - TransactionHelper will handle the commit
        return { userData, shopId, verificationCode };
      } catch (error) {
        // TransactionHelper will handle the abort and logging
        throw error;
      }
      }, { name: 'UserRegistration' });
      
      // Get result from transaction
      const { userData, shopId, verificationCode } = result;
      
      // Send verification email
      try {
        await EmailService.auth.sendVerificationEmail(userData, verificationCode);
        logSuccess(`Verification email sent to ${email}`, 'AuthController');
      } catch (emailError) {
        logError(`Failed to send verification email to ${email}`, 'AuthController', emailError);
        // Don't fail registration if email fails
      }
      
      // Generate JWT token for immediate login
      const token = TokenService.generateAuthTokens(userData);
      
      // Return success response with token
      return ResponseHelper.success(
        res,
        'Registration successful',
        {
          token,
          user: UserHelper.sanitizeUser(userData),
          shop: {
            id: shopId,
            name: shopName
          }
        }
      );
    } catch (error) {
      // Handle specific database errors
      if (error.name === 'MongoServerError' && error.code === 11000) {
        // Duplicate key error
        const field = Object.keys(error.keyValue)[0];
        const errorMessage = field === 'email' 
          ? 'Email already exists'
          : `${field.charAt(0).toUpperCase() + field.slice(1)} already exists`;
        
        return next(new AppError(
          errorMessage,
          409,
          'duplicate_key'
        ));
      }
      
      // TransactionHelper already handled the abort and logging
      return next(new AppError(
        error.message || 'Error during registration',
        error.statusCode || 500,
        error.type || 'registration_error'
      ));
    }
  } catch (error) {
    // Handle general errors not caught in the inner try-catch
    logError('Registration error', 'AuthController', error);
    return next(new AppError(
      error.message || 'Error during registration',
      error.statusCode || 500,
      error.type || 'registration_error'
    ));
  }
};


/**
 * Create a new employee user by an admin
 * POST /api/auth/create-employee
 * Requires authentication and admin authorization
 */
const createEmployee = async (req, res, next) => {
  try {
    const { value, error } = authSchemas.createEmployee.validate(req.body);
    if (error) {
      return next(new AppError(error.details[0].message, 400, 'validation_error'));
    }

    const { fullName, userTitle, email, phone, password, visibility } = value;

    // Check if email already exists (including soft-deleted users)
    const normalizedEmail = email.toLowerCase().trim();
    const existingUser = await User.findOne({
      email: normalizedEmail,
      isDeleted: { $ne: true } // Only check active users
    });
    if (existingUser) {
      return next(new AppError('Email already registered', 409, 'email_exists'));
    }

    // Create employee with granular permissions
    // Note: Password will be automatically hashed by the User model's pre-save middleware
    const newEmployee = new User({
      userId: await idGenerator.generateUserId(User),
      fullName,
      userTitle,
      email,
      phone,
      password: password, // Let the model handle password hashing
      role: 'employee',
      shopId: req.user.shopId,
      visibility: visibility || {
        customerManagement: { create: false, update: false, view: false, delete: false },
        debtManagement: { create: false, update: false, view: false, delete: false },
        reportManagement: { generate: false, delete: false, view: false }
      },
      createdBy: req.user.userId,
      isEmailVerified: true, // Auto-verify for admin-created employees
      verified: true, // Mark as verified for admin-created employees
      status: 'active'
    });

    await newEmployee.save();

    // Remove password from response
    const employeeResponse = newEmployee.toObject();
    delete employeeResponse.password;

    res.status(201).json({
      success: true,
      message: 'Employee created successfully',
      data: {
        employee: employeeResponse
      }
    });

  } catch (error) {
    next(error);
  }
};

module.exports = {
  register,
  createEmployee
};

