const { Debt, Payment } = require('../../models');
const { AppError, logError } = require('../../utils');

/**
 * Get Debt Statistics and ML Insights
 * GET /api/debts/stats
 */
const getDebtStats = async (req, res, next) => {
  try {
    const shopId = req.user.shopId;

    // Get comprehensive debt statistics
    const totalDebts = await Debt.countDocuments({ shopId, isDeleted: false });
    const activeDebts = await Debt.countDocuments({ 
      shopId, 
      isDeleted: false, 
      OutstandingDebt: { $gt: 0 } 
    });

    // Risk level distribution
    const riskDistribution = await Debt.aggregate([
      { 
        $match: { 
          shopId, 
          isDeleted: false, 
          OutstandingDebt: { $gt: 0 } 
        } 
      },
      {
        $group: {
          _id: '$RiskLevel',
          count: { $sum: 1 },
          totalAmount: { $sum: '$OutstandingDebt' }
        }
      }
    ]);

    // Financial overview
    const financialStats = await Debt.aggregate([
      { 
        $match: { 
          shopId, 
          isDeleted: false 
        } 
      },
      {
        $group: {
          _id: null,
          totalLent: { $sum: '$DebtAmount' },
          totalOutstanding: { $sum: '$OutstandingDebt' },
          totalPaid: { $sum: '$PaidAmount' },
          averageDebtAmount: { $avg: '$DebtAmount' },
          averagePaymentRatio: { $avg: '$DebtPaidRatio' }
        }
      }
    ]);

    // Overdue analysis
    const now = new Date();
    const overdueDebts = await Debt.countDocuments({
      shopId,
      isDeleted: false,
      DueDate: { $lt: now },
      OutstandingDebt: { $gt: 0 }
    });

    const overdueAmount = await Debt.aggregate([
      {
        $match: {
          shopId,
          isDeleted: false,
          DueDate: { $lt: now },
          OutstandingDebt: { $gt: 0 }
        }
      },
      {
        $group: {
          _id: null,
          totalOverdue: { $sum: '$OutstandingDebt' }
        }
      }
    ]);

    // Payment delay analysis
    const paymentDelayStats = await Debt.aggregate([
      {
        $match: {
          shopId,
          isDeleted: false,
          PaymentDelay: { $gt: 0 }
        }
      },
      {
        $group: {
          _id: null,
          averageDelay: { $avg: '$PaymentDelay' },
          maxDelay: { $max: '$PaymentDelay' },
          totalDelayedDebts: { $sum: 1 }
        }
      }
    ]);

    // Recent payment activity (last 30 days)
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    const recentPayments = await Payment.aggregate([
      {
        $match: {
          shopId,
          paymentContext: 'debt',
          isDeleted: false,
          paymentDate: { $gte: thirtyDaysAgo }
        }
      },
      {
        $group: {
          _id: null,
          totalPayments: { $sum: 1 },
          totalAmount: { $sum: '$amount' },
          averagePayment: { $avg: '$amount' }
        }
      }
    ]);

    // Customer type analysis
    const customerTypeStats = await Debt.aggregate([
      {
        $match: {
          shopId,
          isDeleted: false
        }
      },
      {
        $group: {
          _id: '$CustomerType',
          count: { $sum: 1 },
          totalAmount: { $sum: '$DebtAmount' },
          averageRisk: { $avg: '$DebtPaidRatio' }
        }
      }
    ]);

    // ML insights summary
    const mlInsights = {
      riskDistribution: riskDistribution.reduce((acc, curr) => {
        acc[curr._id] = {
          count: curr.count,
          totalAmount: curr.totalAmount,
          percentage: totalDebts > 0 ? Math.round((curr.count / totalDebts) * 100) : 0
        };
        return acc;
      }, {}),
      
      highRiskCount: riskDistribution.find(r => r._id === 'High Risk')?.count || 0,
      mediumRiskCount: riskDistribution.find(r => r._id === 'Medium Risk')?.count || 0,
      lowRiskCount: riskDistribution.find(r => r._id === 'Low Risk')?.count || 0,
      
      riskTrend: await calculateRiskTrend(shopId),
      paymentPerformance: {
        onTimePayments: await Debt.countDocuments({ shopId, isDeleted: false, IsOnTime: true }),
        latePayments: await Debt.countDocuments({ shopId, isDeleted: false, IsOnTime: false }),
        averageDelay: paymentDelayStats[0]?.averageDelay || 0
      }
    };

    const stats = {
      overview: {
        totalDebts,
        activeDebts,
        paidDebts: totalDebts - activeDebts,
        overdueDebts,
        overdueAmount: overdueAmount[0]?.totalOverdue || 0
      },
      
      financial: {
        totalLent: financialStats[0]?.totalLent || 0,
        totalOutstanding: financialStats[0]?.totalOutstanding || 0,
        totalPaid: financialStats[0]?.totalPaid || 0,
        averageDebtAmount: Math.round(financialStats[0]?.averageDebtAmount || 0),
        collectionRate: financialStats[0]?.totalLent > 0 ? 
          Math.round(((financialStats[0]?.totalPaid || 0) / financialStats[0]?.totalLent) * 100) : 0
      },
      
      riskAnalysis: mlInsights,
      
      customerInsights: {
        newCustomers: customerTypeStats.find(c => c._id === 'New')?.count || 0,
        returningCustomers: customerTypeStats.find(c => c._id === 'Returning')?.count || 0,
        newCustomerRisk: customerTypeStats.find(c => c._id === 'New')?.averageRisk || 0,
        returningCustomerRisk: customerTypeStats.find(c => c._id === 'Returning')?.averageRisk || 0
      },
      
      recentActivity: {
        paymentsLast30Days: recentPayments[0]?.totalPayments || 0,
        amountLast30Days: recentPayments[0]?.totalAmount || 0,
        averagePaymentLast30Days: Math.round(recentPayments[0]?.averagePayment || 0)
      },
      
      performance: {
        averagePaymentDelay: Math.round(paymentDelayStats[0]?.averageDelay || 0),
        maxPaymentDelay: paymentDelayStats[0]?.maxDelay || 0,
        delayedDebtsCount: paymentDelayStats[0]?.totalDelayedDebts || 0
      }
    };

    res.json({
      success: true,
      message: 'Debt statistics retrieved successfully',
      data: stats,
      generatedAt: new Date()
    });

  } catch (error) {
    logError('Failed to get debt statistics', 'GetDebtStats', error);
    return next(new AppError('Failed to retrieve debt statistics', 500));
  }
};

/**
 * Calculate risk trend over the last 3 months
 */
async function calculateRiskTrend(shopId) {
  try {
    const threeMonthsAgo = new Date();
    threeMonthsAgo.setMonth(threeMonthsAgo.getMonth() - 3);

    const trend = await Debt.aggregate([
      {
        $match: {
          shopId,
          isDeleted: false,
          createdAt: { $gte: threeMonthsAgo }
        }
      },
      {
        $group: {
          _id: {
            year: { $year: '$createdAt' },
            month: { $month: '$createdAt' },
            riskLevel: '$RiskLevel'
          },
          count: { $sum: 1 }
        }
      },
      {
        $sort: { '_id.year': 1, '_id.month': 1 }
      }
    ]);

    return trend.length > 0 ? 'data_available' : 'insufficient_data';
  } catch (error) {
    return 'calculation_error';
  }
}

module.exports = getDebtStats; 