/**
 * Settings Services Index
 * Exports all settings-related services that match backend API exactly
 */

// Core settings services
export { default as fetchSettings } from './fetchSettings';
export { default as updateSettings } from './updateSettings';
export { default as updateSecuritySettings } from './updateSecuritySettings';

// System logs services  
export { default as fetchSystemLogs } from './fetchSystemLogs';
export { default as exportSystemLogs } from './exportSystemLogs';

// EVC credentials services
export { default as fetchEvcCredentials } from './fetchEvcCredentials';
export { default as setEvcCredentials } from './setEvcCredentials';
export { default as testEvcCredentials } from './testEvcCredentials';

// Payment methods services
export { default as fetchPaymentMethods } from './fetchPaymentMethods';
export { default as updatePaymentMethods } from './updatePaymentMethods';

// ML settings services
export { default as fetchMLSettings } from './fetchMLSettings';
export { default as updateMLSettings } from './updateMLSettings';

// Technical ML settings services (SuperAdmin only)
export { default as fetchTechnicalMLSettings } from './fetchTechnicalMLSettings';
export { default as updateTechnicalMLSettings } from './updateTechnicalMLSettings';

// Import all services for default export
import fetchSettings from './fetchSettings';
import updateSettings from './updateSettings';
import updateSecuritySettings from './updateSecuritySettings';
import fetchSystemLogs from './fetchSystemLogs';
import exportSystemLogs from './exportSystemLogs';
import fetchEvcCredentials from './fetchEvcCredentials';
import setEvcCredentials from './setEvcCredentials';
import testEvcCredentials from './testEvcCredentials';
import fetchPaymentMethods from './fetchPaymentMethods';
import updatePaymentMethods from './updatePaymentMethods';
import fetchMLSettings from './fetchMLSettings';
import updateMLSettings from './updateMLSettings';
import fetchTechnicalMLSettings from './fetchTechnicalMLSettings';
import updateTechnicalMLSettings from './updateTechnicalMLSettings';

// Default export for contexts that expect it
const SettingsService = {
  fetchSettings,
  updateSettings,
  updateSecuritySettings,
  fetchSystemLogs,
  exportSystemLogs,
  fetchEvcCredentials,
  setEvcCredentials,
  testEvcCredentials,
  fetchPaymentMethods,
  updatePaymentMethods,
  fetchMLSettings,
  updateMLSettings,
  fetchTechnicalMLSettings,
  updateTechnicalMLSettings
};

export default SettingsService;
