const Joi = require('joi');
const patterns = require('../validationPatterns');

/**
 * Authentication and account management validation schemas
 */
const authSchemas = {
  register: Joi.object({
    // User data (required)
    fullName: patterns.string.fullName.required()
      .messages({
        'string.min': 'Full name must be at least 3 characters',
        'string.max': 'Full name cannot exceed 100 characters',
        'any.required': 'Full name is required'
      }),
    email: patterns.string.email.required()
      .messages({
        'string.email': 'Valid email address is required',
        'any.required': 'Email is required'
      }),
    phone: patterns.string.phone.required()
      .messages({
        'string.pattern.base': 'Phone number must be in international format (e.g. +252xxxxxxxx)',
        'any.required': 'Phone number is required'
      }),
    password: patterns.string.password.required()
      .messages({
        'string.min': 'Password must be at least 8 characters',
        'any.required': 'Password is required'
      }),
      
    // Shop data (optional as a group, but required if any shop field provided)
    shopName: patterns.string.shopName.optional()
      .messages({
        'string.min': 'Shop name must be at least 2 characters',
        'string.max': 'Shop name cannot exceed 100 characters'
      }),
    shopAddress: patterns.string.shopAddress.optional()
      .messages({
        'string.min': 'Shop address must be at least 5 characters',
        'string.max': 'Shop address cannot exceed 200 characters'
      }),
      
    // Ensure shopName and shopAddress are provided together
    shopLogo: Joi.string().uri().allow('').optional(),
    
    // Subscription data with defaults and validation
    planType: Joi.string().valid(...patterns.enums.planType).default('trial'),
    registeredBy: Joi.string().valid('self', 'superAdmin').default('self'),
    paymentMethod: Joi.string().valid('Cash', 'EVC Plus', 'Bank Transfer', 'Mobile Money', 'Check', 'Card', 'Other', 'offline').default('offline'),
    initialPaid: Joi.boolean().default(false),
    discountCode: Joi.string().trim().optional()
      .messages({
        'string.empty': 'Discount code cannot be empty if provided'
      }),
    
    // Payment details (conditionally required if initialPaid is true)
    paymentDetails: patterns.object.paymentDetails.optional()
  }).custom((value, helpers) => {
    // Custom validation to ensure shop details are provided together
    const { shopName, shopAddress } = value;
    
    if ((shopName && !shopAddress) || (!shopName && shopAddress)) {
      return helpers.error('object.shopDetailsRequired');
    }
    
    // Check if initial payment is made, payment details should be provided
    const { initialPaid, paymentDetails, paymentMethod, phone } = value;
    
    // Special case for EVC Plus: if no payment details but we have a phone, we'll use that in the controller
    if (initialPaid && (!paymentDetails || Object.keys(paymentDetails).length === 0)) {
      // If using EVC Plus and we have a phone number, we'll auto-create payment details later
      if (paymentMethod === 'EVC Plus' && phone) {
        // Allow this case - the controller will handle it
      } else {
        // For all other cases, require payment details
        return helpers.error('object.paymentDetailsRequired');
      }
    }
    
    return value;
  }).messages({
    'object.shopDetailsRequired': 'Both shop name and address must be provided together',
    'object.paymentDetailsRequired': 'Payment details are required when initial payment is made'
  }),
  
  // Schema for admin creating employee users
  createEmployee: Joi.object({
    // Basic user information
    fullName: patterns.string.fullName.required()
      .messages({
        'string.min': 'Full name must be at least 3 characters',
        'string.max': 'Full name cannot exceed 100 characters',
        'any.required': 'Full name is required'
      }),
    userTitle: Joi.string().required().min(2).max(50)
      .messages({
        'string.min': 'Job title must be at least 2 characters',
        'string.max': 'Job title cannot exceed 50 characters',
        'any.required': 'Job title is required'
      }),
    email: patterns.string.email.required()
      .messages({
        'string.email': 'Valid email address is required',
        'any.required': 'Email is required'
      }),
    phone: patterns.string.phone.required()
      .messages({
        'string.pattern.base': 'Phone number must be in international format (e.g. +252xxxxxxxx)',
        'any.required': 'Phone number is required'
      }),

    // Password fields
    password: Joi.string().min(6).required()
      .messages({
        'string.min': 'Password must be at least 6 characters',
        'any.required': 'Password is required'
      }),
    confirmPassword: Joi.string().valid(Joi.ref('password')).required()
      .messages({
        'any.only': 'Passwords do not match',
        'any.required': 'Password confirmation is required'
      }),

    // Employee permissions/visibility
    visibility: Joi.object({
      customerManagement: Joi.object({
        create: Joi.boolean().default(false),
        update: Joi.boolean().default(false),
        view: Joi.boolean().default(false),
        delete: Joi.boolean().default(false)
      }).default({}),
      debtManagement: Joi.object({
        create: Joi.boolean().default(false),
        update: Joi.boolean().default(false),
        view: Joi.boolean().default(false),
        delete: Joi.boolean().default(false)
      }).default({}),
      reportManagement: Joi.object({
        generate: Joi.boolean().default(false),
        delete: Joi.boolean().default(false),
        view: Joi.boolean().default(false)
      }).default({})
    }).optional()
  }),

  // Schema for admin resetting employee password
  resetEmployeePassword: Joi.object({
    employeeId: Joi.string().required()
      .messages({
        'any.required': 'Employee ID is required'
      }),
    sendEmail: Joi.boolean().default(true)
      .messages({
        'boolean.base': 'Send email must be a boolean value'
      })
  }),



  verifyEmail: Joi.object({
    email: patterns.string.email.required()
      .messages({
        'string.email': 'Valid email address is required',
        'any.required': 'Email is required'
      }),
    verificationCode: Joi.string().min(6).max(6).required()
      .messages({
        'string.min': 'Verification code must be 6 characters',
        'string.max': 'Verification code must be 6 characters',
        'any.required': 'Verification code is required'
      })
  }),

  login: Joi.object({
    email: patterns.string.email.required()
      .messages({
        'string.email': 'Valid email address is required',
        'any.required': 'Email is required'
      }),
    password: Joi.string().required()
      .messages({
        'any.required': 'Password is required'
      }),
    rememberMe: Joi.boolean().default(false)
  }),

  forgotPassword: Joi.object({
    email: patterns.string.email.required()
      .messages({
        'string.email': 'Valid email address is required',
        'any.required': 'Email is required'
      })
  }),

  checkEmail: Joi.object({
    email: patterns.string.email.required()
      .messages({
        'string.email': 'Valid email address is required',
        'any.required': 'Email is required'
      })
  }),

  resetPassword: Joi.object({
    token: Joi.string().required()
      .messages({
        'any.required': 'Reset token is required'
      }),
    newPassword: patterns.string.password.required()
      .messages({
        'string.min': 'Password must be at least 8 characters',
        'any.required': 'Password is required'
      }),
    confirmPassword: Joi.string().valid(Joi.ref('newPassword')).required()
      .messages({
        'any.only': 'Passwords do not match',
        'any.required': 'Password confirmation is required'
      })
  }),

  changePassword: Joi.object({
    currentPassword: Joi.string().required()
      .messages({
        'any.required': 'Current password is required'
      }),
    newPassword: patterns.string.password.required()
      .messages({
        'string.min': 'New password must be at least 8 characters',
        'any.required': 'New password is required'
      }),
    confirmPassword: Joi.string().valid(Joi.ref('newPassword')).required()
      .messages({
        'any.only': 'Passwords do not match',
        'any.required': 'Password confirmation is required'
      })
  }),

  updateProfile: Joi.object({
    fullName: patterns.string.fullName.optional()
      .messages({
        'string.min': 'Full name must be at least 3 characters',
        'string.max': 'Full name cannot exceed 100 characters'
      }),
    phone: patterns.string.phone.optional()
      .messages({
        'string.pattern.base': 'Phone number must be in international format (e.g. +252xxxxxxxx)'
      })
  }).min(1)
    .messages({
      'object.min': 'At least one field must be provided for update'
    })
};

module.exports = authSchemas;

