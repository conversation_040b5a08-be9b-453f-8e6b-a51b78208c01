# New Registration API Documentation

This document outlines the API endpoints involved in the new user registration and subscription payment flow.

---

## 1. Verify Email and Generate Token

This endpoint is used to verify a user's email address and generate an access token for subsequent steps.

- **URL:** `/api/register/verify-email`
- **Method:** `POST`
- **Description:** Initiates the email verification process. It expects a user ID and email, and upon successful verification, it generates an access token for the user.
- **Request Body:**
  ```json
  {
    "userId": "string",  
    "email": "string"    
  }
  ```
  - `userId`: The ID of the user to verify.
  - `email`: The email address of the user to verify.

- **Successful Response (200 OK):**
  ```json
  {
    "success": true,
    "message": "<PERSON><PERSON> verified successfully, token generated",
    "token": "string" 
  }
  ```
  - `token`: JWT access token to be used in subsequent authenticated requests.

- **Error Responses:**
  - `400 Bad Request`: Invalid input or missing required fields.
  - `404 Not Found`: User not found.
  - `500 Internal Server Error`: Server-side error during token generation or email verification.

---

## 2. Process Payment

This endpoint handles the payment processing for a user's subscription, creates a new subscription record, and updates the user's status.

- **URL:** `/api/register/pay`
- **Method:** `POST`
- **Description:** Processes payment for a selected plan, creates a new `Subscription` document, links it to the `Shop`, and updates the `User` status to `active`. It also handles transaction deduplication to prevent multiple simultaneous payment attempts.
- **Request Body:**
  ```json
  {
    "userId": "string",        
    "shopId": "string",        
    "planId": "string",        
    "paymentMethod": "string", 
    "phone": "string"          
  }
  ```
  - `userId`: The ID of the user initiating the payment.
  - `shopId`: The ID of the shop associated with the user.
  - `planId`: The ID of the subscription plan selected by the user.
  - `paymentMethod`: The chosen payment method (e.g., "EVC Plus", "Cash").
  - `phone`: The phone number associated with the payment method (e.g., for EVC Plus).

- **Successful Response (200 OK):**
  ```json
  {
    "success": true,
    "message": "Payment processed successfully",
    "data": {
      "subscriptionId": "string", 
      "transactionId": "string",  
      "amount": "number",         
      "currency": "string",       
      "paymentMethod": "string"   
    }
  }
  ```
  - `subscriptionId`: The ID of the newly created subscription.
  - `transactionId`: The unique ID of the payment transaction.
  - `amount`: The amount paid for the subscription.
  - `currency`: The currency of the payment (e.g., "USD").
  - `paymentMethod`: The payment method used.

- **Error Responses:**
  - `400 Bad Request`: Missing required payment information, unsupported payment method, or payment failed.
  - `404 Not Found`: User, shop, or plan not found.
  - `409 Conflict`: Payment already in progress for this user.
  - `500 Internal Server Error`: Server-side error during payment processing or subscription creation.
  - `502 Bad Gateway`: Error communicating with the payment gateway.
--- 