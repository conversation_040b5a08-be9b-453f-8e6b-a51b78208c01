const Customer = require('../../models/customer.model');
const Debt = require('../../models/debt.model');
const Payment = require('../../models/payment.model');
const { generateCustomerId, generateDebtId, generatePaymentId } = require('../../utils/generators/idGenerator');
const AppError = require('../../utils/core/AppError');
const DebtNotificationService = require('../../services/debtNotificationService');

/**
 * Create Debt Controller (Admin Role - Shop Owner)
 * Step 1: Customer Takes a Loan (Debt Registration)
 * - References existing customer by customerId (BEST PRACTICE)
 * - Records debt transaction in database
 * - NO risk evaluation yet (due date not passed)
 * - Status: "Active Debt"
 * - Sends push notification to shop admins with customer info and risk assessment
 */
const createDebt = async (req, res, next) => {
  try {
    const {
      customerId, // Reference existing customer (BEST PRACTICE)
      debtAmount,
      dueDate,
      description,
      paidAmount = 0, // Optional initial payment
      paidDate,
      paymentMethod = 'cash'
    } = req.body;

    const shopId = req.user.shopId; // Admin (shop owner) context
    const adminId = req.user.userId;

    // Step 1: Find existing customer by ID (WITH TIMEOUT)
    console.log('🔍 Looking up customer...');
    const customerLookupPromise = Customer.findOne({
      customerId,
      shopId,
      isDeleted: false
    });
    const customerTimeoutPromise = new Promise((_, reject) => {
      setTimeout(() => reject(new Error('Customer lookup timeout after 5 seconds')), 5000);
    });
    
    const customer = await Promise.race([customerLookupPromise, customerTimeoutPromise]);

    if (!customer) {
      return next(new AppError(`Customer with ID ${customerId} not found`, 404));
    }
    console.log('✅ Customer found:', customer.CustomerName);

    // Step 2: Create debt record with "Active Debt" status (WITH TIMEOUT)
    console.log('🆔 Generating debt ID...');
    const debtIdPromise = generateDebtId(Debt);
    const idTimeoutPromise = new Promise((_, reject) => {
      setTimeout(() => reject(new Error('Debt ID generation timeout after 5 seconds')), 5000);
    });
    
    const debtId = await Promise.race([debtIdPromise, idTimeoutPromise]);
    
    // Validate that debtId was generated successfully
    if (!debtId) {
      console.error('Failed to generate debtId');
      return next(new AppError('Failed to generate unique debt ID', 500));
    }
    
    console.log('✅ Generated debtId:', debtId);
    
    const debt = new Debt({
      debtId,
      customerId: customer.customerId,
      shopId,
      CustomerName: customer.CustomerName,
      CustomerType: customer.CustomerType,
      DebtAmount: debtAmount,
      OutstandingDebt: debtAmount - paidAmount,
      DueDate: new Date(dueDate),
      description,
      status: 'active' // Active until due date passes
      // RiskLevel will use model default: 'Active Debt' (no evaluation yet)
      // DebtID, RepaymentTime, DebtPaidRatio auto-calculated in pre-save
    });

    try {
      console.log('💾 Saving debt to database...');
      
      // Add timeout to debt save operation
      const savePromise = debt.save();
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('Debt save timeout after 10 seconds')), 10000);
      });
      
      await Promise.race([savePromise, timeoutPromise]);
      console.log('✅ Debt saved successfully with ID:', debt.debtId);
    } catch (saveError) {
      console.error('❌ Error saving debt:', saveError);
      
      // Handle timeout specifically
      if (saveError.message.includes('timeout')) {
        return next(new AppError('Database operation timed out. Please try again.', 500));
      }
      
      // Handle duplicate key error specifically
      if (saveError.code === 11000) {
        if (saveError.keyValue && saveError.keyValue.DebtID === null) {
          return next(new AppError('Database index mismatch detected. Please contact system administrator to fix DebtID index.', 500));
        } else if (saveError.keyValue && saveError.keyValue.debtId) {
          return next(new AppError(`Debt ID ${saveError.keyValue.debtId} already exists. Please try again.`, 409));
        }
      }
      
      throw saveError; // Re-throw if not handled above
    }

    // Step 3: Create initial payment if provided
    let payment = null;
    if (paidAmount > 0) {
      try {
        console.log('💰 Creating initial payment record...');
        const paymentId = await generatePaymentId(Payment);
        
        payment = new Payment({
          paymentId,
          paymentContext: 'debt',
          debtId: debt.debtId,
          customerId: customer.customerId,
          shopId,
          amount: paidAmount,
          paymentDate: paidDate ? new Date(paidDate) : new Date(),
          paymentMethod
          // PaidAmount, PaidDate synced in pre-save
        });

        // Add timeout to payment save operation
        const paymentSavePromise = payment.save();
        const timeoutPromise = new Promise((_, reject) => {
          setTimeout(() => reject(new Error('Payment save timeout after 5 seconds')), 5000);
        });
        
        await Promise.race([paymentSavePromise, timeoutPromise]);
        console.log('✅ Payment record saved successfully');
        
        // Update debt with payment info
        console.log('🔄 Updating debt with payment info...');
        const debtUpdatePromise = debt.addPayment(paidAmount, payment.paymentDate);
        const debtTimeoutPromise = new Promise((_, reject) => {
          setTimeout(() => reject(new Error('Debt update timeout after 5 seconds')), 5000);
        });
        
        await Promise.race([debtUpdatePromise, debtTimeoutPromise]);
        console.log('✅ Debt updated with payment info');
      } catch (paymentError) {
        console.error('❌ Error creating payment:', paymentError.message);
        // Don't fail the debt creation if payment fails
        payment = null;
      }
    }

    // IMPORTANT: Set customer risk profile to Active Debt when new debt is created
    // Use valid enum values: 'Low Risk', 'Medium Risk', 'High Risk', 'Active Debt'
    if (customer.riskProfile.currentRiskLevel === 'Low Risk' || !customer.riskProfile.currentRiskLevel) {
      try {
        console.log('📊 Updating customer risk profile...');
        customer.riskProfile.currentRiskLevel = 'Active Debt'; // Business logic: new debts = Active Debt
        customer.riskProfile.riskScore = 75; // Active debt risk score
        customer.riskProfile.lastAssessment = new Date();
        customer.riskProfile.mlSource = 'system';
        
        // Add timeout to customer save operation
        const customerSavePromise = customer.save();
        const timeoutPromise = new Promise((_, reject) => {
          setTimeout(() => reject(new Error('Customer save timeout after 5 seconds')), 5000);
        });
        
        await Promise.race([customerSavePromise, timeoutPromise]);
        console.log('✅ Customer risk profile updated successfully');
      } catch (customerSaveError) {
        console.error('❌ Error updating customer risk profile:', customerSaveError.message);
        // Don't fail the debt creation if customer update fails
      }
    }

    // Step 4: Send Push Notification to Shop Admins (OPTIONAL WITH TIMEOUT)
    let notificationResult = null;
    const ENABLE_NOTIFICATIONS = process.env.ENABLE_DEBT_NOTIFICATIONS !== 'false'; // Default enabled
    
    if (ENABLE_NOTIFICATIONS) {
      try {
        console.log('📱 Sending debt creation notification...');
        
        // Add timeout to prevent hanging
        const notificationPromise = DebtNotificationService.sendDebtCreationNotification(
          debt, 
          customer, 
          {
            shopId: debt.shopId,
            skipRiskAssessment: true, // ✅ FIXED: Skip ML for new debts (no payment behavior yet)
            priority: 'normal'
          }
        );
        
        // Set 8-second timeout for notification (increased to handle Firebase init)
        const timeoutPromise = new Promise((_, reject) => {
          setTimeout(() => reject(new Error('Notification timeout after 8 seconds')), 8000);
        });
        
        notificationResult = await Promise.race([notificationPromise, timeoutPromise]);
        
        if (notificationResult.success) {
          console.log(`✅ Push notification sent successfully: ${notificationResult.notificationId}`);
        } else {
          console.log(`⚠️ Push notification failed: ${notificationResult.error}`);
        }
      } catch (notificationError) {
        const isTimeout = notificationError.message.includes('timeout');
        if (isTimeout) {
          console.log('⏰ Push notification timed out (but debt was created successfully)');
          console.log('   This is normal and doesn\'t affect debt creation');
        } else {
          console.error('❌ Push notification error:', notificationError.message);
        }
        // Don't fail the debt creation if notification fails
        notificationResult = {
          success: false,
          error: notificationError.message,
          timedOut: isTimeout
        };
      }
    } else {
      console.log('📱 Debt notifications disabled by environment variable');
      notificationResult = {
        success: false,
        error: 'Notifications disabled',
        disabled: true
      };
    }

    // Response data
    const responseData = {
      success: true,
      message: 'Debt created successfully using customer ID reference (best practice). Risk evaluation will begin after due date.',
      data: {
        debt: {
          debtId: debt.debtId,
          customerName: debt.CustomerName,
          customerType: debt.CustomerType,
          debtAmount: debt.DebtAmount,
          outstandingDebt: debt.OutstandingDebt,
          dueDate: debt.DueDate,
          repaymentTime: debt.RepaymentTime,
          status: debt.status,
          riskStatus: debt.RiskLevel, // "Active Debt" (no evaluation yet)
          description: debt.description
        },
        customer: {
          customerId: customer.customerId,
          name: customer.CustomerName,
          phone: customer.phone,
          type: customer.CustomerType
        },
        payment: payment ? {
          paymentId: payment.paymentId,
          amount: payment.amount,
          date: payment.paymentDate,
          method: payment.paymentMethod
        } : null,
        notification: notificationResult ? {
          sent: notificationResult.success,
          notificationId: notificationResult.notificationId,
          riskAssessment: notificationResult.riskAssessment,
          deliveryStats: notificationResult.deliveryStats,
          error: notificationResult.error || null,
          timedOut: notificationResult.timedOut || false,
          disabled: notificationResult.disabled || false
        } : null,
        mlInfo: {
          evaluationStatus: 'Pending',
          message: 'Risk status set to "Active Debt" (no evaluation yet). ML evaluation will trigger automatically after due date',
          dueDate: debt.DueDate,
          daysUntilDue: Math.ceil((new Date(debt.DueDate) - new Date()) / (1000 * 60 * 60 * 24))
        }
      }
    };

    res.status(201).json(responseData);

  } catch (error) {
    console.error('Create debt error:', error);
    return next(new AppError('Failed to create debt record', 500));
  }
};

module.exports = createDebt; 