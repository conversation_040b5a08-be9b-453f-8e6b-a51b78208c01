"use client";

/**
 * Settings Context - Hooks
 * Provides custom hooks for consuming the settings context
 */

import { useContext } from 'react';
import { SettingsContext } from './index';

/**
 * Custom hook to use settings context
 * Provides access to settings state and actions
 * 
 * @returns {Object} Settings context value
 * @throws {Error} If used outside of a SettingsProvider
 */
export function useSettings() {
  const context = useContext(SettingsContext);
  
  if (!context) {
    throw new Error("useSettings must be used within a SettingsProvider");
  }
  
  return context;
}
