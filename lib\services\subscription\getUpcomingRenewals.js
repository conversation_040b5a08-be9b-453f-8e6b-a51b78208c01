import apiBridge from '@/lib/api/bridge';
import BaseService from '../baseService';

const getUpcomingRenewals = async (params = {}) => {
  try {
    const response = await apiBridge.get(ENDPOINTS.subscription.upcomingRenewals, { params });
    
    return BaseService.processApiResponse(response);
  } catch (error) {
    throw BaseService.handleApiError(error);
  }
};

export default getUpcomingRenewals; 
