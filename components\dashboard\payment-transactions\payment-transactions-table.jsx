import React, { useState, useMemo } from 'react';
import { 
  Eye, 
  CheckCircle, 
  XCircle, 
  MoreVertical, 
  Clock,
  CreditCard,
  Building2,
  Calendar,
  DollarSign
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator
} from '@/components/ui/dropdown-menu';
import { DataTable } from '@/components/dashboard/common/data-table';
import PaymentTransactionsService from '@/lib/services/payment-transactions';

/**
 * PaymentTransactionsTable Component
 * Displays payment transactions in a comprehensive table format
 * 
 * @param {Object} props - Component props
 * @param {Array} props.transactions - Array of payment transactions
 * @param {boolean} props.loading - Loading state
 * @param {Object} props.pagination - Pagination information
 * @param {Function} props.onPageChange - Page change callback
 * @param {Function} props.onPageSizeChange - Page size change callback
 * @param {Function} props.onViewDetails - View details callback
 * @param {Function} props.onApprove - Approve transaction callback
 * @param {Function} props.onReject - Reject transaction callback
 * @returns {JSX.Element} Rendered component
 */
const PaymentTransactionsTable = ({
  transactions = [],
  loading = false,
  pagination = {},
  onPageChange,
  onPageSizeChange,
  onViewDetails,
  onApprove,
  onReject
}) => {
  const [selectedTransaction, setSelectedTransaction] = useState(null);

  // Table columns configuration
  const columns = useMemo(() => [
    {
      key: 'paymentInfo',
      header: 'Payment Details',
      cell: (rowData) => {
        const transaction = rowData?.row?.original || rowData;
        if (!transaction) return <div>-</div>;
        
        return (
          <div className="flex flex-col space-y-1">
            <div className="flex items-center gap-2">
              <CreditCard className="h-4 w-4 text-muted-foreground" />
              <span className="font-medium text-sm">
                {transaction.paymentId || 'N/A'}
              </span>
            </div>
            <div className="text-xs text-muted-foreground">
              {PaymentTransactionsService.formatPaymentMethod(transaction.method)}
            </div>
            {transaction.subscriptionId && (
              <div className="text-xs text-blue-600">
                Sub: {transaction.subscriptionId}
              </div>
            )}
          </div>
        );
      }
    },
    {
      key: 'shopInfo',
      header: 'Shop',
      cell: (rowData) => {
        const transaction = rowData?.row?.original || rowData;
        if (!transaction) return <div>-</div>;
        
        return (
          <div className="flex flex-col space-y-1">
            <div className="flex items-center gap-2">
              <Building2 className="h-4 w-4 text-muted-foreground" />
              <span className="font-medium text-sm">
                {transaction.shopName || 'Unknown Shop'}
              </span>
            </div>
            {transaction.customerName && (
              <div className="text-xs text-muted-foreground">
                Customer: {transaction.customerName}
              </div>
            )}
          </div>
        );
      }
    },
    {
      key: 'amount',
      header: 'Amount',
      cell: (rowData) => {
        const transaction = rowData?.row?.original || rowData;
        if (!transaction) return <div>-</div>;
        
        return (
          <div className="flex flex-col space-y-1">
            <div className="flex items-center gap-2">
              <DollarSign className="h-4 w-4 text-muted-foreground" />
              <span className="font-semibold">
                {PaymentTransactionsService.formatCurrency(transaction.amount)}
              </span>
            </div>
            {transaction.discountAmount > 0 && (
              <div className="text-xs text-green-600">
                Discount: {PaymentTransactionsService.formatCurrency(transaction.discountAmount)}
              </div>
            )}
            {transaction.originalAmount && transaction.originalAmount !== transaction.amount && (
              <div className="text-xs text-muted-foreground line-through">
                Original: {PaymentTransactionsService.formatCurrency(transaction.originalAmount)}
              </div>
            )}
          </div>
        );
      }
    },
    {
      key: 'status',
      header: 'Status',
      cell: (rowData) => {
        const transaction = rowData?.row?.original || rowData;
        if (!transaction) return null;
        
        const variant = PaymentTransactionsService.getStatusVariant(transaction.status);
        const statusText = PaymentTransactionsService.formatStatus(transaction.status);
        
        const getStatusIcon = (status) => {
          switch (status?.toLowerCase()) {
            case 'approved':
            case 'completed':
              return <CheckCircle className="h-3 w-3" />;
            case 'rejected':
            case 'failed':
              return <XCircle className="h-3 w-3" />;
            case 'pending':
            case 'processing':
            default:
              return <Clock className="h-3 w-3" />;
          }
        };
        
        return (
          <Badge variant={variant} className="flex items-center gap-1 w-fit">
            {getStatusIcon(transaction.status)}
            {statusText}
          </Badge>
        );
      }
    },
    {
      key: 'date',
      header: 'Date',
      cell: (rowData) => {
        const transaction = rowData?.row?.original || rowData;
        if (!transaction) return <div>-</div>;
        
        return (
          <div className="flex flex-col space-y-1">
            <div className="flex items-center gap-2">
              <Calendar className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm">
                {PaymentTransactionsService.formatDate(transaction.createdAt)}
              </span>
            </div>
            {transaction.approvedAt && (
              <div className="text-xs text-green-600">
                Approved: {PaymentTransactionsService.formatDate(transaction.approvedAt)}
              </div>
            )}
            {transaction.updatedAt && transaction.updatedAt !== transaction.createdAt && (
              <div className="text-xs text-muted-foreground">
                Updated: {PaymentTransactionsService.formatDate(transaction.updatedAt)}
              </div>
            )}
          </div>
        );
      }
    },
    {
      key: 'actions',
      header: 'Actions',
      cell: (rowData) => {
        const transaction = rowData?.row?.original || rowData;
        if (!transaction) return null;
        
        const isPending = transaction.status?.toLowerCase() === 'pending';
        const canApprove = isPending;
        const canReject = isPending;
        
        return (
          <div className="flex items-center justify-end">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button 
                  variant="ghost" 
                  className="h-8 w-8 p-0"
                  onClick={(e) => e.stopPropagation()}
                >
                  <span className="sr-only">Open menu</span>
                  <MoreVertical className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem 
                  onClick={(e) => {
                    e.stopPropagation();
                    onViewDetails?.(transaction);
                  }}
                >
                  <Eye className="mr-2 h-4 w-4" />
                  View Details
                </DropdownMenuItem>
                
                {canApprove && (
                  <>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem 
                      onClick={(e) => {
                        e.stopPropagation();
                        onApprove?.(transaction);
                      }}
                      className="text-green-600"
                    >
                      <CheckCircle className="mr-2 h-4 w-4" />
                      Approve Payment
                    </DropdownMenuItem>
                  </>
                )}
                
                {canReject && (
                  <DropdownMenuItem 
                    onClick={(e) => {
                      e.stopPropagation();
                      onReject?.(transaction);
                    }}
                    className="text-red-600"
                  >
                    <XCircle className="mr-2 h-4 w-4" />
                    Reject Payment
                  </DropdownMenuItem>
                )}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        );
      }
    }
  ], [onViewDetails, onApprove, onReject]);

  // Empty state component
  const emptyState = (
    <div className="text-center py-12">
      <div className="inline-flex items-center justify-center w-16 h-16 bg-muted rounded-full mb-4">
        <CreditCard className="h-8 w-8 text-muted-foreground" />
      </div>
      <h3 className="text-lg font-medium">No payment transactions found</h3>
      <p className="text-muted-foreground mt-1">
        No payment transactions match your current filters
      </p>
    </div>
  );

  return (
    <Card>
      <CardContent className="p-0">
        <DataTable
          columns={columns}
          data={transactions || []}
          pagination={true}
          currentPage={pagination?.page || 1}
          pageSize={pagination?.limit || 10}
          totalItems={pagination?.total || 0}
          totalPages={pagination?.totalPages || 1}
          onPageChange={onPageChange}
          onPageSizeChange={onPageSizeChange}
          isLoading={loading}
          emptyState={emptyState}
          onRowClick={onViewDetails}
        />
      </CardContent>
    </Card>
  );
};

export default PaymentTransactionsTable; 