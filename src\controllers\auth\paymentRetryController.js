/**
 * Payment Retry Controller
 * Handles payment retry for users with email_verified_pending_payment status
 */

const { User, Shop, Plan } = require('../../models');
const { AppError, logInfo, logError, logSuccess } = require('../../utils');
const EVCPaymentService = require('../../services/evcPaymentService');
const DiscountService = require('../../services/discountService');

/**
 * Check if user can retry payment
 * GET /api/auth/payment-retry/check/:userId
 */
const checkPaymentRetryEligibility = async (req, res, next) => {
  try {
    const { userId } = req.params;
    
    logInfo(`Checking payment retry eligibility for user: ${userId}`, 'paymentRetryController');
    
    // Get user with shop details
    const user = await User.findOne({ userId, isDeleted: false })
      .populate('shopId', 'shopName subscription');
    
    if (!user) {
      throw new AppError('User not found', 404, 'user_not_found');
    }
    
    // Check if user is eligible for payment retry
    const isEligible = user.status === 'email_verified_pending_payment' && 
                      user.emailVerified && 
                      !user.isPaid;
    
    let retryInfo = null;
    
    if (isEligible) {
      // Get the shop's subscription details
      const shop = user.shopId;
      if (shop && shop.subscription) {
        const subscription = shop.subscription;
        
        // Get plan details
        const plan = await Plan.findOne({ type: subscription.planType });
        
        retryInfo = {
          userId: user.userId,
          email: user.email,
          phone: user.phone,
          shopName: shop.shopName,
          planType: subscription.planType,
          planAmount: plan ? plan.pricing.basePrice : null,
          planDisplayName: plan ? plan.displayName : null,
          lastPaymentAttempt: subscription.payment ? subscription.payment.lastPaymentDate : null,
          paymentMethod: subscription.payment ? subscription.payment.method : null
        };
      }
    }
    
    res.json({
      success: true,
      eligible: isEligible,
      reason: isEligible ? 'User can retry payment' : 'User not eligible for payment retry',
      userStatus: user.status,
      emailVerified: user.emailVerified,
      isPaid: user.isPaid,
      retryInfo
    });
    
  } catch (error) {
    logError('Error checking payment retry eligibility', 'paymentRetryController', error);
    next(error);
  }
};

/**
 * Retry payment for eligible user
 * POST /api/auth/payment-retry/:userId
 */
const retryPayment = async (req, res, next) => {
  try {
    const { userId } = req.params;
    const { paymentMethod, phoneNumber, discountCode } = req.body;
    
    logInfo(`Processing payment retry for user: ${userId}`, 'paymentRetryController');
    
    // Get user with shop details
    const user = await User.findOne({ userId, isDeleted: false })
      .populate('shopId', 'shopName subscription');
    
    if (!user) {
      throw new AppError('User not found', 404, 'user_not_found');
    }
    
    // Validate user eligibility
    if (user.status !== 'email_verified_pending_payment') {
      throw new AppError('User not eligible for payment retry', 400, 'not_eligible');
    }
    
    if (!user.emailVerified) {
      throw new AppError('Email not verified', 400, 'email_not_verified');
    }
    
    if (user.isPaid) {
      throw new AppError('Payment already completed', 400, 'already_paid');
    }
    
    const shop = user.shopId;
    if (!shop || !shop.subscription) {
      throw new AppError('Shop subscription not found', 404, 'subscription_not_found');
    }
    
    // Get plan details
    const plan = await Plan.findOne({ type: shop.subscription.planType });
    if (!plan) {
      throw new AppError('Plan not found', 404, 'plan_not_found');
    }
    
    let finalAmount = plan.pricing.basePrice;
    let discountDetails = null;
    
    // Apply discount if provided
    if (discountCode) {
      discountDetails = await DiscountService.verifyDiscountCode(discountCode);
      if (!discountDetails.valid) {
        throw new AppError(
          discountDetails.message || 'Invalid discount code',
          400,
          'invalid_discount_code'
        );
      }
      
      finalAmount = discountDetails.discountDetails.finalAmount;
      logInfo(`Applied discount code ${discountCode} to retry payment`, 'paymentRetryController');
    }
    
    // Handle different payment methods
    if (paymentMethod === 'EVC Plus') {
      if (!phoneNumber) {
        throw new AppError('Phone number required for EVC Plus payment', 400, 'phone_required');
      }
      
      // Process EVC payment
      const paymentData = {
        phone: phoneNumber,
        amount: finalAmount,
        reference: `retry_${Date.now()}_${userId.substring(0, 5)}`,
        description: `${shop.shopName} subscription retry: ${plan.displayName}`,
        shopName: shop.shopName,
        shopId: shop.shopId
      };
      
      logInfo(`Initiating EVC Plus payment retry of $${finalAmount}`, 'paymentRetryController');
      
      const evcResponse = await EVCPaymentService.payByWaafiPay(paymentData);
      
      if (evcResponse && evcResponse.success) {
        // Update user and shop payment status
        user.isPaid = true;
        user.status = 'active';
        
        shop.subscription.initialPaid = true;
        shop.subscription.payment = {
          ...shop.subscription.payment,
          verified: true,
          lastPaymentDate: new Date(),
          transactionId: evcResponse.transactionId,
          method: paymentMethod,
          amount: finalAmount,
          retryCount: (shop.subscription.payment.retryCount || 0) + 1
        };
        
        await user.save();
        await shop.save();
        
        logSuccess(`Payment retry successful for user ${userId}`, 'paymentRetryController');
        
        res.json({
          success: true,
          message: 'Payment completed successfully',
          transactionId: evcResponse.transactionId,
          amount: finalAmount,
          user: {
            userId: user.userId,
            email: user.email,
            status: user.status,
            isPaid: user.isPaid
          }
        });
      } else {
        throw new AppError('Payment failed', 400, 'payment_failed');
      }
      
    } else if (paymentMethod === 'offline') {
      // For offline payments, just update status to pending verification
      shop.subscription.payment = {
        ...shop.subscription.payment,
        method: paymentMethod,
        amount: finalAmount,
        verified: false,
        lastPaymentDate: new Date(),
        retryCount: (shop.subscription.payment.retryCount || 0) + 1
      };
      
      await shop.save();
      
      logSuccess(`Offline payment retry recorded for user ${userId}`, 'paymentRetryController');
      
      res.json({
        success: true,
        message: 'Offline payment recorded. Please wait for verification.',
        amount: finalAmount,
        user: {
          userId: user.userId,
          email: user.email,
          status: user.status,
          isPaid: user.isPaid
        }
      });
    } else {
      throw new AppError('Invalid payment method', 400, 'invalid_payment_method');
    }
    
  } catch (error) {
    logError('Error processing payment retry', 'paymentRetryController', error);
    next(error);
  }
};

/**
 * Reset user to allow new registration (admin only)
 * DELETE /api/auth/payment-retry/:userId/reset
 */
const resetUserForNewRegistration = async (req, res, next) => {
  try {
    const { userId } = req.params;
    const { reason } = req.body;
    
    logInfo(`Resetting user ${userId} for new registration`, 'paymentRetryController');
    
    // Get user with shop details
    const user = await User.findOne({ userId, isDeleted: false })
      .populate('shopId', 'shopName');
    
    if (!user) {
      throw new AppError('User not found', 404, 'user_not_found');
    }
    
    // Only allow reset for users in payment pending state
    if (user.status !== 'email_verified_pending_payment') {
      throw new AppError('User not in payment pending state', 400, 'invalid_status');
    }
    
    // Reset user status
    user.status = 'pending';
    user.emailVerified = false;
    user.isPaid = false;
    
    await user.save();
    
    logSuccess(`User ${userId} reset for new registration`, 'paymentRetryController');
    
    res.json({
      success: true,
      message: 'User reset successfully. They can now register with the same email.',
      user: {
        userId: user.userId,
        email: user.email,
        status: user.status,
        emailVerified: user.emailVerified,
        isPaid: user.isPaid
      }
    });
    
  } catch (error) {
    logError('Error resetting user for new registration', 'paymentRetryController', error);
    next(error);
  }
};

module.exports = {
  checkPaymentRetryEligibility,
  retryPayment,
  resetUserForNewRegistration
}; 