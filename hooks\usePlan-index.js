/**
 * Plan Hooks Index
 * 
 * Centralized export for all plan-related hooks
 * 100% aligned with backend Plan API and business logic
 * 
 * USAGE GUIDE:
 * 
 * 1. For plan lists and management:
 *    import { usePlans } from '@/hooks/usePlan-index';
 * 
 * 2. For single plan operations:
 *    import { usePlan } from '@/hooks/usePlan-index';
 * 
 * 3. For plan statistics:
 *    import { usePlanStats } from '@/hooks/usePlan-index';
 * 
 * 4. For create/update/delete operations:
 *    import { usePlanMutations } from '@/hooks/usePlan-index';
 * 
 * 5. For form dropdowns and selection:
 *    import { usePlanSelection } from '@/hooks/usePlan-index';
 * 
 * 6. For everything (not recommended for performance):
 *    import * as PlanHooks from '@/hooks/usePlan-index';
 */

// Import all plan hooks
import { usePlans } from './usePlans';
import { usePlan } from './usePlan';
import { usePlanStats } from './usePlanStats';
import { usePlanMutations } from './usePlanMutations';
import { usePlanSelection } from './usePlanSelection';

// Re-export all hooks
export {
  usePlans,
  usePlan,
  usePlanStats,
  usePlanMutations,
  usePlanSelection
};

/**
 * Hook Usage Examples and Patterns
 */

// Example 1: Basic plan list management
/*
function PlansPage() {
  const {
    plans,
    loading,
    error,
    activePlans,
    refreshPlans,
    updateFilters
  } = usePlans({
    autoFetch: true,
    includeInactive: true, // SuperAdmin sees all
    showToastMessages: true
  });

  return (
    <div>
      {loading && <div>Loading plans...</div>}
      {error && <div>Error: {error}</div>}
      {plans.map(plan => (
        <div key={plan.planId}>{plan.displayName}</div>
      ))}
    </div>
  );
}
*/

// Example 2: Single plan view with operations
/*
function PlanDetailPage({ planId }) {
  const {
    plan,
    loading,
    error,
    updatePlan,
    toggleStatus,
    deletePlan
  } = usePlan(planId, {
    autoFetch: true,
    showToastMessages: true
  });

  const handleToggleStatus = () => {
    toggleStatus(!plan.isActive);
  };

  return (
    <div>
      {loading && <div>Loading plan...</div>}
      {plan && (
        <div>
          <h1>{plan.displayName}</h1>
          <button onClick={handleToggleStatus}>
            {plan.isActive ? 'Deactivate' : 'Activate'}
          </button>
        </div>
      )}
    </div>
  );
}
*/

// Example 3: Plan creation form
/*
function CreatePlanForm() {
  const {
    createPlan,
    isCreating,
    error
  } = usePlanMutations({
    showToastMessages: true,
    onPlanCreated: (plan) => {
      console.log('Plan created:', plan);
      // Navigate to plan details or refresh list
    }
  });

  const handleSubmit = async (formData) => {
    try {
      await createPlan({
        name: formData.name,
        type: formData.type,
        displayName: formData.displayName,
        description: formData.description,
        pricing: {
          basePrice: formData.price,
          currency: 'USD',
          billingCycle: formData.billingCycle
        }
      });
    } catch (err) {
      console.error('Failed to create plan:', err);
    }
  };

  return (
    <form onSubmit={handleSubmit}>
      // Form fields...
      <button type="submit" disabled={isCreating}>
        {isCreating ? 'Creating...' : 'Create Plan'}
      </button>
    </form>
  );
}
*/

// Example 4: Plan selection dropdown
/*
function PlanSelector({ onPlanSelect }) {
  const {
    plans,
    loading,
    selectOptions,
    selectPlan,
    selectedPlan
  } = usePlanSelection({
    autoFetch: true,
    filterTypes: ['monthly', 'yearly'], // Only show paid plans
    sortBy: 'price',
    onSelectionChange: onPlanSelect
  });

  return (
    <select
      value={selectedPlan?.value || ''}
      onChange={(e) => selectPlan(e.target.value)}
      disabled={loading}
    >
      <option value="">Select a plan...</option>
      {selectOptions.map(option => (
        <option key={option.value} value={option.value}>
          {option.label}
        </option>
      ))}
    </select>
  );
}
*/

// Example 5: Plan statistics dashboard
/*
function PlanStatsDashboard() {
  const {
    stats,
    loading,
    error,
    summary,
    planDetails,
    insights,
    refreshStats
  } = usePlanStats({
    autoFetch: true,
    enableAutoRefresh: true,
    refreshInterval: 300000 // 5 minutes
  });

  if (loading) return <div>Loading statistics...</div>;
  if (error) return <div>Error: {error}</div>;

  return (
    <div>
      <div className="summary-cards">
        <div>Total Plans: {summary.totalPlans}</div>
        <div>Active Plans: {summary.activePlans}</div>
        <div>Revenue: ${stats.totals.totalRevenue}</div>
      </div>
      
      <div className="plan-details">
        {planDetails.map(plan => (
          <div key={plan.planId}>
            <h3>{plan.displayName}</h3>
            <p>Subscriptions: {plan.metrics.activeSubscriptions}</p>
            <p>Revenue: ${plan.metrics.revenue}</p>
          </div>
        ))}
      </div>
    </div>
  );
}
*/

/**
 * Default export - comprehensive plan management hook
 * Combines multiple hooks for complex scenarios
 */
export function usePlanManagement(options = {}) {
  const {
    autoFetchPlans = true,
    autoFetchStats = false,
    includeInactive = true,
    showToastMessages = true,
    onDataChange
  } = options;

  // Use multiple hooks
  const plans = usePlans({
    autoFetch: autoFetchPlans,
    includeInactive,
    showToastMessages,
    onPlansChange: onDataChange
  });

  const stats = usePlanStats({
    autoFetch: autoFetchStats,
    showToastMessages: false // Silent for combined hook
  });

  const mutations = usePlanMutations({
    showToastMessages,
    onSuccess: () => {
      // Refresh data after mutations
      plans.refreshPlans(false);
      if (autoFetchStats) {
        stats.refreshStats(false);
      }
    }
  });

  const selection = usePlanSelection({
    autoFetch: false, // Controlled by plans hook
    showToastMessages: false
  });

  return {
    // Plans data and operations
    ...plans,
    
    // Statistics (prefixed to avoid conflicts)
    statsData: stats.stats,
    statsLoading: stats.loading,
    statsError: stats.error,
    refreshStats: stats.refreshStats,
    
    // Mutations (prefixed to avoid conflicts)
    ...mutations,
    
    // Selection utilities
    planOptions: selection.plans,
    selectOptions: selection.selectOptions,
    
    // Combined operations
    isLoading: plans.loading || stats.loading || mutations.isMutating,
    hasError: !!(plans.error || stats.error || mutations.error),
    
    // Refresh all data
    refreshAll: async () => {
      await Promise.all([
        plans.refreshPlans(false),
        autoFetchStats ? stats.refreshStats(false) : Promise.resolve()
      ]);
    }
  };
}

/**
 * Hook type definitions for TypeScript users
 * (These would be in separate .d.ts files in a TypeScript project)
 */

/*
interface PlanData {
  planId: string;
  name: string;
  type: 'trial' | 'monthly' | 'yearly';
  displayName: string;
  description?: string;
  pricing: {
    basePrice: number;
    currency: string;
    billingCycle: 'one-time' | 'monthly' | 'yearly';
    trialDays: number;
    setupFee: number;
  };
  features: {
    debtTracking: boolean;
    customerPayments: boolean;
    smsReminders: boolean;
    smartRiskScore: boolean;

    businessDashboard: boolean;
    exportReports: boolean;
    customerProfiles: boolean;

    offlineSupport: boolean;
  };
  limits: {
    maxProducts: number;
    maxEmployees: number;
    maxStorageMB: number;
    maxCustomers: number;
    maxDailyTransactions: number;
  };
  isActive: boolean;
  displayOrder: number;
  metadata: {
    isRecommended: boolean;
    tags: string[];
    customFields: Record<string, any>;
  };
  createdAt: string;
  updatedAt: string;
}

interface UsePlansReturn {
  plans: PlanData[];
  loading: boolean;
  error: string | null;
  // ... other properties
}
*/

export default usePlanManagement; 