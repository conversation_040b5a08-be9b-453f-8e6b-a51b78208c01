name: 🚀 Backup Deploy to VPS

on:
  workflow_dispatch:  # Manual trigger only
  
jobs:
  deploy:
    runs-on: ubuntu-latest
    
    steps:
      - name: 📥 Checkout Code
        uses: actions/checkout@v3

      - name: 🧪 Print Debug Info
        run: |
          echo "HOST: ${{ secrets.SSH_HOST }}"
          echo "USER: ${{ secrets.SSH_USER }}"
          echo "Current directory: $(pwd)"
          ssh -V

      - name: 🔐 Setup SSH Key
        run: |
          mkdir -p ~/.ssh
          cat > ~/.ssh/deploy_key <<EOF
          ${{ secrets.SSH_PRIVATE_KEY }}
          EOF
          chmod 600 ~/.ssh/deploy_key
          ssh-keyscan -H ${{ secrets.SSH_HOST }} >> ~/.ssh/known_hosts

      - name: 🧪 Test SSH Connection
        run: |
          ssh -i ~/.ssh/deploy_key -o StrictHostKeyChecking=no ${{ secrets.SSH_USER }}@${{ secrets.SSH_HOST }} 'echo "SSH connection successful"'

      - name: 📋 Create Deployment Script
        run: |
          cat > deploy_script.sh <<EOF
          #!/bin/bash
          set -e
          
          echo "=== Starting Deployment ==="
          echo "Current user: \$(whoami)"
          echo "Current directory: \$(pwd)"
          
          echo "=== Navigating to app directory ==="
          cd /var/www/deyncare-backend.khanciye.com
          pwd
          
          echo "=== Pulling latest code ==="
          git pull origin main
          
          echo "=== Installing dependencies ==="
          npm install --production
          
          echo "=== Restarting PM2 ==="
          pm2 restart all
          
          echo "=== Deployment completed ==="
          pm2 status
          EOF
          
          chmod +x deploy_script.sh

      - name: 🚀 Upload and Execute Deployment Script
        run: |
          # Upload the script
          scp -i ~/.ssh/deploy_key -o StrictHostKeyChecking=no deploy_script.sh ${{ secrets.SSH_USER }}@${{ secrets.SSH_HOST }}:/tmp/deploy_script.sh
          
          # Execute the script
          ssh -i ~/.ssh/deploy_key -o StrictHostKeyChecking=no ${{ secrets.SSH_USER }}@${{ secrets.SSH_HOST }} 'bash /tmp/deploy_script.sh'
          
          # Clean up
          ssh -i ~/.ssh/deploy_key -o StrictHostKeyChecking=no ${{ secrets.SSH_USER }}@${{ secrets.SSH_HOST }} 'rm -f /tmp/deploy_script.sh' 