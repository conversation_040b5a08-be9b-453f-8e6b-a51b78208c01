import apiBridge from '@/lib/api/bridge';
import { ENDPOINTS } from '@/lib/api/contract';
import { handleError, handleSuccess, validateRequiredFields, processApiResponse } from '../baseService';

/**
 * Create a new shop via SuperAdmin API
 * @param {Object} shopData - Shop data to create
 * @param {string} shopData.fullName - Owner full name (required)
 * @param {string} shopData.email - Owner email (required)
 * @param {string} shopData.phone - Owner phone (required)
 * @param {string} shopData.shopName - Shop name (required)
 * @param {string} shopData.shopAddress - Shop address (required)
 * @param {string} shopData.planType - Plan type (required)
 * @param {File} shopData.shopLogo - Shop logo file (optional)
 * @returns {Promise<Object>} Created shop object
 * @note Password is auto-generated by the backend for SuperAdmin registrations
 */
async function createShop(shopData) {
  try {
    console.log('🚀 [ShopService] Creating shop with data:', JSON.stringify(shopData, null, 2));

    // If no planType provided, try to get a default plan
    if (!shopData.planType) {
      console.log('⚠️ [ShopService] No planType provided, attempting to get default plan...');
      try {
        const PlanService = await import('../plan');
        const plansResponse = await PlanService.default.getPlans({ includeInactive: false });

        if (plansResponse.success && plansResponse.data && plansResponse.data.length > 0) {
          // Use the first active plan as default
          const defaultPlan = plansResponse.data[0];
          shopData.planType = defaultPlan.planId || defaultPlan._id;
          console.log('✅ [ShopService] Set default planType:', shopData.planType);
        } else {
          console.log('⚠️ [ShopService] No active plans found, using fallback');
          shopData.planType = 'monthly'; // Fallback to monthly
        }
      } catch (planError) {
        console.error('❌ [ShopService] Error getting default plan:', planError);
        shopData.planType = 'monthly'; // Fallback to monthly
      }
    }

    // Validate required fields for SuperAdmin shop creation (NO PASSWORD NEEDED)
    const requiredFields = [
      'fullName',
      'email',
      'phone',
      'shopName',
      'shopAddress',
      'planType'
    ];
    
    // Add business details validation if provided
    if (shopData.businessDetails) {
      if (!shopData.businessDetails.type) {
        requiredFields.push('businessDetails.type');
      }
      if (!shopData.businessDetails.category) {
        requiredFields.push('businessDetails.category');
      }
    }
    
    const validation = validateRequiredFields(shopData, requiredFields);
    
    if (!validation.isValid) {
      const errorMessage = `Missing required fields: ${validation.missingFields.join(', ')}`;
      handleError({ message: errorMessage }, 'SuperAdminShopService.createShop', true);
      throw new Error(errorMessage);
    }
    
    // Prepare data for SuperAdmin API (matches backend exactly)
    const requestData = {
      // User data (SAME field names as backend expects)
      fullName: shopData.fullName,
      email: shopData.email,
      phone: shopData.phone,
      // NO PASSWORD - backend auto-generates for SuperAdmin registrations
      // Shop data (SAME field names as backend expects)
      shopName: shopData.shopName,
      shopAddress: shopData.shopAddress,
      // Business details (REQUIRED for SuperAdmin registrations)
      businessDetails: shopData.businessDetails || {
        type: 'retail',
        category: 'general_store'
      },
      // Plan type (REQUIRED for SuperAdmin registrations)
      planType: shopData.planType || 'monthly' // Default to monthly if not provided
    };

    // Validate required fields before sending
    if (!requestData.planType) {
      throw new Error('Plan type is required for shop creation');
    }

    console.log('📤 [ShopService] Sending request data:', JSON.stringify(requestData, null, 2));
    
    let response;
    
    // Handle logo upload if provided
    if (shopData.shopLogo instanceof File) {
      
      const formData = new FormData();
      Object.keys(requestData).forEach(key => {
        if (key === 'businessDetails' && typeof requestData[key] === 'object') {
          // Handle nested businessDetails object properly
          formData.append(key, JSON.stringify(requestData[key]));
        } else {
          formData.append(key, requestData[key]);
        }
      });
      formData.append('shopLogo', shopData.shopLogo);
      
      response = await apiBridge.post(ENDPOINTS.SHOPS.BASE, formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        },
        clearCacheEndpoint: ENDPOINTS.SHOPS.BASE
      });
    } else {
      
      response = await apiBridge.post(ENDPOINTS.SHOPS.BASE, requestData, {
        clearCacheEndpoint: ENDPOINTS.SHOPS.BASE
      });
    }
    
    // Process response using utility
    const result = processApiResponse(response, 'Shop created successfully');
    
    if (result && result.shop) {
      // Map response to frontend format
      return {
        id: result.shop.shopId,
        shopId: result.shop.shopId,
        shopName: result.shop.shopName,
        shopAddress: result.shop.address,
        ownerName: result.shop.ownerName,
        email: result.shop.email,
        phone: result.shop.phone,
        status: result.shop.status,
        verified: result.shop.verified,
        logoUrl: result.shop.logoUrl,
        registeredBy: result.shop.registeredBy,
        planType: result.shop.planType,
        owner: result.owner,
        createdAt: new Date().toISOString()
      };
    }
    
    return result;
  } catch (error) {
    // Enhanced error handling for SuperAdmin operations
    if (error.response?.status === 400 && error.response?.data?.message) {
      const errorMessage = `Validation error: ${error.response.data.message}`;
      handleError({ message: errorMessage }, 'SuperAdminShopService.createShop', true);
      throw new Error(errorMessage);
    } else if (error.response?.status === 401) {
      const errorMessage = 'Unauthorized: SuperAdmin access required';
      handleError({ message: errorMessage }, 'SuperAdminShopService.createShop', true);
      throw new Error(errorMessage);
    } else if (error.response?.status === 403) {
      const errorMessage = 'Forbidden: Only SuperAdmins can create shops';
      handleError({ message: errorMessage }, 'SuperAdminShopService.createShop', true);
      throw new Error(errorMessage);
    } else if (error.response?.status === 409) {
      const errorMessage = 'Conflict: Email already exists';
      handleError({ message: errorMessage }, 'SuperAdminShopService.createShop', true);
      throw new Error(errorMessage);
    } else {
      handleError(error, 'SuperAdminShopService.createShop', true);
      throw error; // Re-throw the original error
    }
  }
}

export default createShop;
