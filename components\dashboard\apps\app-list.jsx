"use client";

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { 
  Download, 
  Edit, 
  Trash2, 
  MoreVertical, 
  Star, 
  Search,
  Filter,
  RefreshCw,
  AlertCircle
} from 'lucide-react';
import { useAppsList, useAppManagement } from '@/hooks/use-app-upload';

const platforms = [
  { value: '', label: 'All Platforms' },
  { value: 'android', label: 'Android' },
  { value: 'ios', label: 'iOS' },
  { value: 'windows', label: 'Windows' },
  { value: 'mac', label: 'macOS' },
  { value: 'web', label: 'Web' }
];

export default function AppList({ onEdit }) {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedPlatform, setSelectedPlatform] = useState('');
  const [showLatestOnly, setShowLatestOnly] = useState(false);

  const { 
    apps, 
    loading, 
    error, 
    pagination, 
    updateFilters, 
    changePage, 
    refreshApps 
  } = useAppsList({
    search: searchTerm,
    platform: selectedPlatform,
    isLatest: showLatestOnly || undefined
  });

  const { 
    loading: managing, 
    error: managementError, 
    deleteApp, 
    setLatestVersion, 
    downloadApp 
  } = useAppManagement();

  const handleSearch = (term) => {
    setSearchTerm(term);
    updateFilters({ search: term, page: 1 });
  };

  const handlePlatformFilter = (platform) => {
    setSelectedPlatform(platform);
    updateFilters({ platform: platform || undefined, page: 1 });
  };

  const handleLatestFilter = (latestOnly) => {
    setShowLatestOnly(latestOnly);
    updateFilters({ isLatest: latestOnly || undefined, page: 1 });
  };

  const handleDelete = async (uploadId) => {
    try {
      await deleteApp(uploadId);
      refreshApps();
    } catch (error) {
      console.error('Failed to delete app:', error);
    }
  };

  const handleSetLatest = async (uploadId, platform) => {
    try {
      await setLatestVersion(uploadId, platform);
      refreshApps();
    } catch (error) {
      console.error('Failed to set latest version:', error);
    }
  };

  const handleDownload = async (uploadId) => {
    try {
      await downloadApp(uploadId);
    } catch (error) {
      console.error('Failed to download app:', error);
    }
  };

  const formatFileSize = (bytes) => {
    if (!bytes) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return `${parseFloat((bytes / Math.pow(k, i)).toFixed(2))} ${sizes[i]}`;
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getPlatformBadgeColor = (platform) => {
    const colors = {
      android: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
      ios: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200',
      windows: 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200',
      mac: 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200',
      web: 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200'
    };
    return colors[platform] || 'bg-gray-100 text-gray-800';
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>App Versions</CardTitle>
            <CardDescription>
              Manage uploaded application files and versions
            </CardDescription>
          </div>
          <Button onClick={refreshApps} disabled={loading} size="sm">
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Filters */}
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              placeholder="Search apps..."
              value={searchTerm}
              onChange={(e) => handleSearch(e.target.value)}
              className="pl-10"
            />
          </div>
          
          <Select value={selectedPlatform} onValueChange={handlePlatformFilter}>
            <SelectTrigger className="w-full sm:w-48">
              <Filter className="h-4 w-4 mr-2" />
              <SelectValue placeholder="Filter by platform" />
            </SelectTrigger>
            <SelectContent>
              {platforms.map((platform) => (
                <SelectItem key={platform.value} value={platform.value}>
                  {platform.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          
          <div className="flex items-center space-x-2">
            <input
              id="latest-only"
              type="checkbox"
              checked={showLatestOnly}
              onChange={(e) => handleLatestFilter(e.target.checked)}
              className="rounded border-gray-300"
            />
            <label htmlFor="latest-only" className="text-sm font-medium">
              Latest only
            </label>
          </div>
        </div>

        {/* Error Display */}
        {(error || managementError) && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              {error || managementError}
            </AlertDescription>
          </Alert>
        )}

        {/* Apps Table */}
        <div className="border rounded-lg overflow-hidden">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>App Details</TableHead>
                <TableHead>Platform</TableHead>
                <TableHead>Version</TableHead>
                <TableHead>Size</TableHead>
                <TableHead>Downloads</TableHead>
                <TableHead>Upload Date</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {loading ? (
                <TableRow>
                  <TableCell colSpan={7} className="text-center py-8">
                    <div className="flex items-center justify-center">
                      <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                      Loading apps...
                    </div>
                  </TableCell>
                </TableRow>
              ) : apps.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={7} className="text-center py-8 text-gray-500">
                    No apps found
                  </TableCell>
                </TableRow>
              ) : (
                apps.map((app) => (
                  <TableRow key={app.uploadId}>
                    <TableCell>
                      <div className="space-y-1">
                        <div className="font-medium flex items-center gap-2">
                          {app.appName}
                          {app.isLatest && (
                            <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                          )}
                        </div>
                        <div className="text-sm text-gray-500">
                          Build {app.buildNumber}
                        </div>
                      </div>
                    </TableCell>
                    
                    <TableCell>
                      <Badge className={getPlatformBadgeColor(app.platform)}>
                        {app.platform}
                      </Badge>
                    </TableCell>
                    
                    <TableCell className="font-mono text-sm">
                      {app.version}
                    </TableCell>
                    
                    <TableCell className="text-sm">
                      {formatFileSize(app.fileSize)}
                    </TableCell>
                    
                    <TableCell className="text-sm">
                      {app.downloadCount || 0}
                    </TableCell>
                    
                    <TableCell className="text-sm">
                      {formatDate(app.createdAt)}
                    </TableCell>
                    
                    <TableCell>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="sm" disabled={managing}>
                            <MoreVertical className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem onClick={() => handleDownload(app.uploadId)}>
                            <Download className="h-4 w-4 mr-2" />
                            Download
                          </DropdownMenuItem>
                          
                          {onEdit && (
                            <DropdownMenuItem onClick={() => onEdit(app)}>
                              <Edit className="h-4 w-4 mr-2" />
                              Edit
                            </DropdownMenuItem>
                          )}
                          
                          {!app.isLatest && (
                            <DropdownMenuItem 
                              onClick={() => handleSetLatest(app.uploadId, app.platform)}
                            >
                              <Star className="h-4 w-4 mr-2" />
                              Set as Latest
                            </DropdownMenuItem>
                          )}
                          
                          <AlertDialog>
                            <AlertDialogTrigger asChild>
                              <DropdownMenuItem onSelect={(e) => e.preventDefault()}>
                                <Trash2 className="h-4 w-4 mr-2" />
                                Delete
                              </DropdownMenuItem>
                            </AlertDialogTrigger>
                            <AlertDialogContent>
                              <AlertDialogHeader>
                                <AlertDialogTitle>Delete App Version</AlertDialogTitle>
                                <AlertDialogDescription>
                                  Are you sure you want to delete {app.appName} v{app.version}? 
                                  This action cannot be undone.
                                </AlertDialogDescription>
                              </AlertDialogHeader>
                              <AlertDialogFooter>
                                <AlertDialogCancel>Cancel</AlertDialogCancel>
                                <AlertDialogAction
                                  onClick={() => handleDelete(app.uploadId)}
                                  className="bg-red-600 hover:bg-red-700"
                                >
                                  Delete
                                </AlertDialogAction>
                              </AlertDialogFooter>
                            </AlertDialogContent>
                          </AlertDialog>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </div>

        {/* Pagination */}
        {pagination.totalPages > 1 && (
          <div className="flex items-center justify-between">
            <div className="text-sm text-gray-500">
              Showing {((pagination.page - 1) * pagination.limit) + 1} to{' '}
              {Math.min(pagination.page * pagination.limit, pagination.totalDocs)} of{' '}
              {pagination.totalDocs} apps
            </div>
            
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => changePage(pagination.page - 1)}
                disabled={pagination.page <= 1 || loading}
              >
                Previous
              </Button>
              
              <Button
                variant="outline"
                size="sm"
                onClick={() => changePage(pagination.page + 1)}
                disabled={pagination.page >= pagination.totalPages || loading}
              >
                Next
              </Button>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
} 