const axios = require('axios');
const { Setting } = require('../models');
const riskScoreService = require('./riskScoreService');

class MLRiskService {
  constructor() {
    // Initialize with new Render deployment URL
    this.mlApiUrl = process.env.ML_API_URL || 'https://deyncare-ml-88ga.onrender.com';
    this.mlApiKey = process.env.ML_API_KEY || '';
    this.mlPredictEndpoint = '/predict_single/';
    this.mlKeepAliveEndpoint = '/keep_alive/';
    this.mlHealthEndpoint = '/health';
    this.mlTimeout = 30000; // 30 seconds for Render cold starts
    this.mlEnabled = false;
    
    // Cache for settings (refreshed periodically)
    this.settingsCache = {};
    this.lastSettingsUpdate = null;
    this.settingsCacheExpiry = 5 * 60 * 1000; // 5 minutes
    
    // Keep-alive mechanism for Render
    this.lastKeepAlive = null;
    this.keepAliveInterval = 10 * 60 * 1000; // 10 minutes
    this.startKeepAlive();
  }

  /**
   * Keep-alive mechanism to prevent Render from sleeping
   */
  startKeepAlive() {
    // Only start if ML is enabled and using Render
    if (this.mlApiUrl.includes('onrender.com')) {
      setInterval(async () => {
        try {
          await this.pingKeepAlive();
        } catch (error) {
          console.log('🔄 Keep-alive ping failed (expected if ML is disabled):', error.message);
        }
      }, this.keepAliveInterval);
      
      console.log('🔄 Keep-alive mechanism started for Render deployment');
    }
  }

  /**
   * Ping the keep-alive endpoint to prevent Render sleeping
   */
  async pingKeepAlive() {
    try {
      const isEnabled = await this.isMLEnabled();
      if (!isEnabled) return;

      const baseUrl = this.mlApiUrl.endsWith('/') ? this.mlApiUrl.slice(0, -1) : this.mlApiUrl;
      const keepAliveUrl = `${baseUrl}${this.mlKeepAliveEndpoint}`;
      
      const response = await axios.get(keepAliveUrl, {
        timeout: 10000, // 10 second timeout for keep-alive
        headers: { 'User-Agent': 'DeynCare-Backend-KeepAlive/1.0' }
      });
      
      this.lastKeepAlive = new Date();
      console.log(`🔄 Keep-alive ping successful: ${response.data.status} at ${response.data.timestamp}`);
      
    } catch (error) {
      console.log(`⚠️ Keep-alive ping failed: ${error.message}`);
    }
  }

  /**
   * Load ML settings from database
   */
  async loadMLSettings() {
    try {
      const now = new Date();
      
      // Check if cache is still valid
      if (this.lastSettingsUpdate && 
          (now - this.lastSettingsUpdate) < this.settingsCacheExpiry) {
        return this.settingsCache;
      }

      // Fetch all ML settings from database
      const mlSettings = await Setting.find({
        category: 'ml',
        shopId: null // Global settings only
      }).lean();

      // Convert to cache object
      const settingsMap = {};
      mlSettings.forEach(setting => {
        settingsMap[setting.key] = setting.value;
      });

      // Update cache
      this.settingsCache = settingsMap;
      this.lastSettingsUpdate = now;

      // Update instance properties with fresh settings
      this.mlEnabled = settingsMap.ml_enabled || false;
      this.mlApiUrl = settingsMap.ml_api_base_url || process.env.ML_API_URL || 'https://deyncare-ml-88ga.onrender.com';
      this.mlApiKey = settingsMap.ml_api_key || process.env.ML_API_KEY || ''; // Optional - not enforced by FastAPI
      this.mlPredictEndpoint = settingsMap.ml_predict_endpoint || '/predict_single/';
      this.mlTimeout = (settingsMap.ml_prediction_timeout || 30) * 1000; // Convert to milliseconds (30s for Render cold starts)

      console.log('🔄 ML Settings loaded from database:', {
        enabled: this.mlEnabled,
        apiUrl: this.mlApiUrl,
        endpoint: this.mlPredictEndpoint,
        timeout: this.mlTimeout / 1000 + 's',
        platform: this.mlApiUrl.includes('onrender.com') ? 'Render' : 'Other',
        keepAliveActive: this.mlApiUrl.includes('onrender.com')
      });

      return this.settingsCache;

    } catch (error) {
      console.error('❌ Failed to load ML settings from database:', error.message);
      
      // Fall back to environment variables with new Render URL
      this.mlEnabled = process.env.ML_ENABLED === 'true';
      this.mlApiUrl = process.env.ML_API_URL || 'https://deyncare-ml-88ga.onrender.com';
      this.mlApiKey = process.env.ML_API_KEY || ''; // Optional - not enforced by FastAPI
      this.mlPredictEndpoint = process.env.ML_PREDICT_ENDPOINT || '/predict_single/';
      this.mlTimeout = parseInt(process.env.ML_TIMEOUT || '30') * 1000;
      
      console.log('⚠️ Using fallback environment variables for ML settings');
      return {};
    }
  }

  /**
   * Check if ML is enabled globally
   */
  async isMLEnabled() {
    await this.loadMLSettings();
    return this.mlEnabled;
  }

  /**
   * Map ML API risk levels to database enum values
   * ML API returns: "Low", "Medium", "High"
   * Database expects: "Low Risk", "Medium Risk", "High Risk"
   */
  mapMLRiskLevelToDatabase(mlRiskLevel) {
    const riskMapping = {
      'Low': 'Low Risk',
      'Medium': 'Medium Risk', 
      'High': 'High Risk'
    };
    
    return riskMapping[mlRiskLevel] || 'Medium Risk'; // Default fallback
  }

  /**
   * Step 3-5: ML Risk Evaluation Pipeline
   * - Prepares data for ML model
   * - Calls ML API for prediction
   * - Classifies risk category
   */
  async evaluateRisk(debt, customer) {
    try {
      // Check if ML is enabled globally
      const isEnabled = await this.isMLEnabled();
      if (!isEnabled) {
        console.log('⚠️ ML is disabled globally, using fallback assessment');
        return this.fallbackRiskAssessment(debt, customer);
      }

      // Simple ML Triggering Logic: Skip ML for early payments
      const shouldTriggerML = this.shouldTriggerMLEvaluation(debt);
      if (!shouldTriggerML.trigger) {
        console.log(`🎯 Skipping ML: ${shouldTriggerML.reason}`);
        return this.optimisticRiskAssessment(debt, customer, shouldTriggerML.reason);
      }

      // Load fresh settings
      await this.loadMLSettings();

      // Step 3: Prepare ML payload (updated for new API)
      const mlPayload = this.prepareMLPayload(debt, customer);
      
      // Step 4: Get ML prediction
      const prediction = await this.callMLAPI(mlPayload);
      
      // Step 5: Parse new API response format
      const probabilityOnTime = prediction.probability_on_time; // New API format
      const mlRiskLevel = prediction.risk_level; // New API format: 'Low', 'Medium', 'High'
      
      // Convert ML API risk level to database enum value
      const databaseRiskLevel = this.mapMLRiskLevelToDatabase(mlRiskLevel);
      
      // Convert probability to risk score (inverse relationship)
      // High probability of on-time payment = Low risk score
      const riskScore = Math.round((1 - probabilityOnTime) * 100);
      
      const riskResult = {
        riskLevel: databaseRiskLevel, // Use mapped database enum value
        riskScore: Math.min(riskScore, 100), // Ensure ≤ 100
        confidence: 0.85, // Default confidence
        factors: this.identifyRiskFactors(debt),
        mlPayload: mlPayload,
        rawPrediction: {
          ...prediction,
          probability_on_time: probabilityOnTime,
          ml_predicted_level: mlRiskLevel,
          database_mapped_level: databaseRiskLevel,
          platform: 'Render'
        },
        source: 'ml_api'
      };
      
      // Update customer risk profile
      try {
        await riskScoreService.updateCustomerRiskScore(customer.customerId, {
          riskScore: riskResult.riskScore,
          riskLevel: riskResult.riskLevel,
          source: 'ml_api'
        });
        console.log(`✅ Updated risk score for customer ${customer.customerId}: ${riskResult.riskScore} (${riskResult.riskLevel})`);
      } catch (error) {
        console.error(`❌ Failed to update customer risk score: ${error.message}`);
        // Don't throw error - continue with ML evaluation response
      }
      
      return riskResult;

    } catch (error) {
      console.error('ML Risk Evaluation Error:', error);
      
      // Fallback to rule-based assessment
      return this.fallbackRiskAssessment(debt, customer);
    }
  }

  /**
   * Simple ML Triggering Logic
   * Rule: Only evaluate debts with actual payment behavior
   */
  shouldTriggerMLEvaluation(debt) {
    const now = new Date();
    const dueDate = new Date(debt.DueDate);
    const daysSinceDue = Math.floor((now - dueDate) / (1000 * 60 * 60 * 24));
    const isEarlyPayment = debt.PaymentDelay < 0;
    const isPastDue = now > dueDate;
    const hasPaymentHistory = debt.DebtPaidRatio > 0;

    // ✅ BUSINESS RULE: Skip ML for brand new debts with no payment behavior
    if (!isPastDue && !hasPaymentHistory) {
      return {
        trigger: false,
        reason: `New debt created ${Math.abs(daysSinceDue)} days before due date - no payment behavior to evaluate yet`
      };
    }

    // Simple Rule: Early Payment (any amount) = Skip ML
    if (isEarlyPayment) {
      return {
        trigger: false,
        reason: `Early payment (${Math.abs(debt.PaymentDelay)} days early) - handled by normal business logic`
      };
    }

    // All other cases (on-time or late) = Trigger ML (keep existing logic)
    if (debt.PaymentDelay > 0) {
      return {
        trigger: true,
        reason: `Late payment (${debt.PaymentDelay} days) - ML risk assessment required`
      };
    }

    // On-time payment with actual payment behavior
    if (hasPaymentHistory) {
      return {
        trigger: true,
        reason: 'Payment recorded - ML evaluation for risk assessment'
      };
    }

    // Default: no ML for debts without payment behavior
    return {
      trigger: false,
      reason: 'No payment behavior recorded yet - ML evaluation not applicable'
    };
  }

  /**
   * Optimistic risk assessment for early payments
   * Early payments indicate good customer behavior
   */
  optimisticRiskAssessment(debt, customer, reason) {
    // Early payments get favorable risk scores
    let riskScore = 5; // Start with very low risk (5%)
    let riskLevel = 'Low Risk';

    // Adjust based on payment completeness
    if (debt.DebtPaidRatio >= 1.0) {
      riskScore = 5; // Full early payment = excellent (5%)
      riskLevel = 'Low Risk';
    } else if (debt.DebtPaidRatio >= 0.5) {
      riskScore = 15; // Partial but significant early payment (15%)
      riskLevel = 'Low Risk';
    } else {
      riskScore = 25; // Small early payment (25%)
      riskLevel = 'Low Risk';
    }

    const result = {
      riskLevel: riskLevel,
      riskScore: riskScore,
      confidence: 0.90, // High confidence for early payments
      factors: ['Early payment behavior', 'Good customer conduct'],
      mlPayload: this.prepareMLPayload(debt, customer),
      rawPrediction: { 
        risk_score: riskScore / 100, 
        source: 'system',
        reason: reason
      },
      source: 'system'
    };

    // Update customer risk profile
    try {
      const riskScoreService = require('./riskScoreService');
      riskScoreService.updateCustomerRiskScore(customer.customerId, {
        riskScore: result.riskScore,
        riskLevel: result.riskLevel,
        source: 'system'
      });
      console.log(`✅ Updated risk score for customer ${customer.customerId}: ${result.riskScore} (${result.riskLevel}) - System Logic`);
    } catch (error) {
      console.error(`❌ Failed to update customer risk score: ${error.message}`);
    }

    return result;
  }

  /**
   * Step 3: Prepare data for ML model (New Render API format)
   * New API expects 5 fields including CustomerType
   */
  prepareMLPayload(debt, customer) {
    return {
      DebtPaidRatio: debt.DebtPaidRatio,
      PaymentDelay: debt.PaymentDelay,
      OutstandingDebt: debt.OutstandingDebt,
      DebtAmount: debt.DebtAmount,
      CustomerType: debt.CustomerType || customer.CustomerType || 'Individual' // Added for new API
    };
  }

  /**
   * Step 4: Call ML API for prediction with Render cold start handling
   */
  async callMLAPI(features) {
    try {
      // Ensure we have the latest settings
      await this.loadMLSettings();
      
      // Build the full API URL - ensure no double slashes
      const baseUrl = this.mlApiUrl.endsWith('/') ? this.mlApiUrl.slice(0, -1) : this.mlApiUrl;
      const endpoint = this.mlPredictEndpoint.startsWith('/') ? this.mlPredictEndpoint : `/${this.mlPredictEndpoint}`;
      const fullUrl = `${baseUrl}${endpoint}`;
      
      console.log(`🔗 Calling ML API: ${fullUrl}`);
      console.log(`⏱️ Timeout set to: ${this.mlTimeout / 1000}s (handles Render cold starts)`);
      console.log(`📤 Payload:`, features);
      
      // Prepare headers
      const headers = {
        'Content-Type': 'application/json',
        'User-Agent': 'DeynCare-Backend/1.0'
      };
      
      // API key not required - FastAPI doesn't enforce it currently
      // if (this.mlApiKey) {
      //   headers['X-API-KEY'] = this.mlApiKey;
      // }

      const startTime = Date.now();
      const response = await axios.post(fullUrl, features, {
        headers,
        timeout: this.mlTimeout
      });

      const duration = Date.now() - startTime;
      console.log(`✅ ML API response received successfully in ${duration}ms`);
      console.log(`📥 Response:`, response.data);
      
      // Log if it was likely a cold start
      if (duration > 15000) {
        console.log(`🥶 Cold start detected: ${duration}ms (Render free tier wakeup)`);
      }
      
      return response.data;

    } catch (error) {
      if (error.code === 'ECONNREFUSED') {
        throw new Error('ML API service unavailable - connection refused');
      }
      if (error.code === 'ENOTFOUND') {
        throw new Error('ML API service unavailable - host not found');
      }
      if (error.code === 'ECONNABORTED') {
        const duration = this.mlTimeout / 1000;
        throw new Error(`ML API timeout after ${duration}s - likely Render cold start (free tier sleeps after 15min)`);
      }
      if (error.response) {
        console.error('ML API Error Response:', error.response.data);
        throw new Error(`ML API error: ${error.response.status} - ${error.response.data?.detail || error.response.data?.message || 'Unknown error'}`);
      }
      throw new Error(`ML API error: ${error.message}`);
    }
  }

  /**
   * Step 5: Classify risk category based on ML score
   * Risk Score Mapping:
   * - ≤ 0.3 = Low Risk ✅
   * - 0.3-0.6 = Medium Risk ⚠️
   * - > 0.6 = High Risk ❌
   */
  classifyRisk(riskScore) {
    if (riskScore <= 0.3) {
      return {
        category: 'Low Risk',
        emoji: '✅',
        description: 'Customer has good payment behavior'
      };
    } else if (riskScore <= 0.6) {
      return {
        category: 'Medium Risk',
        emoji: '⚠️',
        description: 'Customer shows moderate risk factors'
      };
    } else {
      return {
        category: 'High Risk',
        emoji: '❌',
        description: 'Customer has high probability of default'
      };
    }
  }

  /**
   * Identify risk factors for explanation
   */
  identifyRiskFactors(debt) {
    const factors = [];

    if (debt.DebtPaidRatio < 0.5) {
      factors.push('Low payment ratio');
    }
    
    if (debt.PaymentDelay > 7) {
      factors.push('Significant payment delay');
    }
    
    if (debt.OutstandingDebt > debt.DebtAmount * 0.8) {
      factors.push('High outstanding balance');
    }
    
    if (debt.CustomerType === 'New') {
      factors.push('New customer');
    }

    return factors.length > 0 ? factors : ['Standard risk assessment'];
  }

  /**
   * Fallback risk assessment when ML API is unavailable
   */
  fallbackRiskAssessment(debt, customer) {
    let riskScore = 0;

    // Factor 1: Payment ratio (40% weight)
    const paymentRatio = debt.DebtPaidRatio;
    riskScore += (1 - paymentRatio) * 40;

    // Factor 2: Payment delay (30% weight)
    const delayDays = Math.max(0, debt.PaymentDelay);
    riskScore += Math.min(delayDays / 30, 1) * 30;

    // Factor 3: Customer type (20% weight)
    if (debt.CustomerType === 'New') {
      riskScore += 20;
    }

    // Factor 4: Outstanding ratio (10% weight)
    const outstandingRatio = debt.OutstandingDebt / debt.DebtAmount;
    riskScore += outstandingRatio * 10;

    const normalizedScore = Math.min(riskScore / 100, 1);
    const classification = this.classifyRisk(normalizedScore);

    return {
      riskLevel: classification.category,
      riskScore: Math.round(normalizedScore * 100),
      confidence: 0.70, // Lower confidence for fallback
      factors: ['Fallback assessment', ...this.identifyRiskFactors(debt)],
      mlPayload: this.prepareMLPayload(debt, customer),
      rawPrediction: { risk_score: normalizedScore, source: 'fallback' },
      source: 'fallback'
    };
  }

  /**
   * Check if debt is due for ML evaluation
   */
  isDueForEvaluation(debt) {
    const now = new Date();
    const dueDate = new Date(debt.DueDate);
    return now > dueDate;
  }

  /**
   * Batch process overdue debts for ML evaluation
   */
  async processOverdueDebts(shopId) {
    const Debt = require('../models/debt.model');
    const Customer = require('../models/customer.model');

    try {
      // Find overdue debts that haven't been ML evaluated
      const overdueDebts = await Debt.find({
        shopId,
        DueDate: { $lt: new Date() },
        RiskLevel: { $in: ['Active Debt', 'Low Risk', 'Medium Risk', 'High Risk'] },
        OutstandingDebt: { $gt: 0 }
      });

      const results = [];

      for (const debt of overdueDebts) {
        try {
          const customer = await Customer.findOne({ customerId: debt.customerId });
          if (customer) {
            const evaluation = await this.evaluateRisk(debt, customer);
            
            // Update debt and customer
            debt.RiskLevel = evaluation.riskLevel;
            customer.riskProfile.currentRiskLevel = evaluation.riskLevel;
            customer.riskProfile.riskScore = evaluation.riskScore;
            customer.riskProfile.lastAssessment = new Date();
            
            await debt.save();
            await customer.save();

            results.push({
              debtId: debt.debtId,
              customerId: debt.customerId,
              riskLevel: evaluation.riskLevel,
              riskScore: evaluation.riskScore
            });
          }
        } catch (error) {
          console.error(`Error evaluating debt ${debt.debtId}:`, error);
        }
      }

      return results;

    } catch (error) {
      console.error('Batch processing error:', error);
      throw error;
    }
  }

  /**
   * Health check for ML API
   */
  async healthCheck() {
    try {
      const baseUrl = this.mlApiUrl.endsWith('/') ? this.mlApiUrl.slice(0, -1) : this.mlApiUrl;
      const healthUrl = `${baseUrl}${this.mlHealthEndpoint}`;
      
      const response = await axios.get(healthUrl, {
        timeout: 10000,
        headers: { 'User-Agent': 'DeynCare-Backend-Health/1.0' }
      });
      
      return {
        status: 'healthy',
        apiUrl: this.mlApiUrl,
        platform: this.mlApiUrl.includes('onrender.com') ? 'Render' : 'Other',
        response: response.data,
        lastKeepAlive: this.lastKeepAlive
      };
      
    } catch (error) {
      return {
        status: 'unhealthy',
        apiUrl: this.mlApiUrl,
        error: error.message,
        lastKeepAlive: this.lastKeepAlive
      };
    }
  }
}

module.exports = new MLRiskService(); 