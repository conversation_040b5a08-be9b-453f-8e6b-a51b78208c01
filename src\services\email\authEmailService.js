const BaseEmailService = require('./baseEmailService');
const { logInfo, logError } = require('../../utils/logger');
const AppError = require('../../utils/core/AppError');

/**
 * Email service for sending authentication-related emails
 */
class AuthEmailService extends BaseEmailService {
  /**
   * Send verification email
   * @param {Object} user - User object containing email and fullName
   * @param {string} verificationCode - Verification code
   * @returns {Promise<boolean>} - Success status
   */
  async sendVerificationEmail(user, verificationCode) {
    try {
      const to = user.email;
      const subject = 'Verify Your DeynCare Account';
      const data = {
        fullName: user.fullName,
        verificationCode: verificationCode,
        expiryTime: '24 hours'
      };
      
      const html = this.renderTemplate('Auth/verification', data);
      return this.sendMail(to, subject, html);
    } catch (error) {
      logError(`Failed to send verification email to ${user?.email}`, 'AuthEmailService', error);
      throw new AppError('Failed to send verification email', 500, 'email_error');
    }
  }

  /**
   * Send password reset email
   * @param {Object} user - User object containing email and fullName
   * @param {string} resetToken - Password reset token
   * @param {Object} options - Additional options (resetByAdmin, adminName)
   * @returns {Promise<boolean>} - Success status
   */
  async sendPasswordResetEmail(user, resetToken, options = {}) {
    try {
      const to = user.email;
      const { resetByAdmin = false, adminName = null } = options;

      const subject = resetByAdmin
        ? 'Password Reset - DeynCare Account'
        : 'Reset Your DeynCare Password';

      // Create reset link with token as query parameter
      const baseUrl = process.env.FRONTEND_URL || 'https://deyncare.cajiibcreative.com';
      const resetLink = `${baseUrl}/reset-password?token=${resetToken}`;

      const data = {
        fullName: user.fullName,
        userTitle: user.userTitle || 'Employee',
        resetToken: resetToken,
        resetLink: resetLink,
        expiryTime: '24 hours',
        resetByAdmin: resetByAdmin,
        adminName: adminName,
        isEmployee: user.role === 'employee'
      };
      
      const html = this.renderTemplate('Auth/password-reset', data);
      
      // Send with timeout to prevent hanging
      const sendPromise = this.sendMail(to, subject, html);
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('Email send timeout')), 10000); // 10 second timeout
      });
      
      await Promise.race([sendPromise, timeoutPromise]);
      return true;
    } catch (error) {
      logError(`Failed to send password reset email to ${user?.email}: ${error.message}`, 'AuthEmailService');
      // Don't throw error to prevent blocking the main request
      return false;
    }
  }

  /**
   * Send welcome email after registration
   * @param {Object} user - User object containing email and fullName
   * @param {Object} shop - Shop object if applicable
   * @param {Object} subscription - Subscription object with plan details
   * @returns {Promise<boolean>} - Success status
   */
  async sendWelcomeEmail(user, shop = null, subscription = null) {
    try {
      const to = user.email;
      const subject = 'Welcome to DeynCare';
      
      // Use subscription data if provided, otherwise fallback to shop data
      const planData = subscription || shop.subscription || {};
      
      const data = {
        fullName: user.fullName,
        shopName: shop ? shop.shopName : 'N/A',
        shopId: shop ? shop.shopId : 'N/A',
        planType: (planData.plan?.type || planData.planType || planData.plan?.name || 'trial').toUpperCase(),
        shopStatus: 'Active', // Welcome email is only sent when shop is fully activated
              loginUrl: `${process.env.FRONTEND_URL || 'https://deyncare.cajiibcreative.com'}/login`,
      dashboardUrl: `${process.env.FRONTEND_URL || 'https://deyncare.cajiibcreative.com'}/dashboard`,
        isShopOwner: !!shop,
        year: new Date().getFullYear()
      };
      
      // Use template or fallback to the welcome.html at the root
      const templateKey = this.templates['Auth/welcome'] ? 'Auth/welcome' : 'welcome';
      const html = this.renderTemplate(templateKey, data);
      return this.sendMail(to, subject, html);
    } catch (error) {
      logError(`Failed to send welcome email to ${user?.email}`, 'AuthEmailService', error);
      throw new AppError('Failed to send welcome email', 500, 'email_error');
    }
  }

  /**
   * Send billing receipt email after successful registration payment
   * @param {Object} user - User object containing email and fullName
   * @param {Object} shop - Shop object with subscription details
   * @param {Object} paymentData - Payment transaction details
   * @param {Object} subscription - Subscription object with plan details
   * @returns {Promise<boolean>} - Success status
   */
  async sendRegistrationReceiptEmail(user, shop, paymentData, subscription = null) {
    try {
      const to = user.email;
      const subject = 'Registration Receipt - DeynCare';
      
      // Use subscription data if provided, otherwise fallback to shop data
      const planData = subscription || shop.subscription || {};
      
      // Calculate billing cycle and next billing date
      const billingCycle = planData.plan?.type === 'monthly' ? 'Monthly' : 'Annual';
      const nextBillingDate = new Date();
      nextBillingDate.setMonth(nextBillingDate.getMonth() + (billingCycle === 'Monthly' ? 1 : 12));
      
      const data = {
        fullName: user.fullName,
        email: user.email,
        phone: user.phone || 'N/A',
        shopName: shop.shopName,
        shopId: shop.shopId,
        planType: planData.plan?.type || planData.planType || 'trial',
        billingCycle: billingCycle,
        subscriptionStart: new Date().toLocaleDateString('en-US', {
          year: 'numeric',
          month: 'long',
          day: 'numeric'
        }),
        nextBillingDate: nextBillingDate.toLocaleDateString('en-US', {
          year: 'numeric',
          month: 'long',
          day: 'numeric'
        }),
        receiptNumber: `REC-${Date.now()}`,
        transactionId: paymentData.transactionId || 'N/A',
        amount: paymentData.amount || '0.00',
        paymentDate: new Date().toLocaleDateString('en-US', {
          year: 'numeric',
          month: 'long',
          day: 'numeric',
          hour: '2-digit',
          minute: '2-digit'
        }),
        paymentMethod: paymentData.method || planData.payment?.method || 'Online Payment',
        dashboardUrl: `${process.env.FRONTEND_URL || 'https://deyncare.cajiibcreative.com'}/dashboard`,
        billingUrl: `${process.env.FRONTEND_URL || 'https://deyncare.cajiibcreative.com'}/billing`,
        supportUrl: `${process.env.FRONTEND_URL || 'https://deyncare.cajiibcreative.com'}/support`,
        privacyUrl: `${process.env.FRONTEND_URL || 'https://deyncare.cajiibcreative.com'}/privacy`,
        termsUrl: `${process.env.FRONTEND_URL || 'https://deyncare.cajiibcreative.com'}/terms`,
        year: new Date().getFullYear()
      };
      
      // Use registration receipt template
      const html = this.renderTemplate('Subscription/registration-receipt', data);
      return this.sendMail(to, subject, html);
    } catch (error) {
      logError(`Failed to send registration receipt email to ${user?.email}`, 'AuthEmailService', error);
      throw new AppError('Failed to send registration receipt email', 500, 'email_error');
    }
  }

  /**
   * Send password changed notification email
   * @param {Object} user - User object containing email and fullName
   * @param {Object} deviceInfo - Information about the device used for the change
   * @returns {Promise<boolean>} - Success status
   */
  async sendPasswordChangedEmail(user, deviceInfo = {}) {
    try {
      const { email, fullName } = user;
      
      // Format date/time for display
      const changeTime = new Date().toLocaleString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
      
      // Get base URL for links
      const baseUrl = process.env.FRONTEND_URL || 'https://deyncare.cajiibcreative.com';
      
      // Prepare template data
      const templateData = {
        fullName: fullName || 'Valued Customer',
        changeTime,
        deviceInfo: deviceInfo.name || 'Unknown Device',
        location: deviceInfo.location || 'Unknown Location',
        loginUrl: `${baseUrl}/login`,
        resetUrl: `${baseUrl}/reset-password`,
        supportUrl: `${baseUrl}/support`
      };

      // Render the template
      const html = this.renderTemplate('Auth/password-changed', templateData);
      
      // Send the email
      return await this.sendMail(
        email,
        'Your DeynCare Password Has Been Changed',
        html
      );
    } catch (error) {
      logError(`Failed to send password changed email to ${user?.email}`, 'AuthEmailService', error);
      throw new AppError('Failed to send password changed notification', 500, 'email_error');
    }
  }
}

module.exports = new AuthEmailService();
