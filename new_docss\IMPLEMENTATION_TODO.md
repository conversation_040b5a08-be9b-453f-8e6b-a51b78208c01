# Implementation TODO List
## DeynCare Backend & Mobile Logic Fixes

### Priority 1: Critical - Session Management (Token Refresh System)

#### Backend Tasks:

- [ ] **1.1 Create Refresh Token Service**
  - [ ] Create `src/services/auth/refreshTokenService.js`
  - [ ] Implement `generateTokenPair()` function
  - [ ] Implement `refreshAccessToken()` function
  - [ ] Add refresh token validation logic
  - [ ] Add refresh token rotation for security

- [ ] **1.2 Update Authentication Controller**
  - [ ] Modify `src/controllers/authController.js`
  - [ ] Add `refreshToken()` method
  - [ ] Update login to return both access and refresh tokens
  - [ ] Add refresh token endpoint to routes

- [ ] **1.3 Update Auth Routes**
  - [ ] Add `/api/auth/refresh-token` endpoint in `src/routes/authRoutes.js`
  - [ ] Create validation schema for refresh token
  - [ ] Add rate limiting for refresh token endpoint

- [ ] **1.4 Update Token Service**
  - [ ] Modify `src/services/auth/tokenService.js`
  - [ ] Add refresh token generation
  - [ ] Add token expiry checking with buffer
  - [ ] Add automatic token refresh logic

- [ ] **1.5 Update Auth Middleware**
  - [ ] Modify `src/middleware/authMiddleware.js`
  - [ ] Add token expiry warning headers
  - [ ] Improve error handling for expired tokens
  - [ ] Add refresh token validation

#### Mobile App Tasks:

- [ ] **1.6 Create Refresh Token Interceptor**
  - [ ] Create `lib/data/network/interceptors/refresh_token_interceptor.dart`
  - [ ] Implement automatic token refresh on 401 errors
  - [ ] Add request retry logic with new tokens
  - [ ] Handle concurrent requests during refresh

- [ ] **1.7 Update Token Manager**
  - [ ] Modify `lib/data/network/token/token_manager.dart`
  - [ ] Add refresh token storage
  - [ ] Add refresh token expiry checking
  - [ ] Add automatic refresh logic

- [ ] **1.8 Update Auth Service**
  - [ ] Modify `lib/data/services/auth_service.dart`
  - [ ] Add refresh token handling
  - [ ] Add background refresh timer
  - [ ] Add silent refresh functionality

- [ ] **1.9 Update Dio Client**
  - [ ] Modify `lib/data/network/clients/dio_client.dart`
  - [ ] Add refresh token interceptor
  - [ ] Add token expiry warning handling
  - [ ] Add request retry mechanism

### Priority 2: High - Shop Settings Management

#### Mobile App Tasks:

- [ ] **2.1 Create Shop Settings Screen**
  - [ ] Create `lib/presentation/screens/settings/shop_settings_screen.dart`
  - [ ] Add shop name input field
  - [ ] Add shop address input field
  - [ ] Add business details form
  - [ ] Add social media links section

- [ ] **2.2 Create Shop Logo Upload Widget**
  - [ ] Create `lib/presentation/widgets/shop/shop_logo_upload.dart`
  - [ ] Add image picker functionality
  - [ ] Add image compression
  - [ ] Add upload progress indicator
  - [ ] Add preview functionality

- [ ] **2.3 Create Shop Service**
  - [ ] Create `lib/data/services/shop/shop_service.dart`
  - [ ] Add `updateShop()` method
  - [ ] Add `uploadLogo()` method
  - [ ] Add `getShopDetails()` method
  - [ ] Add error handling

- [ ] **2.4 Create Shop Repository**
  - [ ] Create `lib/data/repositories/shop_repository_impl.dart`
  - [ ] Add shop update operations
  - [ ] Add logo upload operations
  - [ ] Add caching logic

- [ ] **2.5 Create Shop Use Cases**
  - [ ] Create `lib/domain/usecases/shop/update_shop_use_case.dart`
  - [ ] Create `lib/domain/usecases/shop/upload_logo_use_case.dart`
  - [ ] Create `lib/domain/usecases/shop/get_shop_details_use_case.dart`

- [ ] **2.6 Create Shop Bloc**
  - [ ] Create `lib/presentation/blocs/shop/shop_bloc.dart`
  - [ ] Create `lib/presentation/blocs/shop/shop_event.dart`
  - [ ] Create `lib/presentation/blocs/shop/shop_state.dart`
  - [ ] Add shop update events and states

- [ ] **2.7 Add Navigation**
  - [ ] Add shop settings route to app router
  - [ ] Add navigation from dashboard
  - [ ] Add back navigation handling

### Priority 3: High - User Profile Management

#### Mobile App Tasks:

- [ ] **3.1 Create Profile Settings Screen**
  - [ ] Create `lib/presentation/screens/settings/profile_settings_screen.dart`
  - [ ] Add profile image upload
  - [ ] Add name input field
  - [ ] Add email display (read-only)
  - [ ] Add phone number field

- [ ] **3.2 Create Change Password Form**
  - [ ] Create `lib/presentation/widgets/auth/change_password_form.dart`
  - [ ] Add current password field
  - [ ] Add new password field
  - [ ] Add confirm password field
  - [ ] Add password validation

- [ ] **3.3 Create User Service**
  - [ ] Create `lib/data/services/user/user_service.dart`
  - [ ] Add `changePassword()` method
  - [ ] Add `updateProfile()` method
  - [ ] Add `getUserProfile()` method

- [ ] **3.4 Create User Repository**
  - [ ] Create `lib/data/repositories/user_repository_impl.dart`
  - [ ] Add profile update operations
  - [ ] Add password change operations
  - [ ] Add caching logic

- [ ] **3.5 Create User Use Cases**
  - [ ] Create `lib/domain/usecases/user/change_password_use_case.dart`
  - [ ] Create `lib/domain/usecases/user/update_profile_use_case.dart`
  - [ ] Create `lib/domain/usecases/user/get_profile_use_case.dart`

- [ ] **3.6 Create User Bloc**
  - [ ] Create `lib/presentation/blocs/user/user_bloc.dart`
  - [ ] Create `lib/presentation/blocs/user/user_event.dart`
  - [ ] Create `lib/presentation/blocs/user/user_state.dart`
  - [ ] Add profile update events and states

- [ ] **3.7 Add Navigation**
  - [ ] Add profile settings route to app router
  - [ ] Add navigation from dashboard
  - [ ] Add back navigation handling

### Priority 4: Medium - Offline Support Enhancement

#### Mobile App Tasks:

- [ ] **4.1 Enhance Connectivity Service**
  - [ ] Update `lib/data/services/connectivity_service.dart`
  - [ ] Add better connectivity detection
  - [ ] Add connectivity change notifications
  - [ ] Add offline mode detection

- [ ] **4.2 Create Offline Storage**
  - [ ] Create `lib/data/services/offline_storage_service.dart`
  - [ ] Add local data storage
  - [ ] Add sync queue management
  - [ ] Add conflict resolution

- [ ] **4.3 Update Repositories**
  - [ ] Modify all repositories to support offline operations
  - [ ] Add offline data caching
  - [ ] Add sync when online
  - [ ] Add conflict handling

### Priority 5: Medium - Error Handling & User Feedback

#### Mobile App Tasks:

- [ ] **5.1 Create Error Handler**
  - [ ] Create `lib/core/utils/error_handler.dart`
  - [ ] Add error categorization
  - [ ] Add user-friendly error messages
  - [ ] Add error reporting

- [ ] **5.2 Create Toast Service**
  - [ ] Create `lib/core/utils/toast_service.dart`
  - [ ] Add success messages
  - [ ] Add error messages
  - [ ] Add warning messages

- [ ] **5.3 Update All Screens**
  - [ ] Add error handling to all screens
  - [ ] Add loading states
  - [ ] Add user feedback
  - [ ] Add retry mechanisms

### Testing Tasks:

- [ ] **6.1 Token Refresh Testing**
  - [ ] Test token expiry scenarios
  - [ ] Test automatic refresh
  - [ ] Test concurrent requests
  - [ ] Test refresh token rotation

- [ ] **6.2 Shop Settings Testing**
  - [ ] Test shop name updates
  - [ ] Test logo upload
  - [ ] Test address updates
  - [ ] Test offline functionality

- [ ] **6.3 User Profile Testing**
  - [ ] Test password change
  - [ ] Test profile updates
  - [ ] Test validation
  - [ ] Test error scenarios

- [ ] **6.4 Integration Testing**
  - [ ] Test end-to-end flows
  - [ ] Test cross-platform compatibility
  - [ ] Test performance
  - [ ] Test security

### Documentation Tasks:

- [ ] **7.1 Update API Documentation**
  - [ ] Document new refresh token endpoints
  - [ ] Document shop update endpoints
  - [ ] Document user profile endpoints
  - [ ] Update authentication flow docs

- [ ] **7.2 Update Mobile App Documentation**
  - [ ] Document new screens and features
  - [ ] Document offline functionality
  - [ ] Document error handling
  - [ ] Update user guides

- [ ] **7.3 Create Migration Guide**
  - [ ] Document breaking changes
  - [ ] Document upgrade procedures
  - [ ] Document configuration changes
  - [ ] Document testing procedures

### Deployment Tasks:

- [ ] **8.1 Backend Deployment**
  - [ ] Update environment variables
  - [ ] Update database schema if needed
  - [ ] Deploy to staging environment
  - [ ] Run integration tests
  - [ ] Deploy to production

- [ ] **8.2 Mobile App Deployment**
  - [ ] Build and test mobile app
  - [ ] Update app store listings
  - [ ] Deploy to test flight/play console
  - [ ] Release to production

### Monitoring & Analytics:

- [ ] **9.1 Add Error Tracking**
  - [ ] Integrate error tracking service
  - [ ] Add error reporting
  - [ ] Add performance monitoring
  - [ ] Add user analytics

- [ ] **9.2 Add Health Checks**
  - [ ] Add API health checks
  - [ ] Add database health checks
  - [ ] Add service health checks
  - [ ] Add alerting

---

## Implementation Timeline

### Week 1-2: Token Refresh System
- Complete all Priority 1 tasks
- Focus on backend implementation first
- Then implement mobile app changes
- Test thoroughly

### Week 3-5: Shop Settings
- Complete all Priority 2 tasks
- Implement UI components
- Add API integration
- Test functionality

### Week 6-8: User Profile Management
- Complete all Priority 3 tasks
- Implement profile screens
- Add password change functionality
- Test security features

### Week 9-10: Testing & Documentation
- Complete all testing tasks
- Update documentation
- Prepare for deployment

### Week 11-12: Deployment & Monitoring
- Deploy to production
- Monitor performance
- Address any issues
- Gather user feedback

---

## Success Criteria

### Token Refresh System:
- [ ] Users stay logged in for 30 days without manual re-login
- [ ] Token refresh happens automatically in background
- [ ] No user-visible session interruptions
- [ ] Security maintained with token rotation

### Shop Settings:
- [ ] Users can update shop name and address
- [ ] Users can upload and change shop logo
- [ ] All changes sync properly with backend
- [ ] Offline changes sync when online

### User Profile Management:
- [ ] Users can change their password securely
- [ ] Users can update their name and profile
- [ ] All changes are validated properly
- [ ] Security is maintained throughout

### Overall System:
- [ ] No critical bugs in production
- [ ] Performance remains acceptable
- [ ] User experience is improved
- [ ] System is more robust and reliable 