import apiBridge from '@/lib/api/bridge';
import { ENDPOINTS } from '@/lib/api/contract';;
import { handleError, processApiResponse } from '../baseService';

/**
 * Update subscription details
 * @param {string} subscriptionId - Subscription ID to update
 * @param {Object} updateData - Data to update
 * @returns {Promise<Object>} Updated subscription
 */
async function updateSubscription(subscriptionId, updateData) {
  try {
    const response = await apiBridge.put(
      `${ENDPOINTS.SUBSCRIPTIONS.BASE}/${subscriptionId}`,
      updateData
    );
    
    return processApiResponse(response);
  } catch (error) {
    handleError(error, 'SubscriptionService.updateSubscription', true);
    throw error;
  }
}

export default updateSubscription; 
