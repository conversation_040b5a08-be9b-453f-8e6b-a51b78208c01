"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { useAuth } from "@/contexts/auth-context"
import { toast } from "sonner"
import { errorHandlers } from "@/lib/api/contract"
import { z } from "zod"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { Mail, AlertCircle, CheckCircle2, X } from "lucide-react"

// Form validation schema
const forgotPasswordSchema = z.object({
  email: z.string()
    .min(1, "Email address is required")
    .email("Please enter a valid email address")
})

export function ForgotPasswordModal({ isOpen, onClose }) {
  const [isLoading, setIsLoading] = useState(false)
  const [success, setSuccess] = useState(false)
  const { forgotPassword } = useAuth()
  
  // Initialize form with validation
  const {
    register,
    handleSubmit,
    formState: { errors },
    reset
  } = useForm({
    resolver: zodResolver(forgotPasswordSchema),
    defaultValues: {
      email: ""
    }
  })
  
  // Reset form when modal closes
  useEffect(() => {
    if (!isOpen) {
      reset()
      setSuccess(false)
    }
  }, [isOpen, reset])

  const onSubmit = async (data) => {
    setIsLoading(true)
    
    try {
      await forgotPassword(data.email)
      toast.success("Password reset instructions sent to your email")
      setSuccess(true)
      // Reset form
      reset()
    } catch (err) {
      const friendlyErrorMessage = errorHandlers.getErrorMessage(err)
      toast.error(friendlyErrorMessage)
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-xl">
            {success ? (
              <CheckCircle2 className="h-5 w-5 text-primary" />
            ) : (
              <Mail className="h-5 w-5 text-primary" />
            )}
            {success ? "Check Your Email" : "Reset Password"}
          </DialogTitle>
          <DialogDescription className="text-muted-foreground">
            {success 
              ? "We've sent you an email with instructions to reset your password."
              : "Enter your email address and we'll send you a link to reset your password."}
          </DialogDescription>
        </DialogHeader>
        
        {success ? (
          <div className="space-y-4 text-center py-4">
            <div className="mx-auto flex h-20 w-20 items-center justify-center rounded-full bg-primary/10">
              <CheckCircle2 className="h-10 w-10 text-primary" />
            </div>
            <p className="text-sm text-center text-muted-foreground">
              If you don't receive the email within a few minutes, please check your spam folder or try again.
            </p>
          </div>
        ) : (
          <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="email" className="text-sm font-medium">
                Email address
              </Label>
              <div className="relative">
                <Input
                  id="email"
                  type="email"
                  placeholder="<EMAIL>"
                  className={`pr-10 ${errors.email ? "border-destructive focus:ring-destructive" : ""}`}
                  disabled={isLoading}
                  {...register("email")}
                />
                {errors.email && (
                  <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                    <AlertCircle className="h-5 w-5 text-destructive" />
                  </div>
                )}
              </div>
              {errors.email && (
                <p className="text-xs text-destructive">{errors.email.message}</p>
              )}
            </div>
          
            <DialogFooter className="flex-col sm:flex-row sm:justify-end sm:space-x-2">
              <Button
                type="button"
                variant="outline"
                onClick={onClose}
                disabled={isLoading}
                className="mt-2 sm:mt-0"
              >
                Cancel
              </Button>
              <Button type="submit" disabled={isLoading}>
                {isLoading ? (
                  <span className="flex items-center gap-1">
                    <span className="h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent"></span>
                    Sending...
                  </span>
                ) : (
                  "Send Reset Link"
                )}
              </Button>
            </DialogFooter>
          </form>
        )}
      </DialogContent>
    </Dialog>
  )
}
