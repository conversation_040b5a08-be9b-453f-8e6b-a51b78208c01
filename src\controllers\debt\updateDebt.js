const { Debt, Customer } = require('../../models');
const { AppError, logError, logInfo } = require('../../utils');
const mlService = require('../../services/mlRiskService');

/**
 * Update Debt Information
 * PUT /api/debts/:debtId
 */
const updateDebt = async (req, res, next) => {
  try {
    const { debtId } = req.params;
    const { 
      CustomerName, 
      customerPhone, 
      DebtAmount, 
      DueDate, 
      notes,
      priority,
      // Flutter sends camelCase, backend expects PascalCase
      debtAmount,
      dueDate,
      description
    } = req.body;

    // Handle both naming conventions (camelCase from Flutter, PascalCase from other sources)
    const finalDebtAmount = DebtAmount || debtAmount;
    const finalDueDate = DueDate || dueDate;
    const finalDescription = notes || description;

    // Debug logging for field mapping
    console.log('🔧 [UpdateDebt] Field mapping:', {
      'DebtAmount (PascalCase)': DebtAmount,
      'debtAmount (camelCase)': debtAmount,
      'finalDebtAmount': finalDebtAmount,
      'DueDate (PascalCase)': DueDate,
      'dueDate (camelCase)': dueDate,
      'finalDueDate': finalDueDate
    });

    const shopId = req.user.shopId;

    // Find existing debt
    const debt = await Debt.findOne({ 
      debtId, 
      shopId, 
      isDeleted: false 
    });

    if (!debt) {
      return res.status(404).json({
        success: false,
        message: 'Debt not found'
      });
    }

    // Store original values for comparison
    const originalAmount = debt.DebtAmount;
    const originalDueDate = debt.DueDate;

    // Update allowed fields
    if (CustomerName && CustomerName !== debt.CustomerName) {
      debt.CustomerName = CustomerName;
      
      // Update associated customer if exists
      const customer = await Customer.findOne({ customerId: debt.customerId });
      if (customer) {
        customer.CustomerName = CustomerName;
        await customer.save();
      }
    }

    if (customerPhone) {
      // Update customer phone if provided
      const customer = await Customer.findOne({ customerId: debt.customerId });
      if (customer) {
        customer.phone = customerPhone;
        await customer.save();
      }
    }

    if (finalDebtAmount && finalDebtAmount !== originalAmount) {
      // Update debt amount and recalculate ratios
      debt.DebtAmount = finalDebtAmount;
      
      // Recalculate outstanding debt
      debt.OutstandingDebt = finalDebtAmount - (debt.PaidAmount || 0);
      
      // Recalculate debt paid ratio
      debt.DebtPaidRatio = debt.PaidAmount > 0 ? debt.PaidAmount / finalDebtAmount : 0;
      
      // If debt is now fully paid, update status
      if (debt.OutstandingDebt <= 0) {
        debt.RiskLevel = 'Paid';
        debt.IsOnTime = new Date() <= new Date(debt.DueDate);
      }
    }

    if (finalDueDate && finalDueDate !== originalDueDate.toISOString()) {
      debt.DueDate = new Date(finalDueDate);
      
      // Recalculate payment delay and timing
      const now = new Date();
      if (now > debt.DueDate) {
        debt.PaymentDelay = Math.ceil((now - debt.DueDate) / (1000 * 60 * 60 * 24));
        debt.RepaymentTime = Math.ceil((now - debt.DebtCreationDate) / (1000 * 60 * 60 * 24));
        debt.IsOnTime = false;
      } else {
        debt.PaymentDelay = 0;
        debt.IsOnTime = true;
      }
    }

    if (finalDescription !== undefined) {
      debt.description = finalDescription;
    }

    if (priority !== undefined) {
      debt.priority = priority;
    }

    // Save updated debt
    await debt.save();

    // Note: ML risk evaluation is NOT triggered for debt updates
    // Risk evaluation only occurs for payment recordings or scheduled evaluations
    // This keeps debt editing fast and focused on business needs

    logInfo(`Debt updated: ${debtId} by ${req.user.email}`, 'UpdateDebt');

    // Return updated debt with customer info
    const updatedDebt = await Debt.findOne({ debtId, shopId })
      .populate('customerId', 'CustomerName phone CustomerType')
      .lean();

    res.json({
      success: true,
      message: 'Debt updated successfully',
      data: {
        debtId: updatedDebt.debtId || debtId,
        customerId: updatedDebt.customerId || 'UNKNOWN',
        customerName: updatedDebt.CustomerName || 'Unknown Customer',
        customerPhone: updatedDebt.customerId?.phone || null,
        customerType: updatedDebt.customerId?.CustomerType || updatedDebt.CustomerType || 'New',
        debtAmount: updatedDebt.DebtAmount || 0,
        outstandingDebt: updatedDebt.OutstandingDebt || 0,
        paidAmount: updatedDebt.PaidAmount || 0,
        debtPaidRatio: Math.round((updatedDebt.DebtPaidRatio || 0) * 100),
        dueDate: updatedDebt.DueDate || new Date(),
        riskLevel: updatedDebt.RiskLevel || 'Active Debt',
        paymentDelay: updatedDebt.PaymentDelay || 0,
        isOnTime: updatedDebt.IsOnTime !== undefined ? updatedDebt.IsOnTime : true,
        description: updatedDebt.description || null,
        priority: updatedDebt.priority || null,
        status: updatedDebt.status || 'active',
        createdAt: updatedDebt.createdAt || new Date(),
        updatedAt: updatedDebt.updatedAt || new Date()
      }
    });

  } catch (error) {
    logError('Failed to update debt', 'UpdateDebt', error);
    return next(new AppError('Failed to update debt', 500));
  }
};

module.exports = updateDebt; 