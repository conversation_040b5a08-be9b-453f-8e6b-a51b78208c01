/**
 * Payment Settings Query Hook
 * Uses backend-matching services for payment methods and EVC credentials
 */

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { 
  fetchPaymentMethods,
  updatePaymentMethods,
  fetchEvcCredentials,
  setEvcCredentials,
  testEvcCredentials
} from '../lib/services/settings';

/**
 * Hook for managing payment settings with backend-exact API integration
 * @param {Object} options - Hook options
 * @param {string} [options.context='general'] - Payment context ('general', 'subscription', 'pos')
 * @param {string|null} [options.shopId] - Shop ID for shop-specific settings
 * @returns {Object} Payment settings query result and mutation functions
 */
export const usePaymentSettingsQuery = (options = {}) => {
  const { context = 'general', shopId = null } = options;
  const queryClient = useQueryClient();

  // Query for payment methods
  const paymentMethodsQuery = useQuery({
    queryKey: ['paymentMethods', context],
    queryFn: () => fetchPaymentMethods(context),
    staleTime: 5 * 60 * 1000, // 5 minutes
    cacheTime: 10 * 60 * 1000, // 10 minutes
    refetchOnWindowFocus: false,
    retry: 2,
    onError: (error) => {
      console.error('[usePaymentSettingsQuery] Payment methods fetch error:', error);
    }
  });

  // Query for EVC credentials
  const evcCredentialsQuery = useQuery({
    queryKey: ['evcCredentials', shopId],
    queryFn: () => fetchEvcCredentials(shopId),
    staleTime: 5 * 60 * 1000, // 5 minutes
    cacheTime: 10 * 60 * 1000, // 10 minutes
    refetchOnWindowFocus: false,
    retry: 2,
    onError: (error) => {
      console.error('[usePaymentSettingsQuery] EVC credentials fetch error:', error);
    }
  });

  // Mutation for updating payment methods
  const updatePaymentMethodsMutation = useMutation({
    mutationFn: updatePaymentMethods,
    onSuccess: (data) => {
      console.log('[usePaymentSettingsQuery] Payment methods updated:', data);
      // Invalidate and refetch payment methods
      queryClient.invalidateQueries(['paymentMethods']);
    },
    onError: (error) => {
      console.error('[usePaymentSettingsQuery] Payment methods update error:', error);
    }
  });

  // Mutation for setting EVC credentials
  const setEvcCredentialsMutation = useMutation({
    mutationFn: setEvcCredentials,
    onSuccess: (data) => {
      console.log('[usePaymentSettingsQuery] EVC credentials saved:', data);
      // Invalidate and refetch EVC credentials
      queryClient.invalidateQueries(['evcCredentials']);
    },
    onError: (error) => {
      console.error('[usePaymentSettingsQuery] EVC credentials save error:', error);
    }
  });

  // Mutation for testing EVC credentials
  const testEvcCredentialsMutation = useMutation({
    mutationFn: testEvcCredentials,
    onError: (error) => {
      console.error('[usePaymentSettingsQuery] EVC credentials test error:', error);
    }
  });

  // Computed values
  const isLoading = paymentMethodsQuery.isLoading || evcCredentialsQuery.isLoading;
  const isError = paymentMethodsQuery.isError || evcCredentialsQuery.isError;
  const error = paymentMethodsQuery.error || evcCredentialsQuery.error;

  // Payment methods data (exact backend format)
  const paymentMethodsData = paymentMethodsQuery.data?.data || {
    paymentMethods: [],
    onlinePaymentsEnabled: false,
    offlinePaymentsEnabled: false,
    context: context
  };

  // EVC credentials data (exact backend format)
  const evcCredentialsResponse = evcCredentialsQuery.data || {};
  const evcCredentialsData = evcCredentialsResponse.data || {};
  
  // Check if we actually have credentials (not just a message)
  const hasActualCredentials = evcCredentialsData.merchantUId || 
                              evcCredentialsData.apiUserId || 
                              evcCredentialsData.apiKey || 
                              evcCredentialsData.merchantNo;

  return {
    // Payment Methods
    paymentMethods: paymentMethodsData.paymentMethods,
    onlinePaymentsEnabled: paymentMethodsData.onlinePaymentsEnabled,
    offlinePaymentsEnabled: paymentMethodsData.offlinePaymentsEnabled,
    paymentMethodsContext: paymentMethodsData.context,
    
    // EVC Credentials
    evcCredentials: evcCredentialsData,
    hasEvcCredentials: Boolean(hasActualCredentials),
    
    // Loading States
    isLoading,
    isLoadingPaymentMethods: paymentMethodsQuery.isLoading,
    isLoadingEvcCredentials: evcCredentialsQuery.isLoading,
    
    // Error States
    isError,
    error,
    paymentMethodsError: paymentMethodsQuery.error,
    evcCredentialsError: evcCredentialsQuery.error,
    
    // Mutation States
    isSavingPaymentMethods: updatePaymentMethodsMutation.isLoading,
    isSavingEvcCredentials: setEvcCredentialsMutation.isLoading,
    isTestingEvcCredentials: testEvcCredentialsMutation.isLoading,
    
    // Mutation Functions
    updatePaymentMethods: updatePaymentMethodsMutation.mutateAsync,
    saveEvcCredentials: setEvcCredentialsMutation.mutateAsync,
    testEvcCredentials: testEvcCredentialsMutation.mutateAsync,
    
    // Refetch Functions
    refetchPaymentMethods: paymentMethodsQuery.refetch,
    refetchEvcCredentials: evcCredentialsQuery.refetch,
    refetchAll: () => {
      paymentMethodsQuery.refetch();
      evcCredentialsQuery.refetch();
    }
  };
}; 