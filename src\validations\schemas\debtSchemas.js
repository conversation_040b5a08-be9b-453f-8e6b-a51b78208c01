const Joi = require('joi');

const debtValidation = {
  // Create Debt Validation (Step 1: Customer Takes a Loan)
  createDebt: {
    body: Joi.object({
      // Reference existing customer by ID (BEST PRACTICE)
      customerId: Joi.string()
        .pattern(/^CUST\d{3}$/)
        .required()
        .messages({
          'string.pattern.base': 'Invalid customer ID format. Expected format: CUST001',
          'any.required': 'Customer ID is required'
        }),
      
      debtAmount: Joi.number()
        .positive()
        .precision(2)
        .required()
        .messages({
          'number.positive': 'Debt amount must be a positive number',
          'any.required': 'Debt amount is required'
        }),
      
      dueDate: Joi.date()
        .min('now')
        .required()
        .messages({
          'date.min': 'Due date must be in the future',
          'any.required': 'Due date is required'
        }),
      
      description: Joi.string()
        .trim()
        .max(1000)
        .optional()
        .allow('')
        .messages({
          'string.max': 'Description cannot exceed 1000 characters'
        }),
      
      // Optional initial payment
      paidAmount: Joi.number()
        .min(0)
        .precision(2)
        .optional()
        .messages({
          'number.min': 'Paid amount cannot be negative'
        }),
      
      paidDate: Joi.date()
        .optional()
        .messages({
          'date.base': 'Please provide a valid payment date'
        }),
      
      paymentMethod: Joi.string()
        .valid('cash', 'bank_transfer', 'mobile_money', 'card', 'other')
        .default('cash')
        .optional()
    }).custom((value, helpers) => {
      // Validate that paidAmount doesn't exceed debtAmount
      if (value.paidAmount && value.paidAmount > value.debtAmount) {
        return helpers.error('custom.paidAmountExceeds');
      }
      
      // If paidAmount is provided, paidDate should also be provided
      if (value.paidAmount > 0 && !value.paidDate) {
        value.paidDate = new Date(); // Default to current date
      }
      
      return value;
    }, 'Debt creation validation').messages({
      'custom.paidAmountExceeds': 'Paid amount cannot exceed debt amount'
    })
  },

  // Add Payment Validation (Step 7: Customer Pays)
  addPayment: {
    params: Joi.object({
      debtId: Joi.string()
        .pattern(/^DEBT\d{3}$/)
        .required()
        .messages({
          'string.pattern.base': 'Invalid debt ID format. Expected format: DEBT001',
          'any.required': 'Debt ID is required'
        })
    }),
    
    body: Joi.object({
      amount: Joi.number()
        .positive()
        .precision(2)
        .required()
        .messages({
          'number.positive': 'Payment amount must be positive',
          'any.required': 'Payment amount is required'
        }),
      
      paymentDate: Joi.date()
        .allow('')
        .default(new Date())
        .optional()
        .custom((value, helpers) => {
          // Convert empty string to undefined to trigger default
          if (value === '') {
            return undefined;
          }
          return value;
        })
        .messages({
          'date.base': 'Please provide a valid payment date'
        }),
      
      // NEW: Staff-recorded real payment time (when customer actually paid)
      paidAtReal: Joi.date()
        .max('now')
        .optional()
        .messages({
          'date.max': 'Real payment date cannot be in the future',
          'date.base': 'Please provide a valid real payment date'
        }),
      
      paymentMethod: Joi.string()
        .valid('cash', 'bank_transfer', 'mobile_money', 'card', 'other')
        .default('cash')
        .optional(),
      
      notes: Joi.string()
        .trim()
        .max(500)
        .optional()
        .allow('')
        .messages({
          'string.max': 'Notes cannot exceed 500 characters'
        })
    })
  },

  // Get Debts Validation
  getDebts: {
    query: Joi.object({
      page: Joi.number()
        .integer()
        .min(1)
        .default(1)
        .optional(),
      
      limit: Joi.number()
        .integer()
        .min(1)
        .max(100)
        .default(20)
        .optional(),
      
      status: Joi.string()
        .valid('active', 'paid', 'overdue', 'partially_paid')
        .optional(),
      
      riskLevel: Joi.string()
        .valid('Low Risk', 'Medium Risk', 'High Risk', 'Active Debt')
        .optional(),
      
      customerType: Joi.string()
        .valid('New', 'Returning')
        .optional(),
      
      sortBy: Joi.string()
        .valid('dueDate', 'debtAmount', 'riskScore', 'createdAt')
        .default('createdAt')
        .optional(),
      
      sortOrder: Joi.string()
        .valid('asc', 'desc')
        .default('desc')
        .optional(),
      
      search: Joi.string()
        .trim()
        .max(100)
        .optional()
        .messages({
          'string.max': 'Search term cannot exceed 100 characters'
        })
    })
  },

  // Get Debt By ID Validation
  getDebtById: {
    params: Joi.object({
      debtId: Joi.string()
        .pattern(/^DEBT\d{3}$/)
        .required()
        .messages({
          'string.pattern.base': 'Invalid debt ID format. Expected format: DEBT001',
          'any.required': 'Debt ID is required'
        })
    })
  },

  // Update Debt Validation
  updateDebt: {
    params: Joi.object({
      debtId: Joi.string()
        .pattern(/^DEBT\d{3}$/)
        .required()
        .messages({
          'string.pattern.base': 'Invalid debt ID format. Expected format: DEBT001',
          'any.required': 'Debt ID is required'
        })
    }),
    
    body: Joi.object({
      dueDate: Joi.date()
        .optional()
        .messages({
          'date.base': 'Please provide a valid due date'
        }),
      
      description: Joi.string()
        .trim()
        .max(1000)
        .optional()
        .allow('')
        .messages({
          'string.max': 'Description cannot exceed 1000 characters'
        })
    }).min(1).messages({
      'object.min': 'At least one field must be provided for update'
    })
  },

  // Delete Debt Validation
  deleteDebt: {
    params: Joi.object({
      debtId: Joi.string()
        .pattern(/^DEBT\d{3}$/)
        .required()
        .messages({
          'string.pattern.base': 'Invalid debt ID format. Expected format: DEBT001',
          'any.required': 'Debt ID is required'
        })
    })
  }
};

module.exports = { debtValidation }; 