/**
 * Plan Model
 * Stores plan information for dynamic management
 */
const mongoose = require('mongoose');
const idGenerator = require('../utils/generators/idGenerator');

const planSchema = new mongoose.Schema({
  planId: {
    type: String,
    required: true,
    unique: true,
    trim: true,
    default: () => idGenerator.generateSimpleId('PLAN')
  },
  name: {
    type: String,
    required: true,
    trim: true
  },
  type: {
    type: String,
    enum: ['trial', 'monthly', 'yearly'],
    required: true
  },
  displayName: {
    type: String,
    required: true,
    trim: true
  },
  description: {
    type: String,
    trim: true
  },
  pricing: {
    basePrice: {
      type: Number,
      required: true,
      min: 0
    },
    currency: {
      type: String,
      default: 'USD',
      trim: true
    },
    billingCycle: {
      type: String,
      enum: ['one-time', 'monthly', 'yearly'],
      required: true
    },
    trialDays: {
      type: Number,
      default: 14,
      min: 0
    },
    setupFee: {
      type: Number,
      default: 0,
      min: 0
    }
  },
  features: {
    debtTracking: {
      type: Boolean,
      default: true
    },
    customerPayments: {
      type: Boolean,
      default: true
    },
    smsReminders: {
      type: Boolean,
      default: true
    },
    smartRiskScore: {
      type: Boolean,
      default: true
    },
    businessDashboard: {
      type: Boolean,
      default: true
    },
    exportReports: {
      type: Boolean,
      default: true
    },
    customerProfiles: {
      type: Boolean,
      default: true
    },
    offlineSupport: {
      type: Boolean,
      default: true
    }
  },
  limits: {
    maxEmployees: {
      type: Number,
      default: 10
    },
    maxStorageMB: {
      type: Number,
      default: 500
    },
    maxCustomers: {
      type: Number,
      default: 1000
    },
    maxDailyTransactions: {
      type: Number,
      default: 500
    }
  },
  isActive: {
    type: Boolean,
    default: true
  },
  displayOrder: {
    type: Number,
    default: 1
  },
  metadata: {
    isRecommended: {
      type: Boolean,
      default: false
    },
    tags: [String],
    customFields: mongoose.Schema.Types.Mixed
  },
  createdBy: {
    type: String,
    trim: true
  },
  isDeleted: {
    type: Boolean,
    default: false
  }
}, { 
  timestamps: true 
});

// Create indexes for better performance
planSchema.index({ type: 1, isActive: 1 });
planSchema.index({ isActive: 1, displayOrder: 1 });

/**
 * Create default plans when collection is empty
 */
planSchema.statics.createDefaultPlans = async function() {
  const count = await this.countDocuments({});
  
  if (count === 0) {
    const defaultPlans = [
      {
        planId: idGenerator.generateSimpleId('PLAN'),
        name: 'trial',
        type: 'trial',
        displayName: 'Free Trial',
        description: 'Try all features free for 14 days',
        pricing: {
          basePrice: 0,
          currency: 'USD',
          billingCycle: 'one-time',
          trialDays: 14,
          setupFee: 0
        },
        displayOrder: 1,
        metadata: {
          isRecommended: false,
          tags: ['trial', 'free']
        }
      },
      {
        planId: idGenerator.generateSimpleId('PLAN'),
        name: 'monthly',
        type: 'monthly',
        displayName: 'Monthly Plan',
        description: 'Full access to all features with monthly billing',
        pricing: {
          basePrice: 10,  // Realistic monthly pricing - $10/month
          currency: 'USD',
          billingCycle: 'monthly',
          trialDays: 0,
          setupFee: 0
        },
        displayOrder: 2,
        metadata: {
          isRecommended: true,
          tags: ['monthly', 'standard']
        }
      },
      {
        planId: idGenerator.generateSimpleId('PLAN'),
        name: 'yearly',
        type: 'yearly',
        displayName: 'Annual Plan',
        description: 'Save with yearly billing',
        pricing: {
          basePrice: 96,  // Realistic yearly pricing - $96/year (20% discount from $120)
          currency: 'USD',
          billingCycle: 'yearly',
          trialDays: 0,
          setupFee: 0
        },
        displayOrder: 3,
        metadata: {
          isRecommended: false,
          tags: ['yearly', 'discounted']
        }
      }
    ];
    
    await this.insertMany(defaultPlans);
  }
};

/**
 * Find active plans
 */
planSchema.statics.findActivePlans = async function() {
  return this.find({
    isActive: true,
    isDeleted: false
  }).sort({ displayOrder: 1 });
};

/**
 * Find plan by type
 */
planSchema.statics.findPlanByType = async function(planType) {
  return this.findOne({
    type: planType,
    isActive: true,
    isDeleted: false
  });
};

const Plan = mongoose.model('PricingPlan', planSchema);

module.exports = Plan;
