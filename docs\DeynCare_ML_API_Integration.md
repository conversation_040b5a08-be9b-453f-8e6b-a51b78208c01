# 🚀 **DeynCare ML API Integration Guide**

## 📊 **API Overview**

The DeynCare ML API is a FastAPI-based machine learning service deployed at:
**Base URL:** `https://deyncare-ml.onrender.com/`

### 🎯 **Key Features**
- **Real-time Risk Assessment** for individual debt cases
- **Bulk Processing** via CSV upload
- **FairRisk Algorithm** for transparent risk scoring
- **Multilingual Support** (English & Somali documentation)
- **No API Key Required** for public endpoints

---

## 🔗 **Available Endpoints**

### 1. **Root Endpoint** 
```http
GET https://deyncare-ml.onrender.com/
```
**Response:**
```json
{
  "message": "Welcome to DeynCare ML API! 🚀"
}
```

### 2. **Single Risk Prediction** ⭐
```http
POST https://deyncare-ml.onrender.com/predict_single/
```

**Request Body:**
```json
{
  "DebtPaidRatio": 0.75,
  "PaymentDelay": 5,
  "OutstandingDebt": 250,
  "DebtAmount": 1000
}
```

**Response:**
```json
{
  "RiskScore": 0.45,
  "PredictedRiskLevel": "Medium Risk"
}
```

### 3. **Bulk CSV Processing**
```http
POST https://deyncare-ml.onrender.com/upload_csv/
```

**Request:** Multipart form data with CSV file

**Response:**
```json
{
  "all_records": [...],
  "last_two_records": [...]
}
```

### 4. **Sample Data Download**
```http
GET https://deyncare-ml.onrender.com/sample_data.csv
```

---

## 🧮 **FairRisk Algorithm**

The ML API uses a transparent risk calculation formula:

```
Risk_Score = (1 - DebtPaidRatio) + (PaymentDelay ÷ 10) + (OutstandingDebt ÷ DebtAmount)
```

### **Risk Level Categories:**
- 🟢 **Low Risk:** Score ≤ 0.3
- 🟡 **Medium Risk:** 0.3 < Score ≤ 0.6  
- 🔴 **High Risk:** Score > 0.6

---

## 💾 **Data Model Schema**

### **Input Parameters**

| Field | Type | Description | Example |
|-------|------|-------------|---------|
| `DebtPaidRatio` | `float` | Ratio of paid amount to total debt (0-1) | `0.75` |
| `PaymentDelay` | `int` | Days of payment delay (negative = early) | `5` |
| `OutstandingDebt` | `float` | Remaining unpaid amount | `250.00` |
| `DebtAmount` | `float` | Total original debt amount | `1000.00` |

### **Output Response**

| Field | Type | Description | Example |
|-------|------|-------------|---------|
| `RiskScore` | `float` | Numerical risk score (0-100) | `0.45` |
| `PredictedRiskLevel` | `string` | Risk category | `"Medium Risk"` |

---

## 🛠 **Frontend Integration**

### **Environment Configuration**

Update your `.env.local` file:
```env
NEXT_PUBLIC_ML_API_URL=https://deyncare-ml.onrender.com
ML_API_TIMEOUT=30000
```

### **API Service Integration**

Create or update `lib/services/ml/riskAssessment.js`:

```javascript
/**
 * DeynCare ML API Integration Service
 * Handles risk assessment API calls
 */

const ML_API_BASE_URL = process.env.NEXT_PUBLIC_ML_API_URL || 'https://deyncare-ml.onrender.com';

/**
 * Single customer risk assessment
 * @param {Object} debtData - Debt information
 * @param {number} debtData.DebtPaidRatio - Ratio of paid amount (0-1)
 * @param {number} debtData.PaymentDelay - Days of delay (negative = early)
 * @param {number} debtData.OutstandingDebt - Remaining debt amount
 * @param {number} debtData.DebtAmount - Total debt amount
 * @returns {Promise<Object>} Risk assessment result
 */
export async function assessSingleRisk(debtData) {
  try {
    const response = await fetch(`${ML_API_BASE_URL}/predict_single/`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(debtData),
    });

    if (!response.ok) {
      throw new Error(`ML API Error: ${response.status} ${response.statusText}`);
    }

    const result = await response.json();
    return {
      success: true,
      data: result,
    };
  } catch (error) {
    console.error('[ML API] Risk assessment failed:', error);
    return {
      success: false,
      error: error.message,
    };
  }
}

/**
 * Bulk CSV risk assessment
 * @param {File} csvFile - CSV file with customer data
 * @returns {Promise<Object>} Bulk assessment results
 */
export async function assessBulkRisk(csvFile) {
  try {
    const formData = new FormData();
    formData.append('file', csvFile);

    const response = await fetch(`${ML_API_BASE_URL}/upload_csv/`, {
      method: 'POST',
      body: formData,
    });

    if (!response.ok) {
      throw new Error(`ML API Error: ${response.status} ${response.statusText}`);
    }

    const result = await response.json();
    return {
      success: true,
      data: result,
    };
  } catch (error) {
    console.error('[ML API] Bulk assessment failed:', error);
    return {
      success: false,
      error: error.message,
    };
  }
}

/**
 * Download sample CSV template
 * @returns {Promise<Blob>} CSV file blob
 */
export async function downloadSampleCSV() {
  try {
    const response = await fetch(`${ML_API_BASE_URL}/sample_data.csv`);
    
    if (!response.ok) {
      throw new Error(`Sample download failed: ${response.status}`);
    }

    return await response.blob();
  } catch (error) {
    console.error('[ML API] Sample download failed:', error);
    throw error;
  }
}

/**
 * Convert debt record to ML API format
 * @param {Object} debt - Debt record from database
 * @returns {Object} Formatted data for ML API
 */
export function formatDebtForML(debt) {
  const debtAmount = debt.debtAmount || debt.amount || 0;
  const paidAmount = debt.paidAmount || 0;
  const dueDate = new Date(debt.dueDate);
  const currentDate = new Date();
  
  // Calculate payment delay (positive = late, negative = early)
  const paymentDelay = Math.floor((currentDate - dueDate) / (1000 * 60 * 60 * 24));
  
  return {
    DebtPaidRatio: debtAmount > 0 ? paidAmount / debtAmount : 0,
    PaymentDelay: paymentDelay,
    OutstandingDebt: Math.max(0, debtAmount - paidAmount),
    DebtAmount: debtAmount,
  };
}

/**
 * Get risk level color for UI display
 * @param {string} riskLevel - Risk level from ML API
 * @returns {string} Tailwind CSS color class
 */
export function getRiskLevelColor(riskLevel) {
  switch (riskLevel) {
    case 'Low Risk':
      return 'text-green-600 bg-green-50 border-green-200';
    case 'Medium Risk':
      return 'text-yellow-600 bg-yellow-50 border-yellow-200';
    case 'High Risk':
      return 'text-red-600 bg-red-50 border-red-200';
    default:
      return 'text-gray-600 bg-gray-50 border-gray-200';
  }
}
```

### **React Hook for ML Integration**

Create `hooks/use-ml-risk-assessment.js`:

```javascript
import { useState, useCallback } from 'react';
import { assessSingleRisk, formatDebtForML } from '../lib/services/ml/riskAssessment';

/**
 * Custom hook for ML risk assessment
 */
export function useMLRiskAssessment() {
  const [isAssessing, setIsAssessing] = useState(false);
  const [lastAssessment, setLastAssessment] = useState(null);
  const [error, setError] = useState(null);

  const assessRisk = useCallback(async (debtData) => {
    setIsAssessing(true);
    setError(null);

    try {
      // Format debt data for ML API if needed
      const mlData = typeof debtData.DebtPaidRatio !== 'undefined' 
        ? debtData 
        : formatDebtForML(debtData);

      const result = await assessSingleRisk(mlData);
      
      if (result.success) {
        setLastAssessment(result.data);
        return result.data;
      } else {
        setError(result.error);
        return null;
      }
    } catch (err) {
      setError(err.message);
      return null;
    } finally {
      setIsAssessing(false);
    }
  }, []);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  const clearAssessment = useCallback(() => {
    setLastAssessment(null);
    setError(null);
  }, []);

  return {
    assessRisk,
    isAssessing,
    lastAssessment,
    error,
    clearError,
    clearAssessment,
  };
}
```

---

## 🎨 **UI Components**

### **Risk Assessment Display Component**

Create `components/ml/RiskAssessmentDisplay.jsx`:

```jsx
import React from 'react';
import { Shield, TrendingUp, AlertTriangle, CheckCircle } from 'lucide-react';
import { Badge } from '../ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { getRiskLevelColor } from '../../lib/services/ml/riskAssessment';

export default function RiskAssessmentDisplay({ assessment, isLoading, className = '' }) {
  if (isLoading) {
    return (
      <Card className={`${className} animate-pulse`}>
        <CardContent className="flex items-center justify-center p-6">
          <div className="flex items-center gap-3">
            <div className="w-5 h-5 bg-blue-200 rounded animate-spin"></div>
            <span className="text-blue-600">Assessing risk...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!assessment) {
    return (
      <Card className={`${className} border-gray-200`}>
        <CardContent className="flex items-center justify-center p-6 text-gray-500">
          <div className="text-center">
            <Shield className="w-8 h-8 mx-auto mb-2 text-gray-400" />
            <p>No risk assessment available</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  const getRiskIcon = (riskLevel) => {
    switch (riskLevel) {
      case 'Low Risk':
        return <CheckCircle className="w-5 h-5" />;
      case 'Medium Risk':
        return <TrendingUp className="w-5 h-5" />;
      case 'High Risk':
        return <AlertTriangle className="w-5 h-5" />;
      default:
        return <Shield className="w-5 h-5" />;
    }
  };

  const riskColorClass = getRiskLevelColor(assessment.PredictedRiskLevel);

  return (
    <Card className={`${className} shadow-lg`}>
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center gap-2 text-lg">
          <Shield className="w-5 h-5 text-blue-600" />
          Risk Assessment
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {/* Risk Level Badge */}
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium text-gray-600">Risk Level:</span>
            <Badge className={`${riskColorClass} flex items-center gap-1 px-3 py-1`}>
              {getRiskIcon(assessment.PredictedRiskLevel)}
              {assessment.PredictedRiskLevel}
            </Badge>
          </div>

          {/* Risk Score */}
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium text-gray-600">Risk Score:</span>
            <span className="text-lg font-bold text-gray-900">
              {(assessment.RiskScore * 100).toFixed(1)}%
            </span>
          </div>

          {/* Risk Score Bar */}
          <div className="w-full bg-gray-200 rounded-full h-3">
            <div
              className={`h-3 rounded-full transition-all duration-300 ${
                assessment.RiskScore <= 0.3 
                  ? 'bg-green-500' 
                  : assessment.RiskScore <= 0.6 
                  ? 'bg-yellow-500' 
                  : 'bg-red-500'
              }`}
              style={{ width: `${Math.min(assessment.RiskScore * 100, 100)}%` }}
            ></div>
          </div>

          {/* Risk Description */}
          <div className="text-xs text-gray-500 bg-gray-50 p-3 rounded-lg">
            <strong>FairRisk Algorithm:</strong> This score is calculated using debt payment ratio, 
            payment delays, and outstanding amounts to provide transparent risk assessment.
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
```

---

## 🧪 **Testing Examples**

### **Example 1: Low Risk Customer**
```javascript
const lowRiskData = {
  DebtPaidRatio: 0.9,  // 90% paid
  PaymentDelay: -2,    // Paid 2 days early
  OutstandingDebt: 100,
  DebtAmount: 1000
};
// Expected: Low Risk (Score ≤ 0.3)
```

### **Example 2: High Risk Customer**
```javascript
const highRiskData = {
  DebtPaidRatio: 0.2,  // Only 20% paid
  PaymentDelay: 15,    // 15 days late
  OutstandingDebt: 800,
  DebtAmount: 1000
};
// Expected: High Risk (Score > 0.6)
```

### **Example 3: New Customer**
```javascript
const newCustomerData = {
  DebtPaidRatio: 0,    // No payment yet
  PaymentDelay: 0,     // Not due yet
  OutstandingDebt: 1000,
  DebtAmount: 1000
};
// Expected: High Risk (Score = 1.0)
```

---

## 📱 **Integration with ML Settings**

Update your ML technical settings form to include the real API URL:

```javascript
// In components/settings/ml-technical-settings-form.jsx
const [formData, setFormData] = useState({
  mlApiBaseUrl: 'https://deyncare-ml.onrender.com',
  mlApiKey: '', // Not required for public endpoints
  mlPredictEndpoint: '/predict_single/',
  mlPredictionTimeout: 30,
  riskDataRetentionDays: 365
});
```

---

## 🚀 **Production Deployment**

### **Environment Variables**
```env
# Production Settings
NEXT_PUBLIC_ML_API_URL=https://deyncare-ml.onrender.com
ML_API_TIMEOUT=30000
ML_ENABLE_CACHE=true
ML_CACHE_TTL=3600
```

### **Error Handling Best Practices**

```javascript
// Robust error handling with fallback
export async function assessRiskWithFallback(debtData) {
  try {
    const result = await assessSingleRisk(debtData);
    return result;
  } catch (error) {
    console.warn('[ML API] Primary endpoint failed, using fallback logic');
    
    // Fallback risk calculation
    const fallbackScore = calculateFallbackRisk(debtData);
    return {
      success: true,
      data: {
        RiskScore: fallbackScore,
        PredictedRiskLevel: fallbackScore > 0.6 ? 'High Risk' : 
                           fallbackScore > 0.3 ? 'Medium Risk' : 'Low Risk',
        source: 'fallback'
      }
    };
  }
}
```

---

## 📚 **Additional Resources**

- **API Documentation:** Visit `https://deyncare-ml.onrender.com/docs` for interactive Swagger UI
- **Sample Data:** Download from `https://deyncare-ml.onrender.com/sample_data.csv`
- **GitHub Repository:** Check the FastAPI ML service code structure
- **Deployment Guide:** Refer to `DEPLOYMENT.md` for hosting options

---

## 🎯 **Next Steps**

1. ✅ Integrate the risk assessment service into your debt management workflow
2. ✅ Add real-time risk updates when payments are recorded
3. ✅ Implement bulk risk assessment for existing debts
4. ✅ Add risk-based alerts and notifications
5. ✅ Create ML performance monitoring dashboard

---

**🔥 Your ML integration is now ready to transform debt management with intelligent risk assessment!** 🚀 