# 🛤️ User Registration Journey: Offline Payment Method

## Overview
This document outlines the complete user journey for customers registering with **offline payment methods** (Cash, Bank Transfer, Mobile Money, etc.) in the DeynCare system.

---

## 🎯 **PHASE 1: CUSTOMER REGISTRATION**

### **Step 1: Registration Form**
**Endpoint**: `POST /api/register/init`
**Customer Action**: Fills out registration form

```json
{
  "fullName": "<PERSON>",
  "email": "<EMAIL>",
  "phone": "+************",
  "password": "SecurePass123!",
  "shopName": "Ahmed's Electronics",
  "shopAddress": "Bakara Market, Mogadishu",
  "planType": "monthly",
  "paymentMethod": "offline",  // ← Offline payment selected
  "paymentDetails": {
    "preferredMethod": "Bank Transfer"
  }
}
```

### **Step 2: System Response**
**Result**: Initial registration created

```json
{
  "success": true,
  "message": "Registration initiated successfully",
  "data": {
    "userId": "usr_2024_001",
    "shopId": "shp_2024_001", 
    "subscriptionId": "sub_2024_001",
    "status": {
      "user": "pending_email_verification",
      "shop": "pending",
      "subscription": "pending_payment"
    },
    "nextStep": "email_verification",
    "verificationToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
  }
}
```

---

## 📧 **PHASE 2: EMAIL VERIFICATION**

### **Step 3: Email Verification**
**Customer Action**: Clicks verification link in email
**Endpoint**: `GET /api/register/verify-email?token=...`

### **Step 4: Verification Success**
**System Response**: Email verified, payment instructions displayed

```json
{
  "success": true,
  "message": "Email verified successfully",
  "data": {
    "user": {
      "status": "email_verified_pending_payment"
    },
    "paymentInstructions": {
      "method": "offline",
      "amount": 10.00,
      "currency": "USD",
      "referenceNumber": "DEYN-2024-001",
      "bankDetails": {
        "bankName": "Amal Bank",
        "accountNumber": "**********",
        "accountName": "DeynCare Ltd",
        "branch": "Main Branch"
      },
      "instructions": [
        "Make payment using reference number: DEYN-2024-001",
        "Include your shop name in payment description",
        "Keep receipt for verification",
        "Contact support after payment: +************"
      ]
    },
    "nextStep": "payment_confirmation"
  }
}
```

---

## 💰 **PHASE 3: OFFLINE PAYMENT**

### **Step 5: Customer Makes Payment**
**Customer Actions**:
1. 🏛️ Goes to bank/mobile money agent
2. 💸 Makes payment with reference number `DEYN-2024-001`
3. 📱 Keeps receipt/confirmation
4. 📞 Contacts DeynCare support or waits

**Payment Details**:
- **Amount**: $10.00 (monthly plan)
- **Reference**: DEYN-2024-001  
- **Method**: Bank Transfer/Cash/Mobile Money
- **Receipt**: Customer keeps proof

### **Step 6: Customer Status (Waiting)**
**System Status**:
```json
{
  "user": {
    "status": "email_verified_pending_payment",
    "canLogin": false
  },
  "shop": {
    "status": "pending",
    "isActive": false
  },
  "subscription": {
    "status": "pending_payment",
    "isPaid": false
  }
}
```

---

## 👨‍💼 **PHASE 4: SUPERADMIN VERIFICATION**

### **Step 7: SuperAdmin Reviews Pending Registrations**
**Endpoint**: `GET /api/register/admin/shops?status=pending`
**SuperAdmin Action**: Views pending registrations

```json
{
  "success": true,
  "data": {
    "pendingShops": [
      {
        "shopId": "shp_2024_001",
        "shopName": "Ahmed's Electronics",
        "ownerName": "Ahmed Hassan",
        "email": "<EMAIL>",
        "planType": "monthly",
        "amount": 10.00,
        "paymentMethod": "offline",
        "registeredAt": "2024-01-15T10:30:00Z",
        "referenceNumber": "DEYN-2024-001",
        "status": "pending_payment"
      }
    ]
  }
}
```

### **Step 8: SuperAdmin Verifies Payment**
**SuperAdmin Actions**:
1. 🔍 Checks bank records for reference `DEYN-2024-001`
2. ✅ Confirms payment of $10.00 received
3. 📝 Notes receipt details
4. 👍 Approves registration

### **Step 9: SuperAdmin Approval**
**Endpoint**: `POST /api/register/admin/approve-shop/shp_2024_001`
**SuperAdmin Action**: Approves with payment confirmation

```json
{
  "approvalNotes": "Payment verified via bank transfer",
  "confirmOfflinePayment": true,
  "offlinePaymentDetails": {
    "receiptNumber": "TXN789123456",
    "verifiedAmount": 10.00,
    "paymentDate": "2024-01-15T14:30:00Z",
    "verificationNotes": "Bank transfer confirmed, receipt verified",
    "paymentMethod": "Bank Transfer"
  }
}
```

---

## 🎉 **PHASE 5: ACTIVATION & COMPLETION**

### **Step 10: System Activation**
**System Actions**:
1. ✅ Updates subscription: `pending_payment` → `active`
2. ✅ Updates shop: `pending` → `active` 
3. ✅ Updates user: `email_verified_pending_payment` → `active`
4. 💰 Records payment confirmation
5. 📧 Sends welcome email

### **Step 11: SuperAdmin Response**
```json
{
  "success": true,
  "message": "Shop registration approved and activated successfully",
  "data": {
    "shop": {
      "id": "shp_2024_001",
      "name": "Ahmed's Electronics",
      "status": "active",
      "activatedAt": "2024-01-15T15:00:00Z"
    },
    "user": {
      "id": "usr_2024_001",
      "status": "active",
      "canLogin": true
    },
    "subscription": {
      "id": "sub_2024_001", 
      "status": "active",
      "isPaid": true,
      "startDate": "2024-01-15T15:00:00Z",
      "endDate": "2024-02-14T15:00:00Z"
    },
    "paymentConfirmed": true
  }
}
```

### **Step 12: Welcome Email Sent**
**Email Content**:
- 🎉 Welcome message
- 👤 Login credentials
- 🔗 Dashboard link
- 📞 Support contact
- 🛠️ Setup instructions

### **Step 13: Customer Can Login**
**Customer Actions**:
1. 📧 Receives welcome email
2. 🌐 Visits login page
3. 🔑 Enters credentials
4. 🎯 Accesses full system

---

## ⏱️ **TIMELINE BREAKDOWN**

| Phase | Duration | Status |
|-------|----------|--------|
| **Registration** | 5 minutes | Customer completes form |
| **Email Verification** | 5-30 minutes | Customer clicks email |
| **Payment Instructions** | Immediate | System shows payment details |
| **Customer Payment** | 1-24 hours | Customer makes offline payment |
| **Waiting Period** | 1-48 hours | Customer waits for approval |
| **Admin Verification** | 15-60 minutes | SuperAdmin verifies & approves |
| **System Activation** | Immediate | Automatic activation |
| **Customer Access** | Immediate | Customer can login |

**Total Time**: 2-72 hours (depending on customer payment speed and admin availability)

---

## 🔄 **KEY SYSTEM STATES**

### **User Status Progression**
```
pending_email_verification → 
  email_verified_pending_payment → 
    active
```

### **Shop Status Progression**  
```
pending → active
```

### **Subscription Status Progression**
```
pending_payment → active
```

### **Payment Status Progression**
```
isPaid: false → isPaid: true
```

---

## 📱 **CUSTOMER COMMUNICATION**

### **Registration Confirmation Email**
- ✅ Registration successful
- 📧 Email verification required
- ⏳ Next steps

### **Email Verification Success**
- ✅ Email verified
- 💰 Payment instructions
- 🏛️ Bank details
- 📞 Support contact

### **Welcome & Activation Email**
- 🎉 Account activated
- 🔑 Login credentials  
- 🌐 Dashboard link
- 🛠️ Getting started guide

---

## ⚠️ **ERROR SCENARIOS**

### **Payment Not Found**
If SuperAdmin cannot verify payment:
- 📞 Contact customer for proof
- 📄 Request receipt/screenshot
- 🔄 Customer can resubmit payment
- ⏳ Extended verification period

### **Incorrect Payment Amount**
If payment amount doesn't match:
- 💰 Request additional payment
- 💸 Process partial refund
- 🔄 Customer corrects payment

### **Expired Verification**
If too much time passes:
- 🔄 Regenerate verification
- 📧 Resend instructions
- 📞 Direct customer support

---

This journey ensures a smooth offline payment experience while maintaining security and verification standards. 