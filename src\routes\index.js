const express = require('express');
const router = express.Router();
const planRoutes = require('./planRoutes');
const subscriptionRoutes = require('./subscriptionRoutes');
const planExportRoutes = require('./export/planExportRoutes');
const subscriptionExportRoutes = require('./export/subscriptionExportRoutes');
const superAdminPaymentTransactionRoutes = require('./superAdmin/paymentTransactionRoutes');

// Plan routes
router.use('/plans', planRoutes);
router.use('/plans/export', planExportRoutes);

// Subscription routes
router.use('/subscriptions', subscriptionRoutes);
router.use('/subscriptions/export', subscriptionExportRoutes);

// SuperAdmin Payment Transaction routes
router.use('/superadmin/payment-transactions', superAdminPaymentTransactionRoutes);

module.exports = router; 