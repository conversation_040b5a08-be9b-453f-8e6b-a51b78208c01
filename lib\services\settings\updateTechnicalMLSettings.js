/**
 * Update Technical ML Settings Service
 * 
 * Updates SuperAdmin-only technical ML configuration settings
 * Maps to backend settings: ml_api_base_url, ml_api_key, ml_predict_endpoint, ml_prediction_timeout, risk_data_retention_days
 */
import { updateSettingByKey } from '../../api/modules/settings';
import { handleError, handleSuccess } from '../baseService';

/**
 * Update technical ML settings that SuperAdmin users can manage
 * @param {Object} technicalMLSettings - Technical ML settings to update
 * @param {string} technicalMLSettings.mlApiBaseUrl - ML API base URL
 * @param {string} technicalMLSettings.mlApiKey - ML API authentication key
 * @param {string} technicalMLSettings.mlPredictEndpoint - ML prediction endpoint path
 * @param {number} technicalMLSettings.mlPredictionTimeout - ML prediction timeout in seconds
 * @param {number} technicalMLSettings.riskDataRetentionDays - Risk data retention in days
 * @returns {Object} API response data
 */
const updateTechnicalMLSettings = async (technicalMLSettings) => {
  try {
    const updatePromises = [];
    
    // Update individual technical ML settings using the settingByKey endpoint
    if (technicalMLSettings.mlApiBaseUrl !== undefined) {
      updatePromises.push(
        updateSettingByKey('ml_api_base_url', technicalMLSettings.mlApiBaseUrl)
      );
    }
    
    if (technicalMLSettings.mlApiKey !== undefined) {
      updatePromises.push(
        updateSettingByKey('ml_api_key', technicalMLSettings.mlApiKey)
      );
    }
    
    if (technicalMLSettings.mlPredictEndpoint !== undefined) {
      updatePromises.push(
        updateSettingByKey('ml_predict_endpoint', technicalMLSettings.mlPredictEndpoint)
      );
    }
    
    if (technicalMLSettings.mlPredictionTimeout !== undefined) {
      updatePromises.push(
        updateSettingByKey('ml_prediction_timeout', Number(technicalMLSettings.mlPredictionTimeout))
      );
    }
    
    if (technicalMLSettings.riskDataRetentionDays !== undefined) {
      updatePromises.push(
        updateSettingByKey('risk_data_retention_days', Number(technicalMLSettings.riskDataRetentionDays))
      );
    }
    
    // Execute all updates in parallel
    const results = await Promise.all(updatePromises);
    
    handleSuccess('Technical ML settings updated successfully');
    
    return {
      success: true,
      message: 'Technical ML settings updated successfully',
      updatedSettings: results.length
    };
  } catch (error) {
    handleError(error, 'SettingsService.updateTechnicalMLSettings', true);
    return { success: false, error: error.message };
  }
};

export default updateTechnicalMLSettings; 