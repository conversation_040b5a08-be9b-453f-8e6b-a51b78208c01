/**
 * Settings Context - General Settings Actions
 * Contains actions for managing general settings
 */

import { fetchSettings as fetchSettingsService, updateSettings as updateSettingsService } from '@/lib/services/settings';
import { ACTION_TYPES } from '../types';

/**
 * Fetch all settings
 * @param {Function} dispatch - Reducer dispatch function 
 * @param {Function} throttleRequest - Request throttling function
 * @param {string} category - Optional category filter
 * @returns {Promise} Result of the operation
 */
export const fetchSettings = (dispatch, throttleRequest, category = 'all') => {
  return throttleRequest('fetchSettings', async () => {
    try {
      dispatch({ type: ACTION_TYPES.SET_LOADING, payload: true });
      const settings = await fetchSettingsService(category);
      dispatch({ type: ACTION_TYPES.SET_SETTINGS, payload: settings });
      dispatch({ type: ACTION_TYPES.SET_ERROR, payload: null }); // Clear any previous errors
      return { success: true, data: settings };
    } catch (error) {
      console.error('Settings fetch error:', error);
      const errorMessage = error.response?.status === 500 
        ? 'Server error loading settings. Using cached data if available.'
        : error.message || 'Failed to load settings';
      
      dispatch({ type: ACTION_TYPES.SET_ERROR, payload: errorMessage });
      return { success: false, error: errorMessage };
    }
  });
};

/**
 * Update general settings
 * @param {Function} dispatch - Reducer dispatch function
 * @param {Function} throttleRequest - Request throttling function
 * @param {Object} updatedSettings - Updated settings object
 * @returns {Promise} Result of the operation
 */
export const updateSettings = (dispatch, throttleRequest, updatedSettings) => {
  return throttleRequest('updateSettings', async () => {
    try {
      dispatch({ type: ACTION_TYPES.SET_LOADING, payload: true });
      const result = await updateSettingsService(updatedSettings);
      dispatch({ type: ACTION_TYPES.UPDATE_SETTINGS, payload: updatedSettings });
      dispatch({ type: ACTION_TYPES.SET_ERROR, payload: null }); // Clear any previous errors
      return { success: true, data: result };
    } catch (error) {
      console.error('Settings update error:', error);
      const errorMessage = error.response?.status === 500 
        ? 'Server error updating settings. Please try again later.'
        : error.message || 'Failed to update settings';
      
      dispatch({ type: ACTION_TYPES.SET_ERROR, payload: errorMessage });
      return { success: false, error: errorMessage };
    } finally {
      dispatch({ type: ACTION_TYPES.SET_LOADING, payload: false });
    }
  });
};

/**
 * Clear any error in state
 * @param {Function} dispatch - Reducer dispatch function
 */
export const clearError = (dispatch) => {
  dispatch({ type: ACTION_TYPES.CLEAR_ERROR });
};
