# SuperAdmin Integration Test Plan

## New SuperAdmin Registration Capabilities

### 1. Create Shop with Admin (SuperAdmin)
**Endpoint**: `POST /api/register/admin/create-shop`

```bash
# Test SuperAdmin creating a shop (SAME payload as public registration)
curl -X POST http://localhost:3000/api/register/admin/create-shop \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_SUPERADMIN_TOKEN" \
  -d '{
    "fullName": "Shop Owner Name",
    "email": "<EMAIL>",
    "phone": "+1234567890",
    "password": "SecurePassword123!",
    "shopName": "Test Shop Created by SuperAdmin",
    "shopAddress": "123 Test Street, Test City",
    "planType": "monthly",
    "registeredBy": "superAdmin",
    "paymentMethod": "admin_created",
    "initialPaid": true
  }'
```

**Expected Response**:
```json
{
  "success": true,
  "message": "Shop and user created successfully by <PERSON><PERSON><PERSON><PERSON>",
  "data": {
    "user": {
      "userId": "user_xxxxxxxxx",
      "fullName": "Shop Owner Name",
      "email": "<EMAIL>",
      "role": "admin",
      "status": "active"
    },
    "shop": {
      "id": "shop_xxxxxxxxx",
      "name": "Test Shop Created by SuperAdmin"
    },
    "nextStep": "registration_complete"
  }
}
```

### 2. Create Admin for Existing Shop (SuperAdmin)
**Endpoint**: `POST /api/register/admin/create-admin`

```bash
# Test SuperAdmin creating admin for existing shop
curl -X POST http://localhost:3000/api/register/admin/create-admin \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_SUPERADMIN_TOKEN" \
  -d '{
    "shopId": "existing_shop_id",
    "adminFullName": "New Admin Name",
    "adminEmail": "<EMAIL>", 
    "adminPhone": "+1234567891",
    "adminPassword": "SecurePassword123!",
    "replaceExistingAdmin": false
  }'
```

### 3. Approve Shop Registration (SuperAdmin)
**Endpoint**: `POST /api/register/admin/approve-shop/:shopId`

```bash
# Test SuperAdmin approving a pending shop
curl -X POST http://localhost:3000/api/register/admin/approve-shop/shop_xxxxxxxxx \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_SUPERADMIN_TOKEN" \
  -d '{
    "approvalNotes": "Shop approved after manual review",
    "activateImmediately": true
  }'
```

## System Integration Tests

### Test 1: Verify Old Registration is Disabled
```bash
# This should return 404 or method not allowed
curl -X POST http://localhost:3000/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "fullName": "Test User",
    "email": "<EMAIL>"
  }'
```

### Test 2: Verify New Registration Still Works
```bash
# Public registration should still work
curl -X POST http://localhost:3000/api/register/init \
  -H "Content-Type: application/json" \
  -d '{
    "fullName": "Public User",
    "email": "<EMAIL>",
    "phone": "+1234567892",
    "password": "SecurePassword123!",
    "shopName": "Public Shop",
    "shopAddress": "456 Public Street",
    "planType": "trial"
  }'
```

### Test 3: Role Hierarchy Verification
```bash
# Non-SuperAdmin should be rejected
curl -X POST http://localhost:3000/api/register/admin/create-shop \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer ADMIN_OR_EMPLOYEE_TOKEN" \
  -d '{
    "shopName": "Should Fail",
    "adminEmail": "<EMAIL>"
  }'
```

## Success Criteria

✅ **Old registration disabled**: `/api/auth/register` returns error
✅ **New registration works**: `/api/register/*` endpoints functional
✅ **SuperAdmin can create shops**: All shop creation endpoints work
✅ **Role validation**: Non-SuperAdmins are rejected
✅ **Email verification**: New admins receive verification emails
✅ **Transaction integrity**: Failures don't leave partial data
✅ **Logging**: All operations are properly logged

## Validation Points

1. **Database Consistency**: 
   - Shops created by SuperAdmin have correct status
   - Admin users have proper role and shop association
   - No orphaned records after failures

2. **Security**:
   - Only SuperAdmins can access admin endpoints
   - Password validation enforced
   - Proper authentication required

3. **Email Flow**:
   - Welcome emails sent to new admins
   - Verification codes generated correctly
   - Email failures don't break registration

4. **Business Logic**:
   - SuperAdmin-created shops are pre-approved
   - Payment status correctly set for admin-created shops
   - Subscription status properly initialized

## Post-Integration Cleanup

After successful testing:
1. Remove commented-out old registration route
2. Archive old registration controller files  
3. Update API documentation
4. Update frontend to use new endpoints
5. Create migration guide for existing integrations 