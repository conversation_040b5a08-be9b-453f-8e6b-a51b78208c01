import apiBridge from '@/lib/api/bridge';
import { ENDPOINTS } from '@/lib/api/contract';
import { handleError, processApiResponse } from '../baseService';

/**
 * Test Firebase connection (SuperAdmin only)
 * @returns {Promise<Object>} Connection test result
 */
async function testFirebaseConnection() {
  // Make API request using the bridge (no cache for testing)
  const response = await apiBridge.get(ENDPOINTS.NOTIFICATIONS.PUSH.TEST, {
    skipCache: true
  });

  // Process response without showing success message here (let component handle it)
  const result = processApiResponse(response, null);
  return result;
}

export default testFirebaseConnection; 