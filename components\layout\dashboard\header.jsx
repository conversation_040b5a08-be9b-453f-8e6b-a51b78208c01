"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { cn } from "@/lib/utils";
import { useResponsive } from "@/hooks/use-responsive";
import { ThemeToggleFixed } from "@/components/ui/theme-toggle-fixed";
import { useAuth } from "@/contexts/auth-context";
import { 
  Bell, 
  Menu,
  Search,
  User,
  ChevronDown,
  LogOut,
  Settings,
  ChevronRight
} from "lucide-react";
import { GlobalSearch } from "@/components/dashboard/common/global-search";

export function Header({ className, onToggleSidebar }) {
  const { user, logout } = useAuth();
  const pathname = usePathname();
  const { isMobile } = useResponsive();
  const [scrolled, setScrolled] = useState(false);
  const [userMenuOpen, setUserMenuOpen] = useState(false);
  const [currentTime, setCurrentTime] = useState(new Date());

  // Update time every minute
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 60000);
    return () => clearInterval(timer);
  }, []);

  // Create a more dramatic blur effect with glass-like appearance
  useEffect(() => {
    const handleScroll = () => {
      // Find the main scroll container instead of using window scroll
      const mainScrollContainer = document.querySelector('.single-scroll-container');
      const scrollPosition = mainScrollContainer ? mainScrollContainer.scrollTop : window.scrollY;
      setScrolled(scrollPosition > 5); // Lower threshold for earlier effect
    };

    // Listen to scroll on the main container if it exists, otherwise fallback to window
    const mainScrollContainer = document.querySelector('.single-scroll-container');
    const scrollTarget = mainScrollContainer || window;

    scrollTarget.addEventListener("scroll", handleScroll);
    handleScroll(); // Check initial scroll position
    return () => scrollTarget.removeEventListener("scroll", handleScroll);
  }, []);

  // Format date and time
  const formattedDate = new Intl.DateTimeFormat('en-US', {
    weekday: 'short',
    month: 'short',
    day: 'numeric',
    year: 'numeric'
  }).format(currentTime);
  
  const formattedTime = new Intl.DateTimeFormat('en-US', {
    hour: 'numeric',
    minute: 'numeric',
    hour12: true
  }).format(currentTime);

  // Generate breadcrumbs from pathname
  const generateBreadcrumbs = () => {
    if (pathname === "/dashboard") {
      return (
        <div className="flex items-center text-sm">
          <span className="font-medium">Dashboard</span>
        </div>
      );
    }

    const segments = pathname.split('/').filter(Boolean);
    return (
      <div className="flex items-center space-x-1 text-sm">
        <Link href="/dashboard" className="text-muted-foreground hover:text-foreground">
          Dashboard
        </Link>
        {segments.slice(1).map((segment, index) => {
          const segmentPath = `/${segments.slice(0, index + 2).join('/')}`;
          const isLast = index === segments.slice(1).length - 1;
          const formattedSegment = segment.charAt(0).toUpperCase() + segment.slice(1);
          
          return (
            <div key={segment} className="flex items-center">
              <ChevronRight className="h-4 w-4 text-muted-foreground mx-1" />
              {isLast ? (
                <span className="font-medium">{formattedSegment}</span>
              ) : (
                <Link href={segmentPath} className="text-muted-foreground hover:text-foreground">
                  {formattedSegment}
                </Link>
              )}
            </div>
          );
        })}
      </div>
    );
  };



  return (
    <div className={cn("sticky top-0 z-50 w-full", className)}>
      {/* Glass overlay with blur effect */}
      {scrolled && (
        <div 
          className="absolute inset-0 w-full h-full bg-background/20 dark:bg-background/10 backdrop-blur-2xl z-[-1]" 
          style={{
            boxShadow: "0 10px 30px -10px rgba(0, 0, 0, 0.1)",
            borderBottom: "1px solid rgba(255, 255, 255, 0.1)"
          }}
        />
      )}
      
      <header 
        className={cn(
          "flex h-16 w-full items-center justify-between px-4 transition-all duration-300 z-50",
          scrolled 
            ? "bg-transparent" 
            : "bg-background border-b border-border"
        )}
      >
        {/* Left section - Mobile menu toggle & breadcrumbs */}
        <div className="flex items-center gap-4">
          {isMobile && (
            <button
              onClick={onToggleSidebar}
              className="rounded-md p-2 text-muted-foreground hover:bg-muted"
              aria-label="Toggle menu"
            >
              <Menu className="h-5 w-5" />
            </button>
          )}
          
          {generateBreadcrumbs()}
        </div>
        
        {/* Center section - Search */}
        <div className="hidden md:block flex-1 mx-4 max-w-md">
          <GlobalSearch />
        </div>

        {/* Right section - date/time, theme toggle, notifications, user menu */}
        <div className="flex items-center gap-3">
          {/* Date and time */}
          <div className="hidden md:flex flex-col items-end mr-2">
            <span className="text-xs text-muted-foreground">{formattedDate}</span>
            <span className="text-sm font-medium">{formattedTime}</span>
          </div>
          
          {/* Theme toggle */}
          <ThemeToggleFixed />
          
          {/* Notifications */}
          <button className="relative rounded-full p-2 text-muted-foreground hover:bg-muted hover:text-foreground">
            <Bell className="h-5 w-5" />
            <span className="absolute top-1 right-1 h-2 w-2 rounded-full bg-primary"></span>
          </button>
          
          {/* User menu */}
          <div className="relative">
            <button
              onClick={() => setUserMenuOpen(!userMenuOpen)}
              className="flex items-center gap-2 rounded-md p-2 text-sm font-medium transition-colors hover:bg-muted"
            >
              <div className="h-8 w-8 rounded-full bg-primary/10 flex items-center justify-center text-primary">
                {user?.fullName ? user.fullName.charAt(0).toUpperCase() : "U"}
              </div>
              <div className="hidden md:block text-left">
                <p className="font-medium">{user?.fullName || "User"}</p>
                <p className="text-xs text-muted-foreground">{user?.role || "User"}</p>
              </div>
              <ChevronDown className="h-4 w-4 text-muted-foreground" />
            </button>
            
            {userMenuOpen && (
              <div className="absolute right-0 mt-1 w-56 rounded-md border border-border bg-background shadow-lg">
                <div className="p-2">
                  <div className="border-b border-border pb-2 pt-1 px-2 md:hidden">
                    <p className="font-medium">{user?.fullName || "User"}</p>
                    <p className="text-xs text-muted-foreground">{user?.email || "<EMAIL>"}</p>
                  </div>
                  <Link
                    href="/dashboard/profile"
                    className="flex items-center gap-2 rounded-md px-3 py-2 text-sm hover:bg-muted"
                    onClick={() => setUserMenuOpen(false)}
                  >
                    <User className="h-4 w-4" />
                    Profile
                  </Link>
                  <Link
                    href="/dashboard/settings"
                    className="flex items-center gap-2 rounded-md px-3 py-2 text-sm hover:bg-muted"
                    onClick={() => setUserMenuOpen(false)}
                  >
                    <Settings className="h-4 w-4" />
                    Settings
                  </Link>
                  <hr className="my-1 border-border" />
                  <button
                    onClick={() => {
                      setUserMenuOpen(false);
                      logout();
                    }}
                    className="flex w-full items-center gap-2 rounded-md px-3 py-2 text-sm hover:bg-destructive/10 hover:text-destructive"
                  >
                    <LogOut className="h-4 w-4" />
                    Logout
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      </header>
    </div>
  );
}
