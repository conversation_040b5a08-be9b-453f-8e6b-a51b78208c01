# Backend-Flutter API Match: Authentication & Registration

This document details the precise interaction points and expected behavior between the DeynCare Flutter application (Frontend) and the DeynCare Backend API for the user registration and authentication flow, with a specific focus on subscription `planType`, `paymentMethod`, `initialPaid`, and `discountCode`.

## 1. Registration (`POST /api/auth/register`)

### Frontend (Flutter) Request Payload:

The Flutter application is expected to send the following payload (or a subset) to the `/api/auth/register` endpoint:

```json
{
  "fullName": "John Doe",
  "email": "<EMAIL>",
  "phone": "+252631234567",
  "password": "SecurePassword123",
  "shopName": "John's Awesome Shop",
  "shopAddress": "123 Main St, City, Country",
  "planType": "monthly", // e.g., "trial", "monthly", "yearly"
  "paymentMethod": "EVC Plus", // e.g., "offline", "EVC Plus", "Mobile Money"
  "initialPaid": true, // **CRITICAL: Set to `true` to trigger immediate payment attempts on backend for online methods.**
  "paymentDetails": { // Required if `initialPaid` is true and `paymentMethod` is online
    "phoneNumber": "+252631234567" // For EVC Plus/Mobile Money
    // Other payment gateway specific details might go here
  },
  "discountCode": "DISCOUNT20" // Optional
}
```

### Backend (Node.js) Logic (`registrationController.js`):

The backend's `register` function processes the incoming request as follows:

1.  **Input Validation**: Ensures all required fields are present and correctly formatted.
2.  **Payment Method Availability Check**: Verifies if the `paymentMethod` provided is enabled in the global settings (`payment_methods_available`, `enable_online_payment`, `enable_offline_payment`). If not, it returns an error.
3.  **Shop Logo Upload (Optional)**: If a shop logo file is included in the request, it's processed and saved.
4.  **Discount Code Verification**: If `discountCode` is provided, `DiscountService.verifyDiscountCode` is called. If the code is invalid, an `AppError` is thrown, aborting registration. If valid, `discountDetails` are stored.
5.  **Initial Data Creation**: A new `shopData` object and `userData` object are prepared. Crucially:
    *   `shopData.status` is set to `'pending'`.
    *   `shopData.access.isPaid` is set to `false` and `shopData.access.isActivated` to `false`.
    *   `shopData.subscription.status` is set to `'pending_payment'`.
    *   `userData.status` is set to `'pending_email_verification'`.
    *   A `verificationCode` is generated for the user.
6.  **Payment Processing (Conditional)**:
    *   **Trigger**: This block is executed only if `paymentMethod` is an online method (e.g., `'EVC Plus'`) AND `initialPaid` is `true` in the request payload, AND necessary `paymentDetails` (like `phoneNumber`) are present.
    *   **Price Calculation**: The `planPrice` is determined based on `planType` and adjusted by `discountDetails` if applicable.
    *   **External Payment Call**: An external service (e.g., `EVCPaymentService.retryPayment`) is called to process the payment.
    *   **Payment Outcome**: 
        *   If payment is successful, `paymentDetails.transactionId` is updated within `shopData.subscription`.
        *   If payment fails (e.g., API error, insufficient funds), the error is logged, but **registration proceeds**. `shopData.subscription.initialPaid` might be reset to `false`, and the subscription status remains `'pending_payment'`.
7.  **Database Creation**: The `shopData` and `userData` are saved to the database.
8.  **Email Verification Notification**: An email containing the `verificationCode` is sent to the registered user's email address. The user is now in a `pending_email_verification` state.

### Frontend (Flutter) Response Handling & Navigation:

After successfully sending the registration request, the Flutter application should expect a success response from the backend. The frontend's navigation flow should be:

1.  **Successful Registration (Backend `201 Created` response)**: Navigate the user directly to the **Email Verification Screen**. The backend's successful response does *not* imply immediate dashboard access or a "paid" status. It indicates that the account has been created and is awaiting email verification.
2.  **Payment Not Attempted/Failed at Registration**: If `initialPaid` was `false` (or payment failed during registration), the user remains in `pending_payment` status. The Flutter app might later guide the user to a subscription/payment management screen *after* email verification and login.
3.  **Payment Success (Backend `201 Created` response with payment details)**: Even if payment was successful, the **next mandatory step is still email verification**. The Flutter app should navigate to the Email Verification Screen. Once verified and logged in, the user will have access to the dashboard, and their subscription status will reflect the successful payment.

## 2. Post-Registration Flow:

1.  **Email Verification**: The user receives an email with a verification code. They must use this code in the Flutter app's verification screen to activate their account.
2.  **Login**: After successful email verification, the user can log in. The backend will authenticate them and return `AuthToken` and `User` models.
3.  **Dashboard Access**: Upon successful login, the Flutter app navigates to the dashboard. The app can then check the user's `access.isPaid` and `shop.subscription.status` to determine their current subscription status and guide them to payment if necessary.

## Summary of Key Integration Points:

*   **`initialPaid` Flag**: The Flutter frontend **must** accurately set `initialPaid: true` if an immediate payment is intended with an online payment method. This is currently hardcoded to `false` on the Flutter side.
*   **Post-Registration Navigation**: Always navigate to the Email Verification screen immediately after a successful registration API call, regardless of whether payment was attempted or successful during registration.
*   **Payment Retry/Management**: The Flutter app will need a separate flow for users whose `initialPaid` was `false` or whose payment failed during registration, allowing them to manage their subscription and complete payment after email verification and login. 