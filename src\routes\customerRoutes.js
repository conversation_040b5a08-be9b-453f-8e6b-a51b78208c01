const express = require('express');
const router = express.Router();

// Middleware
const { authenticate, authorize, checkModulePermission } = require('../middleware/authMiddleware');
const { checkUserStatusAccess } = require('../middleware/userStatusMiddleware');
const { validate, validateQuery, validateParams } = require('../middleware/validationMiddleware');
const { customerValidation } = require('../validations/schemas/customerSchemas');

// Controllers
const createCustomer = require('../controllers/customer/createCustomer');
const getAllCustomers = require('../controllers/customer/getAllCustomers');
const getCustomerById = require('../controllers/customer/getCustomerById');
const updateCustomer = require('../controllers/customer/updateCustomer');
const deleteCustomer = require('../controllers/customer/deleteCustomer');
const getCustomerStats = require('../controllers/customer/getCustomerStats');
const getCustomerDebts = require('../controllers/customer/getCustomerDebts');

// All routes require admin authentication (shop owner role)
router.use(authenticate);
router.use(checkUserStatusAccess); // Check user status for pending payment users
router.use(authorize(['admin'])); // Admin = Shop Owner

/**
 * @route   POST /api/customers
 * @desc    Create new customer record
 * @access  Admin (Shop Owner)
 */
router.post('/', 
  authenticate,
  authorize(['admin', 'employee']),
  checkModulePermission('customerManagement', 'create'),
  validate(customerValidation.createCustomer),
  createCustomer
);

/**
 * @route   GET /api/customers
 * @desc    Get all customers for shop with risk profiles
 * @access  Admin (Shop Owner)
 */
router.get('/', 
  authenticate,
  authorize(['admin', 'employee']),
  checkModulePermission('customerManagement', 'view'),
  validateQuery(customerValidation.getCustomers),
  getAllCustomers
);

/**
 * @route   GET /api/customers/stats
 * @desc    Get customer statistics and risk distribution
 * @access  Admin (Shop Owner)
 */
router.get('/stats', getCustomerStats);

/**
 * @route   GET /api/customers/:customerId
 * @desc    Get single customer with full profile (enhanced for mobile with ?mobile=true)
 * @access  Admin (Shop Owner)
 */
router.get('/:customerId',
  authenticate,
  authorize(['admin', 'employee']),
  checkModulePermission('customerManagement', 'view'),
  validateParams(customerValidation.getCustomerById),
  getCustomerById
);

/**
 * @route   PUT /api/customers/:customerId
 * @desc    Update customer information
 * @access  Admin (Shop Owner)
 */
router.put('/:customerId',
  authenticate,
  authorize(['admin', 'employee']),
  checkModulePermission('customerManagement', 'update'),
  validateParams(customerValidation.updateCustomer),
  validate(customerValidation.updateCustomer),
  updateCustomer
);

/**
 * @route   DELETE /api/customers/:customerId
 * @desc    Delete customer record (soft delete)
 * @access  Admin (Shop Owner)
 */
router.delete('/:customerId',
  authenticate,
  authorize(['admin', 'employee']),
  checkModulePermission('customerManagement', 'delete'),
  validateParams(customerValidation.deleteCustomer),
  deleteCustomer
);

/**
 * @route   GET /api/customers/:customerId/debts
 * @desc    Get all debts for specific customer
 * @access  Admin (Shop Owner)
 */
router.get('/:customerId/debts', 
  validateParams(customerValidation.getCustomerDebts),
  validateQuery(customerValidation.getCustomerDebts),
  getCustomerDebts
);

module.exports = router; 
