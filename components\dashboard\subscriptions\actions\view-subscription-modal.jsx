'use client';

import React from 'react';
import {
  Dialog,
  Dialog<PERSON>ontent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { 
  Calendar, 
  CreditCard, 
  User, 
  Building, 
  Clock, 
  DollarSign, 
  Settings,
  FileText,
  AlertCircle,
  CheckCircle,
  XCircle
} from 'lucide-react';

const ViewSubscriptionModal = ({ isOpen, onClose, subscription }) => {
  if (!subscription) return null;

  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const formatCurrency = (amount, currency = 'USD') => {
    if (amount === null || amount === undefined) return 'N/A';
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency
    }).format(amount);
  };

  const getStatusBadge = (status) => {
    const statusConfig = {
      active: { color: 'bg-green-100 text-green-800', icon: CheckCircle },
      trial: { color: 'bg-blue-100 text-blue-800', icon: Clock },
      past_due: { color: 'bg-yellow-100 text-yellow-800', icon: AlertCircle },
      canceled: { color: 'bg-red-100 text-red-800', icon: XCircle },
      expired: { color: 'bg-gray-100 text-gray-800', icon: XCircle }
    };

    const config = statusConfig[status] || statusConfig.active;
    const Icon = config.icon;

    return (
      <Badge className={`${config.color} flex items-center gap-1`}>
        <Icon className="h-3 w-3" />
        {status?.charAt(0).toUpperCase() + status?.slice(1) || 'Unknown'}
      </Badge>
    );
  };

  const getAutoRenewalBadge = (autoRenew) => {
    return (
      <Badge className={autoRenew ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}>
        {autoRenew ? 'Enabled' : 'Disabled'}
      </Badge>
    );
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Subscription Details
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Header Info */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span>Subscription #{subscription.subscriptionId}</span>
                {getStatusBadge(subscription.status)}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div>
                  <p className="text-sm text-gray-500">Created</p>
                  <p className="font-medium">{formatDate(subscription.createdAt)}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-500">Last Updated</p>
                  <p className="font-medium">{formatDate(subscription.updatedAt)}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-500">Shop ID</p>
                  <p className="font-medium">{subscription.shopId || 'N/A'}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-500">Plan ID</p>
                  <p className="font-medium">{subscription.planId || 'N/A'}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Plan Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Building className="h-4 w-4" />
                  Plan Information
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div>
                  <p className="text-sm text-gray-500">Plan Name</p>
                  <p className="font-medium">{subscription.plan?.name || 'N/A'}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-500">Plan Type</p>
                  <Badge variant="outline">
                    {subscription.plan?.type?.toUpperCase() || 'N/A'}
                  </Badge>
                </div>
                <div>
                  <p className="text-sm text-gray-500">Billing Cycle</p>
                  <p className="font-medium">{subscription.pricing?.billingCycle || 'N/A'}</p>
                </div>
              </CardContent>
            </Card>

            {/* Pricing Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <DollarSign className="h-4 w-4" />
                  Pricing Information
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div>
                  <p className="text-sm text-gray-500">Base Price</p>
                  <p className="font-medium">{formatCurrency(subscription.pricing?.basePrice, subscription.pricing?.currency)}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-500">Currency</p>
                  <p className="font-medium">{subscription.pricing?.currency || 'USD'}</p>
                </div>
                {subscription.pricing?.discount?.active && (
                  <div>
                    <p className="text-sm text-gray-500">Active Discount</p>
                    <div className="space-y-1">
                      <p className="font-medium text-green-600">
                        {subscription.pricing.discount.type === 'percentage' 
                          ? `${subscription.pricing.discount.value}% off`
                          : formatCurrency(subscription.pricing.discount.amount)
                        }
                      </p>
                      {subscription.pricing.discount.code && (
                        <p className="text-xs text-gray-500">Code: {subscription.pricing.discount.code}</p>
                      )}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Payment Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <CreditCard className="h-4 w-4" />
                  Payment Information
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div>
                  <p className="text-sm text-gray-500">Payment Method</p>
                  <Badge variant="outline">
                    {subscription.payment?.method?.replace('_', ' ').toUpperCase() || 'N/A'}
                  </Badge>
                </div>
                <div>
                  <p className="text-sm text-gray-500">Payment Status</p>
                  <Badge className={subscription.payment?.verified ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'}>
                    {subscription.payment?.verified ? 'Verified' : 'Pending'}
                  </Badge>
                </div>
                <div>
                  <p className="text-sm text-gray-500">Last Payment</p>
                  <p className="font-medium">{formatDate(subscription.payment?.lastPaymentDate)}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-500">Next Payment</p>
                  <p className="font-medium">{formatDate(subscription.payment?.nextPaymentDate)}</p>
                </div>
                {subscription.payment?.failedPayments > 0 && (
                  <div>
                    <p className="text-sm text-gray-500">Failed Payments</p>
                    <p className="font-medium text-red-600">{subscription.payment.failedPayments}</p>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Period & Renewal */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Calendar className="h-4 w-4" />
                  Period & Renewal
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div>
                  <p className="text-sm text-gray-500">Start Date</p>
                  <p className="font-medium">{formatDate(subscription.dates?.startDate)}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-500">End Date</p>
                  <p className="font-medium">{formatDate(subscription.dates?.endDate)}</p>
                </div>
                {subscription.dates?.trialEndsAt && (
                  <div>
                    <p className="text-sm text-gray-500">Trial Ends</p>
                    <p className="font-medium">{formatDate(subscription.dates.trialEndsAt)}</p>
                  </div>
                )}
                <div>
                  <p className="text-sm text-gray-500">Auto Renewal</p>
                  {getAutoRenewalBadge(subscription.renewalSettings?.autoRenew)}
                </div>
                {subscription.renewalSettings?.renewalAttempts > 0 && (
                  <div>
                    <p className="text-sm text-gray-500">Renewal Attempts</p>
                    <p className="font-medium">{subscription.renewalSettings.renewalAttempts}</p>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Payment Details */}
          {subscription.payment?.paymentDetails && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <User className="h-4 w-4" />
                  Payment Details
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  {subscription.payment.paymentDetails.transactionId && (
                    <div>
                      <p className="text-sm text-gray-500">Transaction ID</p>
                      <p className="font-medium font-mono text-sm">{subscription.payment.paymentDetails.transactionId}</p>
                    </div>
                  )}
                  {subscription.payment.paymentDetails.payerName && (
                    <div>
                      <p className="text-sm text-gray-500">Payer Name</p>
                      <p className="font-medium">{subscription.payment.paymentDetails.payerName}</p>
                    </div>
                  )}
                  {subscription.payment.paymentDetails.payerEmail && (
                    <div>
                      <p className="text-sm text-gray-500">Payer Email</p>
                      <p className="font-medium">{subscription.payment.paymentDetails.payerEmail}</p>
                    </div>
                  )}
                  {subscription.payment.paymentDetails.payerPhone && (
                    <div>
                      <p className="text-sm text-gray-500">Payer Phone</p>
                      <p className="font-medium">{subscription.payment.paymentDetails.payerPhone}</p>
                    </div>
                  )}
                </div>
                {subscription.payment.paymentDetails.notes && (
                  <div className="mt-4">
                    <p className="text-sm text-gray-500">Notes</p>
                    <p className="font-medium">{subscription.payment.paymentDetails.notes}</p>
                  </div>
                )}
              </CardContent>
            </Card>
          )}

          {/* Cancellation Info */}
          {subscription.status === 'canceled' && subscription.cancellation && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-red-600">
                  <XCircle className="h-4 w-4" />
                  Cancellation Information
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                {subscription.cancellation.reason && (
                  <div>
                    <p className="text-sm text-gray-500">Reason</p>
                    <p className="font-medium">{subscription.cancellation.reason}</p>
                  </div>
                )}
                {subscription.cancellation.feedback && (
                  <div>
                    <p className="text-sm text-gray-500">Feedback</p>
                    <p className="font-medium">{subscription.cancellation.feedback}</p>
                  </div>
                )}
                {subscription.dates?.canceledAt && (
                  <div>
                    <p className="text-sm text-gray-500">Canceled At</p>
                    <p className="font-medium">{formatDate(subscription.dates.canceledAt)}</p>
                  </div>
                )}
                <div>
                  <p className="text-sm text-gray-500">Refund Issued</p>
                  <Badge className={subscription.cancellation.refundIssued ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}>
                    {subscription.cancellation.refundIssued ? 'Yes' : 'No'}
                  </Badge>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Action Buttons */}
          <div className="flex justify-end gap-2 pt-4">
            <Button variant="outline" onClick={onClose}>
              Close
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default ViewSubscriptionModal; 