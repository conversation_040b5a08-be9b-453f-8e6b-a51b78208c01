/**
 * Subscription Service - Aggregates all subscription-related service methods
 * 
 * This service follows the clean code pattern established in the codebase:
 * - Individual service files for each operation
 * - Consistent error handling with baseService utilities
 * - API bridge integration with proper caching
 * - Validation and proper response processing
 */

// Import individual service methods
import getSubscriptionHistory from './getSubscriptionHistory';
import getAllSubscriptions from './getAllSubscriptions';
import getSubscriptionById from './getSubscriptionById';
import getCurrentSubscription from './getCurrentSubscription';
import getSubscriptionStats from './getSubscriptionStats';
import createSubscription from './createSubscription';
import updateSubscription from './updateSubscription';
import extendSubscription from './extendSubscription';
import cancelSubscription from './cancelSubscription';
import upgradeFromTrial from './upgradeFromTrial';
import changeSubscriptionPlan from './changeSubscriptionPlan';
import pauseSubscription from './pauseSubscription';
import resumeSubscription from './resumeSubscription';
import recordPayment from './recordPayment';
import updateAutoRenewal from './updateAutoRenewal';
import renewSubscription from './renewSubscription';
import getUpcomingRenewals from './getUpcomingRenewals';
import bulkUpdateSubscriptions from './bulkUpdateSubscriptions';
import bulkCancelSubscriptions from './bulkCancelSubscriptions';
import retryPaymentNow from './retryPaymentNow';
import schedulePaymentRetry from './schedulePaymentRetry';
import getPaymentRetryHistory from './getPaymentRetryHistory';

// SuperAdmin-only services
import triggerCronTasks from './triggerCronTasks';

// Payment retry management services (SuperAdmin only)
import getPaymentRetryStatus from './getPaymentRetryStatus';
import triggerManualPaymentRetry from './triggerManualPaymentRetry';
import cancelScheduledRetries from './cancelScheduledRetries';
import processAllPendingRetries from './processAllPendingRetries';
import getPaymentRetryConfig from './getPaymentRetryConfig';

// Core imports
import apiBridge from '@/lib/api/bridge';
import { ENDPOINTS } from '@/lib/api/contract';;
import { toast } from 'sonner';
import BaseService from '@/lib/services/baseService';

/**
 * SubscriptionService exports all subscription-related API operations
 */
const SubscriptionService = {
  // Core subscription operations
  getSubscriptionHistory,
  getAllSubscriptions,
  getSubscriptionById,
  upgradeFromTrial,
  changeSubscriptionPlan,
  cancelSubscription,
  recordPayment,
  updateAutoRenewal,
  renewSubscription,
  extendSubscription,
  updateSubscription,
  getCurrentSubscription,
  getSubscriptionStats,
  createSubscription,
  pauseSubscription,
  resumeSubscription,
  getUpcomingRenewals,
  bulkUpdateSubscriptions,
  bulkCancelSubscriptions,
  retryPaymentNow,
  schedulePaymentRetry,
  getPaymentRetryHistory,

  // SuperAdmin operations
  getSubscriptionStats,
  bulkUpdateSubscriptions,
  triggerCronTasks,

  // Payment retry management (SuperAdmin only)
  getPaymentRetryStatus,
  triggerManualPaymentRetry,
  cancelScheduledRetries,
  processAllPendingRetries,
  getPaymentRetryConfig,

  /**
   * Get current subscription for logged-in admin/shop
   * @returns {Promise<Object>} Current subscription details
   */
  async getCurrentSubscription() {
    try {
      const response = await apiBridge.get(ENDPOINTS.SUBSCRIPTIONS.CURRENT);
      
      if (response.data && response.data.success) {
        // Backend returns subscription directly in data field, not data.subscription
        return response.data.data;
      }
      
      console.error('Unexpected API response format:', response.data);
      return null;
    } catch (error) {
      BaseService.handleError(error, 'SubscriptionService.getCurrentSubscription', false);
      return null;
    }
  },

  /**
   * Validate subscription data before submission
   * @param {Object} subscriptionData - Subscription data to validate
   * @returns {Object} Validation result
   */
  validateSubscription(subscriptionData) {
    const errors = {};

    // Validate plan selection
    if (!subscriptionData.planId && !subscriptionData.planType) {
      errors.plan = 'Either planId or planType is required';
    }

    // Validate payment method
    if (!subscriptionData.paymentMethod) {
      errors.paymentMethod = 'Payment method is required';
    } else if (!['offline', 'evc_plus'].includes(subscriptionData.paymentMethod)) {
      errors.paymentMethod = 'Payment method must be either "offline" or "evc_plus"';
    }

    // Validate payment details if provided
    if (subscriptionData.paymentDetails) {
      const { paymentDetails } = subscriptionData;
      
      if (paymentDetails.amount && paymentDetails.amount <= 0) {
        errors.amount = 'Payment amount must be positive';
      }
      
      if (paymentDetails.payerEmail && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(paymentDetails.payerEmail)) {
        errors.payerEmail = 'Invalid email format';
      }
      
      if (paymentDetails.payerPhone && !/^[+]?[0-9]{10,15}$/.test(paymentDetails.payerPhone)) {
        errors.payerPhone = 'Invalid phone number format';
      }
    }

    return {
      isValid: Object.keys(errors).length === 0,
      errors
    };
  },

  /**
   * Get subscription analytics data
   * @param {Object} params - Analytics parameters (dateRange, shopId, etc.)
   * @returns {Promise<Object>} Analytics data
   */
  async getSubscriptionAnalytics(params = {}) {
    try {
      const response = await apiBridge.get(ENDPOINTS.SUBSCRIPTIONS.ANALYTICS, { params });
      
      if (response.data && response.data.success) {
        return response.data.data;
      }
      
      console.error('Unexpected API response format:', response.data);
      return null;
    } catch (error) {
      BaseService.handleError(error, 'SubscriptionService.getSubscriptionAnalytics', true);
      throw error;
    }
  },

  /**
   * Export subscription data
   * @param {Object} exportParams - Export parameters (format, filters, etc.)
   * @returns {Promise<Blob>} Export file blob
   */
  async exportSubscriptions(exportParams = {}) {
    try {
      const response = await apiBridge.get(`${ENDPOINTS.SUBSCRIPTIONS.BASE}/export`, {
        params: exportParams,
        responseType: 'blob'
      });
      
      return response.data;
    } catch (error) {
      BaseService.handleError(error, 'SubscriptionService.exportSubscriptions', true);
      throw error;
    }
  },

  /**
   * Validate payment verification data
   * @param {Object} verificationData - Payment verification data
   * @returns {Object} Validation result
   */
  validatePaymentVerification(verificationData) {
    const errors = {};

    if (!verificationData.transactionId) {
      errors.transactionId = 'Transaction ID is required';
    }

    if (!verificationData.paymentMethod) {
      errors.paymentMethod = 'Payment method is required';
    }

    if (verificationData.amount && verificationData.amount <= 0) {
      errors.amount = 'Amount must be positive';
    }

    return {
      isValid: Object.keys(errors).length === 0,
      errors
    };
  },

  /**
   * Clear subscription cache
   * @param {string} specificEndpoint - Optional specific endpoint to clear
   */
  clearCache(specificEndpoint = null) {
    const endpointToClear = specificEndpoint || ENDPOINTS.SUBSCRIPTIONS.BASE;
    BaseService.clearCache(endpointToClear);
  }
};

export default SubscriptionService; 
