"use client";

import { useEffect, useState } from "react";
import { useAuth } from "@/contexts/auth-context";
import { useDashboardOverview } from "@/hooks/use-dashboard-overview";
import { ResponsiveContainer } from "@/components/layout/responsive-container";
import { KpiCard } from "@/components/dashboard/common/kpi-card";
import { DashboardGrowthChart } from "@/components/dashboard/common/dashboard-growth-chart";
import { DashboardPieChart } from "@/components/dashboard/common/dashboard-pie-chart";
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import {
  Users,
  Building2,
  Activity,
  Clock,
  CreditCard,
  ArrowUpRight,
  Folder,
  ShieldCheck,
  Settings,
  CalendarDays,
  Wallet,
  RefreshCw,
  TrendingUp,
  TrendingDown,
  AlertCircle,
  CheckCircle
} from "lucide-react";

export default function DashboardPage() {
  const { user } = useAuth();

  // Use the dashboard overview hook for real API data
  const {
    formattedStats,
    loading,
    refreshing,
    error,
    lastUpdated,
    refreshDashboard,
    formatCurrency,
    formatPercentage,
    getTrendDirection,
    isSuperAdmin,
    hasData
  } = useDashboardOverview({
    autoRefresh: true,
    refreshInterval: 300000, // 5 minutes
    showToastMessages: false // Don't show toast on auto-refresh
  });

  // Handle refresh button click
  const handleRefresh = async () => {
    console.log('[Dashboard] Refresh button clicked');
    await refreshDashboard();
  };

  // Debug logging for loading states
  console.log('[Dashboard] Loading states:', { loading, refreshing, hasData, formattedStats: !!formattedStats });

  // Recent activity data (would come from API)
  const recentActivity = [
    {
      id: 1,
      action: "Payment Verification",
      description: "New offline payment requires verification",
      time: "5 minutes ago",
      priority: "high"
    },
    {
      id: 2,
      action: "New Shop Registration",
      description: "Bosaaso Medical Center registered",
      time: "2 hours ago",
      priority: "normal"
    },
    {
      id: 3,
      action: "Subscription Extended",
      description: "Manual extension for shop ID SH-2023-001",
      time: "Yesterday",
      priority: "normal"
    },
    {
      id: 4,
      action: "Settings Updated",
      description: "Payment methods configuration changed",
      time: "2 days ago",
      priority: "low"
    },
  ];

  // Quick access menu for superadmin
  const quickAccessMenu = [
    {
      title: "User Management",
      description: "Manage users across shops",
      icon: <Users className="h-5 w-5" />,
      href: "/dashboard/users",
      color: "bg-blue-100 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400"
    },
    {
      title: "Shop Management",
      description: "View and manage shops",
      icon: <Building2 className="h-5 w-5" />,
      href: "/dashboard/shops",
      color: "bg-purple-100 dark:bg-purple-900/20 text-purple-600 dark:text-purple-400"
    },
    {
      title: "Subscriptions",
      description: "Manage all subscriptions",
      icon: <CalendarDays className="h-5 w-5" />,
      href: "/dashboard/subscriptions",
      color: "bg-green-100 dark:bg-green-900/20 text-green-600 dark:text-green-400"
    },
    {
      title: "Payments",
      description: "Verify and track payments",
      icon: <Wallet className="h-5 w-5" />,
      href: "/dashboard/payments",
      color: "bg-amber-100 dark:bg-amber-900/20 text-amber-600 dark:text-amber-400"
    },
    {
      title: "Services",
      description: "Manage healthcare services",
      icon: <Folder className="h-5 w-5" />,
      href: "/dashboard/services",
      color: "bg-indigo-100 dark:bg-indigo-900/20 text-indigo-600 dark:text-indigo-400"
    },
    {
      title: "System Settings",
      description: "Configure global settings",
      icon: <Settings className="h-5 w-5" />,
      href: "/dashboard/settings",
      color: "bg-rose-100 dark:bg-rose-900/20 text-rose-600 dark:text-rose-400"
    },
  ];

  // Remove global spinner - use skeleton loaders instead

  return (
    <ResponsiveContainer>
      <div className="flex flex-col gap-6 pb-8">
        {/* Welcome Message */}
        {loading && !formattedStats ? (
          <div className="flex flex-col gap-2">
            <Skeleton className="h-8 w-64" />
            <Skeleton className="h-5 w-96" />
          </div>
        ) : (
          <div className="flex flex-col gap-2">
            <h1 className="text-2xl md:text-3xl font-bold tracking-tight">
              Hi {user?.fullName || user?.name || "SuperAdmin"} 👋
            </h1>
            <p className="text-muted-foreground">
              Here's an overview of your DeynCare superadmin portal
            </p>
          </div>
        )}

        {/* Critical Action Card - Payment Verification Pending */}
        {formattedStats?.payments?.pending > 0 && (
          <Card className="border-l-4 border-l-red-500 dark:border-l-red-400">
            <CardHeader className="pb-2">
              <CardTitle className="text-lg flex items-center gap-2 text-red-600 dark:text-red-400">
                <AlertCircle className="h-5 w-5" />
                Action Required
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex flex-col sm:flex-row justify-between gap-4">
                <div>
                  <h3 className="font-medium">Payment Verification Pending</h3>
                  <p className="text-sm text-muted-foreground mt-1">
                    {formattedStats.payments.pending} offline payment{formattedStats.payments.pending !== 1 ? 's' : ''} need{formattedStats.payments.pending === 1 ? 's' : ''} your verification to activate subscriptions
                  </p>
                </div>
                <Button className="shrink-0" size="sm" onClick={() => window.location.href = '/dashboard/payment-transactions'}>
                  Review Now
                </Button>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Refresh Button */}
        {loading && !formattedStats ? (
          <div className="flex justify-between items-center">
            <Skeleton className="h-6 w-40" />
            <Skeleton className="h-8 w-20" />
          </div>
        ) : (
          <div className="flex justify-between items-center">
            <h2 className="text-xl font-semibold">Dashboard Overview</h2>
            <Button
              variant="outline"
              size="sm"
              onClick={handleRefresh}
              disabled={refreshing}
              className="flex items-center gap-2"
            >
              <RefreshCw className={`h-4 w-4 ${refreshing ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
          </div>
        )}

        {/* Stats Overview - Real API Data */}
        {loading || refreshing ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {[...Array(4)].map((_, i) => (
              <Card key={i} className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <Skeleton className="h-5 w-24" />
                  <Skeleton className="h-5 w-5 rounded" />
                </div>
                <div className="space-y-3">
                  <Skeleton className="h-8 w-20" />
                  <div className="flex items-center gap-2">
                    <Skeleton className="h-4 w-12" />
                    <Skeleton className="h-4 w-16" />
                  </div>
                  <Skeleton className="h-4 w-32" />
                </div>
              </Card>
            ))}
          </div>
        ) : error ? (
          <Card className="border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-950/50">
            <CardContent className="pt-6">
              <div className="flex items-center gap-2 text-red-600 dark:text-red-400">
                <AlertCircle className="h-5 w-5" />
                <span>Failed to load dashboard data: {error}</span>
              </div>
            </CardContent>
          </Card>
        ) : formattedStats ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {/* Users KPI */}
            <KpiCard
              title="Total Users"
              value={formattedStats.users.total.toLocaleString()}
              change={formatPercentage(formattedStats.users.growthRate)}
              trend={getTrendDirection(formattedStats.users.growthRate)}
              icon={Users}
              description={`${formattedStats.users.active} active, ${formattedStats.users.inactive} inactive`}
            />

            {/* Shops KPI */}
            <KpiCard
              title="Total Shops"
              value={formattedStats.shops.total.toLocaleString()}
              change={formatPercentage(formattedStats.shops.growthRate)}
              trend={getTrendDirection(formattedStats.shops.growthRate)}
              icon={Building2}
              description={`${formattedStats.shops.active} active, ${formattedStats.shops.pending} pending`}
            />

            {/* Subscriptions KPI */}
            <KpiCard
              title="Active Subscriptions"
              value={formattedStats.subscriptions.active.toLocaleString()}
              change={`${formattedStats.subscriptions.expiringSoon} expiring soon`}
              trend="neutral"
              icon={CalendarDays}
              description={`${formattedStats.subscriptions.trial} trial, ${formattedStats.subscriptions.expired} expired`}
            />

            {/* Revenue KPI */}
            <KpiCard
              title="Monthly Revenue"
              value={formatCurrency(formattedStats.payments.thisMonthRevenue)}
              change={formatPercentage(formattedStats.payments.revenueGrowth)}
              trend={getTrendDirection(formattedStats.payments.revenueGrowth)}
              icon={Wallet}
              description={`${formattedStats.payments.pending} pending verification`}
            />
          </div>
        ) : null}

        {/* Charts Section */}
        {loading || refreshing ? (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mt-6">
            {[...Array(2)].map((_, i) => (
              <Card key={i}>
                <CardHeader>
                  <Skeleton className="h-6 w-48" />
                  <Skeleton className="h-4 w-32" />
                </CardHeader>
                <CardContent>
                  <Skeleton className="h-[300px] w-full" />
                </CardContent>
              </Card>
            ))}
          </div>
        ) : formattedStats ? (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mt-6">
            {/* Growth Trends Chart */}
            <Card>
              <CardHeader>
                <CardTitle>Platform Growth Trends</CardTitle>
                <CardDescription>Users and Shops growth over time</CardDescription>
              </CardHeader>
              <CardContent>
                <DashboardGrowthChart
                  data={[
                    { month: "Jan", users: Math.max(0, formattedStats.users.total - formattedStats.users.thisMonth * 6), shops: Math.max(0, formattedStats.shops.total - formattedStats.shops.thisMonth * 6) },
                    { month: "Feb", users: Math.max(0, formattedStats.users.total - formattedStats.users.thisMonth * 5), shops: Math.max(0, formattedStats.shops.total - formattedStats.shops.thisMonth * 5) },
                    { month: "Mar", users: Math.max(0, formattedStats.users.total - formattedStats.users.thisMonth * 4), shops: Math.max(0, formattedStats.shops.total - formattedStats.shops.thisMonth * 4) },
                    { month: "Apr", users: Math.max(0, formattedStats.users.total - formattedStats.users.thisMonth * 3), shops: Math.max(0, formattedStats.shops.total - formattedStats.shops.thisMonth * 3) },
                    { month: "May", users: Math.max(0, formattedStats.users.total - formattedStats.users.thisMonth * 2), shops: Math.max(0, formattedStats.shops.total - formattedStats.shops.thisMonth * 2) },
                    { month: "Jun", users: Math.max(0, formattedStats.users.total - formattedStats.users.thisMonth), shops: Math.max(0, formattedStats.shops.total - formattedStats.shops.thisMonth) },
                    { month: "Jul", users: formattedStats.users.total, shops: formattedStats.shops.total }
                  ]}
                  config={{
                    users: { label: "Users", color: "hsl(var(--primary))" },
                    shops: { label: "Shops", color: "hsl(var(--secondary))" }
                  }}
                />
              </CardContent>
            </Card>

            {/* Subscription Distribution Chart */}
            <Card>
              <CardHeader>
                <CardTitle>Subscription Distribution</CardTitle>
                <CardDescription>Current subscription status breakdown</CardDescription>
              </CardHeader>
              <CardContent>
                <DashboardPieChart
                  data={[
                    {
                      status: "Active",
                      count: formattedStats.subscriptions.active
                    },
                    {
                      status: "Trial",
                      count: formattedStats.subscriptions.trial
                    },
                    {
                      status: "Expired",
                      count: formattedStats.subscriptions.expired
                    }
                  ]}
                  config={{
                    active: { label: "Active", color: "hsl(var(--chart-1))" },
                    trial: { label: "Trial", color: "hsl(var(--chart-2))" },
                    expired: { label: "Expired", color: "hsl(var(--chart-3))" }
                  }}
                />
              </CardContent>
            </Card>
          </div>
        ) : null}

        {/* Quick Access Menu */}
        <div className="mt-2">
          <h2 className="text-xl font-semibold mb-4">Quick Access</h2>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
            {quickAccessMenu.map((item) => (
              <a 
                key={item.title} 
                href={item.href}
                className="block group"
              >
                <Card className="group-hover:border-primary transition-colors">
                  <CardContent className="p-6">
                    <div className="flex items-start gap-4">
                      <div className={`rounded-full p-2 ${item.color}`}>
                        {item.icon}
                      </div>
                      <div>
                        <h3 className="font-medium">{item.title}</h3>
                        <p className="text-sm text-muted-foreground mt-1">
                          {item.description}
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </a>
            ))}
          </div>
        </div>

        {/* Recent Activity */}
        <div className="mt-4">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-xl font-semibold">Recent Activity</h2>
            <Button variant="ghost" size="sm" className="gap-1">
              View all
              <ArrowUpRight className="h-4 w-4" />
            </Button>
          </div>
          <Card>
            <CardContent className="p-0">
              <div className="divide-y divide-border">
                {recentActivity.map((activity) => (
                  <div key={activity.id} className="p-4 flex flex-col sm:flex-row sm:items-start justify-between gap-2">
                    <div>
                      <div className="flex items-center gap-2">
                        <h3 className="font-medium">{activity.action}</h3>
                        {activity.priority === "high" && (
                          <span className="inline-flex h-2 w-2 rounded-full bg-red-500"></span>
                        )}
                      </div>
                      <p className="text-sm text-muted-foreground mt-1">
                        {activity.description}
                      </p>
                    </div>
                    <div className="text-xs text-muted-foreground sm:text-right">
                      {activity.time}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </ResponsiveContainer>
  );
}
