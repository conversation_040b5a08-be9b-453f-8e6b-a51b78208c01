/**
 * User Status Middleware
 * Provides access control based on user status for pending approval users
 */
const { logWarning, logInfo } = require('../utils');

/**
 * Check if user status allows access to protected features
 * Specifically restricts users with 'email_verified_pending_payment' status
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object  
 * @param {Function} next - Express next middleware function
 */
const checkUserStatusAccess = (req, res, next) => {
  try {
    // Skip check for superAdmin
    if (req.user && req.user.role === 'superAdmin') {
      return next();
    }

    // Only apply to users with pending payment status
    if (!req.user || req.user.status !== 'email_verified_pending_payment') {
      return next();
    }

    // Define allowed endpoints for users pending payment approval
    const allowedEndpoints = [
      '/api/auth/logout',
      '/api/auth/profile',
      '/api/auth/settings',
      '/api/auth/payment-retry',
      '/api/subscription-status',
      '/api/notifications/register-fcm',
      '/api/notifications/unregister-fcm'
    ];

    // Check if current endpoint is allowed
    const isAllowedEndpoint = allowedEndpoints.some(endpoint => 
      req.originalUrl.startsWith(endpoint)
    );

    if (isAllowedEndpoint) {
      return next();
    }

    // Log unauthorized access attempt
    logWarning(`User with pending payment status attempted to access restricted endpoint: ${req.originalUrl}`, 'UserStatusMiddleware');

    // Return restriction response
    return res.status(403).json({
      success: false,
      error: 'Your account is pending payment approval. Access to this feature is restricted until SuperAdmin verifies your payment.',
      code: 'USER_STATUS_RESTRICTED',
      restrictedAccess: true,
      userStatus: req.user.status,
      actionRequired: 'wait_for_payment_approval',
      allowedFeatures: [
        'profile_management',
        'settings',
        'payment_retry',
        'logout'
      ],
      restrictedFeatures: [
        'customer_management',
        'debt_tracking',
        'payment_processing',
        'risk_assessment',
        'reporting',
        'data_export',
        'shop_management'
      ],
      contactInfo: {
        email: '<EMAIL>',
        phone: '+252 61 234 5678'
      },
      expectedTimeline: '24-48 hours',
      message: 'Please wait for SuperAdmin to verify your payment. You will receive an email notification once approved.'
    });

  } catch (error) {
    logWarning(`Error in user status middleware: ${error.message}`, 'UserStatusMiddleware');
    return next(error);
  }
};

/**
 * Middleware specifically for mobile app endpoints
 * Provides mobile-friendly response format for status restrictions
 */
const checkMobileUserStatusAccess = (req, res, next) => {
  try {
    // Set mobile app flag
    req.isMobileApp = true;
    
    // Use the main user status check
    checkUserStatusAccess(req, res, (error) => {
      if (error) {
        return next(error);
      }
      
      // If user status warning should be included in mobile responses
      if (req.user && req.user.status === 'email_verified_pending_payment') {
        res.locals.userStatusWarning = {
          status: 'pending_approval',
          message: 'Your payment is being reviewed by our team',
          timeline: '24-48 hours',
          restrictedAccess: true
        };
        
        // Modify response to include warning
        const originalSend = res.json;
        res.json = function(data) {
          if (data && typeof data === 'object') {
            data.userStatusWarning = res.locals.userStatusWarning;
          }
          return originalSend.call(this, data);
        };
      }
      
      next();
    });
  } catch (error) {
    next(error);
  }
};

module.exports = {
  checkUserStatusAccess,
  checkMobileUserStatusAccess
};
