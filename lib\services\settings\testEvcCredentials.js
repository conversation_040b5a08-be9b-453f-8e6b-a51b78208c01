/**
 * Test EVC Credentials Service
 * Matches backend: settingsController.testEVCCredentials
 * POST /api/settings/test-evc-credentials
 */

import { testEvcCredentials as testEvcCredentialsAPI } from '../../api/modules/settings';
import { handleError, logApiCall } from '../baseService';

/**
 * Test EVC payment credentials
 * @param {Object} data - Test parameters
 * @param {string|null} [data.shopId] - Shop ID for shop-specific credentials, null for global
 * @param {string} [data.phone] - Phone number for real payment test (optional)
 * @param {number} [data.amount] - Amount for real payment test (optional)
 * @returns {Promise<Object>} Test result
 */
const testEvcCredentials = async (data = {}) => {
  const isRealPaymentTest = data.phone && data.amount;
  const context = `testEvcCredentials${data.shopId ? `(shopId: ${data.shopId})` : '(global)'}${isRealPaymentTest ? ' [REAL_PAYMENT]' : ' [CONNECTIVITY]'}`;
  
  try {
    // Prepare payload in exact backend expected format
    const payload = {
      shopId: data.shopId || null
    };
    
    // Add optional fields for real payment test
    if (data.phone) {
      payload.phone = data.phone;
    }
    if (data.amount) {
      payload.amount = data.amount;
    }
    
    logApiCall(context, 'POST /api/settings/test-evc-credentials', payload);
    
    const response = await testEvcCredentialsAPI(payload);
    
    if (response?.success) {
      console.log(`[${context}] Success:`, {
        message: response.message,
        transactionId: response.transactionId,
        hasCredentials: response.hasCredentials,
        lastUpdated: response.lastUpdated
      });
      
      return {
        success: true,
        message: response.message || 'EVC credentials test completed successfully',
        transactionId: response.transactionId || null,
        hasCredentials: response.hasCredentials,
        lastUpdated: response.lastUpdated
      };
    } else {
      console.warn(`[${context}] API returned success=false:`, response);
      return {
        success: false,
        message: response?.message || 'EVC credentials test failed'
      };
    }
    
  } catch (error) {
    console.error(`[${context}] Error:`, error);
    handleError(error, context, false);
    
    return {
      success: false,
      message: error.message || 'Failed to test EVC credentials',
      error: error
    };
  }
};

export default testEvcCredentials; 