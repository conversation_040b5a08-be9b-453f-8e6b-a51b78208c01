/**
 * ML Settings Page
 * Provides unified interface for ML (Machine Learning) configuration
 * Admin users: Basic ML controls for mobile app
 * SuperAdmin users: Technical ML configuration + admin controls
 */

'use client';

import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '../../../../components/ui/card';
import { Alert, AlertDescription } from '../../../../components/ui/alert';
import { Brain, Settings, Info, Shield, Sparkles, Cpu, Database, Zap } from 'lucide-react';
import MLSettingsForm from '../../../../components/settings/ml-settings-form';
import MLTechnicalSettingsForm from '../../../../components/settings/ml-technical-settings-form';
import { useAuth } from '../../../../contexts/auth-context';

/**
 * ML Settings Page Component
 */
export default function MLSettingsPage() {
  const { user } = useAuth();
  
  // Determine if user can manage technical ML settings (SuperAdmin only)
  const canManageTechnical = user?.role === 'superAdmin';
  
  // For shop admins, show admin-accessible settings only
  const isAdmin = user?.role === 'admin';

  const handleMLSettingsSaved = (result) => {
    console.log('[MLSettingsPage] ML settings saved:', result);
    // Could show notification or trigger refetch
  };

  const handleTechnicalSettingsSaved = (result) => {
    console.log('[MLSettingsPage] Technical ML settings saved:', result);
    // Could show notification or trigger refetch
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 p-6">
      <div className="max-w-7xl mx-auto space-y-8">
        {/* Enhanced Page Header with Gradient Background */}
        <div className="relative overflow-hidden rounded-2xl bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 p-8 text-white shadow-2xl">
          <div className="absolute inset-0 bg-black/10"></div>
          <div className="relative z-10">
            <div className="flex items-center gap-4 mb-4">
              <div className="p-3 bg-white/20 rounded-xl backdrop-blur-sm">
                <Brain className="w-8 h-8" />
              </div>
              <div>
                <h1 className="text-4xl font-bold tracking-tight">
                  ML Settings
                  <Sparkles className="inline-block w-6 h-6 ml-2 text-yellow-300" />
                </h1>
                <p className="text-blue-100 text-lg mt-2">
                  Configure your Machine Learning engine for intelligent risk assessment
                  {isAdmin && (
                    <span className="ml-2 bg-blue-500/30 px-3 py-1 rounded-full text-sm">
                      Admin Controls
                    </span>
                  )}
                </p>
              </div>
            </div>
            
            {/* Stats Row */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-6">
              <div className="bg-white/10 backdrop-blur-sm rounded-xl p-4">
                <div className="flex items-center gap-3">
                  <Cpu className="w-5 h-5 text-blue-200" />
                  <div>
                    <p className="text-sm text-blue-200">ML Engine</p>
                    <p className="font-semibold">Risk Assessment</p>
                  </div>
                </div>
              </div>
              <div className="bg-white/10 backdrop-blur-sm rounded-xl p-4">
                <div className="flex items-center gap-3">
                  <Database className="w-5 h-5 text-purple-200" />
                  <div>
                    <p className="text-sm text-purple-200">Data Processing</p>
                    <p className="font-semibold">Real-time Analysis</p>
                  </div>
                </div>
              </div>
              <div className="bg-white/10 backdrop-blur-sm rounded-xl p-4">
                <div className="flex items-center gap-3">
                  <Zap className="w-5 h-5 text-yellow-200" />
                  <div>
                    <p className="text-sm text-yellow-200">Performance</p>
                    <p className="font-semibold">Optimized Speed</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          {/* Decorative Elements */}
          <div className="absolute top-0 right-0 w-32 h-32 bg-white/5 rounded-full -translate-y-16 translate-x-16"></div>
          <div className="absolute bottom-0 left-0 w-24 h-24 bg-white/5 rounded-full translate-y-12 -translate-x-12"></div>
        </div>

        {/* Permission Notice */}
        {isAdmin && (
          <Alert className="border-l-4 border-l-blue-500 bg-blue-50 shadow-md">
            <Info className="h-5 w-5 text-blue-600" />
            <AlertDescription className="text-blue-800">
              As a shop admin, you can control ML features for your mobile app usage. 
              Technical ML configuration requires SuperAdmin access.
            </AlertDescription>
          </Alert>
        )}

        {/* Admin ML Settings (Available to both Admin and SuperAdmin) */}
        <Card className="shadow-xl border-0 bg-white/80 backdrop-blur-sm">
          <CardHeader className="bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-t-lg">
            <CardTitle className="flex items-center gap-3 text-xl">
              <div className="p-2 bg-white/20 rounded-lg">
                <Settings className="w-6 h-6" />
              </div>
              <div>
                <span>ML Engine Controls</span>
                <p className="text-sm font-normal text-blue-100 mt-1">
                  {isAdmin ? 'Mobile App Configuration' : 'Basic Configuration'}
                </p>
              </div>
            </CardTitle>
          </CardHeader>
          <CardContent className="p-8">
            <div className="space-y-6">
              <div className="bg-gradient-to-r from-blue-50 to-indigo-50 p-4 rounded-xl border border-blue-200">
                <p className="text-blue-800 font-medium">
                  Control when and how the ML engine processes risk assessments for customer payments.
                </p>
              </div>
              <MLSettingsForm onSave={handleMLSettingsSaved} />
            </div>
          </CardContent>
        </Card>

        {/* Technical ML Settings (SuperAdmin only) */}
        {canManageTechnical && (
          <Card className="shadow-xl border-0 bg-white/80 backdrop-blur-sm">
            <CardHeader className="bg-gradient-to-r from-red-500 to-orange-600 text-white rounded-t-lg">
              <CardTitle className="flex items-center gap-3 text-xl">
                <div className="p-2 bg-white/20 rounded-lg">
                  <Shield className="w-6 h-6" />
                </div>
                <div>
                  <span>Technical ML Configuration</span>
                  <p className="text-sm font-normal text-red-100 mt-1">
                    SuperAdmin Only - Advanced Settings
                  </p>
                </div>
              </CardTitle>
            </CardHeader>
            <CardContent className="p-8">
              <div className="space-y-6">
                <Alert className="border-l-4 border-l-orange-500 bg-orange-50 shadow-md">
                  <Shield className="h-5 w-5 text-orange-600" />
                  <AlertDescription className="text-orange-800">
                    <strong>Warning:</strong> These settings affect the core ML engine integration. 
                    Incorrect configuration may disrupt ML functionality across the platform.
                  </AlertDescription>
                </Alert>
                <MLTechnicalSettingsForm onSave={handleTechnicalSettingsSaved} />
              </div>
            </CardContent>
          </Card>
        )}

        {/* Access Restriction Notice for Non-Admin Users */}
        {!isAdmin && !canManageTechnical && (
          <Alert className="border-l-4 border-l-gray-500 bg-gray-50 shadow-md">
            <Info className="h-5 w-5 text-gray-600" />
            <AlertDescription className="text-gray-800">
              ML settings configuration is restricted to Admin and SuperAdmin users only. 
              Contact your administrator for access.
            </AlertDescription>
          </Alert>
        )}

        {/* Development Debug Info */}
        {process.env.NODE_ENV === 'development' && (
          <Card className="shadow-lg border-0 bg-gray-50/80 backdrop-blur-sm">
            <CardHeader className="bg-gradient-to-r from-gray-600 to-gray-700 text-white rounded-t-lg">
              <CardTitle className="text-lg flex items-center gap-2">
                <div className="p-1 bg-white/20 rounded">
                  <Info className="w-4 h-4" />
                </div>
                Debug Information
              </CardTitle>
            </CardHeader>
            <CardContent className="p-6">
              <pre className="text-xs bg-gray-800 text-green-400 p-4 rounded-lg overflow-x-auto font-mono">
                {JSON.stringify({
                  user: {
                    role: user?.role,
                    shopId: user?.shopId,
                    email: user?.email
                  },
                  permissions: {
                    canManageTechnical,
                    isAdmin,
                    hasMLAccess: isAdmin || canManageTechnical
                  }
                }, null, 2)}
              </pre>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
} 