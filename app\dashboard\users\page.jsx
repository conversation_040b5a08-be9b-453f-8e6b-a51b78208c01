"use client";

import { useState, useEffect, useCallback, useRef, useMemo } from 'react';
import { useAuth } from '@/contexts/auth-context';
import { toast } from 'sonner';
import UserService from '@/lib/services/user';
import ShopService from '@/lib/services/shop';
import { useUsersEnhanced } from '@/hooks/use-users-enhanced';
import { KpiCard } from '@/components/dashboard/common/kpi-card';
import { ResponsiveContainer } from '@/components/layout/responsive-container';
import { Users, Activity, Clock, CreditCard, Calendar, BarChart3, UserCheck, UserX, RefreshCw, Plus } from 'lucide-react';

// Import modular components
import UsersHeader from '@/components/dashboard/users/UsersHeader';
import UsersFilters from '@/components/dashboard/users/UsersFilters';
import UsersTable from '@/components/dashboard/users/UsersTable';
import UsersDialogs from '@/components/dashboard/users/UsersDialogs';
import { ExportDialog } from '@/components/dashboard/common/export-dialog';

export default function UsersPage() {
  const { user } = useAuth();

  // Enhanced users hook with integrated statistics and user management
  const {
    users,
    isLoading: isLoadingUsers,
    error,
    pagination,
    filters,
    fetchUsers,
    changePage,
    changePageSize,
    applyFilters,
    clearFilters,
    refreshUserList,
    refreshAll,
    getUserById,
    createUser,
    updateUser,
    deleteUser,
    changeUserStatus,
    // Statistics
    summary,
    activity,
    statsLoading,
    statsError,
    refreshStats
  } = useUsersEnhanced({
    includeStats: true,
    autoRefreshStats: false, // DISABLED to prevent conflicts
    statsRefreshInterval: 300000 // 5 minutes
  });
  
  // Local state for filtering
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [roleFilter, setRoleFilter] = useState('all');
  const [isRefreshing, setIsRefreshing] = useState(false);
  
  // Dialog states
  const [selectedUser, setSelectedUser] = useState(null);
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [shops, setShops] = useState([]);
  const [showDetailsDialog, setShowDetailsDialog] = useState(false);
  const [showChangeStatusDialog, setShowChangeStatusDialog] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [statusAction, setStatusAction] = useState(null);
  
  // Export dialog state
  const [showExportDialog, setShowExportDialog] = useState(false);
  
  // Refs for debouncing and preventing duplicate operations
  const refreshTimeoutRef = useRef(null);
  const filterTimeoutRef = useRef(null);
  const isRefreshingRef = useRef(false);
  
  // Function to handle export
  const handleExport = useCallback(() => {
    setShowExportDialog(true);
  }, []);
  
  // Function to handle edit user
  const handleEditUser = useCallback((user) => {
    setSelectedUser(user);
    setIsFormOpen(true);
  }, []);
  
  // Optimized refresh handler with debounce protection
  const handleRefresh = useCallback(async () => {
    // Prevent multiple refresh operations
    if (isRefreshingRef.current) return;
    
    // Clear any pending refresh timeout
    if (refreshTimeoutRef.current) {
      clearTimeout(refreshTimeoutRef.current);
    }
    
    isRefreshingRef.current = true;
    setIsRefreshing(true);
    
    try {
      // Use the integrated refresh that handles both users and statistics
      await refreshAll();
      toast.success("User data refreshed successfully");
      
    } catch (error) {
      // Only log detailed errors in development
      if (process.env.NODE_ENV !== 'production') {
        console.error('[UsersPage] Refresh error:', error);
      }
      toast.error(`Failed to refresh users: ${error.message || 'Unknown error'}`);
    } finally {
      // Add a delay to prevent rapid successive calls
      refreshTimeoutRef.current = setTimeout(() => {
        setIsRefreshing(false);
        isRefreshingRef.current = false;
      }, 1000); // 1 second minimum between refreshes
    }
  }, [refreshAll]);
  
  // Memoized handlers to prevent unnecessary re-renders
  const handleAddUser = useCallback(() => {
    setSelectedUser(null); // Reset selected user to create new
    setIsFormOpen(true);
  }, []);
  
  const handleCloseForm = useCallback(() => {
    setIsFormOpen(false);
    setSelectedUser(null);
  }, []);
  
  // Optimized fetch shops function with caching
  const fetchShops = useCallback(async () => {
    try {
      // Check if shops are already loaded
      if (shops.length > 0) {
        return shops;
      }
      
      const shopResponse = await ShopService.getShops();
      
      if (shopResponse && shopResponse.shops && Array.isArray(shopResponse.shops)) {
        const shopsList = shopResponse.shops.map(shop => ({
          shopId: shop.id || shop.shopId,
          shopName: shop.shopName
        }));
        
        setShops(shopsList);
        return shopsList;
      }
      
      return [];
      
    } catch (error) {
      console.error("[UserPage] Error fetching shops:", error);
      
      const currentUser = user;
      let fallbackShops = [];
      
      if (currentUser && currentUser.shopId) {
        fallbackShops = [{
          shopId: currentUser.shopId,
          shopName: currentUser.shopName || `Shop ${currentUser.shopId}`
        }];
      } else {
        fallbackShops = [
          { shopId: "shop_1", shopName: "Default Shop" }
        ];
      }
      
      toast.info("Using available shops from your account");
      setShops(fallbackShops);
      return fallbackShops;
    }
  }, [shops.length, user]);
  
  // Fetch shops when the add/edit form is opened (only if not already loaded)
  useEffect(() => {
    if (isFormOpen && shops.length === 0) {
      fetchShops();
    }
  }, [isFormOpen, shops.length, fetchShops]);

  // OPTIMIZED: Debounced search handler
  const handleSearch = useCallback((event) => {
    const query = event.target.value;
    setSearchQuery(query);
  }, []);

  // OPTIMIZED: Filter change handlers
  const handleStatusFilterChange = useCallback((status) => {
    setStatusFilter(status);
  }, []);

  const handleRoleFilterChange = useCallback((role) => {
    setRoleFilter(role);
  }, []);

  // CRITICAL FIX: Properly debounced filter application
  useEffect(() => {
    // Clear existing timeout
    if (filterTimeoutRef.current) {
      clearTimeout(filterTimeoutRef.current);
    }
    
    // Set new timeout
    filterTimeoutRef.current = setTimeout(() => {
      const newFilters = {
        status: statusFilter === 'all' ? '' : statusFilter,
        role: roleFilter === 'all' ? '' : roleFilter,
        search: searchQuery
      };
      
      // CRITICAL: Only apply if filters actually changed
      const currentFiltersString = JSON.stringify(filters);
      const newFiltersString = JSON.stringify(newFilters);
      
      if (currentFiltersString !== newFiltersString) {
        applyFilters(newFilters);
      }
    }, 800); // Increased debounce to 800ms
    
    // Cleanup function
    return () => {
      if (filterTimeoutRef.current) {
        clearTimeout(filterTimeoutRef.current);
      }
    };
  }, [searchQuery, statusFilter, roleFilter, applyFilters, filters]);

  // Memoized CRUD operation handlers
  const handleUserCreated = useCallback(() => {
    setIsFormOpen(false);
    setSelectedUser(null);
  }, []);

  const handleUserUpdated = useCallback(() => {
    setIsFormOpen(false);
    setSelectedUser(null);
  }, []);

  const handleViewDetails = useCallback((user) => {
    setSelectedUser(user);
    setShowDetailsDialog(true);
  }, []);

  const handleChangeStatus = useCallback((user, action) => {
    setSelectedUser(user);
    setStatusAction(action);
    setShowChangeStatusDialog(true);
  }, []);

  const handleDeleteUser = useCallback((user) => {
    setSelectedUser(user);
    setShowDeleteDialog(true);
  }, []);

  const handleStatusChanged = useCallback(() => {
    setShowChangeStatusDialog(false);
    setSelectedUser(null);
    setStatusAction(null);
  }, []);

  const handleUserDeleted = useCallback(async () => {
    setShowDeleteDialog(false);
    setSelectedUser(null);

    // PERFORMANCE FIX: Refresh data after successful delete
    try {
      await refreshAll();
    } catch (error) {
      console.error('[UsersPage] Error refreshing after delete:', error);
      // Don't show error toast as delete was successful
    }
  }, [refreshAll]);

  // Memoized pagination handlers
  const handlePageChange = useCallback((page) => {
    changePage(page);
  }, [changePage]);

  const handlePageSizeChange = useCallback((size) => {
    changePageSize(size);
  }, [changePageSize]);
  
  // Memoized KPI cards to prevent unnecessary re-renders
  const kpiCards = useMemo(() => (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      <KpiCard
        title="Total Users"
        value={summary?.totalUsers?.toLocaleString() || '0'}
        icon="users"
        description="All registered users"
        loading={statsLoading}
      />
      <KpiCard
        title="Active Users"
        value={summary?.activeUsers?.toLocaleString() || '0'}
        icon="activity"
        description="Currently active users"
        trend="up"
        trendValue={summary?.totalUsers > 0 ? `${((summary?.activeUsers / summary?.totalUsers) * 100).toFixed(1)}%` : '0%'}
        trendLabel="of total users"
        loading={statsLoading}
      />
      <KpiCard
        title="Inactive Users"
        value={summary?.inactiveUsers?.toLocaleString() || '0'}
        icon="clock"
        description="Temporarily inactive"
        trend={summary?.inactiveUsers > 0 ? "down" : "neutral"}
        trendValue={summary?.totalUsers > 0 ? `${((summary?.inactiveUsers / summary?.totalUsers) * 100).toFixed(1)}%` : '0%'}
        trendLabel="of total users"
        loading={statsLoading}
      />
      <KpiCard
        title="Suspended Users"
        value={summary?.suspendedUsers?.toLocaleString() || '0'}
        icon="credit"
        description="Suspended accounts"
        trend={summary?.suspendedUsers > 0 ? "down" : "neutral"}
        trendValue={summary?.totalUsers > 0 ? `${((summary?.suspendedUsers / summary?.totalUsers) * 100).toFixed(1)}%` : '0%'}
        trendLabel="of total users"
        loading={statsLoading}
      />
    </div>
  ), [summary, statsLoading]);

  // Cleanup timeouts on unmount
  useEffect(() => {
    return () => {
      if (refreshTimeoutRef.current) {
        clearTimeout(refreshTimeoutRef.current);
      }
      if (filterTimeoutRef.current) {
        clearTimeout(filterTimeoutRef.current);
      }
    };
  }, []);
  
  return (
    <ResponsiveContainer>
      <div className="space-y-6">
        {/* Header */}
        <UsersHeader onAddUserClick={handleAddUser} onExportClick={handleExport} />

        {/* KPI Cards Section - Memoized */}
        {kpiCards}
        
        {/* Filters */}
        <UsersFilters
          searchQuery={searchQuery}
          setSearchQuery={handleSearch}
          statusFilter={statusFilter}
          setStatusFilter={handleStatusFilterChange}
          roleFilter={roleFilter}
          setRoleFilter={handleRoleFilterChange}
          onRefresh={handleRefresh}
          isRefreshing={isRefreshing}
        />
        
        {/* Data Table */}
        <UsersTable
          users={users || []}
          pagination={pagination}
          isLoading={isLoadingUsers}
          onPageChange={handlePageChange}
          onPageSizeChange={handlePageSizeChange}
          onViewDetails={handleViewDetails}
          onEditUser={handleEditUser}
          onChangeStatus={handleChangeStatus}
          onDeleteUser={handleDeleteUser}
          currentUserRole={user?.role}
        />
        
        {/* Dialogs */}
        <UsersDialogs
          // Form dialog props
          isFormOpen={isFormOpen}
          selectedUser={selectedUser}
          shops={shops}
          onCloseForm={handleCloseForm}
          onUserCreated={handleUserCreated}
          onUserUpdated={handleUserUpdated}
          
          // Details dialog props
          showDetailsDialog={showDetailsDialog}
          onCloseDetailsDialog={() => setShowDetailsDialog(false)}
          
          // Status change dialog props
          showChangeStatusDialog={showChangeStatusDialog}
          statusAction={statusAction}
          onCloseStatusDialog={() => setShowChangeStatusDialog(false)}
          onStatusChanged={handleStatusChanged}
          
          // Delete dialog props
          showDeleteDialog={showDeleteDialog}
          onCloseDeleteDialog={() => setShowDeleteDialog(false)}
          onUserDeleted={handleUserDeleted}
        />
        
        {/* Export Dialog */}
        <ExportDialog
          open={showExportDialog}
          onOpenChange={setShowExportDialog}
          exportType="users"
          title="Export Users"
          description="Export user data to CSV format"
        />
      </div>
    </ResponsiveContainer>
  );
}
