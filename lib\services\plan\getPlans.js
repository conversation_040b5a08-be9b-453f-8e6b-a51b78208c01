import apiBridge from '@/lib/api/bridge';
import { ENDPOINTS } from '@/lib/api/contract';
import { handleError, processApiResponse, logApiCall } from '../baseService';

/**
 * Get all plans with filtering and pagination
 * @param {Object} options - Query options
 * @param {number} options.page - Page number (default: 1)
 * @param {number} options.limit - Items per page (default: 10)
 * @param {string} options.status - Filter by status ('active', 'inactive', 'all')
 * @param {string} options.type - Filter by type ('trial', 'monthly', 'yearly', 'all')
 * @param {string} options.search - Search term for plan name
 * @param {string} options.sortBy - Sort field (default: 'displayOrder')
 * @param {string} options.sortOrder - Sort order ('asc', 'desc')
 * @returns {Promise<Object>} Plans list with pagination
 */
async function getPlans(options = {}) {
  try {
    // Prepare query parameters
    const params = {
      page: options.page || 1,
      limit: options.limit || 50, // Increase limit to get all plans
      status: options.status || 'all',
      type: options.type || 'all',
      search: options.search || '',
      sortBy: options.sortBy || 'displayOrder',
      sortOrder: options.sortOrder || 'asc',
      includeInactive: options.includeInactive !== false // Default to true for SuperAdmin
    };

    logApiCall('PlanService.getPlans', ENDPOINTS.PLANS.BASE, params);

    // Make API request using the bridge
    const response = await apiBridge.get(ENDPOINTS.PLANS.BASE, {
      params,
      skipCache: true // Force fresh data to avoid 304 issues
    });

    // Handle the response properly
    let plansData;
    if (response.data && response.data.success) {
      plansData = response.data.data || response.data.plans || [];
    } else if (response.data && Array.isArray(response.data)) {
      plansData = response.data;
    } else if (response.data && response.data.plans) {
      plansData = response.data.plans;
    } else {
      console.warn('[getPlans] Unexpected response format:', response.data);
      plansData = [];
    }

    // Return in the format expected by usePlans hook
    return {
      success: true,
      data: plansData,
      pagination: {
        currentPage: parseInt(params.page),
        totalPages: Math.ceil((plansData.length || 0) / params.limit),
        total: plansData.length || 0,
        limit: parseInt(params.limit)
      }
    };

  } catch (error) {
    console.error('[getPlans] Error:', error);
    handleError(error, 'PlanService.getPlans', true);
    
    // Return error in expected format
    return {
      success: false,
      error: error.message || 'Failed to load plans',
      data: []
    };
  }
}

export default getPlans;
