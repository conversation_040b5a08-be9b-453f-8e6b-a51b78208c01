# Setting Docs: DeynCare SaaS Settings Architecture

## Overview
DeynCare supports a robust, multi-tenant settings system enabling both global (system-wide) and shop-specific configuration. Settings are categorized, validated, auditable, and exposed via secure RESTful APIs.

---

## 1. Setting Categories

Settings are grouped by `category` for clarity and management:

| Category      | Description / Example Use                                  |
|--------------|------------------------------------------------------------|
| system       | Platform-wide toggles, global limits                       |
| notification | Email/SMS templates, reminder schedules                    |
| payment      | Payment methods, gateway credentials                       |
| security     | Password policy, 2FA, rate limiting                        |
| display      | Currency, date/time format, language                       |
| ml           | ML/risk analysis toggles                                   |
| integration  | External API/service configs                               |
| custom       | Shop-specific or ad hoc settings                           |

---

## 2. Data Models

### a. Global & Shop Settings (`Setting`)
- Flexible schema with key, value, category, validation, access level, audit trail, and shopId (null = global).
- Supports value history, editability, and visibility for UI.

### b. Structured Shop Settings (`ShopSetting`)
- Deeply structured per-shop settings for risk, notifications, limits, display, business rules, etc.
- Supports default/fallback logic and category grouping.

---

## 3. API Endpoints

### **Base Path:** `/api/settings`

| Method | Endpoint                         | Description                                    | Auth        |
|--------|-----------------------------------|------------------------------------------------|-------------|
| GET    | `/payment-methods`               | List available payment methods (public)         | None        |
| GET    | `/`                              | Get all/global settings (optionally by category)| Admin/SAdmin|
| POST   | `/evc-credentials`               | Set EVC payment credentials (encrypted)         | Admin/SAdmin|
| POST   | `/test-evc-credentials`          | Test EVC payment credentials                   | Admin/SAdmin|
| PUT    | `/payment-methods`               | Update payment methods (global only)            | SuperAdmin  |
| PUT    | `/`                              | Update security settings (global only)          | SuperAdmin  |

- **Shop-specific endpoints**: Use `shopId` in payload/query to target shop settings.

---

## 4. Security & Access Control
- All sensitive endpoints require JWT authentication and role-based authorization.
- Only SuperAdmins can manage global/system settings; Admins can manage their shop settings.
- Setting schema supports `accessLevel`, `isEditable`, and `isVisible` for fine-grained UI/API control.

---

## 5. Validation & Auditability
- Schema-level validation for type, range, enums, required fields, and value patterns.
- All updates are tracked in a `history` array (value, user, timestamp, reason).
- Validation patterns are centralized for consistency.

---

## 6. Initialization & Defaults
- On startup, system settings are initialized (`startup/settingsInit.js`).
- Shop settings fall back to global defaults if not set.
- Static methods: `getByCategory`, `getShopSettings`, `getGlobalSettings`, `getValueWithDefault`.

---

## 7. Example Setting Document
```json
{
  "key": "enable_online_payment",
  "category": "payment",
  "displayName": "Enable Online Payment",
  "description": "Toggle online payment gateways for all shops.",
  "value": true,
  "dataType": "boolean",
  "defaultValue": true,
  "validation": {},
  "accessLevel": "superAdmin",
  "isEditable": true,
  "isVisible": true,
  "shopId": null,
  "updatedBy": "<EMAIL>",
  "updatedAt": "2025-06-05T13:00:00Z",
  "history": [
    {
      "value": false,
      "updatedBy": "<EMAIL>",
      "updatedAt": "2025-05-01T12:00:00Z",
      "reason": "Initial setup"
    }
  ]
}
```

---

## 8. Best Practices & Recommendations
- Use settings as feature flags for controlled rollouts.
- Expose an endpoint or admin UI for listing all settings and categories.
- Support bulk export/import for backup and migration.
- Cache frequently-read settings for performance (e.g., Redis).
- Expand categories as the platform grows (e.g., billing, api, reporting).
- Use audit trails for compliance and troubleshooting.

---

## 9. Related Files
- `models/setting.model.js`, `models/shopSetting.model.js`
- `controllers/settingsController.js`, `services/settingsService.js`
- `routes/settingsRoutes.js`, `startup/settingsInit.js`
- `utils/SettingsHelper.js`, `validations/validationPatterns.js`

---

## 10. Changelog
- [2025-06-05] Initial version: Full end-to-end documentation for DeynCare SaaS settings architecture.
