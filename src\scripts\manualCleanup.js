const mongoose = require('mongoose');
const { User } = require('../models');

// Manual cleanup for specific orphaned user
const cleanSpecificUser = async () => {
  try {
    // Connect to database
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('✅ Connected to database');

    // Find and clean the specific problematic user
    const email = '<EMAIL>';
    
    const user = await User.findOne({ email });
    if (!user) {
      console.log(`❌ User not found: ${email}`);
      return;
    }

    console.log(`🔍 Found user: ${email} (Status: ${user.status})`);
    
    if (user.status === 'pending_email_verification') {
      // Soft delete the orphaned user
      user.isDeleted = true;
      user.deletedAt = new Date();
      user.status = 'deleted_orphaned';
      await user.save();
      
      console.log(`✅ Successfully cleaned orphaned user: ${email}`);
    } else {
      console.log(`ℹ️ User status is ${user.status}, not cleaning`);
    }

  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    await mongoose.disconnect();
    console.log('📤 Disconnected from database');
  }
};

cleanSpecificUser(); 