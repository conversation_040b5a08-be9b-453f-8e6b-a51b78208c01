/**
 * Password Validation Middleware
 * 
 * @module passwordValidator
 * @description Validates password against security policy
 * @version 1.0.0
 * @since 2025-06-03
 */

const { Setting } = require('../models');
const { AppError } = require('../utils');

/**
 * Get current password policy settings from database
 * Fallback to default settings if not found
 * @returns {Object} Password policy settings
 */
async function getPasswordPolicy() {
  try {
    // Try to get password policy settings from database
    const settings = await Setting.find({ 
      category: 'security',
      key: { $regex: /^password\./ }
    });
    
    // Create settings object from results
    const policy = {};
    
    settings.forEach(setting => {
      const keyPart = setting.key.split('.')[1]; // password.minLength -> minLength
      policy[keyPart] = setting.value;
    });
    
    // Return with defaults for any missing settings
    return {
      minLength: policy.minLength || 8,
      requireUppercase: policy.requireUppercase !== false,
      requireLowercase: policy.requireLowercase !== false,
      requireNumbers: policy.requireNumbers !== false,
      requireSpecialChars: policy.requireSpecialChars !== false,
      passwordExpiryDays: policy.passwordExpiryDays || 90
    };
  } catch (error) {
    console.error('Error loading password policy:', error);
    // Fallback to secure defaults if we can't load from database
    return {
      minLength: 8,
      requireUppercase: true,
      requireLowercase: true,
      requireNumbers: true,
      requireSpecialChars: true,
      passwordExpiryDays: 90
    };
  }
}

/**
 * Validate a password against policy requirements
 * @param {string} password - Password to validate
 * @param {Object} policy - Password policy settings
 * @returns {Object} Validation result with success and errors
 */
function validatePassword(password, policy) {
  const errors = [];
  
  // Check minimum length
  if (password.length < policy.minLength) {
    errors.push(`Password must be at least ${policy.minLength} characters long`);
  }
  
  // Check for uppercase letters
  if (policy.requireUppercase && !/[A-Z]/.test(password)) {
    errors.push('Password must include at least one uppercase letter');
  }
  
  // Check for lowercase letters
  if (policy.requireLowercase && !/[a-z]/.test(password)) {
    errors.push('Password must include at least one lowercase letter');
  }
  
  // Check for numbers
  if (policy.requireNumbers && !/\d/.test(password)) {
    errors.push('Password must include at least one number');
  }
  
  // Check for special characters
  if (policy.requireSpecialChars && !/[^a-zA-Z0-9]/.test(password)) {
    errors.push('Password must include at least one special character');
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Middleware to validate password against policy
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware
 */
async function validatePasswordMiddleware(req, res, next) {
  try {
    // Only validate if there is a password in the request
    const password = req.body.password || req.body.newPassword;
    
    if (!password) {
      return next();
    }
    
    // Get current password policy
    const policy = await getPasswordPolicy();
    
    // Validate password
    const validation = validatePassword(password, policy);
    
    if (!validation.isValid) {
      throw new AppError(
        'Password does not meet security requirements', 
        400, 
        'invalid_password_format',
        { details: validation.errors }
      );
    }
    
    next();
  } catch (error) {
    next(error);
  }
}

module.exports = {
  validatePasswordMiddleware,
  validatePassword,
  getPasswordPolicy
};
