/**
 * App Upload API Service
 * Frontend service for app upload management operations
 */
import { apiRequest } from '../bridge';

const API_BASE = '/api/app';

/**
 * App Upload API Service
 */
export const appUploadAPI = {
  /**
   * Upload new app file (SuperAdmin only)
   * @param {FormData} formData - Form data containing app file and metadata
   * @returns {Promise<Object>} Upload result
   */
  uploadApp: async (formData) => {
    try {
      // Use apiRequest instead of direct fetch to ensure authentication headers are included
      const response = await apiRequest(`${API_BASE}/upload`, {
        method: 'POST',
        data: formData,
        headers: {
          // Don't set Content-Type for FormData, let browser set it with boundary
          // Authorization header will be automatically added by interceptors
        }
      });

      return response.data;
    } catch (error) {
      console.error('Error uploading app:', error);
      throw error;
    }
  },

  /**
   * Get all app uploads with filters (SuperAdmin only)
   * @param {Object} filters - Query filters
   * @returns {Promise<Object>} Apps list with pagination
   */
  getApps: async (filters = {}) => {
    try {
      const queryParams = new URLSearchParams();
      
      Object.keys(filters).forEach(key => {
        if (filters[key] !== undefined && filters[key] !== null && filters[key] !== '') {
          queryParams.append(key, filters[key]);
        }
      });

      const queryString = queryParams.toString();
      const url = queryString ? `${API_BASE}/list?${queryString}` : `${API_BASE}/list`;

      return await apiRequest(url, {
        method: 'GET'
      });
    } catch (error) {
      console.error('Error fetching apps:', error);
      throw error;
    }
  },

  /**
   * Get app upload by ID
   * @param {string} uploadId - Upload ID
   * @returns {Promise<Object>} App upload data
   */
  getAppById: async (uploadId) => {
    try {
      return await apiRequest(`${API_BASE}/${uploadId}`, {
        method: 'GET'
      });
    } catch (error) {
      console.error('Error fetching app by ID:', error);
      throw error;
    }
  },

  /**
   * Get latest app by platform
   * @param {string} platform - Platform (android, ios, etc.)
   * @returns {Promise<Object>} Latest app for platform
   */
  getLatestByPlatform: async (platform) => {
    try {
      return await apiRequest(`${API_BASE}/latest/${platform}`, {
        method: 'GET'
      });
    } catch (error) {
      console.error('Error fetching latest app:', error);
      throw error;
    }
  },

  /**
   * Download app file
   * @param {string} uploadId - Upload ID
   * @returns {Promise<void>} Triggers download
   */
  downloadApp: async (uploadId) => {
    try {
      const url = `${process.env.NEXT_PUBLIC_API_URL}${API_BASE}/download/${uploadId}`;
      window.open(url, '_blank');
    } catch (error) {
      console.error('Error downloading app:', error);
      throw error;
    }
  },

  /**
   * Download latest app (public endpoint)
   * @param {string} platform - Platform (optional, defaults to android)
   * @returns {Promise<void>} Triggers download
   */
  downloadLatest: async (platform = 'android') => {
    try {
      const url = `${process.env.NEXT_PUBLIC_API_URL}${API_BASE}/download/latest?platform=${platform}`;
      window.open(url, '_blank');
    } catch (error) {
      console.error('Error downloading latest app:', error);
      throw error;
    }
  },

  /**
   * Update app metadata (SuperAdmin only)
   * @param {string} uploadId - Upload ID
   * @param {Object} updateData - Update data
   * @returns {Promise<Object>} Updated app data
   */
  updateApp: async (uploadId, updateData) => {
    try {
      return await apiRequest(`${API_BASE}/${uploadId}`, {
        method: 'PUT',
        body: JSON.stringify(updateData)
      });
    } catch (error) {
      console.error('Error updating app:', error);
      throw error;
    }
  },

  /**
   * Delete app upload
   * @param {string} uploadId - Upload ID
   * @returns {Promise<Object>} Deletion result
   */
  deleteApp: async (uploadId) => {
    try {
      return await apiRequest(`${API_BASE}/${uploadId}`, {
        method: 'DELETE'
      });
    } catch (error) {
      console.error('Error deleting app:', error);
      throw error;
    }
  },

  /**
   * Set app as latest version (SuperAdmin only)
   * @param {string} uploadId - Upload ID
   * @param {string} platform - Platform
   * @returns {Promise<Object>} Update result
   */
  setLatestVersion: async (uploadId, platform) => {
    try {
      return await apiRequest(`${API_BASE}/${uploadId}/set-latest`, {
        method: 'PUT',
        body: JSON.stringify({ platform })
      });
    } catch (error) {
      console.error('Error setting latest version:', error);
      throw error;
    }
  },

  /**
   * Get download statistics (SuperAdmin only)
   * @param {Object} filters - Query filters
   * @returns {Promise<Object>} Statistics data
   */
  getStats: async (filters = {}) => {
    try {
      const queryParams = new URLSearchParams();
      
      Object.keys(filters).forEach(key => {
        if (filters[key] !== undefined && filters[key] !== null && filters[key] !== '') {
          queryParams.append(key, filters[key]);
        }
      });

      const queryString = queryParams.toString();
      const url = queryString ? `${API_BASE}/stats?${queryString}` : `${API_BASE}/stats`;

      return await apiRequest(url, {
        method: 'GET'
      });
    } catch (error) {
      console.error('Error fetching app stats:', error);
      throw error;
    }
  }
}; 