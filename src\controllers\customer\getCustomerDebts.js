const { Debt, Payment } = require('../../models');
const { AppError, logError } = require('../../utils');

/**
 * Get All Debts for Specific Customer
 * GET /api/customers/:customerId/debts
 */
const getCustomerDebts = async (req, res, next) => {
  try {
    const { customerId } = req.params;
    const shopId = req.user.shopId;
    
    // Query parameters for filtering and pagination
    const {
      page = 1,
      limit = 20,
      status,
      riskLevel,
      sortBy = 'createdAt',
      sortOrder = 'desc'
    } = req.query;

    // Build filter criteria
    const filter = { customerId, shopId, isDeleted: false };

    // Filter by status
    if (status) {
      switch (status) {
        case 'active':
          filter.OutstandingDebt = { $gt: 0 };
          filter.DueDate = { $gte: new Date() };
          break;
        case 'overdue':
          filter.OutstandingDebt = { $gt: 0 };
          filter.DueDate = { $lt: new Date() };
          break;
        case 'paid':
          filter.OutstandingDebt = { $lte: 0 };
          filter.PaidAmount = { $gt: 0 };
          break;
      }
    }

    // Filter by risk level
    if (riskLevel) {
      filter.RiskLevel = riskLevel;
    }

    // Sort options
    const sortOptions = {};
    sortOptions[sortBy] = sortOrder === 'desc' ? -1 : 1;

    // Pagination
    const skip = (parseInt(page) - 1) * parseInt(limit);
    const limitNum = parseInt(limit);

    // Get debts with payment information
    const debts = await Debt.aggregate([
      { $match: filter },
      {
        $lookup: {
          from: 'payments',
          localField: 'debtId',
          foreignField: 'debtId',
          as: 'payments',
          pipeline: [
            { 
              $match: { 
                paymentContext: 'debt',
                isDeleted: false 
              } 
            },
            { $sort: { paymentDate: -1 } }
          ]
        }
      },
      {
        $addFields: {
          paymentCount: { $size: '$payments' },
          lastPaymentDate: { $max: '$payments.paymentDate' },
          lastPaymentAmount: {
            $let: {
              vars: { firstPayment: { $arrayElemAt: ['$payments', 0] } },
              in: '$$firstPayment.amount'
            }
          }
        }
      },
      { $sort: sortOptions },
      { $skip: skip },
      { $limit: limitNum }
    ]);

    // Get total count for pagination
    const totalDebts = await Debt.countDocuments(filter);
    const totalPages = Math.ceil(totalDebts / limitNum);

    // Calculate summary statistics for this customer
    const summaryStats = await Debt.aggregate([
      { $match: { customerId, shopId, isDeleted: false } },
      {
        $group: {
          _id: null,
          totalDebts: { $sum: 1 },
          totalAmount: { $sum: '$DebtAmount' },
          totalOutstanding: { $sum: '$OutstandingDebt' },
          totalPaid: { $sum: '$PaidAmount' },
          averageDebtAmount: { $avg: '$DebtAmount' },
          averagePaymentRatio: { $avg: '$DebtPaidRatio' },
          activeDebts: {
            $sum: {
              $cond: [{ $gt: ['$OutstandingDebt', 0] }, 1, 0]
            }
          },
          overdueDebts: {
            $sum: {
              $cond: [
                {
                  $and: [
                    { $gt: ['$OutstandingDebt', 0] },
                    { $lt: ['$DueDate', new Date()] }
                  ]
                },
                1,
                0
              ]
            }
          }
        }
      }
    ]);

    // Format debt data for response
    const formattedDebts = debts.map(debt => ({
      debtId: debt.debtId,
      debtAmount: debt.DebtAmount,
      outstandingDebt: debt.OutstandingDebt,
      paidAmount: debt.PaidAmount || 0,
      debtPaidRatio: Math.round(debt.DebtPaidRatio * 100),
      dueDate: debt.DueDate,
      riskLevel: debt.RiskLevel,
      
      // ML fields
      paymentDelay: debt.PaymentDelay,
      repaymentTime: debt.RepaymentTime,
      isOnTime: debt.IsOnTime,
      
      // Status calculation
      status: debt.OutstandingDebt <= 0 
        ? 'Paid' 
        : (new Date() > new Date(debt.DueDate) ? 'Overdue' : 'Active'),
      
      // Payment info
      paymentCount: debt.paymentCount,
      lastPaymentDate: debt.lastPaymentDate,
      lastPaymentAmount: debt.lastPaymentAmount,
      
      // Dates
      createdAt: debt.createdAt,
      updatedAt: debt.updatedAt,
      
      // Notes
      notes: debt.notes
    }));

    // Risk distribution for this customer's debts
    const riskDistribution = await Debt.aggregate([
      { $match: { customerId, shopId, isDeleted: false } },
      {
        $group: {
          _id: '$RiskLevel',
          count: { $sum: 1 },
          totalAmount: { $sum: '$OutstandingDebt' }
        }
      }
    ]);

    const summary = summaryStats[0] || {
      totalDebts: 0,
      totalAmount: 0,
      totalOutstanding: 0,
      totalPaid: 0,
      averageDebtAmount: 0,
      averagePaymentRatio: 0,
      activeDebts: 0,
      overdueDebts: 0
    };

    res.json({
      success: true,
      message: 'Customer debts retrieved successfully',
      data: {
        customerId,
        debts: formattedDebts,
        
        pagination: {
          currentPage: parseInt(page),
          totalPages,
          totalDebts,
          hasNextPage: parseInt(page) < totalPages,
          hasPrevPage: parseInt(page) > 1
        },
        
        summary: {
          totalDebts: summary.totalDebts,
          activeDebts: summary.activeDebts,
          overdueDebts: summary.overdueDebts,
          paidDebts: summary.totalDebts - summary.activeDebts,
          
          financials: {
            totalAmount: summary.totalAmount,
            totalOutstanding: summary.totalOutstanding,
            totalPaid: summary.totalPaid,
            averageDebtAmount: Math.round(summary.averageDebtAmount || 0),
            collectionRate: summary.totalAmount > 0 
              ? Math.round((summary.totalPaid / summary.totalAmount) * 100) 
              : 0
          },
          
          riskDistribution: riskDistribution.reduce((acc, item) => {
            acc[item._id] = {
              count: item.count,
              totalAmount: item.totalAmount
            };
            return acc;
          }, {})
        },
        
        filters: {
          status: status || null,
          riskLevel: riskLevel || null
        }
      }
    });

  } catch (error) {
    logError('Failed to get customer debts', 'GetCustomerDebts', error);
    return next(new AppError('Failed to retrieve customer debts', 500));
  }
};

module.exports = getCustomerDebts; 