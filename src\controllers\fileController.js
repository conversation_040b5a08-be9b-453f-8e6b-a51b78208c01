const path = require('path');
const fs = require('fs').promises;
const { File } = require('../models');
const { AppError, logError, logInfo } = require('../utils');

/**
 * Serve file by fileId
 * GET /api/files/:fileId
 */
const serveFile = async (req, res, next) => {
  try {
    const { fileId } = req.params;
    
    if (!fileId) {
      return next(new AppError('File ID is required', 400, 'missing_file_id'));
    }
    
    // Look up file record in database
    const fileRecord = await File.findOne({ 
      fileId, 
      isDeleted: false 
    });
    
    if (!fileRecord) {
      return res.status(404).json({
        success: false,
        message: 'File not found',
        error: 'The requested file does not exist or has been removed',
        statusCode: 404,
        errorCode: 'file_not_found'
      });
    }
    
    // Check if file has expired
    if (fileRecord.expiresAt && new Date() > fileRecord.expiresAt) {
      return res.status(410).json({
        success: false,
        message: 'File has expired',
        error: 'The requested file has expired and is no longer available',
        statusCode: 410,
        errorCode: 'file_expired'
      });
    }
    
    // Construct file path
    let filePath;
    
    // If the file record has a path field, use it
    if (fileRecord.path) {
      filePath = fileRecord.path;
    } else {
      // Fallback: construct path based on file type and fileName
      const baseUploadDir = path.join(__dirname, '../../uploads');
      
      if (fileRecord.fileType === 'logo') {
        filePath = path.join(baseUploadDir, 'shop-logos', fileRecord.fileName);
      } else if (fileRecord.fileType === 'payment-proof') {
        filePath = path.join(baseUploadDir, 'payment-proofs', fileRecord.fileName);
      } else {
        filePath = path.join(baseUploadDir, fileRecord.fileName);
      }
    }
    
    // Check if file exists on disk
    try {
      await fs.access(filePath);
    } catch (error) {
      logError(`File not found on disk: ${filePath}`, 'FileController');
      return res.status(404).json({
        success: false,
        message: 'File not found on disk',
        error: 'The file exists in database but not on disk',
        statusCode: 404,
        errorCode: 'file_not_found_on_disk'
      });
    }
    
    // Get file stats for proper headers
    const stats = await fs.stat(filePath);
    
    // Set appropriate headers
    const ext = path.extname(filePath).toLowerCase();
    let contentType = 'application/octet-stream';
    
    // Set content type based on file extension
    const mimeTypes = {
      '.jpg': 'image/jpeg',
      '.jpeg': 'image/jpeg',
      '.png': 'image/png',
      '.gif': 'image/gif',
      '.webp': 'image/webp',
      '.pdf': 'application/pdf'
    };
    
    if (mimeTypes[ext]) {
      contentType = mimeTypes[ext];
    }
    
    // Set headers
    res.set({
      'Content-Type': contentType,
      'Content-Length': stats.size,
      'Cache-Control': 'public, max-age=86400', // Cache for 1 day
      'ETag': `"${stats.mtime.getTime()}-${stats.size}"`,
      'Last-Modified': stats.mtime.toUTCString()
    });
    
    // Handle conditional requests
    const ifNoneMatch = req.headers['if-none-match'];
    const ifModifiedSince = req.headers['if-modified-since'];
    
    if (ifNoneMatch === `"${stats.mtime.getTime()}-${stats.size}"` ||
        (ifModifiedSince && new Date(ifModifiedSince) >= stats.mtime)) {
      return res.status(304).end();
    }
    
    // For images, set additional headers
    if (contentType.startsWith('image/')) {
      res.set({
        'X-Content-Type-Options': 'nosniff',
        'Content-Disposition': 'inline'
      });
    }
    
    // Stream the file
    const readStream = require('fs').createReadStream(filePath);
    
    readStream.on('error', (error) => {
      logError(`Error streaming file ${fileId}: ${error.message}`, 'FileController');
      if (!res.headersSent) {
        res.status(500).json({
          success: false,
          message: 'Error serving file',
          error: 'Internal server error while serving file',
          statusCode: 500,
          errorCode: 'file_stream_error'
        });
      }
    });
    
    // Pipe the file to response
    readStream.pipe(res);
    
    // Log successful file access (optional, for analytics)
    logInfo(`File served: ${fileId} (${fileRecord.fileType})`, 'FileController');
    
  } catch (error) {
    logError(`Error serving file: ${error.message}`, 'FileController', error);
    next(error);
  }
};

/**
 * Get file metadata by fileId
 * GET /api/files/:fileId/metadata
 */
const getFileMetadata = async (req, res, next) => {
  try {
    const { fileId } = req.params;
    
    if (!fileId) {
      return next(new AppError('File ID is required', 400, 'missing_file_id'));
    }
    
    // Look up file record in database
    const fileRecord = await File.findOne({ 
      fileId, 
      isDeleted: false 
    }).select('-path'); // Don't expose file system path
    
    if (!fileRecord) {
      return res.status(404).json({
        success: false,
        message: 'File not found',
        statusCode: 404,
        errorCode: 'file_not_found'
      });
    }
    
    res.json({
      success: true,
      data: {
        fileId: fileRecord.fileId,
        fileName: fileRecord.fileName,
        originalName: fileRecord.originalName,
        fileType: fileRecord.fileType,
        size: fileRecord.size,
        extension: fileRecord.extension,
        uploadedAt: fileRecord.uploadedAt,
        linkedEntityType: fileRecord.linkedEntityType,
        linkedEntityId: fileRecord.linkedEntityId,
        description: fileRecord.description,
        isPublic: fileRecord.isPublic,
        metadata: fileRecord.metadata,
        url: fileRecord.url
      }
    });
    
  } catch (error) {
    logError(`Error getting file metadata: ${error.message}`, 'FileController', error);
    next(error);
  }
};

module.exports = {
  serveFile,
  getFileMetadata
};
