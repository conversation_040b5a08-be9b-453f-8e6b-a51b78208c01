"use client";

/**
 * Settings Context - Provider Component
 * Optimized provider with memoization, combined fetching, and enhanced error handling
 */

import React, { useReducer, useRef, useMemo, useEffect, useCallback } from 'react';
import { useAuth } from '@/contexts/auth-context';
import { SettingsContext } from './index';
import { initialState } from './initial-state';
import { settingsReducer } from './reducer';
import { throttleRequest } from './utils/throttle';
import { ACTION_TYPES } from './types';
import { toast } from 'sonner';

// Import actions
import { fetchSettings, updateSettings, clearError } from './actions/general';
import { fetchSecuritySettings, updateSecuritySettings } from './actions/security';
// Shop settings actions removed
import { fetchSystemLogs, exportSystemLogs } from './actions/logs';

// Standardized response formatter
const formatResponse = (success, data = null, error = null) => ({
  success,
  data,
  error,
  timestamp: new Date().toISOString()
});

/**
 * Settings provider component with optimizations
 * Provides global access to settings state and operations
 */
export const SettingsProvider = ({ children }) => {
  // Request tracking to prevent multiple simultaneous requests
  const requestInProgress = useRef(false);
  const requestTimeouts = useRef({});
  const abortControllers = useRef({});
  const requestCache = useRef(new Map());
  
  // State management
  const [state, dispatch] = useReducer(settingsReducer, initialState);
  const { isSuperAdmin } = useAuth();
  
  // Enhanced throttle implementation with retry capability and request cancellation
  const throttle = useCallback((key, fn, delay = 2000, maxRetries = 3) => {
    // Check cache first
    const cachedRequest = requestCache.current.get(key);
    if (cachedRequest && Date.now() - cachedRequest.timestamp < 5000) {
      console.log(`[Settings] Using cached request for ${key}`);
      return Promise.resolve(cachedRequest.data);
    }

    // Create abort controller for this request if it doesn't exist
    if (!abortControllers.current[key]) {
      abortControllers.current[key] = new AbortController();
    }
    
    // Cancel any previous request with the same key
    const cancelPrevious = () => {
      if (abortControllers.current[key]) {
        abortControllers.current[key].abort();
        abortControllers.current[key] = new AbortController();
      }
    };
    
    // Wrap function with retry logic and caching
    const withRetry = async () => {
      let retries = 0;
      let lastError = null;
      
      while (retries <= maxRetries) {
        try {
          // Provide signal to the operation
          const signal = abortControllers.current[key].signal;
          const result = await fn(signal);
          
          // Cache successful result
          requestCache.current.set(key, {
            data: result,
            timestamp: Date.now()
          });
          
          return result;
        } catch (error) {
          lastError = error;
          
          // Don't retry if request was aborted or if max retries reached
          if (error.name === 'AbortError' || retries >= maxRetries) {
            break;
          }
          
          // Increment retry counter
          retries++;
          
          // Log retry attempt
          console.warn(`Retrying '${key}' operation (${retries}/${maxRetries})`);
          
          // Wait with exponential backoff before retrying
          await new Promise(resolve => setTimeout(resolve, 1000 * Math.pow(2, retries)));
        }
      }
      
      // If we get here, all retries failed
      if (lastError) {
        console.error(`Operation '${key}' failed after ${retries} retries:`, lastError);
        throw lastError;
      }
    };
    
    return throttleRequest(requestTimeouts.current, requestInProgress, key, withRetry, delay, cancelPrevious);
  }, []);
  
  // Load settings on mount if user is superAdmin, with optimization for combined fetching
  useEffect(() => {
    if (isSuperAdmin()) {
      // Combined initial data fetch to reduce render cycles
      const fetchInitialData = async () => {
        dispatch({ type: ACTION_TYPES.SET_LOADING, payload: true });
        
        try {
          // Fetch basic settings data
          const settingsData = await fetchSettings(dispatch, throttle);
          
          // Batch update state with initial data
          if (settingsData.success) {
            dispatch({
              type: ACTION_TYPES.SET_INITIAL_DATA,
              payload: {
                settings: settingsData.data
              }
            });
          }
        } catch (error) {
          console.error('Error loading initial settings data:', error);
          dispatch({ type: ACTION_TYPES.SET_ERROR, payload: 'Failed to load initial settings' });
        } finally {
          dispatch({ type: ACTION_TYPES.SET_LOADING, payload: false });
        }
      };
      
      fetchInitialData();
    }
  }, [isSuperAdmin, throttle]);
  
  // Memoized action methods with improved consistency and data refresh patterns
  const fetchSettingsAction = useCallback(
    async (category = 'all') => {
      try {
        const result = await fetchSettings(dispatch, throttle, category);
        return formatResponse(true, result.data);
      } catch (error) {
        toast.error(`Failed to fetch ${category} settings: ${error.message || 'Unknown error'}`);
        return formatResponse(false, null, error.message);
      }
    },
    [throttle]
  );
  
  const updateSettingsAction = useCallback(
    async (updatedSettings) => {
      try {
        // Perform update operation
        const result = await updateSettings(dispatch, throttle, updatedSettings);
        
        // If successful, fetch fresh data to ensure consistency
        if (result?.success) {
          await fetchSettings(dispatch, throttle, 'all');
          toast.success('Settings updated successfully');
        }
        
        return formatResponse(true, result.data);
      } catch (error) {
        toast.error(`Failed to update settings: ${error.message || 'Unknown error'}`);
        return formatResponse(false, null, error.message);
      }
    },
    [throttle]
  );
  
  const fetchSecuritySettingsAction = useCallback(
    async () => {
      try {
        const result = await fetchSecuritySettings(dispatch, throttle);
        return formatResponse(true, result.data);
      } catch (error) {
        toast.error(`Failed to fetch security settings: ${error.message || 'Unknown error'}`);
        return formatResponse(false, null, error.message);
      }
    },
    [throttle]
  );
  
  const updateSecuritySettingsAction = useCallback(
    async (securitySettings) => {
      try {
        // Perform update operation
        const result = await updateSecuritySettings(dispatch, throttle, securitySettings);
        
        // If successful, fetch fresh data to ensure consistency
        if (result?.success) {
          await fetchSecuritySettings(dispatch, throttle);
          toast.success('Security settings updated successfully');
        }
        
        return formatResponse(true, result.data);
      } catch (error) {
        toast.error(`Failed to update security settings: ${error.message || 'Unknown error'}`);
        return formatResponse(false, null, error.message);
      }
    },
    [throttle]
  );
  
  const fetchSystemLogsAction = useCallback(
    async (filters = {}) => {
      try {
        const result = await fetchSystemLogs(dispatch, throttle, filters);
        return formatResponse(true, result.data);
      } catch (error) {
        toast.error(`Failed to fetch system logs: ${error.message || 'Unknown error'}`);
        return formatResponse(false, null, error.message);
      }
    },
    [throttle]
  );
  
  const exportSystemLogsAction = useCallback(
    async (filters = {}) => {
      try {
        const result = await exportSystemLogs(dispatch, filters);
        return formatResponse(true, result.data);
      } catch (error) {
        toast.error(`Failed to export system logs: ${error.message || 'Unknown error'}`);
        return formatResponse(false, null, error.message);
      }
    },
    []
  );
  
  const clearErrorAction = useCallback(
    () => {
      clearError(dispatch);
      return formatResponse(true);
    },
    []
  );
  
  // Add cleanup for any pending requests on unmount
  useEffect(() => {
    return () => {
      // Abort any pending requests when the provider unmounts
      Object.values(abortControllers.current).forEach(controller => {
        if (controller) {
          try {
            controller.abort();
          } catch (e) {
            console.error('Error aborting controller:', e);
          }
        }
      });
      
      // Clear any pending timeouts
      Object.values(requestTimeouts.current).forEach(timeoutId => {
        if (timeoutId) {
          clearTimeout(timeoutId);
        }
      });
    };
  }, []);
  
  // Helper to check if current user has appropriate permissions
  const checkPermission = useCallback(() => {
    const hasSuperAdmin = isSuperAdmin();
    if (!hasSuperAdmin) {
      console.warn('Settings operations require SuperAdmin privileges');
    }
    return hasSuperAdmin;
  }, [isSuperAdmin]);
  
  // Enhanced context value with utility functions and security checks
  const contextValue = useMemo(() => {
    // Create a plain object with all the necessary data
    const plainContext = {
      // State properties
      settings: state.settings,
      settingsByCategory: state.settingsByCategory,
      securitySettings: state.securitySettings,
      systemLogs: state.systemLogs,
      loading: state.loading,
      error: state.error,
      
      // Utility properties
      hasPermission: checkPermission(),
      
      // Memoized actions with role checks
      fetchSettings: (...args) => {
        if (!checkPermission()) {
          return formatResponse(false, null, 'Insufficient permissions');
        }
        return fetchSettingsAction(...args);
      },
      updateSettings: (...args) => {
        if (!checkPermission()) {
          toast.error('You do not have permission to update settings');
          return formatResponse(false, null, 'Insufficient permissions');
        }
        return updateSettingsAction(...args);
      },
      fetchSecuritySettings: fetchSecuritySettingsAction,
      updateSecuritySettings: (...args) => {
        if (!checkPermission()) {
          toast.error('You do not have permission to update security settings');
          return formatResponse(false, null, 'Insufficient permissions');
        }
        return updateSecuritySettingsAction(...args);
      },
      fetchSystemLogs: fetchSystemLogsAction,
      exportSystemLogs: exportSystemLogsAction,
      clearError: clearErrorAction
    };

    // Add a method to cancel pending requests
    plainContext.cancelPendingRequests = () => {
      Object.entries(abortControllers.current).forEach(([key, controller]) => {
        if (controller) controller.abort();
        abortControllers.current[key] = new AbortController();
      });
    };

    return plainContext;
  }, [
    state,
    checkPermission,
    fetchSettingsAction,
    updateSettingsAction,
    fetchSecuritySettingsAction,
    updateSecuritySettingsAction,
    fetchSystemLogsAction,
    exportSystemLogsAction,
    clearErrorAction
  ]);
  
  return (
    <SettingsContext.Provider value={contextValue}>
      {children}
    </SettingsContext.Provider>
  );
};
