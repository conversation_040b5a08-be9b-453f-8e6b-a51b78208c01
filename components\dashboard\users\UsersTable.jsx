import React, { use<PERSON>emo, useCallback } from 'react';
import PropTypes from 'prop-types';
import { DataTable } from '@/components/dashboard/common/data-table';
import { Card, CardContent } from "@/components/ui/card";
import {
  MoreVertical,
  Eye,
  Edit,
  AlertTriangle,
  Shield,
  Trash2,
  User,
  UserCog,
  Users
} from 'lucide-react';
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

/**
 * UsersTable Component - Displays user data in a table format with actions
 * 
 * @param {Object} props - Component props
 * @param {Array<Object>} props.users - Array of user objects to display
 * @param {Object} props.pagination - Pagination configuration
 * @param {boolean} props.isLoading - Whether data is loading
 * @param {Function} props.onPageChange - Callback when page changes
 * @param {Function} props.onPageSizeChange - Callback when page size changes
 * @param {Function} props.onViewDetails - Callback when view action is triggered
 * @param {Function} props.onEditUser - Callback when edit action is triggered
 * @param {Function} props.onChangeStatus - Callback when status change is triggered
 * @param {Function} props.onDeleteUser - Callback when delete action is triggered
 * @param {string} props.currentUserRole - Current user's role for permission checks
 * @returns {JSX.Element} Rendered component
 */
export const UsersTable = ({ 
  users, 
  pagination, 
  isLoading, 
  onPageChange,
  onPageSizeChange,
  onViewDetails,
  onEditUser,
  onChangeStatus,
  onDeleteUser,
  currentUserRole
}) => {
  // Development mode logging
  if (process.env.NODE_ENV !== 'production') {
    console.log('[UsersTable] Rendering with:', { 
      userCount: users?.length || 0, 
      isLoading,
      currentUserRole
    });
  }
  
  /**
   * Determine if current user can edit the target user based on roles
   * @param {string} userRole - Role of the target user
   * @returns {boolean} Whether current user can edit target user
   */
  const canEdit = useCallback((userRole) => {
    if (!currentUserRole) return false;
    if (currentUserRole.toLowerCase() === 'superadmin') return true;
    if (currentUserRole.toLowerCase() === 'admin' && userRole?.toLowerCase() !== 'superadmin') return true;
    return false;
  }, [currentUserRole]);

  /**
   * Determine if current user can delete the target user
   * @param {string} userRole - Role of the target user
   * @returns {boolean} Whether current user can delete target user
   */
  const canDelete = useCallback((userRole) => {
    // Only superadmin can delete users, and they cannot delete other superadmins
    if (!currentUserRole) return false;
    return currentUserRole.toLowerCase() === 'superadmin' && userRole?.toLowerCase() !== 'superadmin';
  }, [currentUserRole]);

  /**
   * Determine if current user can change status of target user
   * @param {string} userRole - Role of the target user
   * @returns {boolean} Whether current user can change target user's status
   */
  const canChangeStatus = useCallback((userRole) => {
    // Superadmins can change status of any non-superadmin user
    // Admins can change status of employees but not other admins or superadmins
    if (!currentUserRole) return false;
    if (currentUserRole.toLowerCase() === 'superadmin' && userRole?.toLowerCase() !== 'superadmin') return true;
    if (currentUserRole.toLowerCase() === 'admin' && userRole?.toLowerCase() === 'employee') return true;
    return false;
  }, [currentUserRole]);

  /**
   * Get role icon based on role value
   * @param {string} role - User role
   * @returns {JSX.Element} Icon component
   */
  const getRoleIcon = useCallback((role) => {
    const normalizedRole = role?.toLowerCase() || '';
    if (normalizedRole === 'superadmin') return <Shield className="h-4 w-4 text-purple-500" />;
    if (normalizedRole === 'admin') return <UserCog className="h-4 w-4 text-blue-500" />;
    return <User className="h-4 w-4 text-gray-500" />;
  }, []);

  /**
   * Format role for display
   * @param {string} role - User role
   * @returns {string} Formatted role name
   */
  const formatRole = useCallback((role) => {
    if (!role) return 'Unknown';
    const normalizedRole = role.toLowerCase();
    if (normalizedRole === 'superadmin') return 'Super Admin';
    if (normalizedRole === 'admin') return 'Admin';
    if (normalizedRole === 'employee') return 'Employee';
    return role; // Return original if not matched
  }, []);

  /**
   * Get status badge styling
   * @param {string} status - User status
   * @returns {JSX.Element} Badge component
   */
  const getStatusBadge = useCallback((status) => {
    if (!status) return <Badge variant="outline">Unknown</Badge>;
    
    const normalizedStatus = status.toLowerCase();
    if (normalizedStatus === 'active') return <Badge className="bg-green-500">Active</Badge>;
    if (normalizedStatus === 'inactive') return <Badge variant="secondary">Inactive</Badge>;
    if (normalizedStatus === 'suspended') return <Badge variant="destructive">Suspended</Badge>;
    
    return <Badge variant="outline">{status}</Badge>;
  }, []);

  // Define columns for the data table using useMemo for performance optimization
  const columns = useMemo(() => [
    {
      accessorKey: 'fullName',
      header: 'Name',
      cell: ({ row }) => {
        const user = row.original;
        return (
          <div className="flex items-center gap-2">
            {getRoleIcon(user.role)}
            <span className="font-medium">{user.fullName}</span>
          </div>
        );
      }
    },
    {
      accessorKey: 'email',
      header: 'Email',
    },
    {
      accessorKey: 'role',
      header: 'Role',
      cell: ({ row }) => {
        const role = row.original.role?.toLowerCase() || '';
        
        // Professional role badges aligned with authorization levels
        if (role === 'superadmin') {
          return (
            <Badge className="bg-purple-100 text-purple-800 hover:bg-purple-200 border-transparent">
              Super Admin
            </Badge>
          );
        } else if (role === 'admin') {
          return (
            <Badge className="bg-blue-100 text-blue-800 hover:bg-blue-200 border-transparent">
              Admin
            </Badge>
          );
        } else if (role === 'employee') {
          return (
            <Badge className="bg-gray-100 text-gray-800 hover:bg-gray-200 border-transparent">
              Employee
            </Badge>
          );
        }
        
        // Default fallback for unknown roles
        return <span className="text-gray-500">{formatRole(row.original.role) || 'Unknown'}</span>;
      }
    },
    {
      accessorKey: 'shopName',
      header: 'Shop',
      cell: ({ row }) => {
        const user = row.original;
        const isSuperAdmin = user.role?.toLowerCase() === 'superadmin';
        
        // If user has a shop name, display it
        if (user.shopName) {
          return <span className="font-medium">{user.shopName}</span>;
        }
        
        // For superAdmin without shop, show global access badge
        if (isSuperAdmin) {
          return (
            <div className="flex items-center">
              <Badge variant="outline" className="bg-purple-50 text-purple-700 border-purple-200 hover:bg-purple-100">
                Global Access
              </Badge>
            </div>
          );
        }
        
        // For other users without shop
        return <span className="text-gray-500 italic">No Shop Assigned</span>;
      }
    },
    {
      accessorKey: 'status',
      header: 'Status',
      cell: ({ row }) => {
        const status = row.original.status?.toLowerCase() || '';
        
        if (status === 'active') {
          return (
            <div className="flex items-center gap-1.5">
              <div className="h-2 w-2 rounded-full bg-green-500"></div>
              <span className="font-medium text-green-700">Active</span>
            </div>
          );
        } else if (status === 'inactive') {
          return (
            <div className="flex items-center gap-1.5">
              <div className="h-2 w-2 rounded-full bg-gray-400"></div>
              <span className="font-medium text-gray-600">Inactive</span>
            </div>
          );
        } else if (status === 'suspended') {
          return (
            <div className="flex items-center gap-1.5">
              <div className="h-2 w-2 rounded-full bg-red-500"></div>
              <span className="font-medium text-red-700">Suspended</span>
            </div>
          );
        }
        
        // Default fallback
        return <span className="text-gray-500">{row.original.status || 'Unknown'}</span>;
      }
    },
    {
      id: 'actions',
      cell: ({ row }) => {
        const user = row.original;
        const isUserSuperAdmin = user.role?.toLowerCase() === 'superadmin';
        const isUserActive = user.status?.toLowerCase() === 'active';
        
        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <MoreVertical className="h-5 w-5" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-[200px] p-2 bg-popover border border-border shadow-md dark:bg-gray-900 dark:border-gray-800">
              {/* View Details - matches getUserById controller function */}
              <DropdownMenuItem 
                onClick={() => onViewDetails(user)}
                className="flex items-center px-3 py-2 text-sm hover:bg-slate-50 dark:hover:bg-gray-800 rounded-md dark:text-gray-100"
              >
                <Eye className="mr-2 h-4 w-4 text-slate-500 dark:text-gray-300" />
                <span>View Details</span>
              </DropdownMenuItem>
              
              {/* Edit User - matches updateUser controller function */}
              {canEdit(user.role) && (
                <DropdownMenuItem 
                  onClick={() => onEditUser(user)}
                  className="flex items-center px-3 py-2 text-sm hover:bg-slate-50 dark:hover:bg-gray-800 rounded-md dark:text-gray-100"
                >
                  <Edit className="mr-2 h-4 w-4 text-slate-500 dark:text-gray-300" />
                  <span>Edit User</span>
                </DropdownMenuItem>
              )}
              
              {/* Status change options - only for non-superadmin users */}
              {canChangeStatus(user.role) && (
                <>
                  <DropdownMenuSeparator className="my-2 dark:bg-gray-700" />
                  
                  {isUserActive ? (
                    <DropdownMenuItem 
                      onClick={() => onChangeStatus(user, 'suspend')}
                      className="flex items-center px-3 py-2 text-sm hover:bg-amber-50 dark:hover:bg-amber-900/30 rounded-md dark:text-amber-200"
                    >
                      <AlertTriangle className="mr-2 h-4 w-4 text-amber-500" />
                      <span className="text-amber-600 font-medium">Suspend User</span>
                    </DropdownMenuItem>
                  ) : (
                    <DropdownMenuItem 
                      onClick={() => onChangeStatus(user, 'activate')}
                      className="flex items-center px-3 py-2 text-sm hover:bg-green-50 dark:hover:bg-green-900/30 rounded-md dark:text-green-200"
                    >
                      <Shield className="mr-2 h-4 w-4 text-green-500" />
                      <span className="text-green-600 font-medium">Activate User</span>
                    </DropdownMenuItem>
                  )}
                </>
              )}
              
              {/* Delete option - only for non-superadmin users, only visible to superadmins */}
              {canDelete(user.role) && (
                <DropdownMenuItem 
                  onClick={() => onDeleteUser(user)}
                  className="flex items-center px-3 py-2 text-sm hover:bg-red-50 dark:hover:bg-red-900/30 rounded-md mt-1 dark:text-red-200"
                >
                  <Trash2 className="mr-2 h-4 w-4 text-red-500" />
                  <span className="text-red-600 font-medium">Delete User</span>
                </DropdownMenuItem>
              )}
            </DropdownMenuContent>
          </DropdownMenu>
        );
      }
    }
  ], [getRoleIcon, formatRole, onViewDetails, onEditUser, onChangeStatus, onDeleteUser, canEdit, canChangeStatus, canDelete]);

  return (
    <Card>
      <CardContent className="p-0">
        <DataTable
          columns={columns}
          data={users || []}
          pagination={true}
          currentPage={pagination?.currentPage || 1}
          pageSize={pagination?.pageSize || 10}
          totalItems={pagination?.totalItems || 0}
          totalPages={pagination?.totalPages || 1}
          isLoading={isLoading}
          onPageChange={onPageChange}
          onPageSizeChange={onPageSizeChange}
        />
      </CardContent>
    </Card>
  );
};

/**
 * PropTypes for type validation during development
 */
UsersTable.propTypes = {
  // Data props
  users: PropTypes.arrayOf(
    PropTypes.shape({
      id: PropTypes.string,
      _id: PropTypes.string,
      fullName: PropTypes.string,
      email: PropTypes.string,
      phone: PropTypes.string,
      role: PropTypes.string,
      status: PropTypes.string,
      createdAt: PropTypes.string,
      updatedAt: PropTypes.string
    })
  ),
  
  // Pagination props
  pagination: PropTypes.shape({
    currentPage: PropTypes.number,
    pageSize: PropTypes.number,
    totalItems: PropTypes.number,
    totalPages: PropTypes.number
  }),
  
  // UI state props
  isLoading: PropTypes.bool,
  
  // Action callbacks
  onPageChange: PropTypes.func,
  onPageSizeChange: PropTypes.func,
  onViewDetails: PropTypes.func,
  onEditUser: PropTypes.func,
  onChangeStatus: PropTypes.func,
  onDeleteUser: PropTypes.func,
  
  // Permission props
  currentUserRole: PropTypes.string
};

export default UsersTable;
