/**
 * Set EVC Credentials Service
 * Matches backend: settingsController.setEVCCredentials
 * POST /api/settings/evc-credentials
 */

import { setEvcCredentials as setEvcCredentialsAPI } from '../../api/modules/settings';
import { handleError, logApiCall } from '../baseService';

/**
 * Set/Update EVC payment credentials
 * @param {Object} credentials - EVC credentials object
 * @param {string} credentials.merchantUId - Merchant UID
 * @param {string} credentials.apiUserId - API User ID  
 * @param {string} credentials.apiKey - API Key
 * @param {string} credentials.merchantNo - Merchant Number
 * @param {string} [credentials.url] - API URL (optional, defaults to Waafi URL)
 * @param {string|null} [credentials.shopId] - Shop ID for shop-specific credentials, null for global
 * @returns {Promise<Object>} Set credentials result
 */
const setEvcCredentials = async (credentials) => {
  const context = `setEvcCredentials${credentials.shopId ? `(shopId: ${credentials.shopId})` : '(global)'}`;
  
  try {
    // Validate required fields (matching backend validation)
    const requiredFields = ['merchantUId', 'apiUserId', 'apiKey', 'merchantNo'];
    const missingFields = requiredFields.filter(field => !credentials[field] || credentials[field].trim() === '');
    
    if (missingFields.length > 0) {
      throw new Error(`Missing required EVC credentials: ${missingFields.join(', ')}`);
    }
    
    // Prepare payload in exact backend expected format
    const payload = {
      merchantUId: credentials.merchantUId.trim(),
      apiUserId: credentials.apiUserId.trim(),
      apiKey: credentials.apiKey.trim(),
      merchantNo: credentials.merchantNo.trim(),
      url: credentials.url?.trim() || 'https://api.waafipay.net/asm',
      shopId: credentials.shopId || null
    };
    
    logApiCall(context, 'POST /api/settings/evc-credentials', { 
      ...payload, 
      apiKey: '[REDACTED]' // Don't log sensitive data
    });
    
    const response = await setEvcCredentialsAPI(payload);
    
    if (response?.success) {
      console.log(`[${context}] Success:`, {
        message: response.message
      });
      
      return {
        success: true,
        message: response.message || 'EVC credentials saved successfully'
      };
    } else {
      console.warn(`[${context}] API returned success=false:`, response);
      return {
        success: false,
        message: response?.message || 'Failed to save EVC credentials'
      };
    }
    
  } catch (error) {
    console.error(`[${context}] Error:`, error);
    handleError(error, context, false);
    
    return {
      success: false,
      message: error.message || 'Failed to save EVC credentials',
      error: error
    };
  }
};

export default setEvcCredentials; 