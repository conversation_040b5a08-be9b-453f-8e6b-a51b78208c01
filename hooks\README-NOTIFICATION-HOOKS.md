# DeynCare Notification Hooks - Backend-Matched Implementation

This document outlines the React hooks for notification management in the DeynCare system. All hooks are **100% backend-matched** and follow the existing patterns established in the codebase.

## 🎯 Available Hooks

### 1. `useNotifications`
**Main notification hook for SuperAdmin operations**

```jsx
import { useNotifications } from '@/hooks/use-notifications';

const NotificationComponent = () => {
  const {
    sendToShops,
    sendBroadcast,
    sendDebtReminders,
    getNotificationStats,
    testFirebaseConnection,
    getNotificationTargets,
    isLoading,
    error,
    clearError
  } = useNotifications({
    showToastMessages: true,
    onSuccess: (operation, result) => console.log('Success:', operation, result),
    onError: (operation, error) => console.error('Error:', operation, error)
  });

  return (
    <div>
      {/* Your notification UI components */}
    </div>
  );
};
```

**Available Methods:**
- `sendToShops(notificationData)` - Send to specific shops
- `sendBroadcast(notificationData)` - Send broadcast notification
- `sendDebtReminders(reminderData)` - Send debt reminders (7_days, 3_days, overdue)
- `getNotificationStats(options)` - Get notification statistics
- `testFirebaseConnection()` - Test Firebase connection
- `getNotificationTargets()` - Get available targets

### 2. `useFCMToken`
**FCM token management hook**

```jsx
import { useFCMToken } from '@/hooks/use-fcm-token';

const FCMComponent = () => {
  const {
    registerToken,
    sendTestNotification,
    isRegistering,
    isSendingTest,
    error,
    validateTokenData
  } = useFCMToken({
    showToastMessages: true,
    onTokenRegistered: (result) => console.log('Token registered:', result)
  });

  const handleRegisterToken = async () => {
    await registerToken({
      token: 'fcm-token-string',
      deviceInfo: {
        deviceId: 'device-123',
        platform: 'android', // Only 'android' or 'ios' accepted
        appVersion: '1.0.0',
        osVersion: '10.0'
      }
    });
  };

  return (
    <div>
      {/* FCM token UI */}
    </div>
  );
};
```

**Available Methods:**
- `registerToken(tokenData)` - Register FCM token
- `sendTestNotification()` - Send test notification
- `validateTokenData(tokenData)` - Validate token data

### 3. `useNotificationStats`
**Statistics and system status hook**

```jsx
import { useNotificationStats } from '@/hooks/use-notification-stats';

const StatsComponent = () => {
  const {
    stats,
    targets,
    systemStatus,
    loadStats,
    loadTargets,
    testFirebase,
    refreshAll,
    isLoading
  } = useNotificationStats({
    autoRefresh: true,
    refreshInterval: 30000, // 30 seconds
    showToastMessages: true
  });

  return (
    <div>
      {/* Statistics dashboard */}
    </div>
  );
};
```

**Available Methods:**
- `loadStats(options)` - Load notification statistics
- `loadTargets()` - Load notification targets
- `testFirebase()` - Test Firebase connection
- `getSystemStatus()` - Get comprehensive system status
- `refreshAll()` - Refresh all data

## 🔗 Backend Endpoint Mapping

### ✅ Available Endpoints
| Hook Method | Backend Endpoint | HTTP Method | Description |
|------------|------------------|-------------|-------------|
| `sendToShops` | `/api/admin/notifications/push/shops` | POST | Send to specific shops |
| `sendBroadcast` | `/api/admin/notifications/push/broadcast` | POST | Send broadcast |
| `sendDebtReminders` | `/api/admin/notifications/push/debt-reminders` | POST | Send debt reminders |
| `getNotificationStats` | `/api/admin/notifications/push/stats` | GET | Get statistics |
| `testFirebaseConnection` | `/api/admin/notifications/push/test` | GET | Test Firebase |
| `getNotificationTargets` | `/api/admin/notifications/push/targets` | GET | Get targets |
| `registerToken` | `/api/fcm/register` | POST | Register FCM token |
| `sendTestNotification` | `/api/fcm/test` | POST | Send test notification |

### ❌ Non-Existent Endpoints
These endpoints do **NOT** exist in the backend and are **NOT** implemented:
- `/api/fcm/unregister` - Unregister FCM token
- `/api/fcm/tokens` - Get user FCM tokens
- `/api/fcm/usage` - Update token usage
- `/api/fcm/cleanup` - Cleanup expired tokens

## 📝 Backend-Matched Validation

### Debt Reminder Types
**Only these values are accepted:**
- `7_days` - 7 days before due date
- `3_days` - 3 days before due date  
- `overdue` - After due date

### Platform Values
**Only these platforms are accepted:**
- `android`
- `ios`

**Note:** `web` platform is **NOT** supported by the backend.

## 🎨 Usage Examples

### Sending Shop Notifications
```jsx
const handleSendToShops = async () => {
  try {
    await sendToShops({
      shopIds: ['shop1', 'shop2'],
      title: 'Important Update',
      message: 'Please check your dashboard for new information.',
      priority: 'high'
    });
  } catch (error) {
    console.error('Failed to send notification:', error);
  }
};
```

### Sending Debt Reminders
```jsx
const handleDebtReminders = async () => {
  try {
    await sendDebtReminders({
      reminderType: '3_days', // Backend values: 7_days, 3_days, overdue
      shopIds: ['shop1', 'shop2'], // Optional
      priority: 'normal'
    });
  } catch (error) {
    console.error('Failed to send debt reminders:', error);
  }
};
```

### FCM Token Registration
```jsx
const handleTokenRegistration = async () => {
  try {
    await registerToken({
      token: 'firebase-token-string',
      deviceInfo: {
        deviceId: 'unique-device-id',
        platform: 'android', // Only 'android' or 'ios'
        appVersion: '1.0.0',
        osVersion: '11.0'
      }
    });
  } catch (error) {
    console.error('Failed to register token:', error);
  }
};
```

## 🔄 Error Handling

All hooks follow consistent error handling patterns:

```jsx
const { sendToShops, error, clearError } = useNotifications({
  onError: (operation, error) => {
    console.error(`${operation} failed:`, error);
    // Custom error handling
  }
});

// Clear errors manually
if (error) {
  clearError();
}
```

## 🚀 Loading States

Each hook provides granular loading states:

```jsx
const {
  isSendingToShops,
  isSendingBroadcast,
  isSendingDebtReminders,
  isLoading // Combined loading state
} = useNotifications();

// Use in UI
{isLoading && <LoadingSpinner />}
{isSendingToShops && <span>Sending to shops...</span>}
```

## 📦 Integration with Services

All hooks use the backend-matched service layer:
- `@/lib/services/notification` - Main notification service
- `@/lib/api/bridge` - API bridge with caching
- `@/lib/api/contract` - Backend-matched contracts

## 🎯 Best Practices

1. **Always use validation** before sending data
2. **Handle errors gracefully** with user feedback
3. **Use loading states** for better UX
4. **Follow backend constraints** (platforms, reminder types)
5. **Cache data appropriately** using provided methods
6. **Clear errors** when appropriate

## 📈 Performance Considerations

- Statistics have built-in caching (5 minutes)
- Targets have built-in caching (10 minutes)
- Auto-refresh can be enabled for real-time data
- Operations prevent concurrent execution
- Error boundaries prevent UI crashes

---

**Note:** This implementation is 100% matched to the backend API and follows all existing frontend patterns. No endpoints that don't exist in the backend are included. 