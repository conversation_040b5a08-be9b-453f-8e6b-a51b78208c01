import React from 'react';
import { Building2, Plus, Download } from 'lucide-react';
import { Button } from '@/components/ui/button';

export const ShopsHeader = ({ onRegisterClick, onCreateClick, onExportClick }) => {
  return (
    <div className="flex items-center justify-between mb-6">
      <div className="flex items-center gap-2">
        <Building2 className="h-6 w-6" />
        <div>
          <h1 className="text-2xl font-bold tracking-tight">Shop Management</h1>
          <p className="text-muted-foreground">
            Register and manage shops in the DeynCare system
          </p>
        </div>
      </div>
      
      <div className="flex items-center gap-2">
        <Button variant="outline" onClick={onExportClick}>
          <Download className="mr-2 h-4 w-4" />
          Export Shops
        </Button>
        <Button onClick={onRegisterClick} variant="default">
          <Plus className="mr-2 h-4 w-4" />
          Register Shop
        </Button>
      </div>
    </div>
  );
};

export default ShopsHeader;
