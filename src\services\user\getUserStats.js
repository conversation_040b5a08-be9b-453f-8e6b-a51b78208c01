/**
 * Get User Statistics Service
 * Provides user stats data for SuperAdmin dashboard
 */
const { User } = require('../../models');
const { logInfo, logError, AppError } = require('../../utils');

/**
 * Get user statistics data
 * @param {Object} options - Stats options
 * @param {Date} options.startDate - Start date for stats (optional)
 * @param {Date} options.endDate - End date for stats (optional)
 * @param {String} options.groupBy - Group by (day, week, month) (optional)
 * @returns {Object} - User stats data
 */
const getUserStats = async (options = {}) => {
  try {
    const { startDate, endDate, groupBy = 'day' } = options;
    
    // Base query conditions
    const baseQuery = { isDeleted: { $ne: true } };
    
    // Add date filter if provided
    if (startDate && endDate) {
      baseQuery.createdAt = { $gte: startDate, $lte: endDate };
    }
    
    // Get total users count (excluding deleted)
    const totalUsers = await User.countDocuments(baseQuery);
    
    // Get active users count
    const activeUsers = await User.countDocuments({
      ...baseQuery,
      status: 'active',
      isSuspended: { $ne: true }
    });
    
    // Get inactive users count - FIXED: Include 'email_verified_pending_payment' status as inactive
    const inactiveUsers = await User.countDocuments({
      ...baseQuery,
      $or: [
        { status: 'inactive' },
        { status: 'email_verified_pending_payment' }
      ]
    });
    
    // Get suspended users count
    const suspendedUsers = await User.countDocuments({
      ...baseQuery,
      $or: [
        { status: 'suspended' },
        { isSuspended: true }
      ]
    });
    
    // Get users by role distribution
    const roleDistribution = await User.aggregate([
      { $match: baseQuery },
      {
        $group: {
          _id: '$role',
          count: { $sum: 1 }
        }
      },
      {
        $project: {
          role: '$_id',
          count: 1,
          percentage: {
            $multiply: [
              { $divide: ['$count', totalUsers] },
              100
            ]
          }
        }
      }
    ]);
    
    // Get users by status distribution
    const statusDistribution = await User.aggregate([
      { $match: baseQuery },
      {
        $group: {
          _id: '$status',
          count: { $sum: 1 }
        }
      },
      {
        $project: {
          status: '$_id',
          count: 1,
          percentage: {
            $multiply: [
              { $divide: ['$count', totalUsers] },
              100
            ]
          }
        }
      }
    ]);
    
    // Get recent user registrations (last 30 days)
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
    
    const recentRegistrations = await User.countDocuments({
      ...baseQuery,
      createdAt: { $gte: thirtyDaysAgo }
    });
    
    // Get user activity stats (users who logged in recently)
    const sevenDaysAgo = new Date();
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
    
    const activeInLastWeek = await User.countDocuments({
      ...baseQuery,
      lastLoginAt: { $gte: sevenDaysAgo }
    });
    
    // Get users with verified emails
    const verifiedUsers = await User.countDocuments({
      ...baseQuery,
      emailVerified: true
    });
    
    // Get users who completed payment
    const paidUsers = await User.countDocuments({
      ...baseQuery,
      isPaid: true
    });
    
    // Get trend data if date range is provided
    let trends = [];
    if (startDate && endDate) {
      let dateFormat;
      let groupField;
      
      switch(groupBy) {
        case 'week':
          dateFormat = '%Y-%U'; // Year-Week
          groupField = { year: '$year', week: '$week' };
          break;
        case 'month':
          dateFormat = '%Y-%m'; // Year-Month
          groupField = { year: '$year', month: '$month' };
          break;
        default: // day
          dateFormat = '%Y-%m-%d'; // Year-Month-Day
          groupField = { year: '$year', month: '$month', day: '$day' };
      }
      
      trends = await User.aggregate([
        {
          $match: {
            ...baseQuery,
            createdAt: { $gte: startDate, $lte: endDate }
          }
        },
        {
          $project: {
            date: { $dateToString: { format: dateFormat, date: '$createdAt' } },
            status: '$status',
            role: '$role',
            year: { $year: '$createdAt' },
            month: { $month: '$createdAt' },
            week: { $week: '$createdAt' },
            day: { $dayOfMonth: '$createdAt' }
          }
        },
        {
          $group: {
            _id: {
              date: '$date',
              status: '$status',
              ...groupField
            },
            count: { $sum: 1 }
          }
        },
        {
          $sort: { '_id.year': 1, '_id.month': 1, '_id.week': 1, '_id.day': 1 }
        }
      ]);
    }
    
    // Format trend data
    const trendsByDate = {};
    trends.forEach(item => {
      if (!trendsByDate[item._id.date]) {
        trendsByDate[item._id.date] = {
          date: item._id.date,
          total: 0,
          byStatus: {}
        };
      }
      
      trendsByDate[item._id.date].byStatus[item._id.status] = item.count;
      trendsByDate[item._id.date].total += item.count;
    });
    
    const result = {
      summary: {
        totalUsers,
        activeUsers,
        inactiveUsers,
        suspendedUsers
      },
      activity: {
        recentRegistrations,
        activeInLastWeek,
        verifiedUsers,
        paidUsers
      },
      distribution: {
        byRole: roleDistribution,
        byStatus: statusDistribution
      },
      trends: Object.values(trendsByDate),
      metadata: {
        calculatedAt: new Date(),
        dateRange: startDate && endDate ? { startDate, endDate } : null,
        groupBy
      }
    };
    
    logInfo(`User statistics calculated successfully. Total users: ${totalUsers}`, 'getUserStats');
    
    return result;
    
  } catch (error) {
    logError('Error calculating user statistics', 'getUserStats', error);
    throw new AppError('Failed to calculate user statistics', 500, 'user_stats_error');
  }
};

module.exports = getUserStats; 