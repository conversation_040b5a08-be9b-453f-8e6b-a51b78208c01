"use client";

import * as z from 'zod';

// Simplified form validation schema for essential shop information only
export const shopFormSchema = z.object({
  // Core shop information - essential fields only
  shopName: z.string().min(2, { message: "Shop name must be at least 2 characters" })
    .max(100, { message: "Shop name cannot exceed 100 characters" }),
  fullName: z.string().min(3, { message: "Full name must be at least 3 characters" })
    .max(100, { message: "Full name cannot exceed 100 characters" }),
  email: z.string()
    .min(1, "Email address is required")
    .email({ message: "Please enter a valid email address" }),
  phone: z.string().min(8, { message: "Phone number must be at least 8 characters" }),
  shopAddress: z.string().min(5, { message: "Shop address must be at least 5 characters" })
    .max(200, { message: "Shop address cannot exceed 200 characters" }),
  
  // Shop status
  status: z.enum(["active", "pending", "suspended"], {
    required_error: "Please select a status",
  })
});

// Simplified default form values
export const defaultFormValues = {
  shopName: "",
  fullName: "",
  email: "",
  phone: "",
  shopAddress: "",
  status: "active"
};

// Transform form data to match SuperAdmin API expectations
export const transformFormData = (data) => {
  return {
    // Required fields with SuperAdmin API naming (exact match with backend)
    fullName: data.fullName,
    email: data.email,
    phone: data.phone,
    shopName: data.shopName,
    shopAddress: data.shopAddress,
    status: data.status || 'active'
  };
};
