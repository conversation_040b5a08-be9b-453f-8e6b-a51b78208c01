/**
 * Get Discount Module
 * Handles retrieving discount codes by ID or code
 */
const { DiscountCode } = require('../../models');
const { AppError, logError } = require('../../utils');

/**
 * Get discount code by ID
 * @param {String} discountId - Discount code ID
 * @param {Boolean} includeInactive - Whether to include inactive codes
 * @returns {Promise<Object>} Discount code
 */
const getDiscountById = async (discountId, includeInactive = false) => {
  try {
    const query = { 
      discountId,
      isDeleted: false
    };
    
    if (!includeInactive) {
      query.isActive = true;
    }
    
    const discountCode = await DiscountCode.findOne(query);
    
    if (!discountCode) {
      throw new AppError('Discount code not found', 404, 'discount_not_found');
    }
    
    return discountCode;
  } catch (error) {
    if (error instanceof AppError) {
      throw error;
    }
    
    logError(`Failed to get discount code: ${error.message}`, 'DiscountService', error);
    throw new AppError('Failed to retrieve discount code', 500, 'discount_retrieval_error');
  }
};

/**
 * Get discount code by code string
 * @param {String} code - Discount code string
 * @param {Boolean} includeInactive - Whether to include inactive codes
 * @returns {Promise<Object>} Discount code
 */
const getDiscountByCode = async (code, includeInactive = false) => {
  try {
    const query = { 
      code: code.toUpperCase(),
      isDeleted: false
    };
    
    if (!includeInactive) {
      query.isActive = true;
    }
    
    const discountCode = await DiscountCode.findOne(query);
    
    if (!discountCode) {
      throw new AppError('Discount code not found', 404, 'discount_not_found');
    }
    
    return discountCode;
  } catch (error) {
    if (error instanceof AppError) {
      throw error;
    }
    
    logError(`Failed to get discount code: ${error.message}`, 'DiscountService', error);
    throw new AppError('Failed to retrieve discount code', 500, 'discount_retrieval_error');
  }
};

module.exports = {
  getDiscountById,
  getDiscountByCode
};
