/**
 * User Service - Aggregates all user-related service methods
 */

import getUsers from './getUsers';
import getUserById from './getUserById';
import createUser from './createUser';
import updateUser from './updateUser';
import deleteUser from './deleteUser';
import changeUserStatus from './changeUserStatus';
import getUserStats from './getUserStats';
import apiBridge from '@/lib/api/bridge';
import { ENDPOINTS } from '@/lib/api/contract';
import { toast } from 'sonner';
import BaseService from '@/lib/services/baseService';

/**
 * UserService exports all user-related API operations
 */
const UserService = {
  getUsers,
  getUserById,
  createUser,
  updateUser,
  deleteUser,
  changeUserStatus,
  getUserStats,
  
  /**
   * Update user profile (own profile)
   * @param {Object} profileData - Updated profile data (name, phone, etc.)
   * @returns {Promise<Object>} Updated user profile
   */
  async updateProfile(profileData) {
    try {
      const response = await apiBridge.put(ENDPOINTS.USERS.PROFILE, profileData, {
        clearCacheEndpoint: ENDPOINTS.AUTH.ME
      });
      
      if (response.data && response.data.success) {
        toast.success('Profile updated successfully');
        return response.data.data.user;
      }
      
      console.error('Unexpected API response format:', response.data);
      toast.error('Failed to update profile');
      return null;
    } catch (error) {
      console.error('Error updating profile:', error);
      BaseService.handleError(error, 'UserService.updateProfile', true);
      throw error;
    }
  },
  
  /**
   * Change user password
   * @param {string} currentPassword - Current password
   * @param {string} newPassword - New password
   * @returns {Promise<boolean>} Success status
   */
  async changePassword(currentPassword, newPassword) {
    try {
      const response = await apiBridge.post(ENDPOINTS.AUTH.CHANGE_PASSWORD, {
        currentPassword,
        newPassword
      });
      
      if (response.data && response.data.success) {
        toast.success('Password changed successfully');
        return true;
      }
      
      console.error('Unexpected API response format:', response.data);
      toast.error('Failed to change password');
      return false;
    } catch (error) {
      console.error('Error changing password:', error);
      BaseService.handleError(error, 'UserService.changePassword', true);
      return false;
    }
  },
  
  /**
   * Verify a user account
   * @param {string} userId - ID of the user to verify
   * @returns {Promise<Object>} Updated user verification status
   */
  async verifyUser(userId) {
    try {
      const response = await apiBridge.patch(`${ENDPOINTS.USERS.BASE}/${userId}/verify`, {
        verified: true
      }, {
        clearCacheEndpoint: ENDPOINTS.USERS.BASE
      });
      
      if (response.data && response.data.success) {
        toast.success('User verified successfully');
        return response.data.data.user;
      }
      
      console.error('Unexpected API response format:', response.data);
      toast.error('Failed to verify user');
      return null;
    } catch (error) {
      console.error(`Error verifying user ${userId}:`, error);
      BaseService.handleError(error, 'UserService.verifyUser', true);
      throw error;
    }
  },
  
  /**
   * Request email verification
   * @param {string} userId - ID of the user
   * @returns {Promise<boolean>} Success status
   */
  async requestEmailVerification(userId) {
    try {
      const response = await apiBridge.post(`${ENDPOINTS.USERS.BASE}/${userId}/verify-email`);
      
      if (response.data && response.data.success) {
        toast.success('Verification email sent successfully');
        return true;
      }
      
      console.error('Unexpected API response format:', response.data);
      toast.error('Failed to send verification email');
      return false;
    } catch (error) {
      console.error(`Error requesting email verification for user ${userId}:`, error);
      BaseService.handleError(error, 'UserService.requestEmailVerification', true);
      return false;
    }
  }
};

export default UserService;
