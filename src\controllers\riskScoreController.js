const riskScoreService = require('../services/riskScoreService');
const { AppError, logInfo, logError } = require('../utils');

/**
 * Risk Score Controller
 * Handles risk score listing for Admin and SuperAdmin
 */

/**
 * Get risk scores for shop (Admin use)
 * @route GET /api/risk/shop
 * @access Private (Admin)
 */
const getShopRiskScores = async (req, res, next) => {
  try {
    const shopId = req.user.shopId;
    const { riskLevel, sortBy, sortOrder, page, limit } = req.query;
    
    if (!shopId) {
      return res.status(403).json({
        success: false,
        message: 'Shop ID required for this operation',
        statusCode: 403
      });
    }
    
    const options = {
      riskLevel,
      sortBy,
      sortOrder,
      page: page ? Number(page) : 1,
      limit: limit ? Number(limit) : 50
    };
    
    const result = await riskScoreService.getShopRiskScores(shopId, options);
    
    logInfo(`Admin retrieved risk scores for shop ${shopId}`, 'RiskScoreController');
    
    return res.status(200).json({
      success: true,
      message: 'Shop risk scores retrieved successfully',
      ...result,
      statusCode: 200
    });
    
  } catch (error) {
    logError('Error getting shop risk scores', 'RiskScoreController', error);
    return next(error instanceof AppError ? error : new AppError('Failed to get shop risk scores', 500));
  }
};

/**
 * Get all risk scores (SuperAdmin use)
 * @route GET /api/risk/all
 * @access Private (SuperAdmin)
 */
const getAllRiskScores = async (req, res, next) => {
  try {
    const { shopId, riskLevel, sortBy, sortOrder, page, limit } = req.query;
    
    const options = {
      shopId,
      riskLevel,
      sortBy,
      sortOrder,
      page: page ? Number(page) : 1,
      limit: limit ? Number(limit) : 100
    };
    
    const result = await riskScoreService.getAllRiskScores(options);
    
    logInfo('SuperAdmin retrieved all risk scores', 'RiskScoreController');
    
    return res.status(200).json({
      success: true,
      message: 'All risk scores retrieved successfully',
      ...result,
      statusCode: 200
    });
    
  } catch (error) {
    logError('Error getting all risk scores', 'RiskScoreController', error);
    return next(error instanceof AppError ? error : new AppError('Failed to get all risk scores', 500));
  }
};

/**
 * Update customer risk score (for ML integration)
 * @route PUT /api/risk/update
 * @access Private (System/SuperAdmin)
 */
const updateRiskScore = async (req, res, next) => {
  try {
    const { customerId, riskScore, riskLevel, source } = req.body;
    
    if (!customerId || riskScore === undefined || !riskLevel) {
      return res.status(400).json({
        success: false,
        message: 'Missing required fields: customerId, riskScore, riskLevel',
        statusCode: 400
      });
    }
    
    const result = await riskScoreService.updateCustomerRiskScore(customerId, {
      riskScore: Number(riskScore),
      riskLevel,
      source: source || 'manual'
    });
    
    return res.status(200).json({
      success: true,
      message: 'Risk score updated successfully',
      data: result,
      statusCode: 200
    });
    
  } catch (error) {
    logError('Error updating risk score', 'RiskScoreController', error);
    return next(error instanceof AppError ? error : new AppError('Failed to update risk score', 500));
  }
};

module.exports = {
  getShopRiskScores,
  getAllRiskScores,
  updateRiskScore
}; 