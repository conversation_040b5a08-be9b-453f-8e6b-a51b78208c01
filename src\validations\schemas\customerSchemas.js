const Joi = require('joi');

const customerValidation = {
  // Create Customer Validation
  createCustomer: {
    body: Joi.object({
      CustomerName: Joi.string()
        .trim()
        .min(2)
        .max(100)
        .required()
        .messages({
          'string.empty': 'Customer name is required',
          'string.min': 'Customer name must be at least 2 characters',
          'string.max': 'Customer name cannot exceed 100 characters'
        }),
      
      CustomerType: Joi.string()
        .valid('new', 'returning')
        .required()
        .messages({
          'any.only': 'Customer type must be either "new" or "returning"',
          'any.required': 'Customer type is required'
        }),
      
      phone: Joi.string()
        .pattern(/^[+]?[0-9\s\-\(\)]{10,15}$/)
        .required()
        .messages({
          'string.pattern.base': 'Please provide a valid phone number',
          'any.required': 'Phone number is required'
        }),
      
      email: Joi.string()
        .email()
        .optional()
        .allow('')
        .messages({
          'string.email': 'Please provide a valid email address'
        }),
      
      address: Joi.string()
        .trim()
        .max(500)
        .optional()
        .allow('')
        .messages({
          'string.max': 'Address cannot exceed 500 characters'
        }),
      
      // Optional fields
      creditLimit: Joi.number()
        .min(0)
        .precision(2)
        .default(0)
        .optional()
        .messages({
          'number.min': 'Credit limit cannot be negative'
        }),
      
      category: Joi.string()
        .trim()
        .min(2)
        .max(50)
        .pattern(/^[a-zA-Z0-9\s_-]+$/)
        .default('regular')
        .optional()
        .messages({
          'string.min': 'Category must be at least 2 characters',
          'string.max': 'Category cannot exceed 50 characters',
          'string.pattern.base': 'Category can only contain letters, numbers, spaces, underscores, and hyphens'
        }),
      
      notes: Joi.string()
        .trim()
        .max(1000)
        .optional()
        .allow('')
        .messages({
          'string.max': 'Notes cannot exceed 1000 characters'
        })
    })
  },

  // Get Customers Validation
  getCustomers: {
    query: Joi.object({
      page: Joi.number()
        .integer()
        .min(1)
        .default(1)
        .optional(),
      
      limit: Joi.number()
        .integer()
        .min(1)
        .max(100)
        .default(20)
        .optional(),
      
      CustomerType: Joi.string()
        .valid('New', 'Returning')
        .optional(),
      
      riskLevel: Joi.string()
        .valid('Low Risk', 'Medium Risk', 'High Risk', 'No Assessment')
        .optional(),
      
      category: Joi.string()
        .trim()
        .min(2)
        .max(50)
        .pattern(/^[a-zA-Z0-9\s_-]+$/)
        .optional()
        .messages({
          'string.min': 'Category must be at least 2 characters',
          'string.max': 'Category cannot exceed 50 characters',
          'string.pattern.base': 'Category can only contain letters, numbers, spaces, underscores, and hyphens'
        }),
      
      sortBy: Joi.string()
        .valid('CustomerName', 'createdAt', 'riskScore', 'totalPurchaseAmount', 'outstandingBalance')
        .default('createdAt')
        .optional(),
      
      sortOrder: Joi.string()
        .valid('asc', 'desc')
        .default('desc')
        .optional(),
      
      search: Joi.string()
        .trim()
        .max(100)
        .optional()
        .messages({
          'string.max': 'Search term cannot exceed 100 characters'
        }),
      
      hasDebt: Joi.boolean()
        .optional(),
      
      hasOutstandingBalance: Joi.boolean()
        .optional()
    })
  },

  // Get Customer By ID Validation
  getCustomerById: {
    params: Joi.object({
      customerId: Joi.string()
        .pattern(/^CUST\d{3}$/)
        .required()
        .messages({
          'string.pattern.base': 'Invalid customer ID format',
          'any.required': 'Customer ID is required'
        })
    })
  },

  // Update Customer Validation
  updateCustomer: {
    params: Joi.object({
      customerId: Joi.string()
        .pattern(/^CUST\d{3}$/)
        .required()
        .messages({
          'string.pattern.base': 'Invalid customer ID format',
          'any.required': 'Customer ID is required'
        })
    }),
    
    body: Joi.object({
      CustomerName: Joi.string()
        .trim()
        .min(2)
        .max(100)
        .optional()
        .messages({
          'string.min': 'Customer name must be at least 2 characters',
          'string.max': 'Customer name cannot exceed 100 characters'
        }),
      
      phone: Joi.string()
        .pattern(/^[+]?[0-9\s\-\(\)]{10,15}$/)
        .optional()
        .messages({
          'string.pattern.base': 'Please provide a valid phone number'
        }),
      
      email: Joi.string()
        .email()
        .optional()
        .allow('')
        .messages({
          'string.email': 'Please provide a valid email address'
        }),
      
      address: Joi.string()
        .trim()
        .max(500)
        .optional()
        .allow('')
        .messages({
          'string.max': 'Address cannot exceed 500 characters'
        }),
      
      creditLimit: Joi.number()
        .min(0)
        .precision(2)
        .optional()
        .messages({
          'number.min': 'Credit limit cannot be negative'
        }),
      
      category: Joi.string()
        .trim()
        .min(2)
        .max(50)
        .pattern(/^[a-zA-Z0-9\s_-]+$/)
        .optional()
        .messages({
          'string.min': 'Category must be at least 2 characters',
          'string.max': 'Category cannot exceed 50 characters',
          'string.pattern.base': 'Category can only contain letters, numbers, spaces, underscores, and hyphens'
        }),
      
      notes: Joi.string()
        .trim()
        .max(1000)
        .optional()
        .allow('')
        .messages({
          'string.max': 'Notes cannot exceed 1000 characters'
        })
    }).min(1).messages({
      'object.min': 'At least one field must be provided for update'
    })
  },

  // Delete Customer Validation
  deleteCustomer: {
    params: Joi.object({
      customerId: Joi.string()
        .pattern(/^CUST\d{3}$/)
        .required()
        .messages({
          'string.pattern.base': 'Invalid customer ID format',
          'any.required': 'Customer ID is required'
        })
    })
  },

  // Get Customer Debts Validation
  getCustomerDebts: {
    params: Joi.object({
      customerId: Joi.string()
        .pattern(/^CUST\d{3}$/)
        .required()
        .messages({
          'string.pattern.base': 'Invalid customer ID format',
          'any.required': 'Customer ID is required'
        })
    }),
    
    query: Joi.object({
      page: Joi.number()
        .integer()
        .min(1)
        .default(1)
        .optional(),
      
      limit: Joi.number()
        .integer()
        .min(1)
        .max(100)
        .default(20)
        .optional(),
      
      status: Joi.string()
        .valid('active', 'paid', 'overdue', 'partially_paid')
        .optional(),
      
      sortBy: Joi.string()
        .valid('dueDate', 'debtAmount', 'createdAt')
        .default('createdAt')
        .optional(),
      
      sortOrder: Joi.string()
        .valid('asc', 'desc')
        .default('desc')
        .optional()
    })
  }
};

module.exports = { customerValidation }; 