/**
 * Get Expiring Subscriptions Service
 * Finds subscriptions that are expiring within a specified number of days
 */
const { Subscription } = require('../../models');
const { logError } = require('../../utils');

/**
 * Get subscriptions expiring within specified days
 * @param {Number} daysAhead - Number of days to look ahead (default: 5)
 * @returns {Promise<Array>} Array of expiring subscriptions
 */
const getExpiringSubscriptions = async (daysAhead = 5) => {
  try {
    const now = new Date();
    const futureDate = new Date();
    futureDate.setDate(now.getDate() + daysAhead);
    
    // Find subscriptions where:
    // 1. Status is 'active'
    // 2. End date is between now and future date
    // 3. Not deleted
    // 4. <PERSON>minder not already sent for this period
    const expiringSubscriptions = await Subscription.find({
      status: 'active',
      'dates.endDate': {
        $gte: now,
        $lte: futureDate
      },
      isDeleted: false,
      $or: [
        { 'notifications.expiryReminderSent': { $exists: false } },
        { 'notifications.expiryReminderSent': false },
        // Also include if reminder was sent more than 24 hours ago
        { 'notifications.expiryReminderSentAt': { $lt: new Date(Date.now() - 24 * 60 * 60 * 1000) } }
      ]
    }).lean();

    return expiringSubscriptions;
  } catch (error) {
    logError('Failed to get expiring subscriptions', 'SubscriptionService', error);
    throw error;
  }
};

module.exports = getExpiringSubscriptions; 