/**
 * SuperAdmin Payment Statistics Service
 * Handles payment analytics and reporting for SuperAdmin
 */
const { Payment, Shop, Subscription } = require('../../models');
const { AppError, logInfo, logError } = require('../../utils');

/**
 * Get comprehensive payment statistics
 * @param {Object} options - Statistics options
 * @returns {Promise<Object>} Payment statistics
 */
const getPaymentStatistics = async (options = {}) => {
  try {
    const { startDate, endDate } = options;

    const query = {
      paymentContext: 'subscription',
      isDeleted: false
    };

    if (startDate && endDate) {
      query.createdAt = {
        $gte: new Date(startDate),
        $lte: new Date(endDate)
      };
    }

    // Get basic statistics
    const [
      totalPayments,
      successfulPayments,
      pendingPayments,
      failedPayments,
      approvedPayments,
      rejectedPayments
    ] = await Promise.all([
      Payment.countDocuments(query),
      Payment.countDocuments({ ...query, status: { $in: ['success', 'approved'] } }),
      Payment.countDocuments({ ...query, status: 'pending' }),
      Payment.countDocuments({ ...query, status: 'failed' }),
      Payment.countDocuments({ ...query, status: 'approved' }),
      Payment.countDocuments({ ...query, status: 'rejected' })
    ]);

    // Calculate success rate
    const successRate = totalPayments > 0 ? (successfulPayments / totalPayments) * 100 : 0;

    // Get revenue statistics
    const revenueStats = await Payment.aggregate([
      { $match: { ...query, status: { $in: ['success', 'approved'] } } },
      {
        $group: {
          _id: null,
          totalRevenue: { $sum: '$amount' },
          averageAmount: { $avg: '$amount' },
          minAmount: { $min: '$amount' },
          maxAmount: { $max: '$amount' }
        }
      }
    ]);

    const revenue = revenueStats.length > 0 ? revenueStats[0] : {
      totalRevenue: 0,
      averageAmount: 0,
      minAmount: 0,
      maxAmount: 0
    };

    // Get payment method distribution
    const methodDistribution = await Payment.aggregate([
      { $match: query },
      {
        $group: {
          _id: '$method',
          count: { $sum: 1 },
          totalAmount: { $sum: '$amount' },
          successCount: {
            $sum: {
              $cond: [
                { $in: ['$status', ['success', 'approved']] },
                1,
                0
              ]
            }
          }
        }
      },
      {
        $project: {
          method: '$_id',
          count: 1,
          totalAmount: 1,
          successCount: 1,
          successRate: {
            $cond: [
              { $eq: ['$count', 0] },
              0,
              { $multiply: [{ $divide: ['$successCount', '$count'] }, 100] }
            ]
          }
        }
      },
      { $sort: { count: -1 } }
    ]);

    // Get status distribution
    const statusDistribution = await Payment.aggregate([
      { $match: query },
      {
        $group: {
          _id: '$status',
          count: { $sum: 1 },
          totalAmount: { $sum: '$amount' }
        }
      },
      {
        $project: {
          status: '$_id',
          count: 1,
          totalAmount: 1,
          percentage: {
            $multiply: [
              { $divide: ['$count', totalPayments] },
              100
            ]
          }
        }
      },
      { $sort: { count: -1 } }
    ]);

    // Get daily payment trends (last 30 days)
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    const dailyTrends = await Payment.aggregate([
      {
        $match: {
          ...query,
          createdAt: { $gte: thirtyDaysAgo }
        }
      },
      {
        $group: {
          _id: {
            $dateToString: {
              format: '%Y-%m-%d',
              date: '$createdAt'
            }
          },
          count: { $sum: 1 },
          totalAmount: { $sum: '$amount' },
          successCount: {
            $sum: {
              $cond: [
                { $in: ['$status', ['success', 'approved']] },
                1,
                0
              ]
            }
          }
        }
      },
      {
        $project: {
          date: '$_id',
          count: 1,
          totalAmount: 1,
          successCount: 1,
          successRate: {
            $cond: [
              { $eq: ['$count', 0] },
              0,
              { $multiply: [{ $divide: ['$successCount', '$count'] }, 100] }
            ]
          }
        }
      },
      { $sort: { date: 1 } }
    ]);

    // Get approval time statistics (for approved payments)
    const approvalTimeStats = await Payment.aggregate([
      {
        $match: {
          ...query,
          status: 'approved',
          approvedAt: { $exists: true },
          createdAt: { $exists: true }
        }
      },
      {
        $project: {
          approvalTimeHours: {
            $divide: [
              { $subtract: ['$approvedAt', '$createdAt'] },
              1000 * 60 * 60 // Convert to hours
            ]
          }
        }
      },
      {
        $group: {
          _id: null,
          averageApprovalTime: { $avg: '$approvalTimeHours' },
          minApprovalTime: { $min: '$approvalTimeHours' },
          maxApprovalTime: { $max: '$approvalTimeHours' }
        }
      }
    ]);

    const approvalTime = approvalTimeStats.length > 0 ? approvalTimeStats[0] : {
      averageApprovalTime: 0,
      minApprovalTime: 0,
      maxApprovalTime: 0
    };

    // Get top performing shops
    const topShops = await Payment.aggregate([
      { $match: { ...query, status: { $in: ['success', 'approved'] } } },
      {
        $group: {
          _id: '$shopId',
          shopName: { $first: '$shopName' },
          totalPayments: { $sum: 1 },
          totalRevenue: { $sum: '$amount' },
          averageAmount: { $avg: '$amount' }
        }
      },
      { $sort: { totalRevenue: -1 } },
      { $limit: 10 }
    ]);

    // Get recent activity (last 10 payments)
    const recentActivity = await Payment.find(query)
      .sort({ createdAt: -1 })
      .limit(10)
      .select('paymentId customerName shopName amount method status createdAt')
      .lean();

    logInfo(`Generated comprehensive payment statistics for ${totalPayments} payments`, 'PaymentStatisticsService');

    return {
      summary: {
        totalPayments,
        successfulPayments,
        pendingPayments,
        failedPayments,
        approvedPayments,
        rejectedPayments,
        successRate: Math.round(successRate * 100) / 100
      },
      revenue: {
        totalRevenue: Math.round(revenue.totalRevenue * 100) / 100,
        averageAmount: Math.round(revenue.averageAmount * 100) / 100,
        minAmount: Math.round(revenue.minAmount * 100) / 100,
        maxAmount: Math.round(revenue.maxAmount * 100) / 100
      },
      methodDistribution,
      statusDistribution,
      dailyTrends,
      approvalTime: {
        averageHours: Math.round(approvalTime.averageApprovalTime * 100) / 100,
        minHours: Math.round(approvalTime.minApprovalTime * 100) / 100,
        maxHours: Math.round(approvalTime.maxApprovalTime * 100) / 100
      },
      topShops,
      recentActivity
    };
  } catch (error) {
    logError(`Error getting payment statistics: ${error.message}`, 'PaymentStatisticsService', error);
    throw new AppError('Failed to get payment statistics', 500, 'statistics_error');
  }
};

/**
 * Get payment success rate trends
 * @param {Object} options - Options for trend analysis
 * @returns {Promise<Object>} Success rate trends
 */
const getSuccessRateTrends = async (options = {}) => {
  try {
    const { days = 30 } = options;

    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    const query = {
      paymentContext: 'subscription',
      isDeleted: false,
      createdAt: { $gte: startDate }
    };

    const trends = await Payment.aggregate([
      { $match: query },
      {
        $group: {
          _id: {
            $dateToString: {
              format: '%Y-%m-%d',
              date: '$createdAt'
            }
          },
          totalPayments: { $sum: 1 },
          successfulPayments: {
            $sum: {
              $cond: [
                { $in: ['$status', ['success', 'approved']] },
                1,
                0
              ]
            }
          }
        }
      },
      {
        $project: {
          date: '$_id',
          totalPayments: 1,
          successfulPayments: 1,
          successRate: {
            $cond: [
              { $eq: ['$totalPayments', 0] },
              0,
              { $multiply: [{ $divide: ['$successfulPayments', '$totalPayments'] }, 100] }
            ]
          }
        }
      },
      { $sort: { date: 1 } }
    ]);

    return trends;
  } catch (error) {
    logError(`Error getting success rate trends: ${error.message}`, 'PaymentStatisticsService', error);
    throw new AppError('Failed to get success rate trends', 500, 'trends_error');
  }
};

/**
 * Get payment method performance analysis
 * @param {Object} options - Analysis options
 * @returns {Promise<Object>} Method performance analysis
 */
const getMethodPerformanceAnalysis = async (options = {}) => {
  try {
    const { startDate, endDate } = options;

    const query = {
      paymentContext: 'subscription',
      isDeleted: false
    };

    if (startDate && endDate) {
      query.createdAt = {
        $gte: new Date(startDate),
        $lte: new Date(endDate)
      };
    }

    const analysis = await Payment.aggregate([
      { $match: query },
      {
        $group: {
          _id: '$method',
          totalPayments: { $sum: 1 },
          successfulPayments: {
            $sum: {
              $cond: [
                { $in: ['$status', ['success', 'approved']] },
                1,
                0
              ]
            }
          },
          totalRevenue: { $sum: '$amount' },
          averageAmount: { $avg: '$amount' },
          failedPayments: {
            $sum: {
              $cond: [
                { $eq: ['$status', 'failed'] },
                1,
                0
              ]
            }
          },
          pendingPayments: {
            $sum: {
              $cond: [
                { $eq: ['$status', 'pending'] },
                1,
                0
              ]
            }
          }
        }
      },
      {
        $project: {
          method: '$_id',
          totalPayments: 1,
          successfulPayments: 1,
          failedPayments: 1,
          pendingPayments: 1,
          totalRevenue: 1,
          averageAmount: 1,
          successRate: {
            $cond: [
              { $eq: ['$totalPayments', 0] },
              0,
              { $multiply: [{ $divide: ['$successfulPayments', '$totalPayments'] }, 100] }
            ]
          },
          failureRate: {
            $cond: [
              { $eq: ['$totalPayments', 0] },
              0,
              { $multiply: [{ $divide: ['$failedPayments', '$totalPayments'] }, 100] }
            ]
          }
        }
      },
      { $sort: { totalPayments: -1 } }
    ]);

    return analysis;
  } catch (error) {
    logError(`Error getting method performance analysis: ${error.message}`, 'PaymentStatisticsService', error);
    throw new AppError('Failed to get method performance analysis', 500, 'analysis_error');
  }
};

/**
 * Get real-time payment dashboard data
 * @returns {Promise<Object>} Real-time dashboard data
 */
const getRealTimeDashboardData = async () => {
  try {
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);

    // Today's statistics
    const todayStats = await Payment.aggregate([
      {
        $match: {
          paymentContext: 'subscription',
          isDeleted: false,
          createdAt: { $gte: today }
        }
      },
      {
        $group: {
          _id: null,
          totalPayments: { $sum: 1 },
          successfulPayments: {
            $sum: {
              $cond: [
                { $in: ['$status', ['success', 'approved']] },
                1,
                0
              ]
            }
          },
          totalRevenue: { $sum: '$amount' }
        }
      }
    ]);

    // Yesterday's statistics
    const yesterdayStats = await Payment.aggregate([
      {
        $match: {
          paymentContext: 'subscription',
          isDeleted: false,
          createdAt: { $gte: yesterday, $lt: today }
        }
      },
      {
        $group: {
          _id: null,
          totalPayments: { $sum: 1 },
          successfulPayments: {
            $sum: {
              $cond: [
                { $in: ['$status', ['success', 'approved']] },
                1,
                0
              ]
            }
          },
          totalRevenue: { $sum: '$amount' }
        }
      }
    ]);

    // Pending payments count
    const pendingCount = await Payment.countDocuments({
      paymentContext: 'subscription',
      isDeleted: false,
      status: 'pending'
    });

    // Recent payments (last 5)
    const recentPayments = await Payment.find({
      paymentContext: 'subscription',
      isDeleted: false
    })
      .sort({ createdAt: -1 })
      .limit(5)
      .select('paymentId customerName shopName amount method status createdAt')
      .lean();

    const today = todayStats.length > 0 ? todayStats[0] : {
      totalPayments: 0,
      successfulPayments: 0,
      totalRevenue: 0
    };

    const yesterday = yesterdayStats.length > 0 ? yesterdayStats[0] : {
      totalPayments: 0,
      successfulPayments: 0,
      totalRevenue: 0
    };

    return {
      today: {
        totalPayments: today.totalPayments,
        successfulPayments: today.successfulPayments,
        totalRevenue: Math.round(today.totalRevenue * 100) / 100,
        successRate: today.totalPayments > 0 ? Math.round((today.successfulPayments / today.totalPayments) * 100 * 100) / 100 : 0
      },
      yesterday: {
        totalPayments: yesterday.totalPayments,
        successfulPayments: yesterday.successfulPayments,
        totalRevenue: Math.round(yesterday.totalRevenue * 100) / 100,
        successRate: yesterday.totalPayments > 0 ? Math.round((yesterday.successfulPayments / yesterday.totalPayments) * 100 * 100) / 100 : 0
      },
      pendingPayments: pendingCount,
      recentPayments
    };
  } catch (error) {
    logError(`Error getting real-time dashboard data: ${error.message}`, 'PaymentStatisticsService', error);
    throw new AppError('Failed to get real-time dashboard data', 500, 'dashboard_error');
  }
};

module.exports = {
  getPaymentStatistics,
  getSuccessRateTrends,
  getMethodPerformanceAnalysis,
  getRealTimeDashboardData
}; 