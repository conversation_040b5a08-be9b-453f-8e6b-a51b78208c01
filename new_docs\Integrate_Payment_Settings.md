# Documentation: Integrating Payment Method Settings

This document outlines how to use the public API endpoint to fetch available payment methods from the DeynCare backend. This allows the mobile app to dynamically show or hide payment options based on the settings configured by the SuperAdmin.

---

## 1. Core Logic: Dynamic Payment Options

The goal is to control which payment methods are available in the mobile app from a central admin panel. The flow is as follows:

1.  **Fetch Payment Settings**: Before displaying any payment options, the mobile app calls a public API endpoint to get the list of currently enabled payment methods.
2.  **Use Context**: The app can specify a `context` (e.g., `pos`, `subscription`) to get the relevant payment methods for that specific screen.
3.  **Render UI Dynamically**: The app uses the API response to dynamically render the UI, showing only the payment methods that are currently active.

---

## 2. Backend API Endpoint

The backend provides a public endpoint to fetch the available payment methods. This endpoint does not require authentication.

-   **Endpoint**: `GET /api/settings/payment-methods`
-   **Method**: `GET`
-   **Description**: Returns a list of available payment methods based on global settings and an optional context.
-   **Query Parameters**:
    -   `context` (optional, string): The context for which to fetch payment methods. Can be `pos`, `subscription`, or `general` (default).

-   **Success Response (200 OK)**:
    ```json
    {
      "success": true,
      "data": {
        "paymentMethods": [
          "Cash",
          "EVC Plus"
        ],
        "onlinePaymentsEnabled": true,
        "offlinePaymentsEnabled": true,
        "context": "pos"
      }
    }
    ```

---

## 3. Frontend (Mobile App) Implementation

The mobile app will call this endpoint to get the payment settings.

### **1. API Service Layer (Fetching Settings)**

Create a function in your API service to call the endpoint. You can pass the context dynamically based on the screen the user is on.

```dart
// Example in Dart (Flutter)
Future<Map<String, dynamic>> fetchPaymentMethods(String context) async {
  final uri = Uri.parse('https://your-api.com/api/settings/payment-methods?context=$context');
  final response = await http.get(uri);

  if (response.statusCode == 200) {
    final jsonResponse = json.decode(response.body);
    return jsonResponse['data'];
  } else {
    throw Exception('Failed to load payment methods');
  }
}
```

### **2. State Management and UI**

-   Use a state management solution (like BLoC or Riverpod) to fetch and store the payment settings when a relevant screen (e.g., POS checkout) is loaded.
-   Use the `paymentMethods` array from the response to dynamically generate the list of payment option buttons or widgets.
-   You can use the `onlinePaymentsEnabled` and `offlinePaymentsEnabled` flags to show a general message to the user if one of the categories is disabled entirely.

### **Example Usage in a Widget:**

```dart
// Example of how to use the fetched data in a Flutter widget

FutureBuilder<Map<String, dynamic>>(
  future: fetchPaymentMethods('pos'), // Fetch settings for the POS screen
  builder: (context, snapshot) {
    if (snapshot.connectionState == ConnectionState.waiting) {
      return CircularProgressIndicator();
    } else if (snapshot.hasError) {
      return Text('Error: ${snapshot.error}');
    } else if (snapshot.hasData) {
      final settings = snapshot.data!;
      final List<String> methods = List<String>.from(settings['paymentMethods']);

      return Wrap(
        spacing: 8.0,
        children: methods.map((method) => ElevatedButton(
          onPressed: () => print('Selected: $method'),
          child: Text(method),
        )).toList(),
      );
    } else {
      return Text('No payment methods available.');
    }
  },
)
```

This approach ensures that the payment options in your mobile app are always in sync with the settings configured in the backend, providing a flexible and centrally managed system.
