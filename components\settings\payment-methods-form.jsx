/**
 * Payment Methods Form Component
 * Matches backend data structure: settingsController.updatePaymentMethods
 * Uses exact backend field names and formats
 */

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Button } from '../ui/button';
import { Switch } from '../ui/switch';
import { Label } from '../ui/label';
import { Alert, AlertDescription } from '../ui/alert';
import { CheckCircle, XCircle, Loader2, CreditCard } from 'lucide-react';
import { usePaymentSettingsQuery } from '../../hooks/use-payment-settings-query';
import { getErrorMessage } from '../../lib/utils/errorHandler';

/**
 * Payment Methods Form Component
 * @param {Object} props - Component props
 * @param {string} [props.context='general'] - Payment context ('general', 'subscription', 'pos')
 * @param {string|null} [props.shopId] - Shop ID for shop-specific settings, null for global
 * @param {Function} [props.onSave] - Callback when settings are saved successfully
 */
export default function PaymentMethodsForm({ context = 'general', shopId = null, onSave }) {
  const {
    paymentMethods,
    onlinePaymentsEnabled,
    offlinePaymentsEnabled,
    paymentMethodsContext,
    isLoadingPaymentMethods,
    isSavingPaymentMethods,
    updatePaymentMethods,
    paymentMethodsError
  } = usePaymentSettingsQuery({ context, shopId });

  // Form state matching exact backend format
  const [formData, setFormData] = useState({
    enableOnline: false,
    enableOffline: false,
    paymentMethods: []
  });

  const [saveStatus, setSaveStatus] = useState(null);

  // Available payment methods with their categories (matching backend logic)
  const availablePaymentMethods = [
    // Online methods
    { name: 'EVC Plus', displayName: 'EVC Plus', category: 'online' },
    { name: 'Card', displayName: 'Credit/Debit Card', category: 'online' },
    { name: 'Mobile Money', displayName: 'Mobile Money', category: 'online' },
    
    // Offline methods
    { name: 'Cash', displayName: 'Cash', category: 'offline' },
    { name: 'Bank Transfer', displayName: 'Bank Transfer', category: 'offline' },
    { name: 'Check', displayName: 'Check', category: 'offline' },
    { name: 'Other', displayName: 'Other', category: 'offline' },
    { name: 'offline', displayName: 'Offline Payment', category: 'offline' }
  ];

  // Load existing settings into form
  useEffect(() => {
    if (!isLoadingPaymentMethods) {
      setFormData({
        enableOnline: onlinePaymentsEnabled,
        enableOffline: offlinePaymentsEnabled,
        paymentMethods: paymentMethods || []
      });
    }
  }, [isLoadingPaymentMethods, onlinePaymentsEnabled, offlinePaymentsEnabled, paymentMethods]);

  // Clear status messages after delay
  useEffect(() => {
    if (saveStatus) {
      const timer = setTimeout(() => setSaveStatus(null), 5000);
      return () => clearTimeout(timer);
    }
  }, [saveStatus]);

  const handleSwitchChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));

    // Auto-update payment methods based on switches
    if (field === 'enableOnline' || field === 'enableOffline') {
      setFormData(prev => {
        const newMethods = [...prev.paymentMethods];
        
        if (field === 'enableOnline' && !value) {
          // Remove online methods when online is disabled
          const onlineMethods = availablePaymentMethods
            .filter(method => method.category === 'online')
            .map(method => method.name);
          return {
            ...prev,
            [field]: value,
            paymentMethods: newMethods.filter(method => !onlineMethods.includes(method))
          };
        }
        
        if (field === 'enableOffline' && !value) {
          // Remove offline methods when offline is disabled
          const offlineMethods = availablePaymentMethods
            .filter(method => method.category === 'offline')
            .map(method => method.name);
          return {
            ...prev,
            [field]: value,
            paymentMethods: newMethods.filter(method => !offlineMethods.includes(method))
          };
        }
        
        return { ...prev, [field]: value };
      });
    }
  };

  const handleMethodToggle = (methodName, enabled) => {
    setFormData(prev => ({
      ...prev,
      paymentMethods: enabled
        ? [...prev.paymentMethods, methodName]
        : prev.paymentMethods.filter(method => method !== methodName)
    }));
  };

  const isMethodEnabled = (methodName) => {
    return formData.paymentMethods.includes(methodName);
  };

  const isMethodAvailable = (method) => {
    if (method.category === 'online') {
      return formData.enableOnline;
    }
    if (method.category === 'offline') {
      return formData.enableOffline;
    }
    return true;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    try {
      // Prepare payload in exact backend expected format:
      // { enableOnline, enableOffline, context, methods, shopId }
      const payload = {
        enableOnline: formData.enableOnline,
        enableOffline: formData.enableOffline,
        context: context,
        methods: formData.paymentMethods,
        shopId: shopId
      };

      console.log('[PaymentMethodsForm] Submitting payload:', payload);

      const result = await updatePaymentMethods(payload);
      console.log('[PaymentMethodsForm] Full result object:', result);
      console.log('[PaymentMethodsForm] Result message type:', typeof result?.message);
      console.log('[PaymentMethodsForm] Result message value:', result?.message);
      
      if (result && result.success) {
        setSaveStatus({ type: 'success', message: 'Payment methods updated successfully' });
        onSave?.(result);
      } else {
        setSaveStatus({ type: 'error', message: 'Failed to update payment methods' });
      }
    } catch (error) {
      console.error('[PaymentMethodsForm] Save error:', error);
      setSaveStatus({ 
        type: 'error', 
        message: 'Failed to update payment methods - error caught'
      });
    }
  };

  if (isLoadingPaymentMethods) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center p-6">
          <Loader2 className="w-6 h-6 animate-spin mr-2" />
          Loading payment methods...
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <CreditCard className="w-5 h-5" />
          Payment Methods Configuration
          <span className="text-sm font-normal text-gray-500">
            {context}{shopId ? `, Shop: ${shopId}` : ', Global'}
          </span>
        </CardTitle>
      </CardHeader>
      
      <CardContent className="space-y-6">
        {/* Error State */}
        {paymentMethodsError && (
          <Alert variant="destructive">
            <XCircle className="h-4 w-4" />
            <AlertDescription>
              Failed to load payment methods: {getErrorMessage(paymentMethodsError)}
            </AlertDescription>
          </Alert>
        )}

        {/* Save Status */}
        {saveStatus && (
          <Alert variant={saveStatus.type === 'success' ? 'default' : 'destructive'}>
            {saveStatus.type === 'success' ? (
              <CheckCircle className="h-4 w-4" />
            ) : (
              <XCircle className="h-4 w-4" />
            )}
            <AlertDescription>
              {String(saveStatus.message || 'Status update')}
            </AlertDescription>
          </Alert>
        )}

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Global Payment Switches */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Global Payment Settings</h3>
            
            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor={`enableOnline-${context}-${shopId || 'global'}`}>Enable Online Payments</Label>
                <p className="text-sm text-gray-500">
                  Allow digital payment methods like EVC, cards, and mobile money
                </p>
              </div>
              <Switch
                id={`enableOnline-${context}-${shopId || 'global'}`}
                checked={formData.enableOnline}
                onCheckedChange={(checked) => handleSwitchChange('enableOnline', checked)}
              />
            </div>

            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor={`enableOffline-${context}-${shopId || 'global'}`}>Enable Offline Payments</Label>
                <p className="text-sm text-gray-500">
                  Allow cash, bank transfers, and other offline payment methods
                </p>
              </div>
              <Switch
                id={`enableOffline-${context}-${shopId || 'global'}`}
                checked={formData.enableOffline}
                onCheckedChange={(checked) => handleSwitchChange('enableOffline', checked)}
              />
            </div>
          </div>

          {/* Individual Payment Methods */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Available Payment Methods</h3>
            
            {/* Online Methods */}
            <div className="space-y-3">
              <h4 className="text-md font-medium text-blue-600">Online Methods</h4>
              {availablePaymentMethods
                .filter(method => method.category === 'online')
                .map(method => (
                  <div key={method.name} className="flex items-center justify-between">
                    <div className={!isMethodAvailable(method) ? 'opacity-50' : ''}>
                      <Label htmlFor={`method-${method.name}-${context}-${shopId || 'global'}`}>
                        {method.displayName}
                      </Label>
                      <p className="text-sm text-gray-500">
                        Digital payment via {method.displayName.toLowerCase()}
                      </p>
                    </div>
                    <Switch
                      id={`method-${method.name}-${context}-${shopId || 'global'}`}
                      checked={isMethodEnabled(method.name)}
                      disabled={!isMethodAvailable(method)}
                      onCheckedChange={(checked) => 
                        handleMethodToggle(method.name, checked)
                      }
                    />
                  </div>
                ))}
            </div>

            {/* Offline Methods */}
            <div className="space-y-3">
              <h4 className="text-md font-medium text-green-600">Offline Methods</h4>
              {availablePaymentMethods
                .filter(method => method.category === 'offline')
                .map(method => (
                  <div key={method.name} className="flex items-center justify-between">
                    <div className={!isMethodAvailable(method) ? 'opacity-50' : ''}>
                      <Label htmlFor={`method-${method.name}-offline-${context}-${shopId || 'global'}`}>
                        {method.displayName}
                      </Label>
                      <p className="text-sm text-gray-500">
                        Payment via {method.displayName.toLowerCase()}
                      </p>
                    </div>
                    <Switch
                      id={`method-${method.name}-offline-${context}-${shopId || 'global'}`}
                      checked={isMethodEnabled(method.name)}
                      disabled={!isMethodAvailable(method)}
                      onCheckedChange={(checked) => 
                        handleMethodToggle(method.name, checked)
                      }
                    />
                  </div>
                ))}
            </div>
          </div>

          {/* Submit Button */}
          <div className="pt-4">
            <Button
              type="submit"
              disabled={isSavingPaymentMethods}
              className="w-full"
            >
              {isSavingPaymentMethods ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  Saving Payment Methods...
                </>
              ) : (
                'Save Payment Methods'
              )}
            </Button>
          </div>

          {/* Debug Info (for development) */}
          {process.env.NODE_ENV === 'development' && (
            <div className="mt-4 p-3 bg-gray-100 rounded text-xs">
              <details>
                <summary className="cursor-pointer font-medium">Debug Info</summary>
                <pre className="mt-2">
                  {JSON.stringify({
                    formData,
                    backendData: {
                      paymentMethods,
                      onlinePaymentsEnabled,
                      offlinePaymentsEnabled,
                      paymentMethodsContext
                    }
                  }, null, 2)}
                </pre>
              </details>
            </div>
          )}
        </form>
      </CardContent>
    </Card>
  );
} 