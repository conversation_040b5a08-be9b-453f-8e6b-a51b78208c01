/**
 * Base Service Utilities
 * 
 * Simplified utility functions for common service operations
 * Replaces the over-engineered class-based approach
 */
import { handleApiError, handleApiSuccess } from '../utils/errorHandler';
import apiBridge from '../api/bridge';

/**
 * Handle API errors consistently across all services
 * @param {Error} error - The error object
 * @param {string} context - Context where the error occurred
 * @param {boolean} showToast - Whether to show a toast message
 * @returns {Object} Standardized error object
 */
export const handleError = (error, context, showToast = true) => {
  return handleApiError(error, context, { 
    showToast, 
    throwError: false 
  });
};

/**
 * Handle API success consistently
 * @param {string} message - Success message
 * @param {Object} data - Optional data to return
 * @returns {Object} Success response
 */
export const handleSuccess = (message, data = null) => {
  return handleApiSuccess(message, data);
};

/**
 * Clear cache for specific endpoints
 * @param {string} endpoint - Endpoint to clear from cache
 */
export const clearCache = (endpoint) => {
  if (apiBridge && apiBridge.clearCache) {
    apiBridge.clearCache(endpoint);
  }
};

/**
 * Validate required fields
 * @param {Object} data - Data to validate
 * @param {Array} requiredFields - Array of required field names
 * @returns {Object} Validation result
 */
export const validateRequiredFields = (data, requiredFields) => {
  const missingFields = requiredFields.filter(field => 
    !data[field] || (typeof data[field] === 'string' && data[field].trim() === '')
  );
  
  if (missingFields.length > 0) {
    return {
      isValid: false,
      message: `Missing required fields: ${missingFields.join(', ')}`,
      missingFields
    };
  }
  
  return { isValid: true };
};

/**
 * Process API response consistently
 * @param {Object} response - API response
 * @param {string} successMessage - Success message to show
 * @returns {Object} Processed response data
 */
export const processApiResponse = (response, successMessage) => {
  // Handle case where response or response.data is undefined
  if (!response || !response.data) {
    console.error('Invalid API response - missing data:', response);
    throw new Error('Invalid response from server');
  }
  
  if (response.data.success) {
    if (successMessage) {
      handleSuccess(successMessage);
    }
    return response.data.data || response.data;
  }
  
  // If not successful, throw error with server message
  const errorMessage = response.data.message || 'Request failed';
  console.error('API request failed:', response.data);
  throw new Error(errorMessage);
};

/**
 * Log API calls for debugging
 * @param {string} context - Context/service making the call
 * @param {string} endpoint - API endpoint being called
 * @param {Object} data - Request data (optional)
 */
export const logApiCall = (context, endpoint, data = null) => {
  if (process.env.NODE_ENV === 'development') {
    console.log(`[${context}] API Call: ${endpoint}`, data ? { data } : '');
  }
};

// Export all utilities as named exports for flexibility
export default {
  handleError,
  handleSuccess,
  clearCache,
  validateRequiredFields,
  processApiResponse,
  logApiCall
};
