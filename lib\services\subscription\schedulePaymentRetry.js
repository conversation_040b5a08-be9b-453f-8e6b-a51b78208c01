import apiBridge from '@/lib/api/bridge';
import BaseService from '../baseService';

const schedulePaymentRetry = async (subscriptionId, retryData) => {
  try {
    const response = await apiBridge.post(
      ENDPOINTS.subscription.schedulePaymentRetry.replace(':subscriptionId', subscriptionId),
      retryData
    );
    
    return BaseService.processApiResponse(response);
  } catch (error) {
    throw BaseService.handleApiError(error);
  }
};

export default schedulePaymentRetry; 
