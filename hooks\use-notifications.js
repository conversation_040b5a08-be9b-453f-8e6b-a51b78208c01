"use client";

import { useState, useCallback, useRef } from 'react';
import { toast } from 'sonner';
import NotificationService from '@/lib/services/notification';

/**
 * Notifications Hook
 * 
 * Comprehensive hook for notification operations (SuperAdmin only)
 * Backend-matched implementation with proper validation
 * 
 * Available backend endpoints:
 * - Push to shops
 * - Broadcast notifications  
 * - Debt reminders (7_days, 3_days, overdue)
 * - Get notification stats
 * - Test Firebase connection
 * - Get notification targets
 */
export function useNotifications(options = {}) {
  const {
    showToastMessages = true,
    onSuccess,
    onError,
    onNotificationSent,
    onStatsLoaded,
    onTargetsLoaded
  } = options;

  // Operation states
  const [isSendingToShops, setIsSendingToShops] = useState(false);
  const [isSendingBroadcast, setIsSendingBroadcast] = useState(false);
  const [isSendingDebtReminders, setIsSendingDebtReminders] = useState(false);
  const [isLoadingStats, setIsLoadingStats] = useState(false);
  const [isTestingFirebase, setIsTestingFirebase] = useState(false);
  const [isLoadingTargets, setIsLoadingTargets] = useState(false);
  const [error, setError] = useState(null);

  // Cache for stats and targets
  const [notificationStats, setNotificationStats] = useState(null);
  const [notificationTargets, setNotificationTargets] = useState(null);

  // Track active operations to prevent conflicts
  const activeOperationsRef = useRef(new Set());

  /**
   * Send notification to specific shops (SuperAdmin only)
   * @param {Object} notificationData - Notification configuration
   * @param {string[]} notificationData.shopIds - Array of shop IDs
   * @param {string} notificationData.title - Notification title (1-100 chars)
   * @param {string} notificationData.message - Notification message (1-500 chars)
   * @param {string} [notificationData.priority='normal'] - Priority: low, normal, high
   * @param {string} [notificationData.actionUrl] - Optional action URL
   * @param {string} [notificationData.actionLabel] - Optional action label
   */
  const sendToShops = useCallback(async (notificationData) => {
    if (activeOperationsRef.current.has('sendToShops')) {
      console.log('[useNotifications] Send to shops operation already in progress');
      return null;
    }

    try {
      activeOperationsRef.current.add('sendToShops');
      setIsSendingToShops(true);
      setError(null);

      console.log('[useNotifications] Sending to shops:', notificationData);

      // Validate using our backend-matched service
      const validation = NotificationService.validateNotificationData(notificationData, 'shops');
      if (!validation.isValid) {
        throw new Error(`Validation failed: ${validation.errors.join(', ')}`);
      }

      const result = await NotificationService.sendToShops(notificationData);

      // Trigger callbacks
      if (onNotificationSent) onNotificationSent('shops', result);
      if (onSuccess) onSuccess('sendToShops', result);

      console.log('[useNotifications] Shops notification sent successfully:', result);
      return result;
    } catch (err) {
      console.error('[useNotifications] Error sending to shops:', err);
      
      const errorMessage = err.message || 'Failed to send notification to shops';
      setError(errorMessage);

      if (showToastMessages) {
        toast.error(errorMessage);
      }

      if (onError) onError('sendToShops', err);
      throw err;
    } finally {
      setIsSendingToShops(false);
      activeOperationsRef.current.delete('sendToShops');
    }
  }, [showToastMessages, onSuccess, onError, onNotificationSent]);

  /**
   * Send broadcast notification to all active users (SuperAdmin only)
   * @param {Object} notificationData - Notification configuration
   * @param {string} notificationData.title - Notification title (1-100 chars)
   * @param {string} notificationData.message - Notification message (1-500 chars)
   * @param {string} [notificationData.priority='normal'] - Priority: low, normal, high
   * @param {string} [notificationData.actionUrl] - Optional action URL
   * @param {string} [notificationData.actionLabel] - Optional action label
   */
  const sendBroadcast = useCallback(async (notificationData) => {
    if (activeOperationsRef.current.has('sendBroadcast')) {
      console.log('[useNotifications] Broadcast operation already in progress');
      return null;
    }

    try {
      activeOperationsRef.current.add('sendBroadcast');
      setIsSendingBroadcast(true);
      setError(null);

      console.log('[useNotifications] Sending broadcast:', notificationData);

      // Validate using our backend-matched service
      const validation = NotificationService.validateNotificationData(notificationData, 'broadcast');
      if (!validation.isValid) {
        throw new Error(`Validation failed: ${validation.errors.join(', ')}`);
      }

      const result = await NotificationService.sendBroadcast(notificationData);

      if (onNotificationSent) onNotificationSent('broadcast', result);
      if (onSuccess) onSuccess('sendBroadcast', result);

      console.log('[useNotifications] Broadcast sent successfully:', result);
      return result;
    } catch (err) {
      console.error('[useNotifications] Error sending broadcast:', err);
      
      const errorMessage = err.message || 'Failed to send broadcast notification';
      setError(errorMessage);

      if (showToastMessages) {
        toast.error(errorMessage);
      }

      if (onError) onError('sendBroadcast', err);
      throw err;
    } finally {
      setIsSendingBroadcast(false);
      activeOperationsRef.current.delete('sendBroadcast');
    }
  }, [showToastMessages, onSuccess, onError, onNotificationSent]);

  /**
   * Send debt reminder notifications (SuperAdmin only)
   * @param {Object} reminderData - Reminder configuration
   * @param {string[]} [reminderData.shopIds] - Specific shop IDs (optional)
   * @param {string} [reminderData.reminderType='overdue'] - Type: 7_days, 3_days, overdue (backend values)
   * @param {number} [reminderData.daysOverdue] - Days overdue filter
   * @param {string} [reminderData.priority='normal'] - Priority: low, normal, high
   */
  const sendDebtReminders = useCallback(async (reminderData = {}) => {
    if (activeOperationsRef.current.has('sendDebtReminders')) {
      console.log('[useNotifications] Debt reminders operation already in progress');
      return null;
    }

    try {
      activeOperationsRef.current.add('sendDebtReminders');
      setIsSendingDebtReminders(true);
      setError(null);
      
      if (showToastMessages) {
        toast.info('Sending debt reminder notifications...');
      }

      console.log('[useNotifications] Sending debt reminders:', reminderData);

      // Validate using our backend-matched service
      const validation = NotificationService.validateNotificationData(reminderData, 'debt_reminders');
      if (!validation.isValid) {
        throw new Error(`Validation failed: ${validation.errors.join(', ')}`);
      }

      const response = await NotificationService.sendDebtReminders(reminderData);

      if (response.success) {
        const result = response.data;
        
        if (showToastMessages) {
          toast.success('Debt reminder notifications sent successfully');
        }

        if (onNotificationSent) onNotificationSent('debt_reminders', result);
        if (onSuccess) onSuccess('sendDebtReminders', result);

        console.log('[useNotifications] Debt reminders sent successfully:', result);
        return result;
      } else {
        throw new Error(response.message || 'Failed to send debt reminders');
      }
    } catch (err) {
      console.error('[useNotifications] Error sending debt reminders:', err);
      
      const errorMessage = err.message || 'Failed to send debt reminders';
      setError(errorMessage);

      if (showToastMessages) {
        toast.error(errorMessage);
      }

      if (onError) onError('sendDebtReminders', err);
      throw err;
    } finally {
      setIsSendingDebtReminders(false);
      activeOperationsRef.current.delete('sendDebtReminders');
    }
  }, [showToastMessages, onSuccess, onError, onNotificationSent]);

  /**
   * Get notification statistics
   * @param {Object} [options] - Query options
   * @param {string} [options.shopId] - Filter by specific shop
   * @param {number} [options.days=30] - Number of days to query
   * @param {boolean} [options.useCache=true] - Whether to use cached data
   */
  const getNotificationStats = useCallback(async (options = {}) => {
    const operationKey = `getStats-${JSON.stringify(options)}`;
    
    if (activeOperationsRef.current.has(operationKey)) {
      console.log('[useNotifications] Get stats operation already in progress');
      return notificationStats;
    }

    try {
      activeOperationsRef.current.add(operationKey);
      setIsLoadingStats(true);
      setError(null);
      
      console.log('[useNotifications] Loading notification stats:', options);

      const response = await NotificationService.getNotificationStats(options);

      if (response.success || response.stats) {
        const stats = response.data || response;
        setNotificationStats(stats);

        if (onStatsLoaded) onStatsLoaded(stats);
        if (onSuccess) onSuccess('getNotificationStats', stats);

        console.log('[useNotifications] Stats loaded successfully:', stats);
        return stats;
      } else {
        throw new Error(response.message || 'Failed to load notification stats');
      }
    } catch (err) {
      console.error('[useNotifications] Error loading stats:', err);
      
      const errorMessage = err.message || 'Failed to load notification stats';
      setError(errorMessage);

      // Return empty stats to prevent UI breaking
      const emptyStats = { stats: {}, details: [] };
      setNotificationStats(emptyStats);

      if (onError) onError('getNotificationStats', err);
      return emptyStats;
    } finally {
      setIsLoadingStats(false);
      activeOperationsRef.current.delete(operationKey);
    }
  }, [notificationStats, onSuccess, onError, onStatsLoaded]);

  /**
   * Test Firebase connection
   */
  const testFirebaseConnection = useCallback(async () => {
    if (activeOperationsRef.current.has('testFirebase')) {
      console.log('[useNotifications] Firebase test already in progress');
      return null;
    }

    try {
      activeOperationsRef.current.add('testFirebase');
      setIsTestingFirebase(true);
      setError(null);
      
      if (showToastMessages) {
        toast.info('Testing Firebase connection...');
      }

      console.log('[useNotifications] Testing Firebase connection');

      const response = await NotificationService.testFirebaseConnection();

      if (response.success) {
        const result = response.data;
        
        if (showToastMessages) {
          toast.success('Firebase connection test successful');
        }

        if (onSuccess) onSuccess('testFirebaseConnection', result);

        console.log('[useNotifications] Firebase test successful:', result);
        return result;
      } else {
        throw new Error(response.message || 'Firebase connection test failed');
      }
    } catch (err) {
      console.error('[useNotifications] Firebase test error:', err);
      
      const errorMessage = err.message || 'Firebase connection test failed';
      setError(errorMessage);

      if (showToastMessages) {
        toast.error(errorMessage);
      }

      if (onError) onError('testFirebaseConnection', err);
      throw err;
    } finally {
      setIsTestingFirebase(false);
      activeOperationsRef.current.delete('testFirebase');
    }
  }, [showToastMessages, onSuccess, onError]);

  /**
   * Get notification targets (shops and users available for notifications)
   * @param {boolean} [useCache=true] - Whether to use cached data
   */
  const getNotificationTargets = useCallback(async (useCache = true) => {
    if (useCache && notificationTargets) {
      return notificationTargets;
    }

    if (activeOperationsRef.current.has('getTargets')) {
      console.log('[useNotifications] Get targets operation already in progress');
      return notificationTargets;
    }

    try {
      activeOperationsRef.current.add('getTargets');
      setIsLoadingTargets(true);
      setError(null);
      
      console.log('[useNotifications] Loading notification targets');

      const response = await NotificationService.getNotificationTargets();

      if (response.success || response.shops || response.users) {
        const targets = response.data || response;
        setNotificationTargets(targets);

        if (onTargetsLoaded) onTargetsLoaded(targets);
        if (onSuccess) onSuccess('getNotificationTargets', targets);

        console.log('[useNotifications] Targets loaded successfully:', targets);
        return targets;
      } else {
        throw new Error(response.message || 'Failed to load notification targets');
      }
    } catch (err) {
      console.error('[useNotifications] Error loading targets:', err);
      
      const errorMessage = err.message || 'Failed to load notification targets';
      setError(errorMessage);

      // Return empty targets to prevent UI breaking
      const emptyTargets = { shops: [], users: [], total: 0 };
      setNotificationTargets(emptyTargets);

      if (onError) onError('getNotificationTargets', err);
      return emptyTargets;
    } finally {
      setIsLoadingTargets(false);
      activeOperationsRef.current.delete('getTargets');
    }
  }, [notificationTargets, onSuccess, onError, onTargetsLoaded]);

  /**
   * Clear error state
   */
  const clearError = useCallback(() => {
    setError(null);
  }, []);

  /**
   * Clear cached data
   */
  const clearCache = useCallback(() => {
    setNotificationStats(null);
    setNotificationTargets(null);
    setError(null);
  }, []);

  // Computed loading state
  const isLoading = isSendingToShops || isSendingBroadcast || isSendingDebtReminders || 
                   isLoadingStats || isTestingFirebase || isLoadingTargets;

  return {
    // Send operations
    sendToShops,
    sendBroadcast,
    sendDebtReminders,
    
    // Query operations
    getNotificationStats,
    testFirebaseConnection,
    getNotificationTargets,
    
    // Loading states
    isSendingToShops,
    isSendingBroadcast,
    isSendingDebtReminders,
    isLoadingStats,
    isTestingFirebase,
    isLoadingTargets,
    isLoading,
    
    // Data state
    notificationStats,
    notificationTargets,
    error,
    
    // Utility methods
    clearError,
    clearCache,
    
    // Validation helper (expose the service validation)
    validateNotificationData: NotificationService.validateNotificationData
  };
} 