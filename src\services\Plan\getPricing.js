/**
 * Get pricing for a specific plan type
 * Returns formatted pricing information for client display
 */
const { logError } = require('../../utils');
const getPlanByType = require('./getPlanByType');

/**
 * Get pricing for a specific plan type
 * @param {string} planType - Type of plan (trial, monthly, yearly)
 * @returns {Promise<Object>} Pricing information
 */
const getPricing = async (planType) => {
  try {
    const plan = await getPlanByType(planType);
    
    return {
      basePrice: plan.pricing.basePrice,
      currency: plan.pricing.currency,
      billingCycle: plan.pricing.billingCycle,
      setupFee: plan.pricing.setupFee,
      trialDays: plan.pricing.trialDays,
      planId: plan.planId,
      planName: plan.displayName
    };
  } catch (error) {
    logError(`Failed to get pricing for plan type: ${planType}`, 'PlanService', error);
    throw error;
  }
};

module.exports = getPricing;
