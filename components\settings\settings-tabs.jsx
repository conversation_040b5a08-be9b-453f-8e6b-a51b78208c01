import { useState } from 'react';
import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Ta<PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";

/**
 * Reusable settings tabs component for consistent navigation across settings pages
 * Maintains tab state and provides a consistent UI pattern
 * 
 * @param {Object} props - Component props
 * @param {string} props.defaultValue - Default selected tab
 * @param {Array} props.tabs - Array of tab objects with id and label
 * @param {React.ReactNode} props.children - Tab content components
 * @param {Function} props.onTabChange - Optional callback when tab changes
 * @returns {JSX.Element} Settings tabs component
 */
export function SettingsTabs({
  defaultValue,
  tabs = [],
  children,
  onTabChange,
  className = '',
}) {
  const [activeTab, setActiveTab] = useState(defaultValue);
  
  const handleTabChange = (value) => {
    setActiveTab(value);
    if (typeof onTabChange === 'function') {
      onTabChange(value);
    }
  };
  
  return (
    <Tabs 
      value={activeTab} 
      onValueChange={handleTabChange} 
      className={className}
      data-testid="settings-tabs"
    >
      <TabsList className="mb-8">
        {tabs.map((tab) => (
          <TabsTrigger 
            key={tab.id} 
            value={tab.id}
            data-testid={`tab-${tab.id}`}
          >
            {tab.label}
          </TabsTrigger>
        ))}
      </TabsList>
      
      {children}
    </Tabs>
  );
}
