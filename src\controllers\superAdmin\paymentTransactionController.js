/**
 * SuperAdmin Payment Transaction Controller
 * Handles all payment transaction management for SuperAdmin
 */
const { Payment, Shop, Subscription } = require('../../models');
const { AppError, logInfo, logError, logSuccess } = require('../../utils');
const PaymentTransactionService = require('../../services/superAdmin/paymentTransactionService');

// Role constants to prevent case sensitivity issues
const ROLES = {
  SUPER_ADMIN: 'superAdmin',
  ADMIN: 'admin',
  EMPLOYEE: 'employee',
  CUSTOMER: 'customer'
};

/**
 * Get all payment transactions with filtering and pagination
 * GET /api/superadmin/payment-transactions
 */
const getAllPaymentTransactions = async (req, res, next) => {
  try {
    // SuperAdmin role validation
    if (req.user.role !== ROLES.SUPER_ADMIN) {
      return next(new AppError('Access denied. SuperAdmin privileges required.', 403, 'forbidden'));
    }

    const {
      page = 1,
      limit = 10,
      status,
      paymentMethod,
      startDate,
      endDate,
      search,
      sortBy = 'createdAt',
      sortOrder = 'desc'
    } = req.query;

    // Validate pagination parameters
    const pageNum = parseInt(page);
    const limitNum = Math.min(parseInt(limit), 100); // Max 100 records per page

    if (pageNum < 1 || limitNum < 1) {
      return next(new AppError('Invalid pagination parameters', 400, 'invalid_pagination'));
    }

    const options = {
      page: pageNum,
      limit: limitNum,
      status,
      method: paymentMethod,
      startDate,
      endDate,
      search,
      sortBy,
      sortOrder
    };

    const result = await PaymentTransactionService.getPaymentTransactions(options);

    logInfo(`SuperAdmin ${req.user.email} retrieved ${result.payments.length} payment transactions`, 'PaymentTransactionController');

    return res.status(200).json({
      success: true,
      message: 'Payment transactions retrieved successfully',
      data: {
        payments: result.payments,
        pagination: result.pagination
      }
    });
  } catch (error) {
    logError(`Error getting payment transactions: ${error.message}`, 'PaymentTransactionController', error);
    return next(error);
  }
};

/**
 * Get single payment transaction by ID
 * GET /api/superadmin/payment-transactions/:paymentId
 */
const getPaymentTransactionById = async (req, res, next) => {
  try {
    // SuperAdmin role validation
    if (req.user.role !== ROLES.SUPER_ADMIN) {
      return next(new AppError('Access denied. SuperAdmin privileges required.', 403, 'forbidden'));
    }

    const { paymentId } = req.params;

    if (!paymentId) {
      return next(new AppError('Payment ID is required', 400, 'missing_payment_id'));
    }

    const payment = await PaymentTransactionService.getPaymentTransactionById(paymentId);

    logInfo(`SuperAdmin ${req.user.email} retrieved payment transaction: ${paymentId}`, 'PaymentTransactionController');

    return res.status(200).json({
      success: true,
      message: 'Payment transaction retrieved successfully',
      data: payment
    });
  } catch (error) {
    logError(`Error getting payment transaction ${req.params.paymentId}: ${error.message}`, 'PaymentTransactionController', error);
    return next(error);
  }
};

/**
 * Approve payment transaction
 * POST /api/superadmin/payment-transactions/:paymentId/approve
 */
const approvePaymentTransaction = async (req, res, next) => {
  try {
    // SuperAdmin role validation
    if (req.user.role !== ROLES.SUPER_ADMIN) {
      return next(new AppError('Access denied. SuperAdmin privileges required.', 403, 'forbidden'));
    }

    const { paymentId } = req.params;
    const { approvalNotes, activateSubscription = true } = req.body;

    if (!paymentId) {
      return next(new AppError('Payment ID is required', 400, 'missing_payment_id'));
    }

    const result = await PaymentTransactionService.approvePayment(
      paymentId,
      req.user.userId,
      approvalNotes,
      activateSubscription
    );

    logSuccess(`SuperAdmin ${req.user.email} approved payment transaction: ${paymentId}`, 'PaymentTransactionController');

    return res.status(200).json({
      success: true,
      message: 'Payment approved successfully',
      data: {
        paymentId: result.paymentId,
        status: result.status,
        approvedAt: result.approvedAt,
        approvedBy: result.approvedBy,
        subscriptionStatus: result.subscriptionStatus
      }
    });
  } catch (error) {
    logError(`Error approving payment transaction ${req.params.paymentId}: ${error.message}`, 'PaymentTransactionController', error);
    return next(error);
  }
};

/**
 * Reject payment transaction
 * POST /api/superadmin/payment-transactions/:paymentId/reject
 */
const rejectPaymentTransaction = async (req, res, next) => {
  try {
    // SuperAdmin role validation
    if (req.user.role !== ROLES.SUPER_ADMIN) {
      return next(new AppError('Access denied. SuperAdmin privileges required.', 403, 'forbidden'));
    }

    const { paymentId } = req.params;
    const { rejectionReason, rejectionNotes } = req.body;

    if (!paymentId) {
      return next(new AppError('Payment ID is required', 400, 'missing_payment_id'));
    }

    if (!rejectionReason) {
      return next(new AppError('Rejection reason is required', 400, 'missing_rejection_reason'));
    }

    const result = await PaymentTransactionService.rejectPayment(
      paymentId,
      req.user.userId,
      rejectionReason,
      rejectionNotes
    );

    logInfo(`SuperAdmin ${req.user.email} rejected payment transaction: ${paymentId}`, 'PaymentTransactionController');

    return res.status(200).json({
      success: true,
      message: 'Payment rejected successfully',
      data: {
        paymentId: result.paymentId,
        status: result.status,
        rejectedAt: result.rejectedAt,
        rejectedBy: result.rejectedBy,
        rejectionReason: result.rejectionReason
      }
    });
  } catch (error) {
    logError(`Error rejecting payment transaction ${req.params.paymentId}: ${error.message}`, 'PaymentTransactionController', error);
    return next(error);
  }
};

/**
 * Export payment transactions
 * GET /api/superadmin/payment-transactions/export
 */
const exportPaymentTransactions = async (req, res, next) => {
  try {
    // SuperAdmin role validation
    if (req.user.role !== ROLES.SUPER_ADMIN) {
      return next(new AppError('Access denied. SuperAdmin privileges required.', 403, 'forbidden'));
    }

    const {
      format = 'csv',
      status,
      paymentMethod,
      startDate,
      endDate,
      search
    } = req.query;

    // Validate export format
    if (!['csv', 'pdf'].includes(format)) {
      return next(new AppError('Invalid export format. Must be csv or pdf', 400, 'invalid_export_format'));
    }

    const options = {
      format,
      status,
      method: paymentMethod,
      startDate,
      endDate,
      search
    };

    const result = await PaymentTransactionService.exportPaymentData(options);

    logInfo(`SuperAdmin ${req.user.email} exported payment transactions in ${format} format`, 'PaymentTransactionController');

    // Set appropriate headers for file download
    res.setHeader('Content-Type', format === 'csv' ? 'text/csv' : 'application/pdf');
    res.setHeader('Content-Disposition', `attachment; filename="payment-transactions-${new Date().toISOString().split('T')[0]}.${format}"`);

    return res.status(200).send(result.data);
  } catch (error) {
    logError(`Error exporting payment transactions: ${error.message}`, 'PaymentTransactionController', error);
    return next(error);
  }
};

/**
 * Get payment transaction statistics
 * GET /api/superadmin/payment-transactions/stats
 */
const getPaymentTransactionStats = async (req, res, next) => {
  try {
    // SuperAdmin role validation
    if (req.user.role !== ROLES.SUPER_ADMIN) {
      return next(new AppError('Access denied. SuperAdmin privileges required.', 403, 'forbidden'));
    }

    const { startDate, endDate } = req.query;

    const options = {
      startDate,
      endDate
    };

    const stats = await PaymentTransactionService.getPaymentStatistics(options);

    logInfo(`SuperAdmin ${req.user.email} retrieved payment transaction statistics`, 'PaymentTransactionController');

    return res.status(200).json({
      success: true,
      message: 'Payment transaction statistics retrieved successfully',
      data: stats
    });
  } catch (error) {
    logError(`Error getting payment transaction statistics: ${error.message}`, 'PaymentTransactionController', error);
    return next(error);
  }
};

module.exports = {
  getAllPaymentTransactions,
  getPaymentTransactionById,
  approvePaymentTransaction,
  rejectPaymentTransaction,
  exportPaymentTransactions,
  getPaymentTransactionStats
}; 