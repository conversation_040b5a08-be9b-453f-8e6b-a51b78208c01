"use client";

import { useState } from 'react';
import PropTypes from 'prop-types';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Chevron<PERSON>eft,
  ChevronRight,
  Chevrons<PERSON>eft,
  ChevronsRight,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { cn } from "@/lib/utils";

/**
 * @typedef {Object} Column
 * @property {string} key - Unique identifier for the column
 * @property {string} header - Column header text
 * @property {Function} [cell] - Cell renderer function
 * @property {string} [accessorKey] - Data key to access (if no cell renderer)
 * @property {string} [headerClassName] - CSS classes for header
 * @property {string} [cellClassName] - CSS classes for cells
 * @property {Object} [style] - Inline styles for header
 * @property {Object} [cellStyle] - Inline styles for cells
 */

/**
 * @typedef {Object} PaginationConfig
 * @property {number} currentPage - Current page number
 * @property {number} pageSize - Items per page
 * @property {number} totalItems - Total items count
 * @property {number} totalPages - Total pages count
 * @property {Function} onPageChange - Page change handler
 * @property {Function} [onPageSizeChange] - Page size change handler
 */

/**
 * Reusable data table component with pagination, sorting, and row selection
 * 
 * @param {Object} props - Component props
 * @param {Array<Column>} props.columns - Column definitions
 * @param {Array<Object>} props.data - Data items to display
 * @param {boolean} [props.pagination=true] - Whether to show pagination
 * @param {number} [props.currentPage=1] - Current page number
 * @param {number} [props.pageSize=10] - Items per page
 * @param {number} [props.totalItems=0] - Total items count
 * @param {number} [props.totalPages=1] - Total pages count
 * @param {Function} [props.onPageChange] - Page change handler
 * @param {Function} [props.onPageSizeChange] - Page size change handler
 * @param {boolean} [props.isLoading=false] - Loading state
 * @param {JSX.Element} [props.emptyState] - Custom empty state UI
 * @param {Function} [props.rowClassName] - Function to determine row class names
 * @param {Function} [props.onRowClick] - Row click handler
 * @param {string} [props.className] - Additional CSS classes
 * @returns {JSX.Element} Rendered component
 */
export function DataTable({
  columns,
  data,
  pagination = true,
  currentPage = 1,
  pageSize = 10,
  totalItems = 0,
  totalPages = 1,
  onPageChange,
  onPageSizeChange,
  isLoading = false,
  emptyState,
  rowClassName,
  onRowClick,
  className,
}) {
  const [selectedRows, setSelectedRows] = useState([]);

  // Calculate pagination values
  const startItem = Math.min((currentPage - 1) * pageSize + 1, totalItems);
  const endItem = Math.min(startItem + pageSize - 1, totalItems);

  // Handle row selection
  const handleRowClick = (item) => {
    if (onRowClick) {
      onRowClick(item);
    }
  };

  // Handle page changes
  const goToPage = (page) => {
    if (page >= 1 && page <= totalPages && onPageChange) {
      onPageChange(page);
    }
  };

  // Render loading state
  if (isLoading) {
    return (
      <div className="w-full rounded-md border bg-background">
        <div className="table-container">
          <Table className={className}>
            <TableHeader>
              <TableRow>
                {columns.map((column) => (
                  <TableHead key={column.id || column.accessorKey || column.header} style={column.style || {}}>
                    {column.header}
                  </TableHead>
                ))}
              </TableRow>
            </TableHeader>
            <TableBody>
              {[...Array(5)].map((_, rowIndex) => (
                <TableRow key={`loading-${rowIndex}`}>
                  {columns.map((column, colIndex) => (
                    <TableCell
                      key={`loading-${rowIndex}-${colIndex}`}
                      style={column.cellStyle || {}}
                      className="px-4 py-3"
                    >
                      <div className="h-4 animate-pulse bg-muted rounded-md" />
                    </TableCell>
                  ))}
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </div>
    );
  }

  // Render empty state
  if (!data || data.length === 0) {
    return (
      <div className="table-container">
        <Table className={className}>
        <TableHeader>
          <TableRow>
            {columns.map((column) => (
              <TableHead key={column.id || column.accessorKey || column.header} style={column.style || {}}>
                {column.header}
              </TableHead>
            ))}
          </TableRow>
        </TableHeader>
        <TableBody>
          <TableRow>
            <TableCell
              colSpan={columns.length}
              className="h-48 text-center"
            >
              {emptyState || (
                <div className="flex flex-col items-center justify-center py-8">
                  <p className="text-muted-foreground">No data available</p>
                </div>
              )}
            </TableCell>
          </TableRow>
        </TableBody>
      </Table>
      </div>
    );
  }

  return (
    <div className="w-full">
      {/* Table wrapper - use table-container for proper horizontal scroll only */}
      <div className="table-container">
        <Table className={className}>
        <TableHeader>
          <TableRow>
            {columns.map((column) => (
              <TableHead
                key={column.id || column.accessorKey || column.header}
                className={column.headerClassName}
                style={column.style || {}}
              >
                {column.header}
              </TableHead>
            ))}
          </TableRow>
        </TableHeader>
        <TableBody>
          {Array.isArray(data) ? data.map((item, rowIndex) => (
            <TableRow
              key={item.id || item.userId || `row-${rowIndex}`}
              className={cn(
                rowClassName ? rowClassName(item) : "",
                onRowClick ? "cursor-pointer hover:bg-muted/50" : ""
              )}
              onClick={() => handleRowClick(item)}
            >
              {columns.map((column, columnIndex) => (
                <TableCell
                  key={`${item.id || item.userId || rowIndex}-${column.id || column.accessorKey || columnIndex}`}
                  className={column.cellClassName}
                  style={column.cellStyle || {}}
                >
                  {column.cell ? (
                    // Support both direct access and row.original patterns for compatibility
                    typeof column.cell.length === 1 ?
                      column.cell(item) :
                      column.cell({ row: { original: item } })
                  ) : item[column.accessorKey || column.id]}
                </TableCell>
              ))}
            </TableRow>
          )) : null}
        </TableBody>
      </Table>
      </div>

      {pagination && totalItems > 0 && (
        <div className="flex items-center justify-between p-4 border-t bg-muted/20">
          <div className="text-sm text-muted-foreground">
            Showing {startItem} to {endItem} of {totalItems} entries
          </div>
          
          <div className="flex items-center space-x-2">
            <div className="flex items-center">
              <span className="text-sm text-muted-foreground mr-2">Rows per page:</span>
              <Select
                value={pageSize.toString()}
                onValueChange={(value) => onPageSizeChange && onPageSizeChange(parseInt(value))}
              >
                <SelectTrigger className="w-16 h-8">
                  <SelectValue placeholder={pageSize} />
                </SelectTrigger>
                <SelectContent>
                  {[10, 25, 50, 100].map((size) => (
                    <SelectItem key={size} value={size.toString()}>
                      {size}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            <div className="flex items-center space-x-1">
              <Button
                variant="outline"
                size="icon"
                className="h-8 w-8"
                onClick={() => goToPage(1)}
                disabled={currentPage <= 1}
              >
                <ChevronsLeft className="h-4 w-4" />
              </Button>
              <Button
                variant="outline"
                size="icon"
                className="h-8 w-8"
                onClick={() => goToPage(currentPage - 1)}
                disabled={currentPage <= 1}
              >
                <ChevronLeft className="h-4 w-4" />
              </Button>
              <span className="text-sm">
                Page {currentPage} of {totalPages || 1}
              </span>
              
              <Button
                variant="outline"
                size="icon"
                className="h-8 w-8"
                onClick={() => goToPage(currentPage + 1)}
                disabled={currentPage === totalPages}
              >
                <ChevronRight className="h-4 w-4" />
                <span className="sr-only">Next page</span>
              </Button>
              <Button
                variant="outline"
                size="icon"
                className="h-8 w-8"
                onClick={() => goToPage(totalPages)}
                disabled={currentPage === totalPages}
              >
                <ChevronsRight className="h-4 w-4" />
                <span className="sr-only">Last page</span>
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

/**
 * PropTypes for DataTable component
 */
DataTable.propTypes = {
  // Structure props
  columns: PropTypes.arrayOf(
    PropTypes.shape({
      key: PropTypes.string.isRequired,
      header: PropTypes.string.isRequired,
      cell: PropTypes.func,
      accessorKey: PropTypes.string,
      headerClassName: PropTypes.string,
      cellClassName: PropTypes.string,
      style: PropTypes.object,
      cellStyle: PropTypes.object
    })
  ).isRequired,
  data: PropTypes.array,
  
  // Pagination props
  pagination: PropTypes.bool,
  currentPage: PropTypes.number,
  pageSize: PropTypes.number,
  totalItems: PropTypes.number,
  totalPages: PropTypes.number,
  onPageChange: PropTypes.func,
  onPageSizeChange: PropTypes.func,
  
  // UI props
  isLoading: PropTypes.bool,
  emptyState: PropTypes.node,
  rowClassName: PropTypes.func,
  onRowClick: PropTypes.func,
  className: PropTypes.string
};
