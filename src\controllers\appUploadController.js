/**
 * App Upload Controller
 * Handles app file upload operations for SuperAdmin
 */
const multer = require('multer');
const path = require('path');
const AppUploadService = require('../services/appUploadService');
const { logInfo, logSuccess, logError, AppError } = require('../utils');

// Configure multer for app file uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, path.join(process.cwd(), 'uploads', 'temp'));
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, 'temp-app-' + uniqueSuffix + path.extname(file.originalname));
  }
});

const fileFilter = (req, file, cb) => {
  // Only allow APK files for Android
  const allowedTypes = ['.apk'];
  const fileExtension = path.extname(file.originalname).toLowerCase();
  
  if (allowedTypes.includes(fileExtension)) {
    cb(null, true);
  } else {
    cb(new AppError('Invalid file type. Only APK files are allowed.', 400), false);
  }
};

const upload = multer({
  storage,
  fileFilter,
  limits: {
    fileSize: 500 * 1024 * 1024 // 500MB limit
  }
});

/**
 * Upload new app file
 * POST /api/app/upload
 */
const uploadApp = async (req, res, next) => {
  try {
    // Check if user exists
    if (!req.user) {
      logError('AUTHENTICATION FAILED: No user found in request', 'AppUploadController');
      return res.status(401).json({
        success: false,
        message: 'Authentication required. User not found in request.',
        statusCode: 401
      });
    }
    
    // Check if user is SuperAdmin
    if (req.user.role !== 'superAdmin') {
      logError(`AUTHORIZATION FAILED: Expected role 'superAdmin', got '${req.user.role}'`, 'AppUploadController');
      return res.status(403).json({
        success: false,
        message: `Access denied. Only SuperAdmin can upload apps. Your role: ${req.user.role}`,
        statusCode: 403
      });
    }

    const fileData = req.file;
    const metadata = req.body;

    if (!fileData) {
      return res.status(400).json({
        success: false,
        message: 'No file uploaded',
        statusCode: 400
      });
    }

    logInfo(`SuperAdmin ${req.user.userId} uploading app: ${metadata.version || 'auto'} for ${metadata.platform || 'android'}`, 'AppUploadController');

    const result = await AppUploadService.uploadApp(fileData, metadata, req.user);

    logSuccess(`App uploaded successfully: ${result.data.uploadId}`, 'AppUploadController');

    res.status(201).json({
      success: true,
      message: result.message,
      data: result.data,
      statusCode: 201
    });

  } catch (error) {
    logError(`Failed to upload app: ${error.message}`, 'AppUploadController', error);
    next(error);
  }
};

/**
 * Get all app uploads with filters
 * GET /api/app/list
 */
const getApps = async (req, res, next) => {
  try {
    // Check if user is SuperAdmin
    if (req.user.role !== 'superAdmin') {
      return res.status(403).json({
        success: false,
        message: 'Access denied. Only SuperAdmin can view app list.',
        statusCode: 403
      });
    }

    const filters = req.query;
    const result = await AppUploadService.getApps(filters);

    res.status(200).json({
      success: true,
      message: result.message,
      data: result.data,
      statusCode: 200
    });

  } catch (error) {
    logError(`Failed to get apps: ${error.message}`, 'AppUploadController', error);
    next(error);
  }
};

/**
 * Get app upload by ID
 * GET /api/app/:uploadId
 */
const getAppById = async (req, res, next) => {
  try {
    const { uploadId } = req.params;
    const result = await AppUploadService.getAppById(uploadId);

    res.status(200).json({
      success: true,
      message: result.message,
      data: result.data,
      statusCode: 200
    });

  } catch (error) {
    logError(`Failed to get app by ID: ${error.message}`, 'AppUploadController', error);
    next(error);
  }
};

/**
 * Get latest app by platform
 * GET /api/app/latest/:platform
 */
const getLatestByPlatform = async (req, res, next) => {
  try {
    const { platform } = req.params;
    const result = await AppUploadService.getLatestByPlatform(platform);

    res.status(200).json({
      success: true,
      message: result.message,
      data: result.data,
      statusCode: 200
    });

  } catch (error) {
    logError(`Failed to get latest app: ${error.message}`, 'AppUploadController', error);
    next(error);
  }
};

/**
 * Download app file
 * GET /api/app/download/:uploadId
 */
const downloadApp = async (req, res, next) => {
  try {
    const { uploadId } = req.params;
    const result = await AppUploadService.downloadApp(uploadId);

    // Set headers for file download
    res.setHeader('Content-Disposition', `attachment; filename="${result.data.fileName}"`);
    res.setHeader('Content-Type', result.data.mimeType);
    res.setHeader('Content-Length', result.data.fileSize);

    // Stream the file
    const fileStream = require('fs').createReadStream(result.data.filePath);
    fileStream.pipe(res);

    fileStream.on('error', (error) => {
      logError(`Error streaming file: ${error.message}`, 'AppUploadController', error);
      if (!res.headersSent) {
        res.status(500).json({
          success: false,
          message: 'Error downloading file',
          statusCode: 500
        });
      }
    });

  } catch (error) {
    logError(`Failed to download app: ${error.message}`, 'AppUploadController', error);
    next(error);
  }
};

/**
 * Download latest app (public endpoint)
 * GET /api/app/download/latest
 */
const downloadLatest = async (req, res, next) => {
  try {
    // Default to Android if no platform specified
    const platform = req.query.platform || 'android';
    
    const result = await AppUploadService.getLatestByPlatform(platform);
    const downloadResult = await AppUploadService.downloadApp(result.data.uploadId);

    // Set headers for file download
    res.setHeader('Content-Disposition', `attachment; filename="${downloadResult.data.fileName}"`);
    res.setHeader('Content-Type', downloadResult.data.mimeType);
    res.setHeader('Content-Length', downloadResult.data.fileSize);

    // Stream the file
    const fileStream = require('fs').createReadStream(downloadResult.data.filePath);
    fileStream.pipe(res);

    fileStream.on('error', (error) => {
      logError(`Error streaming latest app: ${error.message}`, 'AppUploadController', error);
      if (!res.headersSent) {
        res.status(500).json({
          success: false,
          message: 'Error downloading latest app',
          statusCode: 500
        });
      }
    });

  } catch (error) {
    logError(`Failed to download latest app: ${error.message}`, 'AppUploadController', error);
    next(error);
  }
};

/**
 * Update app metadata
 * PUT /api/app/:uploadId
 */
const updateApp = async (req, res, next) => {
  try {
    // Check if user is SuperAdmin
    if (req.user.role !== 'superadmin') {
      return res.status(403).json({
        success: false,
        message: 'Access denied. Only SuperAdmin can update apps.',
        statusCode: 403
      });
    }

    const { uploadId } = req.params;
    const updateData = req.body;

    const result = await AppUploadService.updateApp(uploadId, updateData);

    logSuccess(`App updated: ${uploadId}`, 'AppUploadController');

    res.status(200).json({
      success: true,
      message: result.message,
      data: result.data,
      statusCode: 200
    });

  } catch (error) {
    logError(`Failed to update app: ${error.message}`, 'AppUploadController', error);
    next(error);
  }
};

/**
 * Delete app upload
 * DELETE /api/app/:uploadId
 */
const deleteApp = async (req, res, next) => {
  try {
    // Check if user is SuperAdmin
    if (req.user.role !== 'superAdmin') {
      return res.status(403).json({
        success: false,
        message: 'Access denied. Only SuperAdmin can delete apps.',
        statusCode: 403
      });
    }

    const { uploadId } = req.params;
    const result = await AppUploadService.deleteApp(uploadId);

    logSuccess(`App deleted: ${uploadId} by ${req.user.userId}`, 'AppUploadController');

    res.status(200).json({
      success: true,
      message: result.message,
      statusCode: 200
    });

  } catch (error) {
    logError(`Failed to delete app: ${error.message}`, 'AppUploadController', error);
    next(error);
  }
};

/**
 * Set app as latest version
 * PUT /api/app/:uploadId/set-latest
 */
const setLatestVersion = async (req, res, next) => {
  try {
    // Check if user is SuperAdmin
    if (req.user.role !== 'superadmin') {
      return res.status(403).json({
        success: false,
        message: 'Access denied. Only SuperAdmin can set latest version.',
        statusCode: 403
      });
    }

    const { uploadId } = req.params;
    const { platform } = req.body;

    await AppUploadService.updateApp(uploadId, { isLatest: true });

    logSuccess(`Set as latest version: ${uploadId}`, 'AppUploadController');

    res.status(200).json({
      success: true,
      message: 'Latest version updated successfully',
      statusCode: 200
    });

  } catch (error) {
    logError(`Failed to set latest version: ${error.message}`, 'AppUploadController', error);
    next(error);
  }
};

/**
 * Get download statistics
 * GET /api/app/stats
 */
const getStats = async (req, res, next) => {
  try {
    // Check if user is SuperAdmin
    if (req.user.role !== 'superadmin') {
      return res.status(403).json({
        success: false,
        message: 'Access denied. Only SuperAdmin can view statistics.',
        statusCode: 403
      });
    }

    const filters = req.query;
    const result = await AppUploadService.getStats(filters);

    res.status(200).json({
      success: true,
      message: result.message,
      data: result.data,
      statusCode: 200
    });

  } catch (error) {
    logError(`Failed to get stats: ${error.message}`, 'AppUploadController', error);
    next(error);
  }
};

module.exports = {
  upload,
  uploadApp,
  getApps,
  getAppById,
  getLatestByPlatform,
  downloadApp,
  downloadLatest,
  updateApp,
  deleteApp,
  setLatestVersion,
  getStats
}; 