# Documentation: Fetching and Displaying Subscription Plans

This document outlines the end-to-end process for fetching subscription plans from the DeynCare backend and rendering them on a client application (e.g., mobile or web).

---

## 1. Core Logic: Rendering Subscription Plans

The user flow for selecting a subscription plan is as follows:

1.  **Fetch Subscription Plans**: When the user navigates to the subscription screen, the app requests available plans from the backend.
2.  **Display Plans Dynamically**: Each plan received from the backend is displayed as a selectable card, showing its name, description, price, and features.
3.  **Plan Selection**: When a user taps on a plan, the app stores the selected `planId` for use in the subscription process.
4.  **Show Plan Details**: An optional "Details" button can reveal a modal with a full list of features and usage limits.

---

## 2. Backend API Endpoint

The backend provides a public endpoint to fetch all active subscription plans. This endpoint is designed for client applications and does not require authentication.

-   **Endpoint**: `GET /api/public/plans`
-   **Method**: `GET`
-   **Description**: Returns a list of all active subscription plans, sorted by display order.
-   **Success Response (200 OK)**:
    ```json
    {
      "success": true,
      "data": [
        {
          "planId": "PLAN123",
          "name": "premium_monthly",
          "type": "monthly",
          "displayName": "Premium Monthly",
          "description": "Full access to all features, billed monthly.",
          "pricing": {
            "basePrice": 29.99,
            "currency": "USD",
            "billingCycle": "monthly"
          },
          "features": { ... },
          "limits": { ... },
          "isActive": true,
          "displayOrder": 2,
          "metadata": { ... }
        }
      ]
    }
    ```

---

## 3. Frontend (Client) Implementation

The client application will interact with the public endpoint to fetch and display the plans.

### **1. API Service Layer (Fetching Plans)**

Create a function to call the endpoint.

```dart
// Example in Dart (Flutter)
Future<List<Plan>> fetchSubscriptionPlans() async {
  final response = await http.get(Uri.parse('https://your-api.com/api/public/plans'));

  if (response.statusCode == 200) {
    final jsonResponse = json.decode(response.body);
    final List<dynamic> planData = jsonResponse['data'];
    return planData.map((json) => Plan.fromJson(json)).toList();
  } else {
    throw Exception('Failed to load plans');
  }
}
```

### **2. State Management**

Use a state management solution (like Provider, BLoC, or Riverpod) to:
-   Store the list of fetched plans.
-   Track the currently selected `planId`.
-   Manage loading and error states.

### **3. UI: Displaying Plans**

-   Use a `FutureBuilder` or equivalent to handle the async call.
-   Map the list of `Plan` objects to a list of selectable UI widgets (e.g., `PlanCard`).
-   Visually distinguish the selected plan by changing its border color or showing an icon.

### **4. UI: Plan Selection**

-   When a user taps a `PlanCard`, update the state with the selected `plan.planId`.
-   The "Continue" or "Next" button should become active only after a plan is selected.

This documentation provides a complete roadmap for implementing the plan selection feature securely and efficiently.
