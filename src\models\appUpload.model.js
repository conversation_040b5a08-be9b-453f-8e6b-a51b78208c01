const mongoose = require('mongoose');
const mongoosePaginate = require('mongoose-paginate-v2');

const appUploadSchema = new mongoose.Schema({
  uploadId: {
    type: String,
    required: true,
    unique: true,
    trim: true
  },
  appName: {
    type: String,
    required: true,
    trim: true,
    default: 'DeynCare Mobile App'
  },
  version: {
    type: String,
    required: true,
    trim: true
  },
  buildNumber: {
    type: Number,
    required: true
  },
  fileName: {
    type: String,
    required: true,
    trim: true
  },
  originalFileName: {
    type: String,
    required: true,
    trim: true
  },
  filePath: {
    type: String,
    required: true,
    trim: true
  },
  fileSize: {
    type: Number,
    required: true
  },
  fileType: {
    type: String,
    required: true,
    enum: ['apk', 'ipa', 'exe', 'dmg', 'zip'],
    trim: true
  },
  platform: {
    type: String,
    required: true,
    enum: ['android', 'ios', 'windows', 'mac', 'web'],
    trim: true
  },
  isLatest: {
    type: Boolean,
    default: false
  },
  isActive: {
    type: Boolean,
    default: true
  },
  downloadCount: {
    type: Number,
    default: 0
  },
  releaseNotes: {
    type: String,
    trim: true
  },
  uploadedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  uploadedByName: {
    type: String,
    required: true,
    trim: true
  },
  metadata: {
    checksum: String,
    mimeType: String,
    encoding: String
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes
appUploadSchema.index({ platform: 1, isLatest: 1 });
appUploadSchema.index({ version: 1, buildNumber: 1 });
appUploadSchema.index({ uploadedBy: 1 });
appUploadSchema.index({ createdAt: -1 });

// Virtual for file URL
appUploadSchema.virtual('downloadUrl').get(function() {
  return `/api/app/download/${this.uploadId}`;
});

// Virtual for formatted file size
appUploadSchema.virtual('formattedFileSize').get(function() {
  const bytes = this.fileSize;
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
});

// Static method to set latest version
appUploadSchema.statics.setLatestVersion = async function(uploadId, platform) {
  // Remove isLatest flag from all other versions of the same platform
  await this.updateMany(
    { platform, isLatest: true },
    { $set: { isLatest: false } }
  );
  
  // Set the new version as latest
  return await this.findOneAndUpdate(
    { uploadId },
    { $set: { isLatest: true } },
    { new: true }
  );
};

// Static method to get latest version by platform
appUploadSchema.statics.getLatestByPlatform = async function(platform) {
  return await this.findOne({
    platform,
    isLatest: true,
    isActive: true
  }).populate('uploadedBy', 'fullName email');
};

// Static method to increment download count
appUploadSchema.statics.incrementDownloadCount = async function(uploadId) {
  return await this.findOneAndUpdate(
    { uploadId, isActive: true },
    { $inc: { downloadCount: 1 } },
    { new: true }
  );
};

// Plugins
appUploadSchema.plugin(mongoosePaginate);

const AppUpload = mongoose.model('AppUpload', appUploadSchema);

module.exports = AppUpload; 