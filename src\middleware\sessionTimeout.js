/**
 * Session Timeout Middleware
 * 
 * @module sessionTimeout
 * @description Handles session timeout and auto-logout based on security settings
 * @version 1.0.0
 * @since 2025-06-03
 */

const { Setting } = require('../models');

/**
 * Get session timeout setting from database
 * Fallback to default if not found
 * @returns {Number} Timeout in minutes
 */
async function getSessionTimeout() {
  try {
    const setting = await Setting.findOne({ 
      category: 'security',
      key: 'session.timeout'
    });
    
    return setting ? setting.value : 30; // Default: 30 minutes
  } catch (error) {
    console.error('Error loading session timeout setting:', error);
    return 30; // Default: 30 minutes if error
  }
}

/**
 * Middleware to handle session timeout
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware
 */
async function sessionTimeoutMiddleware(req, res, next) {
  try {
    // Skip for non-authenticated routes
    if (!req.user) {
      return next();
    }
    
    // Get current session timeout setting
    const timeoutMinutes = await getSessionTimeout();
    const timeoutMs = timeoutMinutes * 60 * 1000;
    
    // Check if last activity timestamp exists
    const lastActivity = req.session.lastActivity;
    const now = Date.now();
    
    if (lastActivity && (now - lastActivity > timeoutMs)) {
      // Session has expired - clear session
      req.session.destroy(err => {
        if (err) {
          console.error('Error destroying expired session:', err);
        }
        
        // Return 401 Unauthorized with expired session message
        return res.status(401).json({
          success: false,
          error: {
            code: 'session_expired',
            message: 'Your session has expired due to inactivity. Please log in again.'
          }
        });
      });
      return;
    }
    
    // Update last activity timestamp
    req.session.lastActivity = now;
    
    // Set session expiry header to inform client of remaining time
    if (lastActivity && timeoutMs) {
      const expiresIn = Math.max(0, timeoutMs - (now - lastActivity));
      res.set('X-Session-Expires-In', expiresIn.toString());
    }
    
    next();
  } catch (error) {
    console.error('Session timeout middleware error:', error);
    next(); // Continue even if there's an error with timeout logic
  }
}

module.exports = {
  sessionTimeoutMiddleware,
  getSessionTimeout
};
