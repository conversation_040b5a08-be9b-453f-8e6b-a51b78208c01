/**
 * Bootstrap script for DeynCare Backend
 * This script runs on server startup to perform initialization tasks like:
 * - Checking for and creating a super-admin user if needed
 * - Initializing default settings
 * - Running database migrations
 */

const { logInfo, logSuccess, logError, logWarning, timer } = require('../utils/logger');
const { createSuperAdmin } = require('../scripts/createSuperAdmin');
const { SettingsHelper } = require('../utils');

/**
 * Execute a task with timeout to prevent blocking
 * @param {Function} task - Async task to execute
 * @param {string} taskName - Name of the task for logging
 * @param {number} timeoutMs - Timeout in milliseconds
 * @returns {Promise} Task result or timeout error
 */
const executeWithTimeout = async (task, taskName, timeoutMs = 10000) => {
  const timeoutPromise = new Promise((_, reject) => {
    setTimeout(() => reject(new Error(`${taskName} timed out after ${timeoutMs}ms`)), timeoutMs);
  });
  
  try {
    return await Promise.race([task(), timeoutPromise]);
  } catch (error) {
    logWarning(`${taskName} failed: ${error.message}`, 'Bootstrap');
    throw error;
  }
};

/**
 * Initialize critical startup tasks in parallel
 * @returns {Promise<Object>} Results of critical tasks
 */
const initializeCriticalTasks = async () => {
  const tasks = [];
  
  // Task 1: SuperAdmin creation (if enabled)
  if (process.env.CREATE_SUPER_ADMIN === 'true') {
    tasks.push(
      executeWithTimeout(createSuperAdmin, 'SuperAdmin Creation', 5000)
        .then(result => ({ type: 'superAdmin', success: true, result }))
        .catch(error => ({ type: 'superAdmin', success: false, error: error.message }))
    );
  }
  
  // Task 2: System settings initialization (optimized)
  tasks.push(
    executeWithTimeout(
      () => SettingsHelper.initializeSystemSettingsOptimized(),
      'System Settings Initialization',
      8000
    )
      .then(result => ({ type: 'settings', success: true, result }))
      .catch(error => ({ type: 'settings', success: false, error: error.message }))
  );
  
  // Execute all critical tasks in parallel
  const results = await Promise.allSettled(tasks);
  
  // Process results
  const processedResults = {};
  results.forEach((result, index) => {
    if (result.status === 'fulfilled') {
      const taskResult = result.value;
      processedResults[taskResult.type] = taskResult;
      
      if (taskResult.success) {
        logSuccess(`${taskResult.type} completed successfully`, 'Bootstrap');
      } else {
        logWarning(`${taskResult.type} failed: ${taskResult.error}`, 'Bootstrap');
      }
    } else {
      logWarning(`Critical task ${index} failed: ${result.reason?.message}`, 'Bootstrap');
    }
  });
  
  return processedResults;
};

/**
 * Initialize non-critical background tasks
 * @returns {Promise<void>}
 */
const initializeBackgroundTasks = async () => {
  // Lazy-load heavy dependencies only when needed
  const loadDependency = (modulePath) => {
    try {
      return require(modulePath);
    } catch (error) {
      logWarning(`Failed to load optional dependency ${modulePath}: ${error.message}`, 'Bootstrap');
      return null;
    }
  };
  
  const backgroundTasks = [];
  
  // Task 1: Plan consistency check (non-blocking)
  backgroundTasks.push(
    (async () => {
      try {
        const ensurePlanConsistency = loadDependency('../utils/ensurePlanConsistency');
        if (ensurePlanConsistency) {
          const planTimer = timer.start('Plan Consistency Check');
          const planConsistencyResult = await executeWithTimeout(
            () => ensurePlanConsistency({
              autoMigrate: process.env.AUTO_MIGRATE_PLANS === 'true'
            }),
            'Plan Consistency Check',
            10000
          );
          planTimer();
          
          if (planConsistencyResult.error) {
            logWarning(`Plan consistency issues: ${planConsistencyResult.error}`, 'Bootstrap');
          } else {
            const { plans, subscriptions, migrationRun } = planConsistencyResult;
            logInfo(`Found ${plans} plans and ${subscriptions.total} subscriptions`, 'Bootstrap');
            
            if (migrationRun) {
              logSuccess('Plan migration completed', 'Bootstrap');
            } else if (subscriptions.withoutPlanId > 0) {
              logWarning(`${subscriptions.withoutPlanId} subscriptions need planId migration`, 'Bootstrap');
            }
          }
        }
      } catch (error) {
        logWarning(`Plan consistency check skipped: ${error.message}`, 'Bootstrap');
      }
    })()
  );
  
  // Task 2: ML Risk Evaluation Cron Job
  if (process.env.ML_EVALUATION_ENABLED !== 'false') {
    backgroundTasks.push(
      (async () => {
        try {
          const mlRiskEvaluationCron = loadDependency('../cron/mlRiskEvaluation');
          if (mlRiskEvaluationCron) {
            const mlTimer = timer.start('ML Risk Evaluation');
            mlRiskEvaluationCron.start();
            mlTimer();
            logSuccess('ML Risk Evaluation Cron Job started', 'Bootstrap');
          }
        } catch (error) {
          logWarning(`ML Risk Evaluation failed to start: ${error.message}`, 'Bootstrap');
        }
      })()
    );
  } else {
    logInfo('ML evaluation disabled', 'Bootstrap');
  }
  
  // Task 3: Debt Reminder Cron Job
  if (process.env.DEBT_REMINDERS_ENABLED !== 'false') {
    backgroundTasks.push(
      (async () => {
        try {
          const DebtReminderCron = loadDependency('../cron/debtReminderCron');
          if (DebtReminderCron) {
            const debtReminderTimer = timer.start('Debt Reminder Cron');
            DebtReminderCron.start();
            debtReminderTimer();
            logSuccess('Debt Reminder Cron Job started (daily at 9:00 AM)', 'Bootstrap');
          }
        } catch (error) {
          logWarning(`Debt Reminder Cron failed to start: ${error.message}`, 'Bootstrap');
        }
      })()
    );
  } else {
    logInfo('Debt reminders disabled', 'Bootstrap');
  }
  
  // Execute all background tasks in parallel (fire and forget)
  Promise.allSettled(backgroundTasks).then(() => {
    logSuccess('Background initialization tasks completed', 'Bootstrap');
  }).catch(error => {
    logWarning(`Some background tasks failed: ${error.message}`, 'Bootstrap');
  });
};

/**
 * Main bootstrap function (optimized)
 * @param {Object} options - Bootstrap options
 */
const bootstrap = async (options = {}) => {
  const bootstrapTimer = timer.start('Bootstrap Process');
  
  try {
    logInfo('🚀 Starting DeynCare bootstrap process (optimized)...', 'Bootstrap');
    
    // Phase 1: Critical initialization tasks (blocking)
    const criticalResults = await initializeCriticalTasks();
    
    // Phase 2: Non-critical background tasks (non-blocking)
    // Don't await this - let it run in background
    setImmediate(() => {
      initializeBackgroundTasks();
    });
    
    const totalTime = bootstrapTimer();
    logSuccess(`Bootstrap critical phase completed in ${totalTime.toFixed(2)}ms`, 'Bootstrap');
    logInfo('Background tasks running asynchronously...', 'Bootstrap');
    
    return {
      success: true,
      criticalResults,
      totalTime
    };
  } catch (error) {
    bootstrapTimer();
    logError(`Bootstrap failed: ${error.message}`, 'Bootstrap', error);
    
    // Don't exit on bootstrap failure - let server continue
    return {
      success: false,
      error: error.message
    };
  }
};

module.exports = { bootstrap };
