# 🔐 SuperAdmin Subscription API Documentation

## Base URL
```
http://localhost:5000/api
```

## Authentication
All endpoints require JWT token in Authorization header:
```
Authorization: Bearer <your_jwt_token>
```

## SuperAdmin Role Required
All endpoints below require `superAdmin` role access.

---

## 📊 **1. Get Subscription Statistics**

### Endpoint
```http
GET /subscriptions/stats
```

### Description
Get system-wide subscription statistics and analytics.

### Headers
```
Authorization: Bearer <superAdmin_jwt_token>
Content-Type: application/json
```

### Response Example
```json
{
  "success": true,
  "message": "Subscription statistics retrieved successfully",
  "data": {
    "totalSubscriptions": 150,
    "activeSubscriptions": 120,
    "trialSubscriptions": 25,
    "expiredSubscriptions": 5,
    "revenue": {
      "monthly": 1200,
      "yearly": 960,
      "total": 2160
    },
    "planDistribution": {
      "trial": 25,
      "monthly": 80,
      "yearly": 45
    },
    "conversionRate": 85.5,
    "churnRate": 3.2
  }
}
```

---

## 📋 **2. Get All Subscriptions**

### Endpoint
```http
GET /subscriptions
```

### Description
Retrieve all subscriptions with filtering, sorting, and pagination.

### Query Parameters
| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `page` | number | 1 | Page number |
| `limit` | number | 10 | Items per page |
| `status` | string | - | Filter by status (trial, active, expired, canceled) |
| `planType` | string | - | Filter by plan type (trial, monthly, yearly) |
| `sortBy` | string | createdAt | Sort field |
| `sortOrder` | string | desc | Sort order (asc, desc) |

### Example Requests

#### Get All Subscriptions (Default)
```http
GET /subscriptions
```

#### Get Active Subscriptions Only
```http
GET /subscriptions?status=active
```

#### Get Monthly Plans with Pagination
```http
GET /subscriptions?planType=monthly&page=2&limit=20
```

#### Sort by End Date Ascending
```http
GET /subscriptions?sortBy=dates.endDate&sortOrder=asc
```

### Response Example
```json
{
  "success": true,
  "message": "Subscriptions retrieved successfully",
  "data": {
    "subscriptions": [
      {
        "_id": "64f8a1b2c3d4e5f6a7b8c9d0",
        "subscriptionId": "SUB001",
        "shopId": "SHOP001",
        "plan": {
          "name": "Monthly Pro",
          "type": "monthly",
          "features": {
            "debtTracking": true,
            "customerPayments": true,
            "smsReminders": true
          }
        },
        "pricing": {
          "basePrice": 10,
          "currency": "USD"
        },
        "status": "active",
        "payment": {
          "method": "offline",
          "verified": true,
          "lastPaymentDate": "2024-01-15T10:30:00.000Z"
        },
        "dates": {
          "startDate": "2024-01-01T00:00:00.000Z",
          "endDate": "2024-01-31T23:59:59.000Z"
        },
        "createdAt": "2024-01-01T00:00:00.000Z",
        "updatedAt": "2024-01-15T10:30:00.000Z"
      }
    ],
    "pagination": {
      "currentPage": 1,
      "totalPages": 15,
      "totalItems": 150,
      "itemsPerPage": 10,
      "hasNextPage": true,
      "hasPrevPage": false
    },
    "summary": {
      "total": 150,
      "active": 120,
      "trial": 25,
      "expired": 5,
      "canceled": 0
    }
  }
}
```

---

## ✅ **3. Verify Offline Payment**

### Endpoint
```http
POST /subscriptions/verify-payment/:paymentId
```

### Description
Verify and approve offline payment submissions from shop owners.

### Path Parameters
| Parameter | Type | Description |
|-----------|------|-------------|
| `paymentId` | string | ID of the payment to verify |

### Request Body
```json
{
  "verified": true,
  "notes": "Payment verified successfully. Bank transfer confirmed.",
  "verifiedBy": "superAdmin",
  "verificationDate": "2024-01-15T10:30:00.000Z"
}
```

### Response Example
```json
{
  "success": true,
  "message": "Payment verified successfully",
  "data": {
    "paymentId": "PAY001",
    "subscriptionId": "SUB001",
    "status": "verified",
    "verifiedBy": "superAdmin",
    "verificationDate": "2024-01-15T10:30:00.000Z",
    "subscription": {
      "status": "active",
      "payment": {
        "verified": true
      }
    }
  }
}
```

---

## 🔄 **4. Bulk Operations**

### Endpoint
```http
POST /subscriptions/bulk
```

### Description
Perform bulk operations on multiple subscriptions.

### Request Body Examples

#### Bulk Status Update
```json
{
  "operation": "updateStatus",
  "subscriptionIds": ["SUB001", "SUB002", "SUB003"],
  "data": {
    "status": "active",
    "reason": "Payment verified"
  }
}
```

#### Bulk Plan Change
```json
{
  "operation": "changePlan",
  "subscriptionIds": ["SUB001", "SUB002"],
  "data": {
    "planType": "yearly",
    "reason": "Promotional upgrade"
  }
}
```

#### Bulk Extension
```json
{
  "operation": "extend",
  "subscriptionIds": ["SUB001", "SUB002"],
  "data": {
    "days": 30,
    "reason": "Compensation for service downtime"
  }
}
```

### Response Example
```json
{
  "success": true,
  "message": "Bulk operation completed successfully",
  "data": {
    "operation": "updateStatus",
    "processed": 3,
    "successful": 3,
    "failed": 0,
    "results": [
      {
        "subscriptionId": "SUB001",
        "status": "success",
        "message": "Status updated successfully"
      },
      {
        "subscriptionId": "SUB002",
        "status": "success",
        "message": "Status updated successfully"
      },
      {
        "subscriptionId": "SUB003",
        "status": "success",
        "message": "Status updated successfully"
      }
    ]
  }
}
```

---

## 🔧 **5. Manual Cron Execution**

### Endpoint
```http
POST /subscriptions/cron/run
```

### Description
Manually trigger subscription cron jobs (renewal checks, expiration handling, etc.).

### Request Body
```json
{
  "tasks": ["renewal", "expiration", "payment_retry"],
  "force": true
}
```

### Response Example
```json
{
  "success": true,
  "message": "Cron tasks executed successfully",
  "data": {
    "tasksRun": ["renewal", "expiration", "payment_retry"],
    "results": {
      "renewal": {
        "processed": 15,
        "renewed": 12,
        "failed": 3
      },
      "expiration": {
        "processed": 8,
        "expired": 5,
        "extended": 3
      },
      "payment_retry": {
        "processed": 10,
        "successful": 7,
        "failed": 3
      }
    },
    "executedAt": "2024-01-15T10:30:00.000Z"
  }
}
```

---

## 🔄 **6. Payment Retry Management**

### Get Retry Status
```http
GET /subscriptions/payment-retry/:subscriptionId/status
```

### Trigger Manual Retry
```http
POST /subscriptions/payment-retry/:subscriptionId/trigger
```

### Cancel Scheduled Retries
```http
POST /subscriptions/payment-retry/:subscriptionId/cancel
```

### Process All Pending Retries
```http
POST /subscriptions/payment-retry/process-all
```

### Get Retry Configuration
```http
GET /subscriptions/payment-retry/config
```

---

## 📤 **7. Data Export**

### Export Subscriptions to CSV
```http
GET /export/subscriptions/csv
```

### Export Subscriptions to Excel
```http
GET /export/subscriptions/excel
```

### Query Parameters for Export
| Parameter | Type | Description |
|-----------|------|-------------|
| `status` | string | Filter by status |
| `planType` | string | Filter by plan type |
| `startDate` | string | Filter by start date (YYYY-MM-DD) |
| `endDate` | string | Filter by end date (YYYY-MM-DD) |

### Example Export Request
```http
GET /export/subscriptions/csv?status=active&planType=monthly&startDate=2024-01-01&endDate=2024-01-31
```

---

## 🛠️ **8. Create Subscription (SuperAdmin)**

### Endpoint
```http
POST /subscriptions
```

### Description
Create a new subscription for any shop (SuperAdmin privilege).

### Request Body
```json
{
  "shopId": "SHOP001",
  "planId": "PLAN001",
  "dates": {
    "startDate": "2024-01-01T00:00:00.000Z",
    "endDate": "2024-01-31T23:59:59.000Z"
  },
  "payment": {
    "method": "offline",
    "amount": 10,
    "currency": "USD"
  },
  "notes": "Created by SuperAdmin for promotional campaign",
  "createdBy": "superAdmin"
}
```

### Response Example
```json
{
  "success": true,
  "message": "Subscription created successfully",
  "data": {
    "subscriptionId": "SUB001",
    "shopId": "SHOP001",
    "status": "active",
    "plan": {
      "name": "Monthly Pro",
      "type": "monthly"
    },
    "dates": {
      "startDate": "2024-01-01T00:00:00.000Z",
      "endDate": "2024-01-31T23:59:59.000Z"
    },
    "createdAt": "2024-01-01T00:00:00.000Z"
  }
}
```

---

## 📝 **9. Extend Subscription**

### Endpoint
```http
POST /subscriptions/:subscriptionId/extend
```

### Description
Extend a subscription period (SuperAdmin can extend any subscription).

### Path Parameters
| Parameter | Type | Description |
|-----------|------|-------------|
| `subscriptionId` | string | ID of subscription to extend |

### Request Body
```json
{
  "days": 30,
  "reason": "Compensation for service downtime",
  "extendedBy": "superAdmin"
}
```

### Response Example
```json
{
  "success": true,
  "message": "Subscription extended successfully",
  "data": {
    "subscriptionId": "SUB001",
    "originalEndDate": "2024-01-31T23:59:59.000Z",
    "newEndDate": "2024-03-01T23:59:59.000Z",
    "daysExtended": 30,
    "reason": "Compensation for service downtime",
    "extendedBy": "superAdmin",
    "extendedAt": "2024-01-15T10:30:00.000Z"
  }
}
```

---

## 🚨 **Error Responses**

### 401 Unauthorized
```json
{
  "success": false,
  "message": "Authentication required",
  "error": {
    "code": "auth_required",
    "statusCode": 401
  }
}
```

### 403 Forbidden
```json
{
  "success": false,
  "message": "Role superAdmin not authorized to access this route",
  "error": {
    "code": "permission_denied",
    "statusCode": 403
  }
}
```

### 404 Not Found
```json
{
  "success": false,
  "message": "Subscription not found",
  "error": {
    "code": "subscription_not_found",
    "statusCode": 404
  }
}
```

### 500 Internal Server Error
```json
{
  "success": false,
  "message": "Internal server error",
  "error": {
    "code": "server_error",
    "statusCode": 500
  }
}
```

---

## 🧪 **Postman Collection Setup**

### Environment Variables
Create a Postman environment with these variables:

```json
{
  "base_url": "http://localhost:5000/api",
  "superadmin_token": "your_superadmin_jwt_token_here",
  "subscription_id": "SUB001",
  "payment_id": "PAY001"
}
```

### Pre-request Script (for all requests)
```javascript
pm.request.headers.add({
    key: 'Authorization',
    value: 'Bearer ' + pm.environment.get('superadmin_token')
});
```

### Test Scripts Examples

#### For GET /subscriptions/stats
```javascript
pm.test("Status code is 200", function () {
    pm.response.to.have.status(200);
});

pm.test("Response has required fields", function () {
    const jsonData = pm.response.json();
    pm.expect(jsonData).to.have.property('success', true);
    pm.expect(jsonData.data).to.have.property('totalSubscriptions');
    pm.expect(jsonData.data).to.have.property('activeSubscriptions');
});
```

#### For GET /subscriptions
```javascript
pm.test("Status code is 200", function () {
    pm.response.to.have.status(200);
});

pm.test("Response has pagination", function () {
    const jsonData = pm.response.json();
    pm.expect(jsonData.data).to.have.property('pagination');
    pm.expect(jsonData.data.pagination).to.have.property('currentPage');
    pm.expect(jsonData.data.pagination).to.have.property('totalPages');
});

pm.test("Subscriptions array exists", function () {
    const jsonData = pm.response.json();
    pm.expect(jsonData.data.subscriptions).to.be.an('array');
});
```

---

## 📋 **Quick Test Checklist**

- [ ] **Authentication**: Test with valid SuperAdmin token
- [ ] **Get Stats**: Verify system statistics endpoint
- [ ] **List Subscriptions**: Test pagination and filtering
- [ ] **Verify Payment**: Test payment verification workflow
- [ ] **Bulk Operations**: Test bulk status updates
- [ ] **Manual Cron**: Test cron job execution
- [ ] **Export Data**: Test CSV/Excel export
- [ ] **Create Subscription**: Test subscription creation
- [ ] **Extend Subscription**: Test subscription extension
- [ ] **Error Handling**: Test with invalid tokens/data

---

## 🔗 **Related Documentation**

- [Admin/Shop Owner API Documentation](Admin_Subscription_API.md)
- [Employee API Documentation](Employee_Subscription_API.md)
- [Authentication Guide](Authentication_Guide.md)
- [Error Codes Reference](Error_Codes.md)

---

**Last Updated**: January 2024  
**API Version**: v1.0  
**Base URL**: `http://localhost:5000/api` 