/**
 * Export Service
 * Generic export service for all modules
 */
const { Parser } = require('json2csv');
const ExcelJS = require('exceljs');
const { logError } = require('../../utils');

class ExportService {
  /**
   * Convert data to CSV format
   * @param {Array} data - Array of objects to convert
   * @param {Array} fields - Array of field configurations
   * @param {Object} options - Additional options
   * @returns {String} CSV string
   */
  static async toCSV(data, fields, options = {}) {
    try {
      // Transform fields to json2csv format
      const csvFields = fields.map(field => ({
        label: field.label,
        value: field.key
      }));

      const parser = new Parser({ 
        fields: csvFields,
        ...options
      });
      return parser.parse(data);
    } catch (error) {
      logError('Failed to convert data to CSV', 'ExportService', error);
      throw error;
    }
  }

  /**
   * Convert data to Excel format
   * @param {Array} data - Array of objects to convert
   * @param {Array} fields - Array of field configurations
   * @param {Object} options - Additional options
   * @returns {Buffer} Excel file buffer
   */
  static async toExcel(data, fields, options = {}) {
    try {
      const workbook = new ExcelJS.Workbook();
      const worksheet = workbook.addWorksheet(options.sheetName || 'Data');

      // Add headers
      worksheet.columns = fields.map(field => ({
        header: field.label,
        key: field.key,
        width: field.width || 20
      }));

      // Add rows
      worksheet.addRows(data);

      // Apply styling if provided
      if (options.styling) {
        this.applyExcelStyling(worksheet, options.styling);
      }

      return await workbook.xlsx.writeBuffer();
    } catch (error) {
      logError('Failed to convert data to Excel', 'ExportService', error);
      throw error;
    }
  }

  /**
   * Apply styling to Excel worksheet
   * @private
   */
  static applyExcelStyling(worksheet, styling) {
    // Header styling
    if (styling.header) {
      worksheet.getRow(1).font = { bold: true };
      worksheet.getRow(1).fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'FFE0E0E0' }
      };
    }

    // Column styling
    if (styling.columns) {
      Object.entries(styling.columns).forEach(([key, style]) => {
        const column = worksheet.getColumn(key);
        if (style.width) column.width = style.width;
        if (style.alignment) column.alignment = style.alignment;
      });
    }
  }

  /**
   * Convert snake_case to human readable title case
   * @private
   */
  static toTitleCase(str) {
    return str
      .replace(/_/g, ' ')
      .replace(/\b\w/g, letter => letter.toUpperCase());
  }

  /**
   * Generate export filename
   * @param {String} module - Module name
   * @param {String} format - File format
   * @param {String} customName - Custom filename
   * @returns {String} Formatted filename
   */
  static generateFilename(module, format, customName = '') {
    const timestamp = new Date().toISOString().split('T')[0];
    
    if (customName) {
      // If custom name is provided, use it as is
      return `${customName}_${timestamp}.${format}`;
    }
    
    // Convert module name to human readable format
    const humanReadableName = this.toTitleCase(module);
    return `${humanReadableName} Export ${timestamp}.${format}`;
  }

  /**
   * Process data before export
   * @param {Array} data - Raw data
   * @param {Array} fields - Field configurations
   * @returns {Array} Processed data
   */
  static processData(data, fields) {
    // Handle undefined or null data
    if (!data) {
      logError('Data is undefined or null in processData', 'ExportService');
      return [];
    }

    // Ensure data is an array
    if (!Array.isArray(data)) {
      logError('Data is not an array in processData', 'ExportService');
      return [];
    }

    // Handle empty fields array
    if (!fields || !Array.isArray(fields) || fields.length === 0) {
      logError('Fields array is undefined, null, or empty in processData', 'ExportService');
      return data;
    }

    return data.map(item => {
      const processed = {};
      fields.forEach(field => {
        const value = this.getNestedValue(item, field.key);
        processed[field.key] = this.formatValue(value, field);
      });
      return processed;
    });
  }

  /**
   * Get nested object value
   * @private
   */
  static getNestedValue(obj, path) {
    return path.split('.').reduce((current, key) => 
      current && current[key] !== undefined ? current[key] : null, obj);
  }

  /**
   * Format value based on field configuration
   * @private
   */
  static formatValue(value, field) {
    if (value === null || value === undefined) return '';

    switch (field.type) {
      case 'date':
        return new Date(value).toLocaleDateString();
      case 'datetime':
        return new Date(value).toLocaleString();
      case 'currency':
        return new Intl.NumberFormat('en-US', {
          style: 'currency',
          currency: field.currency || 'USD'
        }).format(value);
      case 'number':
        return new Intl.NumberFormat().format(value);
      case 'boolean':
        return value ? 'Yes' : 'No';
      default:
        return value;
    }
  }
}

module.exports = ExportService; 