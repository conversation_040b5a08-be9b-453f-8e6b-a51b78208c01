"use client";

import { 
  Users, 
  Store, 
  CreditCard, 
  Brain, 
  Bell, 
  BarChart3, 
  Settings, 
  FileText,
  Shield,
  Smartphone,
  TrendingUp,
  UserCheck,
  ArrowRight
} from "lucide-react";
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from "../ui/card";
import { Badge } from "../ui/badge";
import { Button } from "../ui/button";
import Link from "next/link";

const features = [
  {
    category: "Core Management",
    icon: Users,
    color: "bg-blue-500",
    features: [
      {
        icon: Users,
        title: "Multi-Role User Management",
        description: "Complete user lifecycle management with SuperAdmin, Admin, and Employee roles. Advanced permission controls and user analytics.",
        highlights: ["Role-based permissions", "User activity tracking", "Bulk operations"]
      },
      {
        icon: Store,
        title: "Shop Management System",
        description: "Comprehensive shop onboarding, status management, and performance analytics. From registration to enterprise operations.",
        highlights: ["Shop verification", "Status monitoring", "Performance metrics"]
      },
      {
        icon: CreditCard,
        title: "Subscription Management",
        description: "Automated subscription lifecycle with flexible plans, payment retries, and renewal management. EVC Plus integration included.",
        highlights: ["Payment automation", "Plan flexibility", "Retry mechanisms"]
      }
    ]
  },
  {
    category: "Advanced Analytics",
    icon: BarChart3,
    color: "bg-purple-500",
    features: [
      {
        icon: Brain,
        title: "AI Risk Assessment",
        description: "ML-powered risk scoring using FairRisk algorithm. Predicts debt collection success and customer payment behavior.",
        highlights: ["Real-time scoring", "Transparent algorithms", "Predictive insights"]
      },
      {
        icon: BarChart3,
        title: "Real-time Analytics",
        description: "Comprehensive business intelligence with customizable dashboards, KPI tracking, and automated reporting.",
        highlights: ["Live dashboards", "Custom metrics", "Export capabilities"]
      },
      {
        icon: TrendingUp,
        title: "Performance Insights",
        description: "Deep business analytics including revenue tracking, customer behavior analysis, and predictive forecasting.",
        highlights: ["Revenue analytics", "Customer insights", "Trend analysis"]
      }
    ]
  },
  {
    category: "Communication & Automation",
    icon: Bell,
    color: "bg-green-500",
    features: [
      {
        icon: Bell,
        title: "Smart Notifications",
        description: "FCM push notifications with intelligent targeting. Automated debt reminders and business alerts.",
        highlights: ["Push notifications", "Smart targeting", "Automation rules"]
      },
      {
        icon: Smartphone,
        title: "Multi-Channel Payments",
        description: "EVC Plus integration, offline payment tracking, and payment verification with proof management.",
        highlights: ["EVC Plus integration", "Offline payments", "Payment verification"]
      },
      {
        icon: FileText,
        title: "Report Generation",
        description: "Automated report generation with PDF/Excel exports, email delivery, and scheduled reporting.",
        highlights: ["Automated reports", "Multiple formats", "Email delivery"]
      }
    ]
  },
  {
    category: "Enterprise Features",
    icon: Shield,
    color: "bg-orange-500",
    features: [
      {
        icon: Settings,
        title: "Advanced Settings",
        description: "Comprehensive system configuration including security settings, ML parameters, and payment method management.",
        highlights: ["Security controls", "ML configuration", "Payment methods"]
      },
      {
        icon: Shield,
        title: "Enterprise Security",
        description: "Advanced security features with audit logs, session management, and comprehensive access controls.",
        highlights: ["Audit logging", "Session control", "Access management"]
      },
      {
        icon: UserCheck,
        title: "Compliance & Validation",
        description: "Built-in compliance checks, data validation, and business rule enforcement across all operations.",
        highlights: ["Data validation", "Compliance checks", "Business rules"]
      }
    ]
  }
];

export default function FeaturesShowcase() {
  return (
    <section className="py-24 bg-white dark:bg-slate-900">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <div className="text-center mb-16">
          <Badge className="mb-4 bg-slate-100 dark:bg-slate-800 text-slate-700 dark:text-slate-300 border-slate-200 dark:border-slate-700">
            Complete Feature Set
          </Badge>
          <h2 className="text-3xl md:text-4xl font-bold text-slate-900 dark:text-white mb-6">
            Everything You Need to Manage Your Business
          </h2>
          <p className="text-lg text-slate-600 dark:text-slate-300 max-w-3xl mx-auto">
            From small retail shops to enterprise operations, DeynCare provides a comprehensive suite of tools 
            designed for modern business management with advanced AI capabilities.
          </p>
        </div>

        {/* Features Grid */}
        <div className="space-y-20">
          {features.map((category, categoryIndex) => (
            <div key={categoryIndex} className="relative">
              {/* Category Header */}
              <div className="flex items-center gap-4 mb-8">
                <div className={`${category.color} p-3 rounded-xl text-white`}>
                  <category.icon className="w-6 h-6" />
                </div>
                <div>
                  <h3 className="text-2xl font-bold text-slate-900 dark:text-white">{category.category}</h3>
                  <p className="text-slate-600 dark:text-slate-300">Essential tools for business success</p>
                </div>
              </div>

              {/* Features Cards */}
              <div className="grid md:grid-cols-3 gap-8">
                {category.features.map((feature, featureIndex) => (
                  <Card key={featureIndex} className="group hover:shadow-lg transition-all duration-300 border border-slate-200 dark:border-slate-700 hover:border-slate-300 dark:hover:border-slate-600 bg-white dark:bg-slate-800">
                    <CardHeader className="pb-4">
                      <div className="flex items-center gap-3 mb-3">
                        <div className="p-2 bg-slate-100 dark:bg-slate-700 rounded-lg group-hover:bg-slate-200 dark:group-hover:bg-slate-600 transition-colors">
                          <feature.icon className="w-5 h-5 text-slate-600 dark:text-slate-300" />
                        </div>
                        <CardTitle className="text-lg text-slate-900 dark:text-white">{feature.title}</CardTitle>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <p className="text-slate-600 dark:text-slate-300 mb-4 leading-relaxed">
                        {feature.description}
                      </p>
                      
                      {/* Feature Highlights */}
                      <div className="space-y-2 mb-4">
                        {feature.highlights.map((highlight, highlightIndex) => (
                          <div key={highlightIndex} className="flex items-center gap-2 text-sm text-slate-700 dark:text-slate-300">
                            <div className="w-1.5 h-1.5 bg-green-500 rounded-full"></div>
                            <span>{highlight}</span>
                          </div>
                        ))}
                      </div>

                      {/* Learn More Link */}
                      <Button variant="ghost" size="sm" className="mt-2 p-0 h-auto text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300">
                        <Link href="#demo" className="flex items-center gap-1">
                          Learn more
                          <ArrowRight className="w-3 h-3" />
                        </Link>
                      </Button>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
          ))}
        </div>

        {/* Bottom CTA */}
        <div className="mt-20 text-center">
          <div className="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 rounded-2xl p-8 md:p-12 border border-slate-200 dark:border-slate-700">
            <h3 className="text-2xl md:text-3xl font-bold text-slate-900 dark:text-white mb-4">
              Ready to Transform Your Business?
            </h3>
            <p className="text-lg text-slate-600 dark:text-slate-300 mb-8 max-w-2xl mx-auto">
              Join thousands of businesses that have streamlined their operations with DeynCare's 
              comprehensive management platform.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button asChild size="lg" className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white">
                <Link href="/login" className="flex items-center gap-2">
                  Start Free Trial
                  <ArrowRight className="w-4 h-4" />
                </Link>
              </Button>
              <Button asChild variant="outline" size="lg" className="border-slate-300 dark:border-slate-600 dark:text-slate-300 dark:hover:bg-slate-800">
                <Link href="#contact">
                  Contact Sales
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
} 