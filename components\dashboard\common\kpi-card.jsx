"use client"

import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card"
import { cn } from "@/lib/utils"
import { Skeleton } from "@/components/ui/skeleton"
import { 
  ArrowUpIcon, 
  ArrowDownIcon,
  MinusIcon,
  TrendingUpIcon,
  TrendingDownIcon,
  DollarSignIcon,
  UsersIcon,
  ShoppingCartIcon,
  CreditCardIcon,
  ActivityIcon,
  ClockIcon,
  CalendarIcon,
  BarChartIcon,
  LineChartIcon,
  PieChartIcon
} from "lucide-react"

const iconMap = {
  dollar: DollarSignIcon,
  users: UsersIcon,
  cart: ShoppingCartIcon,
  credit: CreditCardIcon,
  activity: ActivityIcon,
  clock: ClockIcon,
  calendar: CalendarIcon,
  bar: BarChartIcon,
  line: LineChartIcon,
  pie: PieChartIcon,
}

const trendIcons = {
  up: ArrowUpIcon,
  down: ArrowDownIcon,
  neutral: MinusIcon,
}

const trendColors = {
  up: "text-emerald-500",
  down: "text-red-500",
  neutral: "text-gray-500",
}

const trendBgColors = {
  up: "bg-emerald-500/10",
  down: "bg-red-500/10",
  neutral: "bg-gray-500/10",
}

/**
 * KPI Card Component
 * A reusable card component for displaying key performance indicators
 * 
 * @param {Object} props - Component props
 * @param {string} props.title - The title of the KPI card
 * @param {string|number} props.value - The main value to display
 * @param {string} [props.description] - Optional description text
 * @param {string} [props.icon] - Icon name from iconMap (dollar, users, cart, etc.)
 * @param {('up'|'down'|'neutral')} [props.trend] - Trend direction
 * @param {string|number} [props.trendValue] - Trend percentage or value
 * @param {string} [props.trendLabel] - Label for trend value
 * @param {string} [props.className] - Additional CSS classes
 * @param {boolean} [props.loading] - Loading state
 * @param {React.ReactNode} [props.children] - Optional children content
 * 
 * @example
 * // Basic usage
 * <KpiCard
 *   title="Total Revenue"
 *   value="$45,231.89"
 *   icon="dollar"
 * />
 * 
 * @example
 * // With trend
 * <KpiCard
 *   title="Active Users"
 *   value="2,345"
 *   icon="users"
 *   trend="up"
 *   trendValue="12.5%"
 *   trendLabel="from last week"
 * />
 */
export function KpiCard({
  title,
  value,
  description,
  icon,
  trend,
  trendValue,
  trendLabel,
  className,
  loading = false,
  children,
}) {
  const Icon = icon ? iconMap[icon] : null
  const TrendIcon = trend ? trendIcons[trend] : null

  return (
    <Card className={cn(
      "relative overflow-hidden transition-all duration-300 ease-in-out",
      "shadow-md hover:shadow-lg",
      className
    )}>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">
          {title}
        </CardTitle>
        {Icon && (
          <div className="h-8 w-8 rounded-full bg-accent p-1.5 text-primary">
            <Icon className="h-5 w-5" />
          </div>
        )}
      </CardHeader>
      <CardContent>
        {loading ? (
          <Skeleton className="h-8 w-24" />
        ) : (
          <div className="text-2xl font-bold">{value}</div>
        )}
        {description && (
          <p className="text-xs text-muted-foreground">
            {description}
          </p>
        )}
        {trend && trendValue && (
          <div className={cn(
            "mt-2 flex items-center gap-1 rounded-full px-2 py-1 text-xs font-medium",
            trendBgColors[trend]
          )}>
            <TrendIcon className={cn("h-3 w-3", trendColors[trend])} />
            <span className={trendColors[trend]}>
              {trendValue}
            </span>
            {trendLabel && (
              <span className="text-muted-foreground">
                {trendLabel}
              </span>
            )}
          </div>
        )}
        {children}
      </CardContent>
    </Card>
  )
}

// Example usage:
/*
<KpiCard
  title="Total Revenue"
  value="$45,231.89"
  description="+20.1% from last month"
  icon="dollar"
  trend="up"
  trendValue="20.1%"
  trendLabel="from last month"
/>
*/ 