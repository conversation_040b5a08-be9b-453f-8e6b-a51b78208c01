# 🎯 **EXACT SuperAdmin Shop CRUD Payload**

## ✅ **USE THIS EXACT PAYLOAD STRUCTURE**

### **POST /api/admin/shops** (CREATE SHOP)

#### **Option 1: JSON (No Logo)**
```json
{
  "fullName": "FITAH",
  "email": "<EMAIL>",
  "phone": "+252686824269", 
  "password": "Hnajiib12345$",
  "shopName": "KHANCIYE SHOP",
  "shopAddress": "Hodan, Mogadisho, Somalia"
}
```

#### **Option 2: JSON with Plan Type**
```json
{
  "fullName": "FITAH",
  "email": "<EMAIL>",
  "phone": "+252686824269",
  "password": "Hnajiib12345$", 
  "shopName": "KHANCIYE SHOP",
  "shopAddress": "Hodan, Mogadisho, Somalia",
  "planType": "monthly"
}
```

#### **Option 3: Multipart with Logo**
```bash
Content-Type: multipart/form-data

fullName=FITAH
email=<EMAIL>
phone=+252686824269
password=Hnajiib12345$
shopName=KHANCIYE SHOP  
shopAddress=Hodan, Mogadisho, Somalia
planType=monthly
shopLogo=@/path/to/logo.png
```

---

## 📋 **FIELD REQUIREMENTS**

| **Field** | **Type** | **Required** | **Example** | **Notes** |
|-----------|----------|--------------|-------------|-----------|
| `fullName` | string | ✅ YES | "FITAH" | Owner's full name (3-100 chars) |
| `email` | string | ✅ YES | "<EMAIL>" | Valid email format |
| `phone` | string | ✅ YES | "+252686824269" | International format |
| `password` | string | ✅ YES | "Hnajiib12345$" | Min 8 characters |
| `shopName` | string | ✅ YES | "KHANCIYE SHOP" | Shop name (2-100 chars) |
| `shopAddress` | string | ✅ YES | "Hodan, Mogadisho, Somalia" | Full address (5-200 chars) |
| `planType` | string | ❌ NO | "monthly" | Defaults to "monthly" |
| `shopLogo` | file | ❌ NO | image file | JPG/PNG/WebP, max 5MB |

---

## 🎯 **WHAT HAPPENS WHEN YOU CREATE**

```javascript
// SuperAdmin creates shop → IMMEDIATE RESULTS:
{
  shop: {
    status: "active",           // ✅ Immediately active
    verified: true,             // ✅ Auto-verified  
    isPaid: true,               // ✅ Considered paid
    isActivated: true           // ✅ Ready to use
  },
  owner: {
    status: "active",           // ✅ Immediately active
    emailVerified: true,        // ✅ No verification needed
    role: "admin"               // ✅ Shop admin
  }
}
```

---

## 🔧 **UPDATE SHOP PAYLOAD**

### **PUT /api/admin/shops/:shopId**

```json
{
  "fullName": "Updated Owner Name",
  "email": "<EMAIL>", 
  "phone": "+252686824270",
  "shopName": "Updated Shop Name",
  "shopAddress": "New Address, Somalia"
}
```

**Note:** All fields are optional for updates. Only send the fields you want to change.

---

## 🖼️ **LOGO UPLOAD PAYLOAD**

### **PUT /api/admin/shops/:shopId/logo**

```bash
Content-Type: multipart/form-data

shopLogo=@/path/to/new-logo.png
```

**Requirements:**
- File types: JPG, PNG, WebP only
- Max size: 5MB
- Field name: exactly `shopLogo`

---

## 🚫 **WHAT NOT TO INCLUDE**

**❌ DO NOT send these fields** (they're handled automatically):
```json
{
  "registeredBy": "superAdmin",     // ❌ Auto-set
  "paymentMethod": "admin_created", // ❌ Auto-set  
  "initialPaid": true,              // ❌ Auto-set
  "paymentDetails": {},             // ❌ Auto-set
  "status": "active",               // ❌ Auto-set
  "verified": true                  // ❌ Auto-set
}
```

---

## ✅ **WORKING EXAMPLES**

### **cURL - JSON Request**
```bash
curl -X POST http://localhost:5000/api/admin/shops \
  -H "Authorization: Bearer YOUR_SUPERADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "fullName": "FITAH",
    "email": "<EMAIL>",
    "phone": "+252686824269",
    "password": "Hnajiib12345$",
    "shopName": "KHANCIYE SHOP",
    "shopAddress": "Hodan, Mogadisho, Somalia"
  }'
```

### **cURL - With Logo**
```bash
curl -X POST http://localhost:5000/api/admin/shops \
  -H "Authorization: Bearer YOUR_SUPERADMIN_TOKEN" \
  -F "fullName=FITAH" \
  -F "email=<EMAIL>" \
  -F "phone=+252686824269" \
  -F "password=Hnajiib12345$" \
  -F "shopName=KHANCIYE SHOP" \
  -F "shopAddress=Hodan, Mogadisho, Somalia" \
  -F "shopLogo=@deyncare_icon.png"
```

### **JavaScript/Fetch**
```javascript
const createShop = async () => {
  const response = await fetch('/api/admin/shops', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${superAdminToken}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      fullName: "FITAH",
      email: "<EMAIL>", 
      phone: "+252686824269",
      password: "Hnajiib12345$",
      shopName: "KHANCIYE SHOP",
      shopAddress: "Hodan, Mogadisho, Somalia"
    })
  });
  
  const result = await response.json();
  console.log(result);
};
```

---

## 🎉 **EXPECTED SUCCESS RESPONSE**

```json
{
  "success": true,
  "message": "Shop and owner created successfully",
  "data": {
    "shop": {
      "shopId": "SHP001234",
      "shopName": "KHANCIYE SHOP",
      "ownerName": "FITAH", 
      "email": "<EMAIL>",
      "phone": "+252686824269",
      "address": "Hodan, Mogadisho, Somalia",
      "logoUrl": "http://localhost:5000/api/files/FILE123456",
      "status": "active",
      "planType": "monthly"
    },
    "owner": {
      "userId": "USR567890",
      "fullName": "FITAH",
      "email": "<EMAIL>", 
      "role": "admin"
    }
  }
}
```

---

## 🔍 **KEY DIFFERENCES FROM PUBLIC REGISTRATION**

| **Aspect** | **Public Registration** | **SuperAdmin CRUD** |
|------------|------------------------|-------------------|
| **Payload Structure** | ✅ Same | ✅ Same |
| **Validation** | Payment logic required | NO payment logic |
| **Result** | Pending → needs verification | Immediately active |
| **Email Verification** | Required | Not required |
| **Payment** | Required for paid plans | Not required |
| **Authorization** | None | SuperAdmin token required |

**🚀 This is exactly the payload structure that public registration uses, but without any payment/verification complexity!** 