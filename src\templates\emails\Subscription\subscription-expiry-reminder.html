<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Subscription Expiry Reminder - DeynCare</title>
    <link rel="stylesheet" href="../css/styles.css">
</head>
<body>
    <div class="email-container">
        <div class="header">
            <img src="https://yourwebsite.com/logo.png" alt="DeynCare Logo" class="logo">
        </div>
        
        <div class="content">
            {{#if (eq urgencyLevel 'urgent')}}
            <div class="alert alert-danger">
                <h1>⚠️ Your Subscription Expires {{#if (eq daysLeft 0)}}Today{{else}}Tomorrow{{/if}}!</h1>
            </div>
            {{else}}
            <h1>📅 Subscription Expiry Reminder</h1>
            {{/if}}
            
            <p>Hello {{shopName}},</p>
            
            <div class="expiry-warning {{urgencyLevel}}">
                <div class="expiry-info">
                    {{#if (eq daysLeft 0)}}
                    <strong>Your DeynCare subscription expires today!</strong>
                    {{else if (eq daysLeft 1)}}
                    <strong>Your DeynCare subscription expires tomorrow!</strong>
                    {{else}}
                    <strong>Your DeynCare subscription expires in {{daysLeft}} days.</strong>
                    {{/if}}
                </div>
                
                <table class="details-table">
                    <tr>
                        <td><strong>Plan:</strong></td>
                        <td>{{planType}} Plan</td>
                    </tr>
                    <tr>
                        <td><strong>Expiry Date:</strong></td>
                        <td>{{endDate}}</td>
                    </tr>
                    {{#if autoRenew}}
                    <tr>
                        <td><strong>Auto-Renewal:</strong></td>
                        <td>Enabled ✅</td>
                    </tr>
                    {{else}}
                    <tr>
                        <td><strong>Auto-Renewal:</strong></td>
                        <td>Disabled ❌</td>
                    </tr>
                    {{/if}}
                </table>
            </div>

            {{#if autoRenew}}
            <div class="renewal-info">
                <h3>🔄 Auto-Renewal Active</h3>
                <p>Good news! Your subscription will automatically renew before the expiry date. Please ensure your payment method is up to date to avoid any interruptions.</p>
                <a href="{{dashboardUrl}}/subscription/payment-methods" class="secondary-button">Update Payment Method</a>
            </div>
            {{else}}
            <div class="action-required">
                <h3>⚡ Action Required</h3>
                <p>Your subscription will not automatically renew. To continue enjoying all DeynCare features without interruption, please renew your subscription now.</p>
                <a href="{{renewUrl}}" class="cta-button">Renew My Subscription</a>
            </div>
            {{/if}}

            <div class="features-at-risk">
                <h3>🚨 Features That Will Be Affected</h3>
                <p>When your subscription expires, the following features will be restricted:</p>
                <ul class="feature-list">
                    <li>✋ Customer management</li>
                    <li>✋ Debt tracking and monitoring</li>
                    <li>✋ Payment processing</li>
                    <li>✋ Risk assessment tools</li>
                    <li>✋ Advanced reporting</li>
                    <li>✋ Data export capabilities</li>
                </ul>
            </div>

            <div class="contact-section">
                <h3>💬 Need Help?</h3>
                <p>If you have any questions about your subscription or need assistance with renewal, our support team is here to help.</p>
                <a href="{{supportUrl}}" class="text-link">Contact Support</a>
            </div>

            <div class="payment-options">
                <h3>💳 Quick Renewal Options</h3>
                <p>Choose your preferred payment method for instant renewal:</p>
                <div class="payment-buttons">
                    <a href="{{renewUrl}}?method=evc" class="payment-button evc">
                        <span class="payment-icon">📱</span>
                        EVC Plus
                    </a>
                    <a href="{{renewUrl}}?method=card" class="payment-button card">
                        <span class="payment-icon">💳</span>
                        Credit Card
                    </a>
                </div>
            </div>
        </div>
        
        <div class="footer">
            <p>&copy; {{currentYear}} DeynCare. All rights reserved.</p>
            <p>
                <a href="{{privacyUrl}}">Privacy Policy</a> | 
                <a href="{{termsUrl}}">Terms of Service</a> | 
                <a href="{{supportUrl}}">Support</a>
            </p>
        </div>
    </div>

    <style>
        .expiry-warning.urgent {
            background: #fee;
            border-left: 4px solid #dc3545;
            padding: 15px;
            margin: 20px 0;
        }
        .expiry-warning.high {
            background: #fff3cd;
            border-left: 4px solid #ffc107;
            padding: 15px;
            margin: 20px 0;
        }
        .expiry-warning.medium {
            background: #d1ecf1;
            border-left: 4px solid #17a2b8;
            padding: 15px;
            margin: 20px 0;
        }
        .feature-list {
            list-style: none;
            padding: 0;
        }
        .feature-list li {
            padding: 5px 0;
            font-weight: 500;
        }
        .payment-buttons {
            display: flex;
            gap: 10px;
            margin: 15px 0;
        }
        .payment-button {
            flex: 1;
            text-align: center;
            padding: 12px;
            text-decoration: none;
            border-radius: 5px;
            font-weight: bold;
        }
        .payment-button.evc {
            background: #28a745;
            color: white;
        }
        .payment-button.card {
            background: #007bff;
            color: white;
        }
        .payment-icon {
            display: block;
            font-size: 24px;
            margin-bottom: 5px;
        }
    </style>
</body>
</html> 