/**
 * Token management module
 * Handles token refresh logic, CSRF protection, and local storage interactions
 */
// Import axios directly to avoid circular dependency
import axios from 'axios';
import { CONFIG } from './config';

// Token refresh state management
// Export this so interceptors can access it
export let isRefreshing = false;
let refreshSubscribers = [];
let lastTokenRefresh = 0;
const TOKEN_REFRESH_COOLDOWN = 60000; // 1 minute cooldown between refreshes

/**
 * Subscribe to token refresh
 * @param {Function} callback - Function to call when token is refreshed
 */
export function subscribeTokenRefresh(callback) {
  refreshSubscribers.push(callback);
}

/**
 * Notify subscribers that token refresh is complete
 * @param {string} token - The new access token
 */
export function onTokenRefreshed(token) {
  refreshSubscribers.forEach(callback => callback(token));
  refreshSubscribers = [];
}

/**
 * Reset refresh state when token refresh fails
 */
export function onTokenRefreshFailed() {
  refreshSubscribers = [];
  isRefreshing = false;
}

/**
 * Token refresh function
 * @returns {Promise<string|null>} New access token or null
 */
export async function refreshToken() {
  console.log('[TokenModule] Starting token refresh');
  
  // Check if we're already refreshing or if we've refreshed recently
  const now = Date.now();
  if (isRefreshing) {
    console.log('[API] Token refresh skipped - already in progress');
    return null;
  }
  
  // Add throttling with some room for concurrent requests
  if (now - lastTokenRefresh < TOKEN_REFRESH_COOLDOWN) {
    console.log('[API] Token refresh skipped - recent refresh within cooldown period');
    // Return the existing token instead of null to allow the request to continue
    return localStorage.getItem('accessToken');
  }
  
  isRefreshing = true;
  lastTokenRefresh = now;
  console.log('[API] Starting token refresh');
  
  try {
    // Get the refresh token
    const refreshToken = localStorage.getItem('refreshToken');
    if (!refreshToken) {
      console.log('[API] No refresh token available');
      onTokenRefreshFailed();
      // Clear any lingering tokens to force re-auth
      localStorage.removeItem('accessToken');
      return null;
    }
    
    // Call the refresh token endpoint
    const baseURL = CONFIG.baseURL || process.env.NEXT_PUBLIC_API_URL || 'https://deyncare-backend.khanciye.com';
    console.log(`[API] Refreshing token with baseURL: ${baseURL}`);
    
    const refreshEndpoint = `${baseURL}/api/auth/refresh-token`;
    console.log(`[API] Making refresh token request to: ${refreshEndpoint}`);
    
    try {
      const response = await axios.post(refreshEndpoint, {
        refreshToken
      }, {
        headers: {
          'Content-Type': 'application/json'
        },
        // Add timeout to prevent long-hanging requests
        timeout: 10000
      });
      
      // Log response for debugging
      console.log('[API] Token refresh response status:', response.status);
      if (response.data) {
        console.log('[API] Token refresh response structure:', Object.keys(response.data));
      }
      
      // Handle the response - first try the standard format
      if (response.data?.data?.accessToken) {
        const newToken = response.data.data.accessToken;
        console.log('[API] Token refreshed successfully (standard format)');
        
        // Update local storage
        localStorage.setItem('accessToken', newToken);
        if (response.data.data.refreshToken) {
          localStorage.setItem('refreshToken', response.data.data.refreshToken);
        }
        
        // Notify subscribers
        onTokenRefreshed(newToken);
        isRefreshing = false;
        
        // Dispatch event for auth context
        if (typeof window !== 'undefined') {
          window.dispatchEvent(new CustomEvent('auth:token:refreshed', { 
            detail: { newToken } 
          }));
        }
        
        return newToken;
      }
      
      // Try alternative response format (just in case API format varies)
      if (response.data?.accessToken) {
        const newToken = response.data.accessToken;
        console.log('[API] Token refreshed successfully (alternative format)');
        
        // Update local storage
        localStorage.setItem('accessToken', newToken);
        if (response.data.refreshToken) {
          localStorage.setItem('refreshToken', response.data.refreshToken);
        }
        
        // Notify subscribers
        onTokenRefreshed(newToken);
        isRefreshing = false;
        
        // Dispatch event for auth context
        if (typeof window !== 'undefined') {
          window.dispatchEvent(new CustomEvent('auth:token:refreshed', { 
            detail: { newToken } 
          }));
        }
        
        return newToken;
      }
      
      // If we got here, response format is unexpected
      console.log('[API] Token refresh failed - invalid response structure:', response.data);
      onTokenRefreshFailed();
      return null;
      
    } catch (requestError) {
      // Handle specific error cases
      console.error('[API] Token refresh request error:', requestError.message);
      
      // Check if it's a network error
      if (requestError.code === 'ECONNREFUSED' || requestError.message.includes('Network Error')) {
        console.warn('[API] Network error during token refresh - server might be down');
      }
      
      throw requestError; // Re-throw to be caught by outer catch
    }
  } catch (error) {
    console.error('[API] Token refresh error:', error);
    onTokenRefreshFailed();
    
    // Dispatch event for auth context
    if (typeof window !== 'undefined') {
      window.dispatchEvent(new CustomEvent('auth:token:refresh:failed'));
    }
    
    // Specific handling for inactive accounts - using stricter check
    if (error.response?.status === 403 && 
        (error.response?.data?.type === 'inactive_account' || 
         error.response?.data?.code === 'account_inactive' ||
         (error.response?.data?.message?.includes('account is inactive') &&
          !error.config?.url?.includes('/api/settings')))) {
      console.warn('[API] Inactive account detected during token refresh');
      
      // Dispatch a specific event for inactive accounts with context
      if (typeof window !== 'undefined') {
        window.dispatchEvent(new CustomEvent('auth:account:inactive', {
          detail: { 
            message: error.response?.data?.message || 'Your account is inactive',
            url: error.config?.url || '',
            isSettingsError: error.config?.url?.includes('/api/settings'),
            errorCode: error.response?.data?.code || '',
            source: 'token_refresh'
          }
        }));
      }
      
      // Store the inactive account status
      localStorage.setItem('accountStatus', 'inactive');
    } else if (error.response?.status === 403 && error.config?.url?.includes('/api/settings')) {
      // Special handling for settings-related errors
      console.warn('[API] Settings permission error during token refresh - not treating as inactive account');
      // Do not mark account as inactive for settings errors
    }
    
    // Check if we should clear tokens on specific errors - more selective about settings-related errors
    const shouldClearTokens = (
      // Only clear on unauthorized if not a settings endpoint
      (error.response?.status === 401 && !error.config?.url?.includes('/api/settings')) || 
      // Only clear on forbidden if genuine account issue and not settings endpoint
      (error.response?.status === 403 && 
       !error.config?.url?.includes('/api/settings') && 
       (error.response?.data?.type === 'inactive_account' || 
        error.response?.data?.code === 'account_inactive')) || 
      // Clear on specific token-related errors
      (error.response?.data?.message && 
        (error.response.data.message.includes('invalid token') || 
         error.response.data.message.includes('expired token') || 
         (error.response.data.message.includes('inactive') && 
          error.response.data.message.includes('account') && 
          !error.config?.url?.includes('/api/settings'))
        )
      )
    );
    
    if (shouldClearTokens) {
      console.log('[API] Clearing invalid tokens due to auth error');
      localStorage.removeItem('accessToken');
      localStorage.removeItem('refreshToken');
    }
    
    return null;
  }
}

/**
 * Get the current access token from localStorage
 * @returns {string|null} The access token or null if not available
 */
export function getAccessToken() {
  if (typeof window === 'undefined') return null;
  return localStorage.getItem('accessToken');
}

/**
 * Set access and refresh tokens in localStorage
 * @param {string} accessToken - The access token
 * @param {string} refreshToken - The refresh token
 */
export function setTokens(accessToken, refreshToken) {
  if (typeof window === 'undefined') return;
  
  if (accessToken) {
    localStorage.setItem('accessToken', accessToken);
  }
  
  if (refreshToken) {
    localStorage.setItem('refreshToken', refreshToken);
  }
}

/**
 * Clear all authentication tokens
 */
export function clearTokens() {
  if (typeof window === 'undefined') return;
  
  localStorage.removeItem('accessToken');
  localStorage.removeItem('refreshToken');
}

/**
 * Get CSRF token from cookies or localStorage
 * @returns {string|null} CSRF token or null if not available
 */
export function getCsrfToken() {
  if (typeof window === 'undefined') return null;
  
  // First try to get it from cookies (secure method set by backend)
  const csrfToken = getCookieValue('XSRF-TOKEN');
  if (csrfToken) {
    return csrfToken;
  }
  
  // Fallback to localStorage (less secure but maintains compatibility)
  return localStorage.getItem('csrfToken');
}

/**
 * Set CSRF token in localStorage
 * @param {string} token - CSRF token
 */
export function setCsrfToken(token) {
  if (typeof window === 'undefined' || !token) return;
  
  localStorage.setItem('csrfToken', token);
}

/**
 * Helper function to get a cookie value by name
 * @param {string} name - Cookie name
 * @returns {string|null} Cookie value or null
 */
function getCookieValue(name) {
  if (typeof window === 'undefined') return null;
  
  try {
    const match = document.cookie.match(new RegExp('(^| )' + name + '=([^;]+)'));
    return match ? decodeURIComponent(match[2]) : null;
  } catch (error) {
    console.error(`[Token] Error reading cookie ${name}:`, error.message);
    return null;
  }
}
