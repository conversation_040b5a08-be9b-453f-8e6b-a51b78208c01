/**
 * Controller for SuperAdmin to manage payment retries
 */
const { logInfo, logError, logSuccess } = require('../../utils/logger');
const { SubscriptionService } = require('../../services');
const AppError = require('../../utils/core/AppError');

/**
 * Get payment retry status for a subscription
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 * @param {Function} next - Next middleware function
 * @returns {Promise<void>}
 */
async function getPaymentRetryStatus(req, res, next) {
  try {
    // Only SuperAdmin can access this endpoint
    if (req.user.role !== 'superAdmin') {
      return next(new AppError('You do not have permission to access this resource', 403, 'forbidden'));
    }

    const { subscriptionId } = req.params;
    
    if (!subscriptionId) {
      return next(new AppError('Subscription ID is required', 400, 'bad_request'));
    }

    const retryStatus = await SubscriptionService.getRetryStatus(subscriptionId);
    
    logInfo(`SuperAdmin ${req.user.email} retrieved payment retry status for subscription ${subscriptionId}`, 'managePaymentRetries');
    
    res.status(200).json({
      success: true,
      data: retryStatus
    });
  } catch (error) {
    logError(`Error getting payment retry status: ${error.message}`, 'managePaymentRetries', error);
    next(error);
  }
}

/**
 * Trigger manual retry for a subscription payment
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 * @param {Function} next - Next middleware function
 * @returns {Promise<void>}
 */
async function triggerManualRetry(req, res, next) {
  try {
    // Only SuperAdmin can access this endpoint
    if (req.user.role !== 'superAdmin') {
      return next(new AppError('You do not have permission to access this resource', 403, 'forbidden'));
    }

    const { subscriptionId } = req.params;
    
    if (!subscriptionId) {
      return next(new AppError('Subscription ID is required', 400, 'bad_request'));
    }

    const result = await SubscriptionService.triggerManualRetry(subscriptionId);
    
    logSuccess(`SuperAdmin ${req.user.email} manually triggered payment retry for subscription ${subscriptionId}`, 'managePaymentRetries');
    
    res.status(200).json({
      success: result.success,
      message: result.message,
      data: {
        newExpiryDate: result.newExpiryDate,
        retryScheduled: result.retryScheduled
      }
    });
  } catch (error) {
    logError(`Error triggering manual payment retry: ${error.message}`, 'managePaymentRetries', error);
    next(error);
  }
}

/**
 * Cancel scheduled retries for a subscription
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 * @param {Function} next - Next middleware function
 * @returns {Promise<void>}
 */
async function cancelScheduledRetries(req, res, next) {
  try {
    // Only SuperAdmin can access this endpoint
    if (req.user.role !== 'superAdmin') {
      return next(new AppError('You do not have permission to access this resource', 403, 'forbidden'));
    }

    const { subscriptionId } = req.params;
    
    if (!subscriptionId) {
      return next(new AppError('Subscription ID is required', 400, 'bad_request'));
    }

    const result = await SubscriptionService.cancelScheduledRetries(subscriptionId);
    
    logInfo(`SuperAdmin ${req.user.email} cancelled scheduled payment retries for subscription ${subscriptionId}`, 'managePaymentRetries');
    
    res.status(200).json({
      success: true,
      message: `Cancelled ${result.cancelledCount} scheduled payment retries`,
      data: result
    });
  } catch (error) {
    logError(`Error cancelling scheduled retries: ${error.message}`, 'managePaymentRetries', error);
    next(error);
  }
}

/**
 * Process all pending payment retries
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 * @param {Function} next - Next middleware function
 * @returns {Promise<void>}
 */
async function processAllPendingRetries(req, res, next) {
  try {
    // Only SuperAdmin can access this endpoint
    if (req.user.role !== 'superAdmin') {
      return next(new AppError('You do not have permission to access this resource', 403, 'forbidden'));
    }

    const results = await SubscriptionService.processPendingRetries();
    
    logInfo(`SuperAdmin ${req.user.email} manually processed all pending payment retries`, 'managePaymentRetries');
    
    res.status(200).json({
      success: true,
      message: `Processed ${results.total} pending payment retries. Successful: ${results.successful}, Failed: ${results.failed}`,
      data: results
    });
  } catch (error) {
    logError(`Error processing pending retries: ${error.message}`, 'managePaymentRetries', error);
    next(error);
  }
}

/**
 * Get payment retry configuration
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 * @param {Function} next - Next middleware function
 * @returns {Promise<void>}
 */
async function getRetryConfiguration(req, res, next) {
  try {
    // Only SuperAdmin can access this endpoint
    if (req.user.role !== 'superAdmin') {
      return next(new AppError('You do not have permission to access this resource', 403, 'forbidden'));
    }

    const retryConfig = SubscriptionService.PAYMENT_RETRY_CONFIG;
    
    res.status(200).json({
      success: true,
      data: retryConfig
    });
  } catch (error) {
    logError(`Error getting retry configuration: ${error.message}`, 'managePaymentRetries', error);
    next(error);
  }
}

module.exports = {
  getPaymentRetryStatus,
  triggerManualRetry,
  cancelScheduledRetries,
  processAllPendingRetries,
  getRetryConfiguration
};
