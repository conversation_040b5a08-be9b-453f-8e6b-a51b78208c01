'use client';

import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { toast } from 'sonner';
import { 
  Save, 
  X, 
  DollarSign, 
  Calendar, 
  CreditCard, 
  Settings,
  AlertTriangle
} from 'lucide-react';
import SubscriptionService from '@/lib/services/subscription';

const EditSubscriptionModal = ({ isOpen, onClose, subscription, onSuccess }) => {
  const [formData, setFormData] = useState({});
  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState({});

  // Initialize form data when subscription changes
  useEffect(() => {
    if (subscription) {
      setFormData({
        // Basic info
        status: subscription.status || 'active',
        
        // Plan details
        planName: subscription.plan?.name || '',
        planType: subscription.plan?.type || 'monthly',
        
        // Pricing
        basePrice: subscription.pricing?.basePrice || 0,
        currency: subscription.pricing?.currency || 'USD',
        billingCycle: subscription.pricing?.billingCycle || 'monthly',
        
        // Discount
        discountActive: subscription.pricing?.discount?.active || false,
        discountType: subscription.pricing?.discount?.type || 'percentage',
        discountValue: subscription.pricing?.discount?.value || 0,
        discountAmount: subscription.pricing?.discount?.amount || 0,
        discountCode: subscription.pricing?.discount?.code || '',
        
        // Payment
        paymentMethod: subscription.payment?.method || 'offline',
        paymentVerified: subscription.payment?.verified || false,
        
        // Payment details
        payerName: subscription.payment?.paymentDetails?.payerName || '',
        payerEmail: subscription.payment?.paymentDetails?.payerEmail || '',
        payerPhone: subscription.payment?.paymentDetails?.payerPhone || '',
        transactionId: subscription.payment?.paymentDetails?.transactionId || '',
        notes: subscription.payment?.paymentDetails?.notes || '',
        
        // Renewal settings
        autoRenew: subscription.renewalSettings?.autoRenew ?? true,
        
        // Dates (for display, not editable in basic edit)
        startDate: subscription.dates?.startDate || '',
        endDate: subscription.dates?.endDate || '',
      });
      setErrors({});
    }
  }, [subscription]);

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: null
      }));
    }
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.planName?.trim()) {
      newErrors.planName = 'Plan name is required';
    }

    if (formData.basePrice < 0) {
      newErrors.basePrice = 'Price cannot be negative';
    }

    if (formData.discountActive) {
      if (formData.discountType === 'percentage') {
        if (formData.discountValue < 0 || formData.discountValue > 100) {
          newErrors.discountValue = 'Percentage must be between 0 and 100';
        }
      } else if (formData.discountType === 'fixed') {
        if (formData.discountAmount < 0) {
          newErrors.discountAmount = 'Discount amount cannot be negative';
        }
      }
    }

    if (formData.payerEmail && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.payerEmail)) {
      newErrors.payerEmail = 'Invalid email format';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm()) {
      toast.error('Please fix the errors in the form');
      return;
    }

    setIsLoading(true);
    try {
      const updateData = {
        // Plan information
        plan: {
          name: formData.planName,
          type: formData.planType,
        },
        
        // Pricing information
        pricing: {
          basePrice: parseFloat(formData.basePrice),
          currency: formData.currency,
          billingCycle: formData.billingCycle,
          discount: formData.discountActive ? {
            active: true,
            type: formData.discountType,
            value: formData.discountType === 'percentage' ? parseFloat(formData.discountValue) : undefined,
            amount: formData.discountType === 'fixed' ? parseFloat(formData.discountAmount) : undefined,
            code: formData.discountCode || undefined,
          } : {
            active: false
          }
        },
        
        // Payment information
        payment: {
          method: formData.paymentMethod,
          verified: formData.paymentVerified,
          paymentDetails: {
            payerName: formData.payerName || undefined,
            payerEmail: formData.payerEmail || undefined,
            payerPhone: formData.payerPhone || undefined,
            transactionId: formData.transactionId || undefined,
            notes: formData.notes || undefined,
          }
        },
        
        // Renewal settings
        renewalSettings: {
          autoRenew: formData.autoRenew,
        },
        
        // Status
        status: formData.status,
      };

      // Remove undefined values to avoid overwriting with undefined
      const cleanUpdateData = JSON.parse(JSON.stringify(updateData));

      await SubscriptionService.updateSubscription(subscription.subscriptionId, cleanUpdateData);
      
      toast.success('Subscription updated successfully');
      onSuccess && onSuccess();
      onClose();
    } catch (error) {
      console.error('Error updating subscription:', error);
      toast.error(error.message || 'Failed to update subscription');
    } finally {
      setIsLoading(false);
    }
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: formData.currency || 'USD'
    }).format(amount || 0);
  };

  if (!subscription) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="dialog-content max-w-4xl">
        <DialogHeader className="dialog-header">
          <DialogTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Edit Subscription #{subscription.subscriptionId}
          </DialogTitle>
        </DialogHeader>

        <div className="dialog-body">
          <div className="space-y-6">
          {/* Status Warning */}
          {subscription.status === 'canceled' && (
            <Card className="border-red-200 bg-red-50">
              <CardContent className="pt-4">
                <div className="flex items-center gap-2 text-red-700">
                  <AlertTriangle className="h-4 w-4" />
                  <span className="text-sm font-medium">
                    This subscription is canceled. Some changes may not take effect.
                  </span>
                </div>
              </CardContent>
            </Card>
          )}

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Basic Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Settings className="h-4 w-4" />
                  Basic Information
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="status">Status</Label>
                  <Select
                    value={formData.status}
                    onValueChange={(value) => handleInputChange('status', value)}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="active">Active</SelectItem>
                      <SelectItem value="trial">Trial</SelectItem>
                      <SelectItem value="past_due">Past Due</SelectItem>
                      <SelectItem value="canceled">Canceled</SelectItem>
                      <SelectItem value="expired">Expired</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="planName">Plan Name</Label>
                  <Input
                    id="planName"
                    value={formData.planName}
                    onChange={(e) => handleInputChange('planName', e.target.value)}
                    placeholder="Enter plan name"
                  />
                  {errors.planName && (
                    <p className="text-sm text-red-500 mt-1">{errors.planName}</p>
                  )}
                </div>

                <div>
                  <Label htmlFor="planType">Plan Type</Label>
                  <Select
                    value={formData.planType}
                    onValueChange={(value) => handleInputChange('planType', value)}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="trial">Trial</SelectItem>
                      <SelectItem value="monthly">Monthly</SelectItem>
                      <SelectItem value="yearly">Yearly</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </CardContent>
            </Card>

            {/* Pricing Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <DollarSign className="h-4 w-4" />
                  Pricing Information
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-2">
                  <div>
                    <Label htmlFor="basePrice">Base Price</Label>
                    <Input
                      id="basePrice"
                      type="number"
                      step="0.01"
                      min="0"
                      value={formData.basePrice}
                      onChange={(e) => handleInputChange('basePrice', e.target.value)}
                    />
                    {errors.basePrice && (
                      <p className="text-sm text-red-500 mt-1">{errors.basePrice}</p>
                    )}
                  </div>
                  <div>
                    <Label htmlFor="currency">Currency</Label>
                    <Select
                      value={formData.currency}
                      onValueChange={(value) => handleInputChange('currency', value)}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="USD">USD</SelectItem>
                        <SelectItem value="EUR">EUR</SelectItem>
                        <SelectItem value="GBP">GBP</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div>
                  <Label htmlFor="billingCycle">Billing Cycle</Label>
                  <Select
                    value={formData.billingCycle}
                    onValueChange={(value) => handleInputChange('billingCycle', value)}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="monthly">Monthly</SelectItem>
                      <SelectItem value="yearly">Yearly</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="pt-2">
                  <p className="text-sm text-gray-500">
                    Total Price: <span className="font-semibold">{formatCurrency(formData.basePrice)}</span>
                  </p>
                </div>
              </CardContent>
            </Card>

            {/* Discount Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2 justify-between">
                  <span className="flex items-center gap-2">
                    <DollarSign className="h-4 w-4" />
                    Discount Settings
                  </span>
                  <Switch
                    checked={formData.discountActive}
                    onCheckedChange={(checked) => handleInputChange('discountActive', checked)}
                  />
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {formData.discountActive && (
                  <>
                    <div>
                      <Label htmlFor="discountType">Discount Type</Label>
                      <Select
                        value={formData.discountType}
                        onValueChange={(value) => handleInputChange('discountType', value)}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="percentage">Percentage</SelectItem>
                          <SelectItem value="fixed">Fixed Amount</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    {formData.discountType === 'percentage' ? (
                      <div>
                        <Label htmlFor="discountValue">Discount Percentage (%)</Label>
                        <Input
                          id="discountValue"
                          type="number"
                          min="0"
                          max="100"
                          value={formData.discountValue}
                          onChange={(e) => handleInputChange('discountValue', e.target.value)}
                        />
                        {errors.discountValue && (
                          <p className="text-sm text-red-500 mt-1">{errors.discountValue}</p>
                        )}
                      </div>
                    ) : (
                      <div>
                        <Label htmlFor="discountAmount">Discount Amount</Label>
                        <Input
                          id="discountAmount"
                          type="number"
                          step="0.01"
                          min="0"
                          value={formData.discountAmount}
                          onChange={(e) => handleInputChange('discountAmount', e.target.value)}
                        />
                        {errors.discountAmount && (
                          <p className="text-sm text-red-500 mt-1">{errors.discountAmount}</p>
                        )}
                      </div>
                    )}

                    <div>
                      <Label htmlFor="discountCode">Discount Code (Optional)</Label>
                      <Input
                        id="discountCode"
                        value={formData.discountCode}
                        onChange={(e) => handleInputChange('discountCode', e.target.value)}
                        placeholder="Enter discount code"
                      />
                    </div>
                  </>
                )}
              </CardContent>
            </Card>

            {/* Payment Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <CreditCard className="h-4 w-4" />
                  Payment Information
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="paymentMethod">Payment Method</Label>
                  <Select
                    value={formData.paymentMethod}
                    onValueChange={(value) => handleInputChange('paymentMethod', value)}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="offline">Offline</SelectItem>
                      <SelectItem value="evc_plus">EVC Plus</SelectItem>
                      <SelectItem value="free">Free</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="flex items-center space-x-2">
                  <Switch
                    id="paymentVerified"
                    checked={formData.paymentVerified}
                    onCheckedChange={(checked) => handleInputChange('paymentVerified', checked)}
                  />
                  <Label htmlFor="paymentVerified">Payment Verified</Label>
                </div>

                <div className="flex items-center space-x-2">
                  <Switch
                    id="autoRenew"
                    checked={formData.autoRenew}
                    onCheckedChange={(checked) => handleInputChange('autoRenew', checked)}
                  />
                  <Label htmlFor="autoRenew">Auto Renewal</Label>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Payment Details */}
          <Card>
            <CardHeader>
              <CardTitle>Payment Details</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="payerName">Payer Name</Label>
                  <Input
                    id="payerName"
                    value={formData.payerName}
                    onChange={(e) => handleInputChange('payerName', e.target.value)}
                    placeholder="Enter payer name"
                  />
                </div>

                <div>
                  <Label htmlFor="payerEmail">Payer Email</Label>
                  <Input
                    id="payerEmail"
                    type="email"
                    value={formData.payerEmail}
                    onChange={(e) => handleInputChange('payerEmail', e.target.value)}
                    placeholder="Enter payer email"
                  />
                  {errors.payerEmail && (
                    <p className="text-sm text-red-500 mt-1">{errors.payerEmail}</p>
                  )}
                </div>

                <div>
                  <Label htmlFor="payerPhone">Payer Phone</Label>
                  <Input
                    id="payerPhone"
                    value={formData.payerPhone}
                    onChange={(e) => handleInputChange('payerPhone', e.target.value)}
                    placeholder="Enter payer phone"
                  />
                </div>

                <div>
                  <Label htmlFor="transactionId">Transaction ID</Label>
                  <Input
                    id="transactionId"
                    value={formData.transactionId}
                    onChange={(e) => handleInputChange('transactionId', e.target.value)}
                    placeholder="Enter transaction ID"
                  />
                </div>
              </div>

              <div className="mt-4">
                <Label htmlFor="notes">Notes</Label>
                <Textarea
                  id="notes"
                  value={formData.notes}
                  onChange={(e) => handleInputChange('notes', e.target.value)}
                  placeholder="Enter additional notes"
                  rows={3}
                />
              </div>
            </CardContent>
          </Card>

          {/* Read-only Date Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Calendar className="h-4 w-4" />
                Period Information (Read-only)
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label>Start Date</Label>
                  <Input 
                    value={formData.startDate ? new Date(formData.startDate).toLocaleDateString() : 'N/A'} 
                    disabled 
                  />
                </div>
                <div>
                  <Label>End Date</Label>
                  <Input 
                    value={formData.endDate ? new Date(formData.endDate).toLocaleDateString() : 'N/A'} 
                    disabled 
                  />
                </div>
              </div>
              <p className="text-sm text-gray-500 mt-2">
                To modify dates, use the "Extend" action or contact support.
              </p>
            </CardContent>
          </Card>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onClose} disabled={isLoading}>
            <X className="h-4 w-4 mr-2" />
            Cancel
          </Button>
          <Button onClick={handleSubmit} disabled={isLoading}>
            {isLoading ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Saving...
              </>
            ) : (
              <>
                <Save className="h-4 w-4 mr-2" />
                Save Changes
              </>
            )}
          </Button>
        </DialogFooter>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default EditSubscriptionModal; 