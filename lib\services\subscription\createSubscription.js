import apiBridge from '@/lib/api/bridge';
import BaseService from '../baseService';

const createSubscription = async (subscriptionData) => {
  try {
    const response = await apiBridge.post(ENDPOINTS.subscription.create, subscriptionData);
    
    return BaseService.processApiResponse(response);
  } catch (error) {
    throw BaseService.handleApiError(error);
  }
};

export default createSubscription; 
