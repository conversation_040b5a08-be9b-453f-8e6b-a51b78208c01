"use client"

import { createContext, useState, useRef, useEffect } from "react";
import { useRouter } from "next/navigation";
import { toast } from "sonner";

// Import auth modules
import { checkAuth, checkTokenExpiry } from "./auth-checks";
import { login, logout, forgotPassword, resetPassword, changePassword } from "./auth-operations";
import { setupAuthEventHandlers, setupTokenRefreshHandlers, checkInactiveAccountStatus, manualTokenRefresh } from "./auth-events";
import { hasRole, isAdmin, isSuperAdmin, isStaff } from "./auth-roles";

// Create auth context
export const AuthContext = createContext({});

/**
 * Authentication Provider Component
 * @param {Object} props - Component props
 * @param {React.ReactNode} props.children - Child components
 */
export function AuthProvider({ children }) {
  // State management
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const router = useRouter();
  
  // Track manual user updates to prevent auth checks from overwriting them
  const userUpdatedManually = useRef(false);
  const manualUpdateTimestamp = useRef(0);
  
  // Add refs to prevent concurrent API calls and infinite loops
  const authCheckInProgress = useRef(false);
  const lastAuthCheckTime = useRef(0);
  const authCheckInterval = 60000; // 1 minute interval between auth checks
  
  // Register router navigation for API usage and handle auth events
  useEffect(() => {
    const cleanup = setupAuthEventHandlers({
      setUser,
      setIsAuthenticated,
      router
    });
    
    return cleanup;
  }, [router]);

  // Check for inactive account status on login page load
  useEffect(() => {
    checkInactiveAccountStatus();
  }, []);
  
  // Check if user is logged in on initial load and validate role
  useEffect(() => {
    const checkAuthWrapper = async () => {
      await checkAuth({
        authCheckInProgress,
        lastAuthCheckTime,
        authCheckInterval,
        setUser,
        setIsAuthenticated,
        setLoading,
        setError,
        router,
        userUpdatedManually,
        manualUpdateTimestamp
      });
    };
    
    // Set up token refresh handlers
    const { cleanup } = setupTokenRefreshHandlers({
      checkAuth: checkAuthWrapper
    });
    
    // Only run on component mount
    if (!authCheckInProgress.current) {
      checkAuthWrapper();
    }
    
    return cleanup;
  }, [router]);
  
  // Login function wrapper
  const loginWrapper = async (email, password) => {
    return await login(
      { setLoading, setError, setUser, setIsAuthenticated },
      email,
      password
    );
  };

  // Logout function wrapper
  const logoutWrapper = async () => {
    return await logout(
      { setLoading, setUser, setIsAuthenticated, router }
    );
  };

  // Forgot password function wrapper
  const forgotPasswordWrapper = async (email) => {
    return await forgotPassword(
      { setLoading, setError },
      email
    );
  };

  // Reset password function wrapper
  const resetPasswordWrapper = async (token, newPassword, confirmPassword) => {
    return await resetPassword(
      { setLoading, setError },
      token,
      newPassword,
      confirmPassword
    );
  };

  // Change password function wrapper
  const changePasswordWrapper = async (currentPassword, newPassword) => {
    return await changePassword(
      { setLoading, setError },
      currentPassword,
      newPassword
    );
  };
  
  // Enhanced setUser wrapper that tracks manual updates
  const setUserWithTracking = (userData) => {
    setUser(userData);
    userUpdatedManually.current = true;
    manualUpdateTimestamp.current = Date.now();
    
    // Reset the flag after 30 seconds to allow auth checks to resume
    setTimeout(() => {
      userUpdatedManually.current = false;
    }, 30000);
  };
  
  // Role checking helpers
  const hasRoleWrapper = (role) => hasRole(user, role);
  const isAdminWrapper = () => isAdmin(user);
  const isSuperAdminWrapper = () => isSuperAdmin(user);
  const isStaffWrapper = () => isStaff(user);

  // Value provided to the context
  const value = {
    user,
    setUser: setUserWithTracking, // Use the enhanced setUser that tracks manual updates
    loading,
    error,
    login: loginWrapper,
    logout: logoutWrapper,
    forgotPassword: forgotPasswordWrapper,
    resetPassword: resetPasswordWrapper,
    refreshToken: manualTokenRefresh,
    changePassword: changePasswordWrapper,
    // Use the actual authentication state
    isAuthenticated,
    // Use helper methods for role checking - provide the functions, don't call them
    isSuperAdmin: isSuperAdminWrapper,
    isAdmin: isAdminWrapper,
    isStaff: isStaffWrapper,
    // Add a general role checker for flexibility
    hasRole: hasRoleWrapper,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}
