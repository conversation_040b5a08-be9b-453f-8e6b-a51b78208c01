"use client";

import { useState, useEffect, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/auth-context';
import { useSubscription } from '@/hooks/use-subscription';
import { usePaymentRetry } from '@/hooks/use-payment-retry';
import { toast } from 'sonner';
import { ResponsiveContainer } from '@/components/layout/responsive-container';
import { KpiCard } from '@/components/dashboard/common/kpi-card';
import { 
  RefreshCw
} from 'lucide-react';

// Import subscription components
import SubscriptionsHeader from '@/components/dashboard/subscriptions/subscriptions-header';
import SubscriptionsTable from '@/components/dashboard/subscriptions/subscriptions-table';
import { ExportDialog } from '@/components/dashboard/common/export-dialog';

export default function SubscriptionsPage() {
  const router = useRouter();
  const { isAuthenticated, isLoading, isSuperAdmin } = useAuth();
  
  // Authentication state tracking
  const [authReady, setAuthReady] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);
  
  // Export dialog state
  const [showExportDialog, setShowExportDialog] = useState(false);

  // Subscription management hook
  const {
    subscriptions,
    subscriptionStats,
    loading: subscriptionsLoading,
    pagination,
    fetchSubscriptions,
    fetchSubscriptionStats,
    triggerCronTasks,
    refreshData
  } = useSubscription();

  // Payment retry management hook
  const {
    retryConfig,
    loading: retryLoading,
    getRetryConfig,
    processAllPendingRetries
  } = usePaymentRetry();

  // Wait for authentication to be fully ready
  useEffect(() => {
    if (isLoading) return;
    
    if (!isAuthenticated) {
      router.push('/login');
      return;
    }
    
    // Only SuperAdmin access for web app
    if (!isSuperAdmin) {
      router.push('/unauthorized');
      return;
    }

    // Check if we have access token before proceeding
    const hasToken = typeof window !== 'undefined' && localStorage.getItem('accessToken');
    
    if (hasToken) {
      setAuthReady(true);
    } else {
      // Retry after a short delay
      setTimeout(() => {
        const retryHasToken = typeof window !== 'undefined' && localStorage.getItem('accessToken');
        if (retryHasToken) {
          setAuthReady(true);
        }
      }, 1000);
    }
  }, [isAuthenticated, isLoading, isSuperAdmin, router]);

  // Initialize data on mount
  useEffect(() => {
    if (authReady) {
      // Hook already initializes subscription data, just get retry config
      getRetryConfig();
    }
  }, [authReady, getRetryConfig]);

  // Handle refresh
  const handleRefresh = async () => {
    if (isRefreshing) return;
    
    setIsRefreshing(true);
    
    try {
      await refreshData();
      await getRetryConfig();
      toast.success("Subscription data refreshed successfully");
    } catch (error) {
      console.error('[SubscriptionsPage] Refresh error:', error);
      toast.error(`Failed to refresh subscriptions: ${error.message}`);
    } finally {
      setTimeout(() => {
        setIsRefreshing(false);
      }, 300);
    }
  };

  // Function to handle export
  const handleExport = () => {
    setShowExportDialog(true);
  };

  // Handle cron tasks trigger
  const handleTriggerCron = async () => {
    try {
      await triggerCronTasks();
      toast.success('Subscription cron tasks triggered successfully');
      // Refresh data after cron execution
      setTimeout(() => {
        refreshData();
      }, 2000);
    } catch (error) {
      toast.error('Failed to trigger cron tasks');
    }
  };

  // Handle process all pending retries
  const handleProcessAllRetries = async () => {
    try {
      await processAllPendingRetries();
      toast.success('All pending payment retries processed');
      // Refresh data after processing
      setTimeout(() => {
        refreshData();
      }, 1000);
    } catch (error) {
      toast.error('Failed to process pending retries');
    }
  };

  // Format currency values
  const formatCurrency = (value, currency = 'USD') => {
    if (!value) return '$0.00';
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency
    }).format(value);
  };

  // Format number values
  const formatNumber = (value) => {
    if (!value) return '0';
    return new Intl.NumberFormat('en-US').format(value);
  };

  // Calculate growth percentage
  const calculateGrowth = (current, previous) => {
    if (!previous || previous === 0) return 0;
    return ((current - previous) / previous * 100).toFixed(1);
  };

  // Show loading state while authentication is being checked
  if (isLoading || !authReady) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <RefreshCw className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  return (
    <ResponsiveContainer>
      <div className="space-y-6">
        {/* Header */}
        <SubscriptionsHeader 
          onRefresh={handleRefresh}
          onTriggerCron={handleTriggerCron}
          onExportClick={handleExport}
          isRefreshing={isRefreshing}
          isSuperAdmin={isSuperAdmin}
        />

        {/* KPI Cards */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <KpiCard
            title="Total Subscriptions"
            value={formatNumber(subscriptionStats?.totalSubscriptions || 0)}
            description="All subscription records"
            icon="users"
            trend={subscriptionStats?.growth?.subscriptions > 0 ? "up" : subscriptionStats?.growth?.subscriptions < 0 ? "down" : "neutral"}
            trendValue={`${calculateGrowth(subscriptionStats?.totalSubscriptions, subscriptionStats?.previous?.totalSubscriptions)}%`}
            trendLabel="from last month"
            loading={subscriptionsLoading}
          />
          
          <KpiCard
            title="Active Subscriptions"
            value={formatNumber(subscriptionStats?.activeSubscriptions || 0)}
            description="Currently active plans"
            icon="activity"
            trend={subscriptionStats?.growth?.active > 0 ? "up" : subscriptionStats?.growth?.active < 0 ? "down" : "neutral"}
            trendValue={`${calculateGrowth(subscriptionStats?.activeSubscriptions, subscriptionStats?.previous?.active)}%`}
            trendLabel="from last month"
            loading={subscriptionsLoading}
          />
          
          <KpiCard
            title="Monthly Revenue"
            value={formatCurrency(subscriptionStats?.totalRevenue || 0)}
            description="Total monthly revenue"
            icon="dollar"
            trend={subscriptionStats?.growth?.revenue > 0 ? "up" : subscriptionStats?.growth?.revenue < 0 ? "down" : "neutral"}
            trendValue={`${calculateGrowth(subscriptionStats?.totalRevenue, subscriptionStats?.previous?.revenue)}%`}
            trendLabel="from last month"
            loading={subscriptionsLoading}
          />
          
          <KpiCard
            title="Trial Subscriptions"
            value={formatNumber(subscriptionStats?.trialSubscriptions || 0)}
            description="Active trial periods"
            icon="calendar"
            trend={subscriptionStats?.growth?.trial > 0 ? "up" : subscriptionStats?.growth?.trial < 0 ? "down" : "neutral"}
            trendValue={`${calculateGrowth(subscriptionStats?.trialSubscriptions, subscriptionStats?.previous?.trial)}%`}
            trendLabel="from last month"
            loading={subscriptionsLoading}
          />
        </div>

        {/* Subscriptions Table with All Actions */}
        <SubscriptionsTable 
          subscriptions={subscriptions}
          isLoading={subscriptionsLoading}
          pagination={pagination}
          currentPage={pagination?.page || 1}
          pageSize={pagination?.limit || 10}
          totalItems={pagination?.total || 0}
          totalPages={pagination?.totalPages || 1}
          onPageChange={(page) => {
            fetchSubscriptions({ page });
          }}
          onPageSizeChange={(size) => {
            fetchSubscriptions({ limit: size, page: 1 }); // Reset to page 1 when changing page size
          }}
          showActions={true}
          isSuperAdmin={isSuperAdmin}
          enableBulkOperations={true}
          onRefresh={refreshData}
        />
        
        {/* Export Dialog */}
        <ExportDialog
          open={showExportDialog}
          onOpenChange={setShowExportDialog}
          exportType="subscriptions"
          title="Export Subscriptions"
          description="Export subscription data to CSV format"
        />
      </div>
    </ResponsiveContainer>
  );
} 