"use client";

import { useState, useCallback, useEffect, useRef } from 'react';
import { toast } from 'sonner';
import PlanService from '@/lib/services/plan';

/**
 * Plan Statistics Hook
 * 
 * Specialized hook for plan statistics with complex backend data handling
 * Handles revenue metrics, subscription counts, expiring plans, etc.
 */
export function usePlanStats(options = {}) {
  const {
    autoFetch = true,
    showToastMessages = true,
    fetchDelay = 400,
    refreshInterval = 300000, // 5 minutes
    enableAutoRefresh = false,
    onStatsChange,
    onError
  } = options;

  // Main stats state
  const [stats, setStats] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [lastUpdated, setLastUpdated] = useState(null);

  // Use refs to prevent duplicate requests and track state
  const isLoadingRef = useRef(false);
  const isMountedRef = useRef(true);
  const refreshIntervalRef = useRef(null);

  /**
   * Fetch plan statistics from backend
   * Handles the complex backend response format exactly
   */
  const fetchStats = useCallback(async (showLoadingToast = false) => {
    // Prevent duplicate requests
    if (isLoadingRef.current) {
      console.log('[usePlanStats] Request already in progress, skipping...');
      return;
    }

    try {
      isLoadingRef.current = true;
      setLoading(true);
      setError(null);

      if (showLoadingToast && showToastMessages) {
        toast.info('Loading plan statistics...');
      }

      console.log('[usePlanStats] Fetching plan statistics...');

      const response = await PlanService.getPlanStats();

      if (!isMountedRef.current) return;

      if (response.success) {
        const backendData = response.data;

        // Process the complex backend response structure
        const processedStats = {
          // Summary metrics (for KPI cards)
          summary: {
            totalPlans: backendData.summary?.totalPlans || 0,
            activePlans: backendData.summary?.activePlans || 0,
            inactivePlans: backendData.summary?.pendingPlans || 0, // backend calls them "pendingPlans"
            pendingPlans: backendData.summary?.pendingPlans || 0
          },

          // Most used plan information
          mostUsedPlan: backendData.summary?.mostUsedPlan ? {
            planId: backendData.summary.mostUsedPlan.planId,
            name: backendData.summary.mostUsedPlan.name,
            displayName: backendData.summary.mostUsedPlan.displayName,
            type: backendData.summary.mostUsedPlan.type,
            activeSubscriptions: backendData.summary.mostUsedPlan.activeSubscriptions
          } : null,

          // Detailed plan statistics
          planDetails: (backendData.planStats || []).map(planStat => ({
            planId: planStat.planId,
            name: planStat.name,
            displayName: planStat.displayName,
            type: planStat.type,
            isActive: planStat.isActive,
            
            // Performance metrics
            metrics: {
              activeSubscriptions: planStat.stats?.activeSubscriptions || 0,
              revenue: planStat.stats?.revenue || 0,
              expiringSoon: planStat.stats?.expiringSoon || 0,
              conversionRate: planStat.stats?.conversionRate || 0,
              averageLifetime: planStat.stats?.averageLifetime || 0
            }
          })),

          // Aggregated totals across all plans
          totals: {
            totalSubscriptions: backendData.totals?.totalSubscriptions || 0,
            totalRevenue: backendData.totals?.totalRevenue || 0,
            totalExpiringSoon: backendData.totals?.totalExpiringSoon || 0
          },

          // Calculated insights
          insights: {
            // Plan with highest revenue
            topRevenueGeneration: (() => {
              const plansByRevenue = (backendData.planStats || [])
                .filter(plan => plan.stats?.revenue > 0)
                .sort((a, b) => (b.stats?.revenue || 0) - (a.stats?.revenue || 0));
              return plansByRevenue[0] || null;
            })(),

            // Plan with most subscriptions
            mostPopularPlan: (() => {
              const plansBySubscriptions = (backendData.planStats || [])
                .filter(plan => plan.stats?.activeSubscriptions > 0)
                .sort((a, b) => (b.stats?.activeSubscriptions || 0) - (a.stats?.activeSubscriptions || 0));
              return plansBySubscriptions[0] || null;
            })(),

            // Plans needing attention (many expiring subscriptions)
            plansNeedingAttention: (backendData.planStats || [])
              .filter(plan => plan.stats?.expiringSoon > 0)
              .sort((a, b) => (b.stats?.expiringSoon || 0) - (a.stats?.expiringSoon || 0)),

            // Average metrics across all plans
            averageMetrics: (() => {
              const activePlans = (backendData.planStats || []).filter(plan => plan.isActive);
              const count = activePlans.length;
              
              if (count === 0) {
                return {
                  averageSubscriptions: 0,
                  averageRevenue: 0,
                  averageConversionRate: 0
                };
              }

              return {
                averageSubscriptions: activePlans.reduce((sum, plan) => 
                  sum + (plan.stats?.activeSubscriptions || 0), 0) / count,
                averageRevenue: activePlans.reduce((sum, plan) => 
                  sum + (plan.stats?.revenue || 0), 0) / count,
                averageConversionRate: activePlans.reduce((sum, plan) => 
                  sum + (plan.stats?.conversionRate || 0), 0) / count
              };
            })()
          }
        };

        setStats(processedStats);
        setLastUpdated(new Date());
        
        if (showLoadingToast && showToastMessages) {
          toast.success('Plan statistics loaded successfully');
        }

        // Trigger callback if provided
        if (onStatsChange) {
          onStatsChange(processedStats);
        }

        console.log('[usePlanStats] Statistics processed successfully:', processedStats);
      } else {
        throw new Error(response.message || 'Failed to load plan statistics');
      }
    } catch (err) {
      console.error('[usePlanStats] Error fetching plan statistics:', err);
      
      if (!isMountedRef.current) return;

      // Handle case where endpoint might not be fully implemented
      if (err.response?.status === 404 || err.message?.includes('not implemented')) {
        console.warn('[usePlanStats] Plan stats endpoint not fully implemented yet');
        
        // Set empty stats structure
        setStats({
          summary: { totalPlans: 0, activePlans: 0, inactivePlans: 0, pendingPlans: 0 },
          mostUsedPlan: null,
          planDetails: [],
          totals: { totalSubscriptions: 0, totalRevenue: 0, totalExpiringSoon: 0 },
          insights: {
            topRevenueGeneration: null,
            mostPopularPlan: null,
            plansNeedingAttention: [],
            averageMetrics: { averageSubscriptions: 0, averageRevenue: 0, averageConversionRate: 0 }
          }
        });

        if (showToastMessages) {
          toast.warning('Plan statistics are not available yet');
        }
        return;
      }

      const errorMessage = err.message || 'Failed to load plan statistics';
      setError(errorMessage);

      if (showToastMessages && !err.message?.includes('throttled')) {
        toast.error(errorMessage);
      }

      // Trigger error callback if provided
      if (onError) {
        onError(err);
      }
    } finally {
      if (isMountedRef.current) {
        setLoading(false);
        isLoadingRef.current = false;
      }
    }
  }, [showToastMessages, onStatsChange, onError]);

  /**
   * Refresh statistics manually
   */
  const refreshStats = useCallback((showToast = true) => {
    return fetchStats(showToast);
  }, [fetchStats]);

  /**
   * Get statistics for a specific plan type
   */
  const getStatsByPlanType = useCallback((planType) => {
    if (!stats?.planDetails) return null;
    
    return stats.planDetails.find(plan => plan.type === planType) || null;
  }, [stats]);

  /**
   * Get top performing plans by metric
   */
  const getTopPlansByMetric = useCallback((metric = 'activeSubscriptions', limit = 5) => {
    if (!stats?.planDetails) return [];
    
    return [...stats.planDetails]
      .sort((a, b) => (b.metrics?.[metric] || 0) - (a.metrics?.[metric] || 0))
      .slice(0, limit);
  }, [stats]);

  /**
   * Calculate percentage change (for future trend analysis)
   */
  const calculateGrowth = useCallback((current, previous) => {
    if (!previous || previous === 0) return current > 0 ? 100 : 0;
    return ((current - previous) / previous) * 100;
  }, []);

  // Setup auto-refresh if enabled
  useEffect(() => {
    if (!enableAutoRefresh || refreshInterval <= 0) return;

    refreshIntervalRef.current = setInterval(() => {
      fetchStats(false); // Silent refresh
    }, refreshInterval);

    return () => {
      if (refreshIntervalRef.current) {
        clearInterval(refreshIntervalRef.current);
      }
    };
  }, [enableAutoRefresh, refreshInterval, fetchStats]);

  // Auto-fetch on mount with delay to prevent React Strict Mode double calls
  useEffect(() => {
    if (!autoFetch) return;

    const timer = setTimeout(() => {
      fetchStats();
    }, fetchDelay);

    return () => clearTimeout(timer);
  }, [autoFetch, fetchDelay, fetchStats]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      isMountedRef.current = false;
      if (refreshIntervalRef.current) {
        clearInterval(refreshIntervalRef.current);
      }
    };
  }, []);

  return {
    // Main data
    stats,
    loading,
    error,
    lastUpdated,

    // Quick access to key metrics
    summary: stats?.summary || null,
    mostUsedPlan: stats?.mostUsedPlan || null,
    planDetails: stats?.planDetails || [],
    totals: stats?.totals || null,
    insights: stats?.insights || null,

    // Utility functions
    refreshStats,
    getStatsByPlanType,
    getTopPlansByMetric,
    calculateGrowth,

    // Control functions
    refetch: refreshStats,
    refresh: refreshStats
  };
} 