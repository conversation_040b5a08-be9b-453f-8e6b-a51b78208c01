/**
 * Test Contact Info Endpoint
 * Quick test to verify the /api/settings/contact-info endpoint implementation
 */

const express = require('express');
const cors = require('cors');

// Import the settings controller and routes
const settingsController = require('./src/controllers/settingsController');

// Create a minimal Express app for testing
const app = express();

// Basic middleware
app.use(cors());
app.use(express.json());

// Add the specific route we want to test
app.get('/api/settings/contact-info', settingsController.getContactInfo);

// Health check endpoint
app.get('/api/health', (req, res) => {
  res.json({ success: true, message: 'Test server is running' });
});

// Error handling middleware
app.use((error, req, res, next) => {
  console.error('Error:', error.message);
  res.status(error.status || 500).json({
    success: false,
    message: error.message || 'Internal server error'
  });
});

// Start test server
const PORT = 3001; // Use different port to avoid conflicts
const server = app.listen(PORT, () => {
  console.log(`🧪 Test server running on port ${PORT}`);
  console.log(`📡 Test endpoint: http://localhost:${PORT}/api/settings/contact-info`);
  
  // Test the endpoint
  testContactInfoEndpoint();
});

async function testContactInfoEndpoint() {
  console.log('\n🔄 Testing contact info endpoint...');
  
  try {
    const response = await fetch(`http://localhost:${PORT}/api/settings/contact-info`);
    const data = await response.json();
    
    console.log('\n📊 Test Results:');
    console.log('================');
    console.log(`Status: ${response.status}`);
    console.log(`Success: ${data.success}`);
    
    if (data.success && data.data) {
      console.log('\n📞 Contact Information:');
      console.log(`Name: ${data.data.name}`);
      console.log(`Phone: ${data.data.phone}`);
      console.log(`Email: ${data.data.email}`);
      console.log(`Support Hours: ${data.data.supportHours}`);
      console.log(`Timezone: ${data.data.timezone}`);
      
      console.log('\n✅ Endpoint is working correctly!');
    } else {
      console.log('\n❌ Endpoint returned unexpected response:', data);
    }
    
  } catch (error) {
    console.log('\n❌ Test failed:', error.message);
  }
  
  // Close the test server
  setTimeout(() => {
    server.close(() => {
      console.log('\n🔚 Test server closed');
      process.exit(0);
    });
  }, 1000);
}

// Handle process termination
process.on('SIGINT', () => {
  console.log('\n🛑 Test interrupted');
  server.close(() => {
    process.exit(0);
  });
});
