"use client";

import { useState, useEffect, useCallback, useMemo, useRef } from "react";
import { isEqual } from "lodash";
import { Card, CardHeader, CardContent, CardTitle, CardDescription } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Separator } from "@/components/ui/separator";
import { toast } from "sonner";
import { Info } from "lucide-react";

/**
 * General Settings Card Component
 * Displays and allows editing of global system settings
 */
export function GeneralSettingsCard({ 
  initialSettings, 
  onSave, 
  loading,
  readOnly = false 
}) {
  // Initialize settings with a default state - only once on mount
  // We use useMemo for the default state to avoid recreating it on every render
  const defaultSettings = useMemo(() => ({
    appName: "DeynCare",
    appVersion: "1.0.0",
    companyName: "",
    companyEmail: "",
    companyPhone: "",
    companyAddress: "",
    maintenance: false,
    enableNotifications: true,
    defaultCurrency: "USD",
  }), []);
  
  // Initialize with default settings, will be updated in useEffect if initialSettings exist
  const [settings, setSettings] = useState(defaultSettings);

  // Track previous settings to prevent unnecessary updates
  const prevInitialSettingsRef = useRef(null);
  
  // Update local state when props change, with deep equality check
  useEffect(() => {
    // Only set state if we have valid initialSettings and they've actually changed
    if (initialSettings && Object.keys(initialSettings).length > 0 && 
        !isEqual(initialSettings, prevInitialSettingsRef.current)) {
      // Update the ref to track the new value
      prevInitialSettingsRef.current = initialSettings;
      
      // Use functional update to avoid stale closures
      setSettings(() => ({
        // First apply defaults
        ...defaultSettings,
        // Then override with initialSettings
        ...initialSettings
      }));
    }
  }, [initialSettings, defaultSettings]); // Only run when initialSettings or defaultSettings change

  // Handle input changes - memoized to prevent unnecessary recreations
  const handleInputChange = useCallback((e) => {
    const { name, value } = e.target;
    setSettings(prev => ({
      ...prev,
      [name]: value
    }));
  }, []);

  // Handle toggle changes - memoized to prevent unnecessary recreations
  const handleToggleChange = useCallback((field) => {
    if (readOnly) {
      toast.error("You don't have permission to modify this setting");
      return;
    }
    
    // Use callback form to ensure we're working with the latest state
    setSettings(prev => ({
      ...prev,
      [field]: !prev[field]
    }));
  }, [readOnly]);

  // Save changes - memoized to prevent unnecessary recreations
  const handleSave = useCallback(() => {
    if (readOnly) {
      toast.error("You don't have permission to save these settings");
      return;
    }
    onSave(settings);
  }, [readOnly, onSave, settings]);

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>General System Settings</CardTitle>
        <CardDescription>
          Configure global application settings
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Application Information */}
        <div className="space-y-4">
          <h3 className="text-lg font-medium">Application Information</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="appName">Application Name</Label>
              <Input
                id="appName"
                name="appName"
                value={settings.appName}
                onChange={handleInputChange}
                readOnly={readOnly}
                className={readOnly ? "bg-muted cursor-not-allowed" : ""}
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="appVersion">Version</Label>
              <Input
                id="appVersion"
                name="appVersion"
                value={settings.appVersion}
                readOnly
                className="bg-muted cursor-not-allowed"
              />
              <p className="text-xs text-muted-foreground">
                System version is managed automatically
              </p>
            </div>
          </div>
        </div>
        
        <Separator />
        
        {/* Company Information */}
        <div className="space-y-4">
          <h3 className="text-lg font-medium">Company Information</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="companyName">Company Name</Label>
              <Input
                id="companyName"
                name="companyName"
                value={settings.companyName}
                onChange={handleInputChange}
                readOnly={readOnly}
                className={readOnly ? "bg-muted cursor-not-allowed" : ""}
                placeholder="Your company name"
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="companyEmail">Email Address</Label>
              <Input
                id="companyEmail"
                name="companyEmail"
                type="email"
                value={settings.companyEmail}
                onChange={handleInputChange}
                readOnly={readOnly}
                className={readOnly ? "bg-muted cursor-not-allowed" : ""}
                placeholder="<EMAIL>"
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="companyPhone">Phone Number</Label>
              <Input
                id="companyPhone"
                name="companyPhone"
                value={settings.companyPhone}
                onChange={handleInputChange}
                readOnly={readOnly}
                className={readOnly ? "bg-muted cursor-not-allowed" : ""}
                placeholder="****** 567 8900"
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="defaultCurrency">Default Currency</Label>
              <Input
                id="defaultCurrency"
                name="defaultCurrency"
                value={settings.defaultCurrency}
                onChange={handleInputChange}
                readOnly={readOnly}
                className={readOnly ? "bg-muted cursor-not-allowed" : ""}
                placeholder="USD"
              />
            </div>
            
            <div className="space-y-2 md:col-span-2">
              <Label htmlFor="companyAddress">Address</Label>
              <Input
                id="companyAddress"
                name="companyAddress"
                value={settings.companyAddress}
                onChange={handleInputChange}
                readOnly={readOnly}
                className={readOnly ? "bg-muted cursor-not-allowed" : ""}
                placeholder="123 Business St, City, Country"
              />
            </div>
          </div>
        </div>
        
        <Separator />
        
        {/* System Options */}
        <div className="space-y-4">
          <h3 className="text-lg font-medium">System Options</h3>
          
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="maintenance">Maintenance Mode</Label>
                <p className="text-sm text-muted-foreground">
                  Temporarily disable the system for maintenance
                </p>
              </div>
              <Switch
                id="maintenance"
                checked={settings.maintenance}
                onCheckedChange={() => handleToggleChange('maintenance')}
                disabled={readOnly}
              />
            </div>
            
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="enableNotifications">System Notifications</Label>
                <p className="text-sm text-muted-foreground">
                  Enable email and in-app notifications
                </p>
              </div>
              <Switch
                id="enableNotifications"
                checked={settings.enableNotifications}
                onCheckedChange={() => handleToggleChange('enableNotifications')}
                disabled={readOnly}
              />
            </div>
          </div>
        </div>
        
        {readOnly && (
          <div className="p-4 bg-blue-50 rounded-md flex items-start space-x-3 text-blue-800">
            <Info className="h-5 w-5 mt-0.5 text-blue-500" />
            <div>
              <p className="font-medium">SuperAdmin Access Required</p>
              <p className="text-sm">
                Only SuperAdmin users can modify general system settings. These settings affect the entire application.
              </p>
            </div>
          </div>
        )}
        
        {!readOnly && (
          <Button 
            onClick={handleSave}
            disabled={loading || readOnly}
            className="w-full mt-4"
          >
            {loading ? "Saving..." : "Save Settings"}
          </Button>
        )}
      </CardContent>
    </Card>
  );
}
