/**
 * Subscription Routes
 * Handles all routes related to subscription management
 * Refactored to use modular controller imports like the Plan system
 */
const express = require('express');
const subscriptionController = require('../controllers/subscription');
const authMiddleware = require('../middleware/authMiddleware');
const { validate } = require('../middleware/validationMiddleware');
const { subscriptionSchemas } = require('../validations');
const upload = require('../middleware/uploadMiddleware');

const router = express.Router();

/**
 * Subscription Routes
 * Base path: /api/subscriptions
 */

// Require authentication for all subscription routes
router.use(authMiddleware.authenticate);

// Get current user's subscription - Must come before /:subscriptionId route
router.get('/current', subscriptionController.getCurrentSubscription);

// Get subscription history
router.get('/history', subscriptionController.getSubscriptionHistory);

// Request subscription upgrade (sends email to SuperAdmin)
router.post(
  '/request-upgrade',
  validate(subscriptionSchemas.requestUpgrade),
  subscriptionController.requestUpgrade
);

// Change subscription plan (between monthly/yearly)
router.post(
  '/change-plan',
  validate(subscriptionSchemas.changePlan),
  subscriptionController.changePlan
);

// Change subscription plan (PUT method for Flutter compatibility)
router.put(
  '/change-plan',
  validate(subscriptionSchemas.changePlan),
  subscriptionController.changePlan
);

// Cancel subscription
router.post(
  '/cancel',
  validate(subscriptionSchemas.cancelSubscription),
  subscriptionController.cancelSubscription
);

// Record payment for a subscription
router.post(
  '/payment',
  validate(subscriptionSchemas.recordPayment),
  subscriptionController.recordPayment
);

// Update auto-renewal settings
router.patch(
  '/auto-renewal',
  validate(subscriptionSchemas.updateAutoRenewal),
  subscriptionController.updateAutoRenewal
);

// Renew subscription
router.post(
  '/renew',
  validate(subscriptionSchemas.renewSubscription),
  subscriptionController.renewSubscription
);





// Get all subscriptions (admin only)
router.get(
  '/',
  authMiddleware.authorize(['admin', 'superAdmin']),
  subscriptionController.getAllSubscriptions
);

// Get subscription stats (SuperAdmin only)
router.get(
  '/stats',
  authMiddleware.authorize('superAdmin'),
  subscriptionController.getSubscriptionStats
);

// Bulk update subscriptions (SuperAdmin only)
router.post(
  '/bulk',
  authMiddleware.authorize('superAdmin'),
  subscriptionController.bulkUpdateSubscriptions
);

// Run subscription cron tasks on demand (SuperAdmin only)
router.post(
  '/cron/run',
  authMiddleware.authorize('superAdmin'),
  subscriptionController.manageCronTasks
);

// Payment retry management routes (SuperAdmin only)

// Get payment retry status for a subscription
router.get(
  '/payment-retry/:subscriptionId/status',
  authMiddleware.authorize('superAdmin'),
  subscriptionController.getPaymentRetryStatus
);

// Trigger manual payment retry for a subscription
router.post(
  '/payment-retry/:subscriptionId/trigger',
  authMiddleware.authorize('superAdmin'),
  subscriptionController.triggerManualRetry
);

// Cancel scheduled retries for a subscription
router.post(
  '/payment-retry/:subscriptionId/cancel',
  authMiddleware.authorize('superAdmin'),
  subscriptionController.cancelScheduledRetries
);

// Process all pending payment retries
router.post(
  '/payment-retry/process-all',
  authMiddleware.authorize('superAdmin'),
  subscriptionController.processAllPendingRetries
);

// Get payment retry configuration
router.get(
  '/payment-retry/config',
  authMiddleware.authorize('superAdmin'),
  subscriptionController.getRetryConfiguration
);

// Get subscription by ID - This must come after other specific routes to avoid conflicts
router.get('/:subscriptionId', subscriptionController.getSubscriptionById);

// Extend subscription (admin only)
router.post(
  '/:subscriptionId/extend',
  authMiddleware.authorize(['admin', 'superAdmin']),
  validate(subscriptionSchemas.extendSubscription),
  subscriptionController.extendSubscription
);

module.exports = router;
