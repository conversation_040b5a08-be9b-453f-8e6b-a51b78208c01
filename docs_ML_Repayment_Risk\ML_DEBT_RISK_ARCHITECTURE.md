# 🤖 ML SERVICE & DEBT MANAGEMENT - END-TO-<PERSON><PERSON> ARCHITECTURE

## 🏗️ **SYSTEM ARCHITECTURE OVERVIEW**

The ML service is deeply integrated with debt management and customer risk profiling through a sophisticated pipeline that evaluates customer creditworthiness based on payment behavior.

---

## 📊 **CORE MODELS & DATA FLOW**

### **1. Customer Model** (`customer.model.js`)
```javascript
riskProfile: {
  currentRiskLevel: 'Low Risk' | 'Medium Risk' | 'High Risk' | 'Critical Risk',
  riskScore: 0-100,           // ML-generated risk score
  lastAssessment: Date,       // When ML last evaluated
  assessmentCount: Number,    // Total evaluations
  mlSource: 'ml_api' | 'system' | 'manual'
}
```

### **2. Debt Model** (`debt.model.js`)
```javascript
// Core ML Input Fields
DebtPaidRatio: 0-1,          // Payment completion ratio
PaymentDelay: Number,        // Days late/early
OutstandingDebt: Number,     // Remaining amount
DebtAmount: Number,          // Original amount
RiskLevel: String,           // ML output classification
```

---

## 🔄 **ML EVALUATION PIPELINE**

### **STEP 1: Debt Creation** ✅
**File:** `createDebt.js`
- ✅ **No ML Evaluation**: New debts start as "Active Debt"
- ✅ **Reason**: No payment behavior data yet
- ✅ **Status**: Waiting for due date to pass

```javascript
// Initial state
debt.RiskLevel = 'Active Debt'  // No evaluation yet
mlInfo: {
  evaluationStatus: 'Pending',
  message: 'Risk evaluation will trigger automatically after due date'
}
```

### **STEP 2: Due Date Monitoring** 🕒
**File:** `mlRiskEvaluation.js` (Cron Job)
- ⏰ **Runs**: Every hour
- 🔍 **Checks**: `DueDate < now AND RiskLevel = 'Active Debt'`
- 🎯 **Purpose**: Identify debts ready for ML evaluation

### **STEP 3: ML Data Preparation** 📊
**File:** `mlRiskService.js` → `prepareMLPayload()`
```javascript
// FastAPI expects exactly 4 fields
{
  DebtPaidRatio: debt.DebtPaidRatio,    // 0-1 payment completion
  PaymentDelay: debt.PaymentDelay,      // Days late (+ or -)
  OutstandingDebt: debt.OutstandingDebt, // Remaining amount
  DebtAmount: debt.DebtAmount           // Original amount
}
```

### **STEP 4: ML API Call** 🌐
**File:** `mlRiskService.js` → `callMLAPI()`
- 🎯 **Endpoint**: `https://deyncare-ml.onrender.com/predict_single/`
- ⏱️ **Timeout**: 10 seconds
- 📤 **Sends**: 4-field payload
- 📥 **Receives**: `{RiskScore: 0-1, PredictedRiskLevel: 'Low/Medium/High Risk'}`

### **STEP 5: Risk Classification** 🎯
**File:** `mlRiskService.js` → `classifyRisk()`
```javascript
// Risk Score Mapping
riskScore <= 0.3  → 'Low Risk' ✅
0.3 < riskScore <= 0.6 → 'Medium Risk' ⚠️
riskScore > 0.6   → 'High Risk' ❌
```

---

## 🎯 **TRIGGER POINTS FOR ML EVALUATION**

### **✅ WHEN ML RUNS:**

#### **1. Payment Recording** (`addPayment.js`)
```javascript
// Only if due date has passed
const isDueDatePassed = now > dueDate;
if (isDueDatePassed) {
  mlEvaluation = await mlService.evaluateRisk(debt, customer);
  // Update debt and customer risk profiles
}
```

#### **2. Automated Cron Job** (`mlRiskEvaluation.js`)
```javascript
// Every hour, finds overdue debts not yet evaluated
const overdueDebts = await Debt.find({
  DueDate: { $lt: now },
  RiskLevel: 'Active Debt',  // Not yet ML evaluated
  OutstandingDebt: { $gt: 0 }
});
```

#### **3. Manual Admin Trigger** (API endpoint)
- Admins can force re-evaluation
- Useful for testing or corrections

### **❌ WHEN ML DOES NOT RUN:**

#### **1. Debt Creation** 
- ❌ No payment history exists
- ✅ Set to "Active Debt" status

#### **2. Debt Editing** (✅ **FIXED ISSUE**)
- ❌ **Previously**: ML ran on every edit (causing 11s delays)
- ✅ **Now**: ML skipped for simple edits
- 🎯 **Reason**: Editing amount ≠ payment behavior change

#### **3. Before Due Date**
- ❌ No evaluation until customer has chance to pay
- ✅ Prevents premature risk assessment

---

## 🔧 **RISK PROFILE MANAGEMENT**

### **Customer Risk Profile Updates** (`riskScoreService.js`)
```javascript
await Customer.findOneAndUpdate(
  { customerId },
  {
    'riskProfile.riskScore': mlResult.riskScore,
    'riskProfile.currentRiskLevel': mlResult.riskLevel,
    'riskProfile.lastAssessment': new Date(),
    'riskProfile.mlSource': 'ml_api',
    $inc: { 'riskProfile.assessmentCount': 1 }
  }
);
```

### **Debt Risk Level Updates**
```javascript
debt.RiskLevel = mlEvaluation.riskLevel;  // 'Low/Medium/High Risk'
await debt.save();
```

---

## 🚀 **FALLBACK MECHANISMS**

### **1. ML API Timeout/Failure**
**File:** `mlRiskService.js` → `fallbackRiskAssessment()`
```javascript
// Rule-based calculation when ML fails
let riskScore = 0;
riskScore += (1 - paymentRatio) * 40;      // 40% weight
riskScore += Math.min(delayDays / 30, 1) * 30; // 30% weight
if (customerType === 'New') riskScore += 20;   // 20% weight
riskScore += outstandingRatio * 10;             // 10% weight
```

### **2. ML Service Disabled**
- Uses fallback assessment
- Graceful degradation
- System continues functioning

---

## 📈 **BUSINESS LOGIC FLOW**

### **Scenario 1: Good Customer** ✅
1. **Debt Created**: $1000, due in 30 days → "Active Debt"
2. **Payment Made**: $1000 on day 25 → ML evaluates → "Low Risk"
3. **Customer Profile**: Updated to "Low Risk" with high score

### **Scenario 2: Late Customer** ⚠️
1. **Debt Created**: $500, due in 15 days → "Active Debt"  
2. **Due Date Passes**: Cron job triggers ML evaluation
3. **Payment Made**: $300 on day 20 (5 days late) → "Medium Risk"
4. **Customer Profile**: Updated to "Medium Risk"

### **Scenario 3: Default Customer** ❌
1. **Debt Created**: $2000, due in 45 days → "Active Debt"
2. **Due Date Passes**: No payment → ML evaluates → "High Risk"
3. **Partial Payment**: $200 on day 60 (15 days late) → Remains "High Risk"

---

## 🎯 **PERFORMANCE OPTIMIZATIONS**

### **1. Settings Caching** (5-minute cache)
```javascript
this.settingsCache = {};
this.lastSettingsUpdate = null;
this.settingsCacheExpiry = 5 * 60 * 1000;
```

### **2. Batch Processing**
- Cron job processes multiple debts efficiently
- Avoids individual API calls

### **3. Conditional Evaluation**
- Only evaluates when business logic requires
- Skips unnecessary ML calls

---

## 🔒 **SECURITY & RELIABILITY**

### **1. Timeout Protection**
```javascript
timeout: this.mlTimeout  // 10 seconds max
```

### **2. Error Handling**
```javascript
catch (mlError) {
  // Fallback to rule-based assessment
  return this.fallbackRiskAssessment(debt, customer);
}
```

### **3. Database Consistency**
- Transactions ensure data integrity
- Rollback on failures

---

## 📊 **MONITORING & ANALYTICS**

### **1. Risk Distribution Tracking**
```javascript
// Aggregated risk statistics
{
  processed: 15,
  highRisk: 3,
  mediumRisk: 7,
  lowRisk: 5,
  errors: 0
}
```

### **2. ML API Health Monitoring**
- Timeout tracking
- Success/failure rates
- Performance metrics

---

## 🎉 **SUMMARY: INTELLIGENT ARCHITECTURE**

### **✅ STRENGTHS:**
- 🧠 **Smart Timing**: ML only runs when needed
- 🔄 **Fallback Ready**: Works even if ML API fails  
- ⚡ **Performance**: Optimized for real-world usage
- 📊 **Data-Driven**: Uses actual payment behavior
- 🔒 **Reliable**: Error handling and timeouts

### **🎯 BUSINESS VALUE:**
- 💰 **Risk Assessment**: Accurate customer evaluation
- 📈 **Predictive**: Identifies high-risk customers early
- 🔍 **Actionable**: Enables informed lending decisions
- ⚡ **Efficient**: Fast operations for day-to-day use

This architecture provides a robust, intelligent system that balances accuracy with performance, ensuring debt management is both efficient and insightful. 