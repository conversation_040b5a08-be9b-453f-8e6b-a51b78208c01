"use client";

import { useState, useEffect } from 'react';

/**
 * Advanced hook for handling responsive design and device detection
 * 
 * Features:
 * - Breakpoint detection (xs, sm, md, lg, xl, 2xl)
 * - Device type detection (mobile, tablet, desktop)
 * - Orientation detection (portrait, landscape)
 * - Touch capability detection
 * - Reduced motion preference detection
 * 
 * @returns {Object} Responsive state object
 */
export function useResponsive() {
  // Initialize with default values for SSR
  const [state, setState] = useState({
    // Breakpoints based on Tailwind's default breakpoints
    breakpoint: 'xs',
    isXs: true,
    isSm: false,
    isMd: false,
    isLg: false,
    isXl: false,
    is2Xl: false,
    
    // Device type
    isMobile: true,
    isTablet: false,
    isDesktop: false,
    
    // Orientation
    orientation: 'portrait',
    isPortrait: true,
    isLandscape: false,
    
    // Capabilities
    hasTouchScreen: false,
    prefersReducedMotion: false,
    
    // Window dimensions
    width: 0,
    height: 0
  });
  
  useEffect(() => {
    // Function to update all responsive states
    const updateResponsiveState = () => {
      const width = window.innerWidth;
      const height = window.innerHeight;
      
      // Determine breakpoint
      let breakpoint = 'xs';
      const isXs = width < 640;
      const isSm = width >= 640 && width < 768;
      const isMd = width >= 768 && width < 1024;
      const isLg = width >= 1024 && width < 1280;
      const isXl = width >= 1280 && width < 1536;
      const is2Xl = width >= 1536;
      
      if (is2Xl) breakpoint = '2xl';
      else if (isXl) breakpoint = 'xl';
      else if (isLg) breakpoint = 'lg';
      else if (isMd) breakpoint = 'md';
      else if (isSm) breakpoint = 'sm';
      
      // Determine device type (approximate)
      const isMobile = width < 768;
      const isTablet = width >= 768 && width < 1024;
      const isDesktop = width >= 1024;
      
      // Determine orientation
      const orientation = height >= width ? 'portrait' : 'landscape';
      const isPortrait = orientation === 'portrait';
      const isLandscape = orientation === 'landscape';
      
      // Check for touch capability
      const hasTouchScreen = 'ontouchstart' in window || 
        navigator.maxTouchPoints > 0 || 
        navigator.msMaxTouchPoints > 0;
      
      // Check reduced motion preference
      const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;
      
      // Update state with all values
      setState({
        breakpoint,
        isXs, isSm, isMd, isLg, isXl, is2Xl,
        isMobile, isTablet, isDesktop,
        orientation, isPortrait, isLandscape,
        hasTouchScreen,
        prefersReducedMotion,
        width,
        height
      });
    };
    
    // Add event listener for resize
    window.addEventListener('resize', updateResponsiveState);
    
    // Update on first render
    updateResponsiveState();
    
    // Clean up
    return () => window.removeEventListener('resize', updateResponsiveState);
  }, []);
  
  // Utility functions
  const isBreakpointUp = (breakpoint) => {
    const breakpoints = { xs: 0, sm: 640, md: 768, lg: 1024, xl: 1280, '2xl': 1536 };
    return state.width >= breakpoints[breakpoint];
  };
  
  const isBreakpointDown = (breakpoint) => {
    const breakpoints = { xs: 0, sm: 640, md: 768, lg: 1024, xl: 1280, '2xl': 1536 };
    return state.width < breakpoints[breakpoint];
  };
  
  // Return state and utility functions
  return {
    ...state,
    isBreakpointUp,
    isBreakpointDown
  };
}

/**
 * Component media query hook for responsive design
 * Usage: const isLarge = useMediaQuery('(min-width: 1024px)');
 * 
 * @param {string} query CSS media query
 * @returns {boolean} Whether the query matches
 */
export function useMediaQuery(query) {
  const [matches, setMatches] = useState(false);

  useEffect(() => {
    const mediaQuery = window.matchMedia(query);
    
    // Initial check
    setMatches(mediaQuery.matches);
    
    // Update when query changes
    const handler = (event) => setMatches(event.matches);
    mediaQuery.addEventListener('change', handler);
    
    // Clean up
    return () => mediaQuery.removeEventListener('change', handler);
  }, [query]);

  return matches;
}

/**
 * Hook to control animations based on user preferences
 * Takes into account reduced motion preferences
 * 
 * @param {boolean} defaultEnabled Whether animations are enabled by default
 * @returns {boolean} Whether animations should be enabled
 */
export function useAnimationPreference(defaultEnabled = true) {
  const prefersReducedMotion = useMediaQuery('(prefers-reduced-motion: reduce)');
  
  // If the user prefers reduced motion, disable animations
  // Otherwise, use the default setting
  return !prefersReducedMotion && defaultEnabled;
}
