/**
 * Settings Context - Security Settings Actions
 * Contains actions for managing security settings
 */

import SettingsService from '@/lib/services/settings';
import { ACTION_TYPES } from '../types';

/**
 * Fetch security settings with throttling and improved error handling
 * @param {Function} dispatch - Reducer dispatch function
 * @param {Function} throttleRequest - Request throttling function
 * @returns {Promise} Result of the operation
 */
export const fetchSecuritySettings = (dispatch, throttleRequest) => {
  return throttleRequest('fetchSecuritySettings', async () => {
    try {
      dispatch({ type: ACTION_TYPES.SET_LOADING, payload: true });
      const settings = await SettingsService.fetchSecuritySettings();
      dispatch({ type: ACTION_TYPES.SET_SECURITY_SETTINGS, payload: settings });
      dispatch({ type: ACTION_TYPES.SET_ERROR, payload: null }); // Clear any previous errors
      return { success: true, data: settings };
    } catch (error) {
      console.error('Security settings fetch error:', error);
      const errorMessage = error.response?.status === 500 
        ? 'Server error loading security settings. Using default settings.'
        : error.message || 'Failed to load security settings';
      
      dispatch({ type: ACTION_TYPES.SET_ERROR, payload: errorMessage });
      return { success: false, error: errorMessage };
    } finally {
      dispatch({ type: ACTION_TYPES.SET_LOADING, payload: false });
    }
  });
};

/**
 * Update security settings
 * @param {Function} dispatch - Reducer dispatch function
 * @param {Function} throttleRequest - Request throttling function
 * @param {Object} securitySettings - Updated security settings
 * @returns {Promise} Result of the operation
 */
export const updateSecuritySettings = (dispatch, throttleRequest, securitySettings) => {
  return throttleRequest('updateSecuritySettings', async () => {
    try {
      dispatch({ type: ACTION_TYPES.SET_LOADING, payload: true });
      const result = await SettingsService.updateSecuritySettings(securitySettings);
      dispatch({ type: ACTION_TYPES.SET_SECURITY_SETTINGS, payload: securitySettings });
      dispatch({ type: ACTION_TYPES.SET_ERROR, payload: null }); // Clear any previous errors
      return { success: true, data: result };
    } catch (error) {
      console.error('Security settings update error:', error);
      const errorMessage = error.response?.status === 500 
        ? 'Server error updating security settings. Please try again later.'
        : error.message || 'Failed to update security settings';
      
      dispatch({ type: ACTION_TYPES.SET_ERROR, payload: errorMessage });
      return { success: false, error: errorMessage };
    } finally {
      dispatch({ type: ACTION_TYPES.SET_LOADING, payload: false });
    }
  });
};
