"use client";

import { 
  Star,
  Quote,
  Users,
  Store,
  TrendingUp,
  Shield,
  CheckCircle,
  ArrowRight
} from "lucide-react";
import { <PERSON>, CardContent, CardHeader } from "../ui/card";
import { Badge } from "../ui/badge";
import { <PERSON><PERSON> } from "../ui/button";
import Link from "next/link";

const testimonials = [
  {
    name: "<PERSON>",
    role: "Shop Owner",
    business: "Hassan Electronics",
    location: "Mogadishu",
    avatar: "AH",
    rating: 5,
    quote: "DeynCare transformed how we manage customer debts. The AI risk assessment helped us reduce bad debts by 40% and the EVC Plus integration made payments seamless.",
    results: {
      improvement: "40% reduction in bad debts",
      metric: "Payment efficiency increased by 60%"
    },
    features: ["AI Risk Assessment", "EVC Plus Integration", "Debt Tracking"]
  },
  {
    name: "Fatima Ali",
    role: "SuperAdmin",
    business: "Barakaat Business Network",
    location: "Hargeisa",
    avatar: "FA",
    rating: 5,
    quote: "Managing 50+ shops across Somalia was impossible before DeynCare. Now I can monitor everything in real-time, from subscriptions to analytics. The platform is incredibly powerful.",
    results: {
      improvement: "50+ shops managed seamlessly",
      metric: "90% time savings on administration"
    },
    features: ["Multi-shop Management", "Real-time Analytics", "Automated Notifications"]
  },
  {
    name: "Mohamed Jama",
    role: "Admin",
    business: "Jama Wholesale Trading",
    location: "Kismayo",
    avatar: "MJ",
    rating: 5,
    quote: "The subscription management and automated payment retries have been game-changers. We never miss renewals anymore and our cash flow is much more predictable.",
    results: {
      improvement: "100% renewal rate",
      metric: "Predictable cash flow achieved"
    },
    features: ["Subscription Management", "Payment Automation", "Financial Reporting"]
  }
];

const successMetrics = [
  {
    metric: "94.7%",
    label: "Average Debt Recovery Rate",
    description: "Businesses using DeynCare see significantly higher debt collection success"
  },
  {
    metric: "60%",
    label: "Time Savings",
    description: "Reduction in administrative tasks through automation and smart workflows"
  },
  {
    metric: "85%",
    label: "Customer Satisfaction",
    description: "Improved customer experience through better payment options and communication"
  },
  {
    metric: "2.5x",
    label: "Revenue Growth",
    description: "Average revenue increase within 6 months of implementing DeynCare"
  }
];

const TestimonialCard = ({ testimonial }) => (
  <Card className="h-full border border-slate-200 dark:border-slate-700 hover:shadow-lg transition-all duration-300 bg-white dark:bg-slate-800">
    <CardHeader className="pb-4">
      <div className="flex items-start justify-between mb-4">
        <div className="flex items-center gap-3">
          <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center text-white font-semibold">
            {testimonial.avatar}
          </div>
          <div>
            <div className="font-semibold text-slate-900 dark:text-white">{testimonial.name}</div>
            <div className="text-sm text-slate-600 dark:text-slate-300">{testimonial.role}</div>
            <div className="text-xs text-slate-500 dark:text-slate-400">{testimonial.business} • {testimonial.location}</div>
          </div>
        </div>
        <div className="flex gap-1">
          {[...Array(testimonial.rating)].map((_, i) => (
            <Star key={i} className="w-4 h-4 fill-yellow-400 text-yellow-400" />
          ))}
        </div>
      </div>
    </CardHeader>
    <CardContent>
      <div className="relative mb-6">
        <Quote className="w-8 h-8 text-slate-300 dark:text-slate-600 mb-2" />
        <p className="text-slate-700 dark:text-slate-300 leading-relaxed italic">
          "{testimonial.quote}"
        </p>
      </div>

      <div className="space-y-4">
        {/* Results */}
        <div className="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg border border-green-100 dark:border-green-800">
          <div className="text-sm font-medium text-green-800 dark:text-green-300 mb-1">Key Results:</div>
          <div className="text-sm text-green-700 dark:text-green-400">• {testimonial.results.improvement}</div>
          <div className="text-sm text-green-700 dark:text-green-400">• {testimonial.results.metric}</div>
        </div>

        {/* Features Used */}
        <div>
          <div className="text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">Features Used:</div>
          <div className="flex flex-wrap gap-2">
            {testimonial.features.map((feature, index) => (
              <Badge key={index} variant="secondary" className="text-xs bg-slate-100 dark:bg-slate-700 text-slate-700 dark:text-slate-300">
                {feature}
              </Badge>
            ))}
          </div>
        </div>
      </div>
    </CardContent>
  </Card>
);

export default function Testimonials() {
  return (
    <section className="py-24 bg-white dark:bg-slate-900">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <div className="text-center mb-16">
          <Badge className="mb-4 bg-green-100 dark:bg-green-900/50 text-green-700 dark:text-green-300 border-green-200 dark:border-green-700">
            Success Stories
          </Badge>
          <h2 className="text-3xl md:text-4xl font-bold text-slate-900 dark:text-white mb-6">
            Trusted by Businesses Across Somalia
          </h2>
          <p className="text-lg text-slate-600 dark:text-slate-300 max-w-3xl mx-auto">
            From small retail shops to large enterprise networks, businesses of all sizes trust DeynCare 
            to streamline their operations and drive growth.
          </p>
        </div>

        {/* Success Metrics */}
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-6 mb-16">
          {successMetrics.map((item, index) => (
            <div key={index} className="text-center">
              <div className="text-3xl md:text-4xl font-bold text-slate-900 dark:text-white mb-2">
                {item.metric}
              </div>
              <div className="font-semibold text-slate-700 dark:text-slate-300 mb-1">{item.label}</div>
              <div className="text-sm text-slate-600 dark:text-slate-400">{item.description}</div>
            </div>
          ))}
        </div>

        {/* Testimonials */}
        <div className="grid lg:grid-cols-3 gap-8 mb-16">
          {testimonials.map((testimonial, index) => (
            <TestimonialCard key={index} testimonial={testimonial} />
          ))}
        </div>

        {/* CTA Section */}
        <div className="text-center">
          <div className="bg-gradient-to-r from-slate-50 to-blue-50 dark:from-slate-800 dark:to-blue-900/30 rounded-2xl p-8 md:p-12 border border-slate-200 dark:border-slate-700">
            <div className="max-w-3xl mx-auto">
              <h3 className="text-2xl md:text-3xl font-bold text-slate-900 dark:text-white mb-4">
                Join Thousands of Successful Businesses
              </h3>
              <p className="text-lg text-slate-600 dark:text-slate-300 mb-8">
                Start your journey with DeynCare today and experience the difference that intelligent 
                business management can make for your operations.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button asChild size="lg" className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white">
                  <Link href="/login" className="flex items-center gap-2">
                    Start Your Free Trial
                    <ArrowRight className="w-4 h-4" />
                  </Link>
                </Button>
                <Button asChild variant="outline" size="lg" className="border-slate-300 dark:border-slate-600 dark:text-slate-300 dark:hover:bg-slate-800">
                  <Link href="#contact">
                    Schedule a Demo
                  </Link>
                </Button>
              </div>
              <div className="mt-6 text-sm text-slate-500 dark:text-slate-400">
                No credit card required • 14-day free trial • Setup in minutes
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
} 