/**
 * App Upload Routes
 * Routes for app file upload and management
 */
const express = require('express');
const router = express.Router();

// Import controllers and middleware
const appUploadController = require('../controllers/appUploadController');
const { authenticate } = require('../middleware/authMiddleware');
const { validate, validateQuery, validateParams } = require('../middleware/validationMiddleware');
const appUploadSchemas = require('../validations/schemas/appUploadSchemas');

// Public routes (no authentication required)
/**
 * @route   GET /api/app/download/latest
 * @desc    Download latest app version (public endpoint)
 * @access  Public
 */
router.get('/download/latest', appUploadController.downloadLatest);

/**
 * @route   GET /api/app/latest/:platform
 * @desc    Get latest app by platform (public endpoint)
 * @access  Public
 */
router.get('/latest/:platform', 
  validateParams(appUploadSchemas.getLatestByPlatform),
  appUploadController.getLatestByPlatform
);

/**
 * @route   GET /api/app/download/:uploadId
 * @desc    Download app by upload ID (public endpoint)
 * @access  Public
 */
router.get('/download/:uploadId',
  validateParams(appUploadSchemas.downloadApp),
  appUploadController.downloadApp
);

// Protected routes (authentication required)
/**
 * @route   POST /api/app/upload
 * @desc    Upload new app file (SuperAdmin only)
 * @access  Private (SuperAdmin)
 */
router.post('/upload',
  authenticate,
  appUploadController.upload.single('appFile'),
  validate(appUploadSchemas.uploadApp),
  appUploadController.uploadApp
);

/**
 * @route   GET /api/app/list
 * @desc    Get all app uploads with filters (SuperAdmin only)
 * @access  Private (SuperAdmin)
 */
router.get('/list',
  authenticate,
  validateQuery(appUploadSchemas.getApps),
  appUploadController.getApps
);

/**
 * @route   GET /api/app/stats
 * @desc    Get download statistics (SuperAdmin only)
 * @access  Private (SuperAdmin)
 */
router.get('/stats',
  authenticate,
  validateQuery(appUploadSchemas.getAppStats),
  appUploadController.getStats
);

/**
 * @route   GET /api/app/:uploadId
 * @desc    Get app upload by ID
 * @access  Private
 */
router.get('/:uploadId',
  authenticate,
  validateParams(appUploadSchemas.getAppById),
  appUploadController.getAppById
);

/**
 * @route   PUT /api/app/:uploadId
 * @desc    Update app metadata (SuperAdmin only)
 * @access  Private (SuperAdmin)
 */
router.put('/:uploadId',
  authenticate,
  validateParams(appUploadSchemas.updateApp),
  validate(appUploadSchemas.updateApp),
  appUploadController.updateApp
);

/**
 * @route   PUT /api/app/:uploadId/set-latest
 * @desc    Set app as latest version (SuperAdmin only)
 * @access  Private (SuperAdmin)
 */
router.put('/:uploadId/set-latest',
  authenticate,
  validateParams(appUploadSchemas.setLatestVersion),
  validate(appUploadSchemas.setLatestVersion),
  appUploadController.setLatestVersion
);

/**
 * @route   DELETE /api/app/:uploadId
 * @desc    Delete app upload (SuperAdmin only)
 * @access  Private (SuperAdmin)
 */
router.delete('/:uploadId',
  authenticate,
  validateParams(appUploadSchemas.deleteApp),
  appUploadController.deleteApp
);

module.exports = router; 