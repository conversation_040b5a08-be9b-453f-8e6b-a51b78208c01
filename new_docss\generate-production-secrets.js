#!/usr/bin/env node

const crypto = require('crypto');

console.log('🔐 DeynCare Production Secrets Generator');
console.log('=' .repeat(50));

// Generate secure random strings
function generateSecret(length = 64) {
  return crypto.randomBytes(length).toString('hex');
}

function generateBase64Secret(length = 32) {
  return crypto.randomBytes(length).toString('base64');
}

console.log('\n📝 Copy these secrets to your production .env file:\n');

console.log('## JWT Secrets');
console.log(`JWT_ACCESS_SECRET=${generateSecret(32)}`);
console.log(`JWT_REFRESH_SECRET=${generateSecret(32)}`);

console.log('\n## Session & Cookie Secrets');
console.log(`COOKIE_SECRET=${generateSecret(16)}`);
console.log(`SESSION_SECRET=${generateSecret(16)}`);
console.log(`CSRF_SECRET=${generateSecret(16)}`);
console.log(`TOKEN_SECRET=${generateSecret(16)}`);

console.log('\n## Additional Security Keys');
console.log(`API_SECRET_KEY=${generateSecret(24)}`);
console.log(`ENCRYPTION_KEY=${generateSecret(16)}`);

console.log('\n🔒 Keep these secrets secure and never commit them to version control!');
console.log('💡 Each secret is cryptographically random and unique.');
console.log('\n⚠️  IMPORTANT: After updating your .env file, restart your application.');

console.log('\n🎯 Production Deployment Checklist:');
console.log('✅ 1. Update .env with generated secrets');
console.log('✅ 2. Change NODE_ENV=production');
console.log('✅ 3. Update CORS_ORIGIN to your domain');
console.log('✅ 4. Update FRONTEND_URL to your domain');  
console.log('✅ 5. Set SESSION_SECURE=true');
console.log('✅ 6. Update admin credentials');
console.log('✅ 7. Test all endpoints');
console.log('✅ 8. Deploy with Docker'); 