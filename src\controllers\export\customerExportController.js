/**
 * Customer Export Controller
 * Handles export requests for customer data (Admin role)
 */
const BaseExportController = require('./baseExportController');
const CustomerService = require('../../services/customerService');
const { logError } = require('../../utils');

class CustomerExportController extends BaseExportController {
  /**
   * Get customer export fields
   * @param {Object} req - Express request object
   * @returns {Array} Field configurations
   */
  static async getExportFields(req) {
    return [
      {
        label: 'Customer ID',
        key: 'customerId',
        type: 'string'
      },
      {
        label: 'Full Name',
        key: 'fullName',
        type: 'string'
      },
      {
        label: 'Phone',
        key: 'phone',
        type: 'string'
      },
      {
        label: 'Email',
        key: 'email',
        type: 'string'
      },
      {
        label: 'Address',
        key: 'address',
        type: 'string'
      },
      {
        label: 'City',
        key: 'city',
        type: 'string'
      },
      {
        label: 'Region',
        key: 'region',
        type: 'string'
      },
      {
        label: 'Country',
        key: 'country',
        type: 'string'
      },
      {
        label: 'Status',
        key: 'status',
        type: 'string'
      },
      {
        label: 'Is Active',
        key: 'isActive',
        type: 'boolean'
      },
      {
        label: 'Total Debts',
        key: 'totalDebts',
        type: 'number'
      },
      {
        label: 'Total Debt Amount',
        key: 'totalDebtAmount',
        type: 'currency',
        currency: 'USD'
      },
      {
        label: 'Outstanding Balance',
        key: 'outstandingBalance',
        type: 'currency',
        currency: 'USD'
      },
      {
        label: 'Risk Level',
        key: 'riskLevel',
        type: 'string'
      },
      {
        label: 'Risk Score',
        key: 'riskScore',
        type: 'number'
      },
      {
        label: 'Last Payment Date',
        key: 'lastPaymentDate',
        type: 'date'
      },
      {
        label: 'Created At',
        key: 'createdAt',
        type: 'datetime'
      },
      {
        label: 'Updated At',
        key: 'updatedAt',
        type: 'datetime'
      }
    ];
  }

  /**
   * Get customer data for export
   * @param {Object} req - Express request object
   * @returns {Array} Customer data
   */
  static async getExportData(req) {
    try {
      const { 
        status, 
        isActive, 
        riskLevel,
        city,
        region,
        country,
        startDate,
        endDate
      } = req.query;
      
      const { shopId } = req.user; // Admin can only export their shop's customers
      
      const filters = { shopId };
      if (status) filters.status = status;
      if (isActive !== undefined) filters.isActive = isActive === 'true';
      if (riskLevel) filters.riskLevel = riskLevel;
      if (city) filters.city = city;
      if (region) filters.region = region;
      if (country) filters.country = country;
      
      if (startDate || endDate) {
        filters.createdAt = {};
        if (startDate) filters.createdAt.$gte = new Date(startDate);
        if (endDate) filters.createdAt.$lte = new Date(endDate);
      }

      // Get all customers for this shop (no limit for export)
      const result = await CustomerService.getAllCustomers(filters, 1, 0);
      
      return result.docs;
    } catch (error) {
      logError('Failed to get customer data for export', 'CustomerExportController', error);
      throw error;
    }
  }

  /**
   * Export customers to CSV
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next middleware function
   */
  static async exportToCSV(req, res, next) {
    req.exportConfig = {
      module: 'customers',
      getFields: this.getExportFields,
      getData: this.getExportData,
      filename: req.query.filename || 'customer_export'
    };

    await this.prepareExport(req, res, next);
    return super.exportToCSV(req, res, next);
  }

  /**
   * Export customers to Excel
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next middleware function
   */
  static async exportToExcel(req, res, next) {
    req.exportConfig = {
      module: 'customers',
      getFields: this.getExportFields,
      getData: this.getExportData,
      filename: req.query.filename || 'customer_export',
      options: {
        sheetName: 'Customers',
        styling: {
          header: true,
          columns: {
            'fullName': { width: 20 },
            'phone': { width: 15 },
            'email': { width: 25 },
            'address': { width: 30 },
            'totalDebtAmount': { width: 15, alignment: { horizontal: 'right' } },
            'outstandingBalance': { width: 15, alignment: { horizontal: 'right' } },
            'riskLevel': { width: 12 },
            'riskScore': { width: 12 },
            'lastPaymentDate': { width: 15 },
            'createdAt': { width: 20 },
            'updatedAt': { width: 20 }
          }
        }
      }
    };

    await this.prepareExport(req, res, next);
    return super.exportToExcel(req, res, next);
  }
}

module.exports = CustomerExportController; 