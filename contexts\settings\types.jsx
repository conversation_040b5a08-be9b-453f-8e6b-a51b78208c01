/**
 * Settings Context - Action Types
 * Defines all possible actions that can be dispatched to the settings reducer
 */

export const ACTION_TYPES = {
  SET_SETTINGS: 'SET_SETTINGS',
  SET_LOADING: 'SET_LOADING',
  SET_ERROR: 'SET_ERROR',
  CLEAR_ERROR: 'C<PERSON>AR_ERROR',
  SET_SECURITY_SETTINGS: 'SET_SECURITY_SETTINGS',
  // Shop settings removed - functionality consolidated with plan-based limitations
  SET_SYSTEM_LOGS: 'SET_SYSTEM_LOGS',
  UPDATE_SETTINGS: 'UPDATE_SETTINGS',
  SET_INITIAL_DATA: 'SET_INITIAL_DATA' // New action for batched updates
};
