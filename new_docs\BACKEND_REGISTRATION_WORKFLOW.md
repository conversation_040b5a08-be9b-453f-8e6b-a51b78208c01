# Backend Registration Workflow Documentation

This document outlines the current backend registration logic for both self-registration (public) and SuperAdmin-initiated registration of admin roles. It also highlights existing issues and proposes best practices.

---

## 1. Self-Registration Workflow (`POST /api/auth/register`)

This endpoint is used by new shop owners to register themselves and their shop.

**Current Workflow:**

1.  **Request Reception:** The `register` function in `src/controllers/auth/registrationController.js` receives `fullName`, `email`, `phone`, `password` (user data), `shopName`, `shopAddress` (shop data), and `planType`, `paymentMethod`, `initialPaid`, `discountCode` (subscription/payment data).
2.  **Payment Details Auto-Population:** If `paymentMethod` is 'EVC Plus' and `initialPaid` is `true`, the `phoneNumber` from the registration data is automatically used for `paymentDetails` if not already provided.
3.  **Payment Method Availability Check:** The system queries `Setting` model to ensure the chosen `paymentMethod` is enabled globally (`payment_methods_available`, `enable_online_payment`, `enable_offline_payment`). If the method is disabled, an `AppError` (400) is thrown.
4.  **Initiate Database Transaction:** A MongoDB transaction is started using `TransactionHelper.withTransaction` (named `UserRegistration`). All subsequent database operations until commit/abort are part of this atomic unit.
5.  **Shop Logo Upload (if provided):** If a `shopLogo` file is part of the request, `FileUploadService.saveShopLogo` is called to store the logo. (Logs show `DUPLICATE FILEID DETECTING. RETRYING WITH` and `SHOP LOGO SAVED ON RETRY`, indicating a robust upload mechanism).
6.  **Discount Code Verification:** If a `discountCode` is provided, `DiscountService.verifyDiscountCode` is called. If invalid, an `AppError` (400) is thrown, and the transaction would abort.
7.  **EVC Plus Payment Attempt (Conditional):**
    *   If `paymentMethod` is 'EVC Plus', `initialPaid` is `true`, and `paymentDetails.phoneNumber` exists, `EVCPaymentService.retryPayment` is called.
    *   The `planPrice` is calculated, considering any valid discount.
    *   **Current Workflow Issue (Design Choice - "Payment Before Verification"):** The payment is attempted at this stage. If the EVC Plus payment fails (e.g., "Insufficient balance," "Invalid PIN code" as seen in logs), the error is logged, and the `initialPaid` flag within the `shopData.subscription` is set to `false`. **The registration process *continues* despite payment failure.**
8.  **Shop Creation:** `ShopService.createShop` is called with `shopData` (including subscription details and `session`). A unique `shopId` is generated, and the shop document is conceptually created within the transaction. (Logs show `SHOP CREATED: SHOP008`).
9.  **User Creation:** `UserService.createUser` is called with `userData` (including `fullName`, `email`, `phone`, `password`, `role: 'admin'`, the generated `shopId`, `registeredBy: 'self'`, and a `verificationCode`). The user document is created within the same transaction. (Logs show `USER CREATED: USR013`).
10. **Attempt to Link User as Shop Owner:**
    *   The code attempts to update the newly created shop with the `ownerId` using `await ShopService.updateShop(shopId, { ownerId: userData.userId }, session);`.
    *   **Critical Issue (Transaction Isolation Bug):** Inside `ShopService.updateShop`, a `Shop.findOne({ shopId }, null, { session });` query is performed. Due to MongoDB's transaction isolation levels, this `findOne` query often **fails to "see" the shop document that was just created** moments earlier within the same transaction. This results in `Shop.findOne` returning `null`.
    *   **Transaction Abort:** When `Shop.findOne` returns `null`, `ShopService.updateShop` throws `AppError('Shop not found', 404, 'shop_not_found')`. This error propagates up to the `TransactionHelper`, which then **aborts the entire `UserRegistration` transaction**.
11. **Data Rollback:** Because the transaction is aborted, **neither the shop nor the user documents are actually persisted to the database**, even though the "created" logs appeared.
12. **Error Response:** The client receives a `404 Not Found` response with the message "Shop not found".
13. **Verification Email/Token (Never Reached):** Because the transaction aborts, the subsequent steps of sending a verification email and generating a JWT token for immediate login are never reached.

---

## 2. SuperAdmin Registration Workflow (`POST /api/auth/create-employee` for Admin Role)

This endpoint is used by a SuperAdmin to create new admin (or employee) accounts for shops. We are focusing on `role: 'admin'` creation here.

**Current Workflow (`req.user.role === 'superAdmin'` and `role === 'admin'`):**

1.  **Request Reception:** The `createEmployee` function in `src/controllers/auth/registrationController.js` receives `fullName`, `email`, `phone`, `role` (set to 'admin'), `password`, and `shopId`.
2.  **Authorization Check:** The system verifies that the authenticated user (`req.user.role`) is a `superAdmin`.
3.  **Role Validation:** It checks if the requested `role` ('admin') is valid for SuperAdmins to create.
4.  **Shop ID Requirement:** It asserts that a `shopId` is provided when creating an 'admin' account.
5.  **Initiate Database Transaction:** A MongoDB transaction is started using `TransactionHelper.withTransaction` (named `SuperAdminUserCreation`).
6.  **Generate Verification Code:** A `verificationCode` is generated within the transaction.
7.  **User Creation:** `UserService.createUser` is called with the provided user details, including `role: 'admin'`, the specified `shopId`, `registeredBy: req.user.userId`, and the `verificationCode`. The user document is created within the transaction.
8.  **Audit Log:** `LogHelper.createUserLog` records the `admin_created` event within the transaction.
9.  **Transaction Commit:** If the user creation succeeds, the transaction commits, and the new admin user document is persisted to the database.
10. **Send Welcome Email (outside transaction):** `EmailService.sendAdminWelcomeEmail` is called to send a welcome email to the newly created admin user, including the verification code.
11. **Success Response:** A success response is returned to the SuperAdmin, confirming the admin account creation.

**Issues/Observations:**

*   This flow seems more robust because there's no immediate `ShopService.updateShop` call after the user creation that would trigger the transaction isolation bug seen in self-registration.
*   The `shopId` must exist *prior* to this operation; this flow doesn't create new shops.

---

### Summary of Current Issues

1.  **Critical Transaction Isolation Bug in Self-Registration:** The primary issue is that the `ShopService.updateShop` call immediately after shop creation in self-registration (within the same transaction) causes a `Shop.findOne` to fail, leading to the entire transaction aborting and no data being persisted. This makes self-registration fundamentally non-functional for successful persistence.
2.  **"Payment Before Verification" Workflow (Self-Registration):** The current design attempts payment even if the user's email is not yet verified. While the backend handles payment failure gracefully, this order is often considered a "low logic" issue for user experience and risk management, as it involves financial transactions with unverified identities.
3.  **Potential for Confusing User State:** If the payment fails but the transaction *were* to succeed (after fixing the isolation bug), the user would have a registered account (unverified) and a shop (unpaid/trial), requiring further steps for a fully active, paid subscription.

---

### Best Practice Workflow Overview

A more robust and user-friendly workflow would typically separate the initial registration/verification from the payment and full activation, especially for paid subscriptions.

#### Best Practice for Self-Registration (Public)

1.  **Step 1: Initial Registration & Email Verification Request (`POST /api/auth/register`)**
    *   Collect `fullName`, `email`, `phone`, `password`, `shopName`, `shopAddress`, `planType`.
    *   **Create User and Shop as `pending_verification`**: Create the `User` and `Shop` documents in the database. Mark `User.status` as `pending_email_verification` and `User.verified` as `false`. Shop status also `pending`.
    *   **Crucial:** **DO NOT** attempt any payment processing at this stage.
    *   Generate a `verificationCode` and persist it with the user.
    *   Send a verification email to the user.
    *   Return a response indicating that registration is successful and the user needs to check their email for verification. **Do not return full auth tokens or redirect to a dashboard yet.**

2.  **Step 2: Email Verification (`POST /api/auth/verify-email`)**
    *   The user clicks a link or enters a code from the email.
    *   Your backend's `/api/auth/verify-email` endpoint handles this.
    *   Upon successful email verification:
        *   Update the `User` status to `active` and `User.verified` to `true`.
        *   Update the `Shop` status to `active`.
        *   **Conditional Next Step:**
            *   If `planType` is **free** or `paymentMethod` is **offline**: Generate an authentication token and redirect the user to their dashboard or a welcome screen.
            *   If `planType` is **paid** and `initialPaid: true` was selected:
                *   Redirect the user to a dedicated **payment initiation screen** in the mobile app.
                *   **DO NOT** automatically generate a full auth token or log them in yet, as payment is still pending.
                *   The payment screen can collect `paymentMethod` and `paymentDetails`.

3.  **Step 3: Payment Processing (for Paid Plans, Post-Verification)**
    *   User explicitly initiates payment from the payment screen.
    *   The backend's payment endpoint (e.g., a new `/api/payment/process-evc`) handles the transaction.
    *   If payment is successful:
        *   Update the `Shop.subscription` details (e.g., `initialPaid: true`, `paymentDate`, `transactionId`).
        *   Generate a full authentication token.
        *   Log the user in and redirect to their dashboard.
    *   If payment fails:
        *   Inform the user of the failure and provide options to retry payment.
        *   The user's account remains active (as email is verified), but the subscription status is `pending_payment`.

#### Best Practice for SuperAdmin Registration (Admin Role)

1.  **Request `POST /api/auth/create-employee`**: SuperAdmin provides user and shop details, and `role: 'admin'`.
2.  **Authorization & Validation**: Confirm SuperAdmin role and valid inputs.
3.  **Create User (`pending_verification`):** Create the `User` document with `role: 'admin'` and the specified `shopId`. Set `User.status` to `pending_email_verification` and `User.verified` to `false`.
4.  **Send Welcome/Verification Email:** Send an email to the new admin user, including a verification link/code.
5.  **Confirmation to SuperAdmin**: Return a success message to the SuperAdmin, indicating the admin account was created and email verification is pending for the new admin.
6.  **New Admin's Verification:** The newly created admin user then follows the email verification process (similar to Step 2 above) to activate their account and gain access to the shop. 