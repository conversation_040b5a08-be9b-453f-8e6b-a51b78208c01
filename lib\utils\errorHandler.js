import { toast } from 'sonner';

/**
 * Centralized Error Handler
 * Consolidates all error handling patterns across the application
 */

/**
 * Centralized error handling utilities
 * Handles different types of errors consistently across the application
 */

/**
 * Extract a human-readable error message from various error types
 * @param {any} error - Error object, string, or other error type
 * @param {string} defaultMessage - Default message if no error message found
 * @returns {string} Human-readable error message
 */
export const getErrorMessage = (error, defaultMessage = 'An unexpected error occurred') => {
  // Handle string errors
  if (typeof error === 'string') {
    return error;
  }
  
  // Handle null/undefined
  if (!error) {
    return defaultMessage;
  }
  
  // Handle React Query errors and Axios errors
  if (error.response?.data?.message) {
    return error.response.data.message;
  }
  
  // Handle objects with message property
  if (error.message) {
    return error.message;
  }
  
  // Handle API response errors
  if (error.data?.message) {
    return error.data.message;
  }
  
  // Handle error with error property (nested errors)
  if (error.error?.message) {
    return error.error.message;
  }
  
  // Handle status text for HTTP errors
  if (error.statusText) {
    return error.statusText;
  }
  
  // Fallback to string representation
  if (typeof error === 'object') {
    try {
      return JSON.stringify(error);
    } catch (e) {
      return defaultMessage;
    }
  }
  
  return defaultMessage;
};

/**
 * Enhanced error handler for API calls
 * @param {Error} error - The error object
 * @param {string} context - Context where error occurred (e.g., 'UserService.createUser')
 * @param {Object} options - Error handling options
 * @param {boolean} options.showToast - Whether to show toast notification (default: true)
 * @param {string} options.defaultMessage - Default error message if none found
 * @param {boolean} options.throwError - Whether to rethrow the error (default: false)
 * @returns {Object} Standardized error object
 */
export const handleApiError = (error, context, options = {}) => {
  const {
    showToast = true,
    defaultMessage = 'Operation failed',
    throwError = false
  } = options;

  const message = getErrorMessage(error, defaultMessage);
  
  // Log detailed error for debugging
  console.error(`[${context}] Error:`, {
    message,
    status: error.response?.status,
    endpoint: error.config?.url,
    method: error.config?.method?.toUpperCase(),
    data: error.response?.data
  });
  
  // Show toast notification if needed
  if (showToast && typeof window !== 'undefined') {
    toast.error(message);
  }
  
  // Create standardized error object
  const standardError = {
    message,
    isNetworkError: !error.response && error.request,
    isAuthError: error.response?.status === 401 || error.response?.status === 403,
    isValidationError: error.response?.status === 400 || error.response?.status === 422,
    isServerError: error.response?.status >= 500,
    isRateLimited: error.response?.status === 429,
    statusCode: error.response?.status || 500,
    originalError: error
  };

  // Add validation errors if present
  if (standardError.isValidationError) {
    standardError.validationErrors = error.response?.data?.errors || 
                                   error.response?.data?.validationErrors || 
                                   null;
  }
  
  if (throwError) {
    // Attach additional properties to the original error
    Object.assign(error, {
      friendlyMessage: message,
      isServerError: standardError.isServerError,
      isAuthError: standardError.isAuthError,
      isRateLimited: standardError.isRateLimited
    });
    throw error;
  }
  
  return standardError;
};

/**
 * Success handler for consistent success messaging
 * @param {string} message - Success message
 * @param {Object} data - Optional data to return
 * @returns {Object} Success response
 */
export const handleApiSuccess = (message, data = null) => {
  toast.success(message);
  return {
    success: true,
    message,
    data
  };
};

/**
 * Format validation errors for display
 * @param {Object|Array} errors - Validation errors
 * @returns {Array} Formatted error array
 */
export const formatValidationErrors = (errors) => {
  if (!errors) return [];
  
  if (Array.isArray(errors)) {
    return errors;
  }
  
  if (typeof errors === 'object') {
    return Object.entries(errors).map(([field, message]) => ({
      field,
      message: Array.isArray(message) ? message[0] : message
    }));
  }
  
  return [{ field: 'general', message: errors.toString() }];
};

export default handleApiError; 