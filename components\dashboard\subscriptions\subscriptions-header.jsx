import React from 'react';
import { RefreshCw, Settings, Download, Plus } from 'lucide-react';
import { Button } from '@/components/ui/button';

/**
 * SubscriptionsHeader Component
 * Header section for the subscriptions dashboard with actions and title
 * 
 * @param {Object} props - Component props
 * @param {Function} props.onRefresh - Callback for refresh action
 * @param {Function} props.onTriggerCron - Callback for triggering cron tasks
 * @param {Function} props.onExportClick - Callback for export action
 * @param {boolean} props.isRefreshing - Whether data is currently refreshing
 * @param {boolean} props.isSuperAdmin - Whether user is SuperAdmin
 * @returns {JSX.Element} Rendered component
 */
const SubscriptionsHeader = ({
  onRefresh,
  onTriggerCron,
  onExportClick,
  isRefreshing = false,
  isSuperAdmin = false
}) => {
  return (
    <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
      {/* Title Section */}
      <div className="flex items-center gap-3">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Subscription Management</h1>
          <p className="text-muted-foreground">
            Manage all subscriptions across the platform
          </p>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex flex-wrap gap-2">
        <Button
          onClick={onRefresh}
          disabled={isRefreshing}
          variant="outline"
          size="sm"
        >
          <RefreshCw className={`h-4 w-4 mr-2 ${isRefreshing ? 'animate-spin' : ''}`} />
          {isRefreshing ? 'Refreshing...' : 'Refresh'}
        </Button>

        <Button
          onClick={onTriggerCron}
          disabled={isRefreshing}
          variant="outline"
          size="sm"
        >
          <Settings className="h-4 w-4 mr-2" />
          Run Cron
        </Button>

        <Button
          onClick={onExportClick}
          variant="outline"
          size="sm"
          disabled={isRefreshing}
        >
          <Download className="h-4 w-4 mr-2" />
          Export
        </Button>
      </div>
    </div>
  );
};

export default SubscriptionsHeader; 