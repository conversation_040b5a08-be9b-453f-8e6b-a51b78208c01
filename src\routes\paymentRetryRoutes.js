/**
 * Payment Retry Routes
 * Routes for handling payment retry functionality
 */

const express = require('express');
const router = express.Router();
const { authenticate, authorize } = require('../middleware/authMiddleware');
const { validate } = require('../middleware/validationMiddleware');
const paymentRetryController = require('../controllers/auth/paymentRetryController');

/**
 * Check if user can retry payment
 * GET /api/auth/payment-retry/check/:userId
 */
router.get('/check/:userId', 
  authenticate, 
  paymentRetryController.checkPaymentRetryEligibility
);

/**
 * Retry payment for eligible user
 * POST /api/auth/payment-retry/:userId
 */
router.post('/:userId',
  authenticate,
  paymentRetryController.retryPayment
);

/**
 * Reset user to allow new registration (admin only)
 * DELETE /api/auth/payment-retry/:userId/reset
 */
router.delete('/:userId/reset',
  authenticate,
  authorize(['admin', 'superAdmin']),
  paymentRetryController.resetUserForNewRegistration
);

module.exports = router; 