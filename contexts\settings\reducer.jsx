"use client";

/**
 * Settings Context - Reducer
 * Manages state transitions for the settings context
 */

import { ACTION_TYPES } from './types';
import formatSettingsForDisplay from '@/lib/services/settings/utils/formatSettingsForDisplay';

/**
 * Reducer for settings state
 * @param {Object} state - Current state
 * @param {Object} action - Action to perform
 * @returns {Object} New state
 */
export const settingsReducer = (state, action) => {
  switch (action.type) {
    case ACTION_TYPES.SET_SETTINGS:
      return { 
        ...state, 
        settings: action.payload,
        settingsByCategory: formatSettingsForDisplay(action.payload),
        loading: false 
      };
      
    case ACTION_TYPES.SET_LOADING:
      return { ...state, loading: action.payload };
      
    case ACTION_TYPES.SET_ERROR:
      return { ...state, error: action.payload, loading: false };
      
    case ACTION_TYPES.CLEAR_ERROR:
      return { ...state, error: null };
      
    case ACTION_TYPES.SET_SECURITY_SETTINGS:
      return {
        ...state,
        securitySettings: action.payload,
        loading: false
      };
      
    // Shop settings case removed - functionality consolidated with plan-based limitations
      
    case ACTION_TYPES.SET_SYSTEM_LOGS:
      return {
        ...state,
        systemLogs: action.payload,
        loading: false
      };
      
    case ACTION_TYPES.UPDATE_SETTINGS:
      // Ensure state.settings is an array before calling map
      const currentSettings = Array.isArray(state.settings) ? state.settings : [];
      const updatedSettings = currentSettings.map(setting => 
        setting.key === action.payload.key ? action.payload : setting
      );
      
      return {
        ...state,
        settings: updatedSettings,
        settingsByCategory: formatSettingsForDisplay(updatedSettings),
        loading: false
      };
      
    case ACTION_TYPES.SET_INITIAL_DATA:
      // Handle initial data loading
      return {
        ...state,
        settings: action.payload.settings || state.settings,
        settingsByCategory: action.payload.settings ? 
          formatSettingsForDisplay(action.payload.settings) : 
          state.settingsByCategory,
        loading: false
      };
      
    default:
      return state;
  }
};