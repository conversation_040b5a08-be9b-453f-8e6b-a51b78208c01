import apiBridge from '@/lib/api/bridge';
import { ENDPOINTS } from '@/lib/api/contract';
import { handleError, processApiResponse } from '../baseService';

/**
 * Send test notification to current user (Backend-matched)
 * Backend sends a predefined test message to all user's active FCM tokens
 * @returns {Promise<Object>} Test result
 */
async function sendTestNotification() {
  try {
    // Make API request using the bridge (no parameters - backend uses predefined message)
    const response = await apiBridge.post(ENDPOINTS.NOTIFICATIONS.FCM.TEST, {}, {
      skipCache: true
    });

    // Process response using utility
    const result = processApiResponse(response, 'Test notification sent successfully');
    return result;
  } catch (error) {
    handleError(error, 'NotificationService.sendTestNotification', true);
    throw error;
  }
}

export default sendTestNotification; 