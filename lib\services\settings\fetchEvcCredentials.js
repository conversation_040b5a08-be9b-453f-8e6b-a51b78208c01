/**
 * Fetch EVC Credentials Service
 * Matches backend: settingsController.getEVCCredentials
 * GET /api/settings/evc-credentials
 */

import { getEvcCredentials } from '../../api/modules/settings';
import { handleError, logApiCall } from '../baseService';

/**
 * Fetch EVC payment credentials
 * @param {string|null} shopId - Shop ID for shop-specific credentials, null for global
 * @returns {Promise<Object>} EVC credentials data
 */
const fetchEvcCredentials = async (shopId = null) => {
  const context = `fetchEvcCredentials${shopId ? `(shopId: ${shopId})` : '(global)'}`;
  
  try {
    logApiCall(context, 'GET /api/settings/evc-credentials', { shopId });
    
    const response = await getEvcCredentials(shopId);
    
    if (response?.success) {
      console.log(`[${context}] Success:`, {
        hasCredentials: response.data && Object.keys(response.data).length > 0,
        message: response.message
      });
      
      // Return the exact format from backend
      return {
        success: true,
        data: response.data || {},
        message: response.message
      };
    } else {
      console.warn(`[${context}] API returned success=false:`, response);
      return {
        success: false,
        data: {},
        message: response?.message || 'Failed to fetch EVC credentials'
      };
    }
    
  } catch (error) {
    console.error(`[${context}] Error:`, error);
    handleError(error, context, false);
    
    // Return consistent error format
    return {
      success: false,
      data: {},
      message: error.message || 'Failed to fetch EVC credentials',
      error: error
    };
  }
};

export default fetchEvcCredentials; 