/**
 * Subscription Export Service
 * Handles export functionality for subscriptions
 */
const ExportService = require('./exportService');
const { SubscriptionService } = require('../');
const { logError } = require('../../utils');

class SubscriptionExportService {
  /**
   * Get fields configuration for subscription exports
   * @returns {Array} Array of field configurations
   */
  static getExportFields() {
    return [
      { label: 'Shop Name', key: 'shop.name' },
      { label: 'Plan Name', key: 'plan.name' },
      { label: 'Plan Type', key: 'plan.type' },
      { label: 'Status', key: 'status' },
      { label: 'Start Date', key: 'startDate' },
      { label: 'End Date', key: 'endDate' },
      { label: 'Auto Renew', key: 'autoRenew' },
      { label: 'Last Payment', key: 'lastPayment.amount' },
      { label: 'Payment Method', key: 'lastPayment.method' },
      { label: 'Created At', key: 'createdAt' },
      { label: 'Updated At', key: 'updatedAt' }
    ];
  }

  /**
   * Export subscriptions to CSV
   * @param {Object} filter - Filter criteria
   * @returns {String} CSV string
   */
  static async exportToCSV(filter = {}) {
    try {
      const subscriptions = await SubscriptionService.getAllSubscriptions(filter, { limit: 0 });
      const fields = this.getExportFields();
      return await ExportService.toCSV(subscriptions.data, fields);
    } catch (error) {
      logError('Failed to export subscriptions to CSV', 'SubscriptionExportService', error);
      throw error;
    }
  }

  /**
   * Export subscriptions to Excel
   * @param {Object} filter - Filter criteria
   * @returns {Buffer} Excel file buffer
   */
  static async exportToExcel(filter = {}) {
    try {
      const subscriptions = await SubscriptionService.getAllSubscriptions(filter, { limit: 0 });
      const fields = this.getExportFields();
      return await ExportService.toExcel(subscriptions.data, fields);
    } catch (error) {
      logError('Failed to export subscriptions to Excel', 'SubscriptionExportService', error);
      throw error;
    }
  }
}

module.exports = SubscriptionExportService; 