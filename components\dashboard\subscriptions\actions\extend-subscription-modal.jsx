'use client';

import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { toast } from 'sonner';
import { 
  Calendar, 
  Clock, 
  Plus, 
  X, 
  AlertCircle,
  CheckCircle,
  Info
} from 'lucide-react';
import SubscriptionService from '@/lib/services/subscription';

const ExtendSubscriptionModal = ({ isOpen, onClose, subscription, onSuccess }) => {
  const [formData, setFormData] = useState({
    extensionType: 'days',
    extensionValue: '',
    reason: '',
    notes: ''
  });
  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState({});
  const [previewDates, setPreviewDates] = useState({});

  useEffect(() => {
    if (subscription) {
      calculatePreview();
    }
  }, [subscription, formData.extensionType, formData.extensionValue]);

  const calculatePreview = () => {
    if (!subscription || !formData.extensionValue) {
      setPreviewDates({});
      return;
    }

    const currentEndDate = new Date(subscription.dates?.endDate || new Date());
    const extensionValue = parseInt(formData.extensionValue);

    if (isNaN(extensionValue) || extensionValue <= 0) {
      setPreviewDates({});
      return;
    }

    let newEndDate = new Date(currentEndDate);

    switch (formData.extensionType) {
      case 'days':
        newEndDate.setDate(newEndDate.getDate() + extensionValue);
        break;
      case 'weeks':
        newEndDate.setDate(newEndDate.getDate() + (extensionValue * 7));
        break;
      case 'months':
        newEndDate.setMonth(newEndDate.getMonth() + extensionValue);
        break;
      case 'years':
        newEndDate.setFullYear(newEndDate.getFullYear() + extensionValue);
        break;
      default:
        break;
    }

    const totalDays = Math.ceil((newEndDate - currentEndDate) / (1000 * 60 * 60 * 24));

    setPreviewDates({
      currentEndDate,
      newEndDate,
      extensionDays: totalDays
    });
  };

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: null
      }));
    }
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.extensionValue || isNaN(formData.extensionValue) || parseInt(formData.extensionValue) <= 0) {
      newErrors.extensionValue = 'Please enter a valid positive number';
    }

    const maxExtension = {
      days: 365,
      weeks: 52,
      months: 12,
      years: 2
    };

    if (parseInt(formData.extensionValue) > maxExtension[formData.extensionType]) {
      newErrors.extensionValue = `Maximum ${formData.extensionType} allowed: ${maxExtension[formData.extensionType]}`;
    }

    if (!formData.reason.trim()) {
      newErrors.reason = 'Please provide a reason for the extension';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm()) {
      toast.error('Please fix the errors in the form');
      return;
    }

    if (!previewDates.newEndDate) {
      toast.error('Unable to calculate new end date');
      return;
    }

    setIsLoading(true);
    try {
      const extensionData = {
        days: previewDates.extensionDays,
        reason: formData.reason,
        notes: formData.notes || undefined,
        newEndDate: previewDates.newEndDate.toISOString(),
        extendedBy: 'admin' // This would typically come from the current user context
      };

      await SubscriptionService.extendSubscription(subscription.subscriptionId, extensionData);
      
      toast.success(`Subscription extended by ${previewDates.extensionDays} days successfully`);
      onSuccess && onSuccess();
      onClose();
    } catch (error) {
      console.error('Error extending subscription:', error);
      toast.error(error.message || 'Failed to extend subscription');
    } finally {
      setIsLoading(false);
    }
  };

  const formatDate = (date) => {
    if (!date) return 'N/A';
    return new Date(date).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getStatusBadge = (status) => {
    const statusConfig = {
      active: { color: 'bg-green-100 text-green-800', icon: CheckCircle },
      trial: { color: 'bg-blue-100 text-blue-800', icon: Clock },
      past_due: { color: 'bg-yellow-100 text-yellow-800', icon: AlertCircle },
      canceled: { color: 'bg-red-100 text-red-800', icon: X },
      expired: { color: 'bg-gray-100 text-gray-800', icon: X }
    };

    const config = statusConfig[status] || statusConfig.active;
    const Icon = config.icon;

    return (
      <Badge className={`${config.color} flex items-center gap-1`}>
        <Icon className="h-3 w-3" />
        {status?.charAt(0).toUpperCase() + status?.slice(1) || 'Unknown'}
      </Badge>
    );
  };

  if (!subscription) return null;

  const quickExtensions = [
    { label: '7 Days', type: 'days', value: 7 },
    { label: '1 Month', type: 'months', value: 1 },
    { label: '3 Months', type: 'months', value: 3 },
    { label: '1 Year', type: 'years', value: 1 }
  ];

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Calendar className="h-5 w-5" />
            Extend Subscription #{subscription.subscriptionId}
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Current Subscription Info */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span>Current Subscription</span>
                {getStatusBadge(subscription.status)}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <p className="text-sm text-gray-500">Plan</p>
                  <p className="font-medium">{subscription.plan?.name || 'N/A'}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-500">Type</p>
                  <Badge variant="outline">
                    {subscription.plan?.type?.toUpperCase() || 'N/A'}
                  </Badge>
                </div>
                <div>
                  <p className="text-sm text-gray-500">Current End Date</p>
                  <p className="font-medium">{formatDate(subscription.dates?.endDate)}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-500">Days Remaining</p>
                  <p className="font-medium">
                    {subscription.dates?.endDate 
                      ? Math.max(0, Math.ceil((new Date(subscription.dates.endDate) - new Date()) / (1000 * 60 * 60 * 24)))
                      : 'N/A'
                    } days
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Quick Extension Options */}
          <Card>
            <CardHeader>
              <CardTitle>Quick Extensions</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
                {quickExtensions.map((extension, index) => (
                  <Button
                    key={index}
                    variant="outline"
                    onClick={() => {
                      handleInputChange('extensionType', extension.type);
                      handleInputChange('extensionValue', extension.value.toString());
                    }}
                    className="h-auto py-3 flex flex-col items-center"
                  >
                    <Plus className="h-4 w-4 mb-1" />
                    <span className="text-sm">{extension.label}</span>
                  </Button>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Custom Extension */}
          <Card>
            <CardHeader>
              <CardTitle>Custom Extension</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="extensionValue">Extension Value</Label>
                  <Input
                    id="extensionValue"
                    type="number"
                    min="1"
                    value={formData.extensionValue}
                    onChange={(e) => handleInputChange('extensionValue', e.target.value)}
                    placeholder="Enter number"
                  />
                  {errors.extensionValue && (
                    <p className="text-sm text-red-500 mt-1">{errors.extensionValue}</p>
                  )}
                </div>
                <div>
                  <Label htmlFor="extensionType">Time Unit</Label>
                  <Select
                    value={formData.extensionType}
                    onValueChange={(value) => handleInputChange('extensionType', value)}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="days">Days</SelectItem>
                      <SelectItem value="weeks">Weeks</SelectItem>
                      <SelectItem value="months">Months</SelectItem>
                      <SelectItem value="years">Years</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div>
                <Label htmlFor="reason">Reason for Extension *</Label>
                <Select
                  value={formData.reason}
                  onValueChange={(value) => handleInputChange('reason', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select a reason" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="customer_request">Customer Request</SelectItem>
                    <SelectItem value="payment_issue">Payment Issue Resolution</SelectItem>
                    <SelectItem value="service_compensation">Service Compensation</SelectItem>
                    <SelectItem value="promotional_extension">Promotional Extension</SelectItem>
                    <SelectItem value="technical_issue">Technical Issue Compensation</SelectItem>
                    <SelectItem value="admin_adjustment">Administrative Adjustment</SelectItem>
                    <SelectItem value="goodwill_gesture">Goodwill Gesture</SelectItem>
                    <SelectItem value="other">Other</SelectItem>
                  </SelectContent>
                </Select>
                {errors.reason && (
                  <p className="text-sm text-red-500 mt-1">{errors.reason}</p>
                )}
              </div>

              <div>
                <Label htmlFor="notes">Additional Notes (Optional)</Label>
                <Textarea
                  id="notes"
                  value={formData.notes}
                  onChange={(e) => handleInputChange('notes', e.target.value)}
                  placeholder="Enter any additional notes or context"
                  rows={3}
                />
              </div>
            </CardContent>
          </Card>

          {/* Preview */}
          {previewDates.newEndDate && (
            <Card className="border-blue-200 bg-blue-50">
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-blue-700">
                  <Info className="h-4 w-4" />
                  Extension Preview
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <p className="text-sm text-blue-600">Current End Date</p>
                      <p className="font-medium text-blue-800">{formatDate(previewDates.currentEndDate)}</p>
                    </div>
                    <div>
                      <p className="text-sm text-blue-600">New End Date</p>
                      <p className="font-medium text-blue-800">{formatDate(previewDates.newEndDate)}</p>
                    </div>
                  </div>
                  <Separator className="bg-blue-200" />
                  <div className="text-center">
                    <p className="text-sm text-blue-600">Total Extension</p>
                    <p className="text-lg font-bold text-blue-800">
                      +{previewDates.extensionDays} days
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Warning for canceled subscriptions */}
          {subscription.status === 'canceled' && (
            <Card className="border-yellow-200 bg-yellow-50">
              <CardContent className="pt-4">
                <div className="flex items-center gap-2 text-yellow-700">
                  <AlertCircle className="h-4 w-4" />
                  <span className="text-sm font-medium">
                    This subscription is canceled. Extension will reactivate the subscription.
                  </span>
                </div>
              </CardContent>
            </Card>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onClose} disabled={isLoading}>
            <X className="h-4 w-4 mr-2" />
            Cancel
          </Button>
          <Button onClick={handleSubmit} disabled={isLoading || !previewDates.newEndDate}>
            {isLoading ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Extending...
              </>
            ) : (
              <>
                <Calendar className="h-4 w-4 mr-2" />
                Extend Subscription
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default ExtendSubscriptionModal; 