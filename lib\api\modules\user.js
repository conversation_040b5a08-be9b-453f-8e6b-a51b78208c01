/**
 * User API module
 * Handles user management API calls
 */
import api from '../index';

const userAPI = {
  /**
   * Get users with pagination and filters
   * @param {Object} filters - Filter criteria
   * @param {number} page - Page number
   * @param {number} limit - Results per page
   * @returns {Promise} API response with users
   */
  getUsers: (filters = {}, page = 1, limit = 10) => {
    // Combine filters with pagination
    const params = { ...filters, page, limit };
    return api.get('/api/users', { params });
  },

  /**
   * Get a specific user by ID
   * @param {string} userId - User ID
   * @returns {Promise} API response with user data
   */
  getUserById: (userId) => api.get(`/api/users/${userId}`),

  /**
   * Create a new user
   * @param {Object} userData - User data
   * @returns {Promise} API response with created user
   */
  createUser: (userData) => api.post('/api/users', userData),

  /**
   * Update a user
   * @param {string} userId - User ID
   * @param {Object} userData - Updated user data
   * @returns {Promise} API response with updated user
   */
  updateUser: (userId, userData) => api.put(`/api/users/${userId}`, userData),

  /**
   * Change user status
   * @param {string} userId - User ID
   * @param {Object} statusData - Status data
   * @returns {Promise} API response
   */
  changeUserStatus: (userId, statusData) => api.patch(`/api/users/${userId}/status`, statusData),

  /**
   * Delete a user
   * @param {string} userId - User ID
   * @param {string} reason - Reason for deletion (optional)
   * @returns {Promise} API response
   */
  deleteUser: (userId, reason) => api.delete(`/api/users/${userId}`, { data: { reason } })
};

export default userAPI;
