const Joi = require('joi');
const patterns = require('../validationPatterns');

/**
 * Shop management validation schemas
 */
const shopSchemas = {
  /**
   * Schema for shop creation
   */
  createShop: Joi.object({
    name: patterns.string.shopName.required(),
    address: patterns.string.shopAddress.required(),
    logo: Joi.string().uri().allow('').optional(),
    ownerId: Joi.string().optional(),
    
    // Subscription data
    subscription: Joi.object({
      planType: Joi.string().valid(...patterns.enums.planType).default('monthly'),
      paymentMethod: Joi.string().valid(...patterns.enums.paymentMethod).default('offline'),
      initialPaid: Joi.boolean().default(false),
      paymentDetails: patterns.object.paymentDetails.optional()
    }).optional().default({})
  }),

  /**
   * Schema for updating shop
   */
  updateShop: Joi.object({
    name: patterns.string.shopName.optional(),
    address: patterns.string.shopAddress.optional(),
    phone: patterns.string.phone.optional(),
    email: patterns.string.email.optional(),
    businessDetails: Joi.object({
      type: Joi.string().optional(),
      description: Joi.string().optional(),
      category: Joi.string().optional()
    }).optional(),
    isActive: Joi.boolean().optional()
  }),

  /**
   * Schema for shop logo update (file upload)
   */
  updateLogo: Joi.object({
    logo: Joi.string().uri().optional()
      .messages({
        'string.uri': 'Logo must be a valid URL'
      })
  }).unknown(true).allow(null), // Allow file upload fields and null values

  /**
   * Schema for subscription update/renewal
   */
  updateSubscription: Joi.object({
    planType: Joi.string().valid(...patterns.enums.planType).required()
      .messages({
        'any.required': 'Plan type is required',
        'any.only': 'Plan type must be either monthly or yearly'
      }),
    paymentMethod: Joi.string().valid(...patterns.enums.paymentMethod).required()
      .messages({
        'any.required': 'Payment method is required',
        'any.only': 'Payment method must be offline, online, evc, or bank'
      }),
    paymentDetails: patterns.object.paymentDetails.optional()
  }),

  /**
   * Schema for changing shop status
   */
  changeShopStatus: Joi.object({
    status: Joi.string().valid('active', 'suspended', 'pending').required()
      .messages({
        'any.only': 'Status must be active, suspended, or pending',
        'any.required': 'Status is required'
      }),
    reason: Joi.string().min(5).max(200)
      .when('status', {
        is: 'suspended',
        then: Joi.required(),
        otherwise: Joi.optional()
      })
      .messages({
        'string.min': 'Reason must be at least 5 characters long',
        'string.max': 'Reason cannot exceed 200 characters',
        'any.required': 'Reason is required when suspending a shop'
      })
  }),

  /**
   * Schema for verifying shop payment
   */
  verifyPayment: Joi.object({
    paymentVerified: Joi.boolean().required()
      .messages({
        'any.required': 'Payment verification status is required'
      }),
    transactionId: Joi.string().optional(),
    paymentMethod: Joi.string().valid(...patterns.enums.paymentMethod).optional(),
    amount: Joi.number().positive().optional(),
    notes: Joi.string().max(500).optional()
  }),

  /**
   * Schema for deleting shop
   */
  deleteShop: Joi.object({
    reason: Joi.string().min(5).max(200).required()
      .messages({
        'string.min': 'Reason must be at least 5 characters long',
        'string.max': 'Reason cannot exceed 200 characters',
        'any.required': 'Reason is required for shop deletion'
      }),
    transferData: Joi.boolean().default(false),
    notifyUsers: Joi.boolean().default(true)
  })
};

module.exports = shopSchemas;
