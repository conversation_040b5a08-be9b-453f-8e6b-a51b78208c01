/**
 * SuperAdmin Dashboard Controller
 * Provides aggregated dashboard data for SuperAdmin overview
 */

const User = require('../../models/user.model');
const Shop = require('../../models/shop.model');
const Subscription = require('../../models/subscription.model');
const Payment = require('../../models/payment.model');
const Plan = require('../../models/Plan.model');
const { AppError, logInfo, logError } = require('../../utils');

// Role constants to prevent case sensitivity issues
const ROLES = {
  SUPER_ADMIN: 'superAdmin',
  ADMIN: 'admin',
  EMPLOYEE: 'employee',
  CUSTOMER: 'customer'
};

/**
 * Get comprehensive dashboard overview
 * GET /api/admin/dashboard/overview
 */
const getDashboardOverview = async (req, res, next) => {
  try {
    // SuperAdmin role validation
    if (req.user.role !== ROLES.SUPER_ADMIN) {
      return next(new AppError('Access denied. SuperAdmin privileges required.', 403, 'forbidden'));
    }

    logInfo(`SuperAdmin ${req.user.email} requested dashboard overview`, 'DashboardController');

    // Parallel data fetching for better performance
    const [
      userStats,
      shopStats,
      subscriptionStats,
      paymentStats,
      systemHealth
    ] = await Promise.all([
      getUserStatistics(),
      getShopStatistics(),
      getSubscriptionStatistics(),
      getPaymentStatistics(),
      getSystemHealth()
    ]);

    const dashboardData = {
      users: userStats,
      shops: shopStats,
      subscriptions: subscriptionStats,
      payments: paymentStats,
      system: systemHealth,
      lastUpdated: new Date().toISOString()
    };

    logInfo(`Dashboard overview generated successfully for ${req.user.email}`, 'DashboardController');

    return res.status(200).json({
      success: true,
      message: 'Dashboard overview retrieved successfully',
      data: dashboardData
    });

  } catch (error) {
    logError(`Error generating dashboard overview: ${error.message}`, 'DashboardController', error);
    return next(new AppError('Failed to generate dashboard overview', 500, 'dashboard_error'));
  }
};

/**
 * Get user statistics for dashboard
 */
const getUserStatistics = async () => {
  try {
    const now = new Date();
    const lastMonth = new Date(now.getFullYear(), now.getMonth() - 1, now.getDate());

    const [totalUsers, activeUsers, thisMonthUsers, lastMonthUsers] = await Promise.all([
      User.countDocuments({ isDeleted: { $ne: true } }),
      User.countDocuments({ isDeleted: { $ne: true }, status: 'active' }),
      User.countDocuments({ 
        isDeleted: { $ne: true }, 
        createdAt: { $gte: new Date(now.getFullYear(), now.getMonth(), 1) }
      }),
      User.countDocuments({ 
        isDeleted: { $ne: true }, 
        createdAt: { 
          $gte: new Date(now.getFullYear(), now.getMonth() - 1, 1),
          $lt: new Date(now.getFullYear(), now.getMonth(), 1)
        }
      })
    ]);

    const growthRate = lastMonthUsers > 0 ? 
      ((thisMonthUsers - lastMonthUsers) / lastMonthUsers * 100).toFixed(1) : 0;

    return {
      total: totalUsers,
      active: activeUsers,
      inactive: totalUsers - activeUsers,
      thisMonth: thisMonthUsers,
      lastMonth: lastMonthUsers,
      growthRate: parseFloat(growthRate),
      growthPositive: growthRate >= 0
    };
  } catch (error) {
    logError(`Error fetching user statistics: ${error.message}`, 'DashboardController', error);
    return {
      total: 0,
      active: 0,
      inactive: 0,
      thisMonth: 0,
      lastMonth: 0,
      growthRate: 0,
      growthPositive: true
    };
  }
};

/**
 * Get shop statistics for dashboard
 */
const getShopStatistics = async () => {
  try {
    const now = new Date();
    const thisMonth = new Date(now.getFullYear(), now.getMonth(), 1);
    const lastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1);

    const [shopStats, thisMonthShops, lastMonthShops] = await Promise.all([
      Shop.aggregate([
        { $match: { isDeleted: { $ne: true } } },
        {
          $group: {
            _id: null,
            total: { $sum: 1 },
            active: { $sum: { $cond: [{ $eq: ['$status', 'active'] }, 1, 0] } },
            pending: { $sum: { $cond: [{ $eq: ['$status', 'pending'] }, 1, 0] } },
            suspended: { $sum: { $cond: [{ $eq: ['$status', 'suspended'] }, 1, 0] } },
            inactive: { $sum: { $cond: [{ $eq: ['$status', 'inactive'] }, 1, 0] } }
          }
        }
      ]),
      Shop.countDocuments({ 
        isDeleted: { $ne: true }, 
        createdAt: { $gte: thisMonth }
      }),
      Shop.countDocuments({ 
        isDeleted: { $ne: true }, 
        createdAt: { 
          $gte: lastMonth,
          $lt: thisMonth
        }
      })
    ]);

    const stats = shopStats[0] || {
      total: 0, active: 0, pending: 0, suspended: 0, inactive: 0
    };

    const growthRate = lastMonthShops > 0 ? 
      ((thisMonthShops - lastMonthShops) / lastMonthShops * 100).toFixed(1) : 0;

    return {
      ...stats,
      thisMonth: thisMonthShops,
      lastMonth: lastMonthShops,
      growthRate: parseFloat(growthRate),
      growthPositive: growthRate >= 0
    };
  } catch (error) {
    logError(`Error fetching shop statistics: ${error.message}`, 'DashboardController', error);
    return {
      total: 0, active: 0, pending: 0, suspended: 0, inactive: 0,
      thisMonth: 0, lastMonth: 0, growthRate: 0, growthPositive: true
    };
  }
};

/**
 * Get subscription statistics for dashboard
 */
const getSubscriptionStatistics = async () => {
  try {
    const now = new Date();
    const expiringThreshold = new Date(now.getTime() + (7 * 24 * 60 * 60 * 1000)); // 7 days

    const subscriptionStats = await Subscription.aggregate([
      { $match: { isDeleted: { $ne: true } } },
      {
        $group: {
          _id: null,
          total: { $sum: 1 },
          active: { $sum: { $cond: [{ $eq: ['$status', 'active'] }, 1, 0] } },
          trial: { $sum: { $cond: [{ $eq: ['$status', 'trial'] }, 1, 0] } },
          expired: { $sum: { $cond: [{ $eq: ['$status', 'expired'] }, 1, 0] } },
          expiringSoon: { 
            $sum: { 
              $cond: [
                { 
                  $and: [
                    { $eq: ['$status', 'active'] },
                    { $lte: ['$endDate', expiringThreshold] }
                  ]
                }, 
                1, 
                0
              ] 
            } 
          }
        }
      }
    ]);

    const stats = subscriptionStats[0] || {
      total: 0, active: 0, trial: 0, expired: 0, expiringSoon: 0
    };

    return stats;
  } catch (error) {
    logError(`Error fetching subscription statistics: ${error.message}`, 'DashboardController', error);
    return {
      total: 0, active: 0, trial: 0, expired: 0, expiringSoon: 0
    };
  }
};

/**
 * Get payment statistics for dashboard
 */
const getPaymentStatistics = async () => {
  try {
    const now = new Date();
    const thisMonth = new Date(now.getFullYear(), now.getMonth(), 1);
    const lastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1);

    const [paymentStats, thisMonthRevenue, lastMonthRevenue] = await Promise.all([
      Payment.aggregate([
        { 
          $match: { 
            isDeleted: { $ne: true },
            paymentContext: 'subscription'
          } 
        },
        {
          $group: {
            _id: null,
            total: { $sum: 1 },
            pending: { $sum: { $cond: [{ $eq: ['$status', 'pending'] }, 1, 0] } },
            approved: { $sum: { $cond: [{ $in: ['$status', ['approved', 'success']] }, 1, 0] } },
            rejected: { $sum: { $cond: [{ $in: ['$status', ['rejected', 'failed']] }, 1, 0] } },
            totalRevenue: { 
              $sum: { 
                $cond: [
                  { $in: ['$status', ['approved', 'success']] }, 
                  '$amount', 
                  0
                ] 
              } 
            }
          }
        }
      ]),
      Payment.aggregate([
        { 
          $match: { 
            isDeleted: { $ne: true },
            paymentContext: 'subscription',
            status: { $in: ['approved', 'success'] },
            createdAt: { $gte: thisMonth }
          } 
        },
        { $group: { _id: null, revenue: { $sum: '$amount' } } }
      ]),
      Payment.aggregate([
        { 
          $match: { 
            isDeleted: { $ne: true },
            paymentContext: 'subscription',
            status: { $in: ['approved', 'success'] },
            createdAt: { 
              $gte: lastMonth,
              $lt: thisMonth
            }
          } 
        },
        { $group: { _id: null, revenue: { $sum: '$amount' } } }
      ])
    ]);

    const stats = paymentStats[0] || {
      total: 0, pending: 0, approved: 0, rejected: 0, totalRevenue: 0
    };

    const thisMonthRev = thisMonthRevenue[0]?.revenue || 0;
    const lastMonthRev = lastMonthRevenue[0]?.revenue || 0;
    const revenueGrowth = lastMonthRev > 0 ?
      ((thisMonthRev - lastMonthRev) / lastMonthRev * 100).toFixed(1) : 0;

    // Debug logging for payment statistics
    logInfo(`Payment Statistics Debug:`, 'DashboardController');
    logInfo(`- Total payments: ${stats.total}`, 'DashboardController');
    logInfo(`- Approved payments: ${stats.approved}`, 'DashboardController');
    logInfo(`- Total revenue: ${stats.totalRevenue}`, 'DashboardController');
    logInfo(`- This month revenue: ${thisMonthRev}`, 'DashboardController');
    logInfo(`- Last month revenue: ${lastMonthRev}`, 'DashboardController');

    return {
      ...stats,
      thisMonthRevenue: thisMonthRev,
      lastMonthRevenue: lastMonthRev,
      revenueGrowth: parseFloat(revenueGrowth),
      revenueGrowthPositive: revenueGrowth >= 0
    };
  } catch (error) {
    logError(`Error fetching payment statistics: ${error.message}`, 'DashboardController', error);
    return {
      total: 0, pending: 0, approved: 0, rejected: 0, totalRevenue: 0,
      thisMonthRevenue: 0, lastMonthRevenue: 0, revenueGrowth: 0, revenueGrowthPositive: true
    };
  }
};

/**
 * Get system health information
 */
const getSystemHealth = async () => {
  try {
    const [totalPlans, activePlans] = await Promise.all([
      Plan.countDocuments({ isDeleted: { $ne: true } }),
      Plan.countDocuments({ isDeleted: { $ne: true }, isActive: true })
    ]);

    return {
      totalPlans,
      activePlans,
      inactivePlans: totalPlans - activePlans,
      systemStatus: 'operational',
      lastHealthCheck: new Date().toISOString()
    };
  } catch (error) {
    logError(`Error fetching system health: ${error.message}`, 'DashboardController', error);
    return {
      totalPlans: 0,
      activePlans: 0,
      inactivePlans: 0,
      systemStatus: 'degraded',
      lastHealthCheck: new Date().toISOString()
    };
  }
};

module.exports = {
  getDashboardOverview
};
