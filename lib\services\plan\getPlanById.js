import apiBridge from '@/lib/api/bridge';
import { ENDPOINTS } from '@/lib/api/contract';
import { handleError, processApiResponse, logApiCall } from '../baseService';

/**
 * Get plan by ID
 * @param {string} planId - Plan ID (should be planId field, not MongoDB _id)
 * @param {Object} options - Additional options
 * @param {boolean} options.skipCache - Skip cache and force fresh request
 * @returns {Promise<Object>} Plan details
 */
async function getPlanById(planId, options = {}) {
  try {
    if (!planId) {
      handleError({ message: 'Plan ID is required' }, 'PlanService.getPlanById', true);
      throw new Error('Plan ID is required');
    }

    const { skipCache = false } = options;

    // FIXED: Add validation to ensure we're using the correct ID format
    
    // Check if planId looks like MongoDB ObjectId (24 hex chars) vs custom planId
    if (planId.length === 24 && /^[0-9a-fA-F]{24}$/.test(planId)) {
      console.warn(`[PlanService.getPlanById] Warning: Received MongoDB ObjectId (${planId}) instead of planId. This may cause 'Plan not found' errors.`);
    }

    logApiCall('PlanService.getPlanById', ENDPOINTS.PLANS.DETAIL(planId));

    // Make API request using the bridge
    const requestOptions = skipCache ? {
      forceFresh: true, // Force fresh request, bypass cache
      useCache: false
    } : {
      cacheKey: `plan-${planId}`,
      cacheTTL: 300000, // 5 minutes cache for normal requests
      useCache: true
    };
    
    const response = await apiBridge.get(ENDPOINTS.PLANS.DETAIL(planId), requestOptions);

    // FIXED: Better response processing with detailed error handling
    if (!response) {
      throw new Error('No response received from server');
    }

    // Process response using utility
    const result = processApiResponse(response);
    
    // Ensure the returned plan has consistent ID field
    if (result.data) {
      
      // Ensure planId is present (prefer planId over _id)
      if (!result.data.planId && result.data._id) {
        console.warn(`[PlanService.getPlanById] Plan missing planId field, has _id: ${result.data._id}`);
      }
    }
    
    return result.data || result;
  } catch (error) {
    // FIXED: Enhanced error logging for debugging
    console.error(`[PlanService.getPlanById] Error fetching plan ${planId}:`, {
      message: error.message,
      status: error.response?.status,
      statusText: error.response?.statusText,
      data: error.response?.data
    });
    
    // Provide more specific error messages
    if (error.response?.status === 404) {
      const message = `Plan with ID '${planId}' not found. Please verify the plan exists and the ID is correct.`;
      handleError({ ...error, message }, 'PlanService.getPlanById', true);
      throw new Error(message);
    }
    
    handleError(error, 'PlanService.getPlanById', true);
    throw error;
  }
}

export default getPlanById;
// Get Plan By ID Service
