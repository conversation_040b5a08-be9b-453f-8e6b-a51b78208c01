# 💳 Payment Endpoints & SuperAdmin Notifications Summary

## 🎯 **PAYMENT METHODS CONTROL**

### **Settings API** (Controls available payment methods)
```
GET http://localhost:5000/api/settings/payment-methods
```
**Response:**
```json
{
  "success": true,
  "data": {
    "paymentMethods": [
      "Cash",
      "EVC Plus", 
      "Bank Transfer",
      "Mobile Money",
      "Check",
      "Card",
      "Other",
      "offline"
    ],
    "onlinePaymentsEnabled": true,
    "offlinePaymentsEnabled": true,
    "context": "general"
  }
}
```
- ✅ **SuperAdmin Controlled** - Only SuperAdmin can modify these settings
- ✅ **App Integration** - Frontend calls this API to show available payment methods
- ✅ **Real-time** - Changes reflect immediately in registration forms

---

## 💰 **PAYMENT PROCESSING ENDPOINTS**

### **1. Basic Payment Processing**
```
POST http://localhost:5000/api/register/pay
```
- **Requires**: Authentication (user must be logged in after email verification)
- **Validation**: `registerSchemas.processPayment`
- **Controller**: `src/controllers/register/paymentController.js`

### **2. Enhanced Offline Payment Submission** ⭐ **NEW**
```
POST http://localhost:5000/api/register/submit-offline-payment
```
- **Requires**: Authentication (user must be logged in)
- **Content-Type**: `multipart/form-data`
- **File Upload**: Optional payment proof (JPG, PNG, PDF, max 5MB)
- **Validation**: `registerSchemas.submitOfflinePayment`
- **Purpose**: Submit offline payment details with optional screenshot proof

---

## 🔄 **PAYMENT PAYLOADS**

### **1. 💳 EVC Plus (Online Payment) - CONFIRMED WORKING**
```json
{
  "planType": "monthly",
  "paymentMethod": "EVC Plus",
  "paymentDetails": {
    "phoneNumber": "+************"
  },
  "discountCode": "KARSHE01"
}
```
**Result**: Immediate payment processing via WaafiPay API

### **2. 💰 Offline Payment Methods**

#### **Generic Offline**
```json
{
  "planType": "monthly",
  "paymentMethod": "offline",
  "paymentDetails": {
    "notes": "Will pay via bank transfer"
  },
  "discountCode": "KARSHE01"
}
```

#### **Cash Payment**
```json
{
  "planType": "monthly",
  "paymentMethod": "Cash",
  "paymentDetails": {
    "notes": "Will pay cash at office"
  }
}
```

#### **Bank Transfer**
```json
{
  "planType": "monthly",
  "paymentMethod": "Bank Transfer",
  "paymentDetails": {
    "notes": "Will transfer to company account"
  }
}
```

### **3. ⭐ Enhanced Offline Payment Submission (NEW)**
```json
// Multipart form data with optional file upload
{
  "payerName": "Ahmed Hassan",
  "payerPhone": "+************",
  "notes": "Paid via bank transfer to company account", 
  "bankDetails": "Transfer to Salaam Bank - Account #*********",
  "transferReference": "TXN20241216001"
}
// + paymentProof: [FILE] (optional JPG/PNG/PDF up to 5MB)
```
**Result**: Professional SuperAdmin notification with payment proof attachment

#### **Other Offline Methods**
```json
{
  "planType": "monthly",
  "paymentMethod": "Check",
  "paymentDetails": {
    "notes": "Will send check via mail"
  }
}
```

---

## 📧 **SUPERADMIN NOTIFICATION SYSTEM**

### **✅ NOTIFICATION TRIGGERS (CONFIRMED IMPLEMENTED)**

#### **Trigger 1: Email Verification (for offline payments)**
- **When**: User verifies email AND payment method is offline
- **File**: `src/controllers/register/verifyEmailController.js`
- **Email Subject**: `"New Offline Payment Order - {Shop Name}"`

#### **Trigger 2: Payment Processing (for offline payments)** 
- **When**: User calls `/api/register/pay` with offline payment method
- **File**: `src/controllers/register/paymentController.js` 
- **Email Subject**: `"New Offline Payment Order - {Shop Name}"`

### **📧 Email Template Used**
- **Template**: `src/templates/emails/Admin/payment-verification-request.html`
- **Service**: `EmailService.admin.sendOfflinePaymentOrderNotification()`
- **Contains**: Shop details, amount, reference number, approve/reject buttons

### **🎯 SuperAdmin Email Content**
```
Subject: New Offline Payment Order - Ahmed's Electronics

Payment Details:
- Shop: Ahmed's Electronics
- Payment ID: DEYN-2024-001ABC
- Amount: $10.00 USD
- Method: Bank Transfer
- Customer: Ahmed Hassan
- Date: 2024-01-15
- Notes: Customer selected Bank Transfer payment method

[Approve Payment] [Reject Payment] [Dashboard]
```

---

## 🔄 **COMPLETE NOTIFICATION FLOW**

### **Scenario 1: Email Verification Trigger**
```
1. User registers with offline payment
2. User verifies email
3. ✅ SuperAdmin gets notification immediately
4. SuperAdmin approves via existing system
```

### **Scenario 2: Payment Processing Trigger**  
```
1. User registers and verifies email
2. User calls /api/register/pay with offline payment
3. ✅ SuperAdmin gets notification immediately  
4. SuperAdmin approves via existing system
```

### **Scenario 3: Online Payment (No Notification)**
```
1. User registers and verifies email
2. User calls /api/register/pay with EVC Plus
3. Payment processed immediately via WaafiPay
4. ❌ No SuperAdmin notification needed (auto-approved)
```

---

## ⚙️ **CONFIGURATION**

### **Environment Variables**
```bash
SUPER_ADMIN_EMAIL=<EMAIL>
FRONTEND_URL=https://app.deyncare.com
```

### **Offline Payment Methods Supported**
- `"offline"` - Generic offline payment
- `"Cash"` - Cash payment
- `"Bank Transfer"` - Bank transfer
- `"Check"` - Check payment  
- `"Other"` - Other offline methods

---

## 🎯 **KEY BENEFITS**

- ✅ **Dual Notification System** - Triggers on both email verification AND payment processing
- ✅ **Real-time Alerts** - SuperAdmin knows immediately when offline orders come in
- ✅ **Professional Emails** - Uses existing payment verification template
- ✅ **Multiple Payment Methods** - Supports all offline payment types
- ✅ **Reference Tracking** - Auto-generated reference numbers
- ✅ **Non-blocking** - Registration succeeds even if notification fails

---

## 📋 **TESTING SCENARIOS**

### **Test 1: Offline Payment via Email Verification**
1. Register with offline payment
2. Verify email
3. ✅ Check SuperAdmin receives notification

### **Test 2: Offline Payment via Payment Endpoint**
1. Register and verify email
2. Call `/api/register/pay` with offline payload
3. ✅ Check SuperAdmin receives notification

### **Test 3: Online Payment (No Notification)**
1. Register and verify email  
2. Call `/api/register/pay` with EVC Plus payload
3. ✅ Payment processes immediately
4. ✅ No SuperAdmin notification sent

---

**Result**: SuperAdmin gets notified immediately when customers submit offline payment orders, allowing for quick approval and activation! 