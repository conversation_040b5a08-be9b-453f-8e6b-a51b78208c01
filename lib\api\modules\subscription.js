import { createApiModule } from '../bridge';

const subscriptionApi = createApiModule({
  baseUrl: '/api/subscriptions',
  tag: 'Subscription API'
});

/**
 * Get subscription history for current shop/admin
 */
export const getSubscriptionHistory = () => {
  return subscriptionApi.get('/history');
};

/**
 * Upgrade subscription from trial to paid plan
 */
export const upgradeFromTrial = (data) => {
  return subscriptionApi.post('/upgrade', data);
};

/**
 * Change active subscription plan
 */
export const changeSubscriptionPlan = (data) => {
  return subscriptionApi.post('/change-plan', data);
};

/**
 * Cancel active subscription
 */
export const cancelSubscription = (data) => {
  return subscriptionApi.post('/cancel', data);
};

/**
 * Record payment for subscription
 */
export const recordPayment = (data) => {
  return subscriptionApi.post('/payment', data);
};

/**
 * Update auto-renewal setting
 */
export const updateAutoRenewal = (data) => {
  return subscriptionApi.patch('/auto-renewal', data);
};

/**
 * Renew existing subscription
 */
export const renewSubscription = (data) => {
  return subscriptionApi.post('/renew', data);
};

/**
 * Get all subscriptions with optional filters
 */
export const getAllSubscriptions = (params = {}) => {
  return subscriptionApi.get('/', { params });
};

/**
 * Get subscription statistics (SuperAdmin only)
 */
export const getSubscriptionStats = () => {
  return subscriptionApi.get('/stats');
};

/**
 * Perform bulk operations on subscriptions (SuperAdmin only)
 */
export const bulkUpdateSubscriptions = (data) => {
  return subscriptionApi.post('/bulk', data);
};

/**
 * Manually trigger subscription cron tasks (SuperAdmin only)
 */
export const triggerCronTasks = () => {
  return subscriptionApi.post('/cron/run');
};

/**
 * Get subscription by ID
 */
export const getSubscriptionById = (subscriptionId) => {
  return subscriptionApi.get(`/${subscriptionId}`);
};

/**
 * Extend subscription duration
 */
export const extendSubscription = (subscriptionId, data) => {
  return subscriptionApi.post(`/${subscriptionId}/extend`, data);
};

// Payment Retry Management (SuperAdmin only)

/**
 * Get payment retry status for subscription
 */
export const getPaymentRetryStatus = (subscriptionId) => {
  return subscriptionApi.get(`/payment-retry/${subscriptionId}/status`);
};

/**
 * Trigger manual payment retry
 */
export const triggerManualPaymentRetry = (subscriptionId) => {
  return subscriptionApi.post(`/payment-retry/${subscriptionId}/trigger`);
};

/**
 * Cancel scheduled payment retries
 */
export const cancelScheduledRetries = (subscriptionId) => {
  return subscriptionApi.post(`/payment-retry/${subscriptionId}/cancel`);
};

/**
 * Process all pending payment retries
 */
export const processAllPendingRetries = () => {
  return subscriptionApi.post('/payment-retry/process-all');
};

/**
 * Get payment retry configuration
 */
export const getPaymentRetryConfig = () => {
  return subscriptionApi.get('/payment-retry/config');
};

export default {
  getSubscriptionHistory,
  upgradeFromTrial,
  changeSubscriptionPlan,
  cancelSubscription,
  recordPayment,
  updateAutoRenewal,
  renewSubscription,
  getAllSubscriptions,
  getSubscriptionStats,
  bulkUpdateSubscriptions,
  triggerCronTasks,
  getSubscriptionById,
  extendSubscription,
  getPaymentRetryStatus,
  triggerManualPaymentRetry,
  cancelScheduledRetries,
  processAllPendingRetries,
  getPaymentRetryConfig
}; 