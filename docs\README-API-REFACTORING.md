# API Refactoring Documentation

## Overview
The DeynCare frontend API module has been refactored into a more maintainable, modular structure that matches our backend architecture approach. This refactoring preserves all existing functionality while making the code more organized and easier to extend.

## New Structure

```
/lib/api/
├── index.js               # Entry point that exports the axios instance
├── config.js              # Sets up baseURL, headers, credentials
├── interceptors.js        # Handles request/response, token logic
├── token.js               # Manages refresh logic and token queue
├── throttle.js            # Prevents over-requesting
├── apiService.js          # Contains the original API service methods
└── modules/
    ├── auth.js            # Authentication methods
    ├── user.js            # User management methods
    └── shop.js            # Placeholder for shop methods
```

## How To Use

### Using the Main API Instance

```javascript
// Import the base API instance (for custom calls)
import api from '@/lib/api';

// Example
const response = await api.get('/api/custom-endpoint');
```

### Using the API Service Methods

```javascript
// Import the apiService with all methods
import { apiService } from '@/lib/api';

// Examples
const login = async (email, password) => {
  try {
    return await apiService.login(email, password);
  } catch (error) {
    console.error('Login failed:', error);
    throw error;
  }
};

const getUsers = async (filters) => {
  try {
    return await apiService.getUsers(filters);
  } catch (error) {
    console.error('Failed to fetch users:', error);
    throw error;
  }
};
```

## Important Notes

1. **Backwards Compatibility**: The exports from `lib/api/index.js` exactly match the original `lib/api.js` file, so existing imports will continue to work.

2. **No New Functionality**: This refactoring preserves the exact functionality of the original API file. We've reorganized the code without adding any new endpoints or behaviors.

3. **Throttling Improvements**: The refactored code maintains the throttling mechanism with special exceptions for critical endpoints like `/api/shops`, `/api/users`, and `/api/settings`.

4. **Token Refresh Logic**: The token refresh mechanism works exactly as before, with better organization and improved readability.

## Migration Plan

For new code, consider importing from the specific modules:

```javascript
// Instead of
import { apiService } from '@/lib/api';
const response = await apiService.login(email, password);

// You could do
import authAPI from '@/lib/api/modules/auth';
const response = await authAPI.login(email, password);
```

However, the original import pattern will continue to work and is perfectly acceptable to use.
