"use client";

import { useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { toast } from 'sonner';
import { Send, X, Plus, Mail } from 'lucide-react';
import ReportService from './report-service';

// Form validation schema
const formSchema = z.object({
  recipients: z.array(
    z.string().email('Invalid email address')
  ).min(1, 'At least one recipient is required'),
  subject: z.string().max(100, 'Subject cannot exceed 100 characters').optional(),
  message: z.string().max(1000, 'Message cannot exceed 1000 characters').optional(),
});

/**
 * Dialog to email a report to recipients
 */
export function EmailReportDialog({ isOpen, onClose, report }) {
  const [isSending, setIsSending] = useState(false);
  const [recipients, setRecipients] = useState([]);
  const [currentEmail, setCurrentEmail] = useState('');
  const reportService = new ReportService();
  
  // Set up form
  const form = useForm({
    resolver: zodResolver(formSchema),
    defaultValues: {
      recipients: [],
      subject: report ? `${report.title} - DeynCare Report` : '',
      message: '',
    },
  });

  /**
   * Handle adding a recipient email
   */
  const handleAddRecipient = () => {
    if (!currentEmail) return;
    
    // Basic email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(currentEmail)) {
      toast.error('Please enter a valid email address');
      return;
    }
    
    if (!recipients.includes(currentEmail)) {
      const newRecipients = [...recipients, currentEmail];
      setRecipients(newRecipients);
      form.setValue('recipients', newRecipients);
      setCurrentEmail('');
    } else {
      toast.error('This email is already added');
    }
  };

  /**
   * Handle removing a recipient
   */
  const handleRemoveRecipient = (email) => {
    const newRecipients = recipients.filter(r => r !== email);
    setRecipients(newRecipients);
    form.setValue('recipients', newRecipients);
  };

  /**
   * Handle enter key press in email input
   */
  const handleKeyPress = (e) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleAddRecipient();
    }
  };

  /**
   * Handle form submission
   */
  const onSubmit = async (data) => {
    if (!report) {
      toast.error('No report selected');
      return;
    }
    
    if (recipients.length === 0) {
      toast.error('Please add at least one recipient');
      return;
    }
    
    setIsSending(true);
    
    try {
      // Prepare email data
      const emailData = {
        recipients: data.recipients,
        subject: data.subject || `${report.title} - DeynCare Report`,
        message: data.message || ''
      };
      
      // Send the email
      await reportService.emailReport(report.reportId, emailData);
      
      toast.success('Report emailed successfully');
      onClose(true); // Refresh report list
    } catch (error) {
      console.error('Error emailing report:', error);
      toast.error('Failed to email report');
    } finally {
      setIsSending(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Email Report</DialogTitle>
          <DialogDescription>
            Send "{report?.title}" report to the specified email addresses.
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="recipients"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Recipients</FormLabel>
                  
                  <div className="flex space-x-2">
                    <FormControl>
                      <Input
                        placeholder="<EMAIL>"
                        value={currentEmail}
                        onChange={(e) => setCurrentEmail(e.target.value)}
                        onKeyPress={handleKeyPress}
                      />
                    </FormControl>
                    
                    <Button
                      type="button"
                      size="icon"
                      onClick={handleAddRecipient}
                    >
                      <Plus className="h-4 w-4" />
                    </Button>
                  </div>
                  
                  <FormDescription>
                    Add email addresses of people who should receive this report
                  </FormDescription>
                  
                  <div className="flex flex-wrap gap-2 mt-2">
                    {recipients.map((email) => (
                      <Badge
                        key={email}
                        variant="secondary"
                        className="px-2 py-1 flex items-center"
                      >
                        <Mail className="h-3 w-3 mr-1" />
                        {email}
                        <Button
                          type="button"
                          variant="ghost"
                          size="icon"
                          className="h-4 w-4 ml-1 p-0"
                          onClick={() => handleRemoveRecipient(email)}
                        >
                          <X className="h-3 w-3" />
                        </Button>
                      </Badge>
                    ))}
                  </div>
                  
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="subject"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Subject (Optional)</FormLabel>
                  <FormControl>
                    <Input 
                      placeholder="Report subject" 
                      {...field} 
                      value={field.value || ''}
                    />
                  </FormControl>
                  <FormDescription>
                    Email subject line. Defaults to report title if left empty.
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="message"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Message (Optional)</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Additional message to include with the report"
                      {...field}
                      value={field.value || ''}
                      rows={3}
                    />
                  </FormControl>
                  <FormDescription>
                    Additional information to include in the email body
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <DialogFooter className="pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={() => onClose(false)}
                disabled={isSending}
              >
                Cancel
              </Button>
              <Button 
                type="submit" 
                disabled={isSending || recipients.length === 0}
              >
                <Send className="mr-2 h-4 w-4" />
                {isSending ? 'Sending...' : 'Send Report'}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
