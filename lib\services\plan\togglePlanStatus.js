/**
 * Toggle Plan Status Service
 * 
 * Toggles plan active/inactive status
 * Matches backend togglePlanStatus.js functionality
 */

import apiBridge from '@/lib/api/bridge';
import { ENDPOINTS } from '@/lib/api/contract';
import { handleError, processApiResponse, logApiCall } from '../baseService';

/**
 * Toggle plan status (SuperAdmin only)
 * Activates or deactivates a plan
 * @param {string} planId - Plan ID
 * @param {boolean} isActive - New status (true = active, false = inactive)
 * @returns {Promise<Object>} API response with updated plan status
 */
async function togglePlanStatus(planId, isActive) {
  try {
    if (!planId) {
      throw new Error('Plan ID is required');
    }

    if (typeof isActive !== 'boolean') {
      throw new Error('isActive must be a boolean');
    }

    logApiCall('PlanService.togglePlanStatus', ENDPOINTS.PLANS.TOGGLE_STATUS(planId), { isActive });

    const response = await apiBridge.patch(ENDPOINTS.PLANS.TOGGLE_STATUS(planId), {
      isActive
    }, {
      skipCache: true
    });

    const result = processApiResponse(response, `Plan ${isActive ? 'activated' : 'deactivated'} successfully`);

    // Clear related caches after status change
    if (typeof window !== 'undefined') {
      localStorage.removeItem('cache_plans-list');
      localStorage.removeItem(`cache_plan-${planId}`);
      localStorage.removeItem('cache_plan-stats');
    }

    return result;
  } catch (error) {
    // Handle potential backend errors
    if (error.response?.status === 403) {
      throw new Error('Access denied. SuperAdmin privileges required.');
    }
    
    if (error.response?.status === 404) {
      throw new Error('Plan not found');
    }

    handleError(error, 'PlanService.togglePlanStatus', true);
    throw error;
  }
}

export default togglePlanStatus; 