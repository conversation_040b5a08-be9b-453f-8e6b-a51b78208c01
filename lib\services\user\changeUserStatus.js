import apiBridge from '@/lib/api/bridge';
import { ENDPOINTS } from '@/lib/api/contract';
import { toast } from 'sonner';
import BaseService from '@/lib/services/baseService';

/**
 * Change a user's status (active, inactive, suspended)
 * @param {string} userId - ID of the user
 * @param {string} status - New status ('active', 'inactive', 'suspended')
 * @param {string} reason - Reason for status change (required for suspension)
 * @param {boolean} sendEmail - Whether to send notification email (default: true)
 * @returns {Promise<Object>} Updated user status
 */
async function changeUserStatus(userId, status, reason = '', sendEmail = true) {
  try {
    // Validate status is one of the allowed values
    if (!['active', 'inactive', 'suspended'].includes(status)) {
      toast.error('Invalid status. Must be active, inactive, or suspended');
      return null;
    }
    
    // Validate reason is provided for suspension
    if (status === 'suspended' && (!reason || reason.trim().length < 5)) {
      toast.error('Please provide a valid reason for suspension (minimum 5 characters)');
      return null;
    }
    
    // Prepare payload
    const payload = { 
      status,
      reason: status === 'suspended' ? reason : undefined,
      sendEmail // Include the sendEmail flag to trigger email notifications
    };
    
    // Make API request using the bridge
    const response = await apiBridge.patch(`${ENDPOINTS.USERS.BASE}/${userId}/status`, payload, {
      clearCacheEndpoint: ENDPOINTS.USERS.BASE
    });
    
    // Process response
    if (response.data && response.data.success) {
      const updatedUser = response.data.data.user;
      toast.success(`User ${status === 'active' ? 'activated' : status === 'inactive' ? 'deactivated' : 'suspended'} successfully`);
      
      // Return updated user data
      return updatedUser;
    }
    
    // Handle unexpected response
    console.error('Unexpected API response format:', response.data);
    toast.error(`Failed to ${status} user: Unexpected response format`);
    return null;
  } catch (error) {
    console.error(`Error changing user status to ${status} for user ${userId}:`, error);
    // Use standardized error handling
    BaseService.handleError(error, 'UserService.changeUserStatus', true);
    throw error;
  }
}

export default changeUserStatus;
