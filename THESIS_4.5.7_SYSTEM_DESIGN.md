# 4.5.7 System Design

## 4.5.7.1 System Architecture Overview

The Deyncare system is a simple debt management platform with 5 main components that work together to help businesses track customer debts and payments.

### System Components

The system has these main parts:

1. **Mobile App** - Built with Flutter for Android and iOS phones
2. **Web Dashboard** - Built with <PERSON>act for administrators to use on computers
3. **Backend API** - Built with Node.js to handle all business logic
4. **Database** - MongoDB to store all data
5. **ML API** - Python service to assess payment risks

### How Components Connect

- Mobile App and Web Dashboard both connect to the Backend API
- Backend API connects to the Database to store and get data
- Backend API connects to ML API to check if customers might not pay
- Backend API connects to external services for payments and notifications

### Simple Architecture

The system uses a simple client-server architecture where:
- **Clients** (Mobile App, Web Dashboard) send requests to the server
- **Server** (Backend API) processes requests and manages data
- **Database** stores all information
- **External Services** handle payments and notifications

## 4.5.7.2 Data Flow Diagrams (DFD)

### Level 0 DFD: Context Diagram

This shows how the Deyncare system interacts with external users and services:

**External Entities:**
- **Users**: People using the mobile app
- **Administrators**: People managing the system through web dashboard
- **Payment Service**: External service that processes payments

**Data Flows:**
- Users send customer data and payment requests to the system
- System sends debt information and notifications back to users
- Administrators send management commands and get reports
- Payment service exchanges payment status with the system

### Level 1 DFD: Main System Processes

**Simple Text-Based Data Flow Diagram:**

```
    USERS ────┐
              │
              ├──► 1. LOGIN ──────────► DATABASE
              │
              ├──► 2. MANAGE CUSTOMERS ► DATABASE
              │
              ├──► 3. TRACK DEBTS ─────► DATABASE
              │
              └──► 4. PROCESS PAYMENTS ► DATABASE
                          ▲                │
                          │                ▼
                   CUSTOMERS         PAYMENT SERVICE
```

**Process Descriptions:**

**Process 1: Login**
- Users enter login details
- System checks user information in database
- System allows or denies access

**Process 2: Manage Customers**
- Users add or update customer information
- System saves customer data to database
- System shows customer lists to users

**Process 3: Track Debts**
- Users create and view debt records
- System stores debt information in database
- System shows debt status to users

**Process 4: Process Payments**
- Users and customers make payment requests
- System processes payments through payment service
- System updates database and confirms payments

## ******* Sequence Diagrams

### *******.1 Simple Login Process

This shows how a user logs into the system:

1. User enters login details in the app
2. App sends login request to backend
3. Backend checks user details in database
4. Database confirms user exists
5. Backend sends success message to app
6. App shows dashboard to user

### *******.2 Simple Payment Process

This shows how a payment is processed:

1. User requests to make a payment in the app
2. App sends payment request to backend
3. Backend sends payment to external payment service
4. Payment service processes the payment
5. Payment service confirms payment success
6. Backend updates the database
7. App shows success message to user

## ******* Database Design

### *******.1 Complete Database Structure

The system uses MongoDB database with these main tables:

**Core Business Tables:**

1. **Shop Table**
   - Stores business information
   - Fields: id, name, owner_name, email, phone, address, status

2. **User Table**
   - Stores user login information
   - Fields: id, email, password, role, shop_id, status

3. **Customer Table**
   - Stores customer information
   - Fields: id, name, phone, address, shop_id, risk_level

4. **Debt Table**
   - Stores debt information
   - Fields: id, amount, paid_amount, due_date, status, customer_id, shop_id

5. **Payment Table**
   - Stores payment records
   - Fields: id, amount, date, method, debt_id, customer_id

**System Management Tables:**

6. **Subscription Table**
   - Stores shop subscription information
   - Fields: id, shop_id, plan_id, status, start_date, end_date

7. **Plan Table**
   - Stores available subscription plans
   - Fields: id, name, price, duration, features

8. **Notification Table**
   - Stores system notifications
   - Fields: id, message, type, user_id, shop_id, sent_date

**Key Relationships:**
- One shop employs many users
- One shop serves many customers
- One shop tracks many debts and receives many payments
- One shop has one subscription
- One customer owes many debts and makes many payments
- One debt receives many payments
- One plan defines many subscriptions

### *******.2 Detailed Data Structure

**Shop Data:**
- ID: Unique shop identifier
- Name: Business name
- Owner Name: Shop owner's name
- Email: Business contact email
- Phone: Business contact number
- Address: Business location
- Status: active, inactive, suspended

**User Data:**
- ID: Unique user identifier
- Email: User login email
- Password: Encrypted password
- Role: admin, employee, or manager
- Shop ID: Which shop the user belongs to
- Status: active, inactive, suspended

**Customer Data:**
- ID: Unique customer identifier
- Name: Customer full name
- Phone: Contact number
- Address: Customer location
- Shop ID: Which shop serves this customer
- Risk Level: low, medium, high (from ML analysis)

**Debt Data:**
- ID: Unique debt identifier
- Amount: Total debt amount
- Paid Amount: Amount already paid
- Due Date: When payment is due
- Status: active, paid, overdue
- Customer ID: Which customer owes this debt
- Shop ID: Which shop is owed this debt

**Payment Data:**
- ID: Unique payment identifier
- Amount: Payment amount
- Date: When payment was made
- Method: cash, mobile money, bank transfer
- Debt ID: Which debt this payment is for
- Customer ID: Which customer made this payment

**Subscription Data:**
- ID: Unique subscription identifier
- Shop ID: Which shop has this subscription
- Plan ID: Which plan the shop is subscribed to
- Status: active, expired, cancelled
- Start Date: When subscription started
- End Date: When subscription expires

**Plan Data:**
- ID: Unique plan identifier
- Name: Plan name (Basic, Premium, Enterprise)
- Price: Monthly or yearly price
- Duration: monthly, yearly
- Features: What features are included

**Notification Data:**
- ID: Unique notification identifier
- Message: Notification text
- Type: payment reminder, risk alert, system update
- User ID: Which user receives this notification
- Shop ID: Which shop this notification is for
- Sent Date: When notification was sent

## ******* System Features

### *******.1 Main Features

**User Management:**
- User login and authentication
- Different user roles (admin, employee)
- Secure password storage

**Customer Management:**
- Add new customers
- View customer information
- Update customer details
- Search customers

**Debt Tracking:**
- Create new debts for customers
- Track payment status
- View outstanding amounts
- Monitor due dates

**Payment Processing:**
- Record payments from customers
- Support multiple payment methods
- Integration with mobile money services
- Automatic debt updates

**Risk Assessment:**
- Automatic risk calculation using ML
- Alert system for high-risk customers
- Payment behavior analysis

### *******.2 System Benefits

**For Business Owners:**
- Better debt management
- Reduced payment delays
- Automated risk alerts
- Easy customer tracking

**For Employees:**
- Simple mobile interface
- Quick payment recording
- Customer information access
- Real-time updates

**For Customers:**
- Multiple payment options
- Payment confirmations
- Debt status visibility

This simple system design provides an effective solution for debt management while being easy to understand and maintain.

This simple system design provides an effective solution for debt management while being easy to understand and maintain.
