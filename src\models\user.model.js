const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
const mongoosePaginate = require('mongoose-paginate-v2');

const userSchema = new mongoose.Schema({
  userId: {
    type: String,
    required: true,
    unique: true,
    trim: true
  },
  fullName: {
    type: String,
    required: true,
    trim: true
  },
  userTitle: {
    type: String,
    trim: true,
    maxlength: 50,
    default: null // Job title/position for employees
  },
  email: {
    type: String,
    required: true,
    unique: true,
    lowercase: true,
    trim: true
  },
  phone: {
    type: String,
    required: true,
    trim: true
  },
  password: {
    type: String,
    required: true
  },
  role: {
    type: String,
    enum: ['superAdmin', 'admin', 'employee'],
    required: true
  },
  shopId: {
    type: String,
    default: null // superAdmin can operate without a shop, admin/employee must have a shop
  },
  status: {
    type: String,
    enum: ['active', 'inactive', 'suspended', 'pending', 'pending_email_verification', 'email_verified_pending_payment', 'deleted_orphaned'],
    default: 'pending'
  },
  // New field: Flag for suspension
  isSuspended: {
    type: Boolean,
    default: false
  },
  // New field: Suspension reason
  suspensionReason: {
    type: String,
    default: null
  },
  // New field: Suspension timestamp
  suspendedAt: {
    type: Date,
    default: null
  },
  // New field: Who suspended the user
  suspendedBy: {
    type: String,
    default: null
  },
  verified: {
    type: Boolean,
    default: false
  },
  // Enhancement: Email verification status
  emailVerified: {
    type: Boolean,
    default: false
  },
  // New fields for multi-step registration flow
  isPaid: {
    type: Boolean,
    default: false // Tracks if user has completed payment for their plan
  },
  isActivated: {
    type: Boolean,
    default: false // True if emailVerified and isPaid are both true
  },
  verificationCode: {
    type: String
  },
  verifiedAt: {
    type: Date
  },
  // New field: Last login time tracking
  lastLoginAt: {
    type: Date,
    default: null
  },
  // New field: Login history
  loginHistory: [{
    timestamp: {
      type: Date,
      default: Date.now
    },
    ipAddress: String,
    device: String,
    browser: String,
    location: String,
    status: {
      type: String,
      enum: ['success', 'failed'],
      default: 'success'
    }
  }],
  // Password reset fields
  resetPasswordToken: {
    type: String
  },
  resetPasswordExpires: {
    type: Date
  },
  // New field: Password history
  passwordHistory: [{
    password: String,
    changedAt: {
      type: Date,
      default: Date.now
    }
  }],
  // New field: Account preferences
  preferences: {
    theme: {
      type: String,
      enum: ['light', 'dark', 'system'],
      default: 'system'
    },
    language: {
      type: String,
      default: 'en'
    },
    notifications: {
      email: {
        type: Boolean,
        default: true
      },
      sms: {
        type: Boolean,
        default: true
      },
      app: {
        type: Boolean,
        default: true
      }
    }
  },

  // New field: Profile picture
  profilePicture: {
    type: String,
    default: null
  },
  // Firebase Cloud Messaging tokens for push notifications
  fcmTokens: [{
    token: {
      type: String,
      required: true
    },
    deviceInfo: {
      platform: String, // 'android', 'ios'
      deviceId: String,
      appVersion: String,
      osVersion: String
    },
    registeredAt: {
      type: Date,
      default: Date.now
    },
    lastUsed: {
      type: Date,
      default: Date.now
    }
  }],
  // Enhancement: Soft delete
  isDeleted: {
    type: Boolean,
    default: false
  },
  deletedAt: {
    type: Date,
    default: null
  },
  // Add visibility field to user schema
  visibility: {
    customerManagement: {
      create: { type: Boolean, default: false },
      update: { type: Boolean, default: false },
      view: { type: Boolean, default: false },
      delete: { type: Boolean, default: false }
    },
    debtManagement: {
      create: { type: Boolean, default: false },
      update: { type: Boolean, default: false },
      view: { type: Boolean, default: false },
      delete: { type: Boolean, default: false }
    },
    reportManagement: {
      generate: { type: Boolean, default: false },
      delete: { type: Boolean, default: false },
      view: { type: Boolean, default: false }
    }
  }
}, { 
  timestamps: true 
});

// Index for optimizing queries
userSchema.index({ shopId: 1, role: 1 });
userSchema.index({ email: 1 });
userSchema.index({ status: 1 });
userSchema.index({ lastLoginAt: 1 });

// Performance optimizations for authentication queries
userSchema.index({ userId: 1, status: 1, isDeleted: 1, isSuspended: 1 }); // Compound index for auth middleware
userSchema.index({ userId: 1, isDeleted: 1 }); // For settings requests
userSchema.index({ email: 1, isDeleted: 1 }); // For login queries
userSchema.index({ verificationCode: 1 }); // For email verification
userSchema.index({ resetPasswordToken: 1 }); // For password reset
userSchema.index({ 'fcmTokens.token': 1 }); // For push notifications
userSchema.index({ shopId: 1, status: 1 }); // For shop-based queries

// Apply the pagination plugin
userSchema.plugin(mongoosePaginate);

// Password hashing middleware
userSchema.pre('save', async function(next) {
  if (!this.isModified('password')) return next();
  
  try {
    // Store old password in history before changing
    if (this.password && this.isModified('password') && !this.isNew) {
      if (!this.passwordHistory) this.passwordHistory = [];
      
      // Only keep up to 5 previous passwords
      if (this.passwordHistory.length >= 5) {
        this.passwordHistory.shift();
      }
      
      this.passwordHistory.push({
        password: this.password,
        changedAt: new Date()
      });
    }
    
    const salt = await bcrypt.genSalt(10);
    this.password = await bcrypt.hash(this.password, salt);
    next();
  } catch (error) {
    next(error);
  }
});

// Method to compare passwords
userSchema.methods.comparePassword = async function(candidatePassword) {
  return bcrypt.compare(candidatePassword, this.password);
};

// Method to check if password was used before
userSchema.methods.isPasswordReused = async function(newPassword) {
  if (!this.passwordHistory || this.passwordHistory.length === 0) {
    return false;
  }
  
  // Check against stored password history
  for (const historyItem of this.passwordHistory) {
    if (await bcrypt.compare(newPassword, historyItem.password)) {
      return true;
    }
  }
  
  return false;
};

// Method to update last login time
userSchema.methods.updateLastLogin = function(loginData = {}) {
  this.lastLoginAt = new Date();
  
  // Add to login history
  if (!this.loginHistory) this.loginHistory = [];
  
  // Keep only last 10 login records
  if (this.loginHistory.length >= 10) {
    this.loginHistory.shift();
  }
  
  this.loginHistory.push({
    timestamp: new Date(),
    ipAddress: loginData.ipAddress || null,
    device: loginData.device || null,
    browser: loginData.browser || null,
    location: loginData.location || null,
    status: loginData.status || 'success'
  });
  
  return this.save();
};

// Method to add or update FCM token
userSchema.methods.addFCMToken = function(tokenData) {
  const { token, deviceInfo } = tokenData;
  
  if (!token) {
    throw new Error('FCM token is required');
  }
  
  // Remove existing token if it exists
  this.fcmTokens = this.fcmTokens.filter(t => t.token !== token);
  
  // Add new token
  this.fcmTokens.push({
    token: token,
    deviceInfo: deviceInfo || {},
    registeredAt: new Date(),
    lastUsed: new Date()
  });
  
  // Keep only last 5 tokens per user
  if (this.fcmTokens.length > 5) {
    this.fcmTokens = this.fcmTokens.slice(-5);
  }
  
  return this.save();
};

// Method to remove FCM token
userSchema.methods.removeFCMToken = function(token) {
  this.fcmTokens = this.fcmTokens.filter(t => t.token !== token);
  return this.save();
};

// Method to update FCM token last used time
userSchema.methods.updateFCMTokenUsage = function(token) {
  const tokenObj = this.fcmTokens.find(t => t.token === token);
  if (tokenObj) {
    tokenObj.lastUsed = new Date();
    return this.save();
  }
  return Promise.resolve(this);
};

// Method to get active FCM tokens (used in last 30 days)
userSchema.methods.getActiveFCMTokens = function() {
  const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
  return this.fcmTokens
    .filter(t => t.lastUsed > thirtyDaysAgo)
    .map(t => t.token);
};

// Updates auto-suspend status based on login attempts
userSchema.statics.checkFailedLoginAttempts = async function(userId) {
  const user = await this.findOne({ userId });
  if (!user) return null;
  
  // Count recent failed attempts (within last 30 minutes)
  const thirtyMinutesAgo = new Date(Date.now() - 30 * 60 * 1000);
  const recentFailedAttempts = user.loginHistory.filter(
    login => login.status === 'failed' && login.timestamp > thirtyMinutesAgo
  ).length;
  
  // Auto-suspend on too many failed attempts
  if (recentFailedAttempts >= 5 && !user.isSuspended) {
    user.isSuspended = true;
    user.status = 'suspended';
    user.suspensionReason = 'Automatic suspension due to multiple failed login attempts';
    await user.save();
  }
  
  return user;
};

// Static method to fix role mismatches in the database
userSchema.statics.fixRoleMismatches = async function() {
  const canonicalRoles = {
    'superadmin': 'superadmin',
    'SUPERADMIN': 'superadmin',
    'SuperAdmin': 'superadmin',
    'superAdmin': 'superadmin',
    'admin': 'admin',
    'ADMIN': 'admin',
    'employee': 'employee',
    'EMPLOYEE': 'employee'
  };
  const users = await this.find({});
  let updated = 0;
  for (const user of users) {
    const correct = canonicalRoles[user.role];
    if (correct && user.role !== correct) {
      user.role = correct;
      await user.save();
      updated++;
    }
  }
  return updated;
};

// Add method to check granular permissions
userSchema.methods.hasModulePermission = function(module, action) {
  // Admin and SuperAdmin have all permissions
  if (this.role === 'admin' || this.role === 'superAdmin') {
    return true;
  }
  
  // Check granular permission for employees
  return this.visibility && 
         this.visibility[module] && 
         this.visibility[module][action] === true;
};

const User = mongoose.model('User', userSchema);

module.exports = User;

