# SuperAdmin Shop API Testing Guide

## 🔧 **Complete CRUD Operations & Endpoints Testing**

This guide provides comprehensive testing scenarios for all SuperAdmin Shop API operations with exact payloads, expected responses, and error cases.

---

## 📋 **Table of Contents**

1. [Authentication Setup](#authentication-setup)
2. [GET Operations](#get-operations)
3. [POST Operations](#post-operations)
4. [PUT Operations](#put-operations)
5. [DELETE Operations](#delete-operations)
6. [Statistics & Analytics](#statistics--analytics)
7. [File Upload Operations](#file-upload-operations)
8. [<PERSON>rror <PERSON>rio<PERSON>](#error-scenarios)
9. [Performance Testing](#performance-testing)

---

## 🔐 **Authentication Setup**

All SuperAdmin Shop API endpoints require SuperAdmin authentication.

### Headers Required:
```http
Authorization: Bearer <SUPERADMIN_JWT_TOKEN>
Content-Type: application/json
```

### Get SuperAdmin Token:
```bash
# Login as SuperAdmin
curl -X POST http://localhost:5000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "SuperAdmin123!"
  }'
```

---

## 📊 **GET Operations**

### 1. **GET All Shops**

**Endpoint:** `GET /api/admin/shops`

```bash
# Basic request
curl -X GET "http://localhost:5000/api/admin/shops" \
  -H "Authorization: Bearer <TOKEN>"

# With pagination
curl -X GET "http://localhost:5000/api/admin/shops?page=1&limit=20" \
  -H "Authorization: Bearer <TOKEN>"

# With filters
curl -X GET "http://localhost:5000/api/admin/shops?status=active&search=MIDNIMO" \
  -H "Authorization: Bearer <TOKEN>"

# Advanced filtering
curl -X GET "http://localhost:5000/api/admin/shops?status=suspended&verified=true&planType=monthly&page=2&limit=10" \
  -H "Authorization: Bearer <TOKEN>"
```

**Expected Response:**
```json
{
  "success": true,
  "data": {
    "shops": [
      {
        "shopId": "shop_12345",
        "shopName": "MIDNIMO SHOP",
        "fullName": "Mohamud Muhidin",
        "email": "<EMAIL>",
        "phone": "+252611234567",
        "shopAddress": "Main Street, Hargeisa, Somalia",
        "status": "active",
        "verified": true,
        "isPaid": true,
        "isActivated": true,
        "logoUrl": "https://deyncare.com/logos/shop_12345.jpg",
        "createdAt": "2024-01-15T10:30:00.000Z",
        "updatedAt": "2024-01-20T14:20:00.000Z"
      }
    ],
    "currentPage": 1,
    "totalPages": 5,
    "total": 87,
    "hasNextPage": true,
    "hasPrevPage": false
  },
  "message": "Shops retrieved successfully"
}
```

### 2. **GET Shop by ID**

**Endpoint:** `GET /api/admin/shops/:shopId`

```bash
curl -X GET "http://localhost:5000/api/admin/shops/shop_12345" \
  -H "Authorization: Bearer <TOKEN>"
```

**Expected Response:**
```json
{
  "success": true,
  "data": {
    "shopId": "shop_12345",
    "shopName": "MIDNIMO SHOP",
    "fullName": "Mohamud Muhidin",
    "email": "<EMAIL>",
    "phone": "+252611234567",
    "shopAddress": "Main Street, Hargeisa, Somalia",
    "status": "active",
    "verified": true,
    "isPaid": true,
    "isActivated": true,
    "logoUrl": "https://deyncare.com/logos/shop_12345.jpg",
    "businessMetrics": {
      "totalRevenue": 15000,
      "totalSales": 250,
      "totalCustomers": 89,
      "totalProducts": 150
    },
    "createdAt": "2024-01-15T10:30:00.000Z",
    "updatedAt": "2024-01-20T14:20:00.000Z"
  },
  "message": "Shop retrieved successfully"
}
```

---

## ➕ **POST Operations**

### 3. **CREATE New Shop**

**Endpoint:** `POST /api/admin/shops`

```bash
# Standard creation
curl -X POST "http://localhost:5000/api/admin/shops" \
  -H "Authorization: Bearer <TOKEN>" \
  -H "Content-Type: application/json" \
  -d '{
    "fullName": "Ahmed Hassan",
    "email": "<EMAIL>",
    "phone": "+252615678901",
    "password": "SecurePass123!",
    "shopName": "AHMED ELECTRONICS",
    "shopAddress": "Wadajir District, Mogadishu, Somalia",
    "planType": "monthly"
  }'

# With logo upload (multipart/form-data)
curl -X POST "http://localhost:5000/api/admin/shops" \
  -H "Authorization: Bearer <TOKEN>" \
  -F "fullName=Ahmed Hassan" \
  -F "email=<EMAIL>" \
  -F "phone=+252615678901" \
  -F "password=SecurePass123!" \
  -F "shopName=AHMED ELECTRONICS" \
  -F "shopAddress=Wadajir District, Mogadishu, Somalia" \
  -F "planType=monthly" \
  -F "shopLogo=@/path/to/logo.jpg"
```

**Expected Response:**
```json
{
  "success": true,
  "data": {
    "shopId": "shop_67890",
    "shopName": "AHMED ELECTRONICS",
    "fullName": "Ahmed Hassan",
    "email": "<EMAIL>",
    "phone": "+252615678901",
    "shopAddress": "Wadajir District, Mogadishu, Somalia",
    "status": "active",
    "verified": true,
    "isPaid": true,
    "isActivated": true,
    "logoUrl": "https://deyncare.com/logos/shop_67890.jpg",
    "user": {
      "userId": "user_12345",
      "email": "<EMAIL>",
      "role": "shopOwner",
      "emailVerified": true
    },
    "createdAt": "2024-01-22T09:15:00.000Z"
  },
  "message": "Shop created successfully"
}
```

---

## ✏️ **PUT Operations**

### 4. **UPDATE Shop Details**

**Endpoint:** `PUT /api/admin/shops/:shopId`

```bash
curl -X PUT "http://localhost:5000/api/admin/shops/shop_12345" \
  -H "Authorization: Bearer <TOKEN>" \
  -H "Content-Type: application/json" \
  -d '{
    "shopName": "MIDNIMO ELECTRONICS UPDATED",
    "shopAddress": "New Location, Hargeisa, Somalia",
    "phone": "+252611234999"
  }'
```

**Expected Response:**
```json
{
  "success": true,
  "data": {
    "shopId": "shop_12345",
    "shopName": "MIDNIMO ELECTRONICS UPDATED",
    "shopAddress": "New Location, Hargeisa, Somalia",
    "phone": "+252611234999",
    "updatedAt": "2024-01-22T11:30:00.000Z"
  },
  "message": "Shop updated successfully"
}
```

### 5. **UPDATE Shop Status**

**Endpoint:** `PUT /api/admin/shops/:shopId/status`

```bash
# Suspend shop
curl -X PUT "http://localhost:5000/api/admin/shops/shop_12345/status" \
  -H "Authorization: Bearer <TOKEN>" \
  -H "Content-Type: application/json" \
  -d '{
    "status": "suspended",
    "reason": "Violating terms of service",
    "sendEmail": true,
    "duration": 30
  }'

# Activate shop
curl -X PUT "http://localhost:5000/api/admin/shops/shop_12345/status" \
  -H "Authorization: Bearer <TOKEN>" \
  -H "Content-Type: application/json" \
  -d '{
    "status": "active",
    "reason": "Suspension period completed",
    "sendEmail": true
  }'
```

**Expected Response:**
```json
{
  "success": true,
  "data": {
    "shopId": "shop_12345",
    "status": "suspended",
    "statusDetails": {
      "reason": "Violating terms of service",
      "changedAt": "2024-01-22T12:00:00.000Z",
      "changedBy": "superAdmin",
      "duration": 30
    },
    "updatedAt": "2024-01-22T12:00:00.000Z"
  },
  "message": "Shop suspended successfully"
}
```

### 6. **UPDATE Shop Logo**

**Endpoint:** `PUT /api/admin/shops/:shopId/logo`

```bash
curl -X PUT "http://localhost:5000/api/admin/shops/shop_12345/logo" \
  -H "Authorization: Bearer <TOKEN>" \
  -F "shopLogo=@/path/to/new-logo.jpg"
```

**Expected Response:**
```json
{
  "success": true,
  "data": {
    "shopId": "shop_12345",
    "logoUrl": "https://deyncare.com/logos/shop_12345_updated.jpg",
    "updatedAt": "2024-01-22T13:15:00.000Z"
  },
  "message": "Shop logo updated successfully"
}
```

---

## 🗑️ **DELETE Operations**

### 7. **DELETE Shop**

**Endpoint:** `DELETE /api/admin/shops/:shopId`

```bash
curl -X DELETE "http://localhost:5000/api/admin/shops/shop_12345" \
  -H "Authorization: Bearer <TOKEN>" \
  -H "Content-Type: application/json" \
  -d '{
    "reason": "Shop requested permanent closure",
    "confirmDeletion": true
  }'
```

**Expected Response:**
```json
{
  "success": true,
  "data": {
    "shopId": "shop_12345",
    "deletedAt": "2024-01-22T14:30:00.000Z",
    "reason": "Shop requested permanent closure"
  },
  "message": "Shop deleted successfully"
}
```

---

## 📊 **Statistics & Analytics**

### 8. **GET Shop Statistics**

**Endpoint:** `GET /api/admin/shops/stats`

```bash
# Basic stats
curl -X GET "http://localhost:5000/api/admin/shops/stats" \
  -H "Authorization: Bearer <TOKEN>"

# Filtered stats
curl -X GET "http://localhost:5000/api/admin/shops/stats?period=30days&status=active" \
  -H "Authorization: Bearer <TOKEN>"
```

**Expected Response:**
```json
{
  "success": true,
  "data": {
    "total": 150,
    "active": 120,
    "suspended": 15,
    "inactive": 10,
    "pending": 5,
    "verified": 135,
    "unverified": 15,
    "registrationStats": {
      "thisMonth": 25,
      "lastMonth": 18,
      "growthRate": "+38.9%"
    },
    "statusDistribution": {
      "active": 80,
      "suspended": 10,
      "inactive": 6.7,
      "pending": 3.3
    },
    "planDistribution": {
      "monthly": 85,
      "yearly": 45,
      "trial": 20
    },
    "revenueMetrics": {
      "totalMRR": 12750,
      "averageRevenuePerShop": 85
    },
    "growth": {
      "dailyGrowth": [
        {"date": "2024-01-20", "newShops": 3},
        {"date": "2024-01-21", "newShops": 5},
        {"date": "2024-01-22", "newShops": 2}
      ],
      "monthlyTrend": "+15.2%"
    },
    "chartData": [
      {"name": "Jan", "shops": 120},
      {"name": "Feb", "shops": 135},
      {"name": "Mar", "shops": 150}
    ]
  },
  "message": "Shop statistics retrieved successfully"
}
```

---

## 📁 **File Upload Operations**

### 9. **Test Logo Upload Validation**

```bash
# Test file size limit (should fail for files > 5MB)
curl -X POST "http://localhost:5000/api/admin/shops" \
  -H "Authorization: Bearer <TOKEN>" \
  -F "fullName=Test User" \
  -F "email=<EMAIL>" \
  -F "phone=+252611111111" \
  -F "password=Test123!" \
  -F "shopName=TEST SHOP" \
  -F "shopAddress=Test Address" \
  -F "planType=monthly" \
  -F "shopLogo=@/path/to/large-file.jpg"

# Test file type validation (should fail for non-image files)
curl -X PUT "http://localhost:5000/api/admin/shops/shop_12345/logo" \
  -H "Authorization: Bearer <TOKEN>" \
  -F "shopLogo=@/path/to/document.pdf"
```

---

## ❌ **Error Scenarios**

### 10. **Test Error Handling**

```bash
# Missing required fields
curl -X POST "http://localhost:5000/api/admin/shops" \
  -H "Authorization: Bearer <TOKEN>" \
  -H "Content-Type: application/json" \
  -d '{
    "shopName": "Incomplete Shop"
  }'

# Expected: 400 Bad Request
{
  "success": false,
  "error": "Validation failed",
  "details": {
    "fullName": "Full name is required",
    "email": "Email is required",
    "password": "Password is required"
  }
}

# Non-existent shop
curl -X GET "http://localhost:5000/api/admin/shops/nonexistent_shop" \
  -H "Authorization: Bearer <TOKEN>"

# Expected: 404 Not Found
{
  "success": false,
  "error": "Shop not found",
  "message": "No shop found with ID: nonexistent_shop"
}

# Unauthorized access (without token)
curl -X GET "http://localhost:5000/api/admin/shops"

# Expected: 401 Unauthorized
{
  "success": false,
  "error": "Unauthorized",
  "message": "Access token is required"
}

# Insufficient permissions (non-SuperAdmin)
curl -X GET "http://localhost:5000/api/admin/shops" \
  -H "Authorization: Bearer <REGULAR_USER_TOKEN>"

# Expected: 403 Forbidden
{
  "success": false,
  "error": "Forbidden",
  "message": "SuperAdmin access required"
}
```

---

## ⚡ **Performance Testing**

### 11. **Load Testing Scenarios**

```bash
# Test pagination with large datasets
for i in {1..10}; do
  curl -X GET "http://localhost:5000/api/admin/shops?page=$i&limit=100" \
    -H "Authorization: Bearer <TOKEN>" &
done

# Test concurrent shop creation
for i in {1..5}; do
  curl -X POST "http://localhost:5000/api/admin/shops" \
    -H "Authorization: Bearer <TOKEN>" \
    -H "Content-Type: application/json" \
    -d "{
      \"fullName\": \"Load Test User $i\",
      \"email\": \"loadtest$<EMAIL>\",
      \"phone\": \"+25261000000$i\",
      \"password\": \"LoadTest123!\",
      \"shopName\": \"LOAD TEST SHOP $i\",
      \"shopAddress\": \"Load Test Address $i\",
      \"planType\": \"monthly\"
    }" &
done
```

---

## 🧪 **API Testing Checklist**

### ✅ **Required Tests:**

**Authentication:**
- [ ] Valid SuperAdmin token works
- [ ] Invalid token returns 401
- [ ] Missing token returns 401
- [ ] Non-SuperAdmin token returns 403

**GET Operations:**
- [ ] Get all shops with default pagination
- [ ] Get shops with custom pagination
- [ ] Get shops with status filter
- [ ] Get shops with search filter
- [ ] Get single shop by valid ID
- [ ] Get single shop by invalid ID (404)
- [ ] Get shop statistics

**POST Operations:**
- [ ] Create shop with all required fields
- [ ] Create shop with logo upload
- [ ] Create shop with missing fields (400)
- [ ] Create shop with invalid email format (400)
- [ ] Create shop with duplicate email (409)

**PUT Operations:**
- [ ] Update shop with valid data
- [ ] Update shop status to suspended
- [ ] Update shop status to active
- [ ] Update shop logo
- [ ] Update non-existent shop (404)

**DELETE Operations:**
- [ ] Delete shop with valid reason
- [ ] Delete non-existent shop (404)
- [ ] Delete without confirmation (400)

**File Upload:**
- [ ] Upload valid image file
- [ ] Upload file too large (400)
- [ ] Upload invalid file type (400)

**Performance:**
- [ ] Response time under 2 seconds for list operations
- [ ] Response time under 1 second for single shop operations
- [ ] Concurrent requests handled properly

---

## 🔧 **Environment Setup for Testing**

### Required Environment Variables:
```bash
# .env
NODE_ENV=development
PORT=5000
JWT_SECRET=your_jwt_secret_here
MONGODB_URI=mongodb://localhost:27017/deyncare_test
SUPERADMIN_EMAIL=<EMAIL>
SUPERADMIN_PASSWORD=SuperAdmin123!
```

### Database Setup:
```bash
# Ensure MongoDB is running
mongod --dbpath /path/to/data

# Create test SuperAdmin user
npm run setup:superadmin
```

---

## 📝 **Notes**

1. **Always use SuperAdmin token** for these endpoints
2. **Validate payloads** match exact field names (`fullName`, `shopAddress`)
3. **Test error scenarios** to ensure proper error handling
4. **Check response times** for performance requirements
5. **Verify email notifications** are sent for status changes
6. **Test file uploads** with various file types and sizes

---

*This guide covers all SuperAdmin Shop API operations. Update test scenarios as new features are added.* 