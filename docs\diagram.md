erDiagram
    SHOP {
        string shopId PK
        string shopName
        string ownerName
        string email
        string phone
        string address
        object location
        string status
        boolean verified
        string currentSubscriptionId FK
        object statistics
        datetime createdAt
        datetime updatedAt
    }
    
    USER {
        string userId PK
        string fullName
        string email
        string phone
        string password
        string role
        string shopId FK
        string status
        boolean emailVerified
        boolean isPaid
        boolean isActivated
        datetime lastLoginAt
        datetime createdAt
    }
    
    CUSTOMER {
        string customerId PK
        string shopId FK
        string fullName
        string email
        string phone
        string alternativePhone
        string category
        number outstandingBalance
        number creditLimit
        number riskScore
        number totalDebtAmount
        datetime lastPurchaseDate
        datetime createdAt
    }
    
    DEBT {
        string debtId PK
        string shopId FK
        string customerId FK
        string customerName
        number debtAmount
        number paidAmount
        boolean isSettled
        datetime dueDate
        string status
        number riskScore
        string riskLevel
        datetime createdAt
    }
    
    PAYMENT {
        string paymentId PK
        string shopId FK
        string customerId FK
        string debtId FK
        string subscriptionId FK
        string paymentContext
        number amount
        string method
        string status
        boolean isConfirmed
        string receiptNumber
        datetime paymentDate
        datetime createdAt
    }
    
    PRODUCT {
        string productId PK
        string shopId FK
        string name
        string description
        number price
        number cost
        number stockQuantity
        string categoryId
        string status
        datetime createdAt
    }
    

    
    SUBSCRIPTION {
        string subscriptionId PK
        string shopId FK
        string planId FK
        object plan
        object pricing
        string status
        object payment
        object dates
        datetime createdAt
    }
    
    PLAN {
        string planId PK
        string name
        string type
        string displayName
        object pricing
        object features
        object limits
        boolean isActive
        datetime createdAt
    }
    
    NOTIFICATION {
        string notificationId PK
        string shopId FK
        string recipient
        string type
        string priority
        string message
        string status
        number deliveryAttempts
        datetime createdAt
    }
    
    DISCOUNT_CODE {
        string discountId PK
        string code
        string type
        number value
        datetime startDate
        datetime expiryDate
        number usageCount
        string scope
        string shopId FK
        boolean isActive
    }
    
    SETTING {
        string key PK
        string category
        mixed value
        string dataType
        string accessLevel
        string shopId FK
        string updatedBy FK
        datetime updatedAt
    }

    %% Primary Relationships
    SHOP ||--o{ USER : "has employees"
    SHOP ||--o{ CUSTOMER : "serves customers"
    SHOP ||--o{ PRODUCT : "sells products"
    SHOP ||--o{ DEBT : "tracks debts"
    SHOP ||--o{ PAYMENT : "receives payments"

    SHOP ||--|| SUBSCRIPTION : "has subscription"
    SHOP ||--o{ NOTIFICATION : "sends notifications"
    SHOP ||--o{ DISCOUNT_CODE : "creates discounts"
    SHOP ||--o{ SETTING : "configures settings"
    
    %% Customer Relationships
    CUSTOMER ||--o{ DEBT : "owes money"
    CUSTOMER ||--o{ PAYMENT : "makes payments"

    
    %% Debt and Payment Relationships
    DEBT ||--o{ PAYMENT : "settled by"
    

    
    %% Subscription Relationships
    PLAN ||--o{ SUBSCRIPTION : "defines plan"
    SUBSCRIPTION ||--o{ PAYMENT : "subscription payments"
    
    %% User Relationships
    USER ||--o{ PAYMENT : "records payments"
    USER ||--o{ SETTING : "updates settings"