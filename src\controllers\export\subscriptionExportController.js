/**
 * Subscription Export Controller
 * Handles export requests for subscriptions
 */
const BaseExportController = require('./baseExportController');
const { SubscriptionService } = require('../../services');
const { logError } = require('../../utils');

class SubscriptionExportController extends BaseExportController {
  /**
   * Get subscription export fields
   * @param {Object} req - Express request object
   * @returns {Array} Field configurations
   */
  static async getExportFields(req) {
    return [
      {
        label: 'Shop Name',
        key: 'shop.name',
        type: 'string'
      },
      {
        label: 'Plan Name',
        key: 'plan.name',
        type: 'string'
      },
      {
        label: 'Plan Type',
        key: 'plan.type',
        type: 'string'
      },
      {
        label: 'Status',
        key: 'status',
        type: 'string'
      },
      {
        label: 'Start Date',
        key: 'startDate',
        type: 'date'
      },
      {
        label: 'Expiry Date',
        key: 'dates.endDate',
        type: 'date'
      },
      {
        label: 'Payment Method',
        key: 'payment.method',
        type: 'string'
      },
      {
        label: 'Created At',
        key: 'createdAt',
        type: 'datetime'
      },
      {
        label: 'Updated At',
        key: 'updatedAt',
        type: 'datetime'
      }
    ];
  }

  /**
   * Get subscription data for export
   * @param {Object} req - Express request object
   * @returns {Array} Subscription data
   */
  static async getExportData(req) {
    try {
      const {
        status,
        planType,
        paymentMethod,
        autoRenew,
        dateRange
      } = req.query;

      const filter = {};
      
      // Apply filters
      if (status) filter.status = status;
      if (planType) filter['plan.type'] = planType;
      if (paymentMethod) filter['payment.method'] = paymentMethod;
      if (autoRenew !== undefined) filter['renewalSettings.autoRenew'] = autoRenew === 'true';
      
      // Apply date range filter
      if (dateRange) {
        if (dateRange.startDate) {
          filter['dates.startDate'] = { $gte: new Date(dateRange.startDate) };
        }
        if (dateRange.endDate) {
          filter['dates.endDate'] = { $lte: new Date(dateRange.endDate) };
        }
      }

      // Get subscriptions with no limit for export
      const result = await SubscriptionService.getAllSubscriptions(filter, { limit: 999999 });
      
      // Return the subscriptions array from the response
      return result.subscriptions || [];
    } catch (error) {
      logError('Failed to get subscription data for export', 'SubscriptionExportController', error);
      throw error;
    }
  }

  /**
   * Export subscriptions to CSV
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next middleware function
   */
  static async exportToCSV(req, res, next) {
    req.exportConfig = {
      module: 'subscriptions',
      getFields: this.getExportFields,
      getData: this.getExportData,
      filename: req.query.filename
    };

    await this.prepareExport(req, res, next);
    return super.exportToCSV(req, res, next);
  }

  /**
   * Export subscriptions to Excel
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next middleware function
   */
  static async exportToExcel(req, res, next) {
    req.exportConfig = {
      module: 'subscriptions',
      getFields: this.getExportFields,
      getData: this.getExportData,
      filename: req.query.filename,
      options: {
        sheetName: 'Subscriptions',
        styling: {
          header: true,
          columns: {
            'startDate': { width: 15 },
            'dates.endDate': { width: 15 },
            'payment.method': { width: 15 },
            'createdAt': { width: 20 },
            'updatedAt': { width: 20 }
          }
        }
      }
    };

    await this.prepareExport(req, res, next);
    return super.exportToExcel(req, res, next);
  }
}

module.exports = SubscriptionExportController; 