"use client";

import { useResponsive } from "@/hooks/use-responsive";
import { cn } from "@/lib/utils";
import { useEffect, useState } from "react";

/**
 * ResponsiveContainer - A wrapper component that provides responsive behavior
 * and optimal layout for different screen sizes
 *
 * @param {Object} props Component props
 * @param {React.ReactNode} props.children Content to display inside the container
 * @param {string} props.className Additional CSS classes
 * @param {string} props.as HTML element to render as (default: 'div')
 * @param {Object} props.sizeProps Custom props for different screen sizes
 * @param {function} props.onBreakpointChange Callback when breakpoint changes
 */
export function ResponsiveContainer({
  children,
  className,
  as: Component = "div",
  sizeProps = {},
  onBreakpointChange,
  ...props
}) {
  const {
    breakpoint,
    isXs,
    isSm,
    isMd,
    isLg,
    isXl,
    is2Xl,
    isMobile,
    isTablet,
    isDesktop,
    width
  } = useResponsive();

  // Keep track of previous breakpoint to detect changes
  const [prevBreakpoint, setPrevBreakpoint] = useState(null);

  // Determine padding based on screen size - avoid conflicts with dashboard layout
  const getPadding = () => {
    // Dashboard layout already provides padding, so we use minimal padding here
    // Only add padding if explicitly requested via className
    return "";
  };

  // Determine max-width based on screen size - let dashboard layout handle this
  const getMaxWidth = () => {
    // Dashboard layout already provides max-w-7xl, so we don't add additional constraints
    // This prevents double scroll issues
    return "max-w-none";
  };

  // Apply responsive classes
  const responsiveClasses = cn(
    // Base layout
    "w-full mx-auto transition-all duration-300",
    
    // Dynamic padding
    getPadding(),
    
    // Max width constraints
    getMaxWidth(),
    
    // Apply any custom classes
    className
  );

  // Get the correct set of props based on current breakpoint
  const breakpointProps = sizeProps[breakpoint] || {};

  // Trigger callback when breakpoint changes
  useEffect(() => {
    if (prevBreakpoint !== breakpoint) {
      setPrevBreakpoint(breakpoint);
      if (onBreakpointChange) {
        onBreakpointChange(breakpoint, prevBreakpoint);
      }
    }
  }, [breakpoint, prevBreakpoint, onBreakpointChange]);

  return (
    <Component
      className={responsiveClasses}
      data-breakpoint={breakpoint}
      data-device={isMobile ? "mobile" : isTablet ? "tablet" : "desktop"}
      {...props}
      {...breakpointProps}
    >
      {children}
    </Component>
  );
}

/**
 * ResponsiveGrid - A responsive grid component that adjusts columns based on screen size
 * 
 * @param {Object} props Component props
 * @param {React.ReactNode} props.children Grid items
 * @param {string} props.className Additional CSS classes
 * @param {number|Object} props.columns Number of columns or object with breakpoint keys
 * @param {string} props.gap Grid gap (default: 'gap-4')
 */
export function ResponsiveGrid({
  children,
  className,
  columns = {
    xs: 1,
    sm: 2,
    md: 3,
    lg: 4,
    xl: 4,
    "2xl": 4
  },
  gap = "gap-4",
  ...props
}) {
  const { breakpoint } = useResponsive();
  
  // Determine number of columns
  const getColumns = () => {
    if (typeof columns === "number") {
      return columns;
    }
    
    return columns[breakpoint] || columns.xs || 1;
  };
  
  // Generate grid template columns
  const gridCols = `grid-cols-${getColumns()}`;
  
  return (
    <div
      className={cn(
        "grid w-full",
        gridCols,
        gap,
        className
      )}
      {...props}
    >
      {children}
    </div>
  );
}

/**
 * ResponsiveShow - Component that conditionally renders content based on breakpoint
 * 
 * @param {Object} props Component props
 * @param {React.ReactNode} props.children Content to conditionally display
 * @param {string|Array} props.show Breakpoints to show content at (e.g., 'md' or ['sm', 'md'])
 * @param {string|Array} props.hide Breakpoints to hide content at
 * @param {boolean} props.showMobile Show on mobile devices
 * @param {boolean} props.showTablet Show on tablet devices
 * @param {boolean} props.showDesktop Show on desktop devices
 */
export function ResponsiveShow({
  children,
  show,
  hide,
  showMobile,
  showTablet,
  showDesktop,
  ...props
}) {
  const responsive = useResponsive();
  const { breakpoint, isMobile, isTablet, isDesktop } = responsive;
  
  // Convert single string to array
  const showBreakpoints = Array.isArray(show) ? show : show ? [show] : [];
  const hideBreakpoints = Array.isArray(hide) ? hide : hide ? [hide] : [];
  
  // Determine if content should be shown
  const shouldShow = () => {
    // Check device type constraints
    if (showMobile !== undefined && isMobile) return showMobile;
    if (showTablet !== undefined && isTablet) return showTablet;
    if (showDesktop !== undefined && isDesktop) return showDesktop;
    
    // Check breakpoint constraints
    if (hideBreakpoints.includes(breakpoint)) return false;
    if (showBreakpoints.length > 0) return showBreakpoints.includes(breakpoint);
    
    // Default to showing if no constraints are specified
    return true;
  };
  
  return shouldShow() ? children : null;
}
