"use client";

import { useState, useEffect } from 'react';
import { use<PERSON><PERSON>, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { toast } from 'sonner';
import { 
  Sheet, 
  SheetContent, 
  SheetDescription, 
  SheetHeader, 
  Sheet<PERSON><PERSON>le,
  SheetFooter
} from "@/components/ui/sheet";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Textarea } from "@/components/ui/textarea";
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import ShopService from '@/lib/services/shop';
import { usePlans } from '@/hooks/usePlans';
import { useAuth } from '@/contexts/auth-context';

// Schema for form validation
const shopSchema = z.object({
  // Shop owner details
  fullName: z.string().min(3, "Full name must be at least 3 characters"),
  email: z.string()
    .min(1, "Email address is required")
    .email("Please enter a valid email address"),
  phone: z.string().min(6, "Phone number must be at least 6 digits"),
  
  // Shop details
  shopName: z.string().min(3, "Shop name must be at least 3 characters"),
  shopAddress: z.string().min(5, "Shop address must be at least 5 characters"),
  
  // Business category details (single field)
  businessCategory: z.string().min(1, "Please select a business category"),
  customCategory: z.string().optional(),
  
  // Subscription details (required for payment processing)
  planType: z.string().min(1, "Please select a subscription plan"),
}).refine((data) => {
  // Make customCategory required when businessCategory is "others"
  if (data.businessCategory === "others" && (!data.customCategory || data.customCategory.trim().length < 2)) {
    return false;
  }
  return true;
}, {
  message: "Custom category is required and must be at least 2 characters when 'Others' is selected",
  path: ["customCategory"], // This will show the error on the customCategory field
});

export function RegistrationDialog({ open, onOpenChange, onSuccess }) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [activeTab, setActiveTab] = useState("details");
  const [shopLogo, setShopLogo] = useState(null);
  const [logoPreview, setLogoPreview] = useState(null);
  
  // Authentication context - needed for usePlans hook to work
  const { isAuthenticated, isLoading, isSuperAdmin } = useAuth();
  const [authReady, setAuthReady] = useState(false);

  // Wait for authentication to be fully ready (same as Plans page)
  useEffect(() => {
    if (isLoading) {
      return;
    }
    
    if (!isAuthenticated || !isSuperAdmin) {
      setAuthReady(false);
      return;
    }

    // Check if we have access token before proceeding
    const hasToken = typeof window !== 'undefined' && localStorage.getItem('accessToken');
    
    if (hasToken) {
      setAuthReady(true);
    } else {
      // Retry after a short delay
      setTimeout(() => {
        const retryHasToken = typeof window !== 'undefined' && localStorage.getItem('accessToken');
        if (retryHasToken) {
          setAuthReady(true);
        }
      }, 1000);
    }
  }, [isAuthenticated, isLoading, isSuperAdmin]);
  
  // Use the same usePlans hook that works in Plans page (only when auth ready)
  const { 
    plans: availablePlans,
    loading: plansLoading,
    error: plansError,
    refreshPlans
  } = usePlans({
    autoFetch: authReady, // Only auto-fetch when authenticated
    includeInactive: false, // Only active plans for registration
    showToastMessages: false // We'll handle messages manually
  });
  
  // Trigger plan loading when dialog opens
  useEffect(() => {
    if (open && authReady) {
      console.log('🔄 [RegistrationDialog] Dialog opened and auth ready, triggering plan refresh...');
      refreshPlans();
    }
  }, [open, authReady, refreshPlans]);
  
  // Form setup with react-hook-form and zod validation
  const form = useForm({
    resolver: zodResolver(shopSchema),
    defaultValues: {
      fullName: "",
      email: "",
      phone: "",
      shopName: "",
      shopAddress: "",
      businessCategory: "",
      customCategory: "",
      planType: "", // Empty so user must select from API plans
    }
  });
  
  // Set default plan when plans are loaded
  useEffect(() => {
    console.log('📋 [RegistrationDialog] Plans effect triggered:', {
      availablePlansLength: availablePlans.length,
      currentPlanType: form.watch("planType"),
      plansLoading,
      plansError,
      availablePlans: availablePlans.map(p => ({
        planId: p.planId,
        _id: p._id,
        displayName: p.displayName,
        type: p.type,
        isActive: p.isActive
      }))
    });

    if (availablePlans.length > 0 && !form.watch("planType")) {
      const defaultPlan = availablePlans[0];
      const planValue = defaultPlan.planId || defaultPlan._id;
      form.setValue("planType", planValue);
      console.log('📋 [RegistrationDialog] Default plan set:', defaultPlan.displayName, 'with value:', planValue);
    } else if (availablePlans.length === 0 && !plansLoading) {
      console.warn('⚠️ [RegistrationDialog] No plans available and not loading');
      // Try to initialize default plans
      if (authReady) {
        console.log('🔧 [RegistrationDialog] Attempting to initialize default plans...');
        refreshPlans();
      }
    }
  }, [availablePlans, form, plansLoading, plansError, authReady, refreshPlans]);
  
  // Handle plans loading errors
  useEffect(() => {
    if (plansError) {
      console.error('💥 [RegistrationDialog] Plans loading error:', plansError);
      toast.error(`Failed to load plans: ${plansError}`);
    }
  }, [plansError]);
  
  // Handle file input change for shop logo
  const handleLogoChange = (event) => {
    const file = event.target.files[0];
    if (file) {
      setShopLogo(file);
      
      // Create a preview URL
      const reader = new FileReader();
      reader.onloadend = () => {
        setLogoPreview(reader.result);
      };
      reader.readAsDataURL(file);
    }
  };
  
  // Handle dialog open
  const handleDialogChange = (openState) => {
    console.log('🔄 Dialog state changing:', openState);
    
    // If opening, load plans and reset form
    if (openState && !open) {
      console.log('📂 Dialog opening - loading plans and resetting form');
      // Reset form first
      form.reset({
        fullName: "",
        email: "",
        phone: "",
        shopName: "",
        shopAddress: "",
        businessCategory: "",
        customCategory: "",
        planType: "",
      });
      setActiveTab("details");
      refreshPlans();
    }
    
    // If closing, reset all states
    if (!openState && open) {
      console.log('📂 Dialog closing - cleaning up states');
      form.reset();
      setActiveTab("details");
      setShopLogo(null);
      setLogoPreview(null);
    }
    
    onOpenChange(openState);
  };
  
  // Handle form submission
  const onSubmit = async (data) => {
    try {
      setIsSubmitting(true);
      console.log('📤 Raw form data:', data);
      
      // Structure the data properly for the API
      const shopData = {
        fullName: data.fullName?.trim(),
        email: data.email?.trim().toLowerCase(),
        phone: data.phone?.trim(),
        shopName: data.shopName?.trim(),
        shopAddress: data.shopAddress?.trim(),
        businessDetails: {
          type: 'retail', // Always send type for backend compatibility
          category: data.businessCategory || 'general_store',
          ...(data.businessCategory === 'others' && data.customCategory?.trim()
            ? { customCategory: data.customCategory.trim() }
            : {}
          )
        },
        planType: data.planType
      };
      
      // Remove any undefined or empty string values
      Object.keys(shopData).forEach(key => {
        if (shopData[key] === undefined || shopData[key] === '') {
          delete shopData[key];
        }
      });
      
      // Ensure businessDetails are clean
      if (shopData.businessDetails) {
        Object.keys(shopData.businessDetails).forEach(key => {
          if (shopData.businessDetails[key] === undefined || shopData.businessDetails[key] === '') {
            delete shopData.businessDetails[key];
          }
        });
      }
      
      // Add the shop logo to the data if available
      if (shopLogo) {
        shopData.shopLogo = shopLogo;
        console.log('🖼️ Logo attached:', shopLogo.name);
      }
      
      console.log('📤 Final structured shop data for API:', JSON.stringify(shopData, null, 2));

      // Validate planType before sending
      if (!shopData.planType) {
        console.error('❌ [RegistrationDialog] Missing planType in final data');
        toast.error('Please select a subscription plan');
        return;
      }

      console.log('🔍 [RegistrationDialog] Sending shop data with planType:', shopData.planType);

      // Create the shop via SuperAdmin API
      const result = await ShopService.createShop(shopData);
      console.log('✅ Shop creation result:', result);
      
      // Only show success if we actually get a valid result
      if (result && (result.shopId || result.id)) {
        console.log('✅ Shop created successfully with ID:', result.shopId || result.id);
        toast.success("Shop registered successfully! Welcome email sent to owner.");
        
        // Close the dialog and trigger success callback
        handleDialogChange(false);
        
        if (onSuccess) {
          onSuccess();
        }
      } else {
        console.error('❌ Shop creation failed - no valid result returned:', result);
        toast.error("Shop registration failed. Please check the form and try again.");
      }
    } catch (error) {
      console.error('❌ Shop registration failed with error:', error);
      console.error('❌ Error response data:', error.response?.data);
      console.error('❌ Error response status:', error.response?.status);
      console.error('❌ Full error object:', JSON.stringify(error.response, null, 2));
      
      // Extract meaningful error message
      let errorMessage = "Failed to register shop";
      
      if (error.response?.data?.details && Array.isArray(error.response.data.details)) {
        // If we have detailed validation errors, show them
        const validationErrors = error.response.data.details
          .map(detail => `${detail.field}: ${detail.message}`)
          .join(', ');
        errorMessage = `Validation failed: ${validationErrors}`;
      } else if (error.response?.data?.details) {
        // If we have detailed validation errors as a single object
        errorMessage = `Validation failed: ${JSON.stringify(error.response.data.details)}`;
      } else if (error.response?.data?.message) {
        errorMessage = error.response.data.message;
      } else if (error.message) {
        errorMessage = error.message;
      }
      
      console.error('❌ Final error message:', errorMessage);
      toast.error(errorMessage);
    } finally {
      setIsSubmitting(false);
    }
  };
  
  return (
    <Sheet open={open} onOpenChange={handleDialogChange}>
      <SheetContent side="right" className="w-full sm:max-w-xl md:max-w-2xl p-0 flex flex-col">
        <SheetHeader className="p-6 pb-2">
          <SheetTitle>Register New Shop</SheetTitle>
          <SheetDescription>
            Create a new shop as a superadmin. The shop will be verified automatically.
          </SheetDescription>
        </SheetHeader>
        
        <form onSubmit={form.handleSubmit(onSubmit)} className="flex flex-col h-full">
          <div className="flex-1">
            <Tabs value={activeTab} onValueChange={setActiveTab} className="h-full flex flex-col">
              <div className="px-6">
                <TabsList className="grid grid-cols-3 mb-4">
                  <TabsTrigger value="details">Shop Details</TabsTrigger>
                  <TabsTrigger value="owner">Owner Information</TabsTrigger>
                  <TabsTrigger value="subscription">Subscription</TabsTrigger>
                </TabsList>
              </div>
              
              <div className="flex-1 px-6 overflow-y-auto">
                <TabsContent value="details" className="mt-0 space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="shopName">Shop Name</Label>
                    <Controller
                      name="shopName"
                      control={form.control}
                      render={({ field }) => (
                        <Input
                          id="shopName"
                          placeholder="Enter shop name"
                          {...field}
                        />
                      )}
                    />
                    {form.formState.errors.shopName && (
                      <p className="text-sm text-destructive">{form.formState.errors.shopName.message}</p>
                    )}
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="shopAddress">Shop Address</Label>
                    <Controller
                      name="shopAddress"
                      control={form.control}
                      render={({ field }) => (
                        <Textarea
                          id="shopAddress"
                          placeholder="Enter shop address"
                          rows={3}
                          {...field}
                        />
                      )}
                    />
                    {form.formState.errors.shopAddress && (
                      <p className="text-sm text-destructive">{form.formState.errors.shopAddress.message}</p>
                    )}
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="businessCategory">Business Category</Label>
                    <Controller
                      name="businessCategory"
                      control={form.control}
                      render={({ field }) => (
                        <Select onValueChange={field.onChange} value={field.value}>
                          <SelectTrigger id="businessCategory">
                            <SelectValue placeholder="Select business category" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="general_store">General Store</SelectItem>
                            <SelectItem value="grocery">Grocery Store</SelectItem>
                            <SelectItem value="electronics">Electronics</SelectItem>
                            <SelectItem value="clothing">Clothing & Fashion</SelectItem>
                            <SelectItem value="restaurant">Restaurant & Food</SelectItem>
                            <SelectItem value="pharmacy">Pharmacy & Health</SelectItem>
                            <SelectItem value="hardware">Hardware & Tools</SelectItem>
                            <SelectItem value="automotive">Automotive</SelectItem>
                            <SelectItem value="beauty_salon">Beauty Salon & Spa</SelectItem>
                            <SelectItem value="others">Others</SelectItem>
                          </SelectContent>
                        </Select>
                      )}
                    />
                    {form.formState.errors.businessCategory && (
                      <p className="text-sm text-destructive">{form.formState.errors.businessCategory.message}</p>
                    )}
                  </div>
                  
                  {/* Show custom category field only when "others" is selected */}
                  {form.watch("businessCategory") === "others" && (
                    <div className="space-y-2">
                      <Label htmlFor="customCategory">Custom Category *</Label>
                      <Controller
                        name="customCategory"
                        control={form.control}
                        render={({ field }) => (
                          <Input
                            id="customCategory"
                            placeholder="Enter custom business category"
                            {...field}
                          />
                        )}
                      />
                      {form.formState.errors.customCategory && (
                        <p className="text-sm text-destructive">{form.formState.errors.customCategory.message}</p>
                      )}
                    </div>
                  )}
                  
                  <div className="space-y-2">
                    <Label htmlFor="shopLogo">Shop Logo (Optional)</Label>
                    <div className="flex items-center gap-4">
                      <Input
                        id="shopLogo"
                        type="file"
                        accept="image/*"
                        onChange={handleLogoChange}
                        className="flex-1"
                      />
                      {logoPreview && (
                        <div className="w-16 h-16 rounded overflow-hidden">
                          <img
                            src={logoPreview}
                            alt="Logo Preview"
                            className="w-full h-full object-cover"
                          />
                        </div>
                      )}
                    </div>
                  </div>
                </TabsContent>
                
                <TabsContent value="owner" className="mt-0 space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="fullName">Owner Full Name</Label>
                    <Controller
                      name="fullName"
                      control={form.control}
                      render={({ field }) => (
                        <Input
                          id="fullName"
                          placeholder="Enter owner's full name"
                          {...field}
                        />
                      )}
                    />
                    {form.formState.errors.fullName && (
                      <p className="text-sm text-destructive">{form.formState.errors.fullName.message}</p>
                    )}
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="email">Email Address</Label>
                    <Controller
                      name="email"
                      control={form.control}
                      render={({ field }) => (
                        <Input
                          id="email"
                          type="email"
                          placeholder="Enter email address"
                          {...field}
                        />
                      )}
                    />
                    {form.formState.errors.email && (
                      <p className="text-sm text-destructive">{form.formState.errors.email.message}</p>
                    )}
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="phone">Phone Number</Label>
                    <Controller
                      name="phone"
                      control={form.control}
                      render={({ field }) => (
                        <Input
                          id="phone"
                          placeholder="Enter phone number"
                          {...field}
                        />
                      )}
                    />
                    {form.formState.errors.phone && (
                      <p className="text-sm text-destructive">{form.formState.errors.phone.message}</p>
                    )}
                  </div>
                </TabsContent>
                
                <TabsContent value="subscription" className="mt-0 space-y-6 pb-32">
                  <div className="space-y-4">
                    <div className="space-y-3">
                      <Label htmlFor="planType" className="text-base font-medium">Subscription Plan</Label>

                      {plansError && (
                        <div className="p-3 text-sm text-destructive bg-destructive/10 border border-destructive/20 rounded-md">
                          Failed to load plans: {plansError}
                        </div>
                      )}

                      <div className="relative z-50">
                        <Controller
                          name="planType"
                          control={form.control}
                          render={({ field }) => (
                            <Select
                              onValueChange={field.onChange}
                              value={field.value}
                              disabled={plansLoading || availablePlans.length === 0}
                            >
                              <SelectTrigger id="planType" className="w-full h-12">
                                <SelectValue
                                  placeholder={
                                    plansLoading
                                      ? "Loading plans..."
                                      : availablePlans.length === 0
                                        ? "No plans available"
                                        : "Select a subscription plan"
                                  }
                                />
                              </SelectTrigger>
                              <SelectContent
                                className="z-[200] max-h-80 overflow-y-auto bg-popover border border-border shadow-xl rounded-lg"
                                position="popper"
                                sideOffset={8}
                                align="start"
                                avoidCollisions={true}
                                collisionPadding={20}
                              >
                                {availablePlans.map((plan) => (
                                  <SelectItem
                                    key={plan.planId || plan._id}
                                    value={plan.planId || plan._id}
                                    className="cursor-pointer hover:bg-accent focus:bg-accent px-4 py-3 border-b border-border/50 last:border-b-0"
                                  >
                                    <div className="flex flex-col space-y-1 w-full">
                                      <div className="flex items-center justify-between w-full">
                                        <span className="font-semibold text-sm text-foreground">{plan.displayName}</span>
                                        <span className="text-sm font-medium text-primary">
                                          ${plan.pricing?.basePrice || 0}
                                        </span>
                                      </div>
                                      <div className="flex items-center justify-between w-full">
                                        <span className="text-xs text-muted-foreground">
                                          {plan.type === 'trial' ? '14-day trial' : `Billed ${plan.pricing?.billingCycle || 'monthly'}`}
                                        </span>
                                        {plan.type === 'trial' && (
                                          <span className="text-xs bg-green-100 text-green-700 px-2 py-0.5 rounded-full">
                                            Free
                                          </span>
                                        )}
                                      </div>
                                    </div>
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                          )}
                        />
                      </div>
                      {form.formState.errors.planType && (
                        <p className="text-sm text-destructive">{form.formState.errors.planType.message}</p>
                      )}
                    </div>

                    {/* Selected Plan Preview */}
                    {form.watch("planType") && (
                      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                        <h4 className="font-medium text-blue-900 mb-2">Selected Plan</h4>
                        {(() => {
                          const selectedPlan = availablePlans.find(p => (p.planId || p._id) === form.watch("planType"));
                          if (!selectedPlan) return null;

                          return (
                            <div className="space-y-2">
                              <div className="flex items-center justify-between">
                                <span className="font-semibold text-blue-900">{selectedPlan.displayName}</span>
                                <span className="text-lg font-bold text-blue-700">
                                  ${selectedPlan.pricing?.basePrice || 0}
                                  <span className="text-sm font-normal">
                                    /{selectedPlan.pricing?.billingCycle || 'month'}
                                  </span>
                                </span>
                              </div>
                              {selectedPlan.description && (
                                <p className="text-sm text-blue-700">{selectedPlan.description}</p>
                              )}
                            </div>
                          );
                        })()}
                      </div>
                    )}
                  </div>

                </TabsContent>
              </div>
            </Tabs>
          </div>
          
          <SheetFooter className="px-6 py-4 border-t">
            <div className="flex justify-between w-full">
              <Button 
                type="button" 
                variant="outline"
                onClick={() => {
                  if (activeTab === "details") {
                    handleDialogChange(false);
                  } else if (activeTab === "owner") {
                    setActiveTab("details");
                  } else if (activeTab === "subscription") {
                    setActiveTab("owner");
                  }
                }}
              >
                {activeTab === "details" ? "Cancel" : "Back"}
              </Button>
              
              <Button 
                type={activeTab === "subscription" ? "submit" : "button"}
                disabled={isSubmitting}
                onClick={async (e) => {
                  // Prevent form submission when navigating tabs
                  if (activeTab !== "subscription") {
                    e.preventDefault();
                  }
                  
                  if (activeTab === "details") {
                    // Validate shop details before proceeding
                    console.log('🔍 Validating shop details...');
                    const currentValues = form.getValues();
                    console.log('Current form values:', currentValues);
                    
                    // Simple validation - just check if required fields have values
                    const shopName = currentValues.shopName?.trim();
                    const shopAddress = currentValues.shopAddress?.trim();
                    const businessCategory = currentValues.businessCategory?.trim();
                    const customCategory = currentValues.customCategory?.trim();
                    
                    if (!shopName || shopName.length < 3) {
                      toast.error("Shop name must be at least 3 characters");
                      return;
                    }
                    
                    if (!shopAddress || shopAddress.length < 5) {
                      toast.error("Shop address must be at least 5 characters");
                      return;
                    }
                    
                    if (!businessCategory) {
                      toast.error("Please select a business category");
                      return;
                    }
                    
                    if (businessCategory === "others" && !customCategory) {
                      toast.error("Please enter a custom category for 'Others'");
                      return;
                    }
                    
                    console.log('✅ Shop details valid, moving to owner tab');
                    setActiveTab("owner");
                    
                  } else if (activeTab === "owner") {
                    // Validate owner details before proceeding
                    console.log('🔍 Validating owner details...');
                    const currentValues = form.getValues();
                    console.log('Current form values:', currentValues);
                    
                    // Simple validation - just check if required fields have values
                    const fullName = currentValues.fullName?.trim();
                    const email = currentValues.email?.trim();
                    const phone = currentValues.phone?.trim();
                    
                    if (!fullName || fullName.length < 3) {
                      toast.error("Full name must be at least 3 characters");
                      return;
                    }
                    
                    if (!email || !email.includes('@')) {
                      toast.error("Please enter a valid email address");
                      return;
                    }
                    
                    if (!phone || phone.length < 6) {
                      toast.error("Phone number must be at least 6 digits");
                      return;
                    }
                    
                    console.log('✅ Owner details valid, moving to subscription tab');
                    setActiveTab("subscription");
                  }
                  // If on subscription tab, form will be submitted via type="submit"
                }}
              >
                {activeTab === "subscription" ? (isSubmitting ? "Registering..." : "Register Shop") : "Next"}
              </Button>
            </div>
          </SheetFooter>
        </form>
      </SheetContent>
    </Sheet>
  );
}
