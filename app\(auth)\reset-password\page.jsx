"use client"

import { useState, useEffect } from "react"
import { useRouter, useSearchParams } from "next/navigation"
import { useAuth } from "@/contexts/auth-context"
import { Logo } from "@/components/ui/logo"
import { ThemeToggleFixed } from "@/components/ui/theme-toggle-fixed"
import { PasswordInput } from "@/components/ui/password-input"
import Link from "next/link"
import { toast } from "sonner"
import { errorHandlers } from "@/lib/api/contract"

export default function ResetPasswordPage() {
  const [newPassword, setNewPassword] = useState("")
  const [confirmPassword, setConfirmPassword] = useState("")
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState("")
  const [success, setSuccess] = useState(false)
  const [token, setToken] = useState("")
  const router = useRouter()
  const searchParams = useSearchParams()
  const { resetPassword } = useAuth()

  useEffect(() => {
    // Get token from URL
    const tokenParam = searchParams.get("token")
    if (tokenParam) {
      setToken(tokenParam)
    } else {
      toast.error("Your password reset link is invalid or has expired. Please request a new one.")
      // Redirect to login after short delay if no valid token
      setTimeout(() => {
        router.push("/login")
      }, 3000)
    }
  }, [searchParams, router])

  const handleSubmit = async (e) => {
    e.preventDefault()
    setIsLoading(true)
    setError("")

    // Validate passwords match
    if (newPassword !== confirmPassword) {
      toast.error("The passwords you entered don't match. Please try again.")
      setIsLoading(false)
      return
    }

    // Validate password strength
    if (newPassword.length < 8) {
      toast.error("Your password must be at least 8 characters long for security.")
      setIsLoading(false)
      return
    }

    try {
      // Pass all required parameters: token, newPassword, and confirmPassword
      await resetPassword(token, newPassword, confirmPassword)
      toast.success("Password reset successful! You can now log in with your new password.")
      setSuccess(true)
      // Reset form
      setNewPassword("")
      setConfirmPassword("")
    } catch (err) {
      const friendlyErrorMessage = errorHandlers.getErrorMessage(err)
      toast.error(friendlyErrorMessage)
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="flex min-h-screen bg-background">
      {/* Main card with split design */}
      <div className="m-auto w-full max-w-6xl overflow-hidden rounded-2xl border border-border shadow-sm relative">
        {/* Theme toggle in top right corner */}
        <div className="absolute top-4 right-4 z-10">
          <ThemeToggleFixed />
        </div>
        <div className="flex flex-col md:flex-row">
          {/* Left side - Reset password form */}
          <div className="w-full p-8 md:w-1/2 bg-background">
            <div className="mb-8 flex flex-col space-y-2 text-center">
              <Logo 
                variant="full" 
                size="2xl" 
                className="mx-auto mb-8 scale-110" 
                href={null}
                showText={true}
              />
              <h1 className="text-3xl font-bold">Reset Password</h1>
              <p className="text-muted-foreground">
                Enter a new password below
              </p>
            </div>

            {success ? (
              <div className="space-y-6">
                <div className="rounded-md border border-border bg-card p-4">
                  <div className="flex">
                    <div className="flex-shrink-0">
                      <svg
                        className="h-5 w-5 text-primary"
                        viewBox="0 0 20 20"
                        fill="currentColor"
                      >
                        <path
                          fillRule="evenodd"
                          d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                          clipRule="evenodd"
                        />
                      </svg>
                    </div>
                    <div className="ml-3">
                      <p className="text-sm font-medium">
                        Password reset successful! You can now log in with your new password.
                      </p>
                    </div>
                  </div>
                </div>
                <Link
                  href="/login"
                  className="block w-full rounded-md bg-primary px-4 py-2 text-center text-sm font-medium text-primary-foreground hover:bg-primary/90"
                >
                  Go to Login
                </Link>
              </div>
            ) : (
              <form onSubmit={handleSubmit} className="space-y-6">
                <div className="space-y-2">
                  <label htmlFor="new-password" className="text-sm font-medium leading-none">
                    New Password
                  </label>
                  <PasswordInput
                    id="new-password"
                    value={newPassword}
                    onChange={(e) => setNewPassword(e.target.value)}
                    required
                    disabled={isLoading}
                  />
                </div>

                <div className="space-y-2">
                  <label htmlFor="confirm-password" className="text-sm font-medium leading-none">
                    Confirm Password
                  </label>
                  <PasswordInput
                    id="confirm-password"
                    value={confirmPassword}
                    onChange={(e) => setConfirmPassword(e.target.value)}
                    required
                    disabled={isLoading}
                  />
                </div>

                <button
                  type="submit"
                  className="w-full rounded-md bg-primary px-4 py-2 text-sm font-medium text-primary-foreground hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50"
                  disabled={isLoading || !token}
                >
                  {isLoading ? "Resetting Password..." : "Reset Password"}
                </button>

                <div className="text-center text-sm">
                  <Link href="/login" className="text-primary hover:underline">
                    Back to login
                  </Link>
                </div>
              </form>
            )}
          </div>
          
          {/* Right side - Background/info section */}
          <div className="hidden md:block md:w-1/2 bg-primary/10 p-8">
            <div className="flex h-full items-center justify-center">
              <div className="text-center">
                <h2 className="mb-4 text-2xl font-bold">Password Security</h2>
                <p className="mb-6 text-muted-foreground">
                  Please choose a strong password that is at least 8 characters long and includes a mix of letters, numbers, and symbols.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
