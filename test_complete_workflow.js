/**
 * Complete Workflow Test Script
 * Tests the end-to-end flow from offline payment submission to superAdmin approval
 */

const axios = require('axios');
const mongoose = require('mongoose');

// Configuration
const BASE_URL = 'http://localhost:5000/api';
const SUPERADMIN_EMAIL = '<EMAIL>';
const SUPERADMIN_PASSWORD = 'SuperAdmin123!';

// Test data
const testUser = {
  fullName: 'Ahmed Hassan Test',
  email: `test.workflow.${Date.now()}@example.com`,
  phone: '+252612345678',
  password: 'TestPassword123!',
  shopName: 'Ahmed Electronics Test',
  shopAddress: 'Mogadishu, Somalia'
};

let testData = {
  user: null,
  shop: null,
  accessToken: null,
  superAdminToken: null,
  paymentTransaction: null
};

/**
 * Test 1: User Registration
 */
async function testUserRegistration() {
  console.log('\n🔄 Step 1: Testing User Registration...');
  
  try {
    const response = await axios.post(`${BASE_URL}/register`, {
      fullName: testUser.fullName,
      email: testUser.email,
      phone: testUser.phone,
      password: testUser.password,
      shopName: testUser.shopName,
      shopAddress: testUser.shopAddress
    });

    if (response.data.success) {
      testData.user = response.data.data.user;
      console.log('✅ User registration successful');
      console.log(`   User ID: ${testData.user.userId}`);
      console.log(`   Email: ${testData.user.email}`);
      return true;
    }
  } catch (error) {
    console.log('❌ User registration failed:', error.response?.data?.message || error.message);
    return false;
  }
}

/**
 * Test 2: Email Verification
 */
async function testEmailVerification() {
  console.log('\n🔄 Step 2: Testing Email Verification...');
  
  try {
    // Use a mock verification code for testing
    const response = await axios.post(`${BASE_URL}/verify-email`, {
      email: testUser.email,
      verificationCode: '123456' // Mock code
    });

    if (response.data.success) {
      testData.accessToken = response.data.data.token.accessToken;
      testData.shop = response.data.data.shop;
      console.log('✅ Email verification successful');
      console.log(`   Shop ID: ${testData.shop.shopId}`);
      return true;
    }
  } catch (error) {
    console.log('❌ Email verification failed:', error.response?.data?.message || error.message);
    console.log('   Note: Using mock verification code. In production, check email for actual code.');
    return false;
  }
}

/**
 * Test 3: Offline Payment Submission
 */
async function testOfflinePaymentSubmission() {
  console.log('\n🔄 Step 3: Testing Offline Payment Submission...');
  
  try {
    const response = await axios.post(`${BASE_URL}/register/pay`, {
      planType: 'monthly',
      paymentMethod: 'offline',
      payerName: testUser.fullName,
      payerPhone: testUser.phone,
      notes: 'Test offline payment for workflow verification'
    }, {
      headers: {
        'Authorization': `Bearer ${testData.accessToken}`
      }
    });

    if (response.data.success && response.data.data.nextStep === 'registration_complete_offline_payment_pending') {
      console.log('✅ Offline payment submission successful');
      console.log(`   Status: ${response.data.data.nextStep}`);
      console.log('   SuperAdmin email notification should be sent');
      return true;
    }
  } catch (error) {
    console.log('❌ Offline payment submission failed:', error.response?.data?.message || error.message);
    return false;
  }
}

/**
 * Test 4: SuperAdmin Login
 */
async function testSuperAdminLogin() {
  console.log('\n🔄 Step 4: Testing SuperAdmin Login...');
  
  try {
    const response = await axios.post(`${BASE_URL}/login`, {
      email: SUPERADMIN_EMAIL,
      password: SUPERADMIN_PASSWORD
    });

    if (response.data.success) {
      testData.superAdminToken = response.data.data.token.accessToken;
      console.log('✅ SuperAdmin login successful');
      return true;
    }
  } catch (error) {
    console.log('❌ SuperAdmin login failed:', error.response?.data?.message || error.message);
    return false;
  }
}

/**
 * Test 5: API Endpoint Verification
 */
async function testAPIEndpoints() {
  console.log('\n🔄 Step 5: Testing API Endpoints...');
  
  const endpoints = [
    { name: 'Payment Transactions List', url: '/superadmin/payment-transactions', method: 'GET' },
    { name: 'Payment Transactions Stats', url: '/superadmin/payment-transactions/stats', method: 'GET' },
    { name: 'Contact Info', url: '/settings/contact-info', method: 'GET', auth: false }
  ];

  let allPassed = true;

  for (const endpoint of endpoints) {
    try {
      const config = {
        method: endpoint.method,
        url: `${BASE_URL}${endpoint.url}`
      };

      if (endpoint.auth !== false) {
        config.headers = {
          'Authorization': `Bearer ${testData.superAdminToken}`
        };
      }

      const response = await axios(config);
      
      if (response.data.success) {
        console.log(`✅ ${endpoint.name}: Working`);
      } else {
        console.log(`❌ ${endpoint.name}: Failed - No success flag`);
        allPassed = false;
      }
    } catch (error) {
      console.log(`❌ ${endpoint.name}: Failed - ${error.response?.status} ${error.response?.statusText}`);
      allPassed = false;
    }
  }

  return allPassed;
}

/**
 * Test 6: Shop Approval
 */
async function testShopApproval() {
  console.log('\n🔄 Step 6: Testing Shop Approval...');
  
  if (!testData.shop) {
    console.log('❌ No shop data available for approval test');
    return false;
  }

  try {
    const response = await axios.post(`${BASE_URL}/register/admin/approve-shop/${testData.shop.shopId}`, {
      approvalNotes: 'Test approval - workflow verification',
      activateImmediately: true,
      confirmOfflinePayment: true,
      offlinePaymentDetails: {
        receiptNumber: `TEST-${Date.now()}`,
        paymentDate: new Date().toISOString(),
        amount: 50.00,
        currency: 'USD',
        paymentMethod: 'offline',
        notes: 'Test payment verification',
        verifiedBy: SUPERADMIN_EMAIL
      }
    }, {
      headers: {
        'Authorization': `Bearer ${testData.superAdminToken}`
      }
    });

    if (response.data.success) {
      console.log('✅ Shop approval successful');
      console.log(`   Shop Status: ${response.data.data.shop?.status || 'Updated'}`);
      return true;
    }
  } catch (error) {
    console.log('❌ Shop approval failed:', error.response?.data?.message || error.message);
    return false;
  }
}

/**
 * Main test runner
 */
async function runCompleteWorkflowTest() {
  console.log('🚀 Starting Complete Workflow Test');
  console.log('=====================================');

  const tests = [
    { name: 'User Registration', fn: testUserRegistration },
    { name: 'Email Verification', fn: testEmailVerification },
    { name: 'Offline Payment Submission', fn: testOfflinePaymentSubmission },
    { name: 'SuperAdmin Login', fn: testSuperAdminLogin },
    { name: 'API Endpoints', fn: testAPIEndpoints },
    { name: 'Shop Approval', fn: testShopApproval }
  ];

  let passedTests = 0;
  let totalTests = tests.length;

  for (const test of tests) {
    const result = await test.fn();
    if (result) {
      passedTests++;
    }
    
    // Add delay between tests
    await new Promise(resolve => setTimeout(resolve, 1000));
  }

  console.log('\n📊 Test Results Summary');
  console.log('========================');
  console.log(`✅ Passed: ${passedTests}/${totalTests}`);
  console.log(`❌ Failed: ${totalTests - passedTests}/${totalTests}`);
  
  if (passedTests === totalTests) {
    console.log('\n🎉 All tests passed! Complete workflow is working correctly.');
  } else {
    console.log('\n⚠️  Some tests failed. Please check the issues above.');
  }

  console.log('\n📋 Next Steps:');
  console.log('1. Check email inbox for superAdmin notification');
  console.log('2. Verify frontend routes work with email action buttons');
  console.log('3. Test production deployment with actual domain URLs');
}

// Run the test
runCompleteWorkflowTest().catch(console.error);
