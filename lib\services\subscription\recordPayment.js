import apiBridge from '@/lib/api/bridge';
import { ENDPOINTS } from '@/lib/api/contract';;
import { handleError, processApiResponse, validateRequiredFields } from '../baseService';

/**
 * Record payment for subscription
 * @param {Object} paymentData - Payment data (amount, transactionId, paymentMethod, currency, notes)
 * @returns {Promise<Object>} Payment record result
 */
async function recordPayment(paymentData) {
  try {
    // Validate required fields
    const requiredFields = ['amount', 'transactionId', 'paymentMethod'];
    const validation = validateRequiredFields(paymentData, requiredFields);
    
    if (!validation.isValid) {
      throw new Error(validation.message);
    }

    // Validate amount is positive
    if (paymentData.amount <= 0) {
      throw new Error('Payment amount must be positive');
    }

    const response = await apiBridge.post(`${ENDPOINTS.SUBSCRIPTIONS.BASE}/payment`, paymentData, {
      clearCacheEndpoint: ENDPOINTS.SUBSCRIPTIONS.BASE
    });
    
    const result = processApiResponse(response, 'Payment recorded successfully');
    return result;
  } catch (error) {
    handleError(error, 'SubscriptionService.recordPayment', true);
    throw error;
  }
}

export default recordPayment; 
