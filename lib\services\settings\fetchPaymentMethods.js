/**
 * Fetch Payment Methods Service
 * Matches backend: settingsController.getPaymentMethods
 * GET /api/settings/payment-methods
 */

import { getPaymentMethods } from '../../api/modules/settings';
import { handleError, logApiCall } from '../baseService';

/**
 * Get available payment methods with online/offline switches
 * @param {string} [context='general'] - Context for payment methods ('general', 'subscription', 'pos')
 * @returns {Promise<Object>} Payment methods data
 */
const fetchPaymentMethods = async (context = 'general') => {
  const apiContext = `fetchPaymentMethods(context: ${context})`;
  
  try {
    logApiCall(apiContext, 'GET /api/settings/payment-methods', { context });
    
    const response = await getPaymentMethods(context);
    
    if (response?.success) {
      console.log(`[${apiContext}] Success:`, {
        paymentMethodsCount: response.data?.paymentMethods?.length || 0,
        onlineEnabled: response.data?.onlinePaymentsEnabled,
        offlineEnabled: response.data?.offlinePaymentsEnabled,
        context: response.data?.context
      });
      
      // Return the exact format from backend:
      // { paymentMethods: Array, onlinePaymentsEnabled: Boolean, offlinePaymentsEnabled: Boolean, context: String }
      return {
        success: true,
        data: {
          paymentMethods: response.data?.paymentMethods || [],
          onlinePaymentsEnabled: response.data?.onlinePaymentsEnabled || false,
          offlinePaymentsEnabled: response.data?.offlinePaymentsEnabled || false,
          context: response.data?.context || context
        }
      };
    } else {
      console.warn(`[${apiContext}] API returned success=false:`, response);
      return {
        success: false,
        data: {
          paymentMethods: [],
          onlinePaymentsEnabled: false,
          offlinePaymentsEnabled: false,
          context: context
        },
        message: response?.message || 'Failed to fetch payment methods'
      };
    }
    
  } catch (error) {
    console.error(`[${apiContext}] Error:`, error);
    handleError(error, apiContext, false);
    
    // Return consistent error format with fallback data
    return {
      success: false,
      data: {
        paymentMethods: [],
        onlinePaymentsEnabled: false,
        offlinePaymentsEnabled: false,
        context: context
      },
      message: error.message || 'Failed to fetch payment methods',
      error: error
    };
  }
};

export default fetchPaymentMethods; 