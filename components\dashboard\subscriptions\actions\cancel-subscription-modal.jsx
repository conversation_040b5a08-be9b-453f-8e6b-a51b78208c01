'use client';

import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { toast } from 'sonner';
import { 
  AlertTriangle, 
  X, 
  Ban
} from 'lucide-react';
import SubscriptionService from '@/lib/services/subscription';

const CancelSubscriptionModal = ({ isOpen, onClose, subscription, onSuccess }) => {
  const [formData, setFormData] = useState({
    reason: '',
    feedback: '',
    refundRequested: false,
    effectiveImmediately: false,
    notifyCustomer: true
  });
  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState({});
  const [confirmation, setConfirmation] = useState(false);

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: null
      }));
    }
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.reason) {
      newErrors.reason = 'Please select a cancellation reason';
    }

    if (!confirmation) {
      newErrors.confirmation = 'Please confirm the cancellation';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm()) {
      toast.error('Please complete all required fields');
      return;
    }

    setIsLoading(true);
    try {
      const cancellationData = {
        reason: formData.reason,
        feedback: formData.feedback || undefined,
        refundRequested: formData.refundRequested,
        effectiveDate: formData.effectiveImmediately ? new Date().toISOString() : undefined,
        notifyCustomer: formData.notifyCustomer,
        canceledBy: 'admin'
      };

      await SubscriptionService.cancelSubscription(subscription.subscriptionId, cancellationData);
      
      toast.success('Subscription canceled successfully');
      onSuccess && onSuccess();
      onClose();
    } catch (error) {
      console.error('Error canceling subscription:', error);
      toast.error(error.message || 'Failed to cancel subscription');
    } finally {
      setIsLoading(false);
    }
  };

  if (!subscription) return null;

  const cancellationReasons = [
    { value: 'cost', label: 'Too expensive' },
    { value: 'features', label: 'Missing features' },
    { value: 'competitor', label: 'Switching to competitor' },
    { value: 'usability', label: 'Difficult to use' },
    { value: 'support', label: 'Poor customer support' },
    { value: 'technical_issues', label: 'Technical issues' },
    { value: 'business_closure', label: 'Business closure' },
    { value: 'other', label: 'Other' }
  ];

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-red-600">
            <Ban className="h-5 w-5" />
            Cancel Subscription #{subscription.subscriptionId}
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Warning */}
          <Card className="border-red-200 bg-red-50">
            <CardContent className="pt-4">
              <div className="flex items-start gap-3">
                <AlertTriangle className="h-5 w-5 text-red-600 mt-0.5" />
                <div className="space-y-1">
                  <p className="font-medium text-red-800">Warning: This action cannot be undone</p>
                  <p className="text-sm text-red-700">
                    Canceling this subscription will terminate access to all services and features.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Cancellation Details */}
          <Card>
            <CardHeader>
              <CardTitle>Cancellation Details</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="reason">Cancellation Reason *</Label>
                <Select
                  value={formData.reason}
                  onValueChange={(value) => handleInputChange('reason', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select a reason" />
                  </SelectTrigger>
                  <SelectContent>
                    {cancellationReasons.map((reason) => (
                      <SelectItem key={reason.value} value={reason.value}>
                        {reason.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {errors.reason && (
                  <p className="text-sm text-red-500 mt-1">{errors.reason}</p>
                )}
              </div>

              <div>
                <Label htmlFor="feedback">Additional Feedback (Optional)</Label>
                <Textarea
                  id="feedback"
                  value={formData.feedback}
                  onChange={(e) => handleInputChange('feedback', e.target.value)}
                  placeholder="Provide additional details..."
                  rows={3}
                />
              </div>
            </CardContent>
          </Card>

          {/* Options */}
          <Card>
            <CardHeader>
              <CardTitle>Cancellation Options</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center space-x-2">
                <Switch
                  id="effectiveImmediately"
                  checked={formData.effectiveImmediately}
                  onCheckedChange={(checked) => handleInputChange('effectiveImmediately', checked)}
                />
                <Label htmlFor="effectiveImmediately">Cancel Immediately</Label>
              </div>

              <div className="flex items-center space-x-2">
                <Switch
                  id="refundRequested"
                  checked={formData.refundRequested}
                  onCheckedChange={(checked) => handleInputChange('refundRequested', checked)}
                />
                <Label htmlFor="refundRequested">Request Refund</Label>
              </div>

              <div className="flex items-center space-x-2">
                <Switch
                  id="notifyCustomer"
                  checked={formData.notifyCustomer}
                  onCheckedChange={(checked) => handleInputChange('notifyCustomer', checked)}
                />
                <Label htmlFor="notifyCustomer">Notify Customer</Label>
              </div>
            </CardContent>
          </Card>

          {/* Confirmation */}
          <Card className="border-red-200">
            <CardContent className="pt-4">
              <div className="flex items-start space-x-2">
                <Checkbox
                  id="confirmation"
                  checked={confirmation}
                  onCheckedChange={setConfirmation}
                />
                <Label htmlFor="confirmation" className="text-sm font-medium">
                  I understand that this action will cancel the subscription and cannot be undone
                </Label>
                {errors.confirmation && (
                  <p className="text-sm text-red-500 mt-1">{errors.confirmation}</p>
                )}
              </div>
            </CardContent>
          </Card>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onClose} disabled={isLoading}>
            <X className="h-4 w-4 mr-2" />
            Keep Subscription
          </Button>
          <Button 
            variant="destructive" 
            onClick={handleSubmit} 
            disabled={isLoading || !confirmation}
          >
            {isLoading ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Canceling...
              </>
            ) : (
              <>
                <Ban className="h-4 w-4 mr-2" />
                Cancel Subscription
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default CancelSubscriptionModal;
