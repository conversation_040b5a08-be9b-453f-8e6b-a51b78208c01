import apiBridge from '@/lib/api/bridge';
import { ENDPOINTS } from '@/lib/api/contract';;
import { handleError, processApiResponse, validateRequiredFields } from '../baseService';

/**
 * Renew existing subscription
 * @param {Object} renewalData - Renewal data (paymentMethod, transactionId, amount, currency, notes)
 * @returns {Promise<Object>} Renewed subscription object
 */
async function renewSubscription(renewalData) {
  try {
    // Validate required fields
    const requiredFields = ['paymentMethod', 'transactionId'];
    const validation = validateRequiredFields(renewalData, requiredFields);
    
    if (!validation.isValid) {
      throw new Error(validation.message);
    }

    // Validate amount if provided
    if (renewalData.amount && renewalData.amount <= 0) {
      throw new Error('Amount must be positive if provided');
    }

    const response = await apiBridge.post(`${ENDPOINTS.SUBSCRIPTIONS.BASE}/renew`, renewalData, {
      clearCacheEndpoint: ENDPOINTS.SUBSCRIPTIONS.BASE
    });
    
    const result = processApiResponse(response, 'Subscription renewed successfully');
    return result.subscription || result;
  } catch (error) {
    handleError(error, 'SubscriptionService.renewSubscription', true);
    throw error;
  }
}

export default renewSubscription; 
