import apiBridge from '@/lib/api/bridge';
import { ENDPOINTS } from '@/lib/api/contract';
import { toast } from 'sonner';
import BaseService from '@/lib/services/baseService';

/**
 * Suspend or deactivate a shop via SuperAdmin API
 * @param {string} shopId - ID of the shop to suspend
 * @param {string} reason - Reason for suspension (required)
 * @param {number} duration - Optional duration in days for suspension
 * @param {boolean} sendEmail - Whether to send email notification
 * @returns {Promise<Object>} Updated shop status
 */
async function suspendShop(shopId, reason, duration = null, sendEmail = true) {
  try {
    // Validate required reason
    if (!reason || reason.trim().length < 5) {
      toast.error('Please provide a valid reason for suspension (minimum 5 characters)');
      return null;
    }
    
    // Prepare request payload for SuperAdmin status change
    const payload = {
      status: 'suspended',
      reason,
      sendEmail
    };
    
    // Add duration if provided
    if (duration) {
      payload.duration = duration;
    }
    
    // Make API request using SuperAdmin status endpoint
    const response = await apiBridge.put(`${ENDPOINTS.SHOPS.BASE}/${shopId}/status`, payload, {
      clearCacheEndpoint: ENDPOINTS.SHOPS.BASE
    });
    
    // Process response
    if (response.data && response.data.success) {
      const updatedShop = response.data.data;
      toast.success('Shop suspended successfully');
      
      return {
        // Core identifiers
        id: updatedShop.shopId || updatedShop._id,
        shopId: updatedShop.shopId || updatedShop._id,
        _id: updatedShop._id || updatedShop.shopId,
        
        // Basic info
        shopName: updatedShop.shopName,
        status: updatedShop.status,
        
        // Suspension details
        suspensionReason: reason,
        suspensionDetails: {
          reason,
          duration,
          suspendedAt: new Date().toISOString(),
          suspendedBy: 'superAdmin'
        },
        
        // Dates
        updatedAt: updatedShop.updatedAt || new Date().toISOString()
      };
    }
    
    // Handle unexpected response
    console.error('[SuperAdminShopService] Unexpected API response format:', response.data);
    toast.error('Failed to suspend shop: Unexpected response format');
    return null;
  } catch (error) {
    console.error(`[SuperAdminShopService] Error suspending shop ${shopId}:`, error);
    
    // Enhanced error handling for SuperAdmin operations
    if (error.response?.status === 400 && error.response?.data?.message) {
      BaseService.handleError({ message: `Validation error: ${error.response.data.message}` }, 'SuperAdminShopService.suspendShop', true);
    } else if (error.response?.status === 401) {
      BaseService.handleError({ message: 'Unauthorized: SuperAdmin access required' }, 'SuperAdminShopService.suspendShop', true);
    } else if (error.response?.status === 403) {
      BaseService.handleError({ message: 'Forbidden: Only SuperAdmins can suspend shops' }, 'SuperAdminShopService.suspendShop', true);
    } else if (error.response?.status === 404) {
      BaseService.handleError({ message: 'Shop not found' }, 'SuperAdminShopService.suspendShop', true);
    } else {
      BaseService.handleError(error, 'SuperAdminShopService.suspendShop', true);
    }
    
    return null;
  }
}

export default suspendShop;
