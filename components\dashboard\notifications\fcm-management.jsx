"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { toast } from "sonner";
import { Activity, Play, Smartphone, TestTube, Settings, CheckCircle } from "lucide-react";

export function FCMManagement({ registerToken, sendTestNotification, testFirebaseConnection, isLoading }) {
  const [testData, setTestData] = useState({
    token: "",
    platform: "android",
    testMessage: "Hello from DeynCare!"
  });

  const handleRegisterToken = async () => {
    if (!testData.token.trim()) {
      toast.error("Please enter a valid FCM token");
      return;
    }
    
    try {
      await registerToken({
        token: testData.token,
        platform: testData.platform,
        userId: "test_user"
      });
      setTestData(prev => ({ ...prev, token: "" }));
    } catch (error) {
      console.error("Token registration failed:", error);
    }
  };

  const handleSendTest = async () => {
    try {
      await sendTestNotification({
        message: testData.testMessage,
        data: { test: true }
      });
    } catch (error) {
      console.error("Test notification failed:", error);
    }
  };

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Smartphone className="h-5 w-5" />
              Register FCM Token
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label>Platform</Label>
              <Select value={testData.platform} onValueChange={(value) => setTestData(prev => ({ ...prev, platform: value }))}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="android">Android</SelectItem>
                  <SelectItem value="ios">iOS</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label>FCM Token</Label>
              <Input
                value={testData.token}
                onChange={(e) => setTestData(prev => ({ ...prev, token: e.target.value }))}
                placeholder="Enter FCM token..."
                className="font-mono text-xs"
              />
            </div>
            <Button onClick={handleRegisterToken} disabled={isLoading} className="w-full">
              <Smartphone className="h-4 w-4 mr-2" />
              Register Token
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TestTube className="h-5 w-5" />
              Test Notifications
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label>Test Message</Label>
              <Input
                value={testData.testMessage}
                onChange={(e) => setTestData(prev => ({ ...prev, testMessage: e.target.value }))}
                placeholder="Test notification message"
              />
            </div>
            <div className="space-y-2">
              <Button onClick={handleSendTest} disabled={isLoading} className="w-full">
                <Play className="h-4 w-4 mr-2" />
                Send Test Notification
              </Button>
              <Button onClick={testFirebaseConnection} variant="outline" disabled={isLoading} className="w-full">
                <Activity className="h-4 w-4 mr-2" />
                Test Firebase Connection
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
