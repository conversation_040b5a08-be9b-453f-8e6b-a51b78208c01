/**
 * Settings Context - Initial State
 * Defines the default state structure for the settings context
 */

export const initialState = {
  settings: [],
  settingsByCategory: {},
  securitySettings: {
    twoFactorAuthRequired: false,
    passwordPolicy: {
      minLength: 8,
      requireUppercase: true,
      requireLowercase: true,
      requireNumbers: true,
      requireSpecialChars: true,
      passwordExpiryDays: 90
    },
    sessionSettings: {
      sessionTimeout: 30,
      maxLoginAttempts: 5,
      lockoutDuration: 15
    },
    securityHeaders: {
      enableCSP: true,
      enableHSTS: true,
      enableXFrameOptions: true,
      enableXSSProtection: true
    }
  },
  // Shop settings removed - each shop now manages their own business rules
  // Plan limitations define the maximum values allowed per subscription tier
  systemLogs: {
    logs: [],
    totalCount: 0
  },
  loading: false,
  error: null
};
