/**
 * Main export file for authentication module
 * This allows consumers to import everything from a single entry point
 */

// Re-export the main provider and hook
export { AuthProvider } from './auth-provider';
export { useAuth } from './auth-hooks';

// Re-export individual modules for direct access when needed
export * as authChecks from './auth-checks';
export * as authEvents from './auth-events';
export * as authOperations from './auth-operations';
export * as authRoles from './auth-roles';

// Export default provider for backwards compatibility
export { AuthProvider as default } from './auth-provider';
