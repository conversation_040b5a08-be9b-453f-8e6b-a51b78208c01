"use client";

import { useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter
} from '@/components/ui/dialog';
import { Textarea } from '@/components/ui/textarea';
import { But<PERSON> } from '@/components/ui/button';
import { toast } from 'sonner';
import UserService from '@/lib/services/user';
import { AlertTriangle, Trash2, Loader2 } from 'lucide-react';

export function DeleteUserDialog({ isOpen, onClose, user, onUserDeleted }) {
  const [reason, setReason] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  
  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();

    // Validate reason
    if (!reason.trim()) {
      toast.error('Please provide a reason for deleting this user');
      return;
    }

    try {
      setIsSubmitting(true);

      // PERFORMANCE FIX: Use UserService directly to avoid hook conflicts
      const success = await UserService.deleteUser(user.userId, reason);

      if (success) {
        // Show success message
        toast.success('User deleted successfully');

        // Notify parent component to refresh data
        if (onUserDeleted) {
          onUserDeleted();
        }

        // Close dialog
        onClose(true);
      }
    } catch (error) {
      console.error('Error deleting user:', error);
      toast.error(error.message || 'Failed to delete user');
    } finally {
      setIsSubmitting(false);
    }
  };
  
  return (
    <Dialog open={isOpen} onOpenChange={(open) => !isSubmitting && onClose(false)}>
      <DialogContent className="sm:max-w-[450px]">
        <DialogHeader>
          <div className="flex items-center gap-3">
            <Trash2 className="h-6 w-6 text-red-500" />
            <DialogTitle>Delete User</DialogTitle>
          </div>
          <DialogDescription>
            This action will permanently delete the user from the system. The user record will be completely removed from the database.
          </DialogDescription>
        </DialogHeader>
        
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="bg-red-500/10 border border-red-500/20 p-4 rounded-md flex gap-3">
            <AlertTriangle className="h-5 w-5 text-red-500 shrink-0 mt-0.5" />
            <div className="text-sm space-y-1">
              <p className="font-semibold text-red-500">Warning: This action cannot be undone</p>
              <p>The user account will be permanently removed from the database. This action is irreversible.</p>
            </div>
          </div>
          
          <div className="bg-muted p-3 rounded-md">
            <div className="font-medium">User Information:</div>
            <div className="grid grid-cols-2 gap-2 mt-2 text-sm">
              <div>
                <span className="text-muted-foreground">Name:</span>
                <span className="ml-1 font-medium">{user.fullName}</span>
              </div>
              <div>
                <span className="text-muted-foreground">Role:</span>
                <span className="ml-1 font-medium capitalize">{user.role}</span>
              </div>
              <div>
                <span className="text-muted-foreground">Email:</span>
                <span className="ml-1">{user.email}</span>
              </div>
              <div>
                <span className="text-muted-foreground">Shop:</span>
                <span className="ml-1">{user.shopName || 'None'}</span>
              </div>
            </div>
          </div>
          
          <div className="space-y-2">
            <label htmlFor="reason" className="text-sm font-medium">
              Reason for deletion:
            </label>
            <Textarea
              id="reason"
              value={reason}
              onChange={(e) => setReason(e.target.value)}
              placeholder="Provide a detailed reason for deleting this user..."
              className="min-h-[100px]"
              required
            />
            <p className="text-xs text-muted-foreground">
              This reason will be logged for audit purposes before the user is permanently deleted.
            </p>
          </div>
          
          <DialogFooter className="gap-2 sm:gap-0">
            <Button variant="outline" type="button" onClick={() => onClose(false)} disabled={isSubmitting}>
              Cancel
            </Button>
            <Button 
              type="submit" 
              disabled={isSubmitting}
              variant="destructive"
            >
              {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              Delete User
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
