/**
 * PaymentRetryThrottle Model
 * Used for MongoDB-based payment retry throttling with TTL expiry.
 */
const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const paymentRetryThrottleSchema = new Schema({
  subscriptionId: { type: Schema.Types.ObjectId, ref: 'Subscription', required: true, index: true },
  shopId: { type: Schema.Types.ObjectId, ref: 'Shop', required: true, index: true },
  attemptedAt: { type: Date, default: Date.now, index: true }
});

// TTL index: automatically remove documents after 24 hours
paymentRetryThrottleSchema.index({ attemptedAt: 1 }, { expireAfterSeconds: 86400 });

module.exports = mongoose.model('PaymentRetryThrottle', paymentRetryThrottleSchema);
