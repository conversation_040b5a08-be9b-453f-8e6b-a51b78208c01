/**
 * SuperAdmin Shop API module
 * 
 * This module provides direct API access for SuperAdmin shop management
 * using the correct /api/admin/shops endpoints and payload structure
 * that matches the backend SuperAdmin controller implementation.
 */
import api from '../index';

/**
 * SuperAdmin Shop API endpoints
 * All endpoints require SuperAdmin authentication
 */
const shopAPI = {
  /**
   * Create a new shop (SuperAdmin)
   * @param {Object} shopData - Shop creation data
   * @param {string} shopData.fullName - Owner full name (required)
   * @param {string} shopData.email - Owner email (required)
   * @param {string} shopData.phone - Owner phone (required)
   * @param {string} shopData.password - Owner password (required)
   * @param {string} shopData.shopName - Shop name (required)
   * @param {string} shopData.shopAddress - Shop address (required)
   * @param {string} shopData.planType - Plan type (optional, default: 'monthly')
   * @param {File} shopData.shopLogo - Shop logo file (optional)
   * @returns {Promise} API response with created shop
   */
  createShop: (shopData) => {
    // Handle multipart form data for logo upload
    if (shopData.shopLogo instanceof File) {
      const formData = new FormData();
      formData.append('fullName', shopData.fullName);
      formData.append('email', shopData.email);
      formData.append('phone', shopData.phone);
      formData.append('password', shopData.password);
      formData.append('shopName', shopData.shopName);
      formData.append('shopAddress', shopData.shopAddress);
      if (shopData.planType) formData.append('planType', shopData.planType);
      formData.append('shopLogo', shopData.shopLogo);
      
      return api.post('/api/admin/shops', formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });
    } else {
      // JSON request without logo
      return api.post('/api/admin/shops', {
        fullName: shopData.fullName,
        email: shopData.email,
        phone: shopData.phone,
        password: shopData.password,
        shopName: shopData.shopName,
        shopAddress: shopData.shopAddress,
        planType: shopData.planType || 'monthly'
      });
    }
  },

  /**
   * Get shops with pagination and filters (SuperAdmin)
   * @param {Object} params - Query parameters
   * @param {number} params.page - Page number (default: 1)
   * @param {number} params.limit - Items per page (default: 20)
   * @param {string} params.status - Filter by status
   * @param {string} params.search - Search query
   * @returns {Promise} API response with shops
   */
  getShops: (params = {}) => api.get('/api/admin/shops', { params }),

  /**
   * Get shop statistics (SuperAdmin)
   * @param {Object} params - Query parameters for stats
   * @returns {Promise} API response with shop statistics
   */
  getShopStats: (params = {}) => api.get('/api/admin/shops/stats', { params }),

  /**
   * Get a specific shop by ID (SuperAdmin)
   * @param {string} shopId - Shop ID
   * @returns {Promise} API response with shop data
   */
  getShopById: (shopId) => api.get(`/api/admin/shops/${shopId}`),

  /**
   * Update a shop (SuperAdmin)
   * @param {string} shopId - Shop ID
   * @param {Object} shopData - Updated shop data
   * @returns {Promise} API response with updated shop
   */
  updateShop: (shopId, shopData) => api.put(`/api/admin/shops/${shopId}`, shopData),

  /**
   * Update shop logo (SuperAdmin)
   * @param {string} shopId - Shop ID
   * @param {File} logoFile - Logo file
   * @returns {Promise} API response
   */
  updateShopLogo: (shopId, logoFile) => {
    const formData = new FormData();
    formData.append('shopLogo', logoFile);
    
    return api.put(`/api/admin/shops/${shopId}/logo`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });
  },

  /**
   * Change shop status (SuperAdmin)
   * @param {string} shopId - Shop ID
   * @param {string} status - New status ('active', 'suspended', 'inactive')
   * @param {string} reason - Reason for status change (required for suspension)
   * @param {boolean} sendEmail - Whether to send email notification
   * @returns {Promise} API response
   */
  changeShopStatus: (shopId, status, reason = '', sendEmail = true) => 
    api.put(`/api/admin/shops/${shopId}/status`, { status, reason, sendEmail }),

  /**
   * Suspend a shop (SuperAdmin) - Helper method
   * @param {string} shopId - Shop ID
   * @param {string} reason - Suspension reason (required)
   * @param {boolean} sendEmail - Whether to send email notification
   * @returns {Promise} API response
   */
  suspendShop: (shopId, reason, sendEmail = true) => 
    api.put(`/api/admin/shops/${shopId}/status`, { 
      status: 'suspended', 
      reason, 
      sendEmail 
    }),

  /**
   * Reactivate a shop (SuperAdmin) - Helper method
   * @param {string} shopId - Shop ID
   * @param {boolean} sendEmail - Whether to send email notification
   * @returns {Promise} API response
   */
  reactivateShop: (shopId, sendEmail = true) => 
    api.put(`/api/admin/shops/${shopId}/status`, { 
      status: 'active', 
      sendEmail 
    }),

  /**
   * Delete a shop (SuperAdmin)
   * @param {string} shopId - Shop ID
   * @param {string} reason - Deletion reason (required)
   * @returns {Promise} API response
   */
  deleteShop: (shopId, reason) => api.delete(`/api/admin/shops/${shopId}`, { data: { reason } })
};

export default shopAPI;
