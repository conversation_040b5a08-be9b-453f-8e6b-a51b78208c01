"use client";

import { useState } from "react";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { CheckCircle2, XCircle } from "lucide-react";

export function VerificationDialog({ 
  open, 
  onOpenChange, 
  action, 
  onConfirm 
}) {
  const [note, setNote] = useState("");

  const handleConfirm = () => {
    onConfirm(note);
    setNote("");
  };

  const handleOpenChange = (newOpen) => {
    onOpenChange(newOpen);
    if (!newOpen) {
      setNote("");
    }
  };

  return (
    <AlertDialog open={open} onOpenChange={handleOpenChange}>
      <AlertDialogContent className="max-w-md">
        <AlertDialogHeader>
          <div className="flex items-center gap-2">
            {action === "approve" ? (
              <CheckCircle2 className="h-5 w-5 text-primary" />
            ) : (
              <XCircle className="h-5 w-5 text-destructive" />
            )}
            <AlertDialogTitle>
              {action === "approve"
                ? "Verify Payment"
                : "Reject Payment"}
            </AlertDialogTitle>
          </div>
          <AlertDialogDescription className="pt-2">
            {action === "approve"
              ? "This will activate the shop's subscription and grant them access to premium features. Are you sure you want to approve this payment?"
              : "This will reject the payment and the shop will need to submit a new payment. The shop will be notified about this rejection. Are you sure?"}
          </AlertDialogDescription>
        </AlertDialogHeader>
        
        <div className="py-3">
          <Label htmlFor="verificationNote" className="text-sm font-medium">
            Add a note for the shop owner (optional)
          </Label>
          <Input
            id="verificationNote"
            placeholder={action === "approve" 
              ? "e.g., Payment verified successfully. Thank you." 
              : "e.g., Please provide a valid transaction reference."
            }
            value={note}
            onChange={(e) => setNote(e.target.value)}
            className="mt-2"
            multiline={true}
          />
        </div>
        
        <AlertDialogFooter className="sm:space-x-2 flex-col sm:flex-row gap-2 sm:gap-0">
          <AlertDialogCancel className="mt-0">Cancel</AlertDialogCancel>
          <AlertDialogAction
            onClick={handleConfirm}
            className={action === "approve" 
              ? "bg-primary hover:bg-primary/90" 
              : "bg-destructive hover:bg-destructive/90"
            }
          >
            {action === "approve" ? "Verify Payment" : "Reject Payment"}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
