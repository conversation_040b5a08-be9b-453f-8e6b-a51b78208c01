/**
 * Soft delete a pricing plan
 * DELETE /api/plans/:planId
 */
const PlanService = require('../../services/PlanService');
const { logInfo, logSuccess, logError } = require('../../utils');

const deletePlan = async (req, res, next) => {
  try {
    const { planId } = req.params;
    logInfo(`Deleting pricing plan: ${planId}`, 'PlanController');
    
    await PlanService.deletePlan(planId, {
      actorId: req.user.userId,
      actorRole: req.user.role
    });
    
    logSuccess(`Deleted pricing plan: ${planId}`, 'PlanController');
    
    return res.status(200).json({
      success: true,
      message: 'Plan deleted successfully'
    });
  } catch (error) {
    logError(`Failed to delete pricing plan: ${req.params.planId}`, 'PlanController', error);
    return next(error);
  }
};

module.exports = deletePlan;
