import apiBridge from '@/lib/api/bridge';
import { ENDPOINTS } from '@/lib/api/contract';;
import { handleError, processApiResponse, validateRequiredFields } from '../baseService';

/**
 * Change active subscription plan
 * @param {Object} planData - Plan change data (planId/planType, prorated, paymentMethod, paymentDetails)
 * @returns {Promise<Object>} Updated subscription object
 */
async function changeSubscriptionPlan(planData) {
  try {
    // Validate either planId or planType is provided
    if (!planData.planId && !planData.planType) {
      throw new Error('Either planId or planType is required');
    }

    const response = await apiBridge.post(`${ENDPOINTS.SUBSCRIPTIONS.BASE}/change-plan`, planData, {
      clearCacheEndpoint: ENDPOINTS.SUBSCRIPTIONS.BASE
    });
    
    const result = processApiResponse(response, 'Subscription plan changed successfully');
    return result.subscription || result;
  } catch (error) {
    handleError(error, 'SubscriptionService.changeSubscriptionPlan', true);
    throw error;
  }
}

export default changeSubscriptionPlan; 
