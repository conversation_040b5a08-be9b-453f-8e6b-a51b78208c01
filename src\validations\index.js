/**
 * Validations index file
 * Centralizes all validation exports for cleaner imports
 */

// Export schemas
const authSchemas = require('./schemas/authSchemas');
const registerSchemas = require('./schemas/registerSchemas');
const userSchemas = require('./schemas/userSchemas');
const shopSchemas = require('./schemas/shopSchemas');
const superAdminSchemas = require('./schemas/superAdminSchemas');
const superAdminPaymentSchemas = require('./schemas/superAdminPaymentSchemas');
const subscriptionSchemas = require('./schemas/subscriptionSchemas');
const paymentSchemas = require('./schemas/paymentSchemas');
const discountSchemas = require('./schemas/discountSchemas');
const reportSchemas = require('./schemas/reportSchemas');
const planSchemas = require('./schemas/planSchemas');
const settingsSchemas = require('./schemas/settingsSchemas');
const exportSchemas = require('./schemas/exportSchemas');
// ML Integration Schemas
const { debtValidation } = require('./schemas/debtSchemas');
const { customerValidation } = require('./schemas/customerSchemas');

module.exports = {
  // Schemas
  authSchemas,
  registerSchemas,
  userSchemas,
  shopSchemas,
  superAdminSchemas,
  superAdminPaymentSchemas,
  subscriptionSchemas,
  paymentSchemas,
  discountSchemas,
  reportSchemas,
  planSchemas,
  settingsSchemas,
  exportSchemas,
  // ML Integration Validations
  debtValidation,
  customerValidation
};
