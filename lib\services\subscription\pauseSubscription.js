import apiBridge from '@/lib/api/bridge';
import BaseService from '../baseService';

const pauseSubscription = async (subscriptionId, pauseData) => {
  try {
    const response = await apiBridge.patch(
      ENDPOINTS.subscription.pauseSubscription.replace(':subscriptionId', subscriptionId),
      pauseData
    );
    
    return BaseService.processApiResponse(response);
  } catch (error) {
    throw BaseService.handleApiError(error);
  }
};

export default pauseSubscription; 
