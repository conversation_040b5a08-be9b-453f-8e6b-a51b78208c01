import apiBridge from '@/lib/api/bridge';
import { ENDPOINTS } from '@/lib/api/contract';
import { toast } from 'sonner';
import BaseService from '@/lib/services/baseService';

/**
 * Get a specific user by ID
 * @param {string} userId - ID of the user to retrieve
 * @returns {Promise<Object>} User object
 */
async function getUserById(userId) {
  try {
    // Make API request using the bridge
    const response = await apiBridge.get(`${ENDPOINTS.USERS.BASE}/${userId}`);
    
    // Process response
    if (response.data && response.data.success) {
      return response.data.data.user || null;
    }
    
    // Handle unexpected response
    console.error('Unexpected API response format:', response.data);
    toast.error('Failed to load user details: Unexpected response format');
    return null;
  } catch (error) {
    console.error(`Error fetching user ${userId}:`, error);
    // Use standardized error handling
    BaseService.handleError(error, 'UserService.getUserById', true);
    return null;
  }
}

export default getUserById;
