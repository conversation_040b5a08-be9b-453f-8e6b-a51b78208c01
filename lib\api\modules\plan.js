/**
 * Plan API Module
 * 
 * Handles all plan-related API endpoints
 * SuperAdmin-only access for plan management
 */

import apiBridge from '@/lib/api/bridge';
import { ENDPOINTS, REQUEST_SCHEMAS } from '@/lib/api/contract';
import BaseService from '@/lib/services/baseService';

const PlanAPI = {
  /**
   * Get all plans (SuperAdmin only)
   * Backend returns all plans including inactive ones for SuperAdmin
   * @param {Object} options - Query options
   * @returns {Promise<Object>} API response with plans
   */
  async getPlans(options = {}) {
    try {
      const { includeInactive = true, ...queryParams } = options;
      
      const response = await apiBridge.get(ENDPOINTS.PLANS.BASE, {
        params: queryParams,
        cacheKey: 'plans-list',
        cacheTTL: 300000 // 5 minutes cache
      });

      return BaseService.processApiResponse(response, 'Plans retrieved successfully');
    } catch (error) {
      console.error('[PlanAPI.getPlans] Error:', error);
      BaseService.handleError(error, 'PlanAPI.getPlans');
      throw error;
    }
  },

  /**
   * Get plan by ID
   * @param {string} planId - Plan ID
   * @returns {Promise<Object>} API response with plan details
   */
  async getPlanById(planId) {
    try {
      if (!planId) {
        throw new Error('Plan ID is required');
      }

      const response = await apiBridge.get(ENDPOINTS.PLANS.DETAIL(planId), {
        cacheKey: `plan-${planId}`,
        cacheTTL: 300000 // 5 minutes cache
      });

      return BaseService.processApiResponse(response, 'Plan retrieved successfully');
    } catch (error) {
      console.error(`[PlanAPI.getPlanById] Error for plan ${planId}:`, error);
      BaseService.handleError(error, 'PlanAPI.getPlanById');
      throw error;
    }
  },

  /**
   * Create new plan (SuperAdmin only)
   * SuperAdmin has full control over individual features
   * @param {Object} planData - Plan data
   * @returns {Promise<Object>} API response with created plan
   */
  async createPlan(planData) {
    try {
      if (!planData) {
        throw new Error('Plan data is required');
      }

      // Backend respects SuperAdmin's feature choices - no forced overrides
      const response = await apiBridge.post(ENDPOINTS.PLANS.BASE, planData, {
        skipCache: true
      });

      // Clear plans cache after creation
      BaseService.clearCache('plans-list');
      
      return BaseService.processApiResponse(response, 'Plan created successfully');
    } catch (error) {
      console.error('[PlanAPI.createPlan] Error:', error);
      BaseService.handleError(error, 'PlanAPI.createPlan');
      throw error;
    }
  },

  /**
   * Update plan (SuperAdmin only)
   * SuperAdmin has full control over individual features
   * @param {string} planId - Plan ID
   * @param {Object} updateData - Data to update
   * @returns {Promise<Object>} API response with updated plan
   */
  async updatePlan(planId, updateData) {
    try {
      if (!planId) {
        throw new Error('Plan ID is required');
      }
      if (!updateData || Object.keys(updateData).length === 0) {
        throw new Error('Update data is required');
      }

      // Backend respects SuperAdmin's feature choices - no forced overrides
      const response = await apiBridge.put(ENDPOINTS.PLANS.DETAIL(planId), updateData, {
        skipCache: true
      });

      // Clear related caches
      BaseService.clearCache('plans-list');
      BaseService.clearCache(`plan-${planId}`);
      
      return BaseService.processApiResponse(response, 'Plan updated successfully');
    } catch (error) {
      console.error(`[PlanAPI.updatePlan] Error for plan ${planId}:`, error);
      BaseService.handleError(error, 'PlanAPI.updatePlan');
      throw error;
    }
  },

  /**
   * Delete plan (SuperAdmin only)
   * Backend performs soft delete by setting isDeleted flag
   * @param {string} planId - Plan ID
   * @returns {Promise<Object>} API response
   */
  async deletePlan(planId) {
    try {
      if (!planId) {
        throw new Error('Plan ID is required');
      }

      const response = await apiBridge.delete(ENDPOINTS.PLANS.DETAIL(planId), {
        skipCache: true
      });

      // Clear related caches
      BaseService.clearCache('plans-list');
      BaseService.clearCache(`plan-${planId}`);
      
      return BaseService.processApiResponse(response, 'Plan deleted successfully');
    } catch (error) {
      console.error(`[PlanAPI.deletePlan] Error for plan ${planId}:`, error);
      BaseService.handleError(error, 'PlanAPI.deletePlan');
      throw error;
    }
  },

  /**
   * Get plan statistics (SuperAdmin only)
   * Backend provides comprehensive stats including revenue, usage, expiring subscriptions
   * @returns {Promise<Object>} API response with plan statistics
   */
  async getPlanStats() {
    try {
      const response = await apiBridge.get(ENDPOINTS.PLANS.STATS, {
        cacheKey: 'plan-stats',
        cacheTTL: 300000 // 5 minutes cache
      });

      return BaseService.processApiResponse(response, 'Plan statistics retrieved successfully');
    } catch (error) {
      // Handle case where endpoint might not be fully implemented
      if (error.response?.status === 404) {
        console.warn('[PlanAPI.getPlanStats] Plan stats endpoint not fully implemented yet');
        return {
          success: true,
          data: {
            summary: {
              totalPlans: 0,
              activePlans: 0,
              pendingPlans: 0,
              mostUsedPlan: null
            },
            data: []
          }
        };
      }
      
      console.error('[PlanAPI.getPlanStats] Error:', error);
      BaseService.handleError(error, 'PlanAPI.getPlanStats');
      throw error;
    }
  },

  /**
   * Toggle plan status (SuperAdmin only)
   * UPDATED: Now properly calls the backend toggle endpoint
   * @param {string} planId - Plan ID
   * @param {boolean} isActive - New status
   * @returns {Promise<Object>} API response
   */
  async togglePlanStatus(planId, isActive) {
    try {
      if (!planId) {
        throw new Error('Plan ID is required');
      }
      if (typeof isActive !== 'boolean') {
        throw new Error('isActive must be a boolean');
      }

      const response = await apiBridge.patch(ENDPOINTS.PLANS.TOGGLE_STATUS(planId), {
        isActive
      }, {
        skipCache: true
      });

      // Clear related caches
      BaseService.clearCache('plans-list');
      BaseService.clearCache(`plan-${planId}`);
      
      return BaseService.processApiResponse(response, `Plan ${isActive ? 'activated' : 'deactivated'} successfully`);
    } catch (error) {
      console.error(`[PlanAPI.togglePlanStatus] Error for plan ${planId}:`, error);
      BaseService.handleError(error, 'PlanAPI.togglePlanStatus');
      throw error;
    }
  },

  /**
   * Duplicate plan (SuperAdmin only)
   * Creates a copy of existing plan with modified name
   * @param {string} planId - Source plan ID
   * @param {string} newName - New plan name
   * @returns {Promise<Object>} API response with duplicated plan
   */
  async duplicatePlan(planId, newName) {
    try {
      if (!planId) {
        throw new Error('Plan ID is required');
      }
      if (!newName) {
        throw new Error('New plan name is required');
      }

      // Get original plan first
      const originalPlan = await this.getPlanById(planId);
      
      if (!originalPlan.success) {
        throw new Error('Failed to retrieve original plan');
      }

      // Create duplicate with new name
      const duplicateData = {
        ...originalPlan.data,
        name: newName.toLowerCase().replace(/\s+/g, '_'),
        displayName: newName,
        // Remove backend-generated fields
        planId: undefined,
        createdAt: undefined,
        updatedAt: undefined,
        createdBy: undefined
      };

      return await this.createPlan(duplicateData);
    } catch (error) {
      console.error(`[PlanAPI.duplicatePlan] Error for plan ${planId}:`, error);
      BaseService.handleError(error, 'PlanAPI.duplicatePlan');
      throw error;
    }
  },

  /**
   * Get plans for selection dropdown
   * Returns only active plans formatted for UI selection
   * @returns {Promise<Object>} API response with formatted plan options
   */
  async getPlansForSelection() {
    try {
      const response = await this.getPlans({ includeInactive: false });
      
      if (!response.success) {
        throw new Error('Failed to retrieve plans');
      }

      const plans = response.data || [];
      const formattedPlans = plans
        .filter(plan => plan.isActive && !plan.isDeleted)
        .sort((a, b) => (a.displayOrder || 1) - (b.displayOrder || 1))
        .map(plan => ({
          value: plan.planId || plan._id,
          label: plan.displayName || plan.name,
          type: plan.type,
          price: plan.pricing?.basePrice || 0,
          currency: plan.pricing?.currency || 'USD',
          billingCycle: plan.pricing?.billingCycle || 'monthly',
          isRecommended: plan.metadata?.isRecommended || false
        }));

      return {
        success: true,
        data: formattedPlans
      };
    } catch (error) {
      console.error('[PlanAPI.getPlansForSelection] Error:', error);
      BaseService.handleError(error, 'PlanAPI.getPlansForSelection');
      throw error;
    }
  }
};

export default PlanAPI; 