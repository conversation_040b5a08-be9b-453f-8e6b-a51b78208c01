const Joi = require('joi');
const { ErrorResponse } = require('../utils');

// Import validation schemas from centralized location 
const { 
  authSchemas,
  userSchemas,
  shopSchemas,
  discountSchemas,
  reportSchemas
} = require('../validations');

/**
 * Factory function to create Joi validation middleware for request body
 * @param {Joi.Schema} schema - Joi schema for validation
 * @returns {Function} Express middleware
 */
const validate = (schema) => {
  return (req, res, next) => {
    // Handle schemas that have a body property (like those in planSchemas)
    const actualSchema = schema.body || schema;
    
    // Ensure actualSchema is a valid Joi schema
    if (!actualSchema || typeof actualSchema.validate !== 'function') {
      console.error('ValidationMiddleware: Invalid schema provided for body validation', actualSchema);
      return res.status(500).json(
        ErrorResponse.create('Internal validation configuration error', 500, 'server_error')
      );
    }
    
    // Pre-process form data: parse JSON strings for multipart/form-data
    if (req.headers['content-type']?.includes('multipart/form-data')) {
      Object.keys(req.body).forEach(key => {
        if (typeof req.body[key] === 'string') {
          try {
            // Try to parse as JSON if it looks like JSON
            if (req.body[key].startsWith('{') && req.body[key].endsWith('}')) {
              const parsed = JSON.parse(req.body[key]);
              req.body[key] = parsed;
            }
          } catch (parseError) {
            // If parsing fails, keep as string - no logging needed
          }
        }
      });
    }
    
    const { error } = actualSchema.validate(req.body, { 
      abortEarly: false,
      stripUnknown: true,
      allowUnknown: false 
    });
    
    if (error) {
      // Only log validation errors in development mode
      if (process.env.NODE_ENV === 'development') {
        console.error('❌ [ValidationMiddleware] Validation failed:', error.details);
      }
      
      // Map Joi errors to readable format
      const errorDetails = error.details.map(detail => ({
        field: detail.path.join('.'),
        message: detail.message,
        type: detail.type
      }));
      
      return res.status(400).json(
        ErrorResponse.create('Validation error', 400, 'validation_error', errorDetails)
      );
    }
    
    // If validation succeeds, pass validated data to req.validatedData
    req.validatedData = actualSchema.validate(req.body, { stripUnknown: true }).value;
    next();
  };
};

/**
 * Factory function to create Joi validation middleware for query parameters
 * @param {Joi.Schema} schema - Joi schema for validation
 * @returns {Function} Express middleware
 */
const validateQuery = (schema) => {
  return (req, res, next) => {
    // Handle schemas that have a query property
    const actualSchema = schema.query || schema;
    
    // Ensure actualSchema is a valid Joi schema
    if (!actualSchema || typeof actualSchema.validate !== 'function') {
      console.error('ValidationMiddleware: Invalid schema provided for query validation', actualSchema);
      return res.status(500).json(
        ErrorResponse.create('Internal validation configuration error', 500, 'server_error')
      );
    }
    
    const { error } = actualSchema.validate(req.query, { 
      abortEarly: false,
      stripUnknown: true,
      allowUnknown: false 
    });
    
    if (error) {
      // Map Joi errors to readable format
      const errorDetails = error.details.map(detail => ({
        field: detail.path.join('.'),
        message: detail.message,
        type: detail.type
      }));
      
      return res.status(400).json(
        ErrorResponse.create('Query validation error', 400, 'query_validation_error', errorDetails)
      );
    }
    
    // If validation succeeds, pass validated data to req.validatedQuery
    req.validatedQuery = actualSchema.validate(req.query, { stripUnknown: true }).value;
    next();
  };
};

/**
 * Factory function to create Joi validation middleware for URL parameters
 * @param {Joi.Schema} schema - Joi schema for validation
 * @returns {Function} Express middleware
 */
const validateParams = (schema) => {
  return (req, res, next) => {
    // Handle schemas that have a params property
    const actualSchema = schema.params || schema;
    
    // Ensure actualSchema is a valid Joi schema
    if (!actualSchema || typeof actualSchema.validate !== 'function') {
      console.error('ValidationMiddleware: Invalid schema provided for params validation', actualSchema);
      return res.status(500).json(
        ErrorResponse.create('Internal validation configuration error', 500, 'server_error')
      );
    }
    
    const { error } = actualSchema.validate(req.params, { 
      abortEarly: false,
      stripUnknown: true,
      allowUnknown: false 
    });
    
    if (error) {
      // Map Joi errors to readable format
      const errorDetails = error.details.map(detail => ({
        field: detail.path.join('.'),
        message: detail.message,
        type: detail.type
      }));
      
      return res.status(400).json(
        ErrorResponse.create('Parameter validation error', 400, 'params_validation_error', errorDetails)
      );
    }
    
    // If validation succeeds, pass validated data to req.validatedParams
    req.validatedParams = actualSchema.validate(req.params, { stripUnknown: true }).value;
    next();
  };
};

module.exports = {
  validate,
  validateQuery,
  validateParams,
  authSchemas,
  userSchemas,
  shopSchemas,
  discountSchemas,
  reportSchemas
};
