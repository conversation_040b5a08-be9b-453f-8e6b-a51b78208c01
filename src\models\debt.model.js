const mongoose = require('mongoose');

const debtSchema = new mongoose.Schema({
  // System IDs
  debtId: {
    type: String,
    required: true,
    unique: true,
    trim: true
  },
  customerId: {
    type: String,
    required: true,
    trim: true
  },
  shopId: {
    type: String,
    required: true,
    trim: true
  },
  
  // CSV Direct Mapping Fields - REQUIRED for ML
  // CustomerID removed - using customerId instead
  CustomerName: {
    type: String,
    required: true,
    trim: true
  },
  CustomerType: {
    type: String,
    enum: ['New', 'Returning'],
    required: true
  },
  // DebtID removed - using debtId instead
  DebtAmount: {
    type: Number,
    required: true,
    min: 0
  },
  OutstandingDebt: {
    type: Number,
    required: true,
    min: 0
  },
  DebtCreationDate: {
    type: Date,
    required: true,
    default: Date.now
  },
  DueDate: {
    type: Date,
    required: true
  },
  RepaymentTime: {
    type: Number,
    min: 1
  },
  
  // Auto-calculated ML Fields
  DebtPaidRatio: {
    type: Number,
    min: 0,
    max: 1,
    default: 0
  },
  PaymentDelay: {
    type: Number,
    default: 0
  },
  IsOnTime: {
    type: Boolean,
    default: true
  },
  
  // Payment Information
  PaidAmount: {
    type: Number,
    default: 0,
    min: 0
  },
  PaidDate: {
    type: Date
  },
  
  // ML Risk Assessment
  RiskLevel: {
    type: String,
    enum: ['Low Risk', 'Medium Risk', 'High Risk', 'Active Debt'],
    default: 'Active Debt'
  },
  
  // Status
  status: {
    type: String,
    enum: ['active', 'paid', 'overdue', 'partially_paid'],
    default: 'active'
  },
  
  // System fields
  description: {
    type: String,
    trim: true
  },
  isDeleted: {
    type: Boolean,
    default: false
  }
}, {
  timestamps: true
});

// Auto-generate DebtID and calculate fields (OPTIMIZED)
debtSchema.pre('save', function(next) {
  try {
    // DebtID generation removed - debtId comes from idGenerator.js
    
    // Ensure DebtCreationDate is set
    if (!this.DebtCreationDate) {
      this.DebtCreationDate = new Date();
    }
    
    // Calculate RepaymentTime (always calculate it)
    if (this.DueDate && this.DebtCreationDate) {
      const timeDiff = this.DueDate.getTime() - this.DebtCreationDate.getTime();
      this.RepaymentTime = Math.max(1, Math.ceil(timeDiff / (1000 * 60 * 60 * 24))); // Minimum 1 day
    }
    
    // Calculate DebtPaidRatio
    if (this.DebtAmount > 0) {
      this.DebtPaidRatio = (this.DebtAmount - this.OutstandingDebt) / this.DebtAmount;
    }
    
    // Update status based on outstanding debt
    if (this.OutstandingDebt === 0) {
      this.status = 'paid';
    } else if (this.OutstandingDebt < this.DebtAmount) {
      this.status = 'partially_paid';
    } else {
      this.status = 'active'; // Default for new debts
    }
    
    // Check if overdue (only if not a new debt)
    if (!this.isNew) {
      const now = new Date();
      if (now > this.DueDate && this.OutstandingDebt > 0) {
        this.status = 'overdue';
      }
    }
    
    next();
  } catch (error) {
    console.error('Error in debt pre-save hook:', error);
    next(error);
  }
});

// Method to get CSV payload format for ML
debtSchema.methods.getCSVPayload = function() {
  return {
    CustomerID: this.customerId, // Use customerId instead of CustomerID
    CustomerName: this.CustomerName,
    CustomerType: this.CustomerType,
    DebtID: this.debtId,
    DebtAmount: this.DebtAmount,
    OutstandingDebt: this.OutstandingDebt,
    DebtCreationDate: this.DebtCreationDate.toISOString().split('T')[0],
    DueDate: this.DueDate.toISOString().split('T')[0],
    RepaymentTime: this.RepaymentTime,
    DebtPaidRatio: this.DebtPaidRatio,
    PaymentDelay: this.PaymentDelay,
    IsOnTime: this.IsOnTime ? 1 : 0,
    PaidAmount: this.PaidAmount,
    PaidDate: this.PaidDate ? this.PaidDate.toISOString().split('T')[0] : null
  };
};

// Method to add payment
debtSchema.methods.addPayment = function(paymentAmount, paymentDate = new Date()) {
  this.PaidAmount += paymentAmount;
  this.PaidDate = paymentDate;
  this.OutstandingDebt = Math.max(0, this.OutstandingDebt - paymentAmount);
  
  // Calculate payment delay
  const dueDate = new Date(this.DueDate);
  const paidDate = new Date(paymentDate);
  this.PaymentDelay = Math.ceil((paidDate - dueDate) / (1000 * 60 * 60 * 24));
  this.IsOnTime = this.PaymentDelay <= 0;
  
  return this.save();
};

// Indexes for performance optimization
debtSchema.index({ shopId: 1, customerId: 1 });
debtSchema.index({ debtId: 1 });
debtSchema.index({ customerId: 1 }); // Changed from CustomerID to customerId
debtSchema.index({ status: 1 });
debtSchema.index({ shopId: 1, isDeleted: 1 }); // For report queries
debtSchema.index({ shopId: 1, createdAt: -1 }); // For date-based reports
debtSchema.index({ shopId: 1, DueDate: 1 }); // For overdue calculations
debtSchema.index({ shopId: 1, RiskLevel: 1 }); // For risk-based queries
debtSchema.index({ shopId: 1, status: 1, isDeleted: 1 }); // Compound index for filtered queries

const Debt = mongoose.model('Debt', debtSchema);
module.exports = Debt;
