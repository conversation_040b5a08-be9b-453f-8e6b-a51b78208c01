'use client';

import React, { useMemo, useState } from 'react';
import PropTypes from 'prop-types';
import { format } from 'date-fns';
import { MoreVertical, Eye, Edit, Ban, Power, Trash2, Calendar, CreditCard, RefreshCw, CheckSquare, XSquare, Users, CheckCircle, XCircle, Clock, AlertTriangle } from 'lucide-react';
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { DataTable } from '@/components/dashboard/common/data-table';
import { formatSubscriptionStatus, formatCurrency } from '@/utils/subscriptionUtils';
import { useSubscription } from '@/hooks/use-subscription';
import { toast } from 'sonner';

// Import Action Modals
import ViewSubscriptionModal from './actions/view-subscription-modal';
import EditSubscriptionModal from './actions/edit-subscription-modal';
import ExtendSubscriptionModal from './actions/extend-subscription-modal';
import CancelSubscriptionModal from './actions/cancel-subscription-modal';
import RetryPaymentModal from './actions/retry-payment-modal';

import SubscriptionService from '@/lib/services/subscription';

/**
 * Helper function to format dates with error handling
 * @param {string|Date|null} dateString - The date string to format
 * @returns {string} Formatted date string or fallback text
 */
const formatDate = (dateString) => {
  try {
    if (!dateString) return 'N/A';
    const date = new Date(dateString);
    return isNaN(date.getTime()) ? 'Invalid date' : format(date, 'MMM dd, yyyy');
  } catch (error) {
    console.error('Error formatting date:', error);
    return 'N/A';
  }
};

/**
 * SubscriptionsTable Component - Displays subscription data in a table format with actions and bulk operations
 * 
 * @param {Object} props - Component props
 * @param {Array} props.subscriptions - Array of subscription objects to display
 * @param {boolean} props.isLoading - Whether data is loading
 * @param {Object|boolean} props.pagination - Pagination config or false to disable
 * @param {Function} props.onPageChange - Callback when page changes
 * @param {Function} props.onPageSizeChange - Callback when page size changes
 * @param {boolean} props.showActions - Whether to show action buttons
 * @param {boolean} props.isSuperAdmin - Whether user is SuperAdmin
 * @param {string} props.title - Optional table title
 * @param {boolean} props.enableBulkOperations - Whether to enable bulk operations
 * @param {Function} props.onRefresh - Callback to refresh data
 * @returns {JSX.Element} Rendered component
 */
export const SubscriptionsTable = ({ 
  subscriptions = [], 
  isLoading,
  pagination = true,
  onPageChange,
  onPageSizeChange,
  showActions = true,
  isSuperAdmin = false,
  title = null,
  enableBulkOperations = false,
  onRefresh
}) => {
  // Bulk operations state
  const [selectedSubscriptions, setSelectedSubscriptions] = useState([]);
  const [showBulkOperations, setShowBulkOperations] = useState(false);
  const [bulkOperation, setBulkOperation] = useState('');
  const [operationData, setOperationData] = useState({});
  const [isProcessing, setIsProcessing] = useState(false);

  // Action modal state
  const [activeModal, setActiveModal] = useState(null);
  const [selectedSubscription, setSelectedSubscription] = useState(null);

  const { bulkUpdateSubscriptions } = useSubscription();

  // Development logging
  if (process.env.NODE_ENV !== 'production') {
    console.log('[SubscriptionsTable] Rendering with:', { 
      subscriptionCount: subscriptions?.length || 0, 
      isLoading
    });
  }

  // Handle subscription selection
  const handleSubscriptionToggle = (subscriptionId) => {
    setSelectedSubscriptions(prev => 
      prev.includes(subscriptionId)
        ? prev.filter(id => id !== subscriptionId)
        : [...prev, subscriptionId]
    );
  };

  // Handle select all
  const handleSelectAll = () => {
    if (selectedSubscriptions.length === subscriptions.length) {
      setSelectedSubscriptions([]);
    } else {
      setSelectedSubscriptions(subscriptions.map(sub => sub.id || sub._id));
    }
  };

  // Handle bulk operation execution
  const handleExecuteBulkOperation = async () => {
    if (!bulkOperation || selectedSubscriptions.length === 0) {
      toast.error('Please select an operation and at least one subscription');
      return;
    }

    setIsProcessing(true);
    
    try {
      const bulkData = {
        operation: bulkOperation,
        subscriptionIds: selectedSubscriptions,
        ...operationData
      };

      await bulkUpdateSubscriptions(bulkData);
      
      // Reset selections and operation
      setSelectedSubscriptions([]);
      setBulkOperation('');
      setOperationData({});
      setShowBulkOperations(false);
      
      // Refresh data
      if (onRefresh) {
        onRefresh();
      }
      
      toast.success(`Bulk operation completed for ${selectedSubscriptions.length} subscriptions`);
    } catch (error) {
      console.error('Bulk operation error:', error);
      toast.error('Failed to execute bulk operation');
    } finally {
      setIsProcessing(false);
    }
  };

  // Modal management
  const openModal = (modalType, subscription) => {
    setSelectedSubscription(subscription);
    setActiveModal(modalType);
  };

  const closeModal = () => {
    setActiveModal(null);
    setSelectedSubscription(null);
  };

  const handleModalSuccess = () => {
    closeModal();
    // Refresh the data
    if (onRefresh) {
      onRefresh();
    }
    // Could also trigger a local refresh here
  };

  // Render operation-specific form fields
  const renderOperationFields = () => {
    switch (bulkOperation) {
      case 'extend':
        return (
          <div className="space-y-4">
            <div>
              <Label htmlFor="days">Extension Days</Label>
              <Select
                value={operationData.days?.toString() || ''}
                onValueChange={(value) => setOperationData({ ...operationData, days: parseInt(value) })}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select extension period" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="7">7 days</SelectItem>
                  <SelectItem value="14">14 days</SelectItem>
                  <SelectItem value="30">30 days</SelectItem>
                  <SelectItem value="60">60 days</SelectItem>
                  <SelectItem value="90">90 days</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label htmlFor="reason">Reason for Extension</Label>
              <Textarea
                id="reason"
                placeholder="Enter reason for extending subscriptions..."
                value={operationData.reason || ''}
                onChange={(e) => setOperationData({ ...operationData, reason: e.target.value })}
              />
            </div>
          </div>
        );
      
      case 'cancel':
        return (
          <div className="space-y-4">
            <div>
              <Label htmlFor="reason">Cancellation Reason</Label>
              <Textarea
                id="reason"
                placeholder="Enter reason for cancellation..."
                value={operationData.reason || ''}
                onChange={(e) => setOperationData({ ...operationData, reason: e.target.value })}
                required
              />
            </div>
            <div className="flex items-center space-x-2">
              <Checkbox
                id="immediate"
                checked={operationData.immediateEffect || false}
                onCheckedChange={(checked) => setOperationData({ ...operationData, immediateEffect: checked })}
              />
              <Label htmlFor="immediate">Cancel immediately (otherwise at end of period)</Label>
            </div>
          </div>
        );

      case 'update_auto_renewal':
        return (
          <div>
            <Label>Auto Renewal Setting</Label>
            <Select
              value={operationData.autoRenew?.toString() || ''}
              onValueChange={(value) => setOperationData({ ...operationData, autoRenew: value === 'true' })}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select auto renewal setting" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="true">Enable Auto Renewal</SelectItem>
                <SelectItem value="false">Disable Auto Renewal</SelectItem>
              </SelectContent>
            </Select>
          </div>
        );

      case 'change_status':
        return (
          <div className="space-y-4">
            <div>
              <Label>New Status</Label>
              <Select
                value={operationData.status || ''}
                onValueChange={(value) => setOperationData({ ...operationData, status: value })}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select new status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="active">Active</SelectItem>
                  <SelectItem value="suspended">Suspended</SelectItem>
                  <SelectItem value="cancelled">Cancelled</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label htmlFor="reason">Reason</Label>
              <Textarea
                id="reason"
                placeholder="Enter reason for status change..."
                value={operationData.reason || ''}
                onChange={(e) => setOperationData({ ...operationData, reason: e.target.value })}
              />
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  // Table columns definition
  const columns = useMemo(() => {
    const baseColumns = [];

    // Add checkbox column for bulk operations
    if (enableBulkOperations && isSuperAdmin) {
      baseColumns.push({
        key: 'select',
        header: (
          <Checkbox
            checked={selectedSubscriptions.length === subscriptions.length && subscriptions.length > 0}
            onCheckedChange={handleSelectAll}
            indeterminate={selectedSubscriptions.length > 0 && selectedSubscriptions.length < subscriptions.length}
          />
        ),
        cell: (subscription) => {
          const subData = subscription.row?.original || subscription;
          const subscriptionId = subData.id || subData._id;
          
          return (
            <Checkbox
              checked={selectedSubscriptions.includes(subscriptionId)}
              onCheckedChange={() => handleSubscriptionToggle(subscriptionId)}
            />
          );
        }
      });
    }

    // Add standard columns
    baseColumns.push(
      {
        key: 'subscription',
        header: 'Subscription',
        cell: (subscription) => {
          const subData = subscription.row?.original || subscription;
          
          // FIXED: Better shop name resolution
          const shopName = subData.shop?.name || 
                          subData.shopName || 
                          subData.shop?.shopName || 
                          subData.shop?.businessName || 
                          'Unknown Shop';
          
          return (
            <div className="flex flex-col">
              <span className="font-medium">{subData.subscriptionId || subData.id || subData._id}</span>
              <span className="text-xs text-muted-foreground">
                {shopName}
              </span>
            </div>
          );
        }
      },
      {
        key: 'plan',
        header: 'Plan',
        cell: (subscription) => {
          const subData = subscription.row?.original || subscription;
          
          return (
            <div className="flex flex-col">
              <span className="font-medium">{subData.plan?.name || subData.planType || 'N/A'}</span>
              <span className="text-xs text-muted-foreground">
                {subData.plan?.displayName || subData.planId || ''}
              </span>
            </div>
          );
        }
      },
      {
        key: 'status',
        header: 'Status',
        cell: (subscription) => {
          const subData = subscription.row?.original || subscription;
          const statusInfo = formatSubscriptionStatus(subData.status);
          
          return (
            <Badge variant={statusInfo.variant} className="capitalize">
              {statusInfo.label}
            </Badge>
          );
        }
      },
      {
        key: 'dates',
        header: 'Period',
        cell: (subscription) => {
          const subData = subscription.row?.original || subscription;
          
          // FIXED: Better period formatting
          const startDate = subData.startDate || subData.dates?.startDate;
          const endDate = subData.endDate || subData.dates?.endDate;
          const billingCycle = subData.pricing?.billingCycle || subData.plan?.type || 'monthly';
          
          return (
            <div className="flex flex-col">
              <span className="text-sm">
                {startDate && endDate ? 
                  `${formatDate(startDate)} - ${formatDate(endDate)}` : 
                  `${billingCycle.charAt(0).toUpperCase() + billingCycle.slice(1)} Plan`
                }
              </span>
              <span className="text-xs text-muted-foreground">
                {subData.remainingDays !== null && subData.remainingDays !== undefined ? 
                  `${subData.remainingDays} days left` : 
                  `${billingCycle} billing`
                }
              </span>
            </div>
          );
        }
      },
      {
        key: 'payment',
        header: 'Payment',
        cell: (subscription) => {
          const subData = subscription.row?.original || subscription;
          
          // FIXED: Better payment amount calculation
          const amount = subData.pricing?.amount || 
                        subData.pricing?.basePrice || 
                        (subData.plan?.type === 'trial' ? 0 : 
                         subData.plan?.type === 'monthly' ? 10 : 
                         subData.plan?.type === 'yearly' ? 96 : 0);
          
          const currency = subData.pricing?.currency || 'USD';
          const paymentMethod = subData.paymentMethod || 
                              subData.payment?.method || 
                              (subData.plan?.type === 'trial' ? 'free' : 'offline');
          
          return (
            <div className="flex flex-col">
              <span className="font-medium">
                {formatCurrency(amount, currency)}
              </span>
              <span className="text-xs text-muted-foreground capitalize">
                {paymentMethod === 'free' ? 'Free Trial' : 
                 paymentMethod === 'offline' ? 'Manual Payment' :
                 paymentMethod === 'evc_plus' ? 'EVC Plus' : 
                 paymentMethod || 'N/A'}
              </span>
            </div>
          );
        }
      },
      {
        key: 'autoRenewal',
        header: 'Auto Renewal',
        cell: (subscription) => {
          const subData = subscription.row?.original || subscription;
          
          return (
            <Badge variant={subData.autoRenewal ? 'success' : 'secondary'}>
              {subData.autoRenewal ? 'Enabled' : 'Disabled'}
            </Badge>
          );
        }
      },
      {
        key: 'lastUpdated',
        header: 'Last Updated',
        cell: (subscription) => {
          const subData = subscription.row?.original || subscription;
          
          return (
            <span className="text-sm">
              {formatDate(subData.updatedAt)}
            </span>
          );
        }
      }
    );

    // Add actions column if showActions is true and user is SuperAdmin
    if (showActions && isSuperAdmin) {
      baseColumns.push({
        key: 'actions',
        header: 'Actions',
        cell: (subscription) => {
          const subData = subscription.row?.original || subscription;
          
          return (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" className="h-8 w-8 p-0">
                  <span className="sr-only">Open menu</span>
                  <MoreVertical className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem onClick={() => handleView(subData)}>
                  <Eye className="mr-2 h-4 w-4" />
                  View Details
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => handleEdit(subData)}>
                  <Edit className="mr-2 h-4 w-4" />
                  Edit
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => handleExtend(subData)}>
                  <Calendar className="mr-2 h-4 w-4" />
                  Extend
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => handleRetry(subData)}>
                  <RefreshCw className="mr-2 h-4 w-4" />
                  Retry Payment
                </DropdownMenuItem>
                {subData.status === 'active' ? (
                  <DropdownMenuItem onClick={() => handleCancel(subData)} className="text-red-600">
                    <Ban className="mr-2 h-4 w-4" />
                    Cancel
                  </DropdownMenuItem>
                ) : (
                  <DropdownMenuItem onClick={() => handleReactivate(subData)} className="text-green-600">
                    <Power className="mr-2 h-4 w-4" />
                    Reactivate
                  </DropdownMenuItem>
                )}
              </DropdownMenuContent>
            </DropdownMenu>
          );
        }
      });
    }

    return baseColumns;
  }, [selectedSubscriptions, subscriptions, enableBulkOperations, isSuperAdmin, showActions]);

  // Action handlers
  const handleView = (subscription) => {
    openModal('view', subscription);
  };

  const handleEdit = (subscription) => {
    openModal('edit', subscription);
  };

  const handleExtend = (subscription) => {
    openModal('extend', subscription);
  };

  const handleRetry = (subscription) => {
    openModal('retry', subscription);
  };

  const handleCancel = (subscription) => {
    openModal('cancel', subscription);
  };

  const handleReactivate = async (subscription) => {
    try {
      // For reactivation, we'll extend the subscription to reactivate it
      openModal('extend', subscription);
    } catch (error) {
      console.error('Error reactivating subscription:', error);
      toast.error('Failed to reactivate subscription');
    }
  };

  // Custom table title with bulk actions
  const tableTitle = title || (enableBulkOperations && selectedSubscriptions.length > 0 
    ? `${selectedSubscriptions.length} subscription${selectedSubscriptions.length !== 1 ? 's' : ''} selected`
    : 'All Subscriptions'
  );

  return (
    <div className="space-y-4">
      {/* Bulk Operations Actions Bar */}
      {enableBulkOperations && selectedSubscriptions.length > 0 && (
        <Card>
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Users className="h-4 w-4" />
                <CardTitle className="text-base">
                  Bulk Operations ({selectedSubscriptions.length} selected)
                </CardTitle>
              </div>
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShowBulkOperations(!showBulkOperations)}
                >
                  {showBulkOperations ? 'Hide Options' : 'Show Options'}
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setSelectedSubscriptions([])}
                >
                  Clear Selection
                </Button>
              </div>
            </div>
          </CardHeader>
          
          {showBulkOperations && (
            <CardContent className="pt-0">
              <div className="space-y-4">
                <div>
                  <Label>Bulk Operation</Label>
                  <Select
                    value={bulkOperation}
                    onValueChange={setBulkOperation}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select operation" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="extend">Extend Subscriptions</SelectItem>
                      <SelectItem value="cancel">Cancel Subscriptions</SelectItem>
                      <SelectItem value="update_auto_renewal">Update Auto Renewal</SelectItem>
                      <SelectItem value="change_status">Change Status</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {bulkOperation && renderOperationFields()}

                {bulkOperation && (
                  <div className="flex gap-2">
                    <Button
                      onClick={handleExecuteBulkOperation}
                      disabled={isProcessing}
                      className="flex-1"
                    >
                      {isProcessing ? (
                        <>
                          <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                          Processing...
                        </>
                      ) : (
                        `Execute Operation (${selectedSubscriptions.length} items)`
                      )}
                    </Button>
                    <Button
                      variant="outline"
                      onClick={() => {
                        setBulkOperation('');
                        setOperationData({});
                      }}
                    >
                      Cancel
                    </Button>
                  </div>
                )}
              </div>
            </CardContent>
          )}
        </Card>
      )}

      {/* Data Table */}
      <Card>
        <CardContent className="p-0">
          <DataTable
            data={subscriptions}
            columns={columns}
            isLoading={isLoading}
            pagination={true}
            currentPage={pagination?.page || 1}
            pageSize={pagination?.limit || 10}
            totalItems={pagination?.total || 0}
            totalPages={pagination?.totalPages || 1}
            onPageChange={onPageChange}
            onPageSizeChange={onPageSizeChange}
            className="w-full"
            emptyState={
              <div className="flex flex-col items-center justify-center py-8">
                <p className="text-muted-foreground">No subscriptions found.</p>
              </div>
            }
          />
        </CardContent>
      </Card>

      {/* Action Modals */}
      <ViewSubscriptionModal
        isOpen={activeModal === 'view'}
        onClose={closeModal}
        subscription={selectedSubscription}
      />

      <EditSubscriptionModal
        isOpen={activeModal === 'edit'}
        onClose={closeModal}
        subscription={selectedSubscription}
        onSuccess={handleModalSuccess}
      />

      <ExtendSubscriptionModal
        isOpen={activeModal === 'extend'}
        onClose={closeModal}
        subscription={selectedSubscription}
        onSuccess={handleModalSuccess}
      />

      <CancelSubscriptionModal
        isOpen={activeModal === 'cancel'}
        onClose={closeModal}
        subscription={selectedSubscription}
        onSuccess={handleModalSuccess}
      />

      <RetryPaymentModal
        isOpen={activeModal === 'retry'}
        onClose={closeModal}
        subscription={selectedSubscription}
        onSuccess={handleModalSuccess}
      />
    </div>
  );
};

// PropTypes for type checking
SubscriptionsTable.propTypes = {
  subscriptions: PropTypes.array,
  isLoading: PropTypes.bool,
  pagination: PropTypes.oneOfType([PropTypes.object, PropTypes.bool]),
  onPageChange: PropTypes.func,
  onPageSizeChange: PropTypes.func,
  showActions: PropTypes.bool,
  isSuperAdmin: PropTypes.bool,
  title: PropTypes.string,
  enableBulkOperations: PropTypes.bool,
  onRefresh: PropTypes.func
};

export default SubscriptionsTable; 