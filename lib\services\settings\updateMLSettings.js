/**
 * Update ML Settings Service
 * 
 * Updates ML (Machine Learning) configuration settings that admin users can manage
 * UPDATED: Admin users can now control ML features for their mobile app usage
 */
import settingsAPI from '../../api/modules/settings';
import { handleError, handleSuccess } from '../baseService';

/**
 * Update ML settings that admin users can manage
 * @param {Object} mlSettings - ML settings to update
 * @param {boolean} mlSettings.mlEnabled - Enable/disable ML engine
 * @param {boolean} mlSettings.autoTriggerOnDue - Auto trigger ML when payment is due
 * @param {boolean} mlSettings.autoTriggerOnPaymentUpdate - Auto trigger ML on payment updates
 * @param {boolean} mlSettings.storeRiskScoreInDB - Store risk scores in database
 * @returns {Object} API response data
 */
const updateMLSettings = async (mlSettings) => {
  try {
    const updatePromises = [];
    
    // Update individual ML settings using the settingByKey endpoint
    if (mlSettings.mlEnabled !== undefined) {
      updatePromises.push(
        settingsAPI.updateSettingByKey('ml_enabled', mlSettings.mlEnabled)
      );
    }
    
    if (mlSettings.autoTriggerOnDue !== undefined) {
      updatePromises.push(
        settingsAPI.updateSettingByKey('ml_auto_trigger_on_due', mlSettings.autoTriggerOnDue)
      );
    }
    
    if (mlSettings.autoTriggerOnPaymentUpdate !== undefined) {
      updatePromises.push(
        settingsAPI.updateSettingByKey('ml_auto_trigger_on_payment_update', mlSettings.autoTriggerOnPaymentUpdate)
      );
    }
    
    if (mlSettings.storeRiskScoreInDB !== undefined) {
      updatePromises.push(
        settingsAPI.updateSettingByKey('store_risk_score_in_db', mlSettings.storeRiskScoreInDB)
      );
    }
    
    // Execute all updates in parallel
    const results = await Promise.all(updatePromises);
    
    handleSuccess('ML settings updated successfully');
    
    return {
      success: true,
      message: 'ML settings updated successfully',
      updatedSettings: results.length
    };
  } catch (error) {
    handleError(error, 'SettingsService.updateMLSettings', true);
    return { success: false, error: error.message };
  }
};

export default updateMLSettings; 