const EmailService = require('./emailService');
const SMSService = require('./smsService');
const Notification = require('../models/notification.model');
const { logInfo, logError } = require('../utils');
const { generateCode } = require('../utils/generators/generateCode');

/**
 * Enhanced Notification Service for handling all notification operations
 * Supports multiple delivery channels: Email, SMS, Push, InApp
 * Provides comprehensive logging and retry mechanisms
 */
class NotificationService {
  
  /**
   * Create a new notification record
   * @param {Object} notificationData - Notification configuration
   * @returns {Promise<Object>} Created notification
   */
  static async createNotification(notificationData) {
    try {
      const {
        shopId,
        recipient,
        recipientType = 'user',
        recipientName,
        type,
        priority = 'medium',
        category = 'transactional',
        message,
        title,
        templateId,
        templateData = {},
        relatedEntity,
        actionUrl,
        actionLabel,
        scheduledAt,
        expiresAt,
        metadata = {},
        tags = [],
        createdBy
      } = notificationData;

      // Validate required fields
      if (!shopId || !recipient || !type || !message || !createdBy) {
        throw new Error('Missing required notification fields: shopId, recipient, type, message, createdBy');
      }

      const notification = new Notification({
        shopId,
        recipient,
        recipientType,
        recipientName,
        type,
        priority,
        category,
        message,
        title,
        templateId,
        templateData,
        relatedEntity,
        actionUrl,
        actionLabel,
        scheduledAt,
        expiresAt,
        metadata,
        tags,
        createdBy
      });

      await notification.save();
      logInfo(`Notification created: ${notification.notificationId}`, 'NotificationService');
      
      return notification;
    } catch (error) {
      logError(`Failed to create notification: ${error.message}`, 'NotificationService', error);
      throw error;
    }
  }

  /**
   * Process and send a notification
   * @param {string} notificationId - Notification ID to process
   * @returns {Promise<boolean>} Success status
   */
  static async processNotification(notificationId) {
    try {
      const notification = await Notification.findOne({ 
        notificationId, 
        isDeleted: false 
      });

      if (!notification) {
        throw new Error(`Notification not found: ${notificationId}`);
      }

      // Check if notification is expired
      if (notification.isExpired) {
        await notification.markExpired();
        logInfo(`Notification expired: ${notificationId}`, 'NotificationService');
        return false;
      }

      // Check if notification is scheduled for future
      if (notification.isScheduled) {
        logInfo(`Notification scheduled for later: ${notificationId}`, 'NotificationService');
        return false;
      }

      // Mark as processing
      notification.status = 'processing';
      await notification.save();

      let deliveryResult = false;

      // Route to appropriate delivery method
      switch (notification.type) {
        case 'Email':
          deliveryResult = await this.sendEmailNotification(notification);
          break;
        case 'SMS':
          deliveryResult = await this.sendSMSNotification(notification);
          break;
        case 'Push':
          deliveryResult = await this.sendPushNotification(notification);
          break;
        case 'InApp':
          deliveryResult = await this.sendInAppNotification(notification);
          break;
        case 'WhatsApp':
          deliveryResult = await this.sendWhatsAppNotification(notification);
          break;
        default:
          throw new Error(`Unsupported notification type: ${notification.type}`);
      }

      return deliveryResult;
    } catch (error) {
      logError(`Failed to process notification ${notificationId}: ${error.message}`, 'NotificationService', error);
      
      // Mark notification as failed if it exists
      try {
        const notification = await Notification.findOne({ notificationId, isDeleted: false });
        if (notification) {
          await notification.incrementAttempts(error.message, 'PROCESSING_ERROR');
        }
      } catch (updateError) {
        logError(`Failed to update notification status: ${updateError.message}`, 'NotificationService');
      }
      
      return false;
    }
  }

  /**
   * Send email notification
   * @param {Object} notification - Notification document
   * @returns {Promise<boolean>} Success status
   */
  static async sendEmailNotification(notification) {
    try {
      logInfo(`Sending email notification: ${notification.notificationId}`, 'NotificationService');
      
      // Debug: Log EmailService structure
      logInfo(`Debug - EmailService type: ${typeof EmailService}`, 'NotificationService');
      logInfo(`Debug - EmailService.admin type: ${typeof EmailService.admin}`, 'NotificationService');
      if (EmailService.admin) {
        logInfo(`Debug - EmailService.admin methods: ${Object.getOwnPropertyNames(EmailService.admin)}`, 'NotificationService');
        logInfo(`Debug - sendAccountSuspensionEmail exists: ${typeof EmailService.admin.sendAccountSuspensionEmail}`, 'NotificationService');
      }
      
      // Use specific email service methods based on template type
      if (notification.templateId) {
        let emailSent = false;
        
        switch (notification.templateId) {
          case 'account_suspension':
            // Debug: Log EmailService structure
            logInfo(`Debug - EmailService type: ${typeof EmailService}`, 'NotificationService');
            logInfo(`Debug - EmailService.admin type: ${typeof EmailService.admin}`, 'NotificationService');
            if (EmailService.admin) {
              logInfo(`Debug - EmailService.admin methods: ${Object.getOwnPropertyNames(EmailService.admin)}`, 'NotificationService');
              logInfo(`Debug - sendAccountSuspensionEmail exists: ${typeof EmailService.admin.sendAccountSuspensionEmail}`, 'NotificationService');
            }
            
            await EmailService.admin.sendAccountSuspensionEmail(
              notification.recipient,
              notification.templateData
            );
            emailSent = true;
            break;
            
          case 'account_reactivation':
            await EmailService.admin.sendAccountReactivationEmail(
              notification.recipient,
              notification.templateData
            );
            emailSent = true;
            break;
            
                     default:
             // For other templates, use the general sendEmail method
             await EmailService.sendEmail({
               to: notification.recipient,
               subject: notification.title || 'DeynCare Notification',
               template: notification.templateId,
               data: notification.templateData || {}
             });
             emailSent = true;
             break;
         }
         
         if (!emailSent) {
           throw new Error(`Unknown template type: ${notification.templateId}`);
         }
      } else {
         // Send plain email using base sendMail method
         await EmailService.baseService.sendMail(
           notification.recipient,
           notification.title || 'DeynCare Notification',
           notification.message
         );
      }

      await notification.markSent();
      logInfo(`Email notification sent successfully: ${notification.notificationId}`, 'NotificationService');
      return true;
    } catch (error) {
      logError(`Failed to send email notification: ${error.message}`, 'NotificationService', error);
      await notification.incrementAttempts(error.message, 'EMAIL_DELIVERY_FAILED');
      return false;
    }
  }

  /**
   * Send SMS notification
   * @param {Object} notification - Notification document
   * @returns {Promise<boolean>} Success status
   */
  static async sendSMSNotification(notification) {
    try {
      logInfo(`Sending SMS notification: ${notification.notificationId}`, 'NotificationService');
      
      const result = await SMSService.sendSMS(
        notification.recipient,
        notification.message,
        notification.shopId
      );

      if (result.success) {
        await notification.markSent(result.messageId, result.cost);
        logInfo(`SMS notification sent successfully: ${notification.notificationId}`, 'NotificationService');
        return true;
      } else {
        throw new Error(result.error || 'SMS delivery failed');
      }
    } catch (error) {
      logError(`Failed to send SMS notification: ${error.message}`, 'NotificationService', error);
      await notification.incrementAttempts(error.message, 'SMS_DELIVERY_FAILED');
      return false;
    }
  }

  /**
   * Send push notification to Admin/Shop Owner mobile app users
   * @param {Object} notification - Notification document
   * @returns {Promise<boolean>} Success status
   */
  static async sendPushNotification(notification) {
    try {
      logInfo(`Sending push notification: ${notification.notificationId}`, 'NotificationService');
      
      const FirebaseService = require('./firebaseService');
      
      // Prepare notification data for Firebase
      const pushData = {
        title: notification.title || 'DeynCare Notification',
        message: notification.message,
        data: {
          notificationId: notification.notificationId,
          type: notification.type,
          category: notification.category,
          shopId: notification.shopId,
          ...(notification.relatedEntity && { relatedEntity: JSON.stringify(notification.relatedEntity) }),
          ...(notification.actionUrl && { actionUrl: notification.actionUrl }),
          ...(notification.actionLabel && { actionLabel: notification.actionLabel })
        },
        priority: notification.priority === 'high' ? 'high' : 'normal'
      };

      let result;

      // Route notification based on recipient type and shop context
      if (notification.shopId && notification.shopId !== 'SYSTEM') {
        // Send to specific shop owners/admins
        result = await FirebaseService.sendToShopOwners([notification.shopId], pushData);
      } else {
        // System-wide notification - broadcast to all admins
        result = await FirebaseService.sendBroadcastToAdmins(pushData);
      }

      if (result.success) {
        // Store delivery details in metadata and mark as sent
        notification.metadata = {
          ...notification.metadata,
          deliveryDetails: {
            totalSent: result.totalSent,
            totalFailed: result.totalFailed,
            totalRecipients: result.totalRecipients,
            provider: 'Firebase FCM'
          }
        };
        
        // Set provider name
        notification.provider = notification.provider || {};
        notification.provider.name = 'Firebase FCM';
        
        // Mark as sent with Firebase message ID (if available) and cost
        await notification.markSent(
          result.messageId || `FCM-${notification.notificationId}`, // providerId
          result.cost || 0 // cost
        );
        
        logInfo(`Push notification sent successfully: ${notification.notificationId} (${result.totalSent}/${result.totalRecipients} devices)`, 'NotificationService');
        return true;
      } else {
        throw new Error(result.error || 'Push notification delivery failed');
      }
    } catch (error) {
      logError(`Failed to send push notification: ${error.message}`, 'NotificationService', error);
      await notification.incrementAttempts(error.message, 'PUSH_DELIVERY_FAILED');
      return false;
    }
  }

  /**
   * Send in-app notification
   * @param {Object} notification - Notification document
   * @returns {Promise<boolean>} Success status
   */
  static async sendInAppNotification(notification) {
    try {
      logInfo(`Creating in-app notification: ${notification.notificationId}`, 'NotificationService');
      
      // In-app notifications are just stored in database for retrieval
      await notification.markSent();
      logInfo(`In-app notification created successfully: ${notification.notificationId}`, 'NotificationService');
      return true;
    } catch (error) {
      logError(`Failed to create in-app notification: ${error.message}`, 'NotificationService', error);
      await notification.incrementAttempts(error.message, 'INAPP_CREATION_FAILED');
      return false;
    }
  }

  /**
   * Send WhatsApp notification
   * @param {Object} notification - Notification document
   * @returns {Promise<boolean>} Success status
   */
  static async sendWhatsAppNotification(notification) {
    try {
      logInfo(`Sending WhatsApp notification: ${notification.notificationId}`, 'NotificationService');
      
      // TODO: Implement WhatsApp Business API integration
      // For now, fallback to SMS if WhatsApp is not available
      return await this.sendSMSNotification(notification);
    } catch (error) {
      logError(`Failed to send WhatsApp notification: ${error.message}`, 'NotificationService', error);
      await notification.incrementAttempts(error.message, 'WHATSAPP_DELIVERY_FAILED');
      return false;
    }
  }

  /**
   * Legacy method: Send user status change notification
   * Maintains backward compatibility while using new notification system
   */
  static async sendStatusChangeNotification(user, status, reason, createdBy = 'system') {
    const logPrefix = `[NotificationService:${status}:${user.email}]`;
    
    try {
      logInfo(`${logPrefix} Processing status change notification to ${status}`, 'NotificationService');
      
      let notificationData = {
        shopId: user.shopId || 'SYSTEM',
        recipient: user.email,
        recipientType: 'user',
        recipientName: user.fullName,
        type: 'Email',
        priority: 'high',
        category: 'system',
        createdBy: createdBy,
        relatedEntity: {
          type: 'user',
          id: user._id || user.userId
        }
      };
      
      if (status === 'suspended') {
        notificationData.title = 'Account Suspended';
        notificationData.message = `Your account has been suspended. Reason: ${reason}`;
        notificationData.templateId = 'account_suspension';
        notificationData.templateData = {
          name: user.fullName,
          reason: reason,
          contactEmail: '<EMAIL>'
        };
      } else if (status === 'active') {
        notificationData.title = 'Account Reactivated';
        notificationData.message = 'Your account has been reactivated and is now active.';
        notificationData.templateId = 'account_reactivation';
        notificationData.templateData = {
          name: user.fullName || 'User',
          contactEmail: '<EMAIL>'
        };
      } else {
        // Generic status change
        notificationData.title = 'Account Status Changed';
        notificationData.message = `Your account status has been changed to: ${status}`;
      }

      // Create and process notification
      const notification = await this.createNotification(notificationData);
      const success = await this.processNotification(notification.notificationId);
      
      logInfo(`${logPrefix} Status change notification processed: ${success}`, 'NotificationService');
      return success;
    } catch (error) {
      logError(`${logPrefix} Failed to send status change notification: ${error.message}`, 'NotificationService', error);
      return false;
    }
  }

  /**
   * Process pending notifications in queue
   * @param {number} batchSize - Number of notifications to process
   * @returns {Promise<Object>} Processing results
   */
  static async processPendingNotifications(batchSize = 50) {
    try {
      const pendingNotifications = await Notification.findPendingForProcessing(batchSize);
      logInfo(`Processing ${pendingNotifications.length} pending notifications`, 'NotificationService');
      
      const results = {
        processed: 0,
        successful: 0,
        failed: 0,
        errors: []
      };

      for (const notification of pendingNotifications) {
        try {
          const success = await this.processNotification(notification.notificationId);
          results.processed++;
          if (success) {
            results.successful++;
          } else {
            results.failed++;
          }
        } catch (error) {
          results.failed++;
          results.errors.push({
            notificationId: notification.notificationId,
            error: error.message
          });
        }
      }

      logInfo(`Notification processing complete: ${JSON.stringify(results)}`, 'NotificationService');
      return results;
    } catch (error) {
      logError(`Failed to process pending notifications: ${error.message}`, 'NotificationService', error);
      throw error;
    }
  }

  /**
   * Clean up expired notifications
   * @returns {Promise<number>} Number of expired notifications cleaned up
   */
  static async cleanupExpiredNotifications() {
    try {
      const expiredNotifications = await Notification.findExpiredNotifications();
      let cleanedUp = 0;

      for (const notification of expiredNotifications) {
        await notification.markExpired();
        cleanedUp++;
      }

      logInfo(`Cleaned up ${cleanedUp} expired notifications`, 'NotificationService');
      return cleanedUp;
    } catch (error) {
      logError(`Failed to cleanup expired notifications: ${error.message}`, 'NotificationService', error);
      throw error;
    }
  }

  /**
   * Get notification statistics
   * @param {string} shopId - Shop ID (optional)
   * @param {number} days - Number of days to analyze
   * @returns {Promise<Object>} Notification statistics
   */
  static async getNotificationStats(shopId = null, days = 30) {
    try {
      const stats = await Notification.getNotificationStats(shopId, days);
      const rawStats = stats[0] || {
        totalNotifications: 0,
        totalCost: 0,
        byStatus: []
      };

      // Transform data to match frontend expectations
      let totalSent = 0;
      let totalDelivered = 0;
      let totalFailed = 0;
      let totalPending = 0;

      // Process byStatus array to calculate totals
      if (rawStats.byStatus && Array.isArray(rawStats.byStatus)) {
        rawStats.byStatus.forEach(statusGroup => {
          const count = statusGroup.count || 0;
          
          switch (statusGroup.status) {
            case 'sent':
              totalSent += count;
              break;
            case 'delivered':
              totalDelivered += count;
              break;
            case 'failed':
              totalFailed += count;
              break;
            case 'pending':
              totalPending += count;
              break;
          }
        });
      }

      // Calculate delivery rate
      const totalAttempted = totalSent + totalDelivered + totalFailed;
      const deliveryRate = totalAttempted > 0 ? ((totalSent + totalDelivered) / totalAttempted) * 100 : 0;

      // Return frontend-compatible format
      return {
        stats: {
          totalSent: totalSent,
          totalDelivered: totalDelivered,
          totalFailed: totalFailed,
          totalPending: totalPending,
          deliveryRate: Math.round(deliveryRate * 10) / 10, // Round to 1 decimal
          totalNotifications: rawStats.totalNotifications,
          totalCost: rawStats.totalCost || 0
        },
        details: rawStats.byStatus || [],
        summary: {
          totalAttempted: totalAttempted,
          successRate: totalAttempted > 0 ? ((totalSent + totalDelivered) / totalAttempted) * 100 : 0,
          failureRate: totalAttempted > 0 ? (totalFailed / totalAttempted) * 100 : 0
        }
      };
    } catch (error) {
      logError(`Failed to get notification stats: ${error.message}`, 'NotificationService', error);
      throw error;
    }
  }
}

module.exports = NotificationService;
