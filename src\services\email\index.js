/**
 * Email Services Index - Main entry point for all email service functionality
 * 
 * This module exports specialized email services organized by functional category.
 * Each service extends the BaseEmailService and handles specific email types.
 * Uses singleton pattern to prevent multiple initializations.
 */

const BaseEmailService = require('./baseEmailService');

// Create singleton instance of base service
const baseEmailService = new BaseEmailService();

// Import specialized services (they will use the same base instance)
const adminEmailService = require('./adminEmailService');
const authEmailService = require('./authEmailService');
const shopEmailService = require('./shopEmailService');
const subscriptionEmailService = require('./subscriptionEmailService');
const reportEmailService = require('./reportEmailService');

/**
 * EmailService aggregates all specialized email services
 * and provides a unified interface for sending emails.
 */
const EmailService = {
  // Base email functionality (singleton instance)
  baseService: baseEmailService,
  
  // Admin emails (account management, suspensions, etc.)
  admin: adminEmailService,
  
  // Authentication emails (verification, password reset, etc.)
  auth: authEmailService,
  
  // Shop emails (shop activation, payment confirmations, etc.)
  shop: shopEmailService,
  
  // Subscription emails (trial ending, renewal, cancellation, etc.)
  subscription: subscriptionEmailService,
  
  // Report emails (scheduled reports, exports, etc.)
  report: reportEmailService,
  
  /**
   * Verify connection to email server
   * @returns {Promise<boolean>} Connection status
   */
  verifyConnection: async () => {
    return await baseEmailService.verifyConnection();
  },
  
  /**
   * Send a general email with a custom template
   * @param {Object} options - Email options
   * @returns {Promise<boolean>} Success status
   */
  sendEmail: async (options) => {
    return await baseEmailService.sendEmail(options);
  },

  /**
   * Initialize all email services
   * @returns {Promise<void>}
   */
  initialize: async () => {
    return await baseEmailService.initialize();
  },

  /**
   * Send payment approval notification
   * @param {Object} data - Payment approval data
   * @returns {Promise<boolean>} Success status
   */
  sendPaymentApprovalNotification: async (data) => {
    try {
      const { customerName, paymentId, amount, shopName, approvalNotes } = data;
      
      const emailOptions = {
        to: process.env.ADMIN_EMAIL || '<EMAIL>',
        subject: `Payment Approved - ${paymentId}`,
        template: 'payment-approval',
        context: {
          customerName,
          paymentId,
          amount: `$${amount.toFixed(2)}`,
          shopName,
          approvalNotes: approvalNotes || 'No additional notes provided',
          approvalDate: new Date().toLocaleDateString(),
          approvalTime: new Date().toLocaleTimeString()
        }
      };

      return await baseEmailService.sendEmail(emailOptions);
    } catch (error) {
      console.error('Error sending payment approval notification:', error);
      return false;
    }
  },

  /**
   * Send payment rejection notification
   * @param {Object} data - Payment rejection data
   * @returns {Promise<boolean>} Success status
   */
  sendPaymentRejectionNotification: async (data) => {
    try {
      const { customerName, paymentId, amount, shopName, rejectionReason, rejectionNotes } = data;
      
      const emailOptions = {
        to: process.env.ADMIN_EMAIL || '<EMAIL>',
        subject: `Payment Rejected - ${paymentId}`,
        template: 'payment-rejection',
        context: {
          customerName,
          paymentId,
          amount: `$${amount.toFixed(2)}`,
          shopName,
          rejectionReason,
          rejectionNotes: rejectionNotes || 'No additional notes provided',
          rejectionDate: new Date().toLocaleDateString(),
          rejectionTime: new Date().toLocaleTimeString()
        }
      };

      return await baseEmailService.sendEmail(emailOptions);
    } catch (error) {
      console.error('Error sending payment rejection notification:', error);
      return false;
    }
  },

  /**
   * Send payment status update notification to customer
   * @param {Object} data - Payment status data
   * @returns {Promise<boolean>} Success status
   */
  sendPaymentStatusUpdateNotification: async (data) => {
    try {
      const { customerEmail, customerName, paymentId, amount, shopName, status, notes } = data;
      
      const emailOptions = {
        to: customerEmail,
        subject: `Payment Status Update - ${paymentId}`,
        template: 'payment-status-update',
        context: {
          customerName,
          paymentId,
          amount: `$${amount.toFixed(2)}`,
          shopName,
          status: status.charAt(0).toUpperCase() + status.slice(1),
          notes: notes || 'No additional notes provided',
          updateDate: new Date().toLocaleDateString(),
          updateTime: new Date().toLocaleTimeString()
        }
      };

      return await baseEmailService.sendEmail(emailOptions);
    } catch (error) {
      console.error('Error sending payment status update notification:', error);
      return false;
    }
  }
};

module.exports = EmailService;
