const { Customer, Debt } = require('../../models');
const { AppError, logError } = require('../../utils');

/**
 * Get All Customers for Shop with Risk Profiles
 * GET /api/customers
 */
const getAllCustomers = async (req, res, next) => {
  try {
    const shopId = req.user.shopId;
    
    // Query parameters for filtering and pagination
    const {
      page = 1,
      limit = 20,
      search,
      riskLevel,
      customerType,
      sortBy = 'createdAt',
      sortOrder = 'desc',
      // NEW: Date filtering parameters
      month,
      year,
      startDate,
      endDate,
      dateRange = 'all'
    } = req.query;

    // Build filter criteria
    const filter = { shopId, isDeleted: false };

    // Search by name or phone
    if (search) {
      filter.$or = [
        { CustomerName: { $regex: search, $options: 'i' } },
        { phone: { $regex: search, $options: 'i' } }
      ];
    }

    // Filter by risk level
    if (riskLevel) {
      filter['riskProfile.currentRiskLevel'] = riskLevel;
    }

    // Filter by customer type
    if (customerType) {
      filter.CustomerType = customerType;
    }

    // NEW: Build date filter
    let dateFilter = {};
    if (dateRange !== 'all') {
      if (month && year) {
        // Monthly filter: ?month=07&year=2025
        const startOfMonth = new Date(year, month - 1, 1);
        const endOfMonth = new Date(year, month, 0, 23, 59, 59);
        dateFilter.createdAt = { $gte: startOfMonth, $lte: endOfMonth };
      } else if (startDate && endDate) {
        // Custom range: ?startDate=2025-01-01&endDate=2025-01-31
        dateFilter.createdAt = { 
          $gte: new Date(startDate), 
          $lte: new Date(endDate) 
        };
      } else if (dateRange === 'daily') {
        // Today only
        const today = new Date();
        const startOfDay = new Date(today.setHours(0, 0, 0, 0));
        const endOfDay = new Date(today.setHours(23, 59, 59, 999));
        dateFilter.createdAt = { $gte: startOfDay, $lte: endOfDay };
      } else if (dateRange === 'weekly') {
        // This week
        const today = new Date();
        const startOfWeek = new Date(today.setDate(today.getDate() - today.getDay()));
        startOfWeek.setHours(0, 0, 0, 0);
        const endOfWeek = new Date(startOfWeek);
        endOfWeek.setDate(startOfWeek.getDate() + 6);
        endOfWeek.setHours(23, 59, 59, 999);
        dateFilter.createdAt = { $gte: startOfWeek, $lte: endOfWeek };
      }
    }

    // Add date filter to existing filter
    const finalFilter = {
      ...filter,
      ...dateFilter
    };

    // Sort options
    const sortOptions = {};
    sortOptions[sortBy] = sortOrder === 'desc' ? -1 : 1;

    // Pagination
    const skip = (parseInt(page) - 1) * parseInt(limit);
    const limitNum = parseInt(limit);

    // Get customers with aggregation for additional statistics
    const customers = await Customer.aggregate([
      { $match: finalFilter },
      {
        $lookup: {
          from: 'debts',
          localField: 'customerId',
          foreignField: 'customerId',
          as: 'debts',
          pipeline: [
            { $match: { isDeleted: false } }
          ]
        }
      },
      {
        $addFields: {
          totalDebts: { $size: '$debts' },
          activeDebts: {
            $size: {
              $filter: {
                input: '$debts',
                cond: { $gt: ['$$this.OutstandingDebt', 0] }
              }
            }
          },
          totalDebtAmount: { $sum: '$debts.DebtAmount' },
          totalOutstanding: { $sum: '$debts.OutstandingDebt' },
          totalPaid: { $sum: '$debts.PaidAmount' },
          averagePaymentRatio: { $avg: '$debts.DebtPaidRatio' },
          hasOverdueDebts: {
            $anyElementTrue: {
              $map: {
                input: '$debts',
                as: 'debt',
                in: {
                  $and: [
                    { $lt: ['$$debt.DueDate', new Date()] },
                    { $gt: ['$$debt.OutstandingDebt', 0] }
                  ]
                }
              }
            }
          }
        }
      },
      { $sort: sortOptions },
      { $skip: skip },
      { $limit: limitNum },
      {
        $project: {
          debts: 0 // Remove the debts array to reduce response size
        }
      }
    ]);

    // Get total count for pagination
    const totalCustomers = await Customer.countDocuments(finalFilter);
    const totalPages = Math.ceil(totalCustomers / limitNum);

    // Format response
    const formattedCustomers = customers.map(customer => ({
      customerId: customer.customerId,
      customerName: customer.CustomerName,
      phone: customer.phone,
      customerType: customer.CustomerType,
      
      // Risk profile
      riskProfile: {
        currentRiskLevel: customer.riskProfile?.currentRiskLevel || 'Not Assessed',
        riskScore: customer.riskProfile?.riskScore || 0,
        lastAssessment: customer.riskProfile?.lastAssessment,
        assessmentCount: customer.riskProfile?.assessmentCount || 0
      },
      
      // Financial summary
      financialSummary: {
        totalDebts: customer.totalDebts || 0,
        activeDebts: customer.activeDebts || 0,
        totalDebtAmount: customer.totalDebtAmount || 0,
        totalOutstanding: customer.totalOutstanding || 0,
        totalPaid: customer.totalPaid || 0,
        paymentRatio: Math.round((customer.averagePaymentRatio || 0) * 100),
        hasOverdueDebts: customer.hasOverdueDebts || false
      },
      
      // System fields
      createdAt: customer.createdAt,
      updatedAt: customer.updatedAt
    }));

    // Risk distribution summary for the current filter
    const riskSummary = await Customer.aggregate([
      { $match: finalFilter },
      {
        $group: {
          _id: '$riskProfile.currentRiskLevel',
          count: { $sum: 1 }
        }
      }
    ]);

    const riskDistribution = riskSummary.reduce((acc, item) => {
      const riskLevel = item._id || 'Not Assessed';
      acc[riskLevel] = item.count;
      return acc;
    }, {});

    // NEW: Add period description to response
    const getPeriodDescription = () => {
      if (dateRange === 'all') return 'All Time';
      if (dateRange === 'daily') return 'Today';
      if (dateRange === 'weekly') return 'This Week';
      if (month && year) {
        return `${new Date(year, month - 1).toLocaleString('default', { month: 'long' })} ${year}`;
      }
      if (startDate && endDate) {
        return `${new Date(startDate).toLocaleDateString()} - ${new Date(endDate).toLocaleDateString()}`;
      }
      return 'Custom Period';
    };

    // NEW: Enhanced summary with period info
    const summary = {
      totalCustomers,
      riskDistribution,
      searchCriteria: {
        search: search || null,
        riskLevel: riskLevel || null,
        customerType: customerType || null,
        dateRange: dateRange || 'all',
        period: getPeriodDescription()
      },
      reportMetadata: {
        generatedAt: new Date().toISOString(),
        dateFilter: dateFilter,
        totalInPeriod: totalCustomers,
        periodDescription: getPeriodDescription()
      }
    };

    res.json({
      success: true,
      message: 'Customers retrieved successfully',
      data: {
        customers: formattedCustomers,
        pagination: {
          currentPage: parseInt(page),
          totalPages,
          totalCustomers,
          hasNextPage: parseInt(page) < totalPages,
          hasPrevPage: parseInt(page) > 1
        },
        summary: summary
      }
    });

  } catch (error) {
    logError('Failed to get customers', 'GetAllCustomers', error);
    return next(new AppError('Failed to retrieve customers', 500));
  }
};

module.exports = getAllCustomers; 
