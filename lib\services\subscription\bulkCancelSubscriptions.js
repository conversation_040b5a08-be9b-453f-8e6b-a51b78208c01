import apiBridge from '@/lib/api/bridge';
import { ENDPOINTS } from '@/lib/api/contract';
import BaseService from '../baseService';

const bulkCancelSubscriptions = async (subscriptionIds, cancellationData) => {
  try {
    const response = await apiBridge.post(ENDPOINTS.subscription.bulkCancel, {
      subscriptionIds,
      ...cancellationData
    });
    
    return BaseService.processApiResponse(response);
  } catch (error) {
    throw BaseService.handleApiError(error);
  }
};

export default bulkCancelSubscriptions; 
