/**
 * Deactivate Expired Subscription Service
 * Deactivates a subscription that has expired
 */
const { Subscription } = require('../../models');
const { logError, logSuccess } = require('../../utils');

/**
 * Deactivate an expired subscription
 * @param {String} subscriptionId - The subscription ID to deactivate
 * @param {Object} actor - Actor performing the action
 * @returns {Promise<Object>} Updated subscription
 */
const deactivateExpiredSubscription = async (subscriptionId, actor = {}) => {
  try {
    const { actorId = 'system', actorRole = 'system', reason = 'Subscription expired' } = actor;
    
    // Find the subscription
    const subscription = await Subscription.findById(subscriptionId);
    
    if (!subscription) {
      throw new Error(`Subscription not found: ${subscriptionId}`);
    }
    
    // Check if already expired status
    if (subscription.status === 'expired') {
      logSuccess(`Subscription ${subscriptionId} already marked as expired`, 'SubscriptionService');
      return subscription;
    }
    
    // Update subscription status and add audit trail
    const updatedSubscription = await Subscription.findByIdAndUpdate(
      subscriptionId,
      {
        $set: {
          status: 'expired',
          'meta.lastModifiedBy': actorId,
          'meta.lastModifiedAt': new Date(),
          'meta.deactivationReason': reason,
          'access.isActive': false,
          'access.restrictedAt': new Date(),
          'access.restrictedBy': actorId
        },
        $push: {
          'meta.statusHistory': {
            status: 'expired',
            changedAt: new Date(),
            changedBy: actorId,
            reason: reason
          }
        }
      },
      { new: true, runValidators: true }
    );
    
    logSuccess(`Successfully deactivated expired subscription: ${subscriptionId}`, 'SubscriptionService');
    
    return updatedSubscription;
  } catch (error) {
    logError('Failed to deactivate expired subscription', 'SubscriptionService', error);
    throw error;
  }
};

module.exports = deactivateExpiredSubscription; 