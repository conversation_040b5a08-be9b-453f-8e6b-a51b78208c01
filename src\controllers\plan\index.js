/**
 * Plan Controller
 * Handles all plan management operations
 */
const getAllPlans = require('./getAllPlans');
const getPlanById = require('./getPlanById');
const createPlan = require('./createPlan');
const updatePlan = require('./updatePlan');
const deletePlan = require('./deletePlan');
const getPlanStats = require('./getPlanStats');
const togglePlanStatus = require('./togglePlanStatus');
const { updatePlanFeatures, getPlanFeatures, getAllFeatures } = require('./updatePlanFeatures');

module.exports = {
  getAllPlans,
  getPlanById,
  createPlan,
  updatePlan,
  deletePlan,
  getPlanStats,
  togglePlanStatus,
  updatePlanFeatures,
  getPlanFeatures,
  getAllFeatures
};
