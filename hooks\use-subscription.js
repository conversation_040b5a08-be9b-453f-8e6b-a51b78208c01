/**
 * Custom hook for subscription operations
 * Provides state management and API integration for subscription features
 */

import { useState, useEffect, useCallback } from 'react';
import { useAuth } from '@/contexts/auth-context';
import SubscriptionService from '@/lib/services/subscription';
import apiBridge from '@/lib/api/bridge';
import { toast } from 'sonner';

export const useSubscription = () => {
  const { user } = useAuth();
  const [loading, setLoading] = useState(false);
  const [subscriptions, setSubscriptions] = useState([]);
  const [currentSubscription, setCurrentSubscription] = useState(null);
  const [subscriptionStats, setSubscriptionStats] = useState(null);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
    total: 0,
    totalPages: 0
  });

  const isSuperAdmin = user?.role === 'superAdmin';

  /**
   * Fetch current subscription
   */
  const fetchCurrentSubscription = useCallback(async () => {
    try {
      setLoading(true);
      const subscription = await SubscriptionService.getCurrentSubscription();
      setCurrentSubscription(subscription);
      return subscription;
    } catch (error) {
      console.error('Error fetching current subscription:', error);
      toast.error('Failed to load current subscription');
      return null;
    } finally {
      setLoading(false);
    }
  }, []);

  /**
   * Fetch all subscriptions (SuperAdmin only)
   */
  const fetchSubscriptions = useCallback(async (params = {}) => {
    if (!isSuperAdmin || loading) return null; // Prevent double fetch if already loading
    
    try {
      setLoading(true);
      
      // FIXED: Merge params with current pagination state
      const currentPagination = pagination || { page: 1, limit: 10 };
      const requestParams = {
        page: params.page || currentPagination.page || 1,
        limit: params.limit || currentPagination.limit || 10,
        ...params
      };
      
      const result = await SubscriptionService.getAllSubscriptions(requestParams);
      
      if (result) {
        setSubscriptions(result.subscriptions || []);
        // FIXED: Update pagination state properly
        setPagination({
          page: result.pagination?.currentPage || requestParams.page,
          limit: result.pagination?.itemsPerPage || requestParams.limit,
          total: result.pagination?.totalItems || 0,
          totalPages: result.pagination?.totalPages || 1,
          hasNextPage: result.pagination?.hasNextPage || false,
          hasPrevPage: result.pagination?.hasPrevPage || false
        });
      }
      
      return result;
    } catch (error) {
      console.error('Error fetching subscriptions:', error);
      toast.error('Failed to load subscriptions');
      return null;
    } finally {
      setLoading(false);
    }
  }, [isSuperAdmin, pagination, loading]);

  /**
   * Fetch subscription statistics (SuperAdmin only)
   */
  const fetchSubscriptionStats = useCallback(async () => {
    if (!isSuperAdmin) return null;
    
    try {
      setLoading(true);
      const stats = await SubscriptionService.getSubscriptionStats();
      setSubscriptionStats(stats);
      return stats;
    } catch (error) {
      console.error('Error fetching subscription stats:', error);
      toast.error('Failed to load subscription statistics');
      return null;
    } finally {
      setLoading(false);
    }
  }, [isSuperAdmin]);

  /**
   * Upgrade subscription from trial
   */
  const upgradeFromTrial = useCallback(async (upgradeData) => {
    try {
      setLoading(true);
      const result = await SubscriptionService.upgradeFromTrial(upgradeData);
      
      if (result) {
        toast.success('Subscription upgraded successfully');
        // Refresh current subscription
        await fetchCurrentSubscription();
      }
      
      return result;
    } catch (error) {
      console.error('Error upgrading subscription:', error);
      return null;
    } finally {
      setLoading(false);
    }
  }, [fetchCurrentSubscription]);

  /**
   * Change subscription plan
   */
  const changeSubscriptionPlan = useCallback(async (planData) => {
    try {
      setLoading(true);
      const result = await SubscriptionService.changeSubscriptionPlan(planData);
      
      if (result) {
        toast.success('Subscription plan changed successfully');
        // Refresh current subscription
        await fetchCurrentSubscription();
      }
      
      return result;
    } catch (error) {
      console.error('Error changing subscription plan:', error);
      return null;
    } finally {
      setLoading(false);
    }
  }, [fetchCurrentSubscription]);

  /**
   * Cancel subscription
   */
  const cancelSubscription = useCallback(async (cancelData) => {
    try {
      setLoading(true);
      const result = await SubscriptionService.cancelSubscription(cancelData);
      
      if (result) {
        toast.success('Subscription cancelled successfully');
        // Refresh current subscription
        await fetchCurrentSubscription();
      }
      
      return result;
    } catch (error) {
      console.error('Error cancelling subscription:', error);
      return null;
    } finally {
      setLoading(false);
    }
  }, [fetchCurrentSubscription]);

  /**
   * Record payment
   */
  const recordPayment = useCallback(async (paymentData) => {
    try {
      setLoading(true);
      const result = await SubscriptionService.recordPayment(paymentData);
      
      if (result) {
        toast.success('Payment recorded successfully');
        // Refresh current subscription
        await fetchCurrentSubscription();
      }
      
      return result;
    } catch (error) {
      console.error('Error recording payment:', error);
      return null;
    } finally {
      setLoading(false);
    }
  }, [fetchCurrentSubscription]);

  /**
   * Update auto-renewal setting
   */
  const updateAutoRenewal = useCallback(async (autoRenew) => {
    try {
      setLoading(true);
      const result = await SubscriptionService.updateAutoRenewal({ autoRenew });
      
      if (result) {
        toast.success(`Auto-renewal ${autoRenew ? 'enabled' : 'disabled'} successfully`);
        // Refresh current subscription
        await fetchCurrentSubscription();
      }
      
      return result;
    } catch (error) {
      console.error('Error updating auto-renewal:', error);
      return null;
    } finally {
      setLoading(false);
    }
  }, [fetchCurrentSubscription]);

  /**
   * Extend subscription
   */
  const extendSubscription = useCallback(async (subscriptionId, extensionData) => {
    try {
      setLoading(true);
      const result = await SubscriptionService.extendSubscription(subscriptionId, extensionData);
      
      if (result) {
        toast.success(`Subscription extended by ${extensionData.days} days`);
        // Refresh subscriptions list
        await fetchSubscriptions();
        // Refresh current subscription if it's the same one
        if (currentSubscription?.id === subscriptionId) {
          await fetchCurrentSubscription();
        }
      }
      
      return result;
    } catch (error) {
      console.error('Error extending subscription:', error);
      return null;
    } finally {
      setLoading(false);
    }
  }, [fetchSubscriptions, fetchCurrentSubscription, currentSubscription]);

  /**
   * Perform bulk operations (SuperAdmin only)
   */
  const bulkUpdateSubscriptions = useCallback(async (bulkData) => {
    if (!isSuperAdmin) return null;
    
    try {
      setLoading(true);
      const result = await SubscriptionService.bulkUpdateSubscriptions(bulkData);
      
      if (result) {
        toast.success(`Bulk operation completed for ${bulkData.subscriptionIds.length} subscriptions`);
        // Refresh subscriptions list
        await fetchSubscriptions();
      }
      
      return result;
    } catch (error) {
      console.error('Error performing bulk operation:', error);
      return null;
    } finally {
      setLoading(false);
    }
  }, [isSuperAdmin, fetchSubscriptions]);

  /**
   * Trigger cron tasks (SuperAdmin only)
   */
  const triggerCronTasks = useCallback(async () => {
    if (!isSuperAdmin) return null;
    
    try {
      setLoading(true);
      const result = await SubscriptionService.triggerCronTasks();
      
      if (result) {
        toast.success('Subscription cron tasks triggered successfully');
      }
      
      return result;
    } catch (error) {
      console.error('Error triggering cron tasks:', error);
      return null;
    } finally {
      setLoading(false);
    }
  }, [isSuperAdmin]);

  // Initialize data on mount (only when user changes)
  useEffect(() => {
    if (user) {
      fetchCurrentSubscription();
      if (isSuperAdmin) {
        fetchSubscriptions();
        fetchSubscriptionStats();
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [user, isSuperAdmin]);

  return {
    // State
    loading,
    subscriptions,
    currentSubscription,
    subscriptionStats,
    pagination,
    isSuperAdmin,

    // Actions
    fetchCurrentSubscription,
    fetchSubscriptions,
    fetchSubscriptionStats,
    upgradeFromTrial,
    changeSubscriptionPlan,
    cancelSubscription,
    recordPayment,
    updateAutoRenewal,
    extendSubscription,
    bulkUpdateSubscriptions,
    triggerCronTasks,

    // Utilities
    refreshData: () => {
      // Clear cache first to ensure fresh data
      if (apiBridge?.clearCache) {
        apiBridge.clearCache('/api/subscriptions');
        apiBridge.clearCache('/api/subscriptions/stats');
      }
      
      fetchCurrentSubscription();
      if (isSuperAdmin) {
        fetchSubscriptions();
        fetchSubscriptionStats();
      }
    }
  };
}; 