# DeynCare API Documentation: Plan & Subscription Modules

---

## PLAN MODULE

**Base Path:** `/api/plans`

> **All endpoints require authentication and are SuperAdmin-only.**

### Endpoints

- **GET** `/api/plans` — Get all plans *(SuperAdmin-only)*
- **GET** `/api/plans/:planId` — Get plan by ID *(SuperAdmin-only)*
- **POST** `/api/plans` — Create plan *(SuperAdmin-only)*
- **PUT** `/api/plans/:planId` — Update plan *(SuperAdmin-only)*
- **DELETE** `/api/plans/:planId` — Delete plan *(SuperAdmin-only)*

---

## SUBSCRIPTION MODULE

**Base Path:** `/api/subscriptions`

> **Most management endpoints are SuperAdmin-only. See each endpoint for role access.**

### Endpoints

- **GET** `/api/subscriptions/current` — Get current subscription *(Authenticated user)*
- **GET** `/api/subscriptions/history` — Get subscription history *(Authenticated user)*
- **POST** `/api/subscriptions` — Create subscription *(Authenticated user)*
- **POST** `/api/subscriptions/upgrade` — Upgrade subscription *(Authenticated user)*
- **POST** `/api/subscriptions/change-plan` — Change plan *(Authenticated user)*
- **POST** `/api/subscriptions/cancel` — Cancel subscription *(Authenticated user)*
- **POST** `/api/subscriptions/payment` — Record payment *(Authenticated user)*
- **PATCH** `/api/subscriptions/auto-renewal` — Update auto-renewal *(Authenticated user)*
- **POST** `/api/subscriptions/renew` — Renew subscription *(Authenticated user)*
- **POST** `/api/subscriptions/evc_plus` — Pay with EVC Plus *(Authenticated user)*
- **POST** `/api/subscriptions/offline-payment` — Submit offline payment *(Admin, ShopOwner)*
- **POST** `/api/subscriptions/verify-payment/:paymentId` — Verify offline payment *(SuperAdmin-only)*
- **GET** `/api/subscriptions/payment-proof/:fileId` — Get payment proof file *(SuperAdmin, Admin, ShopOwner)*
- **GET** `/api/subscriptions/` — Get all subscriptions *(Admin, SuperAdmin)*
- **GET** `/api/subscriptions/analytics` — Get subscription analytics *(SuperAdmin-only)*
- **POST** `/api/subscriptions/bulk` — Bulk update subscriptions *(SuperAdmin-only)*
- **POST** `/api/subscriptions/cron/run` — Run subscription cron tasks *(SuperAdmin-only)*
- **GET** `/api/subscriptions/payment-retry/:subscriptionId/status` — Get payment retry status *(SuperAdmin-only)*
- **POST** `/api/subscriptions/payment-retry/:subscriptionId/trigger` — Trigger manual payment retry *(SuperAdmin-only)*
- **POST** `/api/subscriptions/payment-retry/:subscriptionId/cancel` — Cancel scheduled retries *(SuperAdmin-only)*
- **POST** `/api/subscriptions/payment-retry/process-all` — Process all pending payment retries *(SuperAdmin-only)*
- **GET** `/api/subscriptions/payment-retry/config` — Get payment retry config *(SuperAdmin-only)*
- **GET** `/api/subscriptions/:subscriptionId` — Get subscription by ID *(Authenticated user)*
- **POST** `/api/subscriptions/:subscriptionId/extend` — Extend subscription *(Admin, SuperAdmin)*

---

## Notes
- Only endpoints present in the router files are listed.
- Role access is based strictly on middleware in the actual codebase.
- For payload schemas and validation, refer to Joi schemas in the backend.

---

*Generated by Cascade AI — DeynCare Backend API Documentation (Plan & Subscription)*

---

## PLAN MODULE

### Base Path: `/api/plans`

#### All routes require authentication and SuperAdmin authorization.

### Endpoints

#### 1. **Get All Plans**
- **GET** `/api/plans`
- **Description:** Retrieve all plans (including inactive).
- **Payload:** None

#### 2. **Get Plan by ID**
- **GET** `/api/plans/:planId`
- **Description:** Retrieve a specific plan by its ID.
- **Params:**
  - `planId`: string (required)
- **Payload:** None

#### 3. **Create Plan**
- **POST** `/api/plans`
- **Description:** Create a new pricing plan.
- **Payload Schema:**
```json
{
  "name": "string (required, max 50)",
  "type": "trial | monthly | yearly (required)",
  "displayName": "string (required, max 100)",
  "description": "string (optional, max 500)",
  "pricing": {
    "basePrice": "number (required, min 0)",
    "currency": "string (default: 'USD', max 3)",
    "billingCycle": "one-time | monthly | yearly (required)",
    "trialDays": "number (default: 0, min 0)",
    "setupFee": "number (default: 0, min 0)"
  },
  "limits": {
    "maxProducts": "number (integer, min 1)",
    "maxEmployees": "number (integer, min 1)",
    "maxStorageMB": "number (integer, min 1)",
    "maxCustomers": "number (integer, min 1)",
    "maxDailyTransactions": "number (integer, min 1)"
  },
  "isActive": "boolean (default: true)",
  "displayOrder": "number (integer, default: 1)",
  "metadata": {
    "isRecommended": "boolean (default: false)",
    "tags": ["string"],
    "customFields": {"any": "value"}
  }
}
```

#### 4. **Update Plan**
- **PUT** `/api/plans/:planId`
- **Description:** Update an existing plan.
- **Params:**
  - `planId`: string (required)
- **Payload Schema:** (at least one field required)
```json
{
  "name": "string (max 50)",
  "displayName": "string (max 100)",
  "description": "string (max 500)",
  "pricing": {
    "basePrice": "number (min 0)",
    "currency": "string (max 3)",
    "billingCycle": "one-time | monthly | yearly",
    "trialDays": "number (min 0)",
    "setupFee": "number (min 0)"
  },
  "limits": {
    "maxProducts": "number (integer, min 1)",
    "maxEmployees": "number (integer, min 1)",
    "maxStorageMB": "number (integer, min 1)",
    "maxCustomers": "number (integer, min 1)",
    "maxDailyTransactions": "number (integer, min 1)"
  },
  "isActive": "boolean",
  "displayOrder": "number (integer)",
  "metadata": {
    "isRecommended": "boolean",
    "tags": ["string"],
    "customFields": {"any": "value"}
  }
}
```

#### 5. **Delete Plan**
- **DELETE** `/api/plans/:planId`
- **Description:** Delete a plan.
- **Params:**
  - `planId`: string (required)
- **Payload:** None

---

## SUBSCRIPTION MODULE

### Base Path: `/api/subscriptions`

#### All routes require authentication. Some require SuperAdmin or Admin authorization.

### Endpoints

#### 1. **Get Current Subscription**
- **GET** `/api/subscriptions/current`
- **Description:** Get the current subscription for the authenticated user's shop.
- **Payload:** None

#### 2. **Get Subscription History**
- **GET** `/api/subscriptions/history`
- **Description:** Get the subscription history for the authenticated user's shop.
- **Payload:** None

#### 3. **Create Subscription**
- **POST** `/api/subscriptions`
- **Description:** Create a new subscription (usually after trial).
- **Payload Schema:**
```json
{
  "shopId": "string (required)",
  "planId": "string (optional)",
  "planType": "trial | monthly | yearly (required if planId not provided)",
  "paymentMethod": "offline | evc_plus | free (required for paid plans)",
  "paymentDetails": {
    "transactionId": "string",
    "amount": "number (positive)",
    "receiptUrl": "string (uri)",
    "payerName": "string",
    "payerPhone": "string (phone)",
    "payerEmail": "string (email)",
    "notes": "string (max 500)"
  }
}
```

#### 4. **Upgrade Subscription**
- **POST** `/api/subscriptions/upgrade`
- **Description:** Upgrade from trial to paid plan.
- **Payload Schema:**
```json
{
  "planId": "string (optional)",
  "planType": "monthly | yearly (required if planId not provided)",
  "paymentMethod": "offline | evc_plus (required)",
  "paymentDetails": {
    "transactionId": "string",
    "amount": "number (positive)",
    "receiptUrl": "string (uri)",
    "payerName": "string",
    "payerPhone": "string (phone)",
    "payerEmail": "string (email)",
    "notes": "string (max 500)"
  }
}
```

#### 5. **Change Subscription Plan**
- **POST** `/api/subscriptions/change-plan`
- **Payload Schema:**
```json
{
  "planId": "string (optional)",
  "planType": "monthly | yearly (required if planId not provided)",
  "prorated": "boolean (default: true)",
  "paymentMethod": "offline | evc_plus (optional)",
  "paymentDetails": {
    "transactionId": "string",
    "amount": "number (positive)",
    "notes": "string (max 500)"
  }
}
```

#### 6. **Cancel Subscription**
- **POST** `/api/subscriptions/cancel`
- **Payload Schema:**
```json
{
  "reason": "string (max 500)",
  "feedback": "string (max 1000)",
  "immediateEffect": "boolean (default: false)"
}
```

#### 7. **Record Payment**
- **POST** `/api/subscriptions/payment`
- **Payload Schema:**
```json
{
  "amount": "number (positive, required)",
  "transactionId": "string (required)",
  "paymentMethod": "offline | evc_plus (required)",
  "currency": "string (default: 'USD')",
  "notes": "string (max 500)"
}
```

#### 8. **Update Auto-Renewal**
- **PATCH** `/api/subscriptions/auto-renewal`
- **Payload Schema:**
```json
{
  "autoRenew": "boolean (required)"
}
```

#### 9. **Renew Subscription**
- **POST** `/api/subscriptions/renew`
- **Payload Schema:**
```json
{
  "paymentMethod": "offline | evc_plus (required)",
  "transactionId": "string (required)",
  "amount": "number (positive)",
  "currency": "string (default: 'USD')",
  "notes": "string (max 500)"
}
```

#### 10. **Extend Subscription**
- **POST** `/api/subscriptions/:subscriptionId/extend`
- **Payload Schema:**
```json
{
  "days": "number (integer, positive, required)",
  "reason": "string (max 500)"
}
```

#### 11. **Pay Subscription Using EVC Plus**
- **POST** `/api/subscriptions/evc_plus`
- **Payload Schema:**
```json
{
  "amount": "number (positive, required)",
  "transactionId": "string (required)",
  "paymentMethod": "evc_plus (required)",
  "currency": "string (default: 'USD')",
  "notes": "string (max 500)"
}
```

#### 12. **Submit Offline Payment with Proof**
- **POST** `/api/subscriptions/offline-payment`
- **Authorization:** shopOwner, admin
- **Payload Schema (multipart/form-data):**
- **Fields:**
  - `paymentProof`: file (required)
  - `transactionId`: string (required)
  - `amount`: number (positive, required)
  - `payerName`: string (optional)
  - `payerPhone`: string (phone, optional)
  - `payerEmail`: string (email, optional)
  - `notes`: string (max 500, optional)

#### 13. **Verify Offline Payment**
- **POST** `/api/subscriptions/verify-payment/:paymentId`
- **Authorization:** superAdmin
- **Params:**
  - `paymentId`: string (required)
- **Payload Schema:**
```json
{
  "status": "approved | rejected (required)",
  "adminNote": "string (max 500, optional)"
}
```

---

## Notes
- All endpoints require authentication; some require SuperAdmin or Admin role.
- Payload schemas are based on Joi validation and may have additional constraints in the business logic.
- For detailed error messages and field-level validation, refer to the backend Joi schemas.

---



## Plan Stats API *(Planned)*

- **Endpoint:** `GET /api/plans/stats`
- **Role:** SuperAdmin only
- **Purpose:** Returns statistics for plans, such as:
  - Active Plans
  - Pending Plans
  - Most Used Plan
  - Total Plans
- **Status:** Not implemented yet (planned in codebase).

---

## Subscription Stats API

- **Endpoint:** `GET /api/subscriptions/stats`
- **Role:** SuperAdmin only
- **Purpose:** Returns statistics for subscriptions, such as:
  - Total Subscriptions
  - Active Subscriptions
  - Trial Subscriptions
  - Expiring Soon Subscriptions
  - Average Lifetime
  - Total Revenue
  - Revenue by Plan Type
- **Returns:** JSON with a `summary` object for building dashboard UI cards.

**How "Expiring Soon" Works:**
- `expiringSubscriptions` counts only subscriptions (trial or paid) that will expire within the next 7 days from the current date/time.
- **Expired subscriptions (those whose end date is in the past) are NOT included** in this count or in active/trial stats. This matches SaaS dashboard best practices and ensures your dashboard reflects current and upcoming activity.
- To test this, register a new trial or paid subscription with a current/future end date. As the end date approaches (within 7 days), it will appear in the `expiringSubscriptions` stat. Once expired, it will no longer be counted.

**Sample Response:**
```json
{
  "success": true,
  "summary": {
    "totalSubscriptions": 1234,
    "activeSubscriptions": 800,
    "trialSubscriptions": 120,
    "expiringSubscriptions": 50,
    "averageLifetime": 8,
    "totalRevenue": 15000,
    "revenueByPlanType": {
      "monthly": 9000,
      "yearly": 6000
    }
  }
}
```

---


*Generated by Cascade AI — DeynCare Backend API Documentation (Plan & Subscription)*
