/**
 * User Export Controller
 * Handles export requests for user management data (SuperAdmin only)
 */
const BaseExportController = require('./baseExportController');
const UserService = require('../../services/userService');
const { logError } = require('../../utils');

class UserExportController extends BaseExportController {
  /**
   * Get user export fields
   * @param {Object} req - Express request object
   * @returns {Array} Field configurations
   */
  static async getExportFields(req) {
    return [
      {
        label: 'User ID',
        key: 'userId',
        type: 'string'
      },
      {
        label: 'Full Name',
        key: 'fullName',
        type: 'string'
      },
      {
        label: 'Email',
        key: 'email',
        type: 'string'
      },
      {
        label: 'Role',
        key: 'role',
        type: 'string'
      },
      {
        label: 'Status',
        key: 'status',
        type: 'string'
      },
      {
        label: 'Shop Name',
        key: 'shopName',
        type: 'string'
      },
      {
        label: 'Phone',
        key: 'phone',
        type: 'string'
      },
      {
        label: 'Email Verified',
        key: 'emailVerified',
        type: 'boolean'
      },
      {
        label: 'Is Paid',
        key: 'isPaid',
        type: 'boolean'
      },
      {
        label: 'Is Suspended',
        key: 'isSuspended',
        type: 'boolean'
      },
      {
        label: 'Created At',
        key: 'createdAt',
        type: 'datetime'
      }
    ];
  }

  /**
   * Get user data for export
   * @param {Object} req - Express request object
   * @returns {Array} User data
   */
  static async getExportData(req) {
    try {
      const { 
        role, 
        status, 
        shopId, 
        emailVerified, 
        isPaid, 
        isSuspended,
        startDate,
        endDate
      } = req.query;
      
      const filters = {};
      if (role) filters.role = role;
      if (status) filters.status = status;
      if (shopId) filters.shopId = shopId;
      if (emailVerified !== undefined) filters.emailVerified = emailVerified === 'true';
      if (isPaid !== undefined) filters.isPaid = isPaid === 'true';
      if (isSuspended !== undefined) filters.isSuspended = isSuspended === 'true';
      
      if (startDate || endDate) {
        filters.createdAt = {};
        if (startDate) filters.createdAt.$gte = new Date(startDate);
        if (endDate) filters.createdAt.$lte = new Date(endDate);
      }

      // Get all users with populated shop names (no limit for export)
      const result = await UserService.getAllUsers(filters, 1, 999999);
      
      // Populate shop names
      const usersWithShopNames = await UserService.populateShopNames(result.docs);
      
      return usersWithShopNames;
    } catch (error) {
      logError('Failed to get user data for export', 'UserExportController', error);
      throw error;
    }
  }

  /**
   * Export users to CSV
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next middleware function
   */
  static async exportToCSV(req, res, next) {
    req.exportConfig = {
      module: 'users',
      getFields: this.getExportFields,
      getData: this.getExportData,
      filename: req.query.filename || 'user_management_export'
    };

    await this.prepareExport(req, res, next);
    return super.exportToCSV(req, res, next);
  }

  /**
   * Export users to Excel
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next middleware function
   */
  static async exportToExcel(req, res, next) {
    req.exportConfig = {
      module: 'users',
      getFields: this.getExportFields,
      getData: this.getExportData,
      filename: req.query.filename || 'user_management_export',
      options: {
        sheetName: 'User Management',
        styling: {
          header: true,
          columns: {
            'email': { width: 25 },
            'fullName': { width: 20 },
            'shopName': { width: 20 },
            'createdAt': { width: 20 }
          }
        }
      }
    };

    await this.prepareExport(req, res, next);
    return super.exportToExcel(req, res, next);
  }
}

module.exports = UserExportController; 