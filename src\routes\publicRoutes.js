const express = require('express');
const router = express.Router();
const { getAllPlans } = require('../services/PlanService');
const { logError } = require('../utils');

/**
 * Public route to fetch active subscription plans.
 * GET /api/public/plans
 */
router.get('/plans', async (req, res, next) => {
  try {
    // Fetch only active plans by passing `false` to the service
    const plans = await getAllPlans(false);
    res.status(200).json({ success: true, data: plans });
  } catch (error) {
    logError('Failed to fetch public plans', 'PublicRoutes', error);
    next(error);
  }
});

module.exports = router;
