import { useState, useCallback, useEffect, useRef } from 'react';
import { toast } from 'sonner';
import SuperAdminShopService from '@/lib/services/shop';

/**
 * SuperAdmin Shop Management Hook
 * 
 * Optimized for SuperAdmin operations with:
 * - Zero redundancy
 * - No duplicate API calls
 * - Aligned with SuperAdmin API structure
 * - Clean state management
 * - Proper error handling
 */
export function useShopManagement(options = {}) {
  const {
    includeStats = true,
    autoRefresh = false,
    refreshInterval = 300000, // 5 minutes
    initialFilters = {
      page: 1,
      limit: 20,
      status: '',
      search: ''
    }
  } = options;

  // Single source of truth for state - no redundancy
  const [state, setState] = useState({
    // Shop data
    shops: [],
    isLoading: false,
    error: null,
    
    // Pagination
    pagination: {
      currentPage: 1,
      pageSize: 20,
      totalItems: 0,
      totalPages: 1,
      hasNextPage: false,
      hasPrevPage: false
    },
    
    // Filters
    filters: initialFilters,
    
    // Statistics (if enabled)
    stats: null,
    statsLoading: false,
    statsError: null,
    statsLastUpdated: null,
    
    // Operation states
    operations: {
      creating: false,
      updating: false,
      deleting: false,
      changingStatus: false,
      uploading: false
    }
  });

  // Refs for cleanup and tracking
  const abortControllerRef = useRef(null);
  const refreshTimeoutRef = useRef(null);
  const isMountedRef = useRef(true);
  const lastCallRef = useRef(null);

  // Update state helper to prevent unnecessary re-renders
  const updateState = useCallback((updates) => {
    if (!isMountedRef.current) return;
    
    setState(prevState => ({
      ...prevState,
      ...updates
    }));
  }, []);

  // Optimized debounced API call prevention - reduced intervals since API bridge handles throttling
  const preventDuplicateCall = useCallback((key, minInterval = 800) => {
    const now = Date.now();
    const lastCall = lastCallRef.current?.[key];

    if (lastCall && (now - lastCall) < minInterval) {
      console.log(`[useShopManagement] Preventing duplicate ${key} call (${now - lastCall}ms since last call)`);
      return true; // Silently prevent duplicate calls
    }

    if (!lastCallRef.current) lastCallRef.current = {};
    lastCallRef.current[key] = now;
    console.log(`[useShopManagement] Allowing ${key} call`);
    return false;
  }, []);

  /**
   * Fetch shops with SuperAdmin API - Optimized with API-level throttling
   */
  const fetchShops = useCallback(async (overrideFilters = {}, force = false) => {
    // Reduced interval since API bridge handles throttling - only prevent rapid duplicates
    if (!force && preventDuplicateCall('fetchShops', 1000)) {
      console.log('[useShopManagement] Returning cached shops to prevent duplicates');
      return state.shops;
    }

    // Cancel any existing request
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }
    abortControllerRef.current = new AbortController();

    try {
      updateState({
        isLoading: true,
        error: null
      });

      // Combine filters
      const requestFilters = {
        ...state.filters,
        ...overrideFilters
      };

      // Fetching shops with filters

      const result = await SuperAdminShopService.getShops(requestFilters);

      if (!isMountedRef.current) return [];

      if (result && result.shops) {
        // Shops fetched successfully
        console.log('[useShopManagement] Updating state with shops:', {
          shopsCount: result.shops.length,
          total: result.total,
          currentPage: result.currentPage
        });

        updateState({
          shops: result.shops,
          pagination: {
            currentPage: result.currentPage || 1,
            pageSize: requestFilters.limit || 20,
            totalItems: result.total || 0,
            totalPages: result.totalPages || 1,
            hasNextPage: result.hasNextPage || false,
            hasPrevPage: result.hasPrevPage || false
          },
          isLoading: false,
          error: null
        });

        console.log('[useShopManagement] State update completed, returning shops:', result.shops.length);
        return result.shops;
      } else {
        throw new Error('Invalid response format from server');
      }
    } catch (error) {
      console.error('[useShopManagement] Error fetching shops:', error);
      
      if (!isMountedRef.current || error.name === 'AbortError') {
        return [];
      }

      const errorMessage = error.message || 'Failed to fetch shops';
      updateState({
        isLoading: false,
        error: errorMessage
      });

      throw error;
    }
  }, [preventDuplicateCall, updateState]); // Removed state.filters to prevent stale closures

  /**
   * Fetch shop statistics - Optimized for SuperAdmin dashboard
   */
  const fetchStats = useCallback(async (statsFilters = {}, showToast = false) => {
    if (!includeStats) return null;

    // Reduced interval since stats endpoint is in throttle exceptions
    if (preventDuplicateCall('fetchStats', 1500)) {
      return state.stats;
    }

    try {
      updateState({ statsLoading: true, statsError: null });

      if (showToast) {
        toast.info('Loading shop statistics...');
      }

      // Fetching shop statistics

      const statsData = await SuperAdminShopService.getShopStats(statsFilters);

      if (!isMountedRef.current) return null;

      if (statsData) {
        updateState({
          stats: statsData,
          statsLoading: false,
          statsError: null,
          statsLastUpdated: new Date()
        });

        if (showToast) {
          toast.success('Shop statistics loaded successfully');
        }

        // Statistics loaded successfully
        return statsData;
      } else {
        throw new Error('No statistics data received');
      }
    } catch (error) {
      console.error('[useShopManagement] Error fetching statistics:', error);
      
      if (!isMountedRef.current || error.name === 'AbortError') {
        return null;
      }

      const errorMessage = error.message || 'Failed to load shop statistics';
      updateState({
        statsLoading: false,
        statsError: errorMessage
      });

      if (showToast) {
        toast.error(errorMessage);
      }

      return null;
    }
  }, [includeStats, preventDuplicateCall, updateState, state.stats]);

  /**
   * Get shop by ID - Direct service call
   */
  const getShopById = useCallback(async (shopId) => {
    if (!shopId) {
      throw new Error('Shop ID is required');
    }

    try {
      const shop = await SuperAdminShopService.getShopById(shopId);
      return shop;
    } catch (error) {
      console.error('[useShopManagement] Error fetching shop:', error);
      throw error;
    }
  }, []);

  /**
   * Create shop - SuperAdmin operation
   */
  const createShop = useCallback(async (shopData) => {
    try {
      updateState({
        operations: { ...state.operations, creating: true }
      });

      const result = await SuperAdminShopService.createShop(shopData);

      if (result) {
        toast.success('Shop created successfully');
        
        // Refresh shops list after creation
        await fetchShops({}, true);
        
        // Refresh stats if enabled
        if (includeStats) {
          await fetchStats({}, false);
        }
      }

      return result;
    } catch (error) {
      console.error('[useShopManagement] Error creating shop:', error);
      throw error;
    } finally {
      updateState({
        operations: { ...state.operations, creating: false }
      });
    }
  }, [state.operations, updateState, fetchShops, includeStats, fetchStats]);

  /**
   * Update shop - SuperAdmin operation
   */
  const updateShop = useCallback(async (shopId, updateData) => {
    try {
      updateState({
        operations: { ...state.operations, updating: true }
      });

      const result = await SuperAdminShopService.updateShop(shopId, updateData);

      if (result) {
        toast.success('Shop updated successfully');
        
        // Update local shop data
        const updatedShops = state.shops.map(shop =>
          shop.shopId === shopId || shop.id === shopId ? result : shop
        );
        
        updateState({ shops: updatedShops });
      }

      return result;
    } catch (error) {
      console.error('[useShopManagement] Error updating shop:', error);
      throw error;
    } finally {
      updateState({
        operations: { ...state.operations, updating: false }
      });
    }
  }, [state.operations, state.shops, updateState]);

  /**
   * Delete shop - SuperAdmin operation
   */
  const deleteShop = useCallback(async (shopId, reason) => {
    try {
      updateState({
        operations: { ...state.operations, deleting: true }
      });

      const result = await SuperAdminShopService.deleteShop(shopId, reason);

      if (result) {
        toast.success('Shop deleted successfully');
        
        // Remove from local state
        const updatedShops = state.shops.filter(shop =>
          shop.shopId !== shopId && shop.id !== shopId
        );
        
        updateState({ shops: updatedShops });
        
        // Refresh stats if enabled
        if (includeStats) {
          await fetchStats({}, false);
        }
      }

      return result;
    } catch (error) {
      console.error('[useShopManagement] Error deleting shop:', error);
      throw error;
    } finally {
      updateState({
        operations: { ...state.operations, deleting: false }
      });
    }
  }, [state.operations, state.shops, updateState, includeStats, fetchStats]);

  /**
   * Change shop status - Unified SuperAdmin operation
   */
  const changeShopStatus = useCallback(async (shopId, status, reason = '', sendEmail = true) => {
    try {
      updateState({
        operations: { ...state.operations, changingStatus: true }
      });

      const result = await SuperAdminShopService.changeShopStatus(shopId, status, reason, sendEmail);

      if (result) {
        // Update local shop data
        const updatedShops = state.shops.map(shop =>
          (shop.shopId === shopId || shop.id === shopId) 
            ? { ...shop, status: result.status } 
            : shop
        );
        
        updateState({ shops: updatedShops });
      }

      return result;
    } catch (error) {
      console.error('[useShopManagement] Error changing shop status:', error);
      throw error;
    } finally {
      updateState({
        operations: { ...state.operations, changingStatus: false }
      });
    }
  }, [state.operations, state.shops, updateState]);

  /**
   * Upload shop logo - SuperAdmin operation
   */
  const uploadLogo = useCallback(async (shopId, logoFile) => {
    try {
      updateState({
        operations: { ...state.operations, uploading: true }
      });

      const result = await SuperAdminShopService.uploadLogo(shopId, logoFile);

      if (result) {
        // Update local shop data
        const updatedShops = state.shops.map(shop =>
          (shop.shopId === shopId || shop.id === shopId) 
            ? { ...shop, logoUrl: result.logoUrl } 
            : shop
        );
        
        updateState({ shops: updatedShops });
      }

      return result;
    } catch (error) {
      console.error('[useShopManagement] Error uploading logo:', error);
      throw error;
    } finally {
      updateState({
        operations: { ...state.operations, uploading: false }
      });
    }
  }, [state.operations, state.shops, updateState]);

  /**
   * Update filters and fetch shops
   */
  const updateFilters = useCallback(async (newFilters) => {
    const updatedFilters = { ...state.filters, ...newFilters };
    
    updateState({ filters: updatedFilters });
    
    // Fetch with new filters
    await fetchShops(updatedFilters, true);
  }, [state.filters, updateState, fetchShops]);

  /**
   * Change page
   */
  const changePage = useCallback(async (page) => {
    await updateFilters({ page });
  }, [updateFilters]);

  /**
   * Change page size
   */
  const changePageSize = useCallback(async (limit) => {
    await updateFilters({ limit, page: 1 }); // Reset to first page
  }, [updateFilters]);

  /**
   * Reset filters
   */
  const resetFilters = useCallback(async () => {
    await updateFilters(initialFilters);
  }, [updateFilters, initialFilters]);

  /**
   * Refresh all data
   */
  const refreshAll = useCallback(async () => {
    try {
      const promises = [fetchShops({}, true)];
      
      if (includeStats) {
        promises.push(fetchStats({}, false));
      }
      
      await Promise.all(promises);
      toast.success('Data refreshed successfully');
    } catch (error) {
      console.error('[useShopManagement] Error refreshing data:', error);
      toast.error('Failed to refresh data');
    }
  }, [fetchShops, includeStats, fetchStats]);

  /**
   * Auto-refresh setup
   */
  useEffect(() => {
    if (!autoRefresh) return;

    const setupAutoRefresh = () => {
      if (refreshTimeoutRef.current) {
        clearTimeout(refreshTimeoutRef.current);
      }

      refreshTimeoutRef.current = setTimeout(() => {
        if (isMountedRef.current) {
          refreshAll();
          setupAutoRefresh(); // Setup next refresh
        }
      }, refreshInterval);
    };

    setupAutoRefresh();

    return () => {
      if (refreshTimeoutRef.current) {
        clearTimeout(refreshTimeoutRef.current);
      }
    };
  }, [autoRefresh, refreshInterval, refreshAll]);

  /**
   * Initial data load
   */
  useEffect(() => {
    const loadInitialData = async () => {
      try {
        await fetchShops({}, true);
        
        if (includeStats) {
          await fetchStats({}, false);
        }
      } catch (error) {
        console.error('[useShopManagement] Error loading initial data:', error);
      }
    };

    loadInitialData();
  }, []); // Only run once on mount

  /**
   * Cleanup on unmount
   */
  useEffect(() => {
    return () => {
      isMountedRef.current = false;
      
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
      
      if (refreshTimeoutRef.current) {
        clearTimeout(refreshTimeoutRef.current);
      }
    };
  }, []);

  // Helper operations
  const suspendShop = useCallback((shopId, reason, sendEmail = true) => 
    changeShopStatus(shopId, 'suspended', reason, sendEmail), [changeShopStatus]);
  const activateShop = useCallback((shopId, sendEmail = true) => 
    changeShopStatus(shopId, 'active', '', sendEmail), [changeShopStatus]);
  const deactivateShop = useCallback((shopId, reason, sendEmail = true) => 
    changeShopStatus(shopId, 'inactive', reason, sendEmail), [changeShopStatus]);

  // Compute derived state for statistics (aligned with backend SuperAdmin API response)
  const statsData = state.stats;
  const summaryData = state.stats;
  const activityData = null; // Not provided by simplified backend
  const businessMetricsData = null; // Not provided by simplified backend

  // Debug logging for hook return values
  if (process.env.NODE_ENV === 'development') {
    console.log('[useShopManagement] Hook returning:', {
      shopsCount: state.shops?.length || 0,
      shopsType: typeof state.shops,
      isArray: Array.isArray(state.shops),
      isLoading: state.isLoading,
      error: state.error,
      paginationTotal: state.pagination?.totalItems || 0
    });
  }

  return {
    // Core data
    shops: state.shops,
    pagination: state.pagination,
    filters: state.filters,

    // Loading states
    isLoading: state.isLoading,
    isCreating: state.operations.creating,
    isUpdating: state.operations.updating,
    isDeleting: state.operations.deleting,
    isChangingStatus: state.operations.changingStatus,
    isUploading: state.operations.uploading,
    isAnyOperationInProgress: Object.values(state.operations).some(Boolean) || state.isLoading,
    
    // Error states
    error: state.error,
    
    // Core operations
    fetchShops,
    getShopById,
    createShop,
    updateShop,
    deleteShop,
    changeShopStatus,
    uploadLogo,
    
    // Helper operations
    suspendShop,
    activateShop,
    deactivateShop,
    
    // Pagination and filtering
    updateFilters,
    changePage,
    changePageSize,
    resetFilters,
    
    // Data management
    refreshAll,
    
    // Statistics (if enabled)
    stats: statsData,
    summary: summaryData, // Add summary for KPI cards compatibility
    activity: activityData, // Not provided by simplified backend
    businessMetrics: businessMetricsData, // Not provided by simplified backend
    statsLoading: state.statsLoading,
    statsError: state.statsError,
    statsLastUpdated: state.statsLastUpdated,
    hasStats: state.stats !== null,
    isStatsStale: state.statsLastUpdated && (Date.now() - state.statsLastUpdated.getTime()) > 300000,
    fetchStats,
    refreshStats: () => fetchStats({}, true)
  };
}

/**
 * Simplified hook for managing a single shop's details
 */
export function useShopDetails(initialShopId = null) {
  const [shopId, setShopId] = useState(initialShopId);
  const [shop, setShop] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);

  const loadShop = useCallback(async (id = shopId) => {
    if (!id) {
      setError('Shop ID is required');
      return null;
    }

    setIsLoading(true);
    setError(null);

    try {
      const shopData = await SuperAdminShopService.getShopById(id);
      setShop(shopData);
      return shopData;
    } catch (err) {
      const errorMessage = err.message || 'Failed to load shop details';
      setError(errorMessage);
      toast.error(errorMessage);
      return null;
    } finally {
      setIsLoading(false);
    }
  }, [shopId]);

  const updateShop = useCallback(async (updateData) => {
    if (!shopId) {
      setError('Shop ID is required');
      return null;
    }

    setIsLoading(true);
    setError(null);

    try {
      const updatedShop = await SuperAdminShopService.updateShop(shopId, updateData);
      setShop(updatedShop);
      toast.success('Shop updated successfully');
      return updatedShop;
    } catch (err) {
      const errorMessage = err.message || 'Failed to update shop';
      setError(errorMessage);
      return null;
    } finally {
      setIsLoading(false);
    }
  }, [shopId]);

  const setShopIdAndLoad = useCallback(async (id, loadData = true) => {
    setShopId(id);
    if (loadData && id) {
      return await loadShop(id);
    }
    return null;
  }, [loadShop]);

  useEffect(() => {
    if (shopId) {
      loadShop();
    }
  }, [shopId, loadShop]);

  return {
    shop,
    shopId,
    isLoading,
    error,
    setShopId: setShopIdAndLoad,
    loadShop,
    updateShop
  };
}

/**
 * Simplified hook for managing shop filters
 */
export function useShopFilters(initialFilters = {}) {
  const [filters, setFilters] = useState({
    page: 1,
    limit: 20,
    status: '',
    search: '',
    ...initialFilters
  });

  const updateFilter = useCallback((key, value) => {
    setFilters(prev => ({
      ...prev,
      [key]: value,
      // Reset to first page when changing filters (except pagination)
      ...(key !== 'page' && key !== 'limit' ? { page: 1 } : {})
    }));
  }, []);

  const updateFilters = useCallback((newFilters) => {
    setFilters(prev => ({
      ...prev,
      ...newFilters
    }));
  }, []);

  const resetFilters = useCallback(() => {
    setFilters({
      page: 1,
      limit: 20,
      status: '',
      search: '',
      ...initialFilters
    });
  }, [initialFilters]);

  return {
    filters,
    updateFilter,
    updateFilters,
    resetFilters,
    
    // Helper methods
    setSearch: (search) => updateFilter('search', search),
    setStatus: (status) => updateFilter('status', status),
    setPage: (page) => updateFilter('page', page),
    setPageSize: (limit) => updateFilter('limit', limit)
  };
} 