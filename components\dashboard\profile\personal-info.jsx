"use client"

import { useState, useEffect } from 'react'
import { User, Mail, Phone, Shield, Calendar, MapPin, Crown, Edit3, Save, X } from 'lucide-react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { toast } from 'sonner'
import { cn } from '@/lib/utils'
import { useAuth } from '@/contexts/auth-context'
import apiBridge from '@/lib/api/bridge'

export function PersonalInfo({ user }) {
  const { user: contextUser, setUser } = useAuth()
  const [isEditing, setIsEditing] = useState(false)
  const [editedUser, setEditedUser] = useState({
    fullName: user?.fullName || '',
    phone: user?.phone || ''
  })
  const [loading, setLoading] = useState(false)

  // Sync edited state when user prop changes
  useEffect(() => {
    setEditedUser({
      fullName: user?.fullName || '',
      phone: user?.phone || ''
    })
  }, [user?.fullName, user?.phone])

  // Get role badge color
  const getRoleBadgeColor = (role) => {
    switch (role?.toLowerCase()) {
      case 'superadmin':
        return 'bg-gradient-to-r from-purple-500 to-pink-500 text-white'
      case 'admin':
        return 'bg-gradient-to-r from-blue-500 to-cyan-500 text-white'
      case 'employee':
        return 'bg-gradient-to-r from-green-500 to-emerald-500 text-white'
      default:
        return 'bg-gradient-to-r from-gray-500 to-slate-500 text-white'
    }
  }

  // Get role icon
  const getRoleIcon = (role) => {
    switch (role?.toLowerCase()) {
      case 'superadmin':
        return Crown
      case 'admin':
      case 'employee':
        return Shield
      default:
        return Shield
    }
  }

  const RoleIcon = getRoleIcon(user?.role)

  // Format date
  const formatDate = (date) => {
    if (!date) return 'Not available'
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(new Date(date))
  }

  // Handle save changes
  const handleSave = async () => {
    setLoading(true)
    try {
      // Prepare update data - only include changed fields
      const updateData = {}
      if (editedUser.fullName !== user?.fullName) {
        updateData.fullName = editedUser.fullName
      }
      if (editedUser.phone !== user?.phone) {
        updateData.phone = editedUser.phone
      }
      
      // Check if there are any changes
      if (Object.keys(updateData).length === 0) {
        toast.info('No changes to save')
        setIsEditing(false)
        setLoading(false)
        return
      }
      
      // Call the API to update profile using API bridge
      const response = await apiBridge.put('/api/auth/me', updateData)
      
      // The backend returns the updated user data in response.data.data
      const updatedUserData = response.data.data
      
      // Clear any cached profile data to ensure fresh data on next load
      apiBridge.clearCache(['/api/auth/me', 'profile*', 'auth-profile*'])
      
      // Update the auth context with the complete updated user data
      // Make sure to preserve all existing fields and only update the changed ones
      const mergedUserData = {
        ...contextUser, // Keep existing user data
        ...updatedUserData, // Override with updated data from backend
        updatedAt: new Date().toISOString(), // Add timestamp for cache invalidation
        lastProfileUpdate: Date.now() // Track when profile was last updated
      }
      
      console.log('Profile update - About to call setUser with:', mergedUserData)
      setUser(mergedUserData)
      
      // Update the local edited state to reflect the saved changes
      setEditedUser({
        fullName: updatedUserData.fullName || '',
        phone: updatedUserData.phone || ''
      })
      
      toast.success('Profile updated successfully')
      setIsEditing(false)
      
      console.log('Profile update - Backend response:', updatedUserData)
      console.log('Profile update - Merged user data:', mergedUserData)
      console.log('Profile update - Context user before:', contextUser)
      
      // Force a small delay to ensure state updates are processed
      setTimeout(() => {
        console.log('Profile update - Context user after:', contextUser)
      }, 100)
      
      // No need to fetch fresh data immediately as we just updated it
      // The auth context will handle background sync after the manual update timeout
    } catch (error) {
      console.error('Profile update error:', error)
      toast.error(error.message || 'Failed to update profile')
    } finally {
      setLoading(false)
    }
  }

  // Handle cancel edit
  const handleCancel = () => {
    setEditedUser({
      fullName: user?.fullName || '',
      phone: user?.phone || ''
    })
    setIsEditing(false)
  }

  return (
    <div className="space-y-6">
      {/* Basic Information */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <User className="h-5 w-5" />
                Account Information
              </CardTitle>
              <CardDescription>
                Your basic account details from the system
              </CardDescription>
            </div>
            {!isEditing ? (
              <Button
                variant="outline"
                size="sm"
                onClick={() => setIsEditing(true)}
                className="flex items-center gap-2"
              >
                <Edit3 className="h-4 w-4" />
                Edit
              </Button>
            ) : (
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleCancel}
                  disabled={loading}
                >
                  <X className="h-4 w-4" />
                  Cancel
                </Button>
                <Button
                  size="sm"
                  onClick={handleSave}
                  disabled={loading}
                  className="flex items-center gap-2"
                >
                  <Save className="h-4 w-4" />
                  {loading ? 'Saving...' : 'Save'}
                </Button>
              </div>
            )}
          </div>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Full Name */}
            <div className="space-y-2">
              <Label htmlFor="fullName" className="text-sm font-medium text-muted-foreground">Full Name</Label>
              {isEditing ? (
                <Input
                  id="fullName"
                  value={editedUser.fullName}
                  onChange={(e) => setEditedUser(prev => ({ ...prev, fullName: e.target.value }))}
                  placeholder="Enter your full name"
                />
              ) : (
                <div className="p-3 bg-muted/50 rounded-md">
                  <p className="text-sm">{user?.fullName || 'Not available'}</p>
                </div>
              )}
            </div>

            {/* Email (Read-only) */}
            <div className="space-y-2">
              <Label className="text-sm font-medium text-muted-foreground">Email Address</Label>
              <div className="p-3 bg-muted/30 rounded-md flex items-center gap-2 opacity-75">
                <Mail className="h-4 w-4 text-muted-foreground" />
                <p className="text-sm">{user?.email || 'Not available'}</p>
                <Badge variant="secondary" className="ml-auto text-xs">Read-only</Badge>
              </div>
            </div>

            {/* Phone */}
            <div className="space-y-2">
              <Label htmlFor="phone" className="text-sm font-medium text-muted-foreground">Phone Number</Label>
              {isEditing ? (
                <Input
                  id="phone"
                  value={editedUser.phone}
                  onChange={(e) => setEditedUser(prev => ({ ...prev, phone: e.target.value }))}
                  placeholder="Enter your phone number"
                />
              ) : (
                <div className="p-3 bg-muted/50 rounded-md flex items-center gap-2">
                  <Phone className="h-4 w-4 text-muted-foreground" />
                  <p className="text-sm">{user?.phone || 'Not available'}</p>
                </div>
              )}
            </div>

            {/* Role (Read-only) */}
            <div className="space-y-2">
              <Label className="text-sm font-medium text-muted-foreground">Role</Label>
              <div className="p-3 bg-muted/30 rounded-md opacity-75">
                <Badge 
                  className={cn(
                    "text-xs font-medium px-3 py-1 border-0",
                    getRoleBadgeColor(user?.role)
                  )}
                >
                  <RoleIcon className="h-3 w-3 mr-1" />
                  {user?.role || 'Not available'}
                </Badge>
                <Badge variant="secondary" className="ml-2 text-xs">System assigned</Badge>
              </div>
            </div>

            {/* Shop ID (Read-only) */}
            <div className="space-y-2">
              <Label className="text-sm font-medium text-muted-foreground">Shop ID</Label>
              <div className="p-3 bg-muted/30 rounded-md flex items-center gap-2 opacity-75">
                <MapPin className="h-4 w-4 text-muted-foreground" />
                <p className="text-sm font-mono">{user?.shopId || 'No shop assigned'}</p>
                <Badge variant="secondary" className="ml-auto text-xs">Read-only</Badge>
              </div>
            </div>

            {/* Status (Read-only) */}
            <div className="space-y-2">
              <Label className="text-sm font-medium text-muted-foreground">Account Status</Label>
              <div className="p-3 bg-muted/30 rounded-md opacity-75">
                <Badge variant={user?.status === 'active' ? 'default' : 'secondary'}>
                  {user?.status || 'Unknown'}
                </Badge>
                <Badge variant="secondary" className="ml-2 text-xs">System managed</Badge>
              </div>
            </div>

            {/* Email Verified (Read-only) */}
            <div className="space-y-2">
              <Label className="text-sm font-medium text-muted-foreground">Email Verification</Label>
              <div className="p-3 bg-muted/30 rounded-md opacity-75">
                <Badge variant={user?.emailVerified ? 'default' : 'destructive'}>
                  {user?.emailVerified ? 'Verified' : 'Not Verified'}
                </Badge>
                <Badge variant="secondary" className="ml-2 text-xs">System managed</Badge>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Account Activity */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calendar className="h-5 w-5" />
            Account Activity
          </CardTitle>
          <CardDescription>
            Your account activity and login information
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Account Created */}
            <div className="space-y-2">
              <label className="text-sm font-medium text-muted-foreground">Account Created</label>
              <div className="p-3 bg-muted/50 rounded-md">
                <p className="text-sm">{formatDate(user?.createdAt)}</p>
              </div>
            </div>

            {/* Last Login */}
            <div className="space-y-2">
              <label className="text-sm font-medium text-muted-foreground">Last Login</label>
              <div className="p-3 bg-muted/50 rounded-md">
                <p className="text-sm">{formatDate(user?.lastLoginAt)}</p>
              </div>
            </div>

            {/* Total Login Sessions */}
            <div className="space-y-2">
              <label className="text-sm font-medium text-muted-foreground">Total Login Sessions</label>
              <div className="p-3 bg-muted/50 rounded-md">
                <p className="text-sm font-bold">{user?.loginHistory?.length || 0}</p>
              </div>
            </div>

            {/* Account Updated */}
            <div className="space-y-2">
              <label className="text-sm font-medium text-muted-foreground">Last Updated</label>
              <div className="p-3 bg-muted/50 rounded-md">
                <p className="text-sm">{formatDate(user?.updatedAt)}</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* System Preferences */}
      {user?.preferences && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Shield className="h-5 w-5" />
              System Preferences
            </CardTitle>
            <CardDescription>
              Your current system preferences
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {/* Theme */}
              <div className="space-y-2">
                <label className="text-sm font-medium text-muted-foreground">Theme</label>
                <div className="p-3 bg-muted/50 rounded-md">
                  <p className="text-sm capitalize">{user.preferences.theme || 'System'}</p>
                </div>
              </div>

              {/* Language */}
              <div className="space-y-2">
                <label className="text-sm font-medium text-muted-foreground">Language</label>
                <div className="p-3 bg-muted/50 rounded-md">
                  <p className="text-sm">{user.preferences.language || 'English'}</p>
                </div>
              </div>

              {/* Notifications */}
              <div className="space-y-2">
                <label className="text-sm font-medium text-muted-foreground">Notifications</label>
                <div className="p-3 bg-muted/50 rounded-md space-y-1">
                  <p className="text-xs">Email: {user.preferences.notifications?.email ? 'Enabled' : 'Disabled'}</p>
                  <p className="text-xs">SMS: {user.preferences.notifications?.sms ? 'Enabled' : 'Disabled'}</p>
                  <p className="text-xs">App: {user.preferences.notifications?.app ? 'Enabled' : 'Disabled'}</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
} 