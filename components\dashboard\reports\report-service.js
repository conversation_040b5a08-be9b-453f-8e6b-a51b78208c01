import axios from 'axios';

/**
 * Service for managing reports
 */
class ReportService {
  constructor() {
    this.apiUrl = process.env.NEXT_PUBLIC_API_URL || 'https://deyncare-backend.khanciye.com/api';
    this.endpoints = {
      reports: `${this.apiUrl}/reports`,
      statistics: `${this.apiUrl}/reports/statistics`,
      system: `${this.apiUrl}/reports/system`,
      schedule: `${this.apiUrl}/reports/schedule`
    };
  }

  /**
   * Get all reports with filtering and pagination
   * SuperAdmin: Gets reports across all shops
   * Admin: Gets reports for their shop only
   * @param {Object} filters - Filter parameters
   * @param {number} page - Page number
   * @param {number} limit - Items per page
   * @returns {Promise<Object>} Reports with pagination info
   */
  async getAllReports(filters = {}, page = 1, limit = 10) {
    try {
      // Build query parameters
      const params = new URLSearchParams({
        page,
        limit
      });

      // Add optional filters if they exist
      if (filters.shopId) params.append('shopId', filters.shopId);
      if (filters.type) params.append('type', filters.type);
      if (filters.format) params.append('format', filters.format);
      if (filters.search) params.append('search', filters.search);
      if (filters.startDate) params.append('startDate', filters.startDate);
      if (filters.endDate) params.append('endDate', filters.endDate);

      // For development, use mock data
      if (process.env.NODE_ENV === 'development' && !process.env.NEXT_PUBLIC_USE_API) {
        return this.getMockReports(filters, page, limit);
      }

      // Make API request with auth token
      const token = localStorage.getItem('token');
      const response = await axios.get(`${this.endpoints.reports}?${params.toString()}`, {
        headers: {
          Authorization: `Bearer ${token}`
        }
      });

      return {
        reports: response.data.data.reports,
        pagination: response.data.data.pagination
      };
    } catch (error) {
      console.error('Error fetching reports:', error);
      throw error;
    }
  }

  /**
   * Get reports for a specific shop
   * @param {string} shopId - Shop ID
   * @param {Object} filters - Filter parameters
   * @param {number} page - Page number
   * @param {number} limit - Items per page
   * @returns {Promise<Object>} Reports with pagination info
   */
  async getReportsByShop(shopId, filters = {}, page = 1, limit = 10) {
    try {
      // Build query parameters
      const params = new URLSearchParams({
        page,
        limit
      });

      // Add optional filters if they exist
      if (filters.type) params.append('type', filters.type);
      if (filters.format) params.append('format', filters.format);
      if (filters.search) params.append('search', filters.search);
      if (filters.startDate) params.append('startDate', filters.startDate);
      if (filters.endDate) params.append('endDate', filters.endDate);

      // For development, use mock data
      if (process.env.NODE_ENV === 'development' && !process.env.NEXT_PUBLIC_USE_API) {
        return this.getMockReports({ ...filters, shopId }, page, limit);
      }

      // Make API request with auth token
      const token = localStorage.getItem('token');
      const response = await axios.get(`${this.endpoints.reports}/shop/${shopId}?${params.toString()}`, {
        headers: {
          Authorization: `Bearer ${token}`
        }
      });

      return {
        reports: response.data.data.reports,
        pagination: response.data.data.pagination
      };
    } catch (error) {
      console.error(`Error fetching reports for shop ${shopId}:`, error);
      throw error;
    }
  }

  /**
   * Get a single report by ID
   * @param {string} reportId - Report ID
   * @returns {Promise<Object>} Report details
   */
  async getReportById(reportId) {
    try {
      // For development, use mock data
      if (process.env.NODE_ENV === 'development' && !process.env.NEXT_PUBLIC_USE_API) {
        return this.getMockReportById(reportId);
      }

      // Make API request with auth token
      const token = localStorage.getItem('token');
      const response = await axios.get(`${this.endpoints.reports}/${reportId}`, {
        headers: {
          Authorization: `Bearer ${token}`
        }
      });

      return response.data.data.report;
    } catch (error) {
      console.error(`Error fetching report ${reportId}:`, error);
      throw error;
    }
  }

  /**
   * Generate a new report
   * @param {Object} reportData - Report data
   * @returns {Promise<Object>} Generated report
   */
  async generateReport(reportData) {
    try {
      // For development, use mock data
      if (process.env.NODE_ENV === 'development' && !process.env.NEXT_PUBLIC_USE_API) {
        return this.createMockReport(reportData);
      }

      // Make API request with auth token
      const token = localStorage.getItem('token');
      const response = await axios.post(this.endpoints.reports, reportData, {
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      return response.data.data.report;
    } catch (error) {
      console.error('Error generating report:', error);
      throw error;
    }
  }

  /**
   * Generate a system-wide report (SuperAdmin only)
   * @param {Object} reportData - Report data
   * @returns {Promise<Object>} Generated report
   */
  async generateSystemReport(reportData) {
    try {
      // For development, use mock data
      if (process.env.NODE_ENV === 'development' && !process.env.NEXT_PUBLIC_USE_API) {
        return this.createMockReport({ ...reportData, shopId: 'system' });
      }

      // Make API request with auth token
      const token = localStorage.getItem('token');
      const response = await axios.post(this.endpoints.system, reportData, {
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      return response.data.data.report;
    } catch (error) {
      console.error('Error generating system report:', error);
      throw error;
    }
  }

  /**
   * Get report statistics (SuperAdmin only)
   * @returns {Promise<Object>} Report statistics
   */
  async getReportStatistics() {
    try {
      // For development, use mock data
      if (process.env.NODE_ENV === 'development' && !process.env.NEXT_PUBLIC_USE_API) {
        return this.getMockReportStatistics();
      }

      // Make API request with auth token
      const token = localStorage.getItem('token');
      const response = await axios.get(this.endpoints.statistics, {
        headers: {
          Authorization: `Bearer ${token}`
        }
      });

      return response.data.data.statistics;
    } catch (error) {
      console.error('Error fetching report statistics:', error);
      throw error;
    }
  }

  /**
   * Delete a report
   * @param {string} reportId - Report ID
   * @returns {Promise<Object>} Success result
   */
  async deleteReport(reportId) {
    try {
      // For development, use mock data
      if (process.env.NODE_ENV === 'development' && !process.env.NEXT_PUBLIC_USE_API) {
        return { success: true, message: 'Report deleted successfully' };
      }

      // Make API request with auth token
      const token = localStorage.getItem('token');
      const response = await axios.delete(`${this.endpoints.reports}/${reportId}`, {
        headers: {
          Authorization: `Bearer ${token}`
        }
      });

      return response.data.data;
    } catch (error) {
      console.error(`Error deleting report ${reportId}:`, error);
      throw error;
    }
  }

  /**
   * Email a report to specified recipients
   * @param {string} reportId - Report ID
   * @param {Object} emailData - Email data
   * @returns {Promise<Object>} Success result
   */
  async emailReport(reportId, emailData) {
    try {
      // For development, use mock data
      if (process.env.NODE_ENV === 'development' && !process.env.NEXT_PUBLIC_USE_API) {
        return { success: true, message: 'Report emailed successfully' };
      }

      // Make API request with auth token
      const token = localStorage.getItem('token');
      const response = await axios.post(`${this.endpoints.reports}/${reportId}/email`, emailData, {
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      return response.data.data;
    } catch (error) {
      console.error(`Error emailing report ${reportId}:`, error);
      throw error;
    }
  }

  /**
   * Schedule report delivery
   * @param {Object} scheduleData - Schedule configuration
   * @returns {Promise<Object>} Success result
   */
  async scheduleReportDelivery(scheduleData) {
    try {
      // For development, use mock data
      if (process.env.NODE_ENV === 'development' && !process.env.NEXT_PUBLIC_USE_API) {
        return {
          success: true,
          message: 'Report delivery scheduled successfully',
          schedule: {
            id: 'sch_' + Math.random().toString(36).substring(2, 12),
            ...scheduleData,
            createdAt: new Date().toISOString()
          }
        };
      }

      // Make API request with auth token
      const token = localStorage.getItem('token');
      const response = await axios.post(this.endpoints.schedule, scheduleData, {
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      return response.data.data;
    } catch (error) {
      console.error('Error scheduling report delivery:', error);
      throw error;
    }
  }

  /**
   * Format date for display
   * @param {string|Date} date - Date to format
   * @returns {string} Formatted date
   */
  formatDate(date) {
    if (!date) return 'N/A';
    
    // Use a static date string to avoid hydration errors
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    return dateObj.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  }

  /**
   * Generate mock reports for development
   * @param {Object} filters - Filter parameters
   * @param {number} page - Page number
   * @param {number} limit - Items per page
   * @returns {Promise<Object>} Mock reports with pagination
   */
  getMockReports(filters = {}, page = 1, limit = 10) {
    // Create 50 sample reports
    const allReports = Array.from({ length: 50 }, (_, i) => {
      const id = i + 1;
      const reportId = `rep_${id.toString().padStart(4, '0')}`;
      const shopId = filters.shopId || (Math.floor(Math.random() * 5) + 1).toString();
      const types = ['debt', 'sales', 'ml-risk', 'pos-profit'];
      const formats = ['pdf', 'csv', 'excel'];
      const reportType = types[Math.floor(Math.random() * types.length)];
      
      const date = new Date();
      date.setDate(date.getDate() - Math.floor(Math.random() * 30));
      
      return {
        reportId,
        shopId: shopId === 'system' ? 'system' : `shop_${shopId.padStart(4, '0')}`,
        title: `${reportType.charAt(0).toUpperCase() + reportType.slice(1)} Report ${id}`,
        type: reportType,
        format: formats[Math.floor(Math.random() * formats.length)],
        url: `${process.env.NEXT_PUBLIC_STORAGE_URL || 'https://deyncare-storage.cajiibcreative.com'}/reports/${reportId}.pdf`,
        description: `Sample ${reportType} report for shop ${shopId}`,
        parameters: {
          startDate: new Date(date.getFullYear(), date.getMonth(), 1).toISOString(),
          endDate: new Date(date.getFullYear(), date.getMonth() + 1, 0).toISOString(),
          filters: {}
        },
        generatedAt: date.toISOString(),
        createdBy: `user_${(Math.floor(Math.random() * 3) + 1).toString().padStart(4, '0')}`
      };
    });
    
    // Apply filters
    let filteredReports = [...allReports];
    
    if (filters.shopId && filters.shopId !== 'all') {
      filteredReports = filteredReports.filter(report => 
        report.shopId === `shop_${filters.shopId.padStart(4, '0')}` || 
        (filters.shopId === 'system' && report.shopId === 'system')
      );
    }
    
    if (filters.type && filters.type !== 'all') {
      filteredReports = filteredReports.filter(report => report.type === filters.type);
    }
    
    if (filters.format && filters.format !== 'all') {
      filteredReports = filteredReports.filter(report => report.format === filters.format);
    }
    
    if (filters.search) {
      const search = filters.search.toLowerCase();
      filteredReports = filteredReports.filter(report => 
        report.title.toLowerCase().includes(search) ||
        report.description.toLowerCase().includes(search)
      );
    }
    
    // Calculate pagination
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedReports = filteredReports.slice(startIndex, endIndex);
    
    return {
      reports: paginatedReports,
      pagination: {
        totalDocs: filteredReports.length,
        limit,
        page,
        totalPages: Math.ceil(filteredReports.length / limit),
        hasPrevPage: page > 1,
        hasNextPage: page < Math.ceil(filteredReports.length / limit),
        prevPage: page > 1 ? page - 1 : null,
        nextPage: page < Math.ceil(filteredReports.length / limit) ? page + 1 : null
      }
    };
  }

  /**
   * Get mock report by ID
   * @param {string} reportId - Report ID
   * @returns {Object} Mock report
   */
  getMockReportById(reportId) {
    // Create a sample report based on the ID
    const shopId = Math.floor(Math.random() * 5) + 1;
    const types = ['debt', 'sales', 'ml-risk', 'pos-profit'];
    const formats = ['pdf', 'csv', 'excel'];
    const reportType = types[Math.floor(Math.random() * types.length)];
    
    const date = new Date();
    date.setDate(date.getDate() - Math.floor(Math.random() * 30));
    
    return {
      reportId,
      shopId: `shop_${shopId.toString().padStart(4, '0')}`,
      title: `${reportType.charAt(0).toUpperCase() + reportType.slice(1)} Report`,
      type: reportType,
      format: formats[Math.floor(Math.random() * formats.length)],
      url: `${process.env.NEXT_PUBLIC_STORAGE_URL || 'https://deyncare-storage.cajiibcreative.com'}/reports/${reportId}.pdf`,
      description: `Detailed ${reportType} report for shop ${shopId}`,
      parameters: {
        startDate: new Date(date.getFullYear(), date.getMonth(), 1).toISOString(),
        endDate: new Date(date.getFullYear(), date.getMonth() + 1, 0).toISOString(),
        filters: {}
      },
      generatedAt: date.toISOString(),
      createdBy: `user_${(Math.floor(Math.random() * 3) + 1).toString().padStart(4, '0')}`
    };
  }

  /**
   * Create a mock report
   * @param {Object} reportData - Report data
   * @returns {Object} Mock generated report
   */
  createMockReport(reportData) {
    const reportId = 'rep_' + Math.random().toString(36).substring(2, 10);
    
    return {
      reportId,
      ...reportData,
      url: `${process.env.NEXT_PUBLIC_STORAGE_URL || 'https://deyncare-storage.cajiibcreative.com'}/reports/${reportId}.${reportData.format}`,
      generatedAt: new Date().toISOString()
    };
  }

  /**
   * Get mock report statistics
   * @returns {Object} Mock statistics
   */
  getMockReportStatistics() {
    return {
      totalReports: 257,
      reportsByType: [
        { _id: 'debt', count: 78 },
        { _id: 'sales', count: 103 },
        { _id: 'ml-risk', count: 42 },
        { _id: 'pos-profit', count: 34 }
      ],
      reportsByFormat: [
        { _id: 'pdf', count: 153 },
        { _id: 'excel', count: 68 },
        { _id: 'csv', count: 36 }
      ],
      reportsByShop: [
        { _id: 'shop_0001', count: 45 },
        { _id: 'shop_0002', count: 38 },
        { _id: 'shop_0003', count: 29 },
        { _id: 'shop_0004', count: 25 },
        { _id: 'system', count: 15 }
      ],
      reportsByDay: Array.from({ length: 30 }, (_, i) => {
        const date = new Date();
        date.setDate(date.getDate() - (29 - i));
        
        return {
          _id: date.toISOString().split('T')[0],
          count: Math.floor(Math.random() * 10) + 1
        };
      })
    };
  }
}

export default ReportService;
