const cron = require('node-cron');
const DebtNotificationService = require('../services/debtNotificationService');
const { logInfo, logError } = require('../utils');

/**
 * Simple Debt Reminder Cron Job
 * Runs daily at 9:00 AM to send debt reminders
 * - 7 days before due date
 * - 3 days before due date  
 * - Overdue debts
 */

class DebtReminderCron {
  
  /**
   * Start the debt reminder cron job
   */
  static start() {
    // Run daily at 9:00 AM
    cron.schedule('0 9 * * *', async () => {
      logInfo('🕘 Starting daily debt reminder checks...', 'DebtReminderCron');
      
      try {
        await this.runDailyReminders();
        logInfo('✅ Daily debt reminder checks completed', 'DebtReminderCron');
      } catch (error) {
        logError(`❌ Daily debt reminder checks failed: ${error.message}`, 'DebtReminderCron', error);
      }
    });

    logInfo('📅 Debt reminder cron job started (daily at 9:00 AM)', 'DebtReminderCron');
  }

  /**
   * Run all daily reminder checks
   */
  static async runDailyReminders() {
    const results = {
      sevenDays: null,
      threeDays: null,
      overdue: null
    };

    // 1. Send 7-day reminders
    try {
      logInfo('📅 Checking 7-day reminders...', 'DebtReminderCron');
      results.sevenDays = await DebtNotificationService.sendDebtReminders('7_days');
      logInfo(`📅 7-day reminders: ${results.sevenDays.totalDebts || 0} debts, ${results.sevenDays.totalShops || 0} shops`, 'DebtReminderCron');
    } catch (error) {
      logError(`❌ 7-day reminders failed: ${error.message}`, 'DebtReminderCron');
      results.sevenDays = { success: false, error: error.message };
    }

    // 2. Send 3-day reminders  
    try {
      logInfo('⚠️ Checking 3-day reminders...', 'DebtReminderCron');
      results.threeDays = await DebtNotificationService.sendDebtReminders('3_days');
      logInfo(`⚠️ 3-day reminders: ${results.threeDays.totalDebts || 0} debts, ${results.threeDays.totalShops || 0} shops`, 'DebtReminderCron');
    } catch (error) {
      logError(`❌ 3-day reminders failed: ${error.message}`, 'DebtReminderCron');
      results.threeDays = { success: false, error: error.message };
    }

    // 3. Send overdue reminders
    try {
      logInfo('🚨 Checking overdue reminders...', 'DebtReminderCron');
      results.overdue = await DebtNotificationService.sendDebtReminders('overdue');
      logInfo(`🚨 Overdue reminders: ${results.overdue.totalDebts || 0} debts, ${results.overdue.totalShops || 0} shops`, 'DebtReminderCron');
    } catch (error) {
      logError(`❌ Overdue reminders failed: ${error.message}`, 'DebtReminderCron');
      results.overdue = { success: false, error: error.message };
    }

    // Summary
    const totalDebts = (results.sevenDays?.totalDebts || 0) + 
                      (results.threeDays?.totalDebts || 0) + 
                      (results.overdue?.totalDebts || 0);
    
    const totalShops = Math.max(
      results.sevenDays?.totalShops || 0,
      results.threeDays?.totalShops || 0, 
      results.overdue?.totalShops || 0
    );

    logInfo(`📊 Daily reminder summary: ${totalDebts} total debts across ${totalShops} shops`, 'DebtReminderCron');
    
    return results;
  }

  /**
   * Run reminders manually (for testing)
   */
  static async runManual() {
    logInfo('🔧 Running debt reminders manually...', 'DebtReminderCron');
    return await this.runDailyReminders();
  }

  /**
   * Stop the cron job
   */
  static stop() {
    // Note: node-cron doesn't provide direct stop method for individual jobs
    // This would require storing job references if needed
    logInfo('⏹️ Debt reminder cron job stopped', 'DebtReminderCron');
  }
}

module.exports = DebtReminderCron; 