"use client"

import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { useEffect } from "react"
import { useAuth } from "@/contexts/auth-context"

export default function UnauthorizedPage() {
  const { logout } = useAuth()
  
  // Auto-logout after landing on unauthorized page
  useEffect(() => {
    // Small timeout to allow viewing the message before logout
    const timer = setTimeout(() => {
      logout()
    }, 8000)
    
    return () => clearTimeout(timer)
  }, [logout])
  
  return (
    <div className="flex min-h-screen bg-background items-center justify-center">
      <div className="w-full max-w-md p-8 space-y-6 text-center rounded-lg border border-border shadow-lg">
        <div className="bg-destructive/10 text-destructive p-3 rounded-full w-16 h-16 mx-auto flex items-center justify-center">
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={2} stroke="currentColor" className="w-8 h-8">
            <path strokeLinecap="round" strokeLinejoin="round" d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728A9 9 0 015.636 5.636m12.728 12.728L5.636 5.636" />
          </svg>
        </div>
        
        <h1 className="text-3xl font-bold text-foreground">Access Denied</h1>
        
        <div className="space-y-4">
          <p className="text-muted-foreground">
            This web application is exclusively for DeynCare superadmins.
          </p>
          <p className="text-sm text-muted-foreground">
            If you believe you should have access, please contact system administration.
          </p>
          <p className="text-sm text-muted-foreground/80">
            For admin and employee access, please use the mobile application.
          </p>
        </div>
        
        <div className="pt-4">
          <Link href="/login">
            <Button variant="default" className="w-full bg-primary text-primary-foreground">
              Return to Login
            </Button>
          </Link>
          <p className="mt-4 text-xs text-muted-foreground">
            You will be automatically logged out in a few seconds...
          </p>
        </div>
      </div>
    </div>
  )
}
