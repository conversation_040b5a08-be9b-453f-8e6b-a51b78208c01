"use client"

import { useState, useEffect } from 'react'
import { useAuth } from '@/contexts/auth-context'
import { ProfileHeader } from '@/components/dashboard/profile/profile-header'
import { ProfileTabs } from '@/components/dashboard/profile/profile-tabs'
import { PersonalInfo } from '@/components/dashboard/profile/personal-info'
import { SecuritySettings } from '@/components/dashboard/profile/security-settings'
import { ResponsiveContainer } from '@/components/layout/responsive-container'

export default function ProfilePage() {
  const { user, isLoading } = useAuth()
  const [activeTab, setActiveTab] = useState('personal')

  // Redirect if not authenticated
  useEffect(() => {
    if (!isLoading && !user) {
      window.location.href = '/login'
    }
  }, [user, isLoading])

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="flex flex-col items-center gap-2">
          <div className="h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent"></div>
          <p className="text-sm text-muted-foreground">Loading profile...</p>
        </div>
      </div>
    )
  }

  if (!user) {
    return null
  }

  const renderTabContent = () => {
    switch (activeTab) {
      case 'personal':
        return <PersonalInfo user={user} />
      case 'security':
        return <SecuritySettings user={user} />
      default:
        return <PersonalInfo user={user} />
    }
  }

  return (
    <ResponsiveContainer>
      <div className="space-y-6">
        {/* Profile Header */}
        <ProfileHeader user={user} />
        
        {/* Profile Tabs */}
        <ProfileTabs 
          activeTab={activeTab} 
          onTabChange={setActiveTab} 
        />
        
        {/* Tab Content */}
        <div className="min-h-[500px]">
          {renderTabContent()}
        </div>
      </div>
    </ResponsiveContainer>
  )
} 