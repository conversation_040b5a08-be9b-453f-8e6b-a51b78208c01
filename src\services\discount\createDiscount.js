/**
 * Create Discount Module
 * Handles discount code creation
 */
const { DiscountCode } = require('../../models');
const { AppError, logInfo, logError, logSuccess, idGenerator } = require('../../utils');

/**
 * Create a new discount code
 * @param {Object} discountData - Discount code data
 * @param {String} createdBy - User ID creating the discount
 * @returns {Promise<Object>} Created discount code
 */
const createDiscountCode = async (discountData, createdBy) => {
  try {
    // Check if code already exists
    const existingCode = await DiscountCode.findOne({ 
      code: discountData.code.toUpperCase(), 
      isDeleted: false 
    });
    
    if (existingCode) {
      throw new AppError('Discount code already exists', 400, 'duplicate_code');
    }
    
    // Generate a unique discountId if not provided
    if (!discountData.discountId) {
      // Use the proper ID generator utility with sequential numbering
      discountData.discountId = await idGenerator.generateDiscountId(DiscountCode);
      logInfo(`Generated sequential discountId: ${discountData.discountId}`, 'DiscountService');
    }
    
    // Create discount code
    const discountCode = new DiscountCode({
      ...discountData,
      createdBy,
      updatedBy: createdBy
    });
    
    // Save to database
    await discountCode.save();
    
    logSuccess(`Discount code created: ${discountCode.code}`, 'DiscountService');
    return discountCode;
  } catch (error) {
    if (error instanceof AppError) {
      throw error;
    }
    
    logError(`Failed to create discount code: ${error.message}`, 'DiscountService', error);
    throw new AppError('Failed to create discount code', 500, 'discount_creation_error');
  }
};

module.exports = createDiscountCode;
