import apiBridge from '@/lib/api/bridge';
import { ENDPOINTS } from '@/lib/api/contract';;
import { handleError, processApiResponse, validateRequiredFields } from '../baseService';

/**
 * Update auto-renewal setting for subscription
 * @param {Object} renewalData - Auto-renewal data (autoRenew boolean)
 * @returns {Promise<Object>} Updated subscription object
 */
async function updateAutoRenewal(renewalData) {
  try {
    // Validate required fields
    const requiredFields = ['autoRenew'];
    const validation = validateRequiredFields(renewalData, requiredFields);
    
    if (!validation.isValid) {
      throw new Error(validation.message);
    }

    // Validate autoRenew is boolean
    if (typeof renewalData.autoRenew !== 'boolean') {
      throw new Error('autoRenew must be a boolean value');
    }

    const response = await apiBridge.patch(`${ENDPOINTS.SUBSCRIPTIONS.BASE}/auto-renewal`, renewalData, {
      clearCacheEndpoint: ENDPOINTS.SUBSCRIPTIONS.BASE
    });
    
    const result = processApiResponse(response, 'Auto-renewal setting updated successfully');
    return result.subscription || result;
  } catch (error) {
    handleError(error, 'SubscriptionService.updateAutoRenewal', true);
    throw error;
  }
}

export default updateAutoRenewal; 
