const NotificationService = require('../../services/notificationService');
const FirebaseService = require('../../services/firebaseService');
const User = require('../../models/user.model');
const Shop = require('../../models/shop.model');
const Notification = require('../../models/notification.model');
const { logInfo, logError } = require('../../utils');
const { validationResult } = require('express-validator');

/**
 * Push Notification Controller for SuperAdmin
 * Manages sending push notifications to Admin/Shop Owner mobile app users
 */
class PushNotificationController {

  /**
   * Send push notification to specific shop owners
   * POST /api/admin/notifications/push/shops
   */
  static async sendToShops(req, res) {
    try {
      // Validate request
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: errors.array()
        });
      }

      const {
        shopIds,
        title,
        message,
        priority = 'normal',
        actionUrl,
        actionLabel,
        scheduledAt
      } = req.body;

      const createdBy = req.user.userId;

      logInfo(`SuperAdmin ${createdBy} sending push notification to shops: ${shopIds.join(', ')}`, 'PushNotificationController');

      // Validate shops exist
      const shops = await Shop.find({ shopId: { $in: shopIds } }).select('shopId shopName');
      if (shops.length === 0) {
        return res.status(404).json({
          success: false,
          message: 'No valid shops found'
        });
      }

      const results = [];

      // Send notification to each shop
      for (const shop of shops) {
        try {
          const notificationData = {
            shopId: shop.shopId,
            recipient: 'shop_admins',
            recipientType: 'role',
            recipientName: `${shop.shopName} Admins`,
            type: 'Push',
            priority: priority,
            category: 'admin_communication',
            title: title,
            message: message,
            actionUrl: actionUrl,
            actionLabel: actionLabel,
            scheduledAt: scheduledAt ? new Date(scheduledAt) : undefined,
            relatedEntity: {
              type: 'shop',
              id: shop.shopId
            },
            metadata: {
              sentFromDashboard: true,
              targetShops: shopIds
            },
            createdBy: createdBy
          };

          const notification = await NotificationService.createNotification(notificationData);
          const success = await NotificationService.processNotification(notification.notificationId);

          results.push({
            shopId: shop.shopId,
            shopName: shop.shopName,
            notificationId: notification.notificationId,
            success: success
          });
        } catch (shopError) {
          logError(`Failed to send notification to shop ${shop.shopId}: ${shopError.message}`, 'PushNotificationController');
          results.push({
            shopId: shop.shopId,
            shopName: shop.shopName,
            success: false,
            error: shopError.message
          });
        }
      }

      const successCount = results.filter(r => r.success).length;
      const failureCount = results.length - successCount;

      res.status(200).json({
        success: successCount > 0,
        message: `Notification sent to ${successCount}/${results.length} shops`,
        data: {
          totalShops: results.length,
          successful: successCount,
          failed: failureCount,
          results: results
        }
      });
    } catch (error) {
      logError(`Failed to send shop notifications: ${error.message}`, 'PushNotificationController', error);
      res.status(500).json({
        success: false,
        message: 'Failed to send notifications',
        error: error.message
      });
    }
  }

  /**
   * Send broadcast notification to all admins
   * POST /api/admin/notifications/push/broadcast
   */
  static async sendBroadcast(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: errors.array()
        });
      }

      const {
        title,
        message,
        priority = 'normal',
        actionUrl,
        actionLabel,
        scheduledAt
      } = req.body;

      const createdBy = req.user.userId;

      logInfo(`SuperAdmin ${createdBy} sending broadcast push notification`, 'PushNotificationController');

      const notificationData = {
        shopId: 'SYSTEM',
        recipient: 'all_admins',
        recipientType: 'broadcast',
        recipientName: 'All Admins',
        type: 'Push',
        priority: priority,
        category: 'system_broadcast',
        title: title,
        message: message,
        actionUrl: actionUrl,
        actionLabel: actionLabel,
        scheduledAt: scheduledAt ? new Date(scheduledAt) : undefined,
        relatedEntity: {
          type: 'system',
          id: 'broadcast'
        },
        metadata: {
          sentFromDashboard: true,
          broadcastType: 'all_admins'
        },
        createdBy: createdBy
      };

      const notification = await NotificationService.createNotification(notificationData);
      const success = await NotificationService.processNotification(notification.notificationId);

      if (success) {
        res.status(200).json({
          success: true,
          message: 'Broadcast notification sent successfully',
          data: {
            notificationId: notification.notificationId
          }
        });
      } else {
        res.status(500).json({
          success: false,
          message: 'Failed to send broadcast notification'
        });
      }
    } catch (error) {
      logError(`Failed to send broadcast notification: ${error.message}`, 'PushNotificationController', error);
      res.status(500).json({
        success: false,
        message: 'Failed to send broadcast notification',
        error: error.message
      });
    }
  }

  /**
   * Send simple debt reminders
   * POST /api/admin/notifications/push/debt-reminders
   */
  static async sendDebtReminders(req, res) {
    try {
      const { reminderType, shopIds } = req.body;
      const createdBy = req.user.userId;

      // Validate reminder type
      if (!['7_days', '3_days', 'overdue'].includes(reminderType)) {
        return res.status(400).json({
          success: false,
          message: 'Invalid reminder type. Use: 7_days, 3_days, or overdue'
        });
      }

      const DebtNotificationService = require('../../services/debtNotificationService');
      
      let results = [];

      if (shopIds && shopIds.length > 0) {
        // Send to specific shops
        for (const shopId of shopIds) {
          const result = await DebtNotificationService.sendDebtReminders(reminderType, shopId);
          results.push({
            shopId: shopId,
            success: result.success,
            totalDebts: result.totalDebts || 0,
            message: result.message
          });
        }
      } else {
        // Send to all shops
        const result = await DebtNotificationService.sendDebtReminders(reminderType);
        results.push({
          shopId: 'all',
          success: result.success,
          totalDebts: result.totalDebts || 0,
          totalShops: result.totalShops || 0,
          message: result.message
        });
      }

      const successCount = results.filter(r => r.success).length;

      res.status(200).json({
        success: successCount > 0,
        message: `${reminderType} debt reminders processed`,
        data: {
          reminderType: reminderType,
          results: results,
          successful: successCount,
          failed: results.length - successCount
        }
      });

    } catch (error) {
      logError(`Failed to send debt reminders: ${error.message}`, 'PushNotificationController', error);
      res.status(500).json({
        success: false,
        message: 'Failed to send debt reminders',
        error: error.message
      });
    }
  }

  /**
   * Get notification statistics
   * GET /api/admin/notifications/push/stats
   */
  static async getNotificationStats(req, res) {
    try {
      const { shopId, days = 30 } = req.query;

      const stats = await NotificationService.getNotificationStats(shopId, parseInt(days));

      res.status(200).json({
        success: true,
        message: 'Notification statistics retrieved',
        data: stats
      });
    } catch (error) {
      logError(`Failed to get notification stats: ${error.message}`, 'PushNotificationController', error);
      res.status(500).json({
        success: false,
        message: 'Failed to retrieve notification statistics',
        error: error.message
      });
    }
  }

  /**
   * Test Firebase connection
   * GET /api/admin/notifications/push/test
   */
  static async testFirebaseConnection(req, res) {
    try {
      const result = await FirebaseService.testConnection();

      res.status(result.success ? 200 : 500).json({
        success: result.success,
        message: result.message
      });
    } catch (error) {
      logError(`Firebase connection test failed: ${error.message}`, 'PushNotificationController', error);
      res.status(500).json({
        success: false,
        message: 'Firebase connection test failed',
        error: error.message
      });
    }
  }

  /**
   * Get notification history
   * GET /api/admin/notifications/push/history
   */
  static async getNotificationHistory(req, res) {
    try {
      const { shopId, limit = 50, offset = 0, status, type } = req.query;

      // Build query for notifications
      const query = { 
        isDeleted: false,
        // Only show notifications that have been processed (not just created)
        status: { $in: ['sent', 'delivered', 'failed', 'expired'] }
      };
      
      if (shopId) {
        query.shopId = shopId;
      }
      
      if (status && status !== 'all') {
        query.status = status;
      }
      
      if (type && type !== 'all') {
        query.type = type;
      }

      // Get notifications with pagination
      const notifications = await Notification.find(query)
        .sort({ createdAt: -1 })
        .limit(parseInt(limit))
        .skip(parseInt(offset))
        .select('notificationId shopId recipient recipientType type priority category title message status sentAt deliveredAt createdAt actionUrl metadata');

      // Get total count for pagination
      const totalCount = await Notification.countDocuments(query);

      // Format notifications for frontend
      const formattedNotifications = notifications.map(notification => ({
        id: notification.notificationId,
        type: notification.category || notification.type.toLowerCase(),
        title: notification.title,
        message: notification.message,
        status: notification.status,
        priority: notification.priority,
        sentAt: notification.sentAt || notification.createdAt,
        deliveredAt: notification.deliveredAt,
        recipient: notification.recipient,
        recipientType: notification.recipientType,
        shopId: notification.shopId,
        actionUrl: notification.actionUrl,
        metadata: notification.metadata || {}
      }));

      res.status(200).json({
        success: true,
        message: 'Notification history retrieved',
        data: {
          notifications: formattedNotifications,
          pagination: {
            total: totalCount,
            limit: parseInt(limit),
            offset: parseInt(offset),
            hasMore: (parseInt(offset) + parseInt(limit)) < totalCount
          }
        }
      });
    } catch (error) {
      logError(`Failed to get notification history: ${error.message}`, 'PushNotificationController', error);
      res.status(500).json({
        success: false,
        message: 'Failed to retrieve notification history',
        error: error.message
      });
    }
  }

  /**
   * Get list of shops with admin users for targeting
   * GET /api/admin/notifications/push/targets
   */
  static async getNotificationTargets(req, res) {
    try {
      // 🔍 DEBUG: Check basic database state first - Fixed shop query
      const allShops = await Shop.find({ status: 'active' }).select('shopId shopName status');
      const allUsers = await User.find({ 
        role: { $in: ['admin', 'employee'] }, 
        status: 'active',                    // ✅ Use status field instead of isActive
        isSuspended: false,                  // ✅ Ensure not suspended
        isDeleted: false                     // ✅ Ensure not deleted
      }).select('shopId role fullName fcmTokens status');
      
      logInfo(`DEBUG - Total active shops: ${allShops.length}`, 'PushNotificationController');
      logInfo(`DEBUG - Total admin/employee users: ${allUsers.length}`, 'PushNotificationController');
      logInfo(`DEBUG - Users with FCM tokens: ${allUsers.filter(u => u.fcmTokens && u.fcmTokens.length > 0).length}`, 'PushNotificationController');
      
      // Get shops with active admin users - Fixed aggregation pipeline
      const shops = await Shop.aggregate([
        {
          $match: { status: 'active' }        // ✅ Use status field for shops too
        },
        {
          $lookup: {
            from: 'users',
            localField: 'shopId',
            foreignField: 'shopId',
            as: 'admins'
          }
        },
        {
          $addFields: {
            activeAdmins: {
              $filter: {
                input: '$admins',
                cond: {
                  $and: [
                    { $eq: ['$$this.status', 'active'] },
                    { $eq: ['$$this.isSuspended', false] },
                    { $eq: ['$$this.isDeleted', false] },
                    { $in: ['$$this.role', ['admin', 'employee']] },
                    { $gt: [{ $size: { $ifNull: ['$$this.fcmTokens', []] } }, 0] }
                  ]
                }
              }
            }
          }
        },
        {
          $project: {
            shopId: 1,
            shopName: 1,
            adminCount: { $size: '$activeAdmins' },
            deviceCount: {
              $sum: {
                $map: {
                  input: '$activeAdmins',
                  as: 'admin',
                  in: { $size: { $ifNull: ['$$admin.fcmTokens', []] } }
                }
              }
            }
          }
        },
        {
          $match: { adminCount: { $gt: 0 } }
        },
        {
          $sort: { shopName: 1 }
        }
      ]);

      res.status(200).json({
        success: true,
        message: 'Notification targets retrieved',
        data: {
          totalShops: shops.length,
          shops: shops,
          // 🔍 DEBUG: Include raw database state
          debug: {
            totalShopsInDB: allShops.length,
            totalAdminUsersInDB: allUsers.length,
            usersWithFCMTokens: allUsers.filter(u => u.fcmTokens && u.fcmTokens.length > 0).length,
            shopsInDB: allShops.map(s => ({ shopId: s.shopId, shopName: s.shopName })),
            adminUsers: allUsers.map(u => ({ 
              shopId: u.shopId, 
              role: u.role, 
              fullName: u.fullName, 
              hasFCMTokens: !!(u.fcmTokens && u.fcmTokens.length > 0)
            }))
          }
        }
      });
    } catch (error) {
      logError(`Failed to get notification targets: ${error.message}`, 'PushNotificationController', error);
      res.status(500).json({
        success: false,
        message: 'Failed to retrieve notification targets',
        error: error.message
      });
    }
  }
}

module.exports = PushNotificationController; 