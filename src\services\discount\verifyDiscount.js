/**
 * Verify Discount Module
 * Handles discount code verification for registration
 */
const { AppError, logError } = require('../../utils');

// Import getDiscount to avoid circular dependencies
const { getDiscountByCode } = require('./getDiscount');

/**
 * Verify a discount code for registration
 * @param {String} code - Discount code to verify
 * @returns {Promise<Object>} Verification result with valid flag, message, and code details
 */
const verifyDiscountCode = async (code) => {
  try {
    // Skip validation if no code provided
    if (!code || code.trim() === '') {
      return {
        valid: false,
        message: 'No discount code provided'
      };
    }

    // Get the discount code
    let discountCode;
    try {
      discountCode = await getDiscountByCode(code);
    } catch (error) {
      // If code not found
      if (error.code === 'discount_not_found') {
        return {
          valid: false,
          message: 'Invalid discount code'
        };
      }
      throw error;
    }
    
    // Check if valid
    if (!discountCode.isValid()) {
      return {
        valid: false,
        message: 'Discount code is expired or inactive'
      };
    }
    
    // Check if applicable for subscription context
    if (!discountCode.applicableFor.includes('all') && 
        !discountCode.applicableFor.includes('subscription')) {
      return {
        valid: false,
        message: 'Discount code is not applicable for subscriptions'
      };
    }
    
    // Format response for registration controller
    return {
      valid: true,
      code: discountCode.code,
      discountId: discountCode.discountId,
      type: discountCode.type,
      value: discountCode.value,
      description: discountCode.description
    };
  } catch (error) {
    logError(`Failed to verify discount code: ${error.message}`, 'DiscountService', error);
    throw new AppError('Failed to verify discount code', 500, 'discount_verification_error');
  }
};

module.exports = verifyDiscountCode;
