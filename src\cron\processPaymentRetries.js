/**
 * Payment Retry Cron Job
 * Automatically processes pending payment retries on a schedule
 */
const { SubscriptionService } = require('../services');
const { logInfo, logError, logSuccess } = require('../utils/logger');

/**
 * Process all pending payment retries
 * This function is designed to be called by a cron scheduler
 */
async function runPaymentRetryProcess() {
  try {
    logInfo('Starting scheduled payment retry processing job', 'PaymentRetryCron');
    
    // Process all pending retries
    const results = await SubscriptionService.processPendingRetries();
    
    // Log the results
    logSuccess(
      `Processed ${results.total} payment retries. Success: ${results.successful}, Failed: ${results.failed}`, 
      'PaymentRetryCron'
    );
    
    return results;
  } catch (error) {
    logError('Error in payment retry processing job', 'PaymentRetryCron', error);
    throw error;
  }
}

// Execute if called directly from command line
if (require.main === module) {
  runPaymentRetryProcess()
    .then(results => {
      console.log(`Processed ${results.total} payment retries`);
      process.exit(0);
    })
    .catch(err => {
      console.error('Error processing payment retries:', err);
      process.exit(1);
    });
}

module.exports = runPaymentRetryProcess;
