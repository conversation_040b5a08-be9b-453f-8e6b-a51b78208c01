"use client"

import { useState } from 'react'
import { Camera, Crown, Shield, Calendar, MapPin } from 'lucide-react'
import { But<PERSON> } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Card, CardContent } from '@/components/ui/card'
import { cn } from '@/lib/utils'

export function ProfileHeader({ user }) {
  const [isEditingAvatar, setIsEditingAvatar] = useState(false)

  // Get user initials
  const getInitials = (name) => {
    return name
      ?.split(' ')
      .map(n => n[0])
      .join('')
      .toUpperCase() || 'U'
  }

  // Format join date
  const formatJoinDate = (date) => {
    if (!date) return 'Recently joined'
    return new Intl.DateTimeFormat('en-US', {
      month: 'long',
      year: 'numeric'
    }).format(new Date(date))
  }

  // Get role badge color
  const getRoleBadgeColor = (role) => {
    switch (role?.toLowerCase()) {
      case 'superadmin':
        return 'bg-gradient-to-r from-purple-500 to-pink-500 text-white'
      case 'admin':
        return 'bg-gradient-to-r from-blue-500 to-cyan-500 text-white'
      case 'employee':
        return 'bg-gradient-to-r from-green-500 to-emerald-500 text-white'
      default:
        return 'bg-gradient-to-r from-gray-500 to-slate-500 text-white'
    }
  }

  // Get role icon
  const getRoleIcon = (role) => {
    switch (role?.toLowerCase()) {
      case 'superadmin':
        return Crown
      case 'admin':
      case 'employee':
        return Shield
      default:
        return Shield
    }
  }

  const RoleIcon = getRoleIcon(user?.role)

  return (
    <Card className="relative overflow-hidden border-0 bg-gradient-to-br from-background via-background to-muted/20">
      {/* Background Pattern */}
      <div className="absolute inset-0 bg-grid-pattern opacity-5" />
      
      {/* Header Content */}
      <CardContent className="relative p-6 md:p-8">
        <div className="flex flex-col md:flex-row items-start md:items-center gap-6">
          {/* Avatar Section */}
          <div className="relative group">
            <div className="relative">
              <Avatar className="h-24 w-24 md:h-32 md:w-32 ring-4 ring-background shadow-xl">
                <AvatarImage 
                  src={user?.profilePicture} 
                  alt={user?.fullName || 'User'} 
                />
                <AvatarFallback className="text-2xl md:text-3xl font-bold bg-gradient-to-br from-primary to-secondary text-primary-foreground">
                  {getInitials(user?.fullName)}
                </AvatarFallback>
              </Avatar>
              
              {/* Avatar Edit Button */}
              <Button
                size="icon"
                variant="secondary"
                className={cn(
                  "absolute -bottom-2 -right-2 h-8 w-8 rounded-full shadow-lg transition-all duration-200",
                  "opacity-0 group-hover:opacity-100 scale-90 group-hover:scale-100"
                )}
                onClick={() => setIsEditingAvatar(true)}
              >
                <Camera className="h-4 w-4" />
              </Button>
            </div>
          </div>

          {/* User Info */}
          <div className="flex-1 space-y-4">
            <div className="space-y-2">
              <div className="flex flex-col sm:flex-row sm:items-center gap-3">
                <h1 className="text-3xl md:text-4xl font-bold text-foreground">
                  {user?.fullName || 'User Name'}
                </h1>
                <Badge 
                  className={cn(
                    "w-fit text-xs font-medium px-3 py-1 border-0",
                    getRoleBadgeColor(user?.role)
                  )}
                >
                  <RoleIcon className="h-3 w-3 mr-1" />
                  {user?.role || 'User'}
                </Badge>
              </div>
              
              <p className="text-lg text-muted-foreground">
                {user?.email || '<EMAIL>'}
              </p>
            </div>

            {/* User Stats */}
            <div className="flex flex-wrap gap-4 text-sm text-muted-foreground">
              <div className="flex items-center gap-1">
                <Calendar className="h-4 w-4" />
                <span>Joined {formatJoinDate(user?.createdAt)}</span>
              </div>
              {user?.shopId && (
                <div className="flex items-center gap-1">
                  <MapPin className="h-4 w-4" />
                  <span>Shop ID: {user.shopId}</span>
                </div>
              )}
            </div>

            {/* Quick Stats */}
            <div className="flex flex-wrap gap-6 pt-2">
              <div className="text-center">
                <div className="text-2xl font-bold text-foreground">
                  {user?.status === 'active' ? 'Active' : user?.status || 'Unknown'}
                </div>
                <div className="text-xs text-muted-foreground">Account Status</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-foreground">
                  {user?.loginHistory?.length || '0'}
                </div>
                <div className="text-xs text-muted-foreground">Login Sessions</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-foreground">
                  {user?.lastLoginAt ? new Date(user.lastLoginAt).toLocaleDateString() : 'Never'}
                </div>
                <div className="text-xs text-muted-foreground">Last Login</div>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
} 