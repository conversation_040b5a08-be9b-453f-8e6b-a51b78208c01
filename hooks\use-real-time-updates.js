import { useState, useEffect, useRef } from 'react';
import { realTimeUpdatesService } from '@/lib/services/realTimeUpdates';

export function useRealTimeUpdates(dataType, callback, options = {}) {
  const [isConnected, setIsConnected] = useState(false);
  const [lastUpdate, setLastUpdate] = useState(null);
  const [error, setError] = useState(null);
  const subscriptionRef = useRef(null);

  const {
    autoConnect = true,
    onError = null,
    onConnect = null,
    onDisconnect = null
  } = options;

  useEffect(() => {
    if (autoConnect && dataType && callback) {
      const unsubscribe = realTimeUpdatesService.subscribe(dataType, callback);
      subscriptionRef.current = unsubscribe;
      setIsConnected(true);
      onConnect?.();
    }

    return () => {
      if (subscriptionRef.current) {
        subscriptionRef.current();
        subscriptionRef.current = null;
        setIsConnected(false);
        onDisconnect?.();
      }
    };
  }, [dataType, callback, autoConnect]);

  const connect = () => {
    if (dataType && callback && !subscriptionRef.current) {
      const unsubscribe = realTimeUpdatesService.subscribe(dataType, callback);
      subscriptionRef.current = unsubscribe;
      setIsConnected(true);
      onConnect?.();
    }
  };

  const disconnect = () => {
    if (subscriptionRef.current) {
      subscriptionRef.current();
      subscriptionRef.current = null;
      setIsConnected(false);
      onDisconnect?.();
    }
  };

  return {
    isConnected,
    lastUpdate,
    error,
    connect,
    disconnect
  };
}

export function useRealTimeUpdater() {
  const triggerUpdate = (dataType, data = null, options = {}) => {
    return realTimeUpdatesService.triggerUpdate(dataType, data, options);
  };

  const invalidateCache = (dataTypes) => {
    return realTimeUpdatesService.invalidateCache(dataTypes);
  };

  const isDataStale = (dataType, maxAge = 300000) => {
    return realTimeUpdatesService.isDataStale(dataType, maxAge);
  };

  const getLastUpdateTime = (dataType) => {
    return realTimeUpdatesService.getLastUpdateTime(dataType);
  };

  return {
    triggerUpdate,
    invalidateCache,
    isDataStale,
    getLastUpdateTime
  };
}

export function useAutoRefresh(dataType, refreshFunction, options = {}) {
  const {
    interval = 30000, // 30 seconds default
    enabled = true,
    onlyWhenVisible = true
  } = options;

  useEffect(() => {
    if (!enabled || !refreshFunction) return;

    const refresh = () => {
      // Check if page is visible if onlyWhenVisible is true
      if (onlyWhenVisible && document.hidden) return;
      
      try {
        refreshFunction();
      } catch (error) {
        console.error(`Auto refresh error for ${dataType}:`, error);
      }
    };

    // Set up interval
    const intervalId = setInterval(refresh, interval);

    // Clean up
    return () => {
      clearInterval(intervalId);
    };
  }, [dataType, refreshFunction, interval, enabled, onlyWhenVisible]);
}
