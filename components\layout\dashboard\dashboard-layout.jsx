"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { cn } from "@/lib/utils";
import { useResponsive } from "@/hooks/use-responsive";
import { useAuth } from "@/contexts/auth-context";
import { SettingsProvider } from "@/contexts/settings";
import { Sidebar } from "./sidebar";
import { Header } from "./header";
import { Footer } from "./footer";
import { toast } from "sonner";

export function DashboardLayout({ children }) {
  const router = useRouter();
  const { isAuthenticated, isSuperAdmin, user, loading } = useAuth();
  const [sidebarOpen, setSidebarOpen] = useState(true);
  const { isMobile, isTablet } = useResponsive();
  
  // Close sidebar on mobile by default
  useEffect(() => {
    if (isMobile) {
      setSidebarOpen(false);
    } else {
      setSidebarOpen(true);
    }
  }, [isMobile]);
  
  // Handle authentication
  useEffect(() => {
    if (!loading) {
      if (!isAuthenticated) {
        router.push("/login");
      } else if (!isSuperAdmin) {
        toast.error("Access restricted. This web application is for superadmins only.");
        router.push("/unauthorized");
      }
    }
  }, [isAuthenticated, isSuperAdmin, loading, router]);
  
  // Toggle mobile sidebar
  const toggleSidebar = () => {
    setSidebarOpen(!sidebarOpen);
  };
  
  // If still loading auth state or not authenticated, show loading state
  if (loading || !isAuthenticated || !isSuperAdmin) {
    return (
      <div className="flex h-screen w-full items-center justify-center bg-background">
        <div className="flex flex-col items-center gap-2">
          <div className="h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent"></div>
          <p className="text-sm text-muted-foreground">Loading...</p>
        </div>
      </div>
    );
  }
  
  return (
    <SettingsProvider>
      {/* Fixed height container - no overflow hidden to prevent scroll issues */}
      <div className="flex h-screen bg-background">
        {/* Sidebar - Fixed position with proper scroll only inside sidebar content */}
        <div 
          className={cn(
            "fixed inset-y-0 left-0 z-50 transform transition-transform duration-300 lg:relative lg:translate-x-0",
            sidebarOpen ? "translate-x-0" : "-translate-x-full",
            isMobile && sidebarOpen && "w-[15rem]"
          )}
        >
          <Sidebar />
        </div>
        
        {/* Mobile sidebar overlay */}
        {isMobile && sidebarOpen && (
          <div 
            className="fixed inset-0 z-40 bg-black/50 transition-opacity lg:hidden"
            onClick={toggleSidebar}
          />
        )}
        
        {/* Main content area - Single scroll container */}
        <div className="flex flex-1 flex-col min-h-0">
          {/* Fixed Header */}
          <Header onToggleSidebar={toggleSidebar} />
          
          {/* Main scrollable content - SINGLE scroll container with horizontal overflow prevention */}
          <main className="flex-1 single-scroll-container">
            <div className="p-4 lg:p-6 min-w-0 prevent-double-scroll">
              <div className="mx-auto w-full max-w-7xl min-w-0">
                {children}
              </div>
            </div>
          </main>
          
          {/* Fixed Footer */}
          <Footer />
        </div>
      </div>
    </SettingsProvider>
  );
}
