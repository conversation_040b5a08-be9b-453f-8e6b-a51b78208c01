"use client"

import { Logo, LogoIcon, LogoText, LogoFull } from "./logo"
import { Card, CardContent, CardHeader, CardTitle } from "./card"

/**
 * Logo Test Component
 * Demonstrates all logo variants and sizes for testing purposes
 * Use this component to verify logo integration across light/dark themes
 */
export function LogoTest() {
  const sizes = ["xs", "sm", "md", "lg", "xl"]
  const variants = ["icon", "text", "full"]

  return (
    <div className="space-y-8 p-8">
      <div className="text-center">
        <h1 className="text-3xl font-bold mb-2">DeynCare Logo Integration Test</h1>
        <p className="text-muted-foreground">
          Testing all logo variants and sizes across light/dark themes
        </p>
      </div>

      {/* Logo Variants Grid */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {variants.map((variant) => (
          <Card key={variant} className="p-4">
            <CardHeader>
              <CardTitle className="capitalize">{variant} Variant</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {sizes.map((size) => (
                <div key={size} className="flex items-center justify-center p-4 border rounded-lg">
                  <div className="text-center space-y-2">
                    <Logo variant={variant} size={size} href={null} />
                    <p className="text-xs text-muted-foreground">{size}</p>
                  </div>
                </div>
              ))}
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Quick Access Components */}
      <Card className="p-6">
        <CardHeader>
          <CardTitle>Quick Access Components</CardTitle>
        </CardHeader>
        <CardContent className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="text-center p-4 border rounded-lg">
            <LogoIcon size="lg" />
            <p className="text-sm mt-2">LogoIcon</p>
          </div>
          <div className="text-center p-4 border rounded-lg">
            <LogoText size="lg" />
            <p className="text-sm mt-2">LogoText</p>
          </div>
          <div className="text-center p-4 border rounded-lg">
            <LogoFull size="lg" />
            <p className="text-sm mt-2">LogoFull</p>
          </div>
        </CardContent>
      </Card>

      {/* Usage Examples */}
      <Card className="p-6">
        <CardHeader>
          <CardTitle>Common Usage Examples</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Sidebar Example */}
          <div className="border rounded-lg p-4 bg-card">
            <h3 className="font-medium mb-3">Sidebar Usage</h3>
            <div className="bg-background border rounded p-3">
              <Logo variant="full" size="sm" showText={false} />
            </div>
            <pre className="text-xs mt-2 bg-muted p-2 rounded">
              {`<Logo variant="full" size="sm" showText={false} />`}
            </pre>
          </div>

          {/* Auth Page Example */}
          <div className="border rounded-lg p-4 bg-card">
            <h3 className="font-medium mb-3">Auth Page Usage</h3>
            <div className="bg-background border rounded p-6 text-center">
              <Logo variant="full" size="lg" href={null} showText={true} />
            </div>
            <pre className="text-xs mt-2 bg-muted p-2 rounded">
              {`<Logo variant="full" size="lg" href={null} showText={true} />`}
            </pre>
          </div>

          {/* Mobile Header Example */}
          <div className="border rounded-lg p-4 bg-card">
            <h3 className="font-medium mb-3">Mobile Header Usage</h3>
            <div className="bg-background border rounded p-3">
              <Logo variant="icon" size="sm" />
            </div>
            <pre className="text-xs mt-2 bg-muted p-2 rounded">
              {`<Logo variant="icon" size="sm" />`}
            </pre>
          </div>
        </CardContent>
      </Card>

      {/* Theme Testing */}
      <Card className="p-6">
        <CardHeader>
          <CardTitle>Dark/Light Theme Testing</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Light Theme Simulation */}
            <div className="border rounded-lg p-4 bg-white text-black">
              <h4 className="font-medium mb-3">Light Theme</h4>
              <div className="space-y-3">
                <Logo variant="full" size="md" href={null} />
                <Logo variant="icon" size="md" href={null} />
              </div>
            </div>
            
            {/* Dark Theme Simulation */}
            <div className="border rounded-lg p-4 bg-gray-900 text-white">
              <h4 className="font-medium mb-3">Dark Theme</h4>
              <div className="space-y-3">
                <Logo variant="full" size="md" href={null} />
                <Logo variant="icon" size="md" href={null} />
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

export default LogoTest 