const { 
  AppError,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Log<PERSON><PERSON>per,
  logSuccess,
  logError
} = require('../../utils');

/**
 * Get user profile
 */
const getProfile = async (userId) => {
  try {
    // Use UserHelper to find and sanitize user data
    const user = await UserHelper.findActiveUser(userId);
    
    // Log profile access
    await LogHelper.createAuthLog('profile_viewed', {
      actorId: userId,
      targetId: userId,
      actorRole: user.role,
      shopId: user.shopId || null
    });
    
    // Return sanitized user data
    return UserHelper.sanitizeUser(user);
  } catch (error) {
    if (error instanceof AppError) {
      throw error;
    }
    
    logError(`Get profile error for user ${userId}: ${error.message}`, 'AuthService', error);
    throw new AppError('Failed to retrieve user profile', 500, 'profile_fetch_error');
  }
};

/**
 * Update user profile
 */
const updateProfile = async (userId, updateData) => {
  try {
    const { User } = require('../../models');
    
    // Validate input
    if (!userId) {
      throw new AppError('User ID is required', 400, 'missing_user_id');
    }
    
    if (!updateData || Object.keys(updateData).length === 0) {
      throw new AppError('Update data is required', 400, 'missing_update_data');
    }
    
    // Find the user first
    const user = await UserHelper.findActiveUser(userId);
    
    // Prepare update data - only allow specific fields to be updated
    const allowedFields = ['fullName', 'phone'];
    const updateFields = {};
    
    allowedFields.forEach(field => {
      if (updateData[field] !== undefined) {
        updateFields[field] = updateData[field];
      }
    });
    
    if (Object.keys(updateFields).length === 0) {
      throw new AppError('No valid fields to update', 400, 'no_valid_fields');
    }
    
    // Add updatedAt timestamp
    updateFields.updatedAt = new Date();
    
    // Update the user
    const updatedUser = await User.findOneAndUpdate(
      { userId },
      { $set: updateFields },
      { new: true, runValidators: true }
    );
    
    if (!updatedUser) {
      throw new AppError('User not found', 404, 'user_not_found');
    }
    
    // Log profile update
    await LogHelper.createAuthLog('profile_updated', {
      actorId: userId,
      targetId: userId,
      actorRole: updatedUser.role,
      shopId: updatedUser.shopId || null,
      changes: updateFields
    });
    
    // Return sanitized updated user data
    return UserHelper.sanitizeUser(updatedUser);
  } catch (error) {
    if (error instanceof AppError) {
      throw error;
    }
    
    logError(`Update profile error for user ${userId}: ${error.message}`, 'ProfileService', error);
    throw new AppError('Failed to update user profile', 500, 'profile_update_error');
  }
};

module.exports = {
  getProfile,
  updateProfile
};
