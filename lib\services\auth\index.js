/**
 * Auth Service - Centralized authentication service
 * 
 * This service wraps the API bridge auth methods and provides
 * additional functionality like token management and error handling.
 * It's designed to work alongside your existing auth context without
 * requiring changes to your components.
 */

import apiBridge from '@/lib/api/bridge';
import { ENDPOINTS } from '@/lib/api/contract';
import { toast } from 'sonner';
import BaseService from '@/lib/services/baseService';

/**
 * AuthService provides auth-related API operations using the bridge
 */
const AuthService = {
  /**
   * Log in a user with email and password
   * @param {string} email - User email
   * @param {string} password - User password
   * @returns {Promise<Object>} User data with tokens
   */
  async login(email, password) {
    try {
      const response = await apiBridge.post(ENDPOINTS.AUTH.LOGIN, { email, password });
      
      // Handle token storage (now also handled by the bridge)
      const { accessToken, refreshToken, user } = response.data.data;
      
      // The bridge will store tokens, but we'll also maintain compatibility
      // with the existing implementation
      if (typeof window !== 'undefined') {
        localStorage.setItem('accessToken', accessToken);
        localStorage.setItem('refreshToken', refreshToken);
      }
      
      return response.data.data;
    } catch (error) {
      BaseService.handleError(error, 'AuthService.login', true);
      throw error;
    }
  },

  /**
   * Log out the current user
   * @param {string} refreshToken - Optional refresh token, will use the stored one if not provided
   * @returns {Promise<void>}
   */
  async logout(refreshToken) {
    try {
      // Use provided token or get from storage
      const token = refreshToken || (typeof window !== 'undefined' ? localStorage.getItem('refreshToken') : null);
      
      if (token) {
        await apiBridge.post(ENDPOINTS.AUTH.LOGOUT, { refreshToken: token });
      }
      
      // Clear tokens from storage and bridge
      if (typeof window !== 'undefined') {
        localStorage.removeItem('accessToken');
        localStorage.removeItem('refreshToken');
      }
      
      // Clear tokens in the bridge
      apiBridge.token.clearTokens();
      
    } catch (error) {
      // Still clear tokens even if the API call fails
      if (typeof window !== 'undefined') {
        localStorage.removeItem('accessToken');
        localStorage.removeItem('refreshToken');
      }
      
      // Clear tokens in the bridge
      apiBridge.token.clearTokens();
      
      BaseService.handleError(error, 'AuthService.logout');
      // Don't rethrow the error as logout should always "succeed" from the UI perspective
    }
  },

  /**
   * Log out from all devices
   * @returns {Promise<void>}
   */
  async logoutAll() {
    try {
      await apiBridge.post(ENDPOINTS.AUTH.LOGOUT_ALL);
      
      // Clear tokens from storage and bridge
      if (typeof window !== 'undefined') {
        localStorage.removeItem('accessToken');
        localStorage.removeItem('refreshToken');
      }
      
      // Clear tokens in the bridge
      apiBridge.token.clearTokens();
    } catch (error) {
      // Still clear tokens even if the API call fails
      if (typeof window !== 'undefined') {
        localStorage.removeItem('accessToken');
        localStorage.removeItem('refreshToken');
      }
      
      // Clear tokens in the bridge
      apiBridge.token.clearTokens();
      
      BaseService.handleError(error, 'AuthService.logoutAll');
      // Don't rethrow the error as logout should always "succeed" from the UI perspective
    }
  },

  /**
   * Request a password reset email
   * @param {string} email - User email
   * @returns {Promise<Object>} Response data
   */
  async forgotPassword(email) {
    try {
      const response = await apiBridge.post(ENDPOINTS.AUTH.FORGOT_PASSWORD, { email });
      return response.data;
    } catch (error) {
      BaseService.handleError(error, 'AuthService.forgotPassword', true);
      throw error;
    }
  },

  /**
   * Reset password using token from email
   * @param {string} token - Reset token
   * @param {string} newPassword - New password
   * @param {string} confirmPassword - Confirm new password
   * @returns {Promise<Object>} Response data
   */
  async resetPassword(token, newPassword, confirmPassword) {
    try {
      const response = await apiBridge.post(ENDPOINTS.AUTH.RESET_PASSWORD, { 
        token, 
        newPassword, 
        confirmPassword 
      });
      return response.data;
    } catch (error) {
      BaseService.handleError(error, 'AuthService.resetPassword', true);
      throw error;
    }
  },

  /**
   * Change password while logged in
   * @param {string} currentPassword - Current password
   * @param {string} newPassword - New password
   * @returns {Promise<Object>} Response data
   */
  async changePassword(currentPassword, newPassword) {
    try {
      const response = await apiBridge.post(ENDPOINTS.AUTH.CHANGE_PASSWORD, { 
        currentPassword, 
        newPassword 
      });
      return response.data;
    } catch (error) {
      BaseService.handleError(error, 'AuthService.changePassword', true);
      throw error;
    }
  },

  /**
   * Get current user profile
   * @returns {Promise<Object>} User data
   */
  async getProfile() {
    try {
      const response = await apiBridge.get(ENDPOINTS.AUTH.PROFILE);
      return response.data.data;
    } catch (error) {
      // Don't show toast for auth check failures as these are common and expected
      BaseService.handleError(error, 'AuthService.getProfile', false);
      throw error;
    }
  },

  /**
   * Check if user is authenticated
   * Attempts to get the user profile and returns true if successful
   * @returns {Promise<boolean>} Authentication status
   */
  async isAuthenticated() {
    try {
      await this.getProfile();
      return true;
    } catch (error) {
      return false;
    }
  },

  /**
   * Get access token from storage
   * @returns {string|null} Access token or null if not found
   */
  getAccessToken() {
    if (typeof window !== 'undefined') {
      return localStorage.getItem('accessToken');
    }
    return null;
  },

  /**
   * Get refresh token from storage
   * @returns {string|null} Refresh token or null if not found
   */
  getRefreshToken() {
    if (typeof window !== 'undefined') {
      return localStorage.getItem('refreshToken');
    }
    return null;
  },

  /**
   * Set tokens in storage and bridge
   * @param {string} accessToken - Access token
   * @param {string} refreshToken - Refresh token
   */
  setTokens(accessToken, refreshToken) {
    // Set in local storage
    if (typeof window !== 'undefined') {
      localStorage.setItem('accessToken', accessToken);
      localStorage.setItem('refreshToken', refreshToken);
    }
    
    // Set in bridge
    apiBridge.token.setTokens(accessToken, refreshToken);
  }
};

export default AuthService;
