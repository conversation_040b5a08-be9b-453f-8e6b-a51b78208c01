"use client";

import Link from "next/link";
import { cn } from "@/lib/utils";

export function Footer({ className }) {
  const currentYear = new Date().getFullYear();
  
  return (
    <footer 
      className={cn(
        "w-full border-t border-border bg-background py-4 px-6",
        className
      )}
    >
      <div className="container flex flex-col md:flex-row items-center justify-between text-center md:text-left gap-4">
        <div className="text-sm text-muted-foreground">
          &copy; {currentYear} DeynCare. All rights reserved.
        </div>
        
        <div className="flex items-center space-x-4 text-sm text-muted-foreground">
          <Link href="/terms" className="hover:text-foreground transition-colors">
            Terms
          </Link>
          <Link href="/privacy" className="hover:text-foreground transition-colors">
            Privacy
          </Link>
          <Link href="/help" className="hover:text-foreground transition-colors">
            Help
          </Link>
        </div>
      </div>
    </footer>
  );
}
