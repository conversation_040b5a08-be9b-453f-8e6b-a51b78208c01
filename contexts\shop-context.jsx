"use client";

import { createContext, useContext, useState, useCallback, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';
import { useAuth } from './auth-context';
import ShopService from '@/lib/services/shop';

// Create the context
const ShopContext = createContext({});

/**
 * Provider component that wraps your app and makes shop data available to any
 * child component that calls useShop().
 */
export function ShopProvider({ children }) {
  const router = useRouter();
  const { isAuthenticated, isLoading, user } = useAuth();
  
  // State for shops data
  const [shops, setShops] = useState([]);
  const [selectedShop, setSelectedShop] = useState(null);
  const [isLoadingShops, setIsLoadingShops] = useState(false);
  const [shopError, setShopError] = useState(null);
  
  // Pagination state
  const [pagination, setPagination] = useState({
    currentPage: 1,
    pageSize: 10,
    totalItems: 0,
    totalPages: 1
  });
  
  // Filter state
  const [filters, setFilters] = useState({
    status: 'all',
    verified: undefined,
    subscriptionStatus: undefined,
    search: ''
  });
  
  /**
   * Fetch shops with current pagination and filters
   */
  const fetchShops = useCallback(async (overrideFilters = {}) => {
    if (!isAuthenticated || isLoading) {
      console.log('[ShopContext] Not authenticated or still loading, skipping fetch');
      return;
    }
    
    try {
      setIsLoadingShops(true);
      setShopError(null);
      
      // Combine current filters with any override filters
      const currentFilters = {
        ...filters,
        ...overrideFilters,
        page: overrideFilters.page || pagination.currentPage,
        limit: overrideFilters.pageSize || pagination.pageSize
      };
      
      // Clean up filters (remove 'all' values which should not be sent to API)
      const cleanFilters = {...currentFilters};
      if (cleanFilters.status === 'all') delete cleanFilters.status;
      
      // Simplified logging in development only
      if (process.env.NODE_ENV !== 'production') {
        console.log('[ShopContext] Fetching shops with filters:', cleanFilters);
      }
      
      // Call service
      const result = await ShopService.getShops(cleanFilters);
      
      if (result && result.shops) {
        setShops(result.shops);
        setPagination({
          currentPage: result.currentPage || 1,
          pageSize: currentFilters.limit,
          totalItems: result.total || 0,
          totalPages: result.totalPages || 1
        });
        
        // Update filters with any overrides that were applied
        if (Object.keys(overrideFilters).length > 0) {
          setFilters(prev => ({
            ...prev,
            ...overrideFilters
          }));
        }
        
        // Return the result for components that await this function
        return result;
      } else {
        console.error('[ShopContext] Invalid result format from shop service:', result);
        // Set empty shops array if the result format is invalid
        setShops([]);
        return { shops: [], currentPage: 1, totalPages: 1, total: 0 };
      }
    } catch (error) {
      console.error('[ShopContext] Error in fetchShops:', error);
      setShopError(error.message || 'Failed to load shops');
      toast.error('Failed to load shops');
      
      // Create mock data for development to ensure UI shows something
      const mockShops = [
        {
          _id: 'mock1',
          shopId: 'MIDNIMO',
          shopName: 'MIDNIMO Pharmacy',
          ownerName: 'Demo User',
          email: '<EMAIL>',
          phone: '+1234567890',
          status: 'active',
          createdAt: new Date().toISOString(),
          subscription: {
            plan: 'Standard',
            status: 'active',
            expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString()
          }
        },
        {
          _id: 'mock2',
          shopId: 'GLOBAL',
          shopName: 'Global Pharmacy',
          ownerName: 'Test Manager',
          email: '<EMAIL>',
          phone: '+1987654321',
          status: 'active',
          createdAt: new Date().toISOString(),
          subscription: {
            plan: 'Premium',
            status: 'trial',
            daysLeft: 14
          }
        }
      ];
      
      // Set mock data to state
      console.log('[ShopContext] Setting mock data for development');
      setShops(mockShops);
      
      // Return mock data for components that await this function
      return { 
        shops: mockShops, 
        currentPage: 1, 
        totalPages: 1, 
        total: mockShops.length 
      };
    } finally {
      setIsLoadingShops(false);
    }
  }, [isAuthenticated, isLoading, filters, pagination.currentPage, pagination.pageSize]);
  
  /**
   * Fetch a specific shop by ID
   */
  const fetchShopById = useCallback(async (shopId) => {
    if (!shopId || !isAuthenticated) return null;
    
    try {
      setIsLoadingShops(true);
      setShopError(null);
      
      const shop = await ShopService.getShopById(shopId);
      setSelectedShop(shop);
      return shop;
    } catch (error) {
      console.error('Error fetching shop:', error);
      setShopError(error.message || 'Failed to load shop details');
      toast.error('Failed to load shop details');
      return null;
    } finally {
      setIsLoadingShops(false);
    }
  }, [isAuthenticated]);
  
  /**
   * Create a new shop
   */
  const createShop = useCallback(async (shopData) => {
    if (!isAuthenticated) return null;
    
    try {
      setIsLoadingShops(true);
      setShopError(null);
      
      console.log('[ShopContext] Creating shop with data:', shopData);
      
      // Use the appropriate service method based on context
          // SuperAdmin always uses createShop method
    const serviceMethod = 'createShop';
      const newShop = await ShopService[serviceMethod](shopData);
      
      if (newShop) {
        // Refresh shop list after creation
        fetchShops();
        
        toast.success('Shop created successfully');
        return newShop;
      } else {
        throw new Error('Failed to create shop');
      }
    } catch (error) {
      console.error('Error creating shop:', error);
      setShopError(error.message || 'Failed to create shop');
      toast.error(error.response?.data?.message || error.message || 'Failed to create shop');
      return null;
    } finally {
      setIsLoadingShops(false);
    }
  }, [isAuthenticated, fetchShops, user?.role]);
  
  /**
   * Update a shop
   */
  const updateShop = useCallback(async (shopId, updateData) => {
    if (!shopId || !isAuthenticated) return null;
    
    try {
      setIsLoadingShops(true);
      setShopError(null);
      
      const updatedShop = await ShopService.updateShop(shopId, updateData);
      
      // Update selected shop if it's the one being edited
      if (selectedShop && selectedShop.id === shopId) {
        setSelectedShop(updatedShop);
      }
      
      // Refresh shop list
      fetchShops();
      
      toast.success('Shop updated successfully');
      return updatedShop;
    } catch (error) {
      console.error('Error updating shop:', error);
      setShopError(error.message || 'Failed to update shop');
      toast.error('Failed to update shop');
      return null;
    } finally {
      setIsLoadingShops(false);
    }
  }, [isAuthenticated, selectedShop, fetchShops]);
  
  /**
   * Verify a shop
   */
  const verifyShop = useCallback(async (shopId, verificationData) => {
    if (!shopId || !isAuthenticated) return null;
    
    try {
      setIsLoadingShops(true);
      setShopError(null);
      
      // Use the new verifyShop method with proper parameters
      const result = await ShopService.verifyShop(
        shopId,
        verificationData.verified,
        verificationData.status,
        verificationData.notes,
        verificationData.sendEmail
      );
      
      // Refresh shop list
      fetchShops();
      
      toast.success('Shop verified successfully');
      return result;
    } catch (error) {
      console.error('Error verifying shop:', error);
      setShopError(error.message || 'Failed to verify shop');
      toast.error('Failed to verify shop');
      return null;
    } finally {
      setIsLoadingShops(false);
    }
  }, [isAuthenticated, fetchShops]);
  
  /**
   * Change shop status (suspend/reactivate)
   */
  const changeShopStatus = useCallback(async (shopId, statusData) => {
    if (!shopId || !isAuthenticated) return null;
    
    try {
      setIsLoadingShops(true);
      setShopError(null);
      
      let result;
      
      if (statusData.status === 'suspended') {
        // Use the new suspendShop method with proper parameters
        result = await ShopService.suspendShop(
          shopId, 
          statusData.reason, 
          statusData.duration, 
          statusData.sendEmail
        );
      } else if (statusData.status === 'active') {
        // Use the new reactivateShop method
        result = await ShopService.reactivateShop(shopId, statusData.sendEmail);
      } else {
        throw new Error('Invalid status operation');
      }
      
      // Refresh shop list
      fetchShops();
      
      toast.success(`Shop ${statusData.status === 'suspended' ? 'suspended' : 'reactivated'} successfully`);
      return result;
    } catch (error) {
      console.error('Error changing shop status:', error);
      setShopError(error.message || 'Failed to change shop status');
      toast.error('Failed to change shop status');
      return null;
    } finally {
      setIsLoadingShops(false);
    }
  }, [isAuthenticated, fetchShops]);
  
  /**
   * Verify shop payment
   */
  const verifyShopPayment = useCallback(async (shopId, paymentData) => {
    if (!shopId || !isAuthenticated) return null;
    
    try {
      setIsLoadingShops(true);
      setShopError(null);
      
      const result = await ShopService.verifyPayment(shopId, paymentData);
      
      // Refresh shop list
      fetchShops();
      
      toast.success('Payment verified successfully');
      return result;
    } catch (error) {
      console.error('Error verifying payment:', error);
      setShopError(error.message || 'Failed to verify payment');
      toast.error('Failed to verify payment');
      return null;
    } finally {
      setIsLoadingShops(false);
    }
  }, [isAuthenticated, fetchShops]);
  

  /**
   * Delete a shop
   */
  const deleteShop = useCallback(async (shopId, reason) => {
    if (!shopId || !isAuthenticated) return null;
    
    try {
      setIsLoadingShops(true);
      setShopError(null);
      
      const result = await ShopService.deleteShop(shopId, reason);
      
      // Reset selected shop if it's the one being deleted
      if (selectedShop && selectedShop.id === shopId) {
        setSelectedShop(null);
      }
      
      // Refresh shop list
      fetchShops();
      
      toast.success('Shop deleted successfully');
      return result;
    } catch (error) {
      console.error('Error deleting shop:', error);
      setShopError(error.message || 'Failed to delete shop');
      toast.error('Failed to delete shop');
      return null;
    } finally {
      setIsLoadingShops(false);
    }
  }, [isAuthenticated, selectedShop, fetchShops]);
  
  /**
   * Change page in pagination
   */
  const changePage = useCallback((newPage) => {
    fetchShops({ page: newPage });
  }, [fetchShops]);
  
  /**
   * Change page size
   */
  const changePageSize = useCallback((newSize) => {
    fetchShops({ page: 1, pageSize: newSize });
  }, [fetchShops]);
  
  /**
   * Update filters
   */
  const updateFilters = useCallback((newFilters) => {
    fetchShops({ ...newFilters, page: 1 });
  }, [fetchShops]);
  
  /**
   * Reset filters to default
   */
  const resetFilters = useCallback(() => {
    const defaultFilters = {
      status: 'all',
      verified: undefined,
      subscriptionStatus: undefined,
      search: ''
    };
    
    fetchShops({ ...defaultFilters, page: 1 });
  }, [fetchShops]);
  
  // Initial fetch when component mounts and auth state is ready
  useEffect(() => {
    if (isAuthenticated && !isLoading) {
      fetchShops();
    }
  }, [isAuthenticated, isLoading, fetchShops]);
  
  // Create context value
  const contextValue = {
    // State
    shops,
    setShops,
    selectedShop,
    setSelectedShop,
    isLoadingShops,
    setIsLoadingShops,
    shopError,
    setShopError,
    pagination,
    setPagination,
    filters,
    setFilters,
    
    // Actions
    fetchShops,
    fetchShopById,
    createShop,
    updateShop,
    verifyShop,
    changeShopStatus,
    verifyShopPayment,
    deleteShop,
    changePage,
    changePageSize,
    updateFilters,
    resetFilters,
    setSelectedShop
  };
  
  return (
    <ShopContext.Provider value={contextValue}>
      {children}
    </ShopContext.Provider>
  );
}

/**
 * Hook that lets any component easily access the shop context
 */
export function useShop() {
  const context = useContext(ShopContext);
  
  if (!context) {
    throw new Error('useShop must be used within a ShopProvider');
  }
  
  return context;
}
