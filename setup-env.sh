#!/bin/bash

# ==============================================
# DEYNCARE FRONTEND ENVIRONMENT SETUP SCRIPT
# ==============================================
# This script helps you create a production environment file
# based on the example template

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to prompt for user input
prompt_for_value() {
    local var_name="$1"
    local description="$2"
    local default_value="$3"
    local is_secret="${4:-false}"
    
    echo ""
    echo -e "${BLUE}Setting up: ${NC}$var_name"
    echo -e "${YELLOW}Description: ${NC}$description"
    
    if [ -n "$default_value" ]; then
        echo -e "${YELLOW}Default: ${NC}$default_value"
    fi
    
    if [ "$is_secret" = "true" ]; then
        echo -n "Enter value (input will be hidden): "
        read -s user_input
        echo ""
    else
        echo -n "Enter value: "
        read user_input
    fi
    
    if [ -z "$user_input" ] && [ -n "$default_value" ]; then
        user_input="$default_value"
    fi
    
    echo "$user_input"
}

# Function to generate random string
generate_random_string() {
    openssl rand -base64 32 | tr -d "=+/" | cut -c1-32
}

# Main function
main() {
    clear
    echo "========================================"
    echo "   DeynCare Frontend Environment Setup  "
    echo "========================================"
    echo ""
    
    # Check if env.production.example exists
    if [ ! -f "env.production.example" ]; then
        print_error "env.production.example not found. Please make sure you're in the correct directory."
        exit 1
    fi
    
    # Check if .env.production already exists
    if [ -f ".env.production" ]; then
        print_warning ".env.production already exists."
        echo -n "Do you want to overwrite it? (y/N): "
        read overwrite
        if [[ ! $overwrite =~ ^[Yy]$ ]]; then
            print_status "Setup cancelled."
            exit 0
        fi
    fi
    
    print_status "Starting environment setup..."
    print_success "Great news! Your environment file is already pre-configured with your URLs:"
    echo "  - Frontend: https://deyncare.cajiibcreative.com"
    echo "  - Backend API: https://deyncare-backend.khanciye.com"
    echo ""
    print_status "The environment file only needs the essential API URL variable."
    echo ""
    
    echo -n "Press Enter to continue..."
    read
    
    # Start with the example file
    cp env.production.example .env.production
    
    print_status "Environment file created successfully!"
    print_status "Checking if you need any optional configurations..."
    
    # Optional ML API configuration
    echo ""
    echo -n "Do you have a separate ML API service? (y/N): "
    read setup_ml_api
    
    if [[ $setup_ml_api =~ ^[Yy]$ ]]; then
        ml_api_url=$(prompt_for_value "ML_API_URL" "Your ML API URL")
        echo "" >> .env.production
        echo "# ML API Configuration" >> .env.production
        echo "NEXT_PUBLIC_ML_API_URL=$ml_api_url" >> .env.production
    fi
    
    # Set proper permissions
    chmod 600 .env.production
    
    print_success "Environment file created successfully!"
    print_warning "Important security notes:"
    echo "  1. Never commit .env.production to version control"
    echo "  2. The file has been set to 600 permissions (owner read/write only)"
    echo "  3. Review the file and adjust any additional settings as needed"
    echo ""
    
    echo -n "Do you want to view the created .env.production file? (y/N): "
    read view_file
    if [[ $view_file =~ ^[Yy]$ ]]; then
        echo ""
        echo "========================================"
        echo "   .env.production file contents:"
        echo "========================================"
        cat .env.production
    fi
    
    print_success "Setup completed!"
    print_status "Next steps:"
    echo "  1. Review and adjust .env.production if needed"
    echo "  2. Run: npm run build"
    echo "  3. Run: ./deploy.sh"
}

# Run main function
main "$@" 