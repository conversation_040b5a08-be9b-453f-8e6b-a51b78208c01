const FirebaseService = require('./firebaseService');
const NotificationService = require('./notificationService');
const MLRiskService = require('./mlRiskService');
const { logInfo, logError } = require('../utils');

/**
 * Debt Push Notification Service
 * Handles push notifications when debts are created, including:
 * - Customer information
 * - Debt amount and due date
 * - ML Risk assessment (Low, Medium, High Risk)
 */
class DebtNotificationService {

  /**
   * Send debt creation notification to shop admins
   * @param {Object} debt - Debt document
   * @param {Object} customer - Customer document
   * @param {Object} options - Notification options
   */
  async sendDebtCreationNotification(debt, customer, options = {}) {
    try {
      const {
        shopId,
        skipRiskAssessment = false,
        priority = 'normal'
      } = options;

      // Get risk assessment for the debt
      let riskAssessment = null;
      if (!skipRiskAssessment) {
        try {
          riskAssessment = await MLRiskService.evaluateRisk(debt, customer);
          logInfo(`Risk assessment completed for debt ${debt.debtId}: ${riskAssessment.riskLevel}`, 'DebtNotificationService');
        } catch (error) {
          logError(`Risk assessment failed for debt ${debt.debtId}: ${error.message}`, 'DebtNotificationService');
          // Continue without risk assessment
        }
      }

      // Prepare notification content
      const notificationData = this.prepareDebtNotificationContent(debt, customer, riskAssessment);

      // Create notification record
      const notification = await NotificationService.createNotification({
        shopId: debt.shopId,
        recipient: 'shop_admins',
        recipientType: 'admin',
        recipientName: 'Shop Administrators',
        type: 'Push',
        priority: this.determinePriority(riskAssessment, 'medium'),
        category: 'debt',
        title: notificationData.title,
        message: notificationData.message,
        actionUrl: `deyncare://dashboard/debts/${debt.debtId}`,
        actionLabel: 'View Debt Details',
        relatedEntity: {
          type: 'debt',
          id: debt.debtId,
          customerId: customer.customerId
        },
        metadata: {
          customerName: customer.CustomerName,
          debtAmount: debt.DebtAmount,
          dueDate: debt.DueDate,
          riskLevel: riskAssessment?.riskLevel || 'Pending Assessment',
          riskScore: riskAssessment?.riskScore || null,
          debtCreatedAt: new Date()
        },
        createdBy: debt.createdBy || 'system'
      });

      // Send push notification
      const pushResult = await FirebaseService.sendToShopOwners(
        [debt.shopId],
        {
          title: notificationData.title,
          message: notificationData.message,
          data: {
            type: 'debt_creation',
            debtId: debt.debtId,
            customerId: customer.customerId,
            customerName: customer.CustomerName,
            debtAmount: debt.DebtAmount.toString(),
            dueDate: debt.DueDate.toISOString(),
            riskLevel: riskAssessment?.riskLevel || 'Pending',
            riskScore: riskAssessment?.riskScore?.toString() || '0',
            actionUrl: `deyncare://dashboard/debts/${debt.debtId}`,
            notificationId: notification.notificationId
          },
          priority: this.determinePriority(riskAssessment, 'medium'),
          badge: 1,
          sound: this.getRiskSound(riskAssessment?.riskLevel),
          imageUrl: this.getRiskIcon(riskAssessment?.riskLevel)
        }
      );

      // Update notification with delivery status
      if (pushResult.success) {
        await notification.markSent(pushResult.messageId || null, pushResult.cost || null);
        await notification.markDelivered();
      } else {
        await notification.incrementAttempts(
          pushResult.error || 'Push notification failed',
          'PUSH_DELIVERY_ERROR'
        );
      }

      logInfo(`Debt creation notification sent for ${debt.debtId}: ${pushResult.success ? 'Success' : 'Failed'}`, 'DebtNotificationService');

      return {
        success: pushResult.success,
        notificationId: notification.notificationId,
        riskAssessment: riskAssessment,
        deliveryStats: {
          totalSent: pushResult.totalSent || 0,
          totalFailed: pushResult.totalFailed || 0,
          totalRecipients: pushResult.totalRecipients || 0
        }
      };

    } catch (error) {
      logError(`Failed to send debt creation notification: ${error.message}`, 'DebtNotificationService', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Prepare notification content based on debt, customer, and risk data
   * @param {Object} debt - Debt document
   * @param {Object} customer - Customer document
   * @param {Object} riskAssessment - Risk assessment result
   * @returns {Object} Notification content
   */
  prepareDebtNotificationContent(debt, customer, riskAssessment) {
    const customerName = customer.CustomerName;
    const debtAmount = this.formatCurrency(debt.DebtAmount);
    const dueDate = this.formatDate(debt.DueDate);
    const riskLevel = riskAssessment?.riskLevel || 'Pending Assessment';
    const riskEmoji = this.getRiskEmoji(riskLevel);

    // Base notification content
    let title = `New Debt Created - ${customerName}`;
    let message = `${debtAmount} debt due ${dueDate}`;

    // Enhance with risk information
    if (riskAssessment) {
      const riskScore = riskAssessment.riskScore;
      
      switch (riskLevel) {
        case 'High Risk':
          title = `🚨 High Risk Debt - ${customerName}`;
          message = `${debtAmount} debt (${riskScore}% risk) due ${dueDate}. Immediate attention required!`;
          break;
          
        case 'Medium Risk':
          title = `⚠️ Medium Risk Debt - ${customerName}`;
          message = `${debtAmount} debt (${riskScore}% risk) due ${dueDate}. Monitor closely.`;
          break;
          
        case 'Low Risk':
          title = `✅ New Debt - ${customerName}`;
          message = `${debtAmount} debt (${riskScore}% risk) due ${dueDate}. Low risk customer.`;
          break;
          
        case 'Active Debt':
          title = `📝 Active Debt - ${customerName}`;
          message = `${debtAmount} debt due ${dueDate}. Risk assessment pending until due date.`;
          break;
          
        default:
          title = `📋 New Debt - ${customerName}`;
          message = `${debtAmount} debt due ${dueDate}. Risk assessment in progress.`;
      }
    }

    return {
      title,
      message,
      riskLevel,
      riskEmoji
    };
  }

  /**
   * Send risk level change notification
   * @param {Object} debt - Debt document
   * @param {Object} customer - Customer document
   * @param {string} oldRiskLevel - Previous risk level
   * @param {string} newRiskLevel - New risk level
   * @param {number} riskScore - Risk score
   */
  async sendRiskLevelChangeNotification(debt, customer, oldRiskLevel, newRiskLevel, riskScore) {
    try {
      if (oldRiskLevel === newRiskLevel) {
        return; // No change, skip notification
      }

      const customerName = customer.CustomerName;
      const debtAmount = this.formatCurrency(debt.DebtAmount);
      const riskEmoji = this.getRiskEmoji(newRiskLevel);

      const title = `${riskEmoji} Risk Level Changed - ${customerName}`;
      const message = `${debtAmount} debt risk updated: ${oldRiskLevel} → ${newRiskLevel} (${riskScore}%)`;

      // Create notification record
      const notification = await NotificationService.createNotification({
        shopId: debt.shopId,
        recipient: 'shop_admins',
        recipientType: 'admin',
        recipientName: 'Shop Administrators',
        type: 'Push',
        priority: this.determinePriority({ riskLevel: newRiskLevel }, 'medium'),
        category: 'alert',
        title: title,
        message: message,
        actionUrl: `deyncare://dashboard/debts/${debt.debtId}`,
        actionLabel: 'Review Risk Assessment',
        relatedEntity: {
          type: 'debt',
          id: debt.debtId,
          customerId: customer.customerId
        },
        metadata: {
          customerName: customer.CustomerName,
          debtAmount: debt.DebtAmount,
          oldRiskLevel: oldRiskLevel,
          newRiskLevel: newRiskLevel,
          riskScore: riskScore,
          assessmentDate: new Date()
        },
        createdBy: 'system'
      });

      // Send push notification
      const pushResult = await FirebaseService.sendToShopOwners(
        [debt.shopId],
        {
          title: title,
          message: message,
          data: {
            type: 'risk_change',
            debtId: debt.debtId,
            customerId: customer.customerId,
            customerName: customer.CustomerName,
            oldRiskLevel: oldRiskLevel,
            newRiskLevel: newRiskLevel,
            riskScore: riskScore.toString(),
            actionUrl: `deyncare://dashboard/debts/${debt.debtId}`,
            notificationId: notification.notificationId
          },
          priority: this.determinePriority({ riskLevel: newRiskLevel }, 'medium'),
          badge: 1,
          sound: this.getRiskSound(newRiskLevel)
        }
      );

      logInfo(`Risk change notification sent for ${debt.debtId}: ${oldRiskLevel} → ${newRiskLevel}`, 'DebtNotificationService');

      return {
        success: pushResult.success,
        notificationId: notification.notificationId
      };

    } catch (error) {
      logError(`Failed to send risk change notification: ${error.message}`, 'DebtNotificationService', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Determine notification priority based on risk level
   * @param {Object} riskAssessment - Risk assessment result
   * @param {string} basePriority - Base priority level
   * @returns {string} Priority level
   */
  determinePriority(riskAssessment, basePriority = 'medium') {
    if (!riskAssessment || !riskAssessment.riskLevel) {
      return basePriority;
    }

    switch (riskAssessment.riskLevel) {
      case 'High Risk':
        return 'high';
      case 'Medium Risk':
        return 'medium';
      case 'Low Risk':
        return 'low';
      case 'Active Debt':
        return 'medium';
      default:
        return basePriority;
    }
  }

  /**
   * Get risk emoji for visual indication
   * @param {string} riskLevel - Risk level
   * @returns {string} Emoji
   */
  getRiskEmoji(riskLevel) {
    switch (riskLevel) {
      case 'High Risk':
        return '🚨';
      case 'Medium Risk':
        return '⚠️';
      case 'Low Risk':
        return '✅';
      case 'Active Debt':
        return '📝';
      default:
        return '📋';
    }
  }

  /**
   * Get notification sound based on risk level
   * @param {string} riskLevel - Risk level
   * @returns {string} Sound name
   */
  getRiskSound(riskLevel) {
    switch (riskLevel) {
      case 'High Risk':
        return 'alert'; // Urgent sound
      case 'Medium Risk':
        return 'warning'; // Warning sound
      case 'Low Risk':
        return 'success'; // Success sound
      case 'Active Debt':
        return 'default'; // Default sound
      default:
        return 'default';
    }
  }

  /**
   * Get risk icon URL for rich notifications
   * @param {string} riskLevel - Risk level
   * @returns {string|null} Full icon URL or null if base URL not configured
   */
  getRiskIcon(riskLevel) {
    const baseUrl = process.env.ASSETS_BASE_URL;
    
    // Return null if no base URL configured (Firebase requires full URLs)
    if (!baseUrl || !baseUrl.startsWith('http')) {
      return null;
    }
    
    switch (riskLevel) {
      case 'High Risk':
        return `${baseUrl}/icons/risk-high.png`;
      case 'Medium Risk':
        return `${baseUrl}/icons/risk-medium.png`;
      case 'Low Risk':
        return `${baseUrl}/icons/risk-low.png`;
      case 'Active Debt':
        return `${baseUrl}/icons/debt-active.png`;
      default:
        return `${baseUrl}/icons/debt-default.png`;
    }
  }

  /**
   * Format currency for display
   * @param {number} amount - Amount to format
   * @returns {string} Formatted currency
   */
  formatCurrency(amount) {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 2
    }).format(amount);
  }

  /**
   * Format date for display
   * @param {Date} date - Date to format
   * @returns {string} Formatted date
   */
  formatDate(date) {
    const now = new Date();
    const dueDate = new Date(date);
    const diffTime = dueDate - now;
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays < 0) {
      return `${Math.abs(diffDays)} days overdue`;
    } else if (diffDays === 0) {
      return 'today';
    } else if (diffDays === 1) {
      return 'tomorrow';
    } else if (diffDays <= 7) {
      return `in ${diffDays} days`;
    } else {
      return dueDate.toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric',
        year: dueDate.getFullYear() !== now.getFullYear() ? 'numeric' : undefined
      });
    }
  }

  /**
   * Send bulk debt creation notifications
   * @param {Array} debts - Array of debt objects with customer data
   * @param {Object} options - Notification options
   */
  async sendBulkDebtNotifications(debts, options = {}) {
    const results = [];
    
    for (const { debt, customer } of debts) {
      try {
        const result = await this.sendDebtCreationNotification(debt, customer, options);
        results.push({
          debtId: debt.debtId,
          customerId: customer.customerId,
          success: result.success,
          notificationId: result.notificationId
        });
      } catch (error) {
        results.push({
          debtId: debt.debtId,
          customerId: customer.customerId,
          success: false,
          error: error.message
        });
      }
    }

    return results;
  }

  /**
   * Send simple debt reminder notifications
   * @param {string} reminderType - '7_days', '3_days', or 'overdue'
   * @param {string} shopId - Optional shop ID filter
   */
  async sendDebtReminders(reminderType, shopId = null) {
    try {
      const Debt = require('../models/debt.model');
      const Customer = require('../models/customer.model');
      
      // Calculate date ranges for reminders
      const now = new Date();
      let dateFilter = {};
      let reminderMessage = '';
      let priority = 'medium';

      switch (reminderType) {
        case '7_days':
          const sevenDaysFromNow = new Date(now.getTime() + (7 * 24 * 60 * 60 * 1000));
          dateFilter = {
            DueDate: {
              $gte: sevenDaysFromNow,
              $lt: new Date(sevenDaysFromNow.getTime() + (24 * 60 * 60 * 1000))
            }
          };
          reminderMessage = 'due in 7 days';
          break;
          
        case '3_days':
          const threeDaysFromNow = new Date(now.getTime() + (3 * 24 * 60 * 60 * 1000));
          dateFilter = {
            DueDate: {
              $gte: threeDaysFromNow,
              $lt: new Date(threeDaysFromNow.getTime() + (24 * 60 * 60 * 1000))
            }
          };
          reminderMessage = 'due in 3 days';
          priority = 'high';
          break;
          
        case 'overdue':
          dateFilter = {
            DueDate: { $lt: now }
          };
          reminderMessage = 'overdue';
          priority = 'high';
          break;
          
        default:
          throw new Error('Invalid reminder type. Use: 7_days, 3_days, or overdue');
      }

      // Find debts that need reminders
      const query = {
        ...dateFilter,
        OutstandingDebt: { $gt: 0 },
        status: { $in: ['active', 'partially_paid'] }
      };
      
      if (shopId) {
        query.shopId = shopId;
      }

      const debts = await Debt.find(query);
      
      if (debts.length === 0) {
        logInfo(`No debts found for ${reminderType} reminders`, 'DebtNotificationService');
        return {
          success: true,
          message: `No debts found for ${reminderType} reminders`,
          totalSent: 0
        };
      }

      // Group debts by shop for efficient notifications
      const debtsByShop = {};
      for (const debt of debts) {
        if (!debtsByShop[debt.shopId]) {
          debtsByShop[debt.shopId] = [];
        }
        debtsByShop[debt.shopId].push(debt);
      }

      const results = [];
      
      // Send notifications per shop
      for (const [currentShopId, shopDebts] of Object.entries(debtsByShop)) {
        try {
          const debtCount = shopDebts.length;
          const totalAmount = shopDebts.reduce((sum, debt) => sum + debt.OutstandingDebt, 0);
          const formattedAmount = this.formatCurrency(totalAmount);

          // Simple reminder notification content
          const reminderEmoji = {
            '7_days': '📅',
            '3_days': '⚠️', 
            'overdue': '🚨'
          }[reminderType] || '📋';

          const title = `${reminderEmoji} Debt Reminder`;
          const message = `${debtCount} debts ${reminderMessage} - Total: ${formattedAmount}`;

          // Create notification record
          const notification = await NotificationService.createNotification({
            shopId: currentShopId,
            recipient: 'shop_admins',
            recipientType: 'admin',
            recipientName: 'Shop Administrators',
            type: 'Push',
            priority: priority,
            category: 'reminder',
            title: title,
            message: message,
            actionUrl: `deyncare://dashboard/debts?filter=${reminderType}`,
            actionLabel: 'View Debts',
            relatedEntity: {
              type: 'debt',
              id: reminderType,
              shopId: currentShopId
            },
            metadata: {
              reminderType: reminderType,
              debtCount: debtCount,
              totalAmount: totalAmount,
              shopId: currentShopId,
              sentAt: new Date()
            },
            createdBy: 'system'
          });

          // Send push notification
          const pushResult = await FirebaseService.sendToShopOwners(
            [currentShopId],
            {
              title: title,
              message: message,
              data: {
                type: 'debt_reminder',
                reminderType: reminderType,
                debtCount: debtCount.toString(),
                totalAmount: totalAmount.toString(),
                shopId: currentShopId,
                actionUrl: `deyncare://dashboard/debts?filter=${reminderType}`,
                notificationId: notification.notificationId
              },
              priority: priority,
              badge: debtCount,
              sound: reminderType === 'overdue' ? 'alert' : 'default'
            }
          );

          results.push({
            shopId: currentShopId,
            success: pushResult.success,
            debtCount: debtCount,
            totalAmount: totalAmount,
            notificationId: notification.notificationId
          });

        } catch (shopError) {
          logError(`Failed to send reminder to shop ${currentShopId}: ${shopError.message}`, 'DebtNotificationService');
          results.push({
            shopId: currentShopId,
            success: false,
            error: shopError.message
          });
        }
      }

      const successCount = results.filter(r => r.success).length;
      const totalDebts = debts.length;

      logInfo(`Debt reminders sent: ${successCount}/${Object.keys(debtsByShop).length} shops, ${totalDebts} debts`, 'DebtNotificationService');

      return {
        success: successCount > 0,
        message: `${reminderType} reminders sent to ${successCount} shops`,
        totalDebts: totalDebts,
        totalShops: Object.keys(debtsByShop).length,
        successfulShops: successCount,
        results: results
      };

    } catch (error) {
      logError(`Failed to send debt reminders: ${error.message}`, 'DebtNotificationService', error);
      return {
        success: false,
        error: error.message
      };
    }
  }
}

module.exports = new DebtNotificationService(); 