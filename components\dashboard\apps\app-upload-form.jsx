"use client";

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Upload, FileCheck, AlertCircle, CheckCircle } from 'lucide-react';
import { useAppUpload } from '@/hooks/use-app-upload';

// Only support Android APK files
const platforms = [
  { value: 'android', label: 'Android (APK)' }
];

const fileTypes = {
  android: 'apk'
};

export default function AppUploadForm({ onUploadSuccess }) {
  const [formData, setFormData] = useState({
    appName: 'DeynCare Mobile App',
    platform: 'android',
    fileType: 'apk'
  });
  const [selectedFile, setSelectedFile] = useState(null);
  const [dragActive, setDragActive] = useState(false);
  const [success, setSuccess] = useState(false);

  const { uploading, progress, error, uploadApp, resetUpload } = useAppUpload();

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleFileSelect = (file) => {
    if (file) {
      // Only accept APK files
      if (!file.name.toLowerCase().endsWith('.apk')) {
        alert('Please select an APK file for Android.');
        return;
      }
      setSelectedFile(file);
      setSuccess(false);
      resetUpload();
    }
  };

  const handleDrag = (e) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true);
    } else if (e.type === 'dragleave') {
      setDragActive(false);
    }
  };

  const handleDrop = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
    
    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFileSelect(e.dataTransfer.files[0]);
    }
  };

  const validateForm = () => {
    if (!selectedFile) {
      return 'Please select an APK file to upload';
    }
    
    if (!selectedFile.name.toLowerCase().endsWith('.apk')) {
      return 'Please select a valid APK file';
    }

    return null;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    const validationError = validateForm();
    if (validationError) {
      alert(validationError);
      return;
    }

    try {
      setSuccess(false);

      const metadata = {
        appName: formData.appName,
        platform: formData.platform,
        fileType: formData.fileType
      };

      await uploadApp(selectedFile, metadata);
      
      setSuccess(true);
      setSelectedFile(null);
      setFormData({
        appName: 'DeynCare Mobile App',
        platform: 'android',
        fileType: 'apk'
      });

      // Call success callback to refresh parent component
      if (onUploadSuccess) {
        onUploadSuccess();
      }
    } catch (error) {
      console.error('Upload failed:', error);
    }
  };

  return (
    <Card className="w-full max-w-3xl mx-auto shadow-xl border-0 bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm">
      <CardHeader className="text-center pb-8">
        <div className="mx-auto mb-4 w-16 h-16 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-full flex items-center justify-center shadow-lg">
          <Upload className="h-8 w-8 text-white" />
        </div>
        <CardTitle className="text-2xl font-bold text-gray-900 dark:text-white">
          Upload New App Version
        </CardTitle>
        <CardDescription className="text-base text-gray-600 dark:text-gray-300 mt-2">
          Upload your DeynCare Android App (APK file) and make it available for download
        </CardDescription>
      </CardHeader>
      
      <CardContent className="space-y-8 px-8 pb-8">
        {error && (
          <Alert variant="destructive" className="border-red-200 bg-red-50 dark:bg-red-900/20 dark:border-red-800">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-red-100 dark:bg-red-900/30 rounded-full">
                <AlertCircle className="h-5 w-5 text-red-600 dark:text-red-400" />
              </div>
              <div>
                <h4 className="font-semibold text-red-800 dark:text-red-300">Upload Failed</h4>
                <AlertDescription className="text-red-700 dark:text-red-400 mt-1">{error}</AlertDescription>
              </div>
            </div>
          </Alert>
        )}

        {success && (
          <Alert className="border-green-200 bg-green-50 dark:bg-green-900/20 dark:border-green-800">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-green-100 dark:bg-green-900/30 rounded-full">
                <CheckCircle className="h-5 w-5 text-green-600 dark:text-green-400" />
              </div>
              <div>
                <h4 className="font-semibold text-green-800 dark:text-green-300">Upload Successful!</h4>
                <AlertDescription className="text-green-700 dark:text-green-400 mt-1">
                  Your app has been uploaded and is now available for download.
                </AlertDescription>
              </div>
            </div>
          </Alert>
        )}

        <form onSubmit={handleSubmit} className="space-y-8">
          {/* File Upload Area */}
          <div className="space-y-4">
            <Label htmlFor="file-upload" className="text-lg font-semibold text-gray-900 dark:text-white">
              Select APK File *
            </Label>
            <div
              className={`relative border-2 border-dashed rounded-xl p-8 text-center transition-all duration-300 hover:scale-[1.02] ${
                dragActive
                  ? 'border-blue-500 bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 shadow-lg'
                  : selectedFile
                  ? 'border-green-400 bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20'
                  : 'border-gray-300 dark:border-gray-600 bg-gradient-to-br from-gray-50 to-slate-50 dark:from-gray-800 dark:to-slate-800 hover:border-blue-400 hover:bg-gradient-to-br hover:from-blue-50 hover:to-indigo-50'
              }`}
              onDragEnter={handleDrag}
              onDragLeave={handleDrag}
              onDragOver={handleDrag}
              onDrop={handleDrop}
            >
              <input
                id="file-upload"
                type="file"
                className="hidden"
                accept=".apk"
                onChange={(e) => handleFileSelect(e.target.files[0])}
                disabled={uploading}
              />
              
              {selectedFile ? (
                <div className="space-y-4">
                  <div className="flex items-center justify-center gap-3 text-green-600 dark:text-green-400">
                    <div className="p-3 bg-green-100 dark:bg-green-900/30 rounded-full">
                      <FileCheck className="h-6 w-6" />
                    </div>
                  </div>
                  <div className="text-center">
                    <p className="font-semibold text-gray-900 dark:text-white text-lg">{selectedFile.name}</p>
                    <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                      {(selectedFile.size / (1024 * 1024)).toFixed(2)} MB
                    </p>
                  </div>
                  <button
                    type="button"
                    onClick={() => setSelectedFile(null)}
                    className="text-sm text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 underline"
                  >
                    Choose different file
                  </button>
                </div>
              ) : (
                <div className="space-y-4">
                  <div className="flex items-center justify-center">
                    <div className="p-4 bg-blue-100 dark:bg-blue-900/30 rounded-full">
                      <Upload className="h-10 w-10 text-blue-600 dark:text-blue-400" />
                    </div>
                  </div>
                  <div className="space-y-2">
                    <div>
                      <Label 
                        htmlFor="file-upload" 
                        className="cursor-pointer text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 font-semibold text-lg hover:underline transition-colors"
                      >
                        Click to upload
                      </Label>
                      <span className="text-gray-500 dark:text-gray-400 text-lg"> or drag and drop</span>
                    </div>
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      APK files up to 500MB • Automatic version management
                    </p>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Upload Progress */}
          {uploading && (
            <div className="space-y-4 p-6 bg-blue-50 dark:bg-blue-900/20 rounded-xl border border-blue-200 dark:border-blue-800">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 border-2 border-blue-600 border-t-transparent rounded-full animate-spin"></div>
                  <span className="font-medium text-gray-900 dark:text-white">Uploading your app...</span>
                </div>
                <span className="text-lg font-bold text-blue-600 dark:text-blue-400">{progress}%</span>
              </div>
              <Progress value={progress} className="w-full h-2" />
              <p className="text-sm text-gray-600 dark:text-gray-300 text-center">
                Please wait while we process your upload. This may take a few moments.
              </p>
            </div>
          )}

          {/* App Details */}
          <div className="space-y-4">
            <Label htmlFor="app-name" className="text-lg font-semibold text-gray-900 dark:text-white">
              Application Name
            </Label>
            <Input
              id="app-name"
              value={formData.appName}
              onChange={(e) => handleInputChange('appName', e.target.value)}
              disabled={uploading}
              className="h-12 text-lg border-2 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 dark:focus:ring-blue-800 transition-all duration-200"
              placeholder="Enter application name"
            />
            <p className="text-sm text-gray-500 dark:text-gray-400">
              This name will be displayed to users when they download the app
            </p>
          </div>

          <Button
            type="submit"
            size="lg"
            className="w-full h-14 text-lg font-semibold bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-[1.02]"
            disabled={uploading || !selectedFile}
          >
            {uploading ? (
              <div className="flex items-center gap-2">
                <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                Uploading...
              </div>
            ) : (
              <div className="flex items-center gap-2">
                <Upload className="h-5 w-5" />
                Upload App
              </div>
            )}
          </Button>
        </form>
      </CardContent>
    </Card>
  );
} 