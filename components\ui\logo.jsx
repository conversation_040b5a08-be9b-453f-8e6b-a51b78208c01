"use client"

import Link from "next/link"
import { useTheme } from "next-themes"
import { cn } from "@/lib/utils"
import { useState, useEffect } from "react"

export function Logo({ 
  className = "", 
  variant = "full", // "full", "icon", "text"
  size = "md", // "xs", "sm", "md", "lg", "xl", "2xl"
  href = "/",
  showText = false,
  ...props 
}) {
  const { theme, systemTheme, resolvedTheme } = useTheme()
  const [mounted, setMounted] = useState(false)
  
  // Handle hydration
  useEffect(() => {
    setMounted(true)
  }, [])
  
  // Enhanced dark mode detection
  const isDark = mounted && (
    resolvedTheme === "dark" || 
    theme === "dark" || 
    (theme === "system" && systemTheme === "dark")
  )
  
  // Enhanced size configurations - more compact sizing
  const sizeConfig = {
    xs: {
      icon: "h-6 w-6",
      text: "text-xs font-bold",
      container: "space-x-1.5",
      logoContainer: "h-6"
    },
    sm: {
      icon: "h-8 w-8", 
      text: "text-sm font-bold",
      container: "space-x-2",
      logoContainer: "h-8"
    },
    md: {
      icon: "h-10 w-10",
      text: "text-base font-bold", 
      container: "space-x-2.5",
      logoContainer: "h-10"
    },
    lg: {
      icon: "h-12 w-12",
      text: "text-lg font-bold",
      container: "space-x-3", 
      logoContainer: "h-12"
    },
    xl: {
      icon: "h-16 w-16",
      text: "text-xl font-bold",
      container: "space-x-3",
      logoContainer: "h-16"
    },
    "2xl": {
      icon: "h-20 w-20",
      text: "text-2xl font-bold",
      container: "space-x-4",
      logoContainer: "h-20"
    }
  }
  
  const config = sizeConfig[size] || sizeConfig.md
  
  // Check if custom height/width classes are provided in className
  const hasCustomSize = className.includes('h-') || className.includes('w-')
  
  // Use custom size or fallback to config
  const iconClasses = hasCustomSize 
    ? cn("flex-shrink-0", className.match(/h-\w+/)?.[0] || config.icon, className.match(/w-\w+/)?.[0] || config.icon)
    : config.icon
  
  // DeynCare SVG Icon Component - extracted from deyncare_svg.svg
  const DeynCareIcon = ({ className: iconClassName = "" }) => (
    <svg 
      viewBox="0 0 375 374.999991" 
      className={cn(hasCustomSize ? iconClasses : config.icon, "flex-shrink-0", iconClassName)}
      xmlns="http://www.w3.org/2000/svg"
      preserveAspectRatio="xMidYMid meet"
    >
      <defs>
        <clipPath id="deyncare-clip">
          <path d="M 55 73.648438 L 294.933594 73.648438 L 294.933594 311 L 55 311 Z M 55 73.648438" />
        </clipPath>
      </defs>
      <g>
        <path 
          className={cn(
            "transition-colors duration-200",
            isDark ? "fill-white" : "fill-primary"
          )}
          d="M 147.945312 217.484375 L 174.574219 217.484375 C 187.773438 217.484375 198.726562 206.945312 198.851562 193.75 C 198.980469 180.421875 188.179688 169.539062 174.878906 169.539062 L 105.359375 169.539062 L 122.433594 202.066406 C 127.414062 211.546875 137.238281 217.484375 147.945312 217.484375" 
        />
        <g clipPath="url(#deyncare-clip)">
          <path 
            className={cn(
              "transition-colors duration-200",
              isDark ? "fill-white" : "fill-primary"
            )}
            d="M 174.125 73.648438 L 55.015625 73.648438 L 72.09375 106.171875 C 77.070312 115.65625 86.894531 121.59375 97.601562 121.59375 L 174.265625 121.59375 C 213.894531 121.59375 246.871094 154.015625 246.800781 193.644531 C 246.730469 233.304688 214.554688 265.433594 174.878906 265.433594 L 155.703125 265.433594 L 171.375 295.285156 C 177.515625 306.980469 190.832031 313.058594 203.65625 309.902344 C 255.902344 297.027344 294.664062 249.894531 294.746094 193.695312 C 294.84375 127.667969 240.152344 73.648438 174.125 73.648438" 
          />
        </g>
      </g>
    </svg>
  )
  
  // Dynamic Text Component
  const DynamicText = ({ className: textClassName = "" }) => (
    <span 
      className={cn(
        config.text,
        "transition-all duration-200 tracking-wide font-extrabold",
        isDark 
          ? "text-white drop-shadow-lg" 
          : "text-primary",
        textClassName
      )}
    >
      DeynCare
    </span>
  )
  
  // Logo Content based on variant
  const LogoContent = () => {
    switch (variant) {
      case "icon":
        return <DeynCareIcon />
      
      case "text":
        return <DynamicText />
      
      case "full":
      default:
        return (
          <div className={cn("flex items-center", config.container)}>
            <DeynCareIcon />
            {showText && (
              <DynamicText className="whitespace-nowrap" />
            )}
          </div>
        )
    }
  }
  
  // Debug info in development
  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      console.log('🎨 Logo Performance Debug:', {
        mounted,
        theme,
        systemTheme, 
        resolvedTheme,
        isDark,
        variant,
        size,
        showText,
        performance: 'SVG + CSS (No Images)'
      })
    }
  }, [mounted, theme, systemTheme, resolvedTheme, isDark, variant, size, showText])
  
  // Wrapper with optional Link
  const LogoWrapper = ({ children }) => {
    const wrapperClasses = cn(
      "flex items-center transition-all duration-200 hover:scale-105 active:scale-95",
      "focus:outline-none focus:ring-2 focus:ring-primary/20 rounded-lg",
      className
    )
    
    if (href) {
      return (
        <Link href={href} className={wrapperClasses} {...props}>
          {children}
        </Link>
      )
    }
    
    return (
      <div className={wrapperClasses} {...props}>
        {children}
      </div>
    )
  }
  
  return (
    <LogoWrapper>
      <LogoContent />
    </LogoWrapper>
  )
}

// Specialized logo components for common use cases
export function LogoIcon({ size = "md", className = "", ...props }) {
  return <Logo variant="icon" size={size} className={className} {...props} />
}

export function LogoText({ size = "md", className = "", ...props }) {
  return <Logo variant="text" size={size} className={className} {...props} />
}

export function LogoFull({ size = "md", className = "", showText = true, ...props }) {
  return <Logo variant="full" size={size} showText={showText} className={className} {...props} />
}

// Performance-optimized logo for auth pages
export function AuthLogo({ size = "xl", showText = true, className = "", ...props }) {
  return (
    <Logo 
      variant="full" 
      size={size} 
      showText={showText} 
      className={cn("scale-110", className)}
      {...props} 
    />
  )
}
