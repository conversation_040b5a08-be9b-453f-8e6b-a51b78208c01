const Joi = require('joi');
const patterns = require('../validationPatterns');

/**
 * Report validation schemas
 */
const reportSchemas = {
  /**
   * Schema for generating a new report
   */
  generateReport: Joi.object({
    title: Joi.string().min(3).max(100).required()
      .messages({
        'string.empty': 'Title is required',
        'string.min': 'Title must be at least 3 characters long',
        'string.max': 'Title cannot exceed 100 characters',
        'any.required': 'Title is required'
      }),
    type: Joi.string().valid('debt', 'ml-risk').required()
      .messages({
        'any.only': 'Report type must be one of: debt, ml-risk',
        'any.required': 'Report type is required'
      }),
    format: Joi.string().valid('pdf', 'csv', 'excel').required()
      .messages({
        'any.only': 'Report format must be one of: pdf, csv, excel',
        'any.required': 'Report format is required'
      }),
    description: Joi.string().max(500).allow('').optional()
      .messages({
        'string.max': 'Description cannot exceed 500 characters'
      }),
    shopId: Joi.string().when('$role', {
      is: 'superAdmin',
      then: Joi.string().required(),
      otherwise: Joi.forbidden()
    }).messages({
      'any.required': 'Shop ID is required for SuperAdmin to generate a report'
    }),
    parameters: Joi.object({
      startDate: Joi.date().iso().optional()
        .messages({
          'date.base': 'Start date must be a valid date',
          'date.format': 'Start date must be in ISO format'
        }),
      endDate: Joi.date().iso().min(Joi.ref('startDate')).optional()
        .messages({
          'date.base': 'End date must be a valid date',
          'date.format': 'End date must be in ISO format',
          'date.min': 'End date must be after start date'
        }),
      filters: Joi.object().optional()
    }).optional()
  }),

  /**
   * Schema for getting a report by ID
   */
  getReport: Joi.object({
    reportId: Joi.string().required()
      .messages({
        'string.empty': 'Report ID is required',
        'any.required': 'Report ID is required'
      }),
    type: Joi.string().valid('debt', 'ml-risk').required()
      .messages({
        'any.only': 'Report type must be one of: debt, ml-risk',
        'any.required': 'Report type is required'
      }),
    format: Joi.string().valid('pdf', 'csv', 'excel').required()
      .messages({
        'any.only': 'Report format must be one of: pdf, csv, excel',
        'any.required': 'Report format is required'
      }),
    shopId: Joi.string().when('$role', {
      is: 'superAdmin',
      then: Joi.string().required(),
      otherwise: Joi.forbidden()
    }).messages({
      'any.required': 'Shop ID is required for SuperAdmin to get a report'
    })
  }),

  /**
   * Schema for getting all reports
   */
  getAllReports: Joi.object({
    page: Joi.number().integer().min(1).default(1)
      .messages({
        'number.base': 'Page must be a number',
        'number.integer': 'Page must be an integer',
        'number.min': 'Page must be at least 1'
      }),
    limit: Joi.number().integer().min(1).max(100).default(10)
      .messages({
        'number.base': 'Limit must be a number',
        'number.integer': 'Limit must be an integer',
        'number.min': 'Limit must be at least 1',
        'number.max': 'Limit cannot exceed 100'
      }),
    type: Joi.string().valid('debt', 'ml-risk').optional()
      .messages({
        'any.only': 'Report type must be one of: debt, ml-risk'
      }),
    format: Joi.string().valid('pdf', 'csv', 'excel').optional()
      .messages({
        'any.only': 'Report format must be one of: pdf, csv, excel'
      }),
    startDate: Joi.date().iso().optional()
      .messages({
        'date.base': 'Start date must be a valid date',
        'date.format': 'Start date must be in ISO format'
      }),
    endDate: Joi.date().iso().min(Joi.ref('startDate')).optional()
      .messages({
        'date.base': 'End date must be a valid date',
        'date.format': 'End date must be in ISO format',
        'date.min': 'End date must be after start date'
      }),
    shopId: Joi.string().when('$role', {
      is: 'superAdmin',
      then: Joi.string().optional(),
      otherwise: Joi.forbidden()
    }).messages({
      'any.required': 'Shop ID is required for SuperAdmin to get reports'
    })
  }),

  /**
   * Schema for updating a report
   */
  updateReport: Joi.object({
    reportId: Joi.string().required()
      .messages({
        'string.empty': 'Report ID is required',
        'any.required': 'Report ID is required'
      }),
    title: Joi.string().min(3).max(100).optional()
      .messages({
        'string.min': 'Title must be at least 3 characters long',
        'string.max': 'Title cannot exceed 100 characters'
      }),
    description: Joi.string().max(500).allow('').optional()
      .messages({
        'string.max': 'Description cannot exceed 500 characters'
      }),
    shopId: Joi.string().when('$role', {
      is: 'superAdmin',
      then: Joi.string().optional(),
      otherwise: Joi.forbidden()
    }).messages({
      'any.required': 'Shop ID is required for SuperAdmin to update a report'
    })
  }),

  /**
   * Schema for deleting a report
   */
  deleteReport: Joi.object({
    reportId: Joi.string().required()
      .messages({
        'string.empty': 'Report ID is required',
        'any.required': 'Report ID is required'
      }),
    shopId: Joi.string().when('$role', {
      is: 'superAdmin',
      then: Joi.string().optional(),
      otherwise: Joi.forbidden()
    }).messages({
      'any.required': 'Shop ID is required for SuperAdmin to delete a report'
    })
  }),

  /**
   * Schema for downloading a report
   */
  downloadReport: Joi.object({
    reportId: Joi.string().required()
      .messages({
        'string.empty': 'Report ID is required',
        'any.required': 'Report ID is required'
      }),
    reportType: Joi.string().valid('debt', 'ml-risk').required()
      .messages({
        'any.only': 'Report type must be one of: debt, ml-risk',
        'any.required': 'Report type is required'
      }),
    format: Joi.string().valid('pdf', 'csv', 'excel').required()
      .messages({
        'any.only': 'Report format must be one of: pdf, csv, excel',
        'any.required': 'Report format is required'
      }),
    shopId: Joi.string().when('$role', {
      is: 'superAdmin',
      then: Joi.string().optional(),
      otherwise: Joi.forbidden()
    }).messages({
      'any.required': 'Shop ID is required for SuperAdmin to download a report'
    })
  }),

  /**
   * Schema for customer report data query
   */
  customerReportQuery: Joi.object({
    month: Joi.number().integer().min(1).max(12).optional()
      .messages({
        'number.base': 'Month must be a number',
        'number.min': 'Month must be between 1 and 12',
        'number.max': 'Month must be between 1 and 12'
      }),
    year: Joi.number().integer().min(2020).max(2030).optional()
      .messages({
        'number.base': 'Year must be a number',
        'number.min': 'Year must be between 2020 and 2030',
        'number.max': 'Year must be between 2020 and 2030'
      }),
    startDate: Joi.date().iso().optional()
      .messages({
        'date.base': 'Start date must be a valid date',
        'date.format': 'Start date must be in ISO format (YYYY-MM-DD)'
      }),
    endDate: Joi.date().iso().min(Joi.ref('startDate')).optional()
      .messages({
        'date.base': 'End date must be a valid date',
        'date.format': 'End date must be in ISO format (YYYY-MM-DD)',
        'date.min': 'End date must be after start date'
      }),
    dateRange: Joi.string().valid('daily', 'weekly', 'monthly', 'yearly', 'custom', 'all').default('monthly')
      .messages({
        'any.only': 'Date range must be one of: daily, weekly, monthly, yearly, custom, all'
      }),
    format: Joi.string().valid('json', 'csv', 'excel').default('json')
      .messages({
        'any.only': 'Format must be one of: json, csv, excel'
      }),
    includeRiskData: Joi.string().valid('true', 'false').default('true'),
    sortBy: Joi.string().valid('createdAt', 'CustomerName', 'totalDebt', 'riskLevel').default('createdAt'),
    sortOrder: Joi.string().valid('asc', 'desc').default('desc')
  }).custom((value, helpers) => {
    // Custom validation: if dateRange is 'custom', require startDate and endDate
    if (value.dateRange === 'custom' && (!value.startDate || !value.endDate)) {
      return helpers.error('custom.customDateRange');
    }
    
    // If month is provided, year is required
    if (value.month && !value.year) {
      return helpers.error('custom.monthRequiresYear');
    }
    
    return value;
  }).messages({
    'custom.customDateRange': 'When dateRange is "custom", both startDate and endDate are required',
    'custom.monthRequiresYear': 'When month is provided, year is also required'
  })
};

module.exports = reportSchemas;




