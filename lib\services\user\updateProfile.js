import apiBridge from '@/lib/api/bridge';
import { ENDPOINTS } from '@/lib/api/contract';
import BaseService from '@/lib/services/baseService';

/**
 * Update user profile
 * @param {Object} profileData - Profile data to update
 * @param {string} profileData.fullName - User's full name
 * @param {string} profileData.phone - User's phone number
 * @returns {Promise<Object>} Updated user data
 */
export const updateProfile = async (profileData) => {
  try {
    const response = await apiBridge.put(ENDPOINTS.AUTH.PROFILE, profileData);
    return response.data.data;
  } catch (error) {
    BaseService.handleError(error, 'updateProfile', true);
    throw error;
  }
};

export default {
  updateProfile
}; 