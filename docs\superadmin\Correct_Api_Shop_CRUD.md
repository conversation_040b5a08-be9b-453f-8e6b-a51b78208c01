# 🎯 **Correct API Shop CRUD Documentation**

## 📋 **SuperAdmin Shop Management API - CORRECTED VERSION**

This document contains the **working and tested** SuperAdmin Shop CRUD API endpoints with exact payloads and responses.

---

## 🆕 **CREATE SHOP** 
### `POST http://localhost:5000/api/admin/shops`

**Method**: `POST`  
**Endpoint**: `http://localhost:5000/api/admin/shops`  
**Core Logic**: Create Shop with Owner Account  
**Authentication**: SuperAdmin Bearer Token Required

---

## 📝 **Request Payload Structure**

### **Option 1: JSON Request (No Logo)**
```json
{
  "fullName": "FITAH",
  "email": "<EMAIL>",
  "phone": "+************",
  "password": "Hnajiib12345$",
  "shopName": "KHANCIYE SHOP",
  "shopAddress": "Hodan, Mogadisho, Somalia",
  "planType": "monthly"
}
```

### **Option 2: Multipart Request (With Logo)**
```bash
Content-Type: multipart/form-data

fullName=FITAH
email=<EMAIL>
phone=+************
password=Hnajiib12345$
shopName=KHANCIYE SHOP
shopAddress=Hodan, Mogadisho, Somalia
planType=monthly
shopLogo=@deyncare_icon.png
```

---

## 🎯 **Field Specifications**

| **Field** | **Type** | **Required** | **Validation** | **Example** |
|-----------|----------|--------------|----------------|-------------|
| `fullName` | string | ✅ YES | 3-100 chars | "FITAH" |
| `email` | string | ✅ YES | Valid email format | "<EMAIL>" |
| `phone` | string | ✅ YES | International format (+252...) | "+************" |
| `password` | string | ✅ YES | Min 8 characters | "Hnajiib12345$" |
| `shopName` | string | ✅ YES | 2-100 chars | "KHANCIYE SHOP" |
| `shopAddress` | string |  YES | 5-200 chars | "Hodan, Mogadisho, Somalia" |
| `planType` | string | ❌ NO | trial/monthly/yearly | "monthly" (default) |
| `shopLogo` | file | ❌ NO | JPG/PNG/WebP, max 5MB | image file |


Get All ✅
Get All Stats ✅