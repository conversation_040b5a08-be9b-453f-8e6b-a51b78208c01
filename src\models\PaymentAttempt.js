/**
 * Payment Attempt Model
 * Tracks payment attempts and retries for subscription renewals
 */
const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const paymentAttemptSchema = new Schema(
  {
    subscription: {
      type: Schema.Types.ObjectId,
      ref: 'Subscription',
      required: true,
      index: true
    },
    shop: {
      type: Schema.Types.ObjectId,
      ref: 'Shop',
      required: true,
      index: true
    },
    paymentMethod: {
      type: String,
      required: true,
      enum: ['EVC', 'Cash'],
      default: 'EVC'
    },
    paymentType: {
      type: String,
      enum: ['online', 'offline'],
      required: true,
      default: function() {
        return this.paymentMethod === 'EVC' ? 'online' : 'offline';
      }
    },
    amount: {
      type: Number,
      required: true
    },
    currency: {
      type: String,
      required: true,
      default: 'USD'
    },
    attemptCount: {
      type: Number,
      required: true,
      default: 1
    },
    status: {
      type: String,
      required: true,
      enum: ['pending', 'succeeded', 'failed', 'cancelled'],
      default: 'pending',
      index: true
    },
    lastAttemptedAt: {
      type: Date,
      required: true,
      default: Date.now
    },
    nextRetryAt: {
      type: Date,
      index: true
    },
    lastFailureReason: {
      type: String
    },
    isRetryScheduled: {
      type: Boolean,
      default: false,
      index: true
    },
    transactionId: {
      type: String,
      sparse: true
    },
    paymentReference: {
      type: String,
      sparse: true
    },
    metadata: {
      type: Object
    }
  },
  {
    timestamps: true
  }
);

// Add compound index for finding payment attempts due for retry
paymentAttemptSchema.index({ status: 1, isRetryScheduled: 1, nextRetryAt: 1 });

// Ensure no more than 10 pending attempts per subscription to prevent abuse
paymentAttemptSchema.pre('save', async function (next) {
  if (this.isNew && this.status === 'pending') {
    const count = await this.constructor.countDocuments({
      subscription: this.subscription,
      status: 'pending'
    });
    
    if (count >= 10) {
      const error = new Error('Too many pending payment attempts for this subscription');
      return next(error);
    }
  }
  next();
});

module.exports = mongoose.model('PaymentAttempt', paymentAttemptSchema);
