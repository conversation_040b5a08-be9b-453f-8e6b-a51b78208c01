import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { RefreshCw, Settings, AlertCircle, CheckCircle, XCircle, Clock } from 'lucide-react';
import { usePaymentRetry } from '@/hooks/use-payment-retry';

/**
 * PaymentRetryManagement Component
 * Manages payment retry operations for SuperAdmin
 * 
 * @param {Object} props - Component props
 * @param {Object} props.config - Payment retry configuration
 * @param {boolean} props.loading - Loading state
 * @param {Function} props.onProcessAll - Callback to process all pending retries
 * @returns {JSX.Element} Rendered component
 */
const PaymentRetryManagement = ({ config, loading = false, onProcessAll }) => {
  const [processing, setProcessing] = useState(false);
  
  const {
    processAllPendingRetries,
    getRetryConfig
  } = usePaymentRetry();

  const handleProcessAll = async () => {
    setProcessing(true);
    try {
      await processAllPendingRetries();
      if (onProcessAll) {
        onProcessAll();
      }
    } catch (error) {
      console.error('Error processing retries:', error);
    } finally {
      setProcessing(false);
    }
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="grid gap-4 md:grid-cols-2">
          {[...Array(4)].map((_, i) => (
            <Card key={i}>
              <CardHeader>
                <Skeleton className="h-5 w-32" />
                <Skeleton className="h-4 w-48" />
              </CardHeader>
              <CardContent>
                <Skeleton className="h-8 w-16" />
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Configuration Overview */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Max Retry Attempts</CardTitle>
            <Settings className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{config?.maxRetries || 3}</div>
            <p className="text-xs text-muted-foreground">
              Maximum retry attempts per subscription
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Retry Interval</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{config?.retryInterval || 24}h</div>
            <p className="text-xs text-muted-foreground">
              Time between retry attempts
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pending Retries</CardTitle>
            <AlertCircle className="h-4 w-4 text-yellow-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{config?.pendingRetries || 0}</div>
            <p className="text-xs text-muted-foreground">
              Subscriptions awaiting retry
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Success Rate</CardTitle>
            <CheckCircle className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{config?.successRate || 85}%</div>
            <p className="text-xs text-muted-foreground">
              Retry success rate this month
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Payment Retry Actions</CardTitle>
          <CardDescription>
            Manage payment retry operations across all subscriptions
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex flex-wrap gap-2">
            <Button 
              onClick={handleProcessAll}
              disabled={processing || config?.pendingRetries === 0}
              variant="default"
            >
              <RefreshCw className={`h-4 w-4 mr-2 ${processing ? 'animate-spin' : ''}`} />
              {processing ? 'Processing...' : 'Process All Pending Retries'}
            </Button>

            <Button 
              onClick={() => getRetryConfig()}
              variant="outline"
            >
              <Settings className="h-4 w-4 mr-2" />
              Refresh Config
            </Button>
          </div>

          {config?.pendingRetries === 0 && (
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              <CheckCircle className="h-4 w-4 text-green-500" />
              No pending payment retries
            </div>
          )}
        </CardContent>
      </Card>

      {/* Recent Retry Activity */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Retry Activity</CardTitle>
          <CardDescription>Latest payment retry attempts and results</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {config?.recentActivity?.length > 0 ? (
              config.recentActivity.map((activity, index) => (
                <div key={index} className="flex items-center justify-between border-b pb-2">
                  <div className="flex items-center gap-2">
                    <div className="flex flex-col">
                      <span className="text-sm font-medium">{activity.subscriptionId}</span>
                      <span className="text-xs text-muted-foreground">{activity.timestamp}</span>
                    </div>
                  </div>
                  <Badge variant={activity.status === 'success' ? 'success' : activity.status === 'failed' ? 'destructive' : 'secondary'}>
                    {activity.status}
                  </Badge>
                </div>
              ))
            ) : (
              <div className="text-center py-4 text-muted-foreground">
                No recent retry activity
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Configuration Details */}
      <Card>
        <CardHeader>
          <CardTitle>Retry Configuration</CardTitle>
          <CardDescription>Current payment retry settings</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2">
            <div>
              <h4 className="font-medium mb-2">Retry Strategy</h4>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span>Max Attempts:</span>
                  <span>{config?.maxRetries || 3}</span>
                </div>
                <div className="flex justify-between">
                  <span>Initial Delay:</span>
                  <span>{config?.initialDelay || 24} hours</span>
                </div>
                <div className="flex justify-between">
                  <span>Backoff Multiplier:</span>
                  <span>{config?.backoffMultiplier || 2}x</span>
                </div>
              </div>
            </div>
            <div>
              <h4 className="font-medium mb-2">Notification Settings</h4>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span>Notify on Failure:</span>
                  <span>{config?.notifyOnFailure ? 'Enabled' : 'Disabled'}</span>
                </div>
                <div className="flex justify-between">
                  <span>Notify on Success:</span>
                  <span>{config?.notifyOnSuccess ? 'Enabled' : 'Disabled'}</span>
                </div>
                <div className="flex justify-between">
                  <span>Admin Notifications:</span>
                  <span>{config?.adminNotifications ? 'Enabled' : 'Disabled'}</span>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default PaymentRetryManagement; 