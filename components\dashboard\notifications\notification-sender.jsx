"use client";

import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { toast } from "sonner";
import {
  Send,
  Building2,
  Users,
  MessageSquare,
  AlertTriangle,
  CheckCircle,
  Clock,
  Target,
  Settings
} from "lucide-react";

/**
 * Enhanced Notification Sender Component
 * Provides forms for sending different types of notifications
 */
export function NotificationSender({
  sendToShops,
  sendBroadcast,
  sendDebtReminders,
  targets,
  isLoading = false
}) {
  const [notificationType, setNotificationType] = useState("shops");
  const [formData, setFormData] = useState({
    title: "",
    message: "",
    priority: "normal",
    shopIds: [],
    reminderType: "overdue",
    daysOverdue: "",
    actionUrl: "",
    actionLabel: ""
  });

  const [selectedShops, setSelectedShops] = useState([]);
  const [errors, setErrors] = useState({});

  // Handle form field changes
  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    
    // Clear error when field is changed
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: null
      }));
    }
  };

  // Validate form data
  const validateForm = () => {
    const newErrors = {};

    // Common validations
    if (notificationType !== "debt_reminders") {
      if (!formData.title.trim()) {
        newErrors.title = "Title is required";
      } else if (formData.title.length > 100) {
        newErrors.title = "Title cannot exceed 100 characters";
      }

      if (!formData.message.trim()) {
        newErrors.message = "Message is required";
      } else if (formData.message.length > 500) {
        newErrors.message = "Message cannot exceed 500 characters";
      }
    }

    // Shop-specific validations
    if (notificationType === "shops" && selectedShops.length === 0) {
      newErrors.shops = "At least one shop must be selected";
    }

    // Debt reminder validations
    if (notificationType === "debt_reminders") {
      if (formData.daysOverdue && (isNaN(formData.daysOverdue) || formData.daysOverdue < 0)) {
        newErrors.daysOverdue = "Days overdue must be a positive number";
      }
    }

    // URL validation
    if (formData.actionUrl && !/^https?:\/\/.+/.test(formData.actionUrl)) {
      newErrors.actionUrl = "Action URL must be a valid HTTP/HTTPS URL";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle shop selection
  const toggleShopSelection = (shopId) => {
    setSelectedShops(prev => {
      const newSelection = prev.includes(shopId)
        ? prev.filter(id => id !== shopId)
        : [...prev, shopId];
      
      handleInputChange('shopIds', newSelection);
      return newSelection;
    });
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      toast.error("Please fix the form errors before submitting");
      return;
    }

    try {
      let result;
      const submitData = { ...formData, shopIds: selectedShops };

      switch (notificationType) {
        case "shops":
          result = await sendToShops(submitData);
          break;
        case "broadcast":
          result = await sendBroadcast(submitData);
          break;
        case "debt_reminders":
          result = await sendDebtReminders(submitData);
          break;
        default:
          throw new Error("Invalid notification type");
      }

      // Reset form on success
      setFormData({
        title: "",
        message: "",
        priority: "normal",
        shopIds: [],
        reminderType: "overdue",
        daysOverdue: "",
        actionUrl: "",
        actionLabel: ""
      });
      setSelectedShops([]);
      
    } catch (error) {
      console.error("Failed to send notification:", error);
    }
  };

  // Clear all selections
  const clearSelections = () => {
    setSelectedShops([]);
    handleInputChange('shopIds', []);
  };

  // Select all shops
  const selectAllShops = () => {
    const allShopIds = targets?.shops?.map(shop => shop.shopId) || [];
    setSelectedShops(allShopIds);
    handleInputChange('shopIds', allShopIds);
  };

  const availableShops = targets?.shops || [];

  return (
    <div className="space-y-6">
      {/* Notification Type Selector */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Target className="h-5 w-5" />
            Notification Type
          </CardTitle>
          <CardDescription>
            Choose the type of notification you want to send
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Button
              variant={notificationType === "shops" ? "default" : "outline"}
              className="h-20 flex-col"
              onClick={() => setNotificationType("shops")}
            >
              <Building2 className="h-6 w-6 mb-2" />
              Send to Shops
            </Button>
            <Button
              variant={notificationType === "broadcast" ? "default" : "outline"}
              className="h-20 flex-col"
              onClick={() => setNotificationType("broadcast")}
            >
              <Users className="h-6 w-6 mb-2" />
              Broadcast Message
            </Button>
            <Button
              variant={notificationType === "debt_reminders" ? "default" : "outline"}
              className="h-20 flex-col"
              onClick={() => setNotificationType("debt_reminders")}
            >
              <MessageSquare className="h-6 w-6 mb-2" />
              Debt Reminders
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Notification Form */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Send className="h-5 w-5" />
            {notificationType === "shops" && "Send to Specific Shops"}
            {notificationType === "broadcast" && "Broadcast to All Users"}
            {notificationType === "debt_reminders" && "Send Debt Reminders"}
          </CardTitle>
          <CardDescription>
            {notificationType === "shops" && "Send targeted notifications to selected shops"}
            {notificationType === "broadcast" && "Send a notification to all active users"}
            {notificationType === "debt_reminders" && "Send automated debt reminder notifications"}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Shop Selection (for shop notifications) */}
            {notificationType === "shops" && (
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <Label className="text-base font-medium">Select Shops</Label>
                  <div className="flex gap-2">
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={selectAllShops}
                      disabled={availableShops.length === 0}
                    >
                      Select All
                    </Button>
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={clearSelections}
                      disabled={selectedShops.length === 0}
                    >
                      Clear All
                    </Button>
                  </div>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3 border rounded-lg p-4">
                  {availableShops.length > 0 ? (
                    availableShops.map((shop) => (
                      <div
                        key={shop.shopId}
                        className={`flex items-center space-x-3 p-3 rounded-lg border cursor-pointer transition-colors ${
                          selectedShops.includes(shop.shopId)
                            ? "bg-primary/10 border-primary"
                            : "hover:bg-muted"
                        }`}
                        onClick={() => toggleShopSelection(shop.shopId)}
                      >
                        <div className={`w-4 h-4 rounded border ${
                          selectedShops.includes(shop.shopId) 
                            ? "bg-primary border-primary" 
                            : "border-gray-300"
                        }`}>
                          {selectedShops.includes(shop.shopId) && (
                            <CheckCircle key={`check-${shop.shopId}`} className="w-4 h-4 text-white" />
                          )}
                        </div>
                        <div className="flex-1 min-w-0">
                          <p className="text-sm font-medium truncate">{shop.shopName}</p>
                          <p className="text-xs text-muted-foreground">ID: {shop.shopId}</p>
                        </div>
                      </div>
                    ))
                  ) : (
                    <div className="col-span-full text-center py-8">
                      <Building2 className="h-12 w-12 text-muted-foreground mx-auto mb-2" />
                      <p className="text-muted-foreground">No shops available</p>
                    </div>
                  )}
                </div>
                
                {selectedShops.length > 0 && (
                  <div className="flex items-center gap-2">
                    <Badge variant="secondary">
                      {selectedShops.length} shop{selectedShops.length !== 1 ? 's' : ''} selected
                    </Badge>
                  </div>
                )}
                
                {errors.shops && (
                  <p className="text-sm text-destructive">{errors.shops}</p>
                )}
              </div>
            )}

            {/* Title and Message (for shops and broadcast) */}
            {notificationType !== "debt_reminders" && (
              <div key="title-message-section">
                <div className="space-y-2">
                  <Label htmlFor="title">Notification Title *</Label>
                  <Input
                    id="title"
                    value={formData.title}
                    onChange={(e) => handleInputChange("title", e.target.value)}
                    placeholder="Enter notification title..."
                    maxLength={100}
                    className={errors.title ? "border-destructive" : ""}
                  />
                  <div className="flex justify-between text-sm text-muted-foreground">
                    {errors.title && <span key="title-error" className="text-destructive">{errors.title}</span>}
                    <span key="title-counter" className="ml-auto">{formData.title.length}/100</span>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="message">Message *</Label>
                  <Textarea
                    id="message"
                    value={formData.message}
                    onChange={(e) => handleInputChange("message", e.target.value)}
                    placeholder="Enter your notification message..."
                    maxLength={500}
                    rows={4}
                    className={errors.message ? "border-destructive" : ""}
                  />
                  <div className="flex justify-between text-sm text-muted-foreground">
                    {errors.message && <span key="message-error" className="text-destructive">{errors.message}</span>}
                    <span key="message-counter" className="ml-auto">{formData.message.length}/500</span>
                  </div>
                </div>
              </div>
            )}

            {/* Debt Reminder Specific Fields */}
            {notificationType === "debt_reminders" && (
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="reminderType">Reminder Type</Label>
                  <Select
                    value={formData.reminderType}
                    onValueChange={(value) => handleInputChange("reminderType", value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select reminder type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="7_days">7 Days Before Due</SelectItem>
                      <SelectItem value="3_days">3 Days Before Due</SelectItem>
                      <SelectItem value="overdue">Overdue Debts</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="daysOverdue">Days Overdue Filter (Optional)</Label>
                  <Input
                    id="daysOverdue"
                    type="number"
                    value={formData.daysOverdue}
                    onChange={(e) => handleInputChange("daysOverdue", e.target.value)}
                    placeholder="e.g., 30 (only debts overdue by this many days)"
                    min="0"
                    className={errors.daysOverdue ? "border-destructive" : ""}
                  />
                  {errors.daysOverdue && (
                    <p className="text-sm text-destructive">{errors.daysOverdue}</p>
                  )}
                </div>
              </div>
            )}

            {/* Priority Selection */}
            <div className="space-y-2">
              <Label htmlFor="priority">Priority</Label>
              <Select
                value={formData.priority}
                onValueChange={(value) => handleInputChange("priority", value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select priority" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="low">Low Priority</SelectItem>
                  <SelectItem value="normal">Normal Priority</SelectItem>
                  <SelectItem value="high">High Priority</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Optional Action */}
            <div className="space-y-4">
              <Label className="text-base font-medium">Optional Action (Advanced)</Label>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="actionUrl">Action URL</Label>
                  <Input
                    id="actionUrl"
                    value={formData.actionUrl}
                    onChange={(e) => handleInputChange("actionUrl", e.target.value)}
                    placeholder="https://example.com/action"
                    className={errors.actionUrl ? "border-destructive" : ""}
                  />
                  {errors.actionUrl && (
                    <p className="text-sm text-destructive">{errors.actionUrl}</p>
                  )}
                </div>
                <div className="space-y-2">
                  <Label htmlFor="actionLabel">Action Label</Label>
                  <Input
                    id="actionLabel"
                    value={formData.actionLabel}
                    onChange={(e) => handleInputChange("actionLabel", e.target.value)}
                    placeholder="e.g., View Details"
                    maxLength={50}
                  />
                </div>
              </div>
            </div>

            <Separator />

            {/* Submit Button */}
            <div className="flex items-center justify-between">
              <div className="text-sm text-muted-foreground">
                {notificationType === "shops" && selectedShops.length > 0 && 
                  `Ready to send to ${selectedShops.length} shop${selectedShops.length !== 1 ? 's' : ''}`
                }
                {notificationType === "broadcast" && 
                  `Ready to broadcast to all active users`
                }
                {notificationType === "debt_reminders" && 
                  `Ready to send debt reminders`
                }
              </div>
              <Button
                type="submit"
                disabled={isLoading}
                className="min-w-32"
              >
                {isLoading ? (
                  <>
                    <Clock className="h-4 w-4 mr-2 animate-spin" />
                    Sending...
                  </>
                ) : (
                  <>
                    <Send className="h-4 w-4 mr-2" />
                    Send Notification
                  </>
                )}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
} 