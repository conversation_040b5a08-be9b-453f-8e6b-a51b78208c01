# 🧪 Backend Permission System Testing Guide

## 📋 **Testing Workflow Overview**

1. **Login as Admin** → Get authentication token
2. **Create Employee** → With specific granular permissions
3. **Login as Employee** → Test permission boundaries
4. **Test Permission Enforcement** → Verify access control works

---

## 🔐 **Step 1: Admin Authentication**

### **Login as Admin**
**Endpoint:** `POST /api/auth/login`  
**Who Can Access:** Anyone (Public endpoint)  
**Purpose:** Get admin authentication token

**Request Payload:**
```json
{
  "email": "<EMAIL>",
  "password": "AdminPassword123!"
}
```

**Expected Response:**
```json
{
  "success": true,
  "message": "Login successful",
  "data": {
    "user": {
      "userId": "USR123456",
      "email": "<EMAIL>",
      "role": "admin",
      "shopId": "SHP123456"
    },
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "csrfToken": "csrf_token_here"
  }
}
```

**Save Token:** Copy the `token` value for subsequent requests

---

## 👥 **Step 2: Create Employee with Limited Permissions**

### **Create Employee (Customer Creation Only)**
**Endpoint:** `POST /api/auth/create-employee`  
**Who Can Access:** Admin, SuperAdmin  
**Purpose:** Create employee with only customer creation permission

**Headers:**
```
Authorization: Bearer {admin_token}
Content-Type: application/json
```

**Request Payload:**
```json
{
  "userTitle": "Customer Manager",
  "email": "<EMAIL>",
  "password": "EmployeePass123!",
  "confirmPassword": "EmployeePass123!",
  "visibility": {
    "customerManagement": {
      "create": true,
      "update": false,
      "view": true,
      "delete": false
    },
    "debtManagement": {
      "create": false,
      "update": false,
      "view": false,
      "delete": false
    },
    "reportManagement": {
      "generate": false,
      "delete": false,
      "view": false
    }
  }
}
```

**Expected Response:**
```json
{
  "success": true,
  "message": "Employee created successfully",
  "data": {
    "employee": {
      "userId": "EMP123456",
      "userTitle": "Customer Manager",
      "email": "<EMAIL>",
      "role": "employee",
      "shopId": "SHP123456",
      "visibility": {
        "customerManagement": {
          "create": true,
          "update": false,
          "view": true,
          "delete": false
        },
        "debtManagement": {
          "create": false,
          "update": false,
          "view": false,
          "delete": false
        },
        "reportManagement": {
          "generate": false,
          "delete": false,
          "view": false
        }
      }
    }
  }
}
```

---

## 🔑 **Step 3: Employee Authentication**

### **Login as Employee**
**Endpoint:** `POST /api/auth/login`  
**Who Can Access:** Anyone (Public endpoint)  
**Purpose:** Get employee authentication token

**Request Payload:**
```json
{
  "email": "<EMAIL>",
  "password": "EmployeePass123!"
}
```

**Expected Response:**
```json
{
  "success": true,
  "message": "Login successful",
  "data": {
    "user": {
      "userId": "EMP123456",
      "email": "<EMAIL>",
      "role": "employee",
      "shopId": "SHP123456",
      "visibility": {
        "customerManagement": {
          "create": true,
          "update": false,
          "view": true,
          "delete": false
        }
      }
    },
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "csrfToken": "csrf_token_here"
  }
}
```

**Save Token:** Copy the employee `token` for permission testing

---

## ✅ **Step 4: Test ALLOWED Operations**

### **Test 1: Create Customer (Should Work)**
**Endpoint:** `POST /api/customers`  
**Who Can Access:** Admin, Employee (with customerManagement.create = true)  
**Purpose:** Verify employee can create customers

**Headers:**
```
Authorization: Bearer {employee_token}
Content-Type: application/json
```

**Request Payload:**
```json
{
  "CustomerName": "John Doe",
  "phone": "+252612345678",
  "CustomerType": "New",
  "address": "Mogadishu, Somalia",
  "notes": "Regular customer"
}
```

**Expected Response:**
```json
{
  "success": true,
  "message": "Customer created successfully",
  "data": {
    "customer": {
      "customerId": "CUST123456",
      "CustomerName": "John Doe",
      "phone": "+252612345678",
      "CustomerType": "New"
    }
  }
}
```

### **Test 2: View Customers (Should Work)**
**Endpoint:** `GET /api/customers`  
**Who Can Access:** Admin, Employee (with customerManagement.view = true)  
**Purpose:** Verify employee can view customers

**Headers:**
```
Authorization: Bearer {employee_token}
```

**Expected Response:**
```json
{
  "success": true,
  "data": {
    "customers": [
      {
        "customerId": "CUST123456",
        "CustomerName": "John Doe",
        "phone": "+252612345678"
      }
    ],
    "pagination": {
      "current": 1,
      "pages": 1,
      "total": 1
    }
  }
}
```

---

## ❌ **Step 5: Test FORBIDDEN Operations**

### **Test 3: Update Customer (Should Fail)**
**Endpoint:** `PUT /api/customers/CUST123456`  
**Who Can Access:** Admin, Employee (with customerManagement.update = true)  
**Purpose:** Verify employee CANNOT update customers

**Headers:**
```
Authorization: Bearer {employee_token}
Content-Type: application/json
```

**Request Payload:**
```json
{
  "CustomerName": "John Doe Updated",
  "phone": "+252612345679"
}
```

**Expected Response (403 Forbidden):**
```json
{
  "success": false,
  "message": "Access denied: customerManagement.update permission required",
  "error": {
    "code": "permission_denied",
    "statusCode": 403
  }
}
```

### **Test 4: Delete Customer (Should Fail)**
**Endpoint:** `DELETE /api/customers/CUST123456`  
**Who Can Access:** Admin, Employee (with customerManagement.delete = true)  
**Purpose:** Verify employee CANNOT delete customers

**Headers:**
```
Authorization: Bearer {employee_token}
```

**Expected Response (403 Forbidden):**
```json
{
  "success": false,
  "message": "Access denied: customerManagement.delete permission required",
  "error": {
    "code": "permission_denied",
    "statusCode": 403
  }
}
```

### **Test 5: Create Debt (Should Fail)**
**Endpoint:** `POST /api/debts`  
**Who Can Access:** Admin, Employee (with debtManagement.create = true)  
**Purpose:** Verify employee CANNOT create debts

**Headers:**
```
Authorization: Bearer {employee_token}
Content-Type: application/json
```

**Request Payload:**
```json
{
  "customerId": "CUST123456",
  "DebtAmount": 1000,
  "DueDate": "2024-02-15",
  "Description": "Product purchase"
}
```

**Expected Response (403 Forbidden):**
```json
{
  "success": false,
  "message": "Access denied: debtManagement.create permission required",
  "error": {
    "code": "permission_denied",
    "statusCode": 403
  }
}
```

### **Test 6: Generate Report (Should Fail)**
**Endpoint:** `POST /api/reports`  
**Who Can Access:** Admin, Employee (with reportManagement.generate = true)  
**Purpose:** Verify employee CANNOT generate reports

**Headers:**
```
Authorization: Bearer {employee_token}
Content-Type: application/json
```

**Request Payload:**
```json
{
  "reportType": "customer_summary",
  "dateRange": {
    "startDate": "2024-01-01",
    "endDate": "2024-01-31"
  }
}
```

**Expected Response (403 Forbidden):**
```json
{
  "success": false,
  "message": "Access denied: reportManagement.generate permission required",
  "error": {
    "code": "permission_denied",
    "statusCode": 403
  }
}
```

---

## 🔄 **Step 6: Admin Override Test**

### **Test 7: Admin Can Do Everything**
**Endpoint:** `PUT /api/customers/CUST123456`  
**Who Can Access:** Admin (bypasses all permission checks)  
**Purpose:** Verify admin can update customers despite employee restrictions

**Headers:**
```
Authorization: Bearer {admin_token}
Content-Type: application/json
```

**Request Payload:**
```json
{
  "CustomerName": "John Doe - Updated by Admin",
  "phone": "+252612345679"
}
```

**Expected Response:**
```json
{
  "success": true,
  "message": "Customer updated successfully",
  "data": {
    "customer": {
      "customerId": "CUST123456",
      "CustomerName": "John Doe - Updated by Admin",
      "phone": "+252612345679"
    }
  }
}
```

---

## 📊 **Step 7: Employee Management Testing**

### **Test 8: Get All Employees (Admin Only)**
**Endpoint:** `GET /api/users/employees`  
**Who Can Access:** Admin only  
**Purpose:** Verify admin can view all employees

**Headers:**
```
Authorization: Bearer {admin_token}
```

**Expected Response:**
```json
{
  "success": true,
  "data": {
    "employees": [
      {
        "userId": "EMP123456",
        "userTitle": "Customer Manager",
        "email": "<EMAIL>",
        "role": "employee",
        "visibility": {
          "customerManagement": {
            "create": true,
            "update": false,
            "view": true,
            "delete": false
          }
        }
      }
    ],
    "pagination": {
      "current": 1,
      "pages": 1,
      "total": 1
    }
  }
}
```

### **Test 9: Update Employee Permissions**
**Endpoint:** `PUT /api/users/employees/EMP123456/permissions`  
**Who Can Access:** Admin only  
**Purpose:** Update employee permissions

**Headers:**
```
Authorization: Bearer {admin_token}
Content-Type: application/json
```

**Request Payload:**
```json
{
  "visibility": {
    "customerManagement": {
      "create": true,
      "update": true,
      "view": true,
      "delete": false
    },
    "debtManagement": {
      "create": true,
      "update": false,
      "view": true,
      "delete": false
    },
    "reportManagement": {
      "generate": false,
      "delete": false,
      "view": true
    }
  }
}
```

**Expected Response:**
```json
{
  "success": true,
  "message": "Employee permissions updated successfully",
  "data": {
    "employee": {
      "userId": "EMP123456",
      "visibility": {
        "customerManagement": {
          "create": true,
          "update": true,
          "view": true,
          "delete": false
        },
        "debtManagement": {
          "create": true,
          "update": false,
          "view": true,
          "delete": false
        }
      }
    }
  }
}
```

---

## 🎯 **Testing Summary Checklist**

| **Test Case** | **Expected Result** | **Status** |
|---------------|-------------------|------------|
| Admin Login | ✅ Success with token | ⬜ |
| Create Employee (Limited Permissions) | ✅ Success | ⬜ |
| Employee Login | ✅ Success with token | ⬜ |
| Employee Create Customer | ✅ Success (has permission) | ⬜ |
| Employee View Customers | ✅ Success (has permission) | ⬜ |
| Employee Update Customer | ❌ 403 Forbidden (no permission) | ⬜ |
| Employee Delete Customer | ❌ 403 Forbidden (no permission) | ⬜ |
| Employee Create Debt | ❌ 403 Forbidden (no permission) | ⬜ |
| Employee Generate Report | ❌ 403 Forbidden (no permission) | ⬜ |
| Admin Update Customer | ✅ Success (admin override) | ⬜ |
| Admin View Employees | ✅ Success | ⬜ |
| Admin Update Employee Permissions | ✅ Success | ⬜ |

**🎉 If all tests pass, the granular permission system is working correctly!**

---

## 🚀 **Quick Postman Setup**

### **Environment Variables**
Create these variables in Postman:
- `base_url`: `http://localhost:5000`
- `admin_token`: (Set after admin login)
- `employee_token`: (Set after employee login)

### **Collection Structure**
```
📁 Permission Testing
├── 🔐 Authentication
│   ├── Admin Login
│   └── Employee Login
├── 👥 Employee Management
│   ├── Create Employee
│   ├── Get Employees
│   └── Update Permissions
└── 🧪 Permission Tests
    ├── ✅ Allowed Operations
    │   ├── Create Customer
    │   └── View Customers
    └── ❌ Forbidden Operations
        ├── Update Customer
        ├── Delete Customer
        ├── Create Debt
        └── Generate Report
```

**🎯 Ready for testing! Import this guide into Postman and start testing the permission system.**