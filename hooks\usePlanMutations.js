"use client";

import { useState, useCallback, useRef } from 'react';
import { toast } from 'sonner';
import PlanService from '@/lib/services/plan';

/**
 * Plan Mutations Hook
 * 
 * Specialized hook for plan mutation operations (SuperAdmin only)
 * Handles create, update, delete, toggle, and duplicate operations
 */
export function usePlanMutations(options = {}) {
  const {
    showToastMessages = true,
    onSuccess,
    onError,
    onPlanCreated,
    onPlanUpdated,
    onPlanDeleted,
    onPlanToggled,
    onPlanDuplicated
  } = options;

  // Mutation states
  const [isCreating, setIsCreating] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [isToggling, setIsToggling] = useState(false);
  const [isDuplicating, setIsDuplicating] = useState(false);
  const [error, setError] = useState(null);

  // Track active operations to prevent conflicts
  const activeOperationsRef = useRef(new Set());

  /**
   * Create new plan (SuperAdmin only)
   * Backend forces all features to true automatically
   */
  const createPlan = useCallback(async (planData) => {
    if (activeOperationsRef.current.has('create')) {
      console.log('[usePlanMutations] Create operation already in progress');
      return null;
    }

    try {
      activeOperationsRef.current.add('create');
      setIsCreating(true);
      setError(null);
      
      if (showToastMessages) {
        toast.info('Creating plan...');
      }

      console.log('[usePlanMutations] Creating plan:', planData);

      // Validate required fields before sending to backend
      const requiredFields = ['name', 'type', 'displayName', 'pricing'];
      for (const field of requiredFields) {
        if (!planData[field]) {
          throw new Error(`${field} is required`);
        }
      }

      // Ensure pricing has required sub-fields
      if (!planData.pricing.basePrice && planData.pricing.basePrice !== 0) {
        throw new Error('pricing.basePrice is required');
      }
      if (!planData.pricing.billingCycle) {
        throw new Error('pricing.billingCycle is required');
      }

      const response = await PlanService.createPlan(planData);

      if (response.success) {
        const createdPlan = response.data;
        
        if (showToastMessages) {
          toast.success(`Plan "${planData.displayName}" created successfully`);
        }

        // Trigger callbacks
        if (onPlanCreated) onPlanCreated(createdPlan);
        if (onSuccess) onSuccess('create', createdPlan);

        console.log('[usePlanMutations] Plan created successfully:', createdPlan);
        return createdPlan;
      } else {
        throw new Error(response.message || 'Failed to create plan');
      }
    } catch (err) {
      console.error('[usePlanMutations] Error creating plan:', err);
      
      const errorMessage = err.message || 'Failed to create plan';
      setError(errorMessage);

      if (showToastMessages) {
        toast.error(errorMessage);
      }

      // Trigger error callback
      if (onError) onError('create', err);
      
      throw err;
    } finally {
      setIsCreating(false);
      activeOperationsRef.current.delete('create');
    }
  }, [showToastMessages, onSuccess, onError, onPlanCreated]);

  /**
   * Update existing plan (SuperAdmin only)
   * Backend forces all features to true automatically
   */
  const updatePlan = useCallback(async (planId, updateData) => {
    const operationKey = `update-${planId}`;
    
    if (activeOperationsRef.current.has(operationKey)) {
      console.log('[usePlanMutations] Update operation already in progress for plan:', planId);
      return null;
    }

    try {
      activeOperationsRef.current.add(operationKey);
      setIsUpdating(true);
      setError(null);
      
      if (showToastMessages) {
        toast.info('Updating plan...');
      }

      console.log('[usePlanMutations] Updating plan:', planId, updateData);

      if (!planId) {
        throw new Error('Plan ID is required');
      }

      if (!updateData || Object.keys(updateData).length === 0) {
        throw new Error('Update data is required');
      }

      const response = await PlanService.updatePlan(planId, updateData);

      if (response.success) {
        const updatedPlan = response.data;
        
        if (showToastMessages) {
          toast.success('Plan updated successfully');
        }

        // Trigger callbacks
        if (onPlanUpdated) onPlanUpdated(updatedPlan, updateData);
        if (onSuccess) onSuccess('update', updatedPlan);

        console.log('[usePlanMutations] Plan updated successfully:', updatedPlan);
        return updatedPlan;
      } else {
        throw new Error(response.message || 'Failed to update plan');
      }
    } catch (err) {
      console.error('[usePlanMutations] Error updating plan:', err);
      
      const errorMessage = err.message || 'Failed to update plan';
      setError(errorMessage);

      if (showToastMessages) {
        toast.error(errorMessage);
      }

      // Trigger error callback
      if (onError) onError('update', err);
      
      throw err;
    } finally {
      setIsUpdating(false);
      activeOperationsRef.current.delete(operationKey);
    }
  }, [showToastMessages, onSuccess, onError, onPlanUpdated]);

  /**
   * Toggle plan status (SuperAdmin only)
   * Activates or deactivates a plan
   */
  const togglePlanStatus = useCallback(async (planId, isActive) => {
    const operationKey = `toggle-${planId}`;
    
    if (activeOperationsRef.current.has(operationKey)) {
      console.log('[usePlanMutations] Toggle operation already in progress for plan:', planId);
      return null;
    }

    try {
      activeOperationsRef.current.add(operationKey);
      setIsToggling(true);
      setError(null);
      
      const statusText = isActive ? 'activating' : 'deactivating';
      if (showToastMessages) {
        toast.info(`${statusText.charAt(0).toUpperCase() + statusText.slice(1)} plan...`);
      }

      console.log('[usePlanMutations] Toggling plan status:', planId, isActive);

      if (!planId) {
        throw new Error('Plan ID is required');
      }

      if (typeof isActive !== 'boolean') {
        throw new Error('isActive must be a boolean');
      }

      const response = await PlanService.togglePlanStatus(planId, isActive);

      if (response.success) {
        const updatedPlan = response.data;
        
        if (showToastMessages) {
          toast.success(`Plan ${isActive ? 'activated' : 'deactivated'} successfully`);
        }

        // Trigger callbacks
        if (onPlanToggled) onPlanToggled(updatedPlan, isActive);
        if (onSuccess) onSuccess('toggle', updatedPlan);

        console.log('[usePlanMutations] Plan status toggled successfully:', updatedPlan);
        return updatedPlan;
      } else {
        throw new Error(response.message || 'Failed to toggle plan status');
      }
    } catch (err) {
      console.error('[usePlanMutations] Error toggling plan status:', err);
      
      const errorMessage = err.message || 'Failed to toggle plan status';
      setError(errorMessage);

      if (showToastMessages) {
        toast.error(errorMessage);
      }

      // Trigger error callback
      if (onError) onError('toggle', err);
      
      throw err;
    } finally {
      setIsToggling(false);
      activeOperationsRef.current.delete(operationKey);
    }
  }, [showToastMessages, onSuccess, onError, onPlanToggled]);

  /**
   * Delete plan (SuperAdmin only)
   * Backend performs soft delete
   */
  const deletePlan = useCallback(async (planId) => {
    const operationKey = `delete-${planId}`;
    
    if (activeOperationsRef.current.has(operationKey)) {
      console.log('[usePlanMutations] Delete operation already in progress for plan:', planId);
      return null;
    }

    try {
      activeOperationsRef.current.add(operationKey);
      setIsDeleting(true);
      setError(null);
      
      if (showToastMessages) {
        toast.info('Deleting plan...');
      }

      console.log('[usePlanMutations] Deleting plan:', planId);

      if (!planId) {
        throw new Error('Plan ID is required');
      }

      const response = await PlanService.deletePlan(planId);

      if (response.success) {
        if (showToastMessages) {
          toast.success('Plan deleted successfully');
        }

        // Trigger callbacks
        if (onPlanDeleted) onPlanDeleted(planId);
        if (onSuccess) onSuccess('delete', { planId });

        console.log('[usePlanMutations] Plan deleted successfully:', planId);
        return true;
      } else {
        throw new Error(response.message || 'Failed to delete plan');
      }
    } catch (err) {
      console.error('[usePlanMutations] Error deleting plan:', err);
      
      const errorMessage = err.message || 'Failed to delete plan';
      setError(errorMessage);

      if (showToastMessages) {
        toast.error(errorMessage);
      }

      // Trigger error callback
      if (onError) onError('delete', err);
      
      throw err;
    } finally {
      setIsDeleting(false);
      activeOperationsRef.current.delete(operationKey);
    }
  }, [showToastMessages, onSuccess, onError, onPlanDeleted]);

  /**
   * Duplicate plan (SuperAdmin only)
   */
  const duplicatePlan = useCallback(async (planId, newName, overrides = {}) => {
    const operationKey = `duplicate-${planId}`;
    
    if (activeOperationsRef.current.has(operationKey)) {
      console.log('[usePlanMutations] Duplicate operation already in progress for plan:', planId);
      return null;
    }

    try {
      activeOperationsRef.current.add(operationKey);
      setIsDuplicating(true);
      setError(null);
      
      if (showToastMessages) {
        toast.info('Duplicating plan...');
      }

      console.log('[usePlanMutations] Duplicating plan:', planId, newName, overrides);

      if (!planId) {
        throw new Error('Plan ID is required');
      }

      if (!newName) {
        throw new Error('New plan name is required');
      }

      const response = await PlanService.duplicatePlan(planId, newName, overrides);

      if (response.success) {
        const duplicatedPlan = response.data;
        
        if (showToastMessages) {
          toast.success(`Plan duplicated as "${newName}"`);
        }

        // Trigger callbacks
        if (onPlanDuplicated) onPlanDuplicated(duplicatedPlan, planId, newName);
        if (onSuccess) onSuccess('duplicate', duplicatedPlan);

        console.log('[usePlanMutations] Plan duplicated successfully:', duplicatedPlan);
        return duplicatedPlan;
      } else {
        throw new Error(response.message || 'Failed to duplicate plan');
      }
    } catch (err) {
      console.error('[usePlanMutations] Error duplicating plan:', err);
      
      const errorMessage = err.message || 'Failed to duplicate plan';
      setError(errorMessage);

      if (showToastMessages) {
        toast.error(errorMessage);
      }

      // Trigger error callback
      if (onError) onError('duplicate', err);
      
      throw err;
    } finally {
      setIsDuplicating(false);
      activeOperationsRef.current.delete(operationKey);
    }
  }, [showToastMessages, onSuccess, onError, onPlanDuplicated]);

  /**
   * Initialize default plans
   */
  const initializeDefaultPlans = useCallback(async () => {
    if (activeOperationsRef.current.has('initialize')) {
      console.log('[usePlanMutations] Initialize operation already in progress');
      return null;
    }

    try {
      activeOperationsRef.current.add('initialize');
      setIsCreating(true);
      setError(null);
      
      if (showToastMessages) {
        toast.info('Initializing default plans...');
      }

      console.log('[usePlanMutations] Initializing default plans');

      const response = await PlanService.initializeDefaultPlans();

      if (response.success) {
        if (response.created) {
          if (showToastMessages) {
            toast.success(response.message || 'Default plans created successfully');
          }
        } else {
          if (showToastMessages) {
            toast.info(response.message || 'Default plans already exist');
          }
        }

        // Trigger success callback
        if (onSuccess) onSuccess('initialize', response.data);

        console.log('[usePlanMutations] Default plans initialized:', response);
        return response.data;
      } else {
        throw new Error(response.message || 'Failed to initialize default plans');
      }
    } catch (err) {
      console.error('[usePlanMutations] Error initializing default plans:', err);
      
      const errorMessage = err.message || 'Failed to initialize default plans';
      setError(errorMessage);

      if (showToastMessages) {
        toast.error(errorMessage);
      }

      // Trigger error callback
      if (onError) onError('initialize', err);
      
      throw err;
    } finally {
      setIsCreating(false);
      activeOperationsRef.current.delete('initialize');
    }
  }, [showToastMessages, onSuccess, onError]);

  /**
   * Clear error state
   */
  const clearError = useCallback(() => {
    setError(null);
  }, []);

  /**
   * Check if any mutation is in progress
   */
  const isMutating = isCreating || isUpdating || isDeleting || isToggling || isDuplicating;

  return {
    // Mutation states
    isCreating,
    isUpdating,
    isDeleting,
    isToggling,
    isDuplicating,
    isMutating,
    error,

    // Mutation operations
    createPlan,
    updatePlan,
    togglePlanStatus,
    deletePlan,
    duplicatePlan,
    initializeDefaultPlans,

    // Utility functions
    clearError
  };
} 