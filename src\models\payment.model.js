const mongoose = require('mongoose');

const paymentSchema = new mongoose.Schema({
  // System IDs
  paymentId: {
    type: String,
    required: true,
    unique: true,
    trim: true
  },
  debtId: {
    type: String,
    trim: true
  },
  customerId: {
    type: String,
    required: true,
    trim: true
  },
  shopId: {
    type: String,
    required: true,
    trim: true
  },
  
  // Customer and Shop Information
  customerName: {
    type: String,
    trim: true
  },
  shopName: {
    type: String,
    trim: true
  },
  
  // Payment Context (Debt or Subscription)
  paymentContext: {
    type: String,
    enum: ['debt', 'subscription'],
    required: true
  },
  
  // Context-specific IDs
  subscriptionId: {
    type: String,
    trim: true
  },
  
  // Universal payment fields
  amount: {
    type: Number,
    required: true,
    min: 0
  },
  originalAmount: {
    type: Number,
    min: 0
  },
  discountAmount: {
    type: Number,
    default: 0
  },
  paymentDate: {
    type: Date,
    default: Date.now
  },
  
  // Dual Time Tracking - NEW FEATURE
  paidAtReal: {
    type: Date,
    default: Date.now // Staff-recorded real payment time (when customer actually paid)
  },
  
  // CSV Direct Mapping Fields - REQUIRED for ML (debt context only)
  // CustomerID removed - using customerId instead
  // DebtID removed - using debtId instead
  PaidAmount: {
    type: Number,
    min: 0
  },
  PaidDate: {
    type: Date
  },
  
  // Auto-calculated ML Fields (debt context only)
  PaymentDelay: {
    type: Number,
    default: 0
  },
  IsOnTime: {
    type: Boolean,
    default: true
  },
  
  // Payment method for all contexts
  method: {
    type: String,
    enum: ['cash', 'bank_transfer', 'mobile_money', 'card', 'other', 'offline', 'evc_plus'],
    default: 'cash'
  },
  paymentType: {
    type: String,
    enum: ['online', 'offline'],
    default: 'offline'
  },
  
  // Payment status and confirmation
  status: {
    type: String,
    enum: ['pending', 'processing', 'success', 'failed', 'approved', 'rejected'],
    default: 'pending'
  },
  isConfirmed: {
    type: Boolean,
    default: false
  },
  
  // Context-specific fields
  receiptNumber: {
    type: String,
    trim: true
  },
  reference: {
    type: String,
    trim: true
  },
  referenceNumber: {
    type: String,
    trim: true
  },
  
  // Payment proof
  proofFileId: {
    type: String,
    trim: true
  },
  
  // Refund information
  refund: {
    amount: {
      type: Number,
      default: 0
    }
  },
  
  // Gateway information
  gatewayInfo: {
    gatewayName: String,
    transactionId: String,
    responseCode: String,
    responseMessage: String,
    gatewayFee: {
      type: Number,
      default: 0
    }
  },
  
  // Integration status
  integrationStatus: {
    type: String,
    enum: ['pending', 'success', 'failed', 'not_applicable'],
    default: 'not_applicable'
  },
  
  // Session information
  sessionType: {
    type: String,
    enum: ['online', 'offline'],
    default: 'online'
  },
  
  // Sync status
  syncStatus: {
    type: String,
    enum: ['pending', 'synced', 'failed'],
    default: 'synced'
  },
  syncedAt: {
    type: Date,
    default: Date.now
  },
  
  // Verification attempts
  verificationAttempts: [{
    attemptedAt: {
      type: Date,
      default: Date.now
    },
    attemptedBy: {
      type: String,
      trim: true
    },
    status: {
      type: String,
      enum: ['successful', 'failed']
    },
    notes: {
      type: String,
      trim: true
    }
  }],
  
  // Approval Management (NEW - for SuperAdmin)
  approvedBy: {
    type: String,
    trim: true
  },
  approvedAt: {
    type: Date
  },
  approvalNotes: {
    type: String,
    trim: true
  },
  
  // Status History (NEW - for SuperAdmin)
  statusHistory: [{
    status: {
      type: String,
      enum: ['pending', 'processing', 'success', 'failed', 'approved', 'rejected']
    },
    changedAt: {
      type: Date,
      default: Date.now
    },
    changedBy: {
      type: String,
      trim: true
    },
    notes: {
      type: String,
      trim: true
    }
  }],
  
  // System fields
  notes: {
    type: String,
    trim: true
  },
  recordedBy: {
    type: String,
    trim: true
  },
  recordedFromIp: {
    type: String,
    trim: true
  },
  isDeleted: {
    type: Boolean,
    default: false
  },
  deletedAt: {
    type: Date
  }
}, {
  timestamps: true
});

// Pre-save middleware to handle context-specific fields
paymentSchema.pre('save', function(next) {
  if (this.paymentContext === 'debt') {
    // For debt payments, sync ML fields
    this.PaidAmount = this.amount;
    this.PaidDate = this.paymentDate;
  }
  
  // Add status to history if status changed
  if (this.isModified('status')) {
    if (!this.statusHistory) {
      this.statusHistory = [];
    }
    this.statusHistory.push({
      status: this.status,
      changedAt: new Date(),
      changedBy: this.recordedBy || 'system',
      notes: this.notes || 'Status updated'
    });
  }
  
  next();
});

// Virtual for getting related record ID based on context
paymentSchema.virtual('relatedRecord').get(function() {
  if (this.paymentContext === 'debt') return this.debtId;
  if (this.paymentContext === 'subscription') return this.subscriptionId;
  return null;
});

// Method to get CSV payload format for ML (debt payments only)
paymentSchema.methods.getCSVPayload = function() {
  if (this.paymentContext !== 'debt') {
    throw new Error('CSV payload only available for debt payments');
  }
  
  return {
    CustomerID: this.customerId, // Use customerId instead of CustomerID
    DebtID: this.debtId,
    PaidAmount: this.PaidAmount,
    PaidDate: this.PaidDate.toISOString().split('T')[0],
    PaymentDelay: this.PaymentDelay,
    IsOnTime: this.IsOnTime ? 1 : 0
  };
};

// Method to calculate payment timing relative to due date (debt payments only)
paymentSchema.methods.calculatePaymentTiming = function(dueDate) {
  if (this.paymentContext !== 'debt' || !dueDate) return;
  
  // Use paidAtReal for business logic timing calculation (when customer actually paid)
  // Fallback to PaidDate, then paymentDate if paidAtReal is not available
  const paymentDate = new Date(this.paidAtReal || this.PaidDate || this.paymentDate);
  const due = new Date(dueDate);
  
  // Ensure we have valid dates before calculation
  if (isNaN(paymentDate.getTime()) || isNaN(due.getTime())) {
    console.error('Invalid dates for payment timing calculation:', {
      paidAtReal: this.paidAtReal,
      paymentDate: this.PaidDate || this.paymentDate,
      dueDate
    });
    this.PaymentDelay = 0;
    this.IsOnTime = true;
    return Promise.resolve();
  }
  
  this.PaymentDelay = Math.ceil((paymentDate - due) / (1000 * 60 * 60 * 24));
  this.IsOnTime = this.PaymentDelay <= 0;
  
  return Promise.resolve();
};

// Method to approve payment (NEW - for SuperAdmin)
paymentSchema.methods.approvePayment = function(approvedBy, notes = '') {
  this.status = 'approved';
  this.isConfirmed = true;
  this.approvedBy = approvedBy;
  this.approvedAt = new Date();
  this.approvalNotes = notes;
  
  // Add to status history
  if (!this.statusHistory) {
    this.statusHistory = [];
  }
  this.statusHistory.push({
    status: 'approved',
    changedAt: new Date(),
    changedBy: approvedBy,
    notes: notes || 'Payment approved by SuperAdmin'
  });
  
  return this.save();
};

// Method to reject payment (NEW - for SuperAdmin)
paymentSchema.methods.rejectPayment = function(rejectedBy, notes = '') {
  this.status = 'rejected';
  this.isConfirmed = false;
  this.approvedBy = rejectedBy;
  this.approvedAt = new Date();
  this.approvalNotes = notes;
  
  // Add to status history
  if (!this.statusHistory) {
    this.statusHistory = [];
  }
  this.statusHistory.push({
    status: 'rejected',
    changedAt: new Date(),
    changedBy: rejectedBy,
    notes: notes || 'Payment rejected by SuperAdmin'
  });
  
  return this.save();
};

// Static method to find debt payments
paymentSchema.statics.findDebtPayments = function(shopId, options = {}) {
  const query = {
    shopId,
    paymentContext: 'debt',
    isDeleted: false
  };
  
  if (options.startDate && options.endDate) {
    query.paymentDate = {
      $gte: new Date(options.startDate),
      $lte: new Date(options.endDate)
    };
  }
  
  return this.find(query).sort({ paymentDate: -1 });
};

// Static method to find subscription payments
paymentSchema.statics.findSubscriptionPayments = function(shopId, options = {}) {
  const query = {
    shopId,
    paymentContext: 'subscription',
    isDeleted: false
  };
  
  if (options.startDate && options.endDate) {
    query.paymentDate = {
      $gte: new Date(options.startDate),
      $lte: new Date(options.endDate)
    };
  }
  
  return this.find(query).sort({ paymentDate: -1 });
};

// Static method to find all subscription payments (NEW - for SuperAdmin)
paymentSchema.statics.findAllSubscriptionPayments = function(options = {}) {
  const query = {
    paymentContext: 'subscription',
    isDeleted: false
  };
  
  // Add filters
  if (options.status) {
    query.status = options.status;
  }
  
  if (options.method) {
    query.method = options.method;
  }
  
  if (options.startDate && options.endDate) {
    query.createdAt = {
      $gte: new Date(options.startDate),
      $lte: new Date(options.endDate)
    };
  }
  
  if (options.search) {
    query.$or = [
      { customerName: { $regex: options.search, $options: 'i' } },
      { shopName: { $regex: options.search, $options: 'i' } },
      { paymentId: { $regex: options.search, $options: 'i' } }
    ];
  }
  
  const sort = {};
  if (options.sortBy) {
    sort[options.sortBy] = options.sortOrder === 'asc' ? 1 : -1;
  } else {
    sort.createdAt = -1; // Default sort by creation date
  }
  
  return this.find(query).sort(sort);
};

// Indexes
paymentSchema.index({ shopId: 1, customerId: 1 });
paymentSchema.index({ shopId: 1, paymentContext: 1 }); // Debt/Subscription separation
paymentSchema.index({ debtId: 1 });
paymentSchema.index({ subscriptionId: 1 });
paymentSchema.index({ customerId: 1 }); // Changed from CustomerID to customerId for ML

// NEW indexes for SuperAdmin queries
paymentSchema.index({ status: 1, createdAt: -1 });
paymentSchema.index({ approvedBy: 1, createdAt: -1 });
paymentSchema.index({ shopName: 1 });
paymentSchema.index({ customerName: 1 });
paymentSchema.index({ paymentContext: 1, status: 1 });
paymentSchema.index({ method: 1, createdAt: -1 });

const Payment = mongoose.model('Payment', paymentSchema);
module.exports = Payment;
