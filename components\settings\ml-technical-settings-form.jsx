/**
 * ML Technical Settings Form Component
 * Handles SuperAdmin-only technical ML settings
 * Matches backend: ml_api_base_url, ml_api_key, ml_predict_endpoint, ml_prediction_timeout, risk_data_retention_days
 */

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import { Alert, AlertDescription } from '../ui/alert';
import { CheckCircle, XCircle, Loader2, Shield, Server, Key, Globe, Clock, Database, Eye, EyeOff, TestTube } from 'lucide-react';
import { testMLConnection } from '../../lib/services/ml/riskAssessment';
import { useTechnicalMLSettingsQuery } from '../../hooks/use-technical-ml-settings-query';
import { getErrorMessage } from '../../lib/utils/errorHandler';

/**
 * ML Technical Settings Form Component
 * @param {Object} props - Component props
 * @param {Function} [props.onSave] - Callback when settings are saved successfully
 */
export default function MLTechnicalSettingsForm({ onSave }) {
  const {
    technicalMLSettings,
    isLoadingTechnicalMLSettings,
    isSavingTechnicalMLSettings,
    updateTechnicalMLSettings,
    technicalMLSettingsError
  } = useTechnicalMLSettingsQuery();

  // Form state matching backend technical ML settings structure
  const [formData, setFormData] = useState({
    mlApiBaseUrl: 'https://deyncare-ml.onrender.com',
    mlApiKey: '',
    mlPredictEndpoint: '/predict_single/',
    mlPredictionTimeout: 10,
    riskDataRetentionDays: 365
  });

  const [showPassword, setShowPassword] = useState(false);
  const [errors, setErrors] = useState({});
  const [saveStatus, setSaveStatus] = useState(null);

  // Load existing settings into form
  useEffect(() => {
    if (!isLoadingTechnicalMLSettings && technicalMLSettings) {
      console.log('[MLTechnicalSettingsForm] Loading technical ML settings:', technicalMLSettings);
      setFormData({
        mlApiBaseUrl: technicalMLSettings.mlApiBaseUrl || '',
        mlApiKey: technicalMLSettings.mlApiKey || '',
        mlPredictEndpoint: technicalMLSettings.mlPredictEndpoint || '/predict_single/',
        mlPredictionTimeout: technicalMLSettings.mlPredictionTimeout || 10,
        riskDataRetentionDays: technicalMLSettings.riskDataRetentionDays || 365
      });
    }
  }, [isLoadingTechnicalMLSettings, technicalMLSettings]);

  // Clear status messages after delay
  useEffect(() => {
    if (saveStatus) {
      const timer = setTimeout(() => setSaveStatus(null), 5000);
      return () => clearTimeout(timer);
    }
  }, [saveStatus]);

  // Validation based on backend requirements
  const validateField = (name, value) => {
    const rules = {
      mlApiBaseUrl: { 
        required: false, 
        pattern: /^https?:\/\/.+/,
        maxLength: 255,
        message: 'Must be a valid HTTP/HTTPS URL'
      },
      mlApiKey: { 
        required: false,
        minLength: 8,
        maxLength: 255,
        message: 'API key must be at least 8 characters'
      },
      mlPredictEndpoint: { 
        required: true,
        minLength: 1,
        maxLength: 100,
        pattern: /^\/.*\/$/,
        message: 'Must start and end with / (e.g., /predict_single/)'
      },
      mlPredictionTimeout: { 
        required: true,
        min: 1,
        max: 300,
        message: 'Timeout must be between 1 and 300 seconds'
      },
      riskDataRetentionDays: { 
        required: true,
        min: 1,
        max: 3650,
        message: 'Retention days must be between 1 and 3650 days'
      }
    };

    const rule = rules[name];
    if (!rule) return null;

    if (rule.required && (!value || value === '')) {
      return `${name.replace(/([A-Z])/g, ' $1').trim()} is required`;
    }

    if (value && value !== '') {
      if (rule.minLength && value.length < rule.minLength) {
        return rule.message || `Must be at least ${rule.minLength} characters`;
      }
      if (rule.maxLength && value.length > rule.maxLength) {
        return rule.message || `Cannot exceed ${rule.maxLength} characters`;
      }
      if (rule.pattern && !rule.pattern.test(value)) {
        return rule.message || 'Invalid format';
      }
      if (rule.min && Number(value) < rule.min) {
        return rule.message || `Must be at least ${rule.min}`;
      }
      if (rule.max && Number(value) > rule.max) {
        return rule.message || `Cannot exceed ${rule.max}`;
      }
    }

    return null;
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
    
    // Clear field error on change
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: null }));
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    // Validate all fields
    const newErrors = {};
    Object.keys(formData).forEach(key => {
      const error = validateField(key, formData[key]);
      if (error) newErrors[key] = error;
    });

    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors);
      return;
    }

    try {
      console.log('[MLTechnicalSettingsForm] Submitting technical ML settings:', formData);

      const result = await updateTechnicalMLSettings(formData);
      
      if (result && result.success) {
        setSaveStatus({ type: 'success', message: 'Technical ML settings updated successfully' });
        onSave?.(result);
      } else {
        setSaveStatus({ type: 'error', message: 'Failed to update technical ML settings' });
      }
    } catch (error) {
      console.error('[MLTechnicalSettingsForm] Save error:', error);
      setSaveStatus({ 
        type: 'error', 
        message: getErrorMessage(error, 'Failed to update technical ML settings')
      });
    }
  };

  const togglePasswordVisibility = () => {
    setShowPassword(prev => !prev);
  };

  if (isLoadingTechnicalMLSettings) {
    return (
      <Card className="bg-gradient-to-br from-red-50 to-orange-100 border-red-200 shadow-lg">
        <CardContent className="flex items-center justify-center p-8">
          <div className="text-center">
            <Loader2 className="w-8 h-8 animate-spin text-red-600 mx-auto mb-3" />
            <p className="text-red-800 font-medium">Loading technical ML settings...</p>
            <p className="text-red-600 text-sm mt-1">Fetching advanced configuration</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Error State */}
      {technicalMLSettingsError && (
        <Alert variant="destructive" className="border-l-4 border-l-red-500 bg-red-50 shadow-md">
          <XCircle className="h-5 w-5 text-red-600" />
          <AlertDescription className="text-red-800">
            Failed to load technical ML settings: {getErrorMessage(technicalMLSettingsError)}
          </AlertDescription>
        </Alert>
      )}

      {/* Save Status */}
      {saveStatus && (
        <Alert 
          variant={saveStatus.type === 'success' ? 'default' : 'destructive'}
          className={`border-l-4 shadow-md ${
            saveStatus.type === 'success' 
              ? 'border-l-green-500 bg-green-50' 
              : 'border-l-red-500 bg-red-50'
          }`}
        >
          {saveStatus.type === 'success' ? (
            <CheckCircle className="h-5 w-5 text-green-600" />
          ) : (
            <XCircle className="h-5 w-5 text-red-600" />
          )}
          <AlertDescription className={saveStatus.type === 'success' ? 'text-green-800' : 'text-red-800'}>
            {String(saveStatus.message || 'Status update')}
          </AlertDescription>
        </Alert>
      )}

      <form onSubmit={handleSubmit} className="space-y-8">
        {/* ML API Base URL */}
        <div className="bg-gradient-to-r from-blue-50 to-cyan-50 border-2 border-blue-200 rounded-xl p-6 hover:shadow-lg transition-all duration-200">
          <Label htmlFor="mlApiBaseUrl" className="flex items-center gap-3 text-lg font-semibold text-blue-900 mb-3">
            <div className="p-2 bg-blue-100 rounded-lg">
              <Globe className="w-5 h-5 text-blue-700" />
            </div>
            ML API Base URL
            <span className="text-sm font-normal text-blue-600 bg-blue-100 px-2 py-1 rounded-full">Optional</span>
          </Label>
          <Input
            id="mlApiBaseUrl"
            name="mlApiBaseUrl"
            type="url"
            value={formData.mlApiBaseUrl}
            onChange={handleInputChange}
            placeholder="https://ml-api.deyncare.com"
            className={`h-12 text-base ${errors.mlApiBaseUrl ? 'border-red-500 bg-red-50' : 'border-blue-300 focus:border-blue-500'}`}
          />
          {errors.mlApiBaseUrl && (
            <div className="flex items-center gap-2 mt-2 p-2 bg-red-100 rounded-lg">
              <XCircle className="w-4 h-4 text-red-600" />
              <p className="text-sm text-red-700 font-medium">{errors.mlApiBaseUrl}</p>
            </div>
          )}
          <p className="text-blue-700 mt-2 flex items-center gap-2">
            <Server className="w-4 h-4" />
            Full FastAPI base URL for the ML engine
          </p>
        </div>

        {/* ML API Key */}
        <div className="bg-gradient-to-r from-amber-50 to-orange-50 border-2 border-amber-200 rounded-xl p-6 hover:shadow-lg transition-all duration-200">
          <Label htmlFor="mlApiKey" className="flex items-center gap-3 text-lg font-semibold text-amber-900 mb-3">
            <div className="p-2 bg-amber-100 rounded-lg">
              <Key className="w-5 h-5 text-amber-700" />
            </div>
            ML API Key
            <span className="text-sm font-normal text-amber-600 bg-amber-100 px-2 py-1 rounded-full">Secret</span>
          </Label>
          <div className="relative">
            <Input
              id="mlApiKey"
              name="mlApiKey"
              type={showPassword ? 'text' : 'password'}
              value={formData.mlApiKey}
              onChange={handleInputChange}
              placeholder="Enter ML API authentication key"
              className={`h-12 text-base pr-12 ${errors.mlApiKey ? 'border-red-500 bg-red-50' : 'border-amber-300 focus:border-amber-500'}`}
            />
            <Button
              type="button"
              variant="ghost"
              size="sm"
              className="absolute right-0 top-0 h-full px-3 hover:bg-amber-100"
              onClick={togglePasswordVisibility}
            >
              {showPassword ? (
                <EyeOff className="h-5 w-5 text-amber-600" />
              ) : (
                <Eye className="h-5 w-5 text-amber-600" />
              )}
            </Button>
          </div>
          {errors.mlApiKey && (
            <div className="flex items-center gap-2 mt-2 p-2 bg-red-100 rounded-lg">
              <XCircle className="w-4 h-4 text-red-600" />
              <p className="text-sm text-red-700 font-medium">{errors.mlApiKey}</p>
            </div>
          )}
          <p className="text-amber-700 mt-2 flex items-center gap-2">
            <Shield className="w-4 h-4" />
            API key for authenticating with the ML server
          </p>
        </div>

        {/* ML Predict Endpoint */}
        <div className="bg-gradient-to-r from-green-50 to-emerald-50 border-2 border-green-200 rounded-xl p-6 hover:shadow-lg transition-all duration-200">
          <Label htmlFor="mlPredictEndpoint" className="flex items-center gap-3 text-lg font-semibold text-green-900 mb-3">
            <div className="p-2 bg-green-100 rounded-lg">
              <Server className="w-5 h-5 text-green-700" />
            </div>
            ML Predict Endpoint
            <span className="text-sm font-normal text-white bg-red-500 px-2 py-1 rounded-full">Required</span>
          </Label>
          <Input
            id="mlPredictEndpoint"
            name="mlPredictEndpoint"
            type="text"
            value={formData.mlPredictEndpoint}
            onChange={handleInputChange}
            placeholder="/predict_single/"
            className={`h-12 text-base ${errors.mlPredictEndpoint ? 'border-red-500 bg-red-50' : 'border-green-300 focus:border-green-500'}`}
          />
          {errors.mlPredictEndpoint && (
            <div className="flex items-center gap-2 mt-2 p-2 bg-red-100 rounded-lg">
              <XCircle className="w-4 h-4 text-red-600" />
              <p className="text-sm text-red-700 font-medium">{errors.mlPredictEndpoint}</p>
            </div>
          )}
          <p className="text-green-700 mt-2 flex items-center gap-2">
            <Server className="w-4 h-4" />
            API endpoint path for single predictions (e.g., /predict_single/)
          </p>
        </div>

        {/* ML Prediction Timeout */}
        <div className="bg-gradient-to-r from-purple-50 to-violet-50 border-2 border-purple-200 rounded-xl p-6 hover:shadow-lg transition-all duration-200">
          <Label htmlFor="mlPredictionTimeout" className="flex items-center gap-3 text-lg font-semibold text-purple-900 mb-3">
            <div className="p-2 bg-purple-100 rounded-lg">
              <Clock className="w-5 h-5 text-purple-700" />
            </div>
            Prediction Timeout (seconds)
            <span className="text-sm font-normal text-white bg-red-500 px-2 py-1 rounded-full">Required</span>
          </Label>
          <Input
            id="mlPredictionTimeout"
            name="mlPredictionTimeout"
            type="number"
            min="1"
            max="300"
            value={formData.mlPredictionTimeout}
            onChange={handleInputChange}
            placeholder="10"
            className={`h-12 text-base ${errors.mlPredictionTimeout ? 'border-red-500 bg-red-50' : 'border-purple-300 focus:border-purple-500'}`}
          />
          {errors.mlPredictionTimeout && (
            <div className="flex items-center gap-2 mt-2 p-2 bg-red-100 rounded-lg">
              <XCircle className="w-4 h-4 text-red-600" />
              <p className="text-sm text-red-700 font-medium">{errors.mlPredictionTimeout}</p>
            </div>
          )}
          <p className="text-purple-700 mt-2 flex items-center gap-2">
            <Clock className="w-4 h-4" />
            Maximum time to wait for ML API response (1-300 seconds)
          </p>
        </div>

        {/* Risk Data Retention Days */}
        <div className="bg-gradient-to-r from-indigo-50 to-blue-50 border-2 border-indigo-200 rounded-xl p-6 hover:shadow-lg transition-all duration-200">
          <Label htmlFor="riskDataRetentionDays" className="flex items-center gap-3 text-lg font-semibold text-indigo-900 mb-3">
            <div className="p-2 bg-indigo-100 rounded-lg">
              <Database className="w-5 h-5 text-indigo-700" />
            </div>
            Risk Data Retention (days)
            <span className="text-sm font-normal text-white bg-red-500 px-2 py-1 rounded-full">Required</span>
          </Label>
          <Input
            id="riskDataRetentionDays"
            name="riskDataRetentionDays"
            type="number"
            min="1"
            max="3650"
            value={formData.riskDataRetentionDays}
            onChange={handleInputChange}
            placeholder="365"
            className={`h-12 text-base ${errors.riskDataRetentionDays ? 'border-red-500 bg-red-50' : 'border-indigo-300 focus:border-indigo-500'}`}
          />
          {errors.riskDataRetentionDays && (
            <div className="flex items-center gap-2 mt-2 p-2 bg-red-100 rounded-lg">
              <XCircle className="w-4 h-4 text-red-600" />
              <p className="text-sm text-red-700 font-medium">{errors.riskDataRetentionDays}</p>
            </div>
          )}
          <p className="text-indigo-700 mt-2 flex items-center gap-2">
            <Database className="w-4 h-4" />
            How long to keep ML risk assessment results (1-3650 days)
          </p>
        </div>

        {/* Submit Button */}
        <div className="pt-8">
          <Button
            type="submit"
            disabled={isSavingTechnicalMLSettings}
            className="w-full h-14 text-lg font-semibold bg-gradient-to-r from-red-600 to-orange-600 hover:from-red-700 hover:to-orange-700 shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-[1.02]"
          >
            {isSavingTechnicalMLSettings ? (
              <div className="flex items-center gap-3">
                <Loader2 className="w-6 h-6 animate-spin" />
                <span>Saving Technical Settings...</span>
                <div className="flex gap-1">
                  <div className="w-1 h-1 bg-white rounded-full animate-pulse"></div>
                  <div className="w-1 h-1 bg-white rounded-full animate-pulse delay-75"></div>
                  <div className="w-1 h-1 bg-white rounded-full animate-pulse delay-150"></div>
                </div>
              </div>
            ) : (
              <div className="flex items-center gap-3">
                <Shield className="w-6 h-6" />
                <span>Save Technical ML Settings</span>
                <CheckCircle className="w-5 h-5 opacity-70" />
              </div>
            )}
          </Button>
        </div>

        {/* Debug Info (for development) */}
        {process.env.NODE_ENV === 'development' && (
          <div className="mt-8 p-6 bg-gradient-to-br from-gray-50 to-gray-100 rounded-xl border border-gray-200 shadow-sm">
            <details className="group">
              <summary className="cursor-pointer font-semibold text-gray-700 flex items-center gap-2 hover:text-gray-900 transition-colors">
                <div className="w-2 h-2 bg-gray-400 rounded-full group-open:bg-red-500 transition-colors"></div>
                Technical Debug Information
              </summary>
              <div className="mt-4 bg-gray-800 p-4 rounded-lg">
                <pre className="text-xs text-green-400 font-mono overflow-x-auto">
                  {JSON.stringify({
                    formData,
                    backendData: technicalMLSettings,
                    timestamp: new Date().toISOString(),
                    validationErrors: errors
                  }, null, 2)}
                </pre>
              </div>
            </details>
          </div>
        )}
      </form>
    </div>
  );
} 