/**
 * Payment Retry Service
 * Handles automatic retries for failed subscription payments
 */
const { logInfo, logError, logSuccess } = require('../../utils/logger');
const { Subscription, Shop } = require('../../models');
const PaymentAttempt = require('../../models/PaymentAttempt');
const SubscriptionEmailService = require('../email/subscriptionEmailService');
const paymentService = require('../paymentService');

/**
 * Configuration for payment retry attempts
 * Can be moved to environment variables for more flexibility
 */
const RETRY_CONFIG = {
  maxAttempts: 3,                  // Maximum number of retry attempts
  initialDelayHours: 24,           // Hours to wait before first retry
  subsequentDelayHours: 48,        // Hours between subsequent retries
  gracePeriodDays: 7,              // Days of grace period after expiration
  notificationThresholds: [3, 1]    // Days before expiry to send notifications
};

/**
 * Record a failed payment attempt and schedule a retry if appropriate
 * @param {Object} params - Parameters
 * @param {string} params.subscriptionId - Subscription ID
 * @param {string} params.paymentMethod - Payment method that failed (EVC or Cash)
 * @param {string} params.failureReason - Reason for payment failure
 * @param {number} params.amount - Payment amount
 * @param {string} params.currency - Currency code
 * @returns {Promise<Object>} - Retry information
 */
const PaymentRetryThrottle = require('../../models/PaymentRetryThrottle');

async function recordFailedPayment({ subscriptionId, paymentMethod, failureReason, amount, currency }) {
  try {
    // Throttle: Max 3 retries per day and 30s cooldown using MongoDB TTL collection
    const since = new Date(Date.now() - 24*60*60*1000);
    const retryCount = await PaymentRetryThrottle.countDocuments({
      subscriptionId,
      attemptedAt: { $gte: since }
    });
    if (retryCount >= 3) {
      throw new Error('Maximum retry attempts reached for today. Please try again tomorrow.');
    }
    // Throttle: 30s cooldown
    const lastAttempt = await PaymentRetryThrottle.findOne({ subscriptionId }).sort({ attemptedAt: -1 });
    if (lastAttempt && Date.now() - lastAttempt.attemptedAt.getTime() < 30 * 1000) {
      throw new Error('Please wait 30 seconds before retrying payment.');
    }
    // Insert new throttle doc
    // Retrieve shopId from subscription for proper logging
    let shopId = null;
    const subscriptionDoc = await Subscription.findById(subscriptionId);
    if (subscriptionDoc && subscriptionDoc.shopId) {
      shopId = subscriptionDoc.shopId;
    }
    await PaymentRetryThrottle.create({ subscriptionId, shopId });

    // Validate payment method is one of the allowed values
    if (!['EVC', 'Cash'].includes(paymentMethod)) {
      paymentMethod = paymentMethod === 'evc' ? 'EVC' : 'Cash'; // Handle legacy values
    }
    
    // Get the subscription
    const subscription = await Subscription.findById(subscriptionId).populate('shop').populate('plan');
    
    if (!subscription) {
      throw new Error(`Subscription not found: ${subscriptionId}`);
    }

    // Create or update payment attempt record
    const existingAttempt = await PaymentAttempt.findOne({
      subscription: subscriptionId,
      status: 'pending'
    });

    let paymentAttempt;
    
    if (existingAttempt) {
      // Update existing attempt
      existingAttempt.attemptCount += 1;
      existingAttempt.lastAttemptedAt = new Date();
      existingAttempt.lastFailureReason = failureReason;
      
      if (existingAttempt.attemptCount >= RETRY_CONFIG.maxAttempts) {
        existingAttempt.status = 'failed';
        existingAttempt.isRetryScheduled = false;
        // Explicitly mark subscription and shop as past_due/suspended if payment is required
        if (subscription && subscription.status !== 'active') {
          subscription.status = 'past_due';
          await subscription.save();
        }
        if (subscription && subscription.shop) {
          const shop = subscription.shop;
          if (shop.status !== 'active') {
            shop.status = 'suspended';
            await shop.save();
          }
        }
      } else {
        // Schedule next retry
        const delayHours = existingAttempt.attemptCount === 1 
          ? RETRY_CONFIG.initialDelayHours 
          : RETRY_CONFIG.subsequentDelayHours;
          
        existingAttempt.nextRetryAt = new Date(Date.now() + (delayHours * 60 * 60 * 1000));
        existingAttempt.isRetryScheduled = true;
      }
      
      paymentAttempt = await existingAttempt.save();
    } else {
      // Record failed payment attempt
      const paymentAttempt = await PaymentAttempt.create({
        subscriptionId,
        paymentMethod,
        failureReason,
        amount,
        currency,
        status: 'failed',
        attemptedAt: new Date(),
        nextRetryAt: new Date(Date.now() + (RETRY_CONFIG.initialDelayHours * 60 * 60 * 1000)),
        isRetryScheduled: true
      });
    }

    // Send notification email about failed payment
    if (subscription.shop && subscription.shop.email) {
      // If payment retry is successful, update statuses
      if (paymentAttempt.isRetryScheduled === false && existingAttempt && existingAttempt.status !== 'failed') {
        subscription.status = 'active';
        await subscription.save();
        if (subscription.shop) {
          subscription.shop.status = 'active';
          await subscription.shop.save();
        }
      }
      const shop = subscription.shop;
      const nextRetryDate = paymentAttempt.nextRetryAt;
      const hoursUntilNextRetry = Math.round((nextRetryDate - new Date()) / (60 * 60 * 1000));
      const remainingAttempts = RETRY_CONFIG.maxAttempts - paymentAttempt.attemptCount;

      if (paymentAttempt.isRetryScheduled) {
        await SubscriptionEmailService.sendPaymentFailedRetryEmail({
          email: shop.email,
          shopName: shop.name,
          planType: subscription.plan.type,
          amount: paymentAttempt.amount,
          currency: paymentAttempt.currency,
          paymentMethod,
          failureReason,
          nextRetryDate,
          hoursUntilNextRetry,
          remainingAttempts,
          gracePeriodDays: RETRY_CONFIG.gracePeriodDays
        });
      } else {
        // Send final failure notice if no more retries
        const daysRemaining = Math.round((subscription.expiresAt - new Date()) / (24 * 60 * 60 * 1000));
        
        await SubscriptionEmailService.sendAutoRenewalFailedEmail({
          email: shop.email,
          shopName: shop.name,
          planType: subscription.plan.type,
          amount: paymentAttempt.amount,
          currency: paymentAttempt.currency,
          expiryDate: subscription.expiresAt,
          daysRemaining: Math.max(0, daysRemaining),
          remainingAttempts: 0
        });
      }
    }

    logInfo(
      `Payment failed for subscription ${subscriptionId}. ` +
      `${paymentAttempt.isRetryScheduled ? 'Retry scheduled' : 'No more retries available'}.`,
      'paymentRetryService'
    );

    return {
      subscriptionId,
      attemptCount: paymentAttempt.attemptCount,
      isRetryScheduled: paymentAttempt.isRetryScheduled,
      nextRetryAt: paymentAttempt.nextRetryAt,
      remainingAttempts: RETRY_CONFIG.maxAttempts - paymentAttempt.attemptCount
    };
  } catch (error) {
    logError('Failed to record payment failure', 'paymentRetryService', error);
    throw error;
  }
}

/**
 * Process all pending payment retries that are due
 * @returns {Promise<Object>} - Results of retry processing
 */
async function processPendingRetries() {
  try {
    const now = new Date();
    
    // Find all payment attempts due for retry
    const pendingRetries = await PaymentAttempt.find({
      status: 'pending',
      isRetryScheduled: true,
      nextRetryAt: { $lte: now }
    }).populate({
      path: 'subscription',
      populate: [
        { path: 'shop' },
        { path: 'plan' }
      ]
    });

    logInfo(`Found ${pendingRetries.length} pending payment retries to process`, 'paymentRetryService');

    const results = {
      total: pendingRetries.length,
      successful: 0,
      failed: 0,
      details: []
    };

    // Process each retry
    for (const retry of pendingRetries) {
      try {
        if (!retry.subscription || !retry.subscription.shop) {
          // Skip if subscription or shop is missing
          retry.status = 'failed';
          retry.lastFailureReason = 'Subscription or shop not found';
          await retry.save();
          
          results.failed++;
          results.details.push({
            subscriptionId: retry.subscription ? retry.subscription._id : 'unknown',
            status: 'failed',
            reason: 'Subscription or shop not found'
          });
          continue;
        }

        const subscription = retry.subscription;
        const shop = subscription.shop;
        const plan = subscription.plan;
        
        // Attempt payment using payment service
        const paymentResult = await paymentService.processSubscriptionPayment({
          subscriptionId: subscription._id,
          shopId: shop._id,
          amount: retry.amount,
          currency: retry.currency,
          paymentMethod: retry.paymentMethod
        });

        if (paymentResult.success) {
          // Payment successful
          retry.status = 'succeeded';
          retry.isRetryScheduled = false;
          await retry.save();

          // Update subscription expiration
          const extensionMonths = plan.type === 'yearly' ? 12 : 1;
          const newExpiryDate = new Date(subscription.expiresAt);
          newExpiryDate.setMonth(newExpiryDate.getMonth() + extensionMonths);
          
          subscription.expiresAt = newExpiryDate;
          subscription.status = 'active';
          subscription.autoRenewFailures = 0;
          await subscription.save();

          // Send success notification
          await SubscriptionEmailService.sendSubscriptionRenewedEmail({
            email: shop.email,
            shopName: shop.name,
            planType: plan.type,
            endDate: newExpiryDate,
            price: retry.amount,
            currency: retry.currency
          });

          results.successful++;
          results.details.push({
            subscriptionId: subscription._id,
            status: 'succeeded',
            newExpiryDate
          });

          logSuccess(
            `Successfully processed payment retry for subscription ${subscription._id}`,
            'paymentRetryService'
          );
        } else {
          // Payment still failing
          await recordFailedPayment({
            subscriptionId: subscription._id,
            paymentMethod: retry.paymentMethod,
            failureReason: paymentResult.error || 'Unknown payment error',
            amount: retry.amount,
            currency: retry.currency
          });

          results.failed++;
          results.details.push({
            subscriptionId: subscription._id,
            status: 'failed',
            reason: paymentResult.error || 'Unknown payment error',
            attemptCount: retry.attemptCount + 1
          });
        }
      } catch (retryError) {
        logError(
          `Error processing payment retry for attempt ${retry._id}`,
          'paymentRetryService',
          retryError
        );

        results.failed++;
        results.details.push({
          subscriptionId: retry.subscription ? retry.subscription._id : 'unknown',
          status: 'error',
          reason: retryError.message
        });
      }
    }

    logInfo(
      `Completed processing ${results.total} payment retries. ` +
      `Successful: ${results.successful}, Failed: ${results.failed}`,
      'paymentRetryService'
    );

    return results;
  } catch (error) {
    logError('Failed to process pending retries', 'paymentRetryService', error);
    throw error;
  }
}

/**
 * Get retry status for a subscription
 * @param {string} subscriptionId - Subscription ID
 * @returns {Promise<Object>} - Retry status information
 */
async function getRetryStatus(subscriptionId) {
  try {
    const paymentAttempt = await PaymentAttempt.findOne({
      subscription: subscriptionId,
      status: 'pending'
    }).sort({ createdAt: -1 });

    if (!paymentAttempt) {
      return {
        hasActiveRetries: false,
        message: 'No pending payment retries found for this subscription'
      };
    }

    const remainingAttempts = RETRY_CONFIG.maxAttempts - paymentAttempt.attemptCount;
    const hoursUntilNextRetry = paymentAttempt.nextRetryAt 
      ? Math.round((paymentAttempt.nextRetryAt - new Date()) / (60 * 60 * 1000))
      : null;

    return {
      hasActiveRetries: paymentAttempt.isRetryScheduled,
      attemptCount: paymentAttempt.attemptCount,
      remainingAttempts,
      lastAttemptedAt: paymentAttempt.lastAttemptedAt,
      nextRetryAt: paymentAttempt.nextRetryAt,
      hoursUntilNextRetry,
      lastFailureReason: paymentAttempt.lastFailureReason
    };
  } catch (error) {
    logError(`Failed to get retry status for subscription ${subscriptionId}`, 'paymentRetryService', error);
    throw error;
  }
}

/**
 * Cancel scheduled retries for a subscription
 * @param {string} subscriptionId - Subscription ID
 * @returns {Promise<Object>} - Result of cancellation
 */
async function cancelScheduledRetries(subscriptionId) {
  try {
    const result = await PaymentAttempt.updateMany(
      {
        subscription: subscriptionId,
        status: 'pending',
        isRetryScheduled: true
      },
      {
        $set: {
          isRetryScheduled: false,
          status: 'cancelled',
          updatedAt: new Date()
        }
      }
    );

    logInfo(
      `Cancelled ${result.modifiedCount} scheduled payment retries for subscription ${subscriptionId}`,
      'paymentRetryService'
    );

    return {
      success: true,
      cancelledCount: result.modifiedCount
    };
  } catch (error) {
    logError(`Failed to cancel retries for subscription ${subscriptionId}`, 'paymentRetryService', error);
    throw error;
  }
}

/**
 * Manually trigger a retry for a subscription
 * @param {string} subscriptionId - Subscription ID
 * @returns {Promise<Object>} - Result of the manual retry
 */
async function triggerManualRetry(subscriptionId) {
  try {
    const subscription = await Subscription.findById(subscriptionId)
      .populate('shop')
      .populate('plan');
    
    if (!subscription) {
      throw new Error(`Subscription not found: ${subscriptionId}`);
    }

    // Get the most recent payment attempt
    const paymentAttempt = await PaymentAttempt.findOne({
      subscription: subscriptionId
    }).sort({ createdAt: -1 });

    if (!paymentAttempt) {
      throw new Error(`No payment attempts found for subscription ${subscriptionId}`);
    }

    // Attempt payment using payment service
    const paymentResult = await paymentService.processSubscriptionPayment({
      subscriptionId: subscription._id,
      shopId: subscription.shop._id,
      amount: paymentAttempt.amount,
      currency: paymentAttempt.currency,
      paymentMethod: paymentAttempt.paymentMethod
    });

    if (paymentResult.success) {
      // Payment successful
      paymentAttempt.status = 'succeeded';
      paymentAttempt.isRetryScheduled = false;
      await paymentAttempt.save();

      // Update subscription expiration
      const extensionMonths = subscription.plan.type === 'yearly' ? 12 : 1;
      const newExpiryDate = new Date(subscription.expiresAt);
      newExpiryDate.setMonth(newExpiryDate.getMonth() + extensionMonths);
      
      subscription.expiresAt = newExpiryDate;
      subscription.status = 'active';
      subscription.autoRenewFailures = 0;
      await subscription.save();

      // Send success notification
      await SubscriptionEmailService.sendSubscriptionRenewedEmail({
        email: subscription.shop.email,
        shopName: subscription.shop.name,
        planType: subscription.plan.type,
        endDate: newExpiryDate,
        price: paymentAttempt.amount,
        currency: paymentAttempt.currency
      });

      logSuccess(
        `Successfully processed manual payment retry for subscription ${subscription._id}`,
        'paymentRetryService'
      );

      return {
        success: true,
        message: 'Payment successful',
        newExpiryDate
      };
    } else {
      // Payment still failing
      await recordFailedPayment({
        subscriptionId: subscription._id,
        paymentMethod: paymentAttempt.paymentMethod,
        failureReason: paymentResult.error || 'Unknown payment error',
        amount: paymentAttempt.amount,
        currency: paymentAttempt.currency
      });

      return {
        success: false,
        message: 'Payment failed',
        error: paymentResult.error || 'Unknown payment error',
        retryScheduled: true
      };
    }
  } catch (error) {
    logError(`Failed to trigger manual retry for subscription ${subscriptionId}`, 'paymentRetryService', error);
    throw error;
  }
}

module.exports = {
  recordFailedPayment,
  processPendingRetries,
  getRetryStatus,
  cancelScheduledRetries,
  triggerManualRetry,
  RETRY_CONFIG
};
