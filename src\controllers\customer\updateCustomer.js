const { Customer, Debt } = require('../../models');
const { AppError, logError, logInfo } = require('../../utils');

/**
 * Update Customer Information
 * PUT /api/customers/:customerId
 */
const updateCustomer = async (req, res, next) => {
  try {
    const { customerId } = req.params;
    const { CustomerName, phone, notes } = req.body;
    const shopId = req.user.shopId;

    // Find existing customer
    const customer = await Customer.findOne({ 
      customerId, 
      shopId, 
      isDeleted: false 
    });

    if (!customer) {
      return res.status(404).json({
        success: false,
        message: 'Customer not found'
      });
    }

    // Store original values for comparison
    const originalName = customer.CustomerName;
    const originalPhone = customer.phone;

    // Update allowed fields
    if (CustomerName && CustomerName !== originalName) {
      customer.CustomerName = CustomerName;
      
      // Update the name in all associated debts
      await Debt.updateMany(
        { customerId, shopId, isDeleted: false },
        { $set: { CustomerName } }
      );
    }

    if (phone && phone !== originalPhone) {
      customer.phone = phone;
    }

    if (notes !== undefined) {
      customer.notes = notes;
    }

    // Save updated customer
    await customer.save();

    logInfo(`Customer updated: ${customerId} by ${req.user.email}`, 'UpdateCustomer');

    // Get updated customer with debt statistics
    const updatedCustomer = await Customer.aggregate([
      { $match: { customerId, shopId, isDeleted: false } },
      {
        $lookup: {
          from: 'debts',
          localField: 'customerId',
          foreignField: 'customerId',
          as: 'debts',
          pipeline: [
            { $match: { isDeleted: false } }
          ]
        }
      },
      {
        $addFields: {
          totalDebts: { $size: '$debts' },
          activeDebts: {
            $size: {
              $filter: {
                input: '$debts',
                cond: { $gt: ['$$this.OutstandingDebt', 0] }
              }
            }
          },
          totalOutstanding: { $sum: '$debts.OutstandingDebt' }
        }
      },
      {
        $project: {
          debts: 0 // Remove debts array from response
        }
      }
    ]);

    if (updatedCustomer.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Customer not found after update'
      });
    }

    const customerData = updatedCustomer[0];

    res.json({
      success: true,
      message: 'Customer updated successfully',
      data: {
        customerId: customerData.customerId,
        customerName: customerData.CustomerName,
        phone: customerData.phone,
        customerType: customerData.CustomerType,
        notes: customerData.notes,
        
        // Risk profile
        riskProfile: customerData.riskProfile || {
          currentRiskLevel: 'Not Assessed',
          riskScore: 0,
          lastAssessment: null,
          assessmentCount: 0
        },
        
        // Quick stats
        statistics: {
          totalDebts: customerData.totalDebts || 0,
          activeDebts: customerData.activeDebts || 0,
          totalOutstanding: customerData.totalOutstanding || 0
        },
        
        // Update info
        updatedAt: customerData.updatedAt,
        changesApplied: {
          nameChanged: CustomerName !== originalName,
          phoneChanged: phone !== originalPhone,
          notesUpdated: notes !== undefined
        }
      }
    });

  } catch (error) {
    logError('Failed to update customer', 'UpdateCustomer', error);
    return next(new AppError('Failed to update customer', 500));
  }
};

module.exports = updateCustomer; 