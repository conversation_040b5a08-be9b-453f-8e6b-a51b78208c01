"use client";

import { useState, useCallback, useRef } from 'react';
import { toast } from 'sonner';
import NotificationService from '@/lib/services/notification';

/**
 * FCM Token Management Hook
 * 
 * Backend-matched implementation for FCM token operations
 * Only includes endpoints that exist in the backend:
 * - Register FCM token
 * - Send test notification
 * 
 * Note: Backend does NOT have endpoints for:
 * - Unregister token
 * - Get user tokens
 * - Update token usage
 * - Cleanup expired tokens
 */
export function useFCMToken(options = {}) {
  const {
    showToastMessages = true,
    onSuccess,
    onError,
    onTokenRegistered,
    onTestSent
  } = options;

  // Operation states
  const [isRegistering, setIsRegistering] = useState(false);
  const [isSendingTest, setIsSendingTest] = useState(false);
  const [error, setError] = useState(null);

  // Track active operations to prevent conflicts
  const activeOperationsRef = useRef(new Set());

  /**
   * Register FCM token for current user
   * @param {Object} tokenData - Token registration data
   * @param {string} tokenData.token - FCM token string
   * @param {Object} [tokenData.deviceInfo] - Device information
   * @param {string} [tokenData.deviceInfo.deviceId] - Unique device identifier
   * @param {string} [tokenData.deviceInfo.platform] - Platform: android, ios (backend only accepts these)
   * @param {string} [tokenData.deviceInfo.appVersion] - App version
   * @param {string} [tokenData.deviceInfo.osVersion] - OS version
   */
  const registerToken = useCallback(async (tokenData) => {
    if (activeOperationsRef.current.has('register')) {
      console.log('[useFCMToken] Register operation already in progress');
      return null;
    }

    try {
      activeOperationsRef.current.add('register');
      setIsRegistering(true);
      setError(null);
      
      if (showToastMessages) {
        toast.info('Registering FCM token...');
      }

      console.log('[useFCMToken] Registering token:', tokenData);

      // Validate required fields
      if (!tokenData?.token) {
        throw new Error('FCM token is required');
      }

      // Validate platform if provided (backend only accepts android/ios)
      if (tokenData.deviceInfo?.platform && 
          !['android', 'ios'].includes(tokenData.deviceInfo.platform)) {
        throw new Error('Platform must be android or ios');
      }

      const response = await NotificationService.registerFCMToken(tokenData);

      if (response.success) {
        const result = response.data;
        
        if (showToastMessages) {
          toast.success('FCM token registered successfully');
        }

        // Trigger callbacks
        if (onTokenRegistered) onTokenRegistered(result);
        if (onSuccess) onSuccess('registerToken', result);

        console.log('[useFCMToken] Token registered successfully:', result);
        return result;
      } else {
        throw new Error(response.message || 'Failed to register FCM token');
      }
    } catch (err) {
      console.error('[useFCMToken] Error registering token:', err);
      
      const errorMessage = err.message || 'Failed to register FCM token';
      setError(errorMessage);

      if (showToastMessages) {
        toast.error(errorMessage);
      }

      if (onError) onError('registerToken', err);
      throw err;
    } finally {
      setIsRegistering(false);
      activeOperationsRef.current.delete('register');
    }
  }, [showToastMessages, onSuccess, onError, onTokenRegistered]);

  /**
   * Send test notification to current user
   * Backend sends a predefined test message to all user's active FCM tokens
   */
  const sendTestNotification = useCallback(async () => {
    if (activeOperationsRef.current.has('sendTest')) {
      console.log('[useFCMToken] Test notification operation already in progress');
      return null;
    }

    try {
      activeOperationsRef.current.add('sendTest');
      setIsSendingTest(true);
      setError(null);
      
      if (showToastMessages) {
        toast.info('Sending test notification...');
      }

      console.log('[useFCMToken] Sending test notification');

      const response = await NotificationService.sendTestNotification();

      if (response.success) {
        const result = response.data;
        
        if (showToastMessages) {
          toast.success('Test notification sent successfully');
        }

        // Trigger callbacks
        if (onTestSent) onTestSent(result);
        if (onSuccess) onSuccess('sendTestNotification', result);

        console.log('[useFCMToken] Test notification sent successfully:', result);
        return result;
      } else {
        throw new Error(response.message || 'Failed to send test notification');
      }
    } catch (err) {
      console.error('[useFCMToken] Error sending test notification:', err);
      
      const errorMessage = err.message || 'Failed to send test notification';
      setError(errorMessage);

      if (showToastMessages) {
        toast.error(errorMessage);
      }

      if (onError) onError('sendTestNotification', err);
      throw err;
    } finally {
      setIsSendingTest(false);
      activeOperationsRef.current.delete('sendTest');
    }
  }, [showToastMessages, onSuccess, onError, onTestSent]);

  /**
   * Clear error state
   */
  const clearError = useCallback(() => {
    setError(null);
  }, []);

  // Computed loading state
  const isLoading = isRegistering || isSendingTest;

  return {
    // Operations (only backend-available endpoints)
    registerToken,
    sendTestNotification,
    
    // Loading states
    isRegistering,
    isSendingTest,
    isLoading,
    
    // Error state
    error,
    clearError,
    
    // Helper methods for token validation
    validateTokenData: (tokenData) => {
      const errors = [];
      
      if (!tokenData?.token) {
        errors.push('FCM token is required');
      }
      
      if (tokenData.deviceInfo?.platform && 
          !['android', 'ios'].includes(tokenData.deviceInfo.platform)) {
        errors.push('Platform must be android or ios');
      }
      
      return {
        isValid: errors.length === 0,
        errors
      };
    }
  };
} 