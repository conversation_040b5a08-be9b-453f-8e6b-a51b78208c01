"use client";

import { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  Dialog<PERSON>eader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import {
  Tabs,
  TabsContent,
  <PERSON><PERSON>List,
  TabsTrigger,
} from "@/components/ui/tabs";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  FileText,
  Calendar,
  ArrowDownToLine,
  Send,
  BarChart3,
  FileCog,
  User,
  Building,
  Clock
} from "lucide-react";
import { Separator } from "@/components/ui/separator";
import { Skeleton } from "@/components/ui/skeleton";
import { toast } from "sonner";
import ReportService from './report-service';

/**
 * Dialog to display report details
 */
export function ReportDetailsDialog({ isOpen, onClose, report, onEmailReport }) {
  const [isLoading, setIsLoading] = useState(true);
  const [reportDetails, setReportDetails] = useState(null);
  const reportService = new ReportService();

  // Load report details
  useEffect(() => {
    async function loadReportDetails() {
      if (!report || !isOpen) return;
      
      setIsLoading(true);
      try {
        // In a real implementation, we would fetch detailed report data
        // For now, we'll just use the provided report
        const details = await reportService.getReportById(report.reportId);
        setReportDetails(details);
      } catch (error) {
        console.error('Error loading report details:', error);
        toast.error('Failed to load report details');
      } finally {
        setIsLoading(false);
      }
    }

    loadReportDetails();
  }, [report, isOpen]);

  /**
   * Handle report download
   */
  const handleDownload = () => {
    // In a real implementation, this would download the actual file
    const url = reportDetails?.url;
    
    toast.success('Downloading report...');
    
    // Mock download for development
    setTimeout(() => {
      window.open(url, '_blank');
    }, 500);
  };

  /**
   * Handle report email
   */
  const handleEmail = () => {
    if (onEmailReport && reportDetails) {
      onEmailReport(reportDetails);
    }
  };

  /**
   * Get report type badge
   */
  const getReportTypeBadge = (type) => {
    const colors = {
      'debt': 'bg-blue-50 text-blue-600 hover:bg-blue-100',
      'sales': 'bg-green-50 text-green-600 hover:bg-green-100',
      'ml-risk': 'bg-purple-50 text-purple-600 hover:bg-purple-100',
      'pos-profit': 'bg-amber-50 text-amber-600 hover:bg-amber-100',
    };

    const labels = {
      'debt': 'Debt Report',
      'sales': 'Sales Report',
      'ml-risk': 'Risk Analysis',
      'pos-profit': 'POS Profit'
    };

    return (
      <Badge variant="outline" className={colors[type] || ''}>
        {labels[type] || type}
      </Badge>
    );
  };

  /**
   * Get report format badge
   */
  const getReportFormatBadge = (format) => {
    const colors = {
      'pdf': 'bg-red-50 text-red-600 hover:bg-red-100',
      'csv': 'bg-emerald-50 text-emerald-600 hover:bg-emerald-100',
      'excel': 'bg-green-50 text-green-600 hover:bg-green-100',
    };

    const labels = {
      'pdf': 'PDF Document',
      'csv': 'CSV Spreadsheet',
      'excel': 'Excel Spreadsheet'
    };

    return (
      <Badge variant="outline" className={colors[format] || ''}>
        {labels[format] || format}
      </Badge>
    );
  };

  if (!isOpen) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-5xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-xl font-semibold">
            {isLoading ? (
              <Skeleton className="h-8 w-2/3" />
            ) : (
              <>
                <span className="mr-2">{reportDetails?.title}</span>
                {reportDetails?.type && getReportTypeBadge(reportDetails.type)}
              </>
            )}
          </DialogTitle>
        </DialogHeader>

        {isLoading ? (
          <div className="space-y-4">
            <Skeleton className="h-48 w-full" />
            <div className="grid grid-cols-2 gap-4">
              <Skeleton className="h-24 w-full" />
              <Skeleton className="h-24 w-full" />
            </div>
          </div>
        ) : (
          <Tabs defaultValue="overview" className="w-full">
            <TabsList className="grid grid-cols-2 mb-6">
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="parameters">Parameters</TabsTrigger>
            </TabsList>

            <TabsContent value="overview">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-base font-semibold flex items-center">
                      <FileText className="mr-2 h-5 w-5 text-muted-foreground" />
                      Report Information
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="space-y-2">
                      <div className="text-sm text-muted-foreground">Title</div>
                      <div className="font-medium">{reportDetails?.title}</div>
                    </div>
                    
                    <div className="space-y-2">
                      <div className="text-sm text-muted-foreground">Type</div>
                      <div>{getReportTypeBadge(reportDetails?.type)}</div>
                    </div>
                    
                    <div className="space-y-2">
                      <div className="text-sm text-muted-foreground">Format</div>
                      <div>{getReportFormatBadge(reportDetails?.format)}</div>
                    </div>
                    
                    <div className="space-y-2">
                      <div className="text-sm text-muted-foreground">Description</div>
                      <div className="text-sm">
                        {reportDetails?.description || 'No description provided'}
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="text-base font-semibold flex items-center">
                      <Clock className="mr-2 h-5 w-5 text-muted-foreground" />
                      Date & Source Information
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="space-y-2">
                      <div className="text-sm text-muted-foreground">Generated On</div>
                      <div className="font-medium">
                        {reportService.formatDate(reportDetails?.generatedAt)}
                      </div>
                    </div>
                    
                    <div className="space-y-2">
                      <div className="text-sm text-muted-foreground">Shop</div>
                      <div className="flex items-center">
                        <Building className="mr-2 h-4 w-4 text-muted-foreground" />
                        {reportDetails?.shopId === 'system' ? (
                          <Badge variant="outline" className="bg-indigo-50 text-indigo-600">
                            System-wide Report
                          </Badge>
                        ) : (
                          <span>{reportDetails?.shopId || 'Unknown'}</span>
                        )}
                      </div>
                    </div>
                    
                    <div className="space-y-2">
                      <div className="text-sm text-muted-foreground">Created By</div>
                      <div className="flex items-center">
                        <User className="mr-2 h-4 w-4 text-muted-foreground" />
                        <span>{reportDetails?.createdBy || 'Unknown'}</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              <div className="mt-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-base font-semibold flex items-center">
                      <BarChart3 className="mr-2 h-5 w-5 text-muted-foreground" />
                      Report Actions
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="flex flex-wrap gap-4">
                      <Button onClick={handleDownload} className="flex-1">
                        <ArrowDownToLine className="mr-2 h-4 w-4" />
                        Download Report
                      </Button>
                      
                      <Button onClick={handleEmail} variant="outline" className="flex-1">
                        <Send className="mr-2 h-4 w-4" />
                        Email Report
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            <TabsContent value="parameters">
              <Card>
                <CardHeader>
                  <CardTitle className="text-base font-semibold flex items-center">
                    <FileCog className="mr-2 h-5 w-5 text-muted-foreground" />
                    Report Parameters
                  </CardTitle>
                  <CardDescription>
                    Parameters used to generate this report
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <div className="text-sm text-muted-foreground">Date Range</div>
                      <div className="flex items-center space-x-2">
                        <Calendar className="h-4 w-4 text-muted-foreground" />
                        <span>
                          {reportDetails?.parameters?.startDate ? 
                            `${reportService.formatDate(reportDetails?.parameters?.startDate)} - ${reportService.formatDate(reportDetails?.parameters?.endDate)}` : 
                            'No date range specified'}
                        </span>
                      </div>
                    </div>
                  </div>
                  
                  {reportDetails?.parameters?.filters && Object.keys(reportDetails.parameters.filters).length > 0 && (
                    <>
                      <Separator />
                      <div className="space-y-2">
                        <div className="text-sm font-medium">Filters Applied</div>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                          {Object.entries(reportDetails.parameters.filters).map(([key, value]) => (
                            <div key={key} className="flex justify-between text-sm">
                              <span className="text-muted-foreground capitalize">{key.replace(/([A-Z])/g, ' $1')}</span>
                              <span>{value.toString()}</span>
                            </div>
                          ))}
                        </div>
                      </div>
                    </>
                  )}
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        )}

        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            Close
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
