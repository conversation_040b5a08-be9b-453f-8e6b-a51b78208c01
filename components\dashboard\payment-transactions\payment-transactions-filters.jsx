import React from 'react';
import { Search, Filter, X, Calendar } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '@/components/ui/select';

/**
 * PaymentTransactionsFilters Component
 * Filtering controls for payment transactions list
 * 
 * @param {Object} props - Component props
 * @param {string} props.searchQuery - Current search query
 * @param {Function} props.onSearchChange - Search change callback
 * @param {string} props.statusFilter - Current status filter
 * @param {Function} props.onStatusFilterChange - Status filter change callback
 * @param {string} props.paymentMethodFilter - Current payment method filter
 * @param {Function} props.onPaymentMethodFilterChange - Payment method filter change callback
 * @param {string} props.dateRangeFilter - Current date range filter
 * @param {Function} props.onDateRangeFilterChange - Date range filter change callback
 * @param {Function} props.onClearFilters - Clear all filters callback
 * @returns {JSX.Element} Rendered component
 */
const PaymentTransactionsFilters = ({
  searchQuery = '',
  onSearchChange,
  statusFilter = 'all',
  onStatusFilterChange,
  paymentMethodFilter = 'all',
  onPaymentMethodFilterChange,
  dateRangeFilter = 'all',
  onDateRangeFilterChange,
  onClearFilters
}) => {
  const hasActiveFilters = searchQuery || 
    statusFilter !== 'all' || 
    paymentMethodFilter !== 'all' || 
    dateRangeFilter !== 'all';

  return (
    <div className="space-y-4">
      {/* Search Bar */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="flex-1 relative">
          <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
          <Input
            placeholder="Search by shop name, payment ID, subscription ID..."
            className="pl-10 pr-10"
            value={searchQuery}
            onChange={(e) => onSearchChange?.(e.target.value)}
          />
          {searchQuery && (
            <X
              className="absolute right-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground cursor-pointer hover:text-foreground"
              onClick={() => onSearchChange?.('')}
            />
          )}
        </div>
      </div>

      {/* Filter Controls */}
      <div className="flex flex-wrap gap-3 items-center">
        {/* Status Filter */}
        <div className="min-w-[150px]">
          <Select value={statusFilter} onValueChange={onStatusFilterChange}>
            <SelectTrigger className="w-full">
              <Filter className="h-4 w-4 mr-2" />
              <SelectValue placeholder="Status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Statuses</SelectItem>
              <SelectItem value="pending">Pending</SelectItem>
              <SelectItem value="approved">Approved</SelectItem>
              <SelectItem value="rejected">Rejected</SelectItem>
              <SelectItem value="processing">Processing</SelectItem>
              <SelectItem value="completed">Completed</SelectItem>
              <SelectItem value="failed">Failed</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Payment Method Filter */}
        <div className="min-w-[160px]">
          <Select value={paymentMethodFilter} onValueChange={onPaymentMethodFilterChange}>
            <SelectTrigger className="w-full">
              <SelectValue placeholder="Payment Method" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Methods</SelectItem>
              <SelectItem value="evc">EVC Plus</SelectItem>
              <SelectItem value="bank_transfer">Bank Transfer</SelectItem>
              <SelectItem value="cash">Cash</SelectItem>
              <SelectItem value="mobile_money">Mobile Money</SelectItem>
              <SelectItem value="credit_card">Credit Card</SelectItem>
              <SelectItem value="debit_card">Debit Card</SelectItem>
              <SelectItem value="offline">Offline Payment</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Date Range Filter */}
        <div className="min-w-[140px]">
          <Select value={dateRangeFilter} onValueChange={onDateRangeFilterChange}>
            <SelectTrigger className="w-full">
              <Calendar className="h-4 w-4 mr-2" />
              <SelectValue placeholder="Date Range" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Time</SelectItem>
              <SelectItem value="today">Today</SelectItem>
              <SelectItem value="yesterday">Yesterday</SelectItem>
              <SelectItem value="week">This Week</SelectItem>
              <SelectItem value="month">This Month</SelectItem>
              <SelectItem value="quarter">This Quarter</SelectItem>
              <SelectItem value="year">This Year</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Clear Filters Button */}
        {hasActiveFilters && (
          <Button
            variant="outline"
            size="sm"
            onClick={onClearFilters}
            className="whitespace-nowrap"
          >
            <X className="h-4 w-4 mr-1" />
            Clear Filters
          </Button>
        )}
      </div>

      {/* Active Filters Indicator */}
      {hasActiveFilters && (
        <div className="text-sm text-muted-foreground">
          Showing filtered results
          {searchQuery && <span> for "{searchQuery}"</span>}
        </div>
      )}
    </div>
  );
};

export default PaymentTransactionsFilters; 