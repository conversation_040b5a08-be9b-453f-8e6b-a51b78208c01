# DeynCare Dashboard Logic Design

## 📊 Overview

This document outlines the comprehensive dashboard logic design for DeynCare, covering all core features, API architecture, and real-time data management strategies.

## 🏗️ Dashboard Architecture

### Core Features Analysis

#### 1. **User Management System**
- **Location**: `app/dashboard/users/`
- **Components**: `components/dashboard/users/`
- **Services**: `lib/services/user/`
- **Features**:
  - User creation and editing
  - Role management (SuperAdmin, Admin, Employee)
  - User status management
  - Bulk operations
  - User analytics and stats

#### 2. **Shop Management System**
- **Location**: `app/dashboard/shops/`
- **Components**: `components/dashboard/shops/`
- **Services**: `lib/services/shop/`
- **Features**:
  - Shop registration and onboarding
  - Shop details management
  - Shop status (active, suspended, pending)
  - Shop analytics and performance metrics
  - Shop-user associations

#### 3. **Plan Management System**
- **Location**: `app/dashboard/plans/`
- **Components**: `components/dashboard/plans/`
- **Services**: `lib/services/plan/`
- **Features**:
  - Plan creation and editing
  - Plan pricing and features
  - Plan activation/deactivation
  - Plan statistics and usage metrics
  - Plan template management

#### 4. **Subscription Management System**
- **Location**: `app/dashboard/subscriptions/`
- **Components**: `components/dashboard/subscriptions/`
- **Services**: `lib/services/subscription/`
- **Features**:
  - Subscription lifecycle management
  - Payment processing and retries
  - Subscription analytics
  - Bulk subscription operations
  - Renewal and cancellation management

#### 5. **Payment Management System**
- **Location**: `app/dashboard/payments/`
- **Components**: `components/dashboard/payments/`
- **Services**: `lib/services/payment/`
- **Features**:
  - Payment verification
  - Payment history and tracking
  - Payment method management
  - Payment analytics and reporting

#### 6. **Notification System**
- **Location**: `app/dashboard/settings/notifications/`
- **Components**: `components/dashboard/notifications/`
- **Services**: `lib/services/notification/`
- **Features**:
  - FCM token management
  - Notification broadcasting
  - Notification history
  - Real-time activity tracking

#### 7. **Reporting System**
- **Location**: `app/dashboard/reports/`
- **Components**: `components/dashboard/reports/`
- **Services**: `lib/services/export/`
- **Features**:
  - Report generation
  - Data export (PDF, Excel)
  - Email reporting
  - Report scheduling

#### 8. **Settings Management**
- **Location**: `app/dashboard/settings/`
- **Components**: `components/settings/`
- **Services**: `lib/services/settings/`
- **Features**:
  - General settings
  - Security settings
  - Payment method configuration
  - ML/AI settings
  - System logs

## 🔄 Current API Structure

### API Layer Architecture

```
lib/api/
├── bridge.js          # Main API client
├── config.js          # API configuration
├── contract.js        # API contracts and schemas
├── interceptors.js    # Request/response interceptors
├── token.js           # Token management
├── throttle.js        # Rate limiting
└── modules/
    ├── auth.js        # Authentication endpoints
    ├── user.js        # User management endpoints
    ├── shop.js        # Shop management endpoints
    ├── plan.js        # Plan management endpoints
    ├── subscription.js # Subscription endpoints
    ├── notification.js # Notification endpoints
    └── settings.js    # Settings endpoints
```

### Service Layer

```
lib/services/
├── baseService.js     # Base service class
├── auth/             # Authentication services
├── user/             # User management services
├── shop/             # Shop management services
├── plan/             # Plan management services
├── subscription/     # Subscription services
├── notification/     # Notification services
├── settings/         # Settings services
├── export/           # Export and reporting services
├── ml/               # ML/AI services
└── search/           # Search services
```

## 📈 Data Visualization Components

### Chart Components

```
components/dashboard/common/
├── chart-line-label.jsx    # Line charts with labels
├── chart-pie-interactive.jsx # Interactive pie charts
├── chart-radar-label.jsx   # Radar/spider charts
├── data-table.jsx         # Advanced data tables
└── kpi-card.jsx           # KPI metric cards
```

### Chart Integration Pattern

```javascript
// Example chart usage pattern
import { ChartContainer, ChartTooltip, ChartLegend } from "@/components/ui/chart"

const DashboardChart = ({ data, config }) => {
  return (
    <ChartContainer config={config}>
      <LineChart data={data}>
        <ChartTooltip />
        <ChartLegend />
        <Line dataKey="value" stroke="var(--color-primary)" />
      </LineChart>
    </ChartContainer>
  )
}
```

## 🚀 Proposed Centralized Dashboard System

### 1. **Dashboard Data Manager**

Create a centralized dashboard service that manages all data:

```javascript
// lib/services/dashboard/DashboardManager.js
class DashboardManager {
  constructor() {
    this.cache = new Map()
    this.subscribers = new Map()
    this.realTimeUpdates = new Map()
  }

  // Centralized data fetching with caching
  async fetchDashboardData(modules = []) {
    const promises = modules.map(module => this.fetchModuleData(module))
    const results = await Promise.all(promises)
    return this.aggregateData(results)
  }

  // Real-time data subscription
  subscribeToRealTimeUpdates(moduleId, callback) {
    if (!this.subscribers.has(moduleId)) {
      this.subscribers.set(moduleId, new Set())
    }
    this.subscribers.get(moduleId).add(callback)
  }

  // Cache management
  getCachedData(key) {
    return this.cache.get(key)
  }

  setCachedData(key, data, ttl = 300000) { // 5 minutes default
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl
    })
  }
}
```

### 2. **Real-time Data Hook**

```javascript
// hooks/use-dashboard-data.js
import { useState, useEffect } from 'react'
import { DashboardManager } from '@/lib/services/dashboard/DashboardManager'

export const useDashboardData = (modules = [], options = {}) => {
  const [data, setData] = useState({})
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)

  useEffect(() => {
    const dashboardManager = new DashboardManager()
    
    const fetchData = async () => {
      try {
        setLoading(true)
        const result = await dashboardManager.fetchDashboardData(modules)
        setData(result)
        setError(null)
      } catch (err) {
        setError(err)
      } finally {
        setLoading(false)
      }
    }

    fetchData()

    // Set up real-time updates
    if (options.realTime) {
      modules.forEach(module => {
        dashboardManager.subscribeToRealTimeUpdates(module, (newData) => {
          setData(prev => ({
            ...prev,
            [module]: newData
          }))
        })
      })
    }

    return () => {
      // Cleanup subscriptions
      dashboardManager.cleanup()
    }
  }, [modules, options.realTime])

  return { data, loading, error }
}
```

### 3. **Dashboard Context Provider**

```javascript
// contexts/dashboard-context.jsx
import { createContext, useContext, useReducer } from 'react'

const DashboardContext = createContext()

const dashboardReducer = (state, action) => {
  switch (action.type) {
    case 'SET_MODULE_DATA':
      return {
        ...state,
        [action.module]: action.data
      }
    case 'SET_LOADING':
      return {
        ...state,
        loading: {
          ...state.loading,
          [action.module]: action.loading
        }
      }
    case 'SET_ERROR':
      return {
        ...state,
        errors: {
          ...state.errors,
          [action.module]: action.error
        }
      }
    default:
      return state
  }
}

export const DashboardProvider = ({ children }) => {
  const [state, dispatch] = useReducer(dashboardReducer, {
    users: {},
    shops: {},
    plans: {},
    subscriptions: {},
    payments: {},
    notifications: {},
    reports: {},
    settings: {},
    loading: {},
    errors: {}
  })

  return (
    <DashboardContext.Provider value={{ state, dispatch }}>
      {children}
    </DashboardContext.Provider>
  )
}

export const useDashboard = () => {
  const context = useContext(DashboardContext)
  if (!context) {
    throw new Error('useDashboard must be used within a DashboardProvider')
  }
  return context
}
```

### 4. **Data Aggregation Service**

```javascript
// lib/services/dashboard/DataAggregator.js
export class DataAggregator {
  static aggregateUserStats(users) {
    return {
      total: users.length,
      active: users.filter(u => u.status === 'active').length,
      byRole: users.reduce((acc, user) => {
        acc[user.role] = (acc[user.role] || 0) + 1
        return acc
      }, {}),
      recentRegistrations: users.filter(u => 
        new Date(u.createdAt) > new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
      ).length
    }
  }

  static aggregateShopStats(shops) {
    return {
      total: shops.length,
      active: shops.filter(s => s.status === 'active').length,
      suspended: shops.filter(s => s.status === 'suspended').length,
      byCategory: shops.reduce((acc, shop) => {
        acc[shop.category] = (acc[shop.category] || 0) + 1
        return acc
      }, {}),
      revenue: shops.reduce((acc, shop) => acc + (shop.revenue || 0), 0)
    }
  }

  static aggregateSubscriptionStats(subscriptions) {
    return {
      total: subscriptions.length,
      active: subscriptions.filter(s => s.status === 'active').length,
      expired: subscriptions.filter(s => s.status === 'expired').length,
      revenue: subscriptions.reduce((acc, sub) => acc + (sub.amount || 0), 0),
      byPlan: subscriptions.reduce((acc, sub) => {
        acc[sub.planType] = (acc[sub.planType] || 0) + 1
        return acc
      }, {})
    }
  }
}
```

### 5. **Real-time Updates with WebSocket**

```javascript
// lib/services/dashboard/RealtimeService.js
export class RealtimeService {
  constructor() {
    this.ws = null
    this.reconnectAttempts = 0
    this.maxReconnectAttempts = 5
    this.subscribers = new Map()
  }

  connect() {
    this.ws = new WebSocket(process.env.NEXT_PUBLIC_WS_URL)
    
    this.ws.onopen = () => {
      console.log('WebSocket connected')
      this.reconnectAttempts = 0
    }

    this.ws.onmessage = (event) => {
      const data = JSON.parse(event.data)
      this.handleMessage(data)
    }

    this.ws.onclose = () => {
      console.log('WebSocket disconnected')
      this.reconnect()
    }

    this.ws.onerror = (error) => {
      console.error('WebSocket error:', error)
    }
  }

  handleMessage(data) {
    const { type, module, payload } = data
    
    if (this.subscribers.has(module)) {
      this.subscribers.get(module).forEach(callback => {
        callback(payload)
      })
    }
  }

  subscribe(module, callback) {
    if (!this.subscribers.has(module)) {
      this.subscribers.set(module, new Set())
    }
    this.subscribers.get(module).add(callback)
  }

  unsubscribe(module, callback) {
    if (this.subscribers.has(module)) {
      this.subscribers.get(module).delete(callback)
    }
  }

  reconnect() {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++
      setTimeout(() => this.connect(), 1000 * this.reconnectAttempts)
    }
  }
}
```

## 🔧 Implementation Strategy

### Phase 1: Core Infrastructure

1. **Create Dashboard Manager**
   - Implement centralized data fetching
   - Add caching mechanism
   - Create subscription system

2. **Implement Real-time Updates**
   - Set up WebSocket connection
   - Create message handling system
   - Implement reconnection logic

3. **Create Dashboard Context**
   - Set up global state management
   - Create reducer for dashboard actions
   - Implement context provider

### Phase 2: Data Integration

1. **Aggregate Existing APIs**
   - Combine all service calls
   - Implement parallel data fetching
   - Add error handling and retries

2. **Create Dashboard Hooks**
   - Implement `useDashboardData` hook
   - Create module-specific hooks
   - Add loading and error states

3. **Optimize Performance**
   - Implement data caching
   - Add request deduplication
   - Create lazy loading strategies

### Phase 3: Advanced Features

1. **Real-time Charts**
   - Connect charts to real-time data
   - Implement smooth transitions
   - Add interactive features

2. **Advanced Analytics**
   - Create data aggregation services
   - Implement trend analysis
   - Add predictive insights

3. **Performance Monitoring**
   - Add performance metrics
   - Implement error tracking
   - Create dashboard health checks

## 📊 Dashboard Layout Structure

```
Dashboard Layout
├── Header
│   ├── Global Search
│   ├── Notifications
│   └── User Profile
├── Sidebar
│   ├── Navigation Menu
│   ├── Quick Actions
│   └── System Status
└── Main Content
    ├── KPI Cards Row
    ├── Charts Section
    │   ├── User Growth Chart
    │   ├── Revenue Chart
    │   └── Subscription Stats
    ├── Recent Activities
    └── Quick Actions Panel
```

## 🎯 Key Performance Indicators (KPIs)

### User Management KPIs
- Total Users
- Active Users
- User Growth Rate
- User Retention Rate
- Users by Role Distribution

### Shop Management KPIs
- Total Shops
- Active Shops
- Shop Registration Rate
- Shop Revenue
- Shop Performance Metrics

### Subscription KPIs
- Total Subscriptions
- Active Subscriptions
- Subscription Revenue
- Churn Rate
- Plan Distribution

### Payment KPIs
- Total Revenue
- Payment Success Rate
- Failed Payments
- Payment Methods Distribution
- Revenue Growth

## 🔄 Data Flow Architecture

```
User Action → Dashboard Component → Hook → Dashboard Manager → API Service → Backend API
     ↓                                                              ↓
Real-time Updates ← WebSocket ← Dashboard Manager ← API Response ← Backend
     ↓
Dashboard State Update → Component Re-render → UI Update
```

## 🛠️ Technical Implementation Details

### API Call Optimization

```javascript
// Batch API calls to reduce network requests
const batchApiCalls = async (endpoints) => {
  const promises = endpoints.map(endpoint => apiCall(endpoint))
  const results = await Promise.allSettled(promises)
  return results.reduce((acc, result, index) => {
    if (result.status === 'fulfilled') {
      acc[endpoints[index]] = result.value
    } else {
      acc[endpoints[index]] = { error: result.reason }
    }
    return acc
  }, {})
}
```

### Cache Strategy

```javascript
// Implement smart caching with TTL
class CacheManager {
  constructor() {
    this.cache = new Map()
    this.ttl = new Map()
  }

  set(key, value, ttl = 300000) {
    this.cache.set(key, value)
    this.ttl.set(key, Date.now() + ttl)
  }

  get(key) {
    if (this.isExpired(key)) {
      this.delete(key)
      return null
    }
    return this.cache.get(key)
  }

  isExpired(key) {
    const expiry = this.ttl.get(key)
    return expiry && Date.now() > expiry
  }
}
```

### Error Handling Strategy

```javascript
// Comprehensive error handling
class ErrorHandler {
  static handleApiError(error, module) {
    const errorMessage = error.response?.data?.message || error.message
    
    // Log error for debugging
    console.error(`[${module}] API Error:`, errorMessage)
    
    // Show user-friendly message
    toast.error(this.getUserFriendlyMessage(error))
    
    // Send to error tracking service
    this.trackError(error, module)
  }

  static getUserFriendlyMessage(error) {
    const status = error.response?.status
    switch (status) {
      case 401:
        return "Please log in again"
      case 403:
        return "You don't have permission to perform this action"
      case 500:
        return "Server error. Please try again later"
      default:
        return "Something went wrong. Please try again"
    }
  }
}
```

## 🚀 Future Enhancements

### 1. **AI-Powered Insights**
- Predictive analytics for user behavior
- Automated report generation
- Smart notifications and alerts

### 2. **Advanced Visualizations**
- Interactive dashboard widgets
- Custom chart builders
- Real-time map visualizations

### 3. **Performance Optimization**
- Server-side rendering for charts
- Progressive data loading
- Background data synchronization

### 4. **Mobile Optimization**
- Responsive dashboard design
- Touch-friendly interactions
- Offline capability

## 📝 Conclusion

This dashboard logic design provides a comprehensive, scalable, and maintainable approach to managing all DeynCare features. The centralized API architecture ensures efficient data management, while real-time updates keep the dashboard responsive and current.

The proposed system eliminates redundant API calls, provides consistent data across all components, and sets the foundation for future enhancements and scaling. 