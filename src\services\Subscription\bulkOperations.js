/**
 * Subscription Bulk Operations Service
 * Handles bulk operations on subscriptions for SuperAdmin
 */
const { Subscription, Plan } = require('../../models');
const { logInfo, logSuccess, logError } = require('../../utils');

/**
 * Bulk update subscription status
 * @param {Array} subscriptionIds - Array of subscription IDs
 * @param {String} status - New status value
 * @param {Object} options - Additional options
 * @returns {Object} - Result of the bulk operation
 */
const bulkUpdateStatus = async (subscriptionIds, status, options = {}) => {
  const { actorId, actorRole } = options;
  const allowedStatuses = ['active', 'cancelled', 'suspended', 'expired'];
  
  if (!allowedStatuses.includes(status)) {
    throw new Error(`Invalid status: ${status}. Allowed values: ${allowedStatuses.join(', ')}`);
  }
  
  logInfo(`Bulk updating status to ${status} for ${subscriptionIds.length} subscriptions`, 'SubscriptionService');
  
  const errors = [];
  let updatedCount = 0;
  
  // Process each subscription individually to handle errors gracefully
  for (const subscriptionId of subscriptionIds) {
    try {
      const subscription = await Subscription.findOne({ subscriptionId });
      
      if (!subscription) {
        errors.push({ subscriptionId, error: 'Subscription not found' });
        continue;
      }
      
      // Skip if status is already set
      if (subscription.status === status) {
        continue;
      }
      
      // Update status and add audit info
      subscription.status = status;
      subscription.updatedBy = actorId;
      
      // Add status change history
      subscription.statusHistory = subscription.statusHistory || [];
      subscription.statusHistory.push({
        from: subscription.status,
        to: status,
        changedAt: new Date(),
        changedBy: actorId,
        changedByRole: actorRole,
        reason: `Bulk update by ${actorRole}`
      });
      
      await subscription.save();
      updatedCount++;
      
    } catch (error) {
      logError(`Failed to update status for subscription ${subscriptionId}`, 'SubscriptionService', error);
      errors.push({ subscriptionId, error: error.message });
    }
  }
  
  return {
    updatedCount,
    failedCount: subscriptionIds.length - updatedCount,
    errors
  };
};

/**
 * Bulk change subscription plans
 * @param {Array} subscriptionIds - Array of subscription IDs
 * @param {String} planId - New plan ID
 * @param {Object} options - Additional options
 * @returns {Object} - Result of the bulk operation
 */
const bulkChangePlan = async (subscriptionIds, planId, options = {}) => {
  const { actorId, actorRole } = options;
  
  // Verify plan exists
  const plan = await Plan.findOne({ planId });
  if (!plan) {
    throw new Error(`Plan with ID ${planId} not found`);
  }
  
  logInfo(`Bulk changing plan to ${plan.name} for ${subscriptionIds.length} subscriptions`, 'SubscriptionService');
  
  const errors = [];
  let updatedCount = 0;
  
  for (const subscriptionId of subscriptionIds) {
    try {
      const subscription = await Subscription.findOne({ subscriptionId });
      
      if (!subscription) {
        errors.push({ subscriptionId, error: 'Subscription not found' });
        continue;
      }
      
      // Skip if plan is already set
      if (subscription.planId === planId) {
        continue;
      }
      
      // Store old plan info
      const oldPlan = {
        planId: subscription.planId,
        type: subscription.plan?.type,
        name: subscription.plan?.name
      };
      
      // Update plan info
      subscription.planId = planId;
      subscription.plan = {
        type: plan.type,
        name: plan.name,
        pricing: plan.pricing,
        features: plan.features
      };
      
      subscription.updatedBy = actorId;
      
      // Add plan change history
      subscription.planChanges = subscription.planChanges || [];
      subscription.planChanges.push({
        oldPlan,
        newPlan: {
          planId,
          type: plan.type,
          name: plan.name
        },
        changedAt: new Date(),
        changedBy: actorId,
        changedByRole: actorRole,
        reason: `Bulk update by ${actorRole}`
      });
      
      await subscription.save();
      updatedCount++;
      
    } catch (error) {
      logError(`Failed to change plan for subscription ${subscriptionId}`, 'SubscriptionService', error);
      errors.push({ subscriptionId, error: error.message });
    }
  }
  
  return {
    updatedCount,
    failedCount: subscriptionIds.length - updatedCount,
    errors
  };
};

/**
 * Bulk apply discount to subscriptions
 * @param {Array} subscriptionIds - Array of subscription IDs
 * @param {Object} discountData - Discount data
 * @param {Object} options - Additional options
 * @returns {Object} - Result of the bulk operation
 */
const bulkApplyDiscount = async (subscriptionIds, discountData, options = {}) => {
  const { actorId, actorRole } = options;
  const { discountPercentage, discountAmount, reason } = discountData;
  
  logInfo(`Bulk applying discount to ${subscriptionIds.length} subscriptions`, 'SubscriptionService');
  
  const errors = [];
  let updatedCount = 0;
  
  for (const subscriptionId of subscriptionIds) {
    try {
      const subscription = await Subscription.findOne({ subscriptionId });
      
      if (!subscription) {
        errors.push({ subscriptionId, error: 'Subscription not found' });
        continue;
      }
      
      // Apply discount
      subscription.pricing = subscription.pricing || {};
      
      if (discountPercentage) {
        subscription.pricing.discountPercentage = discountPercentage;
      }
      
      if (discountAmount) {
        subscription.pricing.discountAmount = discountAmount;
      }
      
      // Calculate final price if applicable
      if (subscription.pricing.basePrice) {
        let finalPrice = subscription.pricing.basePrice;
        
        if (discountAmount) {
          finalPrice -= discountAmount;
        } else if (discountPercentage) {
          finalPrice = finalPrice * (1 - (discountPercentage / 100));
        }
        
        subscription.pricing.finalPrice = Math.max(0, finalPrice);
      }
      
      // Add discount history
      subscription.discountHistory = subscription.discountHistory || [];
      subscription.discountHistory.push({
        discountPercentage,
        discountAmount,
        appliedAt: new Date(),
        appliedBy: actorId,
        appliedByRole: actorRole,
        reason: reason || `Bulk discount by ${actorRole}`
      });
      
      subscription.updatedBy = actorId;
      await subscription.save();
      updatedCount++;
      
    } catch (error) {
      logError(`Failed to apply discount for subscription ${subscriptionId}`, 'SubscriptionService', error);
      errors.push({ subscriptionId, error: error.message });
    }
  }
  
  return {
    updatedCount,
    failedCount: subscriptionIds.length - updatedCount,
    errors
  };
};

/**
 * Bulk extend subscription expiration dates
 * @param {Array} subscriptionIds - Array of subscription IDs
 * @param {Number} days - Number of days to extend
 * @param {Object} options - Additional options
 * @returns {Object} - Result of the bulk operation
 */
const bulkExtendExpiration = async (subscriptionIds, days, options = {}) => {
  const { actorId, actorRole } = options;
  
  if (!days || days <= 0) {
    throw new Error('Days to extend must be a positive number');
  }
  
  logInfo(`Bulk extending ${subscriptionIds.length} subscriptions by ${days} days`, 'SubscriptionService');
  
  const errors = [];
  let updatedCount = 0;
  
  for (const subscriptionId of subscriptionIds) {
    try {
      const subscription = await Subscription.findOne({ subscriptionId });
      
      if (!subscription) {
        errors.push({ subscriptionId, error: 'Subscription not found' });
        continue;
      }
      
      // Calculate new expiration date
      const currentExpiresAt = subscription.expiresAt || new Date();
      const newExpiresAt = new Date(currentExpiresAt);
      newExpiresAt.setDate(newExpiresAt.getDate() + days);
      
      // Store previous expiration
      const previousExpiresAt = subscription.expiresAt;
      
      // Update expiration
      subscription.expiresAt = newExpiresAt;
      subscription.updatedBy = actorId;
      
      // Add extension history
      subscription.extensionHistory = subscription.extensionHistory || [];
      subscription.extensionHistory.push({
        previousExpiresAt,
        newExpiresAt,
        daysAdded: days,
        extendedAt: new Date(),
        extendedBy: actorId,
        extendedByRole: actorRole,
        reason: `Bulk extension by ${actorRole}`
      });
      
      // If subscription was expired, activate it
      if (subscription.status === 'expired') {
        subscription.status = 'active';
        
        // Add status change history
        subscription.statusHistory = subscription.statusHistory || [];
        subscription.statusHistory.push({
          from: 'expired',
          to: 'active',
          changedAt: new Date(),
          changedBy: actorId,
          changedByRole: actorRole,
          reason: 'Reactivated due to extension'
        });
      }
      
      await subscription.save();
      updatedCount++;
      
    } catch (error) {
      logError(`Failed to extend subscription ${subscriptionId}`, 'SubscriptionService', error);
      errors.push({ subscriptionId, error: error.message });
    }
  }
  
  return {
    updatedCount,
    failedCount: subscriptionIds.length - updatedCount,
    errors
  };
};

/**
 * Count subscriptions by plan ID with additional filters
 * @param {String} planId - Plan ID to count subscriptions for
 * @param {Object} filters - Additional filters
 * @returns {Number} - Count of subscriptions
 */
const countSubscriptionsByPlanId = async (planId, filters = {}) => {
  const query = { planId };
  
  if (filters.status) {
    if (Array.isArray(filters.status)) {
      query.status = { $in: filters.status };
    } else {
      query.status = filters.status;
    }
  }
  
  if (filters.expiresWithin) {
    const futureDate = new Date();
    futureDate.setDate(futureDate.getDate() + filters.expiresWithin);
    
    query.expiresAt = { 
      $lte: futureDate,
      $gte: new Date()
    };
  }
  
  return Subscription.countDocuments(query);
};

/**
 * Get revenue data for a specific plan
 * @param {String} planId - Plan ID to get revenue for
 * @param {Number} days - Number of days to look back
 * @returns {Object} - Revenue data
 */
const getPlanRevenue = async (planId, days = 30) => {
  const startDate = new Date();
  startDate.setDate(startDate.getDate() - days);
  
  // Import Payment model
  const { Payment } = require('../../models');
  
  // Calculate revenue based on ALL active subscriptions (not just recent ones)
  const activeSubscriptions = await Subscription.find({ 
    planId,
    status: { $in: ['active', 'trial'] },
    isDeleted: { $ne: true }
  });
  
  if (activeSubscriptions.length === 0) {
    return {
      total: 0,
      count: 0,
      conversionRate: 0,
      averageLifetime: 0
    };
  }
  
  // Calculate total recurring revenue from all active subscriptions
  const recurringRevenue = activeSubscriptions.reduce((sum, sub) => {
    // Use totalPrice virtual or basePrice as fallback
    let price = 0;
    if (sub.totalPrice !== undefined && !isNaN(sub.totalPrice)) {
      price = sub.totalPrice;
    } else if (sub.pricing?.basePrice !== undefined && !isNaN(sub.pricing.basePrice)) {
      price = sub.pricing.basePrice;
    }
    return sum + price;
  }, 0);
  
  // Get subscription IDs for payment history
  const subscriptionIds = activeSubscriptions.map(sub => sub.subscriptionId);
  
  // Get actual payments in the last N days for reference
  const paymentData = await Payment.aggregate([
    {
      $match: {
        paymentContext: 'subscription',
        subscriptionId: { $in: subscriptionIds },
        paymentDate: { $gte: startDate },
        isDeleted: false
      }
    },
    {
      $group: {
        _id: null,
        total: { $sum: '$amount' },
        count: { $sum: 1 }
      }
    }
  ]);
  
  // Get conversion rates
  const conversions = await Subscription.aggregate([
    {
      $match: {
        planId,
        'metadata.previousPlans.0': { $exists: true }
      }
    },
    {
      $project: {
        initialPlan: { $arrayElemAt: ['$metadata.previousPlans.type', 0] },
        currentPlan: '$plan.type'
      }
    },
    {
      $group: {
        _id: '$initialPlan',
        count: { $sum: 1 }
      }
    }
  ]);
  
  // Calculate average lifetime for this plan
  const lifetimeData = await Subscription.aggregate([
    {
      $match: {
        planId,
        status: { $in: ['active', 'expired', 'canceled'] },
        createdAt: { $exists: true }
      }
    },
    {
      $project: {
        lifetimeDays: {
          $divide: [
            { $subtract: [{ $ifNull: ['$dates.endDate', new Date()] }, '$createdAt'] },
            1000 * 60 * 60 * 24
          ]
        }
      }
    },
    {
      $group: {
        _id: null,
        averageLifetime: { $avg: '$lifetimeDays' }
      }
    }
  ]);
  
  // Return recurring revenue (what the plan generates from active subscriptions)
  // This represents the monthly/yearly recurring revenue, not just payments made
  return {
    total: recurringRevenue,
    count: activeSubscriptions.length,
    actualPayments: paymentData.length > 0 ? paymentData[0].total : 0,
    paymentCount: paymentData.length > 0 ? paymentData[0].count : 0,
    conversionRate: conversions.length > 0 ? conversions[0].count : 0,
    averageLifetime: lifetimeData.length > 0 ? Math.round(lifetimeData[0].averageLifetime) : 0
  };
};

module.exports = {
  bulkUpdateStatus,
  bulkChangePlan,
  bulkApplyDiscount,
  bulkExtendExpiration,
  countSubscriptionsByPlanId,
  getPlanRevenue
};
