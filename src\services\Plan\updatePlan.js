/**
 * Update a plan
 * Allows SuperAdmin to dynamically control features
 */
const { logSuccess, logError } = require('../../utils');
const getPlanById = require('./getPlanById');

/**
 * Update a plan
 * Respects SuperAdmin's feature choices instead of forcing all features
 * @param {string} planId - ID of the plan to update
 * @param {Object} updateData - Data to update
 * @param {Object} options - Additional options
 * @returns {Promise<Object>} Updated plan
 */
const updatePlan = async (planId, updateData, options = {}) => {
  try {
    const plan = await getPlanById(planId);
    
    // Allow SuperAdmin to control features dynamically
    if (updateData.features) {
      logSuccess(`Updating features for plan ${planId}: ${Object.keys(updateData.features).filter(key => updateData.features[key]).join(', ')}`, 'PlanService');
    }
    
    // Update fields
    Object.keys(updateData).forEach(key => {
      if (key === 'pricing' || key === 'features' || key === 'limits' || key === 'metadata') {
        // For nested objects, update each property to maintain any unspecified ones
        Object.keys(updateData[key]).forEach(subKey => {
          plan[key][subKey] = updateData[key][subKey];
        });
      } else {
        plan[key] = updateData[key];
      }
    });
    
    const updatedPlan = await plan.save();
    logSuccess(`Updated plan: ${planId}`, 'PlanService');
    
    return updatedPlan;
  } catch (error) {
    logError(`Failed to update plan: ${planId}`, 'PlanService', error);
    throw error;
  }
};

module.exports = updatePlan;
