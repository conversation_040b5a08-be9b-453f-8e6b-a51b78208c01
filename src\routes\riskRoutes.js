const express = require('express');
const riskScoreController = require('../controllers/riskScoreController');
const { authenticate, authorize } = require('../middleware/authMiddleware');

const router = express.Router();

/**
 * Risk Score Routes
 * Base path: /api/risk
 */

/**
 * @route   GET /api/risk/shop
 * @desc    Get risk scores for shop (Admin only)
 * @access  Private (Admin)
 * @query   riskLevel, sortBy, sortOrder, page, limit
 */
router.get(
  '/shop',
  authenticate,
  authorize(['admin']),
  riskScoreController.getShopRiskScores
);

/**
 * @route   GET /api/risk/all
 * @desc    Get all risk scores across platform (SuperAdmin only)
 * @access  Private (SuperAdmin)
 * @query   shopId, riskLevel, sortBy, sortOrder, page, limit
 */
router.get(
  '/all',
  authenticate,
  authorize(['superAdmin']),
  riskScoreController.getAllRiskScores
);

/**
 * @route   PUT /api/risk/update
 * @desc    Update customer risk score (System/SuperAdmin)
 * @access  Private (SuperAdmin)
 * @body    customerId, riskScore, riskLevel, source
 */
router.put(
  '/update',
  authenticate,
  authorize(['superAdmin']),
  riskScoreController.updateRiskScore
);

module.exports = router; 