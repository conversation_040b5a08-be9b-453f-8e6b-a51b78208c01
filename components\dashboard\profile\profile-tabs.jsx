"use client"

import { User, <PERSON> } from 'lucide-react'
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Badge } from '@/components/ui/badge'
import { cn } from '@/lib/utils'

export function ProfileTabs({ activeTab, onTabChange }) {
  const tabs = [
    {
      id: 'personal',
      label: 'Basic Information',
      icon: User,
      description: 'View your account information'
    },
    {
      id: 'security',
      label: 'Security',
      icon: Shield,
      description: 'Change your password',
      badge: 'Important'
    }
  ]

  return (
    <div className="w-full">
      <Tabs value={activeTab} onValueChange={onTabChange} className="w-full">
        <TabsList className="grid w-full grid-cols-2 h-auto p-1 bg-muted/50">
          {tabs.map((tab) => {
            const Icon = tab.icon
            return (
              <TabsTrigger
                key={tab.id}
                value={tab.id}
                className={cn(
                  "flex flex-col items-center gap-2 p-4 h-auto data-[state=active]:bg-background data-[state=active]:shadow-sm",
                  "transition-all duration-200 hover:bg-background/50"
                )}
              >
                <div className="flex items-center gap-2">
                  <Icon className="h-4 w-4" />
                  <span className="font-medium">{tab.label}</span>
                  {tab.badge && (
                    <Badge variant="secondary" className="text-xs px-1.5 py-0.5">
                      {tab.badge}
                    </Badge>
                  )}
                </div>
                <p className="text-xs text-muted-foreground text-center hidden sm:block">
                  {tab.description}
                </p>
              </TabsTrigger>
            )
          })}
        </TabsList>
      </Tabs>
    </div>
  )
} 