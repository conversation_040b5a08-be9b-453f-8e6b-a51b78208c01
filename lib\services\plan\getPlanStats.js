/**
 * Get Plan Statistics Service
 * 
 * Retrieves comprehensive plan statistics for SuperAdmin dashboard
 * UPDATED: Now handles the complex backend response format exactly
 */

import apiBridge from '@/lib/api/bridge';
import { ENDPOINTS } from '@/lib/api/contract';
import { handleError, processApiResponse, logApiCall } from '../baseService';

/**
 * Get plan statistics (SuperAdmin only)
 * Backend provides comprehensive stats including:
 * - Number of active subscriptions per plan
 * - Revenue metrics (last 30 days)
 * - Usage trends
 * - Expiring subscriptions (next 7 days)
 * - Most used plan
 * @returns {Promise<Object>} API response with detailed plan statistics
 */
async function getPlanStats() {
  try {
    logApiCall('PlanService.getPlanStats', ENDPOINTS.PLANS.STATS);

    const response = await apiBridge.get(ENDPOINTS.PLANS.STATS, {
      cacheKey: 'plan-stats',
      cacheTTL: 300000 // 5 minutes cache
    });

    // Skip processApiResponse since it only returns response.data.data
    // We need the full response structure for plan stats
    const backendData = response.data;
    
    // Handle the complex backend response structure
    if (backendData && backendData.success) {
      
      const processedData = {
        success: true,
        data: {
          // Summary section
          summary: {
            totalPlans: backendData.summary?.totalPlans || 0,
            activePlans: backendData.summary?.activePlans || 0,
            pendingPlans: backendData.summary?.pendingPlans || 0,
            inactivePlans: backendData.summary?.pendingPlans || 0,
            mostUsedPlan: backendData.summary?.mostUsedPlan || null
          },
          
          // Detailed plan stats
          planStats: (backendData.data || []).map(planStat => ({
            planId: planStat.planId,
            name: planStat.name,
            displayName: planStat.displayName,
            type: planStat.type,
            isActive: planStat.isActive,
            
            // Statistics for this plan
            stats: {
              activeSubscriptions: planStat.stats?.activeSubscriptions || 0,
              revenue: planStat.stats?.revenue || 0,
              expiringSoon: planStat.stats?.expiringSoon || 0,
              conversionRate: planStat.stats?.conversionRate || 0,
              averageLifetime: planStat.stats?.averageLifetime || 0
            }
          })),
          
          // Use backend-provided totals directly
          totals: {
            totalSubscriptions: backendData.totals?.totalSubscriptions || 0,
            totalRevenue: backendData.totals?.totalRevenue || 0,
            totalExpiringSoon: backendData.totals?.totalExpiringSoon || 0
          }
        }
      };

      return processedData;
    }

    // Fallback structure if backend returns unexpected format
    return {
      success: true,
      data: {
        summary: {
          totalPlans: 0,
          activePlans: 0,
          pendingPlans: 0,
          inactivePlans: 0,
          mostUsedPlan: null
        },
        planStats: [],
        totals: {
          totalSubscriptions: 0,
          totalRevenue: 0,
          totalExpiringSoon: 0
        }
      }
    };
  } catch (error) {
    // Handle case where endpoint might not be fully implemented
    if (error.response?.status === 404) {
      console.warn('[PlanService.getPlanStats] Plan stats endpoint not fully implemented yet');
      return {
        success: true,
        data: {
          summary: {
            totalPlans: 0,
            activePlans: 0,
            pendingPlans: 0,
            inactivePlans: 0,
            mostUsedPlan: null
          },
          planStats: [],
          totals: {
            totalSubscriptions: 0,
            totalRevenue: 0,
            totalExpiringSoon: 0
          }
        }
      };
    }
    
    handleError(error, 'PlanService.getPlanStats', true);
    throw error;
  }
}

export default getPlanStats;