"use client";

import { 
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>Title,
  <PERSON>alogFooter
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { 
  X, 
  Pencil,
  Phone,
  Mail,
  MapPin,
  Building2,
  Calendar
} from "lucide-react";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { getInitials, getBadgeVariant, formatDate } from "./utils/formatters";

export function ShopDetailsDialog({ open, onOpenChange, shop, onEdit }) {
  if (!shop) return null;
  
  // Debug: Log shop data to see what we're working with
  console.log('🏪 Shop data:', shop);
  console.log('🖼️ Shop logoUrl:', shop.logoUrl);
  
  // Generate logo URL with improved logic
  const getLogoUrl = (logoUrl) => {
    console.log('📥 Input logoUrl:', logoUrl);

    if (!logoUrl) {
      console.log('❌ No logoUrl provided');
      return null;
    }

    // If it's already a full URL, use it as-is
    if (logoUrl.startsWith('http')) {
      console.log('✅ Using full URL as-is');
      return logoUrl;
    }

    // If it starts with /api/files/, prepend the base URL
    if (logoUrl.startsWith('/api/files/')) {
      const fullUrl = `${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000'}${logoUrl}`;
      console.log('✅ Generated full URL from /api/files/:', fullUrl);
      return fullUrl;
    }

    // For fileId-based URLs (like FILE123), construct the proper API endpoint
    const baseUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000';
    const finalUrl = `${baseUrl}/api/files/${logoUrl}`;
    console.log('✅ Generated fileId-based URL:', finalUrl);
    return finalUrl;
  };

  const logoUrl = getLogoUrl(shop.logoUrl);
  console.log('🎯 Final logoUrl for display:', logoUrl);
  
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="w-[95vw] max-w-4xl max-h-[90vh] p-0 overflow-hidden">
        {/* Fixed Header */}
        <DialogHeader className="sticky top-0 z-10 bg-background border-b p-6 pb-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="relative">
                <Avatar className="h-20 w-20 border-4 border-white shadow-lg ring-2 ring-gray-100">
                  {logoUrl ? (
                    <AvatarImage
                      src={logoUrl}
                      alt={`${shop.shopName} logo`}
                      className="object-cover"
                      onError={(e) => {
                        console.error('❌ Logo failed to load:', logoUrl);
                        e.target.style.display = 'none';
                      }}
                      onLoad={() => {
                        console.log('✅ Logo loaded successfully:', logoUrl);
                      }}
                    />
                  ) : null}
                  <AvatarFallback className="text-xl font-bold bg-gradient-to-br from-blue-500 to-purple-600 text-white">
                    {getInitials(shop.shopName)}
                  </AvatarFallback>
                </Avatar>
                {/* Logo indicator */}
                {logoUrl && (
                  <div className="absolute -bottom-1 -right-1 w-6 h-6 bg-green-500 rounded-full flex items-center justify-center shadow-md">
                    <span className="text-white text-xs font-bold">✓</span>
                  </div>
                )}
              </div>
              <div>
                <DialogTitle className="text-xl font-bold flex items-center gap-3">
                  {shop.shopName}
                  <Badge variant={getBadgeVariant(shop.status)} className="text-xs">
                    {typeof shop.status === 'string' 
                      ? shop.status.charAt(0).toUpperCase() + shop.status.slice(1) 
                      : 'Unknown'}
                  </Badge>
                </DialogTitle>
                <p className="text-sm text-muted-foreground mt-1 mb-2">
                  {shop.shopAddress || shop.address || 'No address provided'}
                </p>
                <div className="flex items-center gap-2 flex-wrap">
                  {shop.access?.isPaid && (
                    <Badge variant="outline" className="text-xs bg-green-50 text-green-700 border-green-200">
                      💰 PAID
                    </Badge>
                  )}
                  {shop.access?.isActivated && (
                    <Badge variant="outline" className="text-xs bg-blue-50 text-blue-700 border-blue-200">
                      ⚡ ACTIVATED
                    </Badge>
                  )}
                  {logoUrl && (
                    <Badge variant="outline" className="text-xs bg-purple-50 text-purple-700 border-purple-200">
                      🖼️ HAS LOGO
                    </Badge>
                  )}
                  <Badge variant="outline" className="text-xs bg-gray-50 text-gray-700 border-gray-200">
                    ID: {shop.shopId}
                  </Badge>
                </div>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={onEdit}
                className="h-9"
              >
                <Pencil className="h-4 w-4 mr-2" />
                Edit Shop
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => onOpenChange(false)}
                className="h-9"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </DialogHeader>

        {/* Scrollable Content */}
        <div className="flex-1 min-h-0">
          <ScrollArea className="h-full max-h-[calc(90vh-200px)]">
            <div className="p-6 pt-2 space-y-6">
              {/* Contact Information */}
              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-lg flex items-center gap-2">
                    <Phone className="h-5 w-5 text-blue-600" />
                    Contact Information
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
                      <Phone className="h-4 w-4 text-muted-foreground" />
                      <div>
                        <p className="text-xs text-muted-foreground uppercase tracking-wide">Phone</p>
                        <p className="font-medium">{shop.phone || 'Not specified'}</p>
                      </div>
                    </div>
                    <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
                      <Mail className="h-4 w-4 text-muted-foreground" />
                      <div>
                        <p className="text-xs text-muted-foreground uppercase tracking-wide">Email</p>
                        <p className="font-medium text-sm">{shop.email || 'Not specified'}</p>
                      </div>
                    </div>
                  </div>
                  <div className="p-3 bg-gray-50 rounded-lg">
                    <p className="text-xs text-muted-foreground uppercase tracking-wide mb-1">Full Address</p>
                    <p className="font-medium">{shop.shopAddress || 'Not specified'}</p>
                  </div>
                </CardContent>
              </Card>
              
              {/* Shop Details */}
              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-lg flex items-center gap-2">
                    <Building2 className="h-5 w-5 text-green-600" />
                    Shop Details
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="p-3 bg-gray-50 rounded-lg">
                      <p className="text-xs text-muted-foreground uppercase tracking-wide">Owner</p>
                      <p className="font-medium">{shop.ownerName || shop.fullName || 'Not specified'}</p>
                    </div>
                    <div className="p-3 bg-gray-50 rounded-lg">
                      <p className="text-xs text-muted-foreground uppercase tracking-wide">Status</p>
                      <Badge variant={getBadgeVariant(shop.status)} className="mt-1">
                        {shop.status?.charAt(0).toUpperCase() + shop.status?.slice(1) || 'Unknown'}
                      </Badge>
                    </div>
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="p-3 bg-gray-50 rounded-lg">
                      <p className="text-xs text-muted-foreground uppercase tracking-wide">Created</p>
                      <p className="font-medium">{formatDate(shop.createdAt)}</p>
                    </div>
                    <div className="p-3 bg-gray-50 rounded-lg">
                      <p className="text-xs text-muted-foreground uppercase tracking-wide">Last Updated</p>
                      <p className="font-medium">{formatDate(shop.updatedAt)}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
              
              {/* Subscription Info */}
              {shop.subscription && (
                <Card>
                  <CardHeader className="pb-3">
                    <CardTitle className="text-lg flex items-center gap-2">
                      <Calendar className="h-5 w-5 text-purple-600" />
                      Subscription Details
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="p-3 bg-gray-50 rounded-lg">
                        <p className="text-xs text-muted-foreground uppercase tracking-wide">Plan</p>
                        <p className="font-medium">{shop.subscription.plan || shop.subscription.planType || 'Not specified'}</p>
                      </div>
                      <div className="p-3 bg-gray-50 rounded-lg">
                        <p className="text-xs text-muted-foreground uppercase tracking-wide">Status</p>
                        <p className="font-medium">{shop.subscription.status || 'Not specified'}</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}
            </div>
          </ScrollArea>
        </div>

        {/* Fixed Footer */}
        <DialogFooter className="p-6 pt-4 border-t bg-background">
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Close
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
