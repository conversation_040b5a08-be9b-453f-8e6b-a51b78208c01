"use client";

import { createContext, useState, useContext, useEffect, useCallback } from "react";
import UserService from "@/lib/services/user";
import { toast } from "sonner";
import { useAuth } from "@/contexts/auth-context";
import { errorHandlers } from "@/lib/api/contract";

// Create user context
const UserContext = createContext({});

/**
 * Provider component for user management
 */
export function UserProvider({ children }) {
  const [users, setUsers] = useState([]);
  const [currentUser, setCurrentUser] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [pagination, setPagination] = useState({
    currentPage: 1,
    pageSize: 10,
    totalItems: 0,
    totalPages: 0
  });
  const [filters, setFilters] = useState({});
  
  // Get auth context for permissions
  const { user: authUser } = useAuth();
  
  // Load users
  const loadUsers = useCallback(async (newFilters = {}, page = 1, limit = 10) => {
    setLoading(true);
    setError(null);
    
    try {
      // Update state immediately
      setFilters(newFilters);
      setPagination(prev => ({ ...prev, currentPage: page, pageSize: limit }));
      
      // Fetch users
      const usersData = await UserService.getUsers(newFilters, page, limit);
      
      // Update state
      setUsers(usersData);
      setPagination(prev => ({ 
        ...prev, 
        totalItems: usersData.length, // This will be improved when backend provides proper pagination
        totalPages: Math.ceil(usersData.length / limit)
      }));
      
      return usersData;
    } catch (err) {
      setError(err.message || 'Failed to load users');
      console.error('Failed to load users', err);
      return [];
    } finally {
      setLoading(false);
    }
  }, []);
  
  // Get user by ID
  const getUserById = useCallback(async (userId) => {
    setLoading(true);
    setError(null);
    
    try {
      const user = await UserService.getUserById(userId);
      return user;
    } catch (err) {
      setError(err.message || 'Failed to get user');
      console.error(`Failed to get user ${userId}`, err);
      return null;
    } finally {
      setLoading(false);
    }
  }, []);
  
  // Create user
  const createUser = useCallback(async (userData) => {
    setLoading(true);
    setError(null);
    
    try {
      // Validate superAdmin role requires a shop association
      if (userData.role === 'superAdmin' && (!userData.shopId || userData.shopId === 'no-shop')) {
        toast.error('SuperAdmin users must be associated with a shop');
        throw new Error('SuperAdmin users must be associated with a shop');
      }
      
      // Validate admin can only create employees for their own shop
      if (authUser && authUser.role === 'admin' && userData.role === 'employee' && authUser.shopId !== userData.shopId) {
        toast.error('Admin users can only create employees for their own shop');
        throw new Error('Admin users can only create employees for their own shop');
      }
      
      const newUser = await UserService.createUser(userData);
      
      // Update users list if new user was created
      if (newUser) {
        // IMPROVED: Instead of just adding to local state, we now properly resync with server
        // This ensures the table reflects the actual server state with proper pagination/filtering
        await loadUsers(filters, pagination.currentPage, pagination.pageSize);
      }
      
      return newUser;
    } catch (err) {
      setError(err.message || 'Failed to create user');
      console.error('Failed to create user', err);
      throw err;
    } finally {
      setLoading(false);
    }
  }, [authUser, loadUsers, filters, pagination.currentPage, pagination.pageSize]);
  
  // Update user
  const updateUser = useCallback(async (userId, userData) => {
    setLoading(true);
    setError(null);
    
    try {
      const updatedUser = await UserService.updateUser(userId, userData);
      
      // Update users list if user was updated
      if (updatedUser) {
        // IMPROVED: Instead of just updating local state, we now properly resync with server
        // This ensures the table reflects the actual server state with proper pagination/filtering
        await loadUsers(filters, pagination.currentPage, pagination.pageSize);
        
        // Update current user if it's the one being updated
        if (currentUser && currentUser.userId === userId) {
          setCurrentUser(updatedUser);
        }
      }
      
      return updatedUser;
    } catch (err) {
      setError(err.message || 'Failed to update user');
      console.error(`Failed to update user ${userId}`, err);
      throw err;
    } finally {
      setLoading(false);
    }
  }, [currentUser, loadUsers, filters, pagination.currentPage, pagination.pageSize]);
  
  // Delete user
  const deleteUser = useCallback(async (userId, reason) => {
    setLoading(true);
    setError(null);
    
    try {
      const success = await UserService.deleteUser(userId, reason);
      
      // Update users list if user was deleted
      if (success) {
        // IMPROVED: Instead of just filtering local state, we now properly resync with server
        // This ensures the table reflects the actual server state with proper pagination/filtering
        await loadUsers(filters, pagination.currentPage, pagination.pageSize);
        
        // Clear current user if it's the one being deleted
        if (currentUser && currentUser.userId === userId) {
          setCurrentUser(null);
        }
      }
      
      return success;
    } catch (err) {
      setError(err.message || 'Failed to delete user');
      console.error(`Failed to delete user ${userId}`, err);
      throw err;
    } finally {
      setLoading(false);
    }
  }, [currentUser, loadUsers, filters, pagination.currentPage, pagination.pageSize]);
  
  // Change user status
  const changeUserStatus = useCallback(async (userId, status, reason, sendEmail = true) => {
    try {
      console.log(`[UserContext] Attempting to change user ${userId} status to ${status}`);
      
      // First check if we're still authenticated before making the request
      const token = localStorage.getItem('accessToken');
      if (!token) {
        console.warn('[UserContext] No access token found, redirecting to login');
        toast.error('Your session has expired. Please login again.');
        if (typeof window !== 'undefined' && window.routerNavigate) {
          window.routerNavigate('/login');
        }
        return null;
      }
      
      // Proceed with status change - include sendEmail parameter to trigger notifications
      const updatedUser = await UserService.changeUserStatus(userId, status, reason, sendEmail);
      
      if (updatedUser) {
        console.log(`[UserContext] Successfully changed user ${userId} status to ${status}`);
        
        // IMPROVED: Instead of just updating local state, we now properly resync with server
        // This ensures the table reflects the actual server state with proper pagination/filtering
        await loadUsers(filters, pagination.currentPage, pagination.pageSize);
        
        // Success message based on status
        if (status === 'active') {
          toast.success('User has been reactivated successfully');
        } else if (status === 'suspended') {
          toast.success('User has been suspended successfully');
        } else {
          toast.success(`User status changed to ${status} successfully`);
        }
        
        return updatedUser;
      }
      return null;
    } catch (error) {
      console.error(`Failed to change status for user ${userId}`, error);
      
      // Handle authentication errors specifically
      if (error.response?.status === 401) {
        console.warn('[UserContext] Authentication error (401) during status change');
        toast.error('Your session has expired. Please login again.');
        
        // Clear tokens and redirect to login
        localStorage.removeItem('accessToken');
        localStorage.removeItem('refreshToken');
        
        if (typeof window !== 'undefined' && window.routerNavigate) {
          window.routerNavigate('/login');
        }
        return null;
      }
      
      // Use the centralized error handler for other errors
      const errorMessage = errorHandlers.getErrorMessage(error);
      toast.error(`Failed to change user status: ${errorMessage}`);
      return null;
    }
  }, [loadUsers, filters, pagination]);
  
  // Set current user
  const selectUser = useCallback((user) => {
    setCurrentUser(user);
  }, []);
  
  // Context value
  const value = {
    users,
    currentUser,
    loading,
    error,
    pagination,
    filters,
    loadUsers,
    getUserById,
    createUser,
    updateUser,
    deleteUser,
    changeUserStatus,
    selectUser,
    setFilters,
    setPagination
  };
  
  return (
    <UserContext.Provider value={value}>
      {children}
    </UserContext.Provider>
  );
}

/**
 * Hook for using user context
 */
export function useUserContext() {
  const context = useContext(UserContext);
  
  if (!context) {
    throw new Error('useUserContext must be used within a UserProvider');
  }
  
  return context;
}
