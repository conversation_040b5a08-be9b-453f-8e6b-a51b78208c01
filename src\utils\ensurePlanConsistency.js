/**
 * Plan Model Consistency Checker (Optimized)
 * 
 * This utility ensures that Plan model data is consistent
 * and subscriptions have proper planId references.
 */
const { logInfo, logWarning, logSuccess } = require('./logger');

// Lazy load models to prevent circular dependencies
const getPlanModel = () => require('../models/Plan.model');
const getSubscriptionModel = () => require('../models/subscription.model');

/**
 * Checks if default plans exist and creates them if not (optimized)
 * @returns {Promise<Array>} Array of plans
 */
async function ensureDefaultPlansOptimized() {
  try {
    const Plan = getPlanModel();
    
    // Use a single aggregation query to check and get count efficiently
    const [planStats] = await Plan.aggregate([
      {
        $group: {
          _id: null,
          count: { $sum: 1 },
          plans: { $push: '$$ROOT' }
        }
      }
    ]);
    
    if (!planStats || planStats.count === 0) {
      logInfo('No plans found. Creating default plans...', 'PlanConsistency');
      await Plan.createDefaultPlans();
      logSuccess('Default plans created successfully', 'PlanConsistency');
      
      // Return newly created plans
      return Plan.find({}).lean();
    }
    
    return planStats.plans || [];
  } catch (error) {
    logWarning(`Failed to check/create default plans: ${error.message}`, 'PlanConsistency');
    // Fall back to traditional method
    const Plan = getPlanModel();
    const plansCount = await Plan.countDocuments({});
    
    if (plansCount === 0) {
      logInfo('No plans found. Creating default plans...', 'PlanConsistency');
      await Plan.createDefaultPlans();
      logSuccess('Default plans created successfully', 'PlanConsistency');
    }
    
    return Plan.find({}).lean();
  }
}

/**
 * Checks if subscriptions have planId references (optimized)
 * @returns {Promise<Object>} Statistics about subscription references
 */
async function checkSubscriptionReferencesOptimized() {
  try {
    const Subscription = getSubscriptionModel();
    
    // Use a single aggregation query to get all stats efficiently
    const [stats] = await Subscription.aggregate([
      {
        $group: {
          _id: null,
          total: { $sum: 1 },
          withPlanId: {
            $sum: {
              $cond: [
                { $ifNull: ['$planId', false] },
                1,
                0
              ]
            }
          }
        }
      }
    ]);
    
    if (!stats) {
      logInfo('No subscriptions found in database', 'PlanConsistency');
      return {
        total: 0,
        withPlanId: 0,
        withoutPlanId: 0,
        percentComplete: 100
      };
    }
    
    const result = {
      total: stats.total,
      withPlanId: stats.withPlanId,
      withoutPlanId: stats.total - stats.withPlanId,
      percentComplete: stats.total > 0 ? Math.round(stats.withPlanId / stats.total * 100) : 100
    };
    
    if (result.withoutPlanId > 0) {
      logWarning(`${result.withoutPlanId} of ${result.total} subscriptions (${100 - result.percentComplete}%) need planId migration`, 'PlanConsistency');
    } else if (result.total > 0) {
      logSuccess(`All ${result.total} subscriptions have planId references`, 'PlanConsistency');
    }
    
    return result;
  } catch (error) {
    logWarning(`Failed to check subscription references: ${error.message}`, 'PlanConsistency');
    // Fall back to traditional method
    const Subscription = getSubscriptionModel();
    const totalCount = await Subscription.countDocuments({});
    const missingPlanIdCount = await Subscription.countDocuments({ planId: { $exists: false } });
    
    return {
      total: totalCount,
      withPlanId: totalCount - missingPlanIdCount,
      withoutPlanId: missingPlanIdCount,
      percentComplete: totalCount > 0 ? Math.round((totalCount - missingPlanIdCount) / totalCount * 100) : 100
    };
  }
}

/**
 * Ensures Plan model consistency on application startup (optimized)
 * @param {Object} options Configuration options
 * @param {boolean} options.autoMigrate Whether to automatically run migration if needed
 * @returns {Promise<Object>} Status of consistency check
 */
async function ensurePlanConsistency(options = {}) {
  try {
    // Run both checks in parallel for better performance
    const [plans, stats] = await Promise.all([
      ensureDefaultPlansOptimized(),
      checkSubscriptionReferencesOptimized()
    ]);
    
    // Auto-migrate if configured and needed
    if (options.autoMigrate && stats.withoutPlanId > 0) {
      logInfo('Auto-migration enabled. Initiating plan migration...', 'PlanConsistency');
      try {
        const { runMigration } = require('../scripts/migratePlanModel');
        await runMigration();
        
        // Re-check after migration
        const postMigrationStats = await checkSubscriptionReferencesOptimized();
        return { 
          plans: plans.length, 
          subscriptions: postMigrationStats,
          migrationRun: true 
        };
      } catch (migrationError) {
        logWarning(`Migration failed: ${migrationError.message}`, 'PlanConsistency');
        return { 
          plans: plans.length, 
          subscriptions: stats,
          migrationRun: false,
          migrationError: migrationError.message
        };
      }
    }
    
    return { 
      plans: plans.length, 
      subscriptions: stats,
      migrationRun: false
    };
  } catch (error) {
    logWarning(`Plan consistency check failed: ${error.message}`, 'PlanConsistency', error);
    return { error: error.message };
  }
}

module.exports = ensurePlanConsistency;
