/**
 * Get Shop Statistics Service
 * Provides shop stats data for SuperAdmin dashboard
 */
const { Shop, Subscription, Payment } = require('../../models');
const { logInfo, logError, AppError } = require('../../utils');

/**
 * Get shop statistics data
 * @param {Object} options - Stats options
 * @param {Date} options.startDate - Start date for stats (optional)
 * @param {Date} options.endDate - End date for stats (optional)
 * @param {String} options.groupBy - Group by (day, week, month) (optional)
 * @returns {Object} - Shop stats data
 */
const getShopStats = async (options = {}) => {
  try {
    const { startDate, endDate, groupBy = 'day' } = options;
    
    // Base query conditions
    const baseQuery = { isDeleted: { $ne: true } };
    
    // Add date filter if provided
    if (startDate && endDate) {
      baseQuery.createdAt = { $gte: startDate, $lte: endDate };
    }
    
    // Get total shops count (excluding deleted)
    const totalShops = await Shop.countDocuments(baseQuery);
    
    // Get active shops count
    const activeShops = await Shop.countDocuments({
      ...baseQuery,
      status: 'active',
      'access.isActivated': true
    });
    
    // Get pending verification shops count
    const pendingVerificationShops = await Shop.countDocuments({
      ...baseQuery,
      $or: [
        { status: 'pending' },
        { verified: false }
      ]
    });
    
    // Get revenue-generating shops (shops with paid subscriptions)
    const revenueGeneratingShops = await Shop.countDocuments({
      ...baseQuery,
      'access.isPaid': true,
      status: 'active'
    });
    
    // Get shops by status distribution
    const statusDistribution = await Shop.aggregate([
      { $match: baseQuery },
      {
        $group: {
          _id: '$status',
          count: { $sum: 1 }
        }
      },
      {
        $project: {
          status: '$_id',
          count: 1,
          percentage: {
            $multiply: [
              { $divide: ['$count', totalShops] },
              100
            ]
          }
        }
      }
    ]);
    
    // Get shops by business type distribution
    const businessTypeDistribution = await Shop.aggregate([
      { $match: baseQuery },
      {
        $group: {
          _id: '$businessDetails.type',
          count: { $sum: 1 }
        }
      },
      {
        $project: {
          businessType: '$_id',
          count: 1,
          percentage: {
            $multiply: [
              { $divide: ['$count', totalShops] },
              100
            ]
          }
        }
      }
    ]);
    
    // Get shops by location (city) distribution
    const locationDistribution = await Shop.aggregate([
      { $match: baseQuery },
      {
        $group: {
          _id: '$location.city',
          count: { $sum: 1 }
        }
      },
      {
        $project: {
          city: '$_id',
          count: 1,
          percentage: {
            $multiply: [
              { $divide: ['$count', totalShops] },
              100
            ]
          }
        }
      },
      { $sort: { count: -1 } },
      { $limit: 10 } // Top 10 cities
    ]);
    
    // Get recent shop registrations (last 30 days)
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
    
    const recentRegistrations = await Shop.countDocuments({
      ...baseQuery,
      createdAt: { $gte: thirtyDaysAgo }
    });
    
    // Get shops with high activity (based on statistics)
    const highActivityShops = await Shop.countDocuments({
      ...baseQuery,
      status: 'active',
      $or: [

        { 'statistics.totalRevenue': { $gte: 10000 } },
        { 'statistics.totalCustomers': { $gte: 50 } }
      ]
    });
    
    // Calculate total revenue across all shops
    const totalRevenueData = await Shop.aggregate([
      { $match: { ...baseQuery, status: 'active' } },
      {
        $group:         {
          _id: null,
          totalRevenue: { $sum: '$statistics.totalRevenue' },
          totalCustomers: { $sum: '$statistics.totalCustomers' }
        }
      }
    ]);
    
    const aggregatedStats = totalRevenueData.length > 0 ? totalRevenueData[0] : {
      totalRevenue: 0,
      totalCustomers: 0
    };
    
    // Get shops with active subscriptions
    const shopsWithSubscriptions = await Shop.countDocuments({
      ...baseQuery,
      currentSubscriptionId: { $ne: null },
      'access.isPaid': true
    });
    
    // Get trend data if date range is provided
    let trends = [];
    if (startDate && endDate) {
      let dateFormat;
      let groupField;
      
      switch(groupBy) {
        case 'week':
          dateFormat = '%Y-%U'; // Year-Week
          groupField = { year: '$year', week: '$week' };
          break;
        case 'month':
          dateFormat = '%Y-%m'; // Year-Month
          groupField = { year: '$year', month: '$month' };
          break;
        default: // day
          dateFormat = '%Y-%m-%d'; // Year-Month-Day
          groupField = { year: '$year', month: '$month', day: '$day' };
      }
      
      trends = await Shop.aggregate([
        {
          $match: {
            ...baseQuery,
            createdAt: { $gte: startDate, $lte: endDate }
          }
        },
        {
          $project: {
            date: { $dateToString: { format: dateFormat, date: '$createdAt' } },
            status: '$status',
            businessType: '$businessDetails.type',
            year: { $year: '$createdAt' },
            month: { $month: '$createdAt' },
            week: { $week: '$createdAt' },
            day: { $dayOfMonth: '$createdAt' }
          }
        },
        {
          $group: {
            _id: {
              date: '$date',
              status: '$status',
              ...groupField
            },
            count: { $sum: 1 }
          }
        },
        {
          $sort: { '_id.year': 1, '_id.month': 1, '_id.week': 1, '_id.day': 1 }
        }
      ]);
    }
    
    // Format trend data
    const trendsByDate = {};
    trends.forEach(item => {
      if (!trendsByDate[item._id.date]) {
        trendsByDate[item._id.date] = {
          date: item._id.date,
          total: 0,
          byStatus: {}
        };
      }
      
      trendsByDate[item._id.date].byStatus[item._id.status] = item.count;
      trendsByDate[item._id.date].total += item.count;
    });
    
    // Calculate average revenue per shop
    const averageRevenuePerShop = revenueGeneratingShops > 0 
      ? Math.round(aggregatedStats.totalRevenue / revenueGeneratingShops) 
      : 0;
    
    const result = {
      summary: {
        totalShops,
        activeShops,
        pendingVerificationShops,
        revenueGeneratingShops
      },
      activity: {
        recentRegistrations,
        highActivityShops,
        shopsWithSubscriptions,
        averageRevenuePerShop
      },
      distribution: {
        byStatus: statusDistribution,
        byBusinessType: businessTypeDistribution,
        byLocation: locationDistribution
      },
      aggregatedBusinessMetrics: {
        totalRevenue: aggregatedStats.totalRevenue,
        totalCustomers: aggregatedStats.totalCustomers
      },
      trends: Object.values(trendsByDate),
      metadata: {
        calculatedAt: new Date(),
        dateRange: startDate && endDate ? { startDate, endDate } : null,
        groupBy
      }
    };
    
    logInfo(`Shop statistics calculated successfully. Total shops: ${totalShops}`, 'getShopStats');
    
    return result;
    
  } catch (error) {
    logError('Error calculating shop statistics', 'getShopStats', error);
    throw new AppError('Failed to calculate shop statistics', 500, 'shop_stats_error');
  }
};

module.exports = getShopStats; 