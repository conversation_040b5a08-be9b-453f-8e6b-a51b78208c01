/**
 * Settings Context - System Logs Actions
 * Contains actions for managing system logs
 */

import SettingsService from '@/lib/services/settings';
import { ACTION_TYPES } from '../types';

/**
 * Fetch system logs
 * @param {Function} dispatch - Reducer dispatch function
 * @param {Function} throttleRequest - Request throttling function
 * @param {Object} filters - Log filters (type, level, search, dates, pagination)
 * @returns {Promise} Result of the operation
 */
export const fetchSystemLogs = (dispatch, throttleRequest, filters = {}) => {
  return throttleRequest('fetchSystemLogs', async () => {
    try {
      dispatch({ type: ACTION_TYPES.SET_LOADING, payload: true });
      const result = await SettingsService.fetchSystemLogs(filters);
      dispatch({ 
        type: ACTION_TYPES.SET_SYSTEM_LOGS, 
        payload: {
          logs: result.logs || [],
          totalCount: result.totalCount || 0
        }
      });
      dispatch({ type: ACTION_TYPES.SET_ERROR, payload: null }); // Clear any previous errors
      return { success: true, data: result };
    } catch (error) {
      console.error('System logs fetch error:', error);
      const errorMessage = error.response?.status === 500 
        ? 'Server error loading system logs. Please try again later.'
        : error.message || 'Failed to load system logs';
      
      dispatch({ type: ACTION_TYPES.SET_ERROR, payload: errorMessage });
      return { success: false, error: errorMessage };
    } finally {
      dispatch({ type: ACTION_TYPES.SET_LOADING, payload: false });
    }
  });
};

/**
 * Export system logs
 * @param {Function} dispatch - Reducer dispatch function
 * @param {Object} filters - Log filters including export format
 * @returns {Promise} Result of the operation
 */
export const exportSystemLogs = (dispatch, filters = {}) => {
  try {
    dispatch({ type: ACTION_TYPES.SET_LOADING, payload: true });
    const result = SettingsService.exportSystemLogs(filters);
    dispatch({ type: ACTION_TYPES.SET_LOADING, payload: false });
    return Promise.resolve({ success: true, data: result });
  } catch (error) {
    dispatch({ type: ACTION_TYPES.SET_ERROR, payload: error.message });
    return Promise.resolve({ success: false, error: error.message });
  }
};
