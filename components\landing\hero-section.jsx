"use client";

import Image from "next/image";
import Link from "next/link";
import { ArrowRight, Shield, Users, TrendingUp, Smartphone, UserPlus, CreditCard } from "lucide-react";
import { Button } from "../ui/button";
import { Badge } from "../ui/badge";

export default function HeroSection() {
  return (
    <section className="relative bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900 pt-20 pb-32 overflow-hidden">
      {/* Background decoration with enhanced animations */}
      <div className="absolute inset-0 bg-gradient-to-r from-blue-600/5 to-purple-600/5 dark:from-blue-400/10 dark:to-purple-400/10"></div>
      <div className="absolute top-0 left-0 w-full h-full">
        <div className="absolute top-10 left-10 w-72 h-72 bg-blue-400/10 dark:bg-blue-500/20 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-10 right-10 w-96 h-96 bg-purple-400/10 dark:bg-purple-500/20 rounded-full blur-3xl animate-pulse" style={{animationDelay: '2s'}}></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-80 h-80 bg-gradient-to-r from-green-400/5 to-blue-400/5 dark:from-green-500/10 dark:to-blue-500/10 rounded-full blur-3xl animate-bounce" style={{animationDuration: '6s'}}></div>
      </div>

      <div className="container mx-auto px-4 relative">
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          {/* Left Content */}
          <div className="text-center lg:text-left animate-fade-in-up">
            {/* Badge */}
            <Badge className="mb-6 bg-blue-100 dark:bg-blue-900/50 text-blue-700 dark:text-blue-300 border-blue-200 dark:border-blue-700 hover:bg-blue-150 animate-bounce" style={{animationDelay: '0.5s'}}>
              <Shield className="w-3 h-3 mr-1" />
              Trusted by 500+ Businesses
            </Badge>

            {/* Main Headline with enhanced animations */}
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold leading-tight mb-6 animate-fade-in-up" style={{animationDelay: '0.2s'}}>
              <span className="text-slate-900 dark:text-white">Complete Business</span>{" "}
              <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-purple-600 dark:from-blue-400 dark:to-purple-400 animate-gradient-x">
                Management
              </span>{" "}
              <span className="text-slate-900 dark:text-white">Platform</span>
            </h1>

            {/* Subheadline */}
            <p className="text-lg md:text-xl text-slate-600 dark:text-slate-300 mb-8 leading-relaxed max-w-2xl mx-auto lg:mx-0 animate-fade-in-up" style={{animationDelay: '0.4s'}}>
              Streamline your business with intelligent debt management, customer tracking, and automated payment processing. Built for modern businesses.
            </p>

            {/* Feature highlights */}
            <div className="grid sm:grid-cols-2 gap-4 mb-8 max-w-lg mx-auto lg:mx-0 animate-fade-in-up" style={{animationDelay: '0.6s'}}>
              <div className="flex items-center gap-2 text-slate-700 dark:text-slate-300">
                <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                <span className="text-sm font-medium">Shop Management</span>
              </div>
              <div className="flex items-center gap-2 text-slate-700 dark:text-slate-300">
                <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" style={{animationDelay: '0.5s'}}></div>
                <span className="text-sm font-medium">Debt Tracking</span>
              </div>
              <div className="flex items-center gap-2 text-slate-700 dark:text-slate-300">
                <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" style={{animationDelay: '1s'}}></div>
                <span className="text-sm font-medium">Payment Processing</span>
              </div>
              <div className="flex items-center gap-2 text-slate-700 dark:text-slate-300">
                <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" style={{animationDelay: '1.5s'}}></div>
                <span className="text-sm font-medium">Business Analytics</span>
              </div>
            </div>

            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <Link 
                href="/login"
                className="group relative inline-flex items-center justify-center px-8 py-4 text-lg font-medium text-white bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl hover:from-blue-700 hover:to-purple-700 transition-all duration-300 shadow-xl hover:shadow-2xl transform hover:-translate-y-1"
              >
                <span className="relative z-10">Get Started Free</span>
                <div className="absolute inset-0 bg-gradient-to-r from-blue-400 to-purple-400 rounded-xl opacity-0 group-hover:opacity-20 transition-opacity duration-300"></div>
              </Link>
              
              <button
                onClick={() => {
                  const downloadUrl = `${process.env.NEXT_PUBLIC_API_URL}/api/app/download/latest?platform=android`;
                  const link = document.createElement('a');
                  link.href = downloadUrl;
                  link.download = 'DeynCare.apk';
                  document.body.appendChild(link);
                  link.click();
                  document.body.removeChild(link);
                }}
                className="group relative inline-flex items-center justify-center px-8 py-4 text-lg font-medium text-blue-600 bg-white border-2 border-blue-600 rounded-xl hover:bg-blue-50 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1"
              >
                <span className="relative z-10 flex items-center gap-2">
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                  Download App
                </span>
              </button>
            </div>

            {/* Trust indicators */}
            <div className="mt-12 pt-8 border-t border-slate-200 dark:border-slate-700 animate-fade-in-up" style={{animationDelay: '1s'}}>
              <p className="text-sm text-slate-500 dark:text-slate-400 mb-4">Trusted by businesses across Somalia</p>
              <div className="flex items-center justify-center lg:justify-start gap-6 opacity-60">
                <div className="text-xs text-slate-400 dark:text-slate-500 font-semibold hover:opacity-100 transition-opacity cursor-default">RETAIL</div>
                <div className="text-xs text-slate-400 dark:text-slate-500 font-semibold hover:opacity-100 transition-opacity cursor-default">WHOLESALE</div>
                <div className="text-xs text-slate-400 dark:text-slate-500 font-semibold hover:opacity-100 transition-opacity cursor-default">SERVICES</div>
                <div className="text-xs text-slate-400 dark:text-slate-500 font-semibold hover:opacity-100 transition-opacity cursor-default">ENTERPRISE</div>
              </div>
            </div>
          </div>

          {/* Right Content - Enhanced Dashboard Preview */}
          <div className="relative animate-fade-in-right" style={{animationDelay: '0.5s'}}>
            {/* Browser Window Frame - Enhanced Size */}
            <div className="relative bg-white dark:bg-slate-800 rounded-2xl shadow-2xl border border-slate-200 dark:border-slate-700 overflow-hidden transform hover:scale-105 transition-all duration-500 hover:shadow-3xl">
              {/* Browser Header */}
              <div className="flex items-center justify-between px-6 py-4 bg-slate-50 dark:bg-slate-900 border-b border-slate-200 dark:border-slate-700">
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-500 rounded-lg flex items-center justify-center animate-pulse">
                    <svg width="16" height="16" viewBox="0 0 375 375" className="text-white">
                      <path d="M 147.945312 217.484375 L 174.574219 217.484375 C 187.773438 217.484375 198.726562 206.945312 198.851562 193.75 C 198.980469 180.421875 188.179688 169.539062 174.878906 169.539062 L 105.359375 169.539062 L 122.433594 202.066406 C 127.414062 211.546875 137.238281 217.484375 147.945312 217.484375" fill="currentColor"/>
                      <path d="M 174.125 73.648438 L 55.015625 73.648438 L 72.09375 106.171875 C 77.070312 115.65625 86.894531 121.59375 97.601562 121.59375 L 174.265625 121.59375 C 213.894531 121.59375 246.871094 154.015625 246.800781 193.644531 C 246.730469 233.304688 214.554688 265.433594 174.878906 265.433594 L 155.703125 265.433594 L 171.375 295.285156 C 177.515625 306.980469 190.832031 313.058594 203.65625 309.902344 C 255.902344 297.027344 294.664062 249.894531 294.746094 193.695312 C 294.84375 127.667969 240.152344 73.648438 174.125 73.648438" fill="currentColor"/>
                    </svg>
                  </div>
                  <div>
                    <div className="font-semibold text-slate-900 dark:text-white">DeynCare Dashboard</div>
                    <div className="text-xs text-slate-500 dark:text-slate-400">SuperAdmin Panel</div>
                  </div>
                </div>
                <div className="flex gap-2">
                  <div className="w-3 h-3 bg-red-400 rounded-full animate-pulse"></div>
                  <div className="w-3 h-3 bg-yellow-400 rounded-full animate-pulse" style={{animationDelay: '0.5s'}}></div>
                  <div className="w-3 h-3 bg-green-400 rounded-full animate-pulse" style={{animationDelay: '1s'}}></div>
                </div>
              </div>

              {/* Dashboard Image - Enhanced with larger size */}
              <div className="relative p-6">
                <div className="relative rounded-xl overflow-hidden shadow-lg group">
                  <Image
                    src="/images/dashboard.png"
                    alt="DeynCare Dashboard Interface"
                    width={1200}
                    height={900}
                    className="w-full h-auto object-cover transition-transform duration-700 group-hover:scale-110"
                    priority
                  />
                  {/* Enhanced overlay with animation */}
                  <div className="absolute inset-0 bg-gradient-to-t from-slate-900/20 via-transparent to-transparent dark:from-slate-900/40 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                  
                  {/* Animated scan line effect */}
                  <div className="absolute inset-0 bg-gradient-to-r from-transparent via-blue-400/20 to-transparent w-full h-1 animate-scan-line"></div>
                </div>

                {/* Enhanced Live indicators with animations */}
                <div className="absolute top-8 right-8 flex items-center gap-2 bg-green-500/90 backdrop-blur-sm text-white px-4 py-2 rounded-full text-sm font-medium shadow-lg animate-bounce-slow hover:scale-110 transition-transform cursor-pointer">
                  <div className="w-2 h-2 bg-white rounded-full animate-ping"></div>
                  Live Data
                </div>
                
                <div className="absolute bottom-8 left-8 flex items-center gap-2 bg-blue-500/90 backdrop-blur-sm text-white px-4 py-2 rounded-full text-sm font-medium shadow-lg animate-float hover:scale-110 transition-transform cursor-pointer">
                  <Shield className="w-4 h-4 animate-spin-slow" />
                  AI Powered
                </div>

                {/* New floating elements */}
                <div className="absolute top-1/2 left-4 flex items-center gap-2 bg-purple-500/90 backdrop-blur-sm text-white px-3 py-1 rounded-full text-xs font-medium shadow-lg animate-fade-in-out">
                  <div className="w-2 h-2 bg-white rounded-full"></div>
                  Real-time
                </div>
              </div>

              {/* Enhanced Feature highlights at bottom */}
              <div className="px-6 pb-6">
                <div className="grid grid-cols-3 gap-3">
                  <div className="text-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg hover:scale-105 transition-transform duration-300 cursor-pointer group">
                    <Users className="w-5 h-5 mx-auto mb-2 text-blue-600 dark:text-blue-400 group-hover:animate-bounce" />
                    <div className="text-sm font-medium text-blue-900 dark:text-blue-300">5,241 Users</div>
                  </div>
                  <div className="text-center p-4 bg-green-50 dark:bg-green-900/20 rounded-lg hover:scale-105 transition-transform duration-300 cursor-pointer group">
                    <TrendingUp className="w-5 h-5 mx-auto mb-2 text-green-600 dark:text-green-400 group-hover:animate-bounce" />
                    <div className="text-sm font-medium text-green-900 dark:text-green-300">$12,590</div>
                  </div>
                  <div className="text-center p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg hover:scale-105 transition-transform duration-300 cursor-pointer group">
                    <Shield className="w-5 h-5 mx-auto mb-2 text-purple-600 dark:text-purple-400 group-hover:animate-bounce" />
                    <div className="text-sm font-medium text-purple-900 dark:text-purple-300">36 Shops</div>
                  </div>
                </div>
              </div>
            </div>

            {/* Enhanced Background accents with animations */}
            <div className="absolute -top-8 -right-8 w-32 h-32 bg-gradient-to-r from-blue-400 to-purple-400 dark:from-blue-500 dark:to-purple-500 rounded-full opacity-20 blur-2xl animate-pulse-slow"></div>
            <div className="absolute -bottom-8 -left-8 w-24 h-24 bg-gradient-to-r from-green-400 to-blue-400 dark:from-green-500 dark:to-blue-500 rounded-full opacity-20 blur-2xl animate-float"></div>
            
            {/* New floating particles */}
            <div className="absolute top-4 left-4 w-2 h-2 bg-blue-400 rounded-full animate-ping opacity-60"></div>
            <div className="absolute bottom-4 right-4 w-3 h-3 bg-purple-400 rounded-full animate-bounce opacity-40" style={{animationDelay: '1s'}}></div>
            <div className="absolute top-1/3 -right-2 w-1 h-1 bg-green-400 rounded-full animate-pulse opacity-50" style={{animationDelay: '2s'}}></div>
          </div>
        </div>
      </div>

      <style jsx>{`
        @keyframes fade-in-up {
          from {
            opacity: 0;
            transform: translateY(30px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }
        
        @keyframes fade-in-right {
          from {
            opacity: 0;
            transform: translateX(30px);
          }
          to {
            opacity: 1;
            transform: translateX(0);
          }
        }
        
        @keyframes gradient-x {
          0%, 100% {
            background-size: 200% 200%;
            background-position: left center;
          }
          50% {
            background-size: 200% 200%;
            background-position: right center;
          }
        }
        
        @keyframes scan-line {
          0% {
            transform: translateX(-100%);
          }
          100% {
            transform: translateX(100vw);
          }
        }
        
        @keyframes bounce-slow {
          0%, 100% {
            transform: translateY(0);
          }
          50% {
            transform: translateY(-10px);
          }
        }
        
        @keyframes float {
          0%, 100% {
            transform: translateY(0px);
          }
          50% {
            transform: translateY(-15px);
          }
        }
        
        @keyframes fade-in-out {
          0%, 100% {
            opacity: 0.7;
          }
          50% {
            opacity: 1;
          }
        }
        
        @keyframes spin-slow {
          from {
            transform: rotate(0deg);
          }
          to {
            transform: rotate(360deg);
          }
        }
        
        @keyframes pulse-slow {
          0%, 100% {
            opacity: 0.2;
          }
          50% {
            opacity: 0.4;
          }
        }
        
        .animate-fade-in-up {
          animation: fade-in-up 0.8s ease-out forwards;
        }
        
        .animate-fade-in-right {
          animation: fade-in-right 0.8s ease-out forwards;
        }
        
        .animate-gradient-x {
          animation: gradient-x 3s ease-in-out infinite;
        }
        
        .animate-scan-line {
          animation: scan-line 3s linear infinite;
        }
        
        .animate-bounce-slow {
          animation: bounce-slow 2s ease-in-out infinite;
        }
        
        .animate-float {
          animation: float 3s ease-in-out infinite;
        }
        
        .animate-fade-in-out {
          animation: fade-in-out 2s ease-in-out infinite;
        }
        
        .animate-spin-slow {
          animation: spin-slow 8s linear infinite;
        }
        
        .animate-pulse-slow {
          animation: pulse-slow 4s ease-in-out infinite;
        }
      `}</style>
    </section>
  );
} 