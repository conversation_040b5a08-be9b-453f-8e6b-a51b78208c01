const BaseEmailService = require('./baseEmailService');
const { logInfo, logError } = require('../../utils/logger');
const AppError = require('../../utils/core/AppError');

/**
 * Email service for sending admin-related emails
 */
class AdminEmailService extends BaseEmailService {
  /**
   * Send account suspension notification email
   * @param {string} to - Recipient email
   * @param {Object} data - Email data containing name, reason, and contactEmail
   * @returns {Promise<boolean>} - Success status
   */
  async sendAccountSuspensionEmail(to, data) {
    try {
      // Required data validation
      if (!data.name) {
        logError('Missing name for suspension email', 'AdminEmailService');
        data.name = 'User';
      }

      if (!data.reason) {
        logError('Missing suspension reason for email', 'AdminEmailService');
        data.reason = 'Policy violation';
      }
      
      // Set defaults for any missing fields
      const templateData = {
        name: data.name,
        reason: data.reason,
        contactEmail: data.contactEmail || '<EMAIL>',
        suspensionDate: new Date().toLocaleDateString(),
        year: new Date().getFullYear(),
        subject: 'Your Account Has Been Suspended'
      };
      
      // Render email template
      const html = this.renderTemplate('Admin/account-suspension', templateData);
      
      // Send the email
      return await this.sendMail(to, 'Your Account Has Been Suspended', html);
    } catch (error) {
      logError(`Failed to send account suspension email to ${to}`, 'AdminEmailService', error);
      throw new AppError('Failed to send suspension notification', 500, 'email_error');
    }
  }

  /**
   * Send account reactivation notification email
   * @param {string} to - Recipient email
   * @param {Object} data - Email data containing name and activation details
   * @returns {Promise<boolean>} - Success status
   */
  async sendAccountReactivationEmail(to, data) {
    try {
      // Set default template data
      const templateData = {
        name: data.name || 'User',
        activationDate: new Date().toLocaleDateString(),
        loginUrl: `${process.env.FRONTEND_URL || 'https://deyncare.cajiibcreative.com'}/login`,
        contactEmail: data.contactEmail || '<EMAIL>',
        year: new Date().getFullYear(),
        subject: 'Your Account Has Been Reactivated'
      };
      
      // Explicitly use the exact case that matches the filesystem
      const html = this.renderTemplate('Admin/account-reactivation', templateData);
      
      // Send email directly with explicit template rendering
      return await this.sendMail(to, 'Your Account Has Been Reactivated', html);
    } catch (error) {
      logError(`Failed to send account reactivation email to ${to}`, 'AdminEmailService', error);
      throw new AppError('Failed to send reactivation notification', 500, 'email_error');
    }
  }

  /**
   * Send offline payment order notification to SuperAdmin
   * @param {string} to - SuperAdmin email
   * @param {Object} data - Payment order data
   * @returns {Promise<boolean>} - Success status
   */
  async sendOfflinePaymentOrderNotification(to, data) {
    try {
      // Set template data for the payment verification request
      const templateData = {
        shopName: data.shopName,
        paymentId: data.referenceNumber || data.userId,
        subscriptionId: data.subscriptionId || 'N/A',
        amount: data.amount,
        currency: data.currency || 'USD',
        paymentMethod: data.paymentMethod || 'Offline',
        payerName: data.customerName,
        requestDate: new Date().toLocaleDateString(),
        notes: data.notes || 'Customer selected offline payment method',

        // Action URLs
        approveUrl: `${process.env.FRONTEND_URL || 'https://deyncare.cajiibcreative.com'}/admin/payments/approve/${data.shopId}`,
        rejectUrl: `${process.env.FRONTEND_URL || 'https://deyncare.cajiibcreative.com'}/admin/payments/reject/${data.shopId}`,
        dashboardUrl: `${process.env.FRONTEND_URL || 'https://deyncare.cajiibcreative.com'}/admin/pending-payments`,

        // Fallback URLs
        proofFileUrl: data.proofFileUrl || '#',
        privacyUrl: `${process.env.FRONTEND_URL || 'https://deyncare.cajiibcreative.com'}/privacy`,
        termsUrl: `${process.env.FRONTEND_URL || 'https://deyncare.cajiibcreative.com'}/terms`
      };

      // Prepare attachments if payment proof exists
      const attachments = [];
      if (data.paymentProofPath) {
        const fs = require('fs');
        const path = require('path');

        try {
          const filePath = path.resolve(data.paymentProofPath);
          if (fs.existsSync(filePath)) {
            const fileContent = fs.readFileSync(filePath);
            const fileName = path.basename(filePath);
            const fileExtension = path.extname(fileName).toLowerCase();

            // Determine content type based on file extension
            let contentType = 'application/octet-stream';
            if (['.jpg', '.jpeg'].includes(fileExtension)) {
              contentType = 'image/jpeg';
            } else if (fileExtension === '.png') {
              contentType = 'image/png';
            } else if (fileExtension === '.pdf') {
              contentType = 'application/pdf';
            }

            attachments.push({
              filename: `payment-proof-${data.shopName}-${fileName}`,
              content: fileContent,
              contentType
            });

            logInfo(`Payment proof attachment added: ${fileName}`, 'AdminEmailService');
          }
        } catch (fileError) {
          logError(`Failed to attach payment proof: ${fileError.message}`, 'AdminEmailService');
          // Continue without attachment if file processing fails
        }
      }

      // Use enhanced sendEmail method with attachments
      const subject = `New Offline Payment Order - ${data.shopName}`;
      const result = await this.sendEmail({
        to,
        subject,
        template: 'Admin/payment-verification-request',
        data: templateData,
        attachments
      });

      logInfo(`Offline payment order notification sent to SuperAdmin: ${to} ${attachments.length > 0 ? 'with attachment' : ''}`, 'AdminEmailService');
      return result;
    } catch (error) {
      logError(`Failed to send offline payment order notification to ${to}`, 'AdminEmailService', error);
      throw new AppError('Failed to send payment order notification', 500, 'email_error');
    }
  }
}

module.exports = new AdminEmailService();
