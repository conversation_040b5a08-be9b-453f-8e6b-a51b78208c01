# 🚀 Manual Deployment Guide

This guide explains how to use the enhanced `deploy.sh` script to deploy your DeynCare backend directly on your VPS server.

## 📋 Prerequisites

- VPS server with SSH access
- Git installed on server
- Node.js and NPM installed
- PM2 process manager installed
- Repository cloned and current directory is the project root

## 🔧 Setup Instructions

### Step 1: Upload the Script to Your Server

**Option A: Using SCP (from your local machine)**
```bash
scp deploy.sh your-user@your-server-ip:/var/www/deyncare-backend.khanciye.com/
```

**Option B: The script is already in your repository**
```bash
# SSH into your server
ssh your-user@your-server-ip

# Navigate to your project directory
cd /var/www/deyncare-backend.khanciye.com

# The deploy.sh script should already be there
```

### Step 2: Make the Script Executable

```bash
chmod +x deploy.sh
```

## 🚀 Usage Examples

### Full Deployment
```bash
# Run complete deployment
./deploy.sh
```

### Check System Requirements
```bash
# Check if all required tools are installed
./deploy.sh --check
```

### Pull Code Only
```bash
# Just pull the latest code
./deploy.sh --pull
```

### Restart PM2 Only
```bash
# Just restart PM2 processes
./deploy.sh --restart
```

### Show Current Status
```bash
# Show system info and current status
./deploy.sh --status
```

### Create Backup Only
```bash
# Create backup without deploying
./deploy.sh --backup
```

### Health Check Only
```bash
# Perform health check
./deploy.sh --health
```

### Show Help
```bash
# Show all available options
./deploy.sh --help
```

## 📊 Script Features

### ✅ What the Script Does:

1. **System Check**: Verifies all required tools are installed
2. **Backup Creation**: Creates timestamped backup of current deployment
3. **Code Pull**: Fetches and pulls latest code from main branch
4. **Dependencies**: Installs/updates NPM packages
5. **Directory Creation**: Creates necessary directories (logs, uploads)
6. **PM2 Restart**: Reloads PM2 processes
7. **Cleanup**: Removes old backups (keeps last 5)
8. **Health Check**: Verifies application is running correctly
9. **Logging**: Logs all operations to `/var/log/deyncare-deploy.log`

### 🎨 Visual Output:
- **Color-coded messages**: Green for success, red for errors, yellow for warnings
- **Timestamps**: All operations are timestamped
- **Progress indicators**: Clear indication of what's happening
- **Summary**: Shows deployment summary at the end

## 🔍 Troubleshooting

### Common Issues:

#### 1. Permission Denied
```bash
# Fix script permissions
chmod +x deploy.sh
```

#### 2. Directory Not Found
```bash
# Check if you're in the correct directory
pwd
ls -la deploy.sh

# Navigate to correct directory
cd /var/www/deyncare-backend.khanciye.com
```

#### 3. Git Not Configured
```bash
# Configure git
git config --global user.email "<EMAIL>"
git config --global user.name "Your Name"
```

#### 4. PM2 Not Installed
```bash
# Install PM2 globally
npm install -g pm2
```

#### 5. Node.js Not Installed
```bash
# Install Node.js (Ubuntu/Debian)
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs
```

## 📋 Pre-Deployment Checklist

- [ ] Repository cloned to `/var/www/deyncare-backend.khanciye.com`
- [ ] Git configured with user name and email
- [ ] Node.js and NPM installed
- [ ] PM2 installed and configured
- [ ] SSH access to server
- [ ] Write permissions to application directory
- [ ] Script permissions set (`chmod +x deploy.sh`)

## 🔒 Security Considerations

- Script creates backups in `/var/backups/deyncare-backend`
- Logs operations to `/var/log/deyncare-deploy.log`
- Only keeps last 5 backups to save disk space
- Checks requirements before deployment
- Uses `set -e` to exit on any error

## 📝 Log Files

- **Deployment Log**: `/var/log/deyncare-deploy.log`
- **PM2 Logs**: Check with `pm2 logs deyncare-backend`
- **Application Logs**: Check your app's log configuration

## 🔄 Rollback Procedure

If something goes wrong:

1. **Stop current processes**:
   ```bash
   pm2 stop deyncare-backend
   ```

2. **Restore from backup**:
   ```bash
   cd /var/backups/deyncare-backend
   ls -la  # Find the backup you want to restore
   tar -xzf backup-YYYYMMDD-HHMMSS.tar.gz -C /var/www/deyncare-backend.khanciye.com/
   ```

3. **Restart PM2**:
   ```bash
   cd /var/www/deyncare-backend.khanciye.com
   pm2 start ecosystem.config.js --env production
   ```

## 🎯 Quick Start Guide

1. **SSH into your server**:
   ```bash
   ssh your-user@your-server-ip
   ```

2. **Navigate to project directory**:
   ```bash
   cd /var/www/deyncare-backend.khanciye.com
   ```

3. **Run deployment**:
   ```bash
   ./deploy.sh
   ```

That's it! The script will handle everything automatically.

## 📞 Support

If you encounter issues:

1. Check the log file: `tail -f /var/log/deyncare-deploy.log`
2. Run system check: `./deploy.sh --check`
3. Check PM2 status: `pm2 status`
4. Verify you're in the correct directory: `pwd`

## 🌟 Advanced Usage

### Automated Deployment with Cron
```bash
# Edit crontab
crontab -e

# Add this line for daily deployment at 2 AM
0 2 * * * cd /var/www/deyncare-backend.khanciye.com && ./deploy.sh >> /var/log/cron-deploy.log 2>&1
```

### Monitoring Deployment
```bash
# Watch deployment in real-time
tail -f /var/log/deyncare-deploy.log

# Check PM2 monitoring
pm2 monit
```

---

**✅ This enhanced deployment script provides a reliable and comprehensive way to deploy your backend with full control and monitoring!** 