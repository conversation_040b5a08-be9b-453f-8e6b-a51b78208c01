const express = require('express');
const userController = require('../controllers/userController');
const { authenticate, authorize } = require('../middleware/authMiddleware');
const { validate } = require('../middleware/validationMiddleware');
const { userSchemas } = require('../validations');

const router = express.Router();

/**
 * User Management Routes
 * Base path: /api/users
 * These endpoints are primarily for SuperAdmin functionality
 */

// Debug middleware to log all requests to /api/users
router.use((req, res, next) => {
  console.log(`[UserRoutes] Request: ${req.method} ${req.originalUrl} - Path: ${req.path}`);
  next();
});

// Get user statistics - SuperAdmin only
router.get(
  '/stats',
  authenticate,
  authorize(['superAdmin']),
  userController.getUserStats
);

// List all users - SuperAdmin only
router.get(
  '/',
  authenticate,
  authorize(['superAdmin']),
  userController.getAllUsers
);

// EMPLOYEE MANAGEMENT ROUTES (Must come BEFORE /:userId to avoid conflicts)

// Get all employees for the shop (Admin only)
router.get('/employees',
  (req, res, next) => {
    console.log(`[Route] /employees route matched! Method: ${req.method}, URL: ${req.originalUrl}`);
    next();
  },
  authenticate,
  authorize(['admin']),
  userController.getEmployees
);

// Get employee by ID (Admin only)
router.get('/employees/:employeeId',
  authenticate,
  authorize(['admin']),
  userController.getEmployeeById
);

// Update employee permissions (Admin only)
router.put('/employees/:employeeId/permissions',
  authenticate,
  authorize(['admin']),
  userController.updateEmployeePermissions
);

// Delete employee (Admin only)
router.delete('/employees/:employeeId',
  authenticate,
  authorize(['admin']),
  userController.deleteEmployee
);

// Reset employee password (Admin only)
router.post('/employees/:employeeId/reset-password',
  authenticate,
  authorize(['admin']),
  validate(require('../validations/schemas/authSchemas').resetEmployeePassword),
  userController.resetEmployeePassword
);

// Get user by ID - SuperAdmin can get any user, Admin can only get users from their shop
router.get(
  '/:userId',
  authenticate,
  authorize(['superAdmin', 'admin']),
  userController.getUserById
);

// Create new user - SuperAdmin can create users for any shop
router.post(
  '/',
  authenticate,
  authorize(['superAdmin']),
  validate(userSchemas.createUser),
  userController.createUser
);

// Update user - SuperAdmin can update any user, Admin can only update users from their shop
router.put(
  '/:userId',
  authenticate,
  authorize(['superAdmin', 'admin']),
  validate(userSchemas.updateUser),
  userController.updateUser
);

// Change user status - SuperAdmin can change any user's status, Admin can only change users from their shop
router.patch(
  '/:userId/status',
  authenticate,
  authorize(['superAdmin', 'admin']),
  validate(userSchemas.changeUserStatus),
  // Add error catching middleware for this route specifically
  (req, res, next) => {
    try {
      // Apply additional validation that the schema might not catch
      const { status } = req.body;
      
      // Verify the status enum value is valid
      if (!['active', 'inactive', 'suspended'].includes(status)) {
        return res.status(400).json({
          success: false, 
          message: 'Invalid status value provided',
          statusCode: 400,
          type: 'validation_error'
        });
      }
      
      // All checks passed - continue to controller
      next();
    } catch (err) {
      console.error('User status route validation error:', err);
      return res.status(400).json({
        success: false,
        message: 'Error in status update request',
        statusCode: 400,
        type: 'validation_error'
      });
    }
  },
  userController.changeUserStatus
);

// Delete user - SuperAdmin can delete any user, Admin can only delete users from their shop
router.delete(
  '/:userId',
  authenticate,
  authorize(['superAdmin', 'admin']),
  validate(userSchemas.deleteUser),
  userController.deleteUser
);

module.exports = router;



