"use client";

import { useState } from 'react';
import { toast } from 'sonner';
import { Trash2, AlertTriangle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { usePlanMutations } from '@/hooks/usePlan-index';
import { formatCurrency } from './utils/planFormatters';

export function DeletePlanDialog({ isOpen, onClose, plan, onSuccess }) {
  const [loading, setLoading] = useState(false);
  // FIXED: Disable showToastMessages to prevent duplicate toasts (service layer already shows them)
  const { deletePlan } = usePlanMutations({ 
    showToastMessages: false  // Service layer already shows toast messages
  });

  if (!plan) return null;

  const handleDelete = async () => {
    try {
      setLoading(true);
      
      await deletePlan(plan.planId);
      
      // Toast message already shown by service layer
      onSuccess?.();
      onClose();
    } catch (error) {
      console.error('Error deleting plan:', error);
      // Error toast already shown by service layer
    } finally {
      setLoading(false);
    }
  };

  const hasActiveSubscriptions = plan.subscriberCount > 0;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-destructive">
            <Trash2 className="h-5 w-5" />
            Delete Plan
          </DialogTitle>
          <DialogDescription>
            This action cannot be undone. This will permanently delete the plan.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* Plan Details */}
          <div className="rounded-lg border p-4 space-y-2">
            <div className="flex justify-between">
              <span className="text-sm text-muted-foreground">Plan Name:</span>
              <span className="text-sm font-medium">{plan.displayName}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-muted-foreground">Type:</span>
              <span className="text-sm capitalize">{plan.type}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-muted-foreground">Price:</span>
              <span className="text-sm">
                {plan.pricing ? formatCurrency(plan.pricing.basePrice, plan.pricing.currency) : 'Free'}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-muted-foreground">Active Subscribers:</span>
              <span className="text-sm font-medium">{plan.subscriberCount || 0}</span>
            </div>
          </div>

          {/* Warning for active subscriptions */}
          {hasActiveSubscriptions && (
            <Alert className="border-orange-200 bg-orange-50">
              <AlertTriangle className="h-4 w-4 text-orange-600" />
              <AlertDescription className="text-orange-800">
                <strong>Warning:</strong> This plan has {plan.subscriberCount} active subscription(s). 
                Deleting this plan may affect existing subscribers. Consider deactivating the plan instead.
              </AlertDescription>
            </Alert>
          )}

          {/* Confirmation */}
          <Alert className="border-red-200 bg-red-50">
            <AlertTriangle className="h-4 w-4 text-red-600" />
            <AlertDescription className="text-red-800">
              <strong>This action is irreversible!</strong> All plan data including pricing, limits, 
              and configuration will be permanently deleted.
            </AlertDescription>
          </Alert>

          {/* Action Buttons */}
          <div className="flex justify-end gap-3 pt-4">
            <Button
              variant="outline"
              onClick={onClose}
              disabled={loading}
            >
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={handleDelete}
              disabled={loading}
            >
              {loading ? 'Deleting...' : 'Delete Plan'}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
} 