/**
 * Get Current Subscription Controller
 * Handles retrieving the current user's/shop's active subscription
 */
const { SubscriptionService } = require('../../services');
const { successResponse } = require('../../utils');

/**
 * Get current subscription for authenticated user/shop
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
const getCurrentSubscription = async (req, res, next) => {
  try {
    const { shopId, role } = req.user;
    
    // For SuperAdmin, we might need to handle differently
    // For now, if no shopId (like SuperAdmin), return empty or handle appropriately
    if (!shopId && role === 'superAdmin') {
      return successResponse(res, {
        message: 'SuperAdmin does not have a subscription',
        data: null,
      });
    }
    
    if (!shopId) {
      throw new Error('No shop associated with this user');
    }
    
    // Get current subscription for the shop
    const subscription = await SubscriptionService.getCurrentSubscriptionByShop(shopId);
    
    // Return successful response
    return successResponse(res, {
      message: 'Current subscription retrieved successfully',
      data: subscription,
    });
  } catch (error) {
    next(error);
  }
};

module.exports = getCurrentSubscription; 