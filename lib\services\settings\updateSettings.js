/**
 * Update Settings Service
 * Handles both general settings (individual keys) and security settings (bulk update)
 */

import { updateSecuritySettings, updateSettingByKey } from '../../api/modules/settings';
import { handleError, logApiCall } from '../baseService';

/**
 * Update general settings by updating individual setting keys
 * @param {Object} settings - Settings object to update
 * @returns {Promise<Object>} Update result
 */
const updateGeneralSettings = async (settings) => {
  const context = 'updateGeneralSettings';
  
  try {
    logApiCall(context, 'PATCH /api/settings/:key (multiple)', settings);
    
    // Map frontend setting names to backend setting keys
    const settingKeyMap = {
      appName: 'app_name',
      companyName: 'company_name',
      companyEmail: 'company_email',
      companyPhone: 'company_phone',
      companyAddress: 'company_address',
      maintenance: 'maintenance_mode',
      enableNotifications: 'enable_notifications',
      defaultCurrency: 'default_currency'
    };
    
    // Update each setting individually
    const updatePromises = [];
    
    for (const [frontendKey, value] of Object.entries(settings)) {
      if (frontendKey in settingKeyMap) {
        const backendKey = settingKeyMap[frontendKey];
        updatePromises.push(
          updateSettingByKey(backendKey, value)
            .then(result => ({ key: frontendKey, success: true, result }))
            .catch(error => ({ key: frontendKey, success: false, error }))
        );
      }
    }
    
    if (updatePromises.length === 0) {
      return {
        success: false,
        message: 'No valid settings to update'
      };
    }
    
    // Execute all updates in parallel
    const results = await Promise.all(updatePromises);
    
    // Check if all updates succeeded
    const failed = results.filter(r => !r.success);
    const succeeded = results.filter(r => r.success);
    
    if (failed.length === 0) {
      console.log(`[${context}] All settings updated successfully:`, succeeded.map(s => s.key));
      return {
        success: true,
        message: `Successfully updated ${succeeded.length} setting(s)`,
        updated: succeeded.length
      };
    } else if (succeeded.length > 0) {
      console.warn(`[${context}] Partial success:`, { succeeded: succeeded.length, failed: failed.length });
      return {
        success: true,
        message: `Updated ${succeeded.length}/${results.length} settings. Some failed.`,
        updated: succeeded.length,
        failed: failed.length,
        errors: failed.map(f => ({ key: f.key, error: f.error.message }))
      };
    } else {
      console.error(`[${context}] All updates failed:`, failed);
      return {
        success: false,
        message: 'Failed to update any settings',
        errors: failed.map(f => ({ key: f.key, error: f.error.message }))
      };
    }
    
  } catch (error) {
    console.error(`[${context}] Error:`, error);
    handleError(error, context, false);
    
    return {
      success: false,
      message: error.message || 'Failed to update settings',
      error: error
    };
  }
};

/**
 * Update settings - handles both general and security settings
 * @param {Object} settings - Settings object to update
 * @returns {Promise<Object>} Update result
 */
const updateSettings = async (settings) => {
  // Check if this looks like security settings (has nested objects like passwordPolicy)
  const isSecuritySettings = settings.passwordPolicy || settings.sessionSettings || settings.securityHeaders;
  
  if (isSecuritySettings) {
    // Use the security settings endpoint
    const context = 'updateSecuritySettings';
    
    try {
      logApiCall(context, 'PUT /api/settings', settings);
      
      const response = await updateSecuritySettings(settings);
      
      if (response?.success) {
        console.log(`[${context}] Success:`, {
          message: response.message
        });
        
        return {
          success: true,
          message: response.message || 'Security settings updated successfully'
        };
      } else {
        console.warn(`[${context}] API returned success=false:`, response);
        return {
          success: false,
          message: response?.message || 'Failed to update security settings'
        };
      }
      
    } catch (error) {
      console.error(`[${context}] Error:`, error);
      handleError(error, context, false);
      
      return {
        success: false,
        message: error.message || 'Failed to update security settings',
        error: error
      };
    }
  } else {
    // Use individual setting updates for general settings
    return updateGeneralSettings(settings);
  }
};

export default updateSettings;
