/**
 * Apply Discount Module
 * Handles applying discount codes and tracking usage
 */
const { AppError, logError } = require('../../utils');

// Import modules to avoid circular dependencies
const { getDiscountByCode } = require('./getDiscount');
const validateAndCalculateDiscount = require('./validateDiscount');

/**
 * Apply a discount code to a purchase
 * @param {String} code - Discount code
 * @param {Number} amount - Purchase amount
 * @param {String} context - Context (subscription, debt)
 * @param {String} userId - User ID
 * @param {String} shopId - Shop ID
 * @returns {Promise<Object>} Applied discount details
 */
const applyDiscountCode = async (code, amount, context = 'subscription', userId, shopId = null) => {
  try {
    // First validate and calculate
    const discountDetails = await validateAndCalculateDiscount(
      code, amount, context, userId, shopId
    );
    
    // Get the discount code
    const discountCode = await getDiscountByCode(code);
    
    // Increment usage count
    await discountCode.use();
    
    // Return discount details
    return {
      ...discountDetails,
      applied: true,
      usageCount: discountCode.usageCount,
      usageLimit: discountCode.usageLimit
    };
  } catch (error) {
    if (error instanceof AppError) {
      throw error;
    }
    
    logError(`Failed to apply discount code: ${error.message}`, 'DiscountService', error);
    throw new AppError('Failed to apply discount code', 500, 'discount_application_error');
  }
};

module.exports = applyDiscountCode;
