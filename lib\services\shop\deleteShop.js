import apiBridge from '@/lib/api/bridge';
import { ENDPOINTS } from '@/lib/api/contract';
import { toast } from 'sonner';
import BaseService from '@/lib/services/baseService';

/**
 * Delete a shop via SuperAdmin API
 * @param {string} shopId - ID of the shop to delete
 * @param {string} reason - Reason for deletion (required by SuperAdmin backend)
 * @returns {Promise<boolean>} Success status
 */
async function deleteShop(shopId, reason) {
  try {
    // Ensure reason is provided as it's required by the SuperAdmin backend
    if (!reason || reason.trim().length < 3) {
      toast.error('Please provide a valid reason for deletion (minimum 3 characters)');
      return false;
    }
    
    // Make API request using SuperAdmin endpoint with proper payload
    const response = await apiBridge.delete(`${ENDPOINTS.SHOPS.BASE}/${shopId}`, {
      data: { reason }, // SuperAdmin expects reason in request body
      clearCacheEndpoint: ENDPOINTS.SHOPS.BASE
    });
    
    // Process response
    if (response.data && response.data.success) {
      toast.success('Shop deleted successfully');
      return true;
    }
    
    // Handle unexpected response
    console.error('[SuperAdminShopService] Unexpected API response format:', response.data);
    toast.error('Failed to delete shop: Unexpected response format');
    return false;
  } catch (error) {
    console.error(`[SuperAdminShopService] Error deleting shop ${shopId}:`, error);
    
    // Enhanced error handling for SuperAdmin operations
    if (error.response?.status === 400 && error.response?.data?.message) {
      BaseService.handleError({ message: `Validation error: ${error.response.data.message}` }, 'SuperAdminShopService.deleteShop', true);
    } else if (error.response?.status === 401) {
      BaseService.handleError({ message: 'Unauthorized: SuperAdmin access required' }, 'SuperAdminShopService.deleteShop', true);
    } else if (error.response?.status === 403) {
      BaseService.handleError({ message: 'Forbidden: Only SuperAdmins can delete shops' }, 'SuperAdminShopService.deleteShop', true);
    } else if (error.response?.status === 404) {
      BaseService.handleError({ message: 'Shop not found' }, 'SuperAdminShopService.deleteShop', true);
    } else {
      BaseService.handleError(error, 'SuperAdminShopService.deleteShop', true);
    }
    
    return false;
  }
}

export default deleteShop;
