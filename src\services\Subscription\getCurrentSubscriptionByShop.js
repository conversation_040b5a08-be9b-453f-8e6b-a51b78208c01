/**
 * Get Current Subscription by Shop Service
 */
const { Subscription } = require('../../models');
const { AppError, logError } = require('../../utils');

/**
 * Get current active subscription for a shop
 * @param {string} shopId - ID of the shop
 * @returns {Promise<Object>} Found subscription or null
 */
const getCurrentSubscriptionByShop = async (shopId) => {
  try {
    // Find the most recent, non-deleted subscription for the shop (regardless of status)
    const subscription = await Subscription.findOne({ 
      shopId, 
      isDeleted: false
    })
    .sort({ createdAt: -1 }) // Get the most recent one
    .populate('plan'); // If you have plan population
    // Removed .lean() to return Mongoose document with save() method

    return subscription;
  } catch (error) {
    logError(`Failed to get current subscription for shop: ${shopId}`, 'SubscriptionService', error);
    throw error;
  }
};

module.exports = getCurrentSubscriptionByShop; 