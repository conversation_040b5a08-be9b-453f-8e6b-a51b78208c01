/**
 * EVC Payment Service
 * Handles all interactions with the WaafiPay API for EVC Plus payments
 */
const evcPlus = require('evc-plus');
const { AppError, logError, logSuccess, logInfo } = require('../utils');

/**
 * EVC Payment Service provides methods for processing EVC Plus payments
 */
const EVCPaymentService = {
  /**
   * Format merchant phone number according to WaafiPay requirements
   * @param {string} phone - Phone number to format
   * @returns {string} Formatted phone number
   */
  formatMerchantPhone: (phone) => {
    return evcPlus.formatMerchantPhone(phone);
  },
  
  /**
   * Process a payment using WaafiPay/EVC Plus
   * @param {Object} paymentData - Payment details
   * @param {string} paymentData.phone - Customer phone number
   * @param {number} paymentData.amount - Payment amount
   * @param {string} paymentData.description - Payment description
   * @param {string} paymentData.reference - Internal reference (debtId, subscriptionId, etc.)
   * @param {string} paymentData.shopName - Shop name for transaction description
   * @param {string} paymentData.shopId - Shop ID for shop-specific credentials (optional)
   * @returns {Promise<Object>} Payment response from WaafiPay
   */
  payByWaafiPay: async (paymentData) => {
    try {
      // Format phone number
      const formattedPhone = EVCPaymentService.formatMerchantPhone(paymentData.phone);
      
      // Import necessary services
      const { SettingsHelper } = require('../utils');
      
      // Get secure API credentials from settings
      let credentials = await SettingsHelper.getEVCCredentials(paymentData.shopId);
      
      if (!credentials || Object.keys(credentials).length === 0) {
        logError('No EVC credentials found in settings', 'EVCPaymentService');
        throw new AppError('Payment provider credentials not found', 500, 'missing_credentials');
      }
      
      const { merchantUId, apiUserId, apiKey, merchantNo, url } = credentials;

      const axios = require('axios');
      const waafiPayload = {
        schemaVersion: '1.0',
        requestId: Date.now().toString(),
        timestamp: Date.now(),
        channelName: 'WEB',
        serviceName: 'API_PURCHASE',
        serviceParams: {
          merchantUid: merchantUId,
          apiUserId: apiUserId,
          apiKey: `API-${apiKey}`,
          paymentMethod: 'mwallet_account',
          payerInfo: {
            accountNo: formattedPhone
          },
          transactionInfo: {
            referenceId: paymentData.reference,
            invoiceId: paymentData.reference,
            amount: paymentData.amount,
            currency: 'USD',
            description: paymentData.description || `Payment for ${paymentData.shopName || 'DeynCare'}`
          }
        }
      };

      const requestConfig = {
        method: 'post',
        url,
        headers: {
          'Content-Type': 'application/json',
          'Accept': '*/*'
        },
        data: waafiPayload,
        timeout: 45000, // Increased timeout to 45 seconds
      };

      let waafiResponse;
      try {
        waafiResponse = await axios(requestConfig);
      } catch (error) {
        // Check if this is a timeout error
        if (error.code === 'ECONNABORTED') {
          // Check if the transaction might have succeeded despite the timeout
          try {
            // Attempt to verify the transaction status
            const verifyResponse = await axios({
              method: 'post',
              url,
              headers: {
                'Content-Type': 'application/json',
                'Accept': '*/*'
              },
              data: {
                ...waafiPayload,
                serviceName: 'API_CHECK_STATUS',
                serviceParams: {
                  ...waafiPayload.serviceParams,
                  transactionId: paymentData.reference
                }
              },
              timeout: 10000
            });

            if (verifyResponse.data && 
                (verifyResponse.data.responseCode === '0' || 
                 (verifyResponse.data.responseCode === '2001' && verifyResponse.data.errorCode === '0'))) {
              logSuccess(`EVC Plus payment verified successful after timeout: ${paymentData.reference}`, 'EVCPaymentService');
              return {
                success: true,
                transactionId: verifyResponse.data.transactionId || paymentData.reference,
                responseCode: verifyResponse.data.responseCode,
                responseMessage: 'Payment verified successful after timeout',
                paymentInfo: verifyResponse.data
              };
            }
          } catch (verifyError) {
            logError(`Failed to verify transaction status after timeout: ${verifyError.message}`, 'EVCPaymentService');
          }
        }
        throw error;
      }

      const waafiData = waafiResponse.data;

      if (!waafiData) {
        throw new AppError('No data received from WaafiPay API response', 500, 'invalid_gateway_response');
      }

      if (waafiData.responseCode === '0' || (waafiData.responseCode === '2001' && waafiData.errorCode === '0')) {
        logSuccess(`EVC Plus payment successful: ${waafiData.transactionId || waafiData.responseId} (Code: ${waafiData.responseCode})`, 'EVCPaymentService');
        return {
          success: true,
          transactionId: waafiData.transactionId || waafiData.responseId,
          responseCode: waafiData.responseCode,
          responseMessage: waafiData.responseMsg,
          paymentInfo: waafiData
        };
      } else {
        logError(`EVC Plus payment failed (Code: ${waafiData.responseCode}): ${waafiData.responseMsg}`, 'EVCPaymentService', waafiData);
        return {
          success: false,
          responseCode: waafiData.responseCode,
          responseMessage: waafiData.responseMsg,
          paymentInfo: waafiData
        };
      }
    } catch (error) {
      logError(`EVC Plus payment processing error: ${error.message}`, 'EVCPaymentService', error);
      
      if (error instanceof AppError) {
        throw error;
      }
      
      throw new AppError(
        `Payment processing failed: ${error.message}`, 
        502, 
        'evc_payment_error'
      );
    }
  },
  
  /**
   * Retry a failed EVC Plus payment with exponential backoff
   * @param {Object} paymentData - Payment details (same as payByWaafiPay)
   * @param {number} retries - Number of retry attempts (default: 2)
   * @param {number} delay - Initial delay in ms before first retry (default: 1000ms)
   * @returns {Promise<Object>} Payment response from WaafiPay
   */
  retryPayment: async (paymentData, retries = 2, delay = 1000) => {
    try {
      // First attempt
      try {
        return await EVCPaymentService.payByWaafiPay(paymentData);
      } catch (error) {
        // If not a retriable error or no retries left, throw
        if (!error.retriable || retries <= 0) {
          throw error;
        }
        
        // Wait for delay before retrying
        await new Promise(resolve => setTimeout(resolve, delay));
        
        // Exponential backoff for next retry
        return EVCPaymentService.retryPayment(
          paymentData,
          retries - 1,
          delay * 2
        );
      }
    } catch (error) {
      logError(`All payment retry attempts failed for ${paymentData.reference}: ${error.message}`, 'EVCPaymentService', error);
      throw error;
    }
  },
  
  /**
   * Test EVC payment credentials by making a minimal API call
   * @param {string} shopId - Shop ID for shop-specific credentials (optional)
   * @returns {Promise<Object>} Test result with success status and message
   */
  testConnection: async (shopId = null) => {
    try {
      // Import necessary services
      const { SettingsHelper } = require('../utils');
      
      // Get secure API credentials (first try shop-specific, then fall back to global)
      // SettingsHelper.getEVCCredentials now handles decryption with shop-specific keys
      let credentials = await SettingsHelper.getEVCCredentials(shopId);
      
      // Log credential access for audit purposes
      logInfo(`EVC credentials accessed for ${shopId ? 'shop ' + shopId : 'global system'} for connection test`, 'EVCPaymentService');
      
      // Fall back to environment variables if not found in settings
      if (!credentials) {
        credentials = {
          merchantUId: process.env.WAAFI_MERCHANT_UID,
          apiUserId: process.env.WAAFI_API_USER_ID,
          apiKey: process.env.WAAFI_API_KEY,
          merchantNo: process.env.WAAFI_MERCHANT_NO,
          url: process.env.WAAFI_API_URL || 'https://api.waafi.app/asm'
        };
      }
      
      const { merchantUId, apiUserId, apiKey, merchantNo, url } = credentials;
      
      if (!merchantUId || !apiUserId || !apiKey) {
        return {
          success: false,
          message: 'Missing required EVC credentials'
        };
      }
      
      // Perform a minimal API check without actual payment
      // This is just a simple validation request to the API
      // In a real implementation, you would use a dedicated API endpoint for validation
      // For this example, we're just checking if credentials exist and are well-formed
      
      logInfo(`Testing EVC credentials for ${shopId ? 'shop ' + shopId : 'global system'}`, 'EVCPaymentService');
      
      // For demonstration, we'll just validate the format of the credentials
      // In a real implementation, you should make an actual API call to verify
      const isValid = merchantUId && merchantUId.length >= 5 &&
        apiUserId && apiUserId.length >= 5 &&
        apiKey && apiKey.length >= 8;
      
      if (isValid) {
        return {
          success: true,
          message: 'EVC credentials validated successfully'
        };
      } else {
        return {
          success: false,
          message: 'EVC credentials failed validation check'
        };
      }
    } catch (error) {
      logError(`Failed to test EVC connection: ${error.message}`, 'EVCPaymentService', error);
      return {
        success: false,
        message: `Connection test failed: ${error.message}`
      };
    }
  },

  /**
   * Test EVC payment by making a real payment request with a minimal amount
   * This is used to verify that the credentials work for actual payments
   * @param {Object} testData - Test payment data
   * @param {string} testData.phone - Customer phone number to test with
   * @param {number} testData.amount - Test payment amount (should be minimal)
   * @param {string} testData.shopId - Shop ID for shop-specific credentials (optional)
   * @param {string} testData.description - Test payment description
   * @param {string} testData.reference - Reference ID for the test transaction
   * @returns {Promise<Object>} Test result with success status and message
   */
  testPayment: async (testData) => {
    try {
      const { phone, amount, shopId, description, reference } = testData;
      
      if (!phone || !amount) {
        return {
          success: false,
          message: 'Phone number and amount are required for payment testing'
        };
      }
      
      // Use a small amount to avoid significant charges
      const testAmount = Math.min(parseFloat(amount) || 0.1, 1.0);
      
      logInfo(`Initiating EVC test payment for phone ${phone} with amount $${testAmount}`, 'EVCPaymentService');
      
      // Make an actual payment request using the payByWaafiPay method
      const paymentResult = await EVCPaymentService.payByWaafiPay({
        phone,
        amount: testAmount,
        shopId,
        description: description || 'DeynCare EVC Integration Test',
        reference: reference || `test-${Date.now()}`,
        shopName: 'DeynCare Test'
      });
      
      if (paymentResult.success) {
        return {
          success: true,
          message: `Test payment successful! Transaction ID: ${paymentResult.transactionId}`,
          transactionId: paymentResult.transactionId
        };
      } else {
        // Payment request was processed but failed at the payment gateway
        return {
          success: false,
          message: `Test payment failed: ${paymentResult.responseMessage || 'Unknown error'}`,
          responseCode: paymentResult.responseCode
        };
      }
    } catch (error) {
      logError(`EVC test payment failed: ${error.message}`, 'EVCPaymentService', error);
      return {
        success: false,
        message: `Test payment failed: ${error.message}`
      };
    }
  }
};

module.exports = EVCPaymentService;
