/**
 * Customer Service
 * Service wrapper for customer-related operations
 */
const { getAllCustomers } = require('../controllers/customer/getAllCustomers');
const { getCustomerById } = require('../controllers/customer/getCustomerById');
const { createCustomer } = require('../controllers/customer/createCustomer');
const { updateCustomer } = require('../controllers/customer/updateCustomer');
const { deleteCustomer } = require('../controllers/customer/deleteCustomer');
const { getCustomerStats } = require('../controllers/customer/getCustomerStats');
const { getCustomerDebts } = require('../controllers/customer/getCustomerDebts');

class CustomerService {
  /**
   * Get all customers
   * @param {Object} filters - Filter options
   * @param {number} page - Page number  
   * @param {number} limit - Items per page
   * @returns {Promise<Object>} Customers with pagination
   */
  static async getAllCustomers(filters = {}, page = 1, limit = 10) {
    // Create mock request object
    const req = {
      query: { ...filters, page, limit },
      user: { shopId: filters.shopId }
    };
    
    // Create mock response object
    let result = {};
    const res = {
      json: (data) => {
        result = data;
      },
      status: () => res
    };
    
    // Call the controller
    await getAllCustomers(req, res);
    
    return result.data || { docs: [], pagination: {} };
  }

  /**
   * Get customer by ID
   * @param {string} customerId - Customer ID
   * @returns {Promise<Object>} Customer data
   */
  static async getCustomerById(customerId) {
    const req = {
      params: { customerId }
    };
    
    let result = {};
    const res = {
      json: (data) => {
        result = data;
      },
      status: () => res
    };
    
    await getCustomerById(req, res);
    
    return result.data?.customer;
  }

  /**
   * Create a new customer
   * @param {Object} customerData - Customer data
   * @returns {Promise<Object>} Created customer
   */
  static async createCustomer(customerData) {
    const req = {
      body: customerData,
      user: { shopId: customerData.shopId }
    };
    
    let result = {};
    const res = {
      json: (data) => {
        result = data;
      },
      status: () => res
    };
    
    await createCustomer(req, res);
    
    return result.data?.customer;
  }

  /**
   * Update customer
   * @param {string} customerId - Customer ID
   * @param {Object} updateData - Update data
   * @returns {Promise<Object>} Updated customer
   */
  static async updateCustomer(customerId, updateData) {
    const req = {
      params: { customerId },
      body: updateData
    };
    
    let result = {};
    const res = {
      json: (data) => {
        result = data;
      },
      status: () => res
    };
    
    await updateCustomer(req, res);
    
    return result.data?.customer;
  }

  /**
   * Delete customer
   * @param {string} customerId - Customer ID
   * @returns {Promise<boolean>} Success status
   */
  static async deleteCustomer(customerId) {
    const req = {
      params: { customerId }
    };
    
    let result = {};
    const res = {
      json: (data) => {
        result = data;
      },
      status: () => res
    };
    
    await deleteCustomer(req, res);
    
    return result.success;
  }

  /**
   * Get customer statistics
   * @param {Object} filters - Filter options
   * @returns {Promise<Object>} Customer statistics
   */
  static async getCustomerStats(filters = {}) {
    const req = {
      query: filters,
      user: { shopId: filters.shopId }
    };
    
    let result = {};
    const res = {
      json: (data) => {
        result = data;
      },
      status: () => res
    };
    
    await getCustomerStats(req, res);
    
    return result.data?.stats;
  }

  /**
   * Get customer debts
   * @param {string} customerId - Customer ID
   * @param {Object} filters - Filter options
   * @returns {Promise<Object>} Customer debts
   */
  static async getCustomerDebts(customerId, filters = {}) {
    const req = {
      params: { customerId },
      query: filters
    };
    
    let result = {};
    const res = {
      json: (data) => {
        result = data;
      },
      status: () => res
    };
    
    await getCustomerDebts(req, res);
    
    return result.data?.debts || [];
  }
}

module.exports = CustomerService; 