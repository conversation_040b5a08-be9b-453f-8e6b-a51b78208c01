// src/scheduler/index.js
// Centralized scheduler for all recurring jobs using node-cron

const cron = require('node-cron');
const mongoose = require('mongoose');
const dotenv = require('dotenv');
const {
  processTrialReminders,
  processExpiryReminders,
  processAutoRenewals,
  processExpiredDeactivation,
  runAllTasks
} = require('../cron/subscriptionTasks');
const runPaymentRetryProcess = require('../cron/processPaymentRetries');
const mlRiskEvaluationCron = require('../cron/mlRiskEvaluation');
const DebtReminderCron = require('../cron/debtReminderCron');

// Load environment variables
dotenv.config();

// Ensure Mongoose is connected before running jobs
async function ensureDbConnection() {
  if (mongoose.connection.readyState === 1) return;
  await mongoose.connect(process.env.MONGODB_URI, {
    useNewUrlParser: true,
    useUnifiedTopology: true
  });
}

// Subscription Lifecycle Jobs
cron.schedule('0 2 * * *', async () => {
  // Every day at 2:00 AM
  await ensureDbConnection();
  console.log('[CRON] Running all subscription lifecycle tasks...');
  await runAllTasks();
});

cron.schedule('0 3 * * *', async () => {
  // Every day at 3:00 AM
  await ensureDbConnection();
  console.log('[CRON] Running subscription expiry reminders...');
  await processExpiryReminders();
});

cron.schedule('0 4 * * *', async () => {
  // Every day at 4:00 AM
  await ensureDbConnection();
  console.log('[CRON] Running subscription auto-renewals...');
  await processAutoRenewals();
});

cron.schedule('0 5 * * *', async () => {
  // Every day at 5:00 AM
  await ensureDbConnection();
  console.log('[CRON] Running expired subscription deactivation...');
  await processExpiredDeactivation();
});

// Payment Retry Job
cron.schedule('0 * * * *', async () => {
  // Every hour
  await ensureDbConnection();
  console.log('[CRON] Running payment retry processing...');
  await runPaymentRetryProcess();
});

// ML Risk Evaluation Job
if (mlRiskEvaluationCron && typeof mlRiskEvaluationCron.start === 'function') {
  mlRiskEvaluationCron.start();
}

// Debt Reminder Job
if (process.env.DEBT_REMINDERS_ENABLED === 'true') {
  DebtReminderCron.start();
  console.log('[Scheduler] Debt reminder cron job initialized (daily at 9:00 AM)');
} else {
  console.log('[Scheduler] Debt reminder cron job disabled (DEBT_REMINDERS_ENABLED=false)');
}

console.log('[Scheduler] All cron jobs scheduled.');
