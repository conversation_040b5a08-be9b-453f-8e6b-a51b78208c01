/**
 * Fetch System Logs Service
 * 
 * Retrieves system logs with optional filtering
 */
import settingsAPI from '../../api/modules/settings';
import { handleError } from '../baseService';

/**
 * Fetch system logs with filters
 * @param {Object} filters - Log filters (type, level, search, pagination)
 * @returns {Object} Logs data with pagination info
 */
const fetchSystemLogs = async (filters = {}) => {
  try {
    const response = await settingsAPI.getSystemLogs(filters);
    return response.data;
  } catch (error) {
    handleError(error, 'SettingsService.fetchSystemLogs', true);
    return { logs: [], pagination: { total: 0, page: 1, limit: 10 } };
  }
};

export default fetchSystemLogs;
