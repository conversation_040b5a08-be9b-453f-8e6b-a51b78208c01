'use client';

import { useCallback, useState, useEffect, useRef } from 'react';
import { useUserContext } from '@/contexts/user-context';
import UserService from '@/lib/services/user';
import { toast } from 'sonner';
import { useRealTimeUpdates, useRealTimeUpdater, useAutoRefresh } from './use-real-time-updates';

/**
 * Enhanced hook for user management operations
 * OPTIMIZED: Eliminated infinite loops and redundant API calls
 */
export function useUsersEnhanced(options = {}) {
  // Extract options with defaults
  const { 
    initialFilters = {}, 
    initialPage = 1, 
    initialLimit = 10,
    skipInitialFetch = false,
    onInitialized = () => {},
    includeStats = true,
    autoRefreshStats = false, // DISABLED by default
    statsRefreshInterval = 300000 // 5 minutes default
  } = options;

  // Get user context
  const {
    users,
    loading,
    error,
    pagination,
    filters,
    loadUsers,
    getUserById,
    createUser,
    updateUser,
    deleteUser,
    changeUserStatus,
    selectUser,
    setFilters,
    setPagination
  } = useUserContext();

  // Real-time updates integration
  const { triggerUpdate, invalidateCache } = useRealTimeUpdater();

  // Statistics state management
  const [stats, setStats] = useState(null);
  const [statsLoading, setStatsLoading] = useState(false);
  const [statsError, setStatsError] = useState(null);
  const [statsLastUpdated, setStatsLastUpdated] = useState(null);
  
  // Refs for cleanup and tracking
  const abortControllerRef = useRef(null);
  const refreshIntervalRef = useRef(null);
  const isMountedRef = useRef(true);
  
  // CRITICAL FIX: Enhanced debouncing and request deduplication
  const fetchStatsInProgressRef = useRef(false);
  const statsDebounceRef = useRef(null);
  const isInitializedRef = useRef(false);
  const lastStatsCallRef = useRef(0);
  const MIN_STATS_INTERVAL = 2000; // Minimum 2 seconds between stats calls

  // Fetch users with enhanced parameters
  const fetchUsers = useCallback(async (newFilters, newPage, newLimit) => {
    try {
      const result = await loadUsers(
        newFilters !== undefined ? newFilters : filters,
        newPage !== undefined ? newPage : pagination.currentPage,
        newLimit !== undefined ? newLimit : pagination.pageSize
      );
      
      // REMOVED: Real-time update to prevent loops
      return result;
    } catch (error) {
      console.error('Error fetching users:', error);
      return [];
    }
  }, [loadUsers, filters, pagination]);

  // Enhanced refreshUserList with real-time updates
  const refreshUserList = useCallback(async () => {
    try {
      const result = await fetchUsers();
      return result;
    } catch (error) {
      console.error('[useUsersEnhanced] Error refreshing users:', error);
      toast.error('Failed to refresh users');
      throw error;
    }
  }, [fetchUsers]);

  // OPTIMIZED: Enhanced debounced stats fetcher
  const fetchStats = useCallback(async (statsFilters = {}, showToast = false) => {
    if (!includeStats) return null;
    
    // CRITICAL: Enforce minimum interval between calls
    const now = Date.now();
    if (now - lastStatsCallRef.current < MIN_STATS_INTERVAL) {
      return null;
    }
    
    // CRITICAL: Prevent duplicate calls
    if (fetchStatsInProgressRef.current) {
      return null;
    }

    // Clear any pending debounced call
    if (statsDebounceRef.current) {
      clearTimeout(statsDebounceRef.current);
      statsDebounceRef.current = null;
    }

    // Mark as in progress and update last call time
    fetchStatsInProgressRef.current = true;
    lastStatsCallRef.current = now;

    // Cancel any existing request
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    // Create new abort controller
    abortControllerRef.current = new AbortController();

    try {
      setStatsLoading(true);
      setStatsError(null);

      if (showToast) {
        toast.info('Loading user statistics...');
      }

      const statsData = await UserService.getUserStats(statsFilters);

      // Check if component is still mounted
      if (!isMountedRef.current) {
        return null;
      }

      if (statsData) {
        setStats(statsData);
        setStatsLastUpdated(new Date());
        
        if (showToast) {
          toast.success('User statistics loaded successfully');
        }
        
        return statsData;
      } else {
        setStatsError('No statistics data received');
        if (showToast) {
          toast.error('Failed to load user statistics');
        }
        return null;
      }
    } catch (err) {
      console.error('[useUsersEnhanced] Error fetching statistics:', err);
      
      // Check if component is still mounted and error wasn't due to abort
      if (!isMountedRef.current || err.name === 'AbortError') {
        return null;
      }

      const errorMessage = err.message || 'Failed to load user statistics';
      setStatsError(errorMessage);
      
      if (showToast) {
        toast.error(errorMessage);
      }
      
      return null;
    } finally {
      if (isMountedRef.current) {
        setStatsLoading(false);
      }
      // CRITICAL: Reset the in-progress flag
      fetchStatsInProgressRef.current = false;
    }
  }, [includeStats]);

  // REMOVED: Real-time updates to prevent infinite loops
  // The page will handle updates manually through user actions

  // Refresh statistics
  const refreshStats = useCallback((showToast = true) => {
    return fetchStats({}, showToast);
  }, [fetchStats]);

  // Combined refresh for both users and stats
  const refreshAll = useCallback(async () => {
    const promises = [refreshUserList()];
    if (includeStats) {
      promises.push(refreshStats(false)); // Silent stats refresh
    }
    
    try {
      await Promise.all(promises);
      
    } catch (error) {
      console.error('[useUsersEnhanced] Error refreshing data:', error);
      throw error; // Let the caller handle the error
    }
  }, [refreshUserList, refreshStats, includeStats]);
  
  // Change page
  const changePage = useCallback((page) => {
    setPagination(prev => ({ ...prev, currentPage: page }));
    return fetchUsers(undefined, page, undefined);
  }, [fetchUsers, setPagination]);
  
  // Change page size
  const changePageSize = useCallback((pageSize) => {
    setPagination(prev => ({ ...prev, pageSize }));
    return fetchUsers(undefined, 1, pageSize); // Reset to first page when changing page size
  }, [fetchUsers, setPagination]);
  
  // Apply filters with loading check
  const applyFilters = useCallback((newFilters) => {
    // Prevent applying filters if already loading to avoid race conditions
    if (loading) {
      return;
    }
    
    setFilters(newFilters);
    return fetchUsers(newFilters, 1, undefined); // Reset to first page when applying filters
  }, [fetchUsers, setFilters, loading]);
  
  // Clear filters
  const clearFilters = useCallback(() => {
    setFilters({});
    return fetchUsers({}, 1, undefined); // Reset to first page when clearing filters
  }, [fetchUsers, setFilters]);
  
  // Enhanced CRUD operations without real-time updates to prevent loops
  const enhancedCreateUser = useCallback(async (userData) => {
    try {
      const result = await createUser(userData);
      // REMOVED: Real-time update to prevent loops
      return result;
    } catch (error) {
      console.error('[useUsersEnhanced] Error creating user:', error);
      throw error;
    }
  }, [createUser]);

  const enhancedUpdateUser = useCallback(async (userId, updateData) => {
    try {
      const result = await updateUser(userId, updateData);
      // REMOVED: Real-time update to prevent loops
      return result;
    } catch (error) {
      console.error('[useUsersEnhanced] Error updating user:', error);
      throw error;
    }
  }, [updateUser]);

  const enhancedDeleteUser = useCallback(async (userId) => {
    try {
      const result = await deleteUser(userId);
      // REMOVED: Real-time update to prevent loops
      return result;
    } catch (error) {
      console.error('[useUsersEnhanced] Error deleting user:', error);
      throw error;
    }
  }, [deleteUser]);

  const enhancedChangeUserStatus = useCallback(async (userId, newStatus, reason) => {
    try {
      const result = await changeUserStatus(userId, newStatus, reason);
      // REMOVED: Real-time update to prevent loops
      return result;
    } catch (error) {
      console.error('[useUsersEnhanced] Error changing user status:', error);
      throw error;
    }
  }, [changeUserStatus]);

  // REMOVED: Auto-refresh to prevent conflicts and improve performance
  // Manual refresh will be handled by user actions

  // OPTIMIZED: Single initialization with better error handling
  useEffect(() => {
    const initializeData = async () => {
      // CRITICAL: Prevent multiple initializations
      if (isInitializedRef.current || skipInitialFetch) {
        return;
      }
      
      isInitializedRef.current = true;
      
      try {
        // Sequential initialization to prevent race conditions
        await fetchUsers();
        
        // Only fetch stats if needed and not already in progress
        if (includeStats && !fetchStatsInProgressRef.current) {
          // Delay stats fetch slightly to prevent concurrent requests
          setTimeout(() => {
            fetchStats({}, false);
          }, 500);
        }
        
        onInitialized();
      } catch (error) {
        console.error('[useUsersEnhanced] Error during initialization:', error);
        isInitializedRef.current = false; // Reset on error to allow retry
      }
    };

    initializeData();
  }, []); // CRITICAL: Empty dependency array to ensure single initialization

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      isMountedRef.current = false;
      
      // Cancel any pending request
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
      
      // Clear refresh interval
      if (refreshIntervalRef.current) {
        clearInterval(refreshIntervalRef.current);
      }
      
      // Clear stats debounce
      if (statsDebounceRef.current) {
        clearTimeout(statsDebounceRef.current);
      }
    };
  }, []);

  // Compute derived state for statistics
  const hasStats = stats !== null;
  const isStatsStale = statsLastUpdated && (Date.now() - statsLastUpdated.getTime()) > 300000; // 5 minutes

  return {
    users,
    isLoading: loading,
    error,
    pagination,
    filters,
    fetchUsers,
    changePage,
    changePageSize,
    applyFilters,
    clearFilters,
    refreshUsers: refreshUserList, // Alias for backwards compatibility
    refreshUserList, // Required by change-status-dialog component
    refreshAll, // New method to refresh both users and stats
    getUserById,
    createUser: enhancedCreateUser,
    updateUser: enhancedUpdateUser,
    deleteUser: enhancedDeleteUser,
    changeUserStatus: enhancedChangeUserStatus,
    selectUser,

    // Statistics functionality
    stats,
    statsLoading,
    statsError,
    statsLastUpdated,
    hasStats,
    isStatsStale,
    fetchStats,
    refreshStats,
    
    // Easy access to statistics data
    summary: stats?.summary || {
      totalUsers: 0,
      activeUsers: 0,
      inactiveUsers: 0,
      suspendedUsers: 0
    },
    
    activity: stats?.activity || {
      recentRegistrations: 0,
      activeInLastWeek: 0,
      verifiedUsers: 0,
      paidUsers: 0
    },
    
    distribution: stats?.distribution || {
      byRole: [],
      byStatus: []
    },
    
    trends: stats?.trends || []
  };
}
