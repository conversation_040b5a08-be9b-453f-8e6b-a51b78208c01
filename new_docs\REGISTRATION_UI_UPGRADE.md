# Registration UI Upgrade Plan

## 1. Use Cases Updates

### CheckAuthStatusUseCase
```dart
class CheckAuthStatusUseCase {
  Future<AuthStatus> execute() async {
    final isLoggedIn = await _repository.isLoggedIn();
    final registrationStatus = await _repository.getRegistrationStatus();
    final paymentStatus = await _repository.getPaymentStatus();
    
    return AuthStatus(
      isLoggedIn: isLoggedIn,
      registrationStatus: registrationStatus,
      paymentStatus: paymentStatus,
    );
  }
}
```

### New Registration Use Cases
```dart
class InitRegistrationUseCase {
  Future<(User, RegistrationProgress)> execute({
    required String fullName,
    required String email,
    required String phone,
    required String password,
    required String shopName,
    required String shopAddress,
    String planType = 'trial',
    String paymentMethod = 'offline',
    bool initialPaid = false,
    String? discountCode,
  });
}

class VerifyEmailUseCase {
  Future<(User, AuthToken)> execute({
    required String email,
    required String verificationCode,
  });
}

class ProcessPaymentUseCase {
  Future<(User, AuthToken)> execute({
    required String userId,
    required String shopId,
    required String planId,
    required String paymentMethod,
    required Map<String, dynamic> paymentDetails,
    String? discountCode,
  });
}
```

### Enhanced Plan Use Case
```dart
class GetPlanDetailsUseCase {
  Future<PlanDetails> execute(String planId) async {
    final plan = await _repository.getPlanById(planId);
    return PlanDetails(
      plan: plan,
      trialPeriod: plan.trialPeriodDays,
      features: plan.features,
      limitations: plan.limitations,
      upgradePaths: plan.upgradePaths,
      supportsDiscount: plan.supportsDiscountCodes,
    );
  }
}
```

## 2. Auth Bloc Updates

### New Events
```dart
abstract class AuthEvent extends Equatable {
  const AuthEvent();
}

class InitRegistrationRequested extends AuthEvent {
  final String fullName;
  final String email;
  final String phone;
  final String password;
  final String shopName;
  final String shopAddress;
  final String planType;
  final String paymentMethod;
  final bool initialPaid;
  final String? discountCode;
}

class EmailVerificationRequested extends AuthEvent {
  final String email;
  final String verificationCode;
}

class PaymentProcessingRequested extends AuthEvent {
  final String userId;
  final String shopId;
  final String planId;
  final String paymentMethod;
  final Map<String, dynamic> paymentDetails;
  final String? discountCode;
}

class RegistrationStepCompleted extends AuthEvent {
  final String step;
  final Map<String, dynamic> data;
}
```

### New States
```dart
abstract class AuthState extends Equatable {
  const AuthState();
}

class RegistrationInProgress extends AuthState {
  final String currentStep;
  final int progress;
  final Map<String, dynamic> data;
}

class EmailVerificationPending extends AuthState {
  final String email;
  final String userId;
  final DateTime expiresAt;
}

class PaymentProcessing extends AuthState {
  final String userId;
  final String planId;
  final String paymentMethod;
  final Map<String, dynamic> paymentDetails;
}

class RegistrationComplete extends AuthState {
  final User user;
  final AuthToken token;
}
```

## 3. UI Updates

### RegisterScreen Updates
```dart
class RegisterScreen extends StatefulWidget {
  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AuthBloc, AuthState>(
      builder: (context, state) {
        if (state is RegistrationInProgress) {
          return RegistrationProgressView(
            currentStep: state.currentStep,
            progress: state.progress,
            data: state.data,
          );
        }
        
        if (state is EmailVerificationPending) {
          return EmailVerificationView(
            email: state.email,
            userId: state.userId,
            expiresAt: state.expiresAt,
          );
        }
        
        if (state is PaymentProcessing) {
          return PaymentProcessingView(
            userId: state.userId,
            planId: state.planId,
            paymentMethod: state.paymentMethod,
            paymentDetails: state.paymentDetails,
          );
        }
        
        return RegistrationForm();
      },
    );
  }
}
```

### PaymentScreen Updates
```dart
class PaymentScreen extends StatefulWidget {
  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AuthBloc, AuthState>(
      builder: (context, state) {
        if (state is PaymentProcessing) {
          return PaymentProcessingView(
            userId: state.userId,
            planId: state.planId,
            paymentMethod: state.paymentMethod,
            paymentDetails: state.paymentDetails,
          );
        }
        
        return PaymentForm(
          onPaymentMethodSelected: _handlePaymentMethodSelected,
          onPaymentDetailsSubmitted: _handlePaymentDetailsSubmitted,
          onDiscountCodeApplied: _handleDiscountCodeApplied,
        );
      },
    );
  }
}
```

## 4. Implementation Steps

1. **Phase 1: Use Cases**
   - Create new use cases
   - Update existing use cases
   - Add new models

2. **Phase 2: Bloc Updates**
   - Add new events
   - Add new states
   - Update event handlers

3. **Phase 3: UI Updates**
   - Create new screens
   - Update existing screens
   - Add progress tracking

4. **Phase 4: Testing**
   - Unit tests for use cases
   - Widget tests for UI
   - Integration tests for flow

## 5. Migration Strategy

1. **Backward Compatibility**
   - Keep old screens temporarily
   - Add new screens alongside
   - Gradually deprecate old screens

2. **User Experience**
   - Clear progress indicators
   - Helpful error messages
   - Smooth transitions

3. **Error Handling**
   - Specific error states
   - Recovery options
   - User guidance 