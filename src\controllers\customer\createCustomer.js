const Customer = require('../../models/customer.model');
const { generateCustomerId } = require('../../utils/generators/idGenerator');
const { AppError, logError, logInfo } = require('../../utils');

/**
 * Create Customer Controller (Admin Role - Shop Owner)
 * - Creates customer record for debt transactions
 * - Generates unique CustomerID for ML tracking
 * - Initializes risk profile
 */
const createCustomer = async (req, res, next) => {
  try {
    const {
      CustomerName,
      CustomerType, // "new" or "returning"
      phone,
      email,
      address,
      // Optional fields
      creditLimit,
      category,
      notes
    } = req.body;

    const shopId = req.user.shopId;

    // Enhanced validation
    if (!CustomerName || CustomerName.trim().length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Customer name is required',
        errorCode: 'validation_error'
      });
    }

    if (!CustomerType || !['new', 'returning'].includes(CustomerType.toLowerCase())) {
      return res.status(400).json({
        success: false,
        message: 'Customer type must be either "new" or "returning"',
        errorCode: 'validation_error'
      });
    }

    if (!phone || phone.trim().length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Phone number is required',
        errorCode: 'validation_error'
      });
    }

    // Check if customer already exists (more specific error messages)
    const existingCustomerByName = await Customer.findOne({
      shopId,
      CustomerName: CustomerName.trim(),
      isDeleted: false
    });

    if (existingCustomerByName) {
      return res.status(400).json({
        success: false,
        message: `Customer with name "${CustomerName.trim()}" already exists`,
        errorCode: 'duplicate_customer_name'
      });
    }

    const existingCustomerByPhone = await Customer.findOne({
      shopId,
      phone: phone.trim(),
      isDeleted: false
    });

    if (existingCustomerByPhone) {
      return res.status(400).json({
        success: false,
        message: `Customer with phone number "${phone.trim()}" already exists`,
        errorCode: 'duplicate_customer_phone'
      });
    }

    // Generate proper customer ID using idGenerator system
    const customerId = await generateCustomerId(Customer);

    if (!customerId) {
      logError('Failed to generate customer ID', 'CreateCustomer');
      return res.status(500).json({
        success: false,
        message: 'Failed to generate customer ID. Please try again.',
        errorCode: 'id_generation_failed'
      });
    }

    // Create customer record with normalized data
    const customer = new Customer({
      customerId,
      shopId,
      CustomerName: CustomerName.trim(),
      CustomerType: CustomerType.toLowerCase() === 'new' ? 'New' : 'Returning',
      phone: phone.trim(),
      email: email ? email.trim() : undefined,
      address: address ? address.trim() : undefined,
      // Optional fields
      creditLimit: creditLimit || 0,
      category: category ? category.trim() : 'regular',
      notes: notes ? notes.trim() : undefined
      // riskProfile will use model defaults: Low Risk, score 0, no assessment date
    });

    await customer.save();

    logInfo(`Customer created successfully: ${customerId} (${CustomerName.trim()}) by ${req.user.email}`, 'CreateCustomer');

    // Enhanced response data
    const responseData = {
      success: true,
      message: 'Customer created successfully',
      data: {
        customer: {
          customerId: customer.customerId,
          customerName: customer.CustomerName, // Use consistent naming
          customerType: customer.CustomerType,
          phone: customer.phone,
          email: customer.email,
          address: customer.address,
          creditLimit: customer.creditLimit,
          category: customer.category,
          notes: customer.notes,
          riskProfile: customer.riskProfile,
          createdAt: customer.createdAt,
          updatedAt: customer.updatedAt
        },
        capabilities: {
          canTakeDebt: true,
          mlTrackingEnabled: true
        }
      }
    };

    res.status(201).json(responseData);

  } catch (error) {
    logError('Create customer error', 'CreateCustomer', error);
    
    // Handle specific MongoDB errors
    if (error.code === 11000) {
      const field = Object.keys(error.keyPattern)[0];
      return res.status(400).json({
        success: false,
        message: `Customer with this ${field} already exists`,
        errorCode: 'duplicate_key_error'
      });
    }

    // Handle validation errors
    if (error.name === 'ValidationError') {
      const validationErrors = Object.values(error.errors).map(err => err.message);
      return res.status(400).json({
        success: false,
        message: `Validation failed: ${validationErrors.join(', ')}`,
        errorCode: 'validation_error'
      });
    }

    return next(new AppError('Failed to create customer', 500));
  }
};

module.exports = createCustomer; 