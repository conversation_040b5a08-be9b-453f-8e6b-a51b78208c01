"use client";

import Link from "next/link";
import { Logo } from "@/components/ui/logo";
import HeroSection from "@/components/landing/hero-section";
import FeaturesShowcase from "@/components/landing/features-showcase";
import StatsSection from "@/components/landing/stats-section";
import Testimonials from "@/components/landing/testimonials";

export default function Home() {
  return (
    <div className="flex min-h-screen flex-col">
      {/* Header */}
      <header className="border-b border-slate-200 dark:border-slate-700 bg-white/95 dark:bg-slate-900/95 backdrop-blur-sm sticky top-0 z-50">
        <div className="container mx-auto flex h-16 items-center justify-between px-4 sm:px-6 lg:px-8">
          {/* Logo - responsive sizing */}
          <div className="flex items-center">
            <Logo 
              variant="full" 
              size="lg"
              showText={true}
              className="sm:flex hidden"
            />
            {/* Mobile logo - icon only */}
            <Logo 
              variant="icon" 
              size="md"
              className="sm:hidden flex"
            />
          </div>
          
          {/* Navigation - improved mobile layout */}
          <div className="flex items-center gap-2 sm:gap-4">
            <Link 
              href="/login" 
              className="rounded-md px-3 py-2 sm:px-4 text-xs sm:text-sm font-medium text-slate-700 dark:text-slate-300 hover:bg-slate-100 dark:hover:bg-slate-800 transition-colors"
            >
              <span className="hidden sm:inline">Log in</span>
              <span className="sm:hidden">Login</span>
            </Link>
            <button
              onClick={() => {
                const downloadUrl = `${process.env.NEXT_PUBLIC_API_URL}/api/app/download/latest?platform=android`;
                const link = document.createElement('a');
                link.href = downloadUrl;
                link.download = 'DeynCare.apk';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
              }}
              className="rounded-md bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 px-3 py-2 sm:px-4 text-xs sm:text-sm font-medium text-white transition-all shadow-sm"
            >
              <span className="hidden sm:inline">Download App</span>
              <span className="sm:hidden">Download</span>
            </button>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <HeroSection />

      {/* Features Showcase */}
      <FeaturesShowcase />

      {/* Stats and Analytics Section */}
      <StatsSection />

      {/* Testimonials and Use Cases */}
      <Testimonials />

      {/* Footer */}
      <footer id="contact" className="bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 text-white py-20">
        <div className="container mx-auto px-4">
          <div className="grid gap-12 lg:grid-cols-4 md:grid-cols-2">
            {/* Brand Section with Logo */}
            <div className="lg:col-span-2">
              <div className="flex items-center gap-3 mb-6">
                <div className="w-12 h-12 bg-white rounded-xl flex items-center justify-center p-2">
                  <svg 
                    width="32" 
                    height="32" 
                    viewBox="0 0 375 375" 
                    className="w-full h-full"
                  >
                    <path 
                      d="M 147.945312 217.484375 L 174.574219 217.484375 C 187.773438 217.484375 198.726562 206.945312 198.851562 193.75 C 198.980469 180.421875 188.179688 169.539062 174.878906 169.539062 L 105.359375 169.539062 L 122.433594 202.066406 C 127.414062 211.546875 137.238281 217.484375 147.945312 217.484375" 
                      fill="#141dee"
                    />
                    <path 
                      d="M 174.125 73.648438 L 55.015625 73.648438 L 72.09375 106.171875 C 77.070312 115.65625 86.894531 121.59375 97.601562 121.59375 L 174.265625 121.59375 C 213.894531 121.59375 246.871094 154.015625 246.800781 193.644531 C 246.730469 233.304688 214.554688 265.433594 174.878906 265.433594 L 155.703125 265.433594 L 171.375 295.285156 C 177.515625 306.980469 190.832031 313.058594 203.65625 309.902344 C 255.902344 297.027344 294.664062 249.894531 294.746094 193.695312 C 294.84375 127.667969 240.152344 73.648438 174.125 73.648438" 
                      fill="#141dee"
                    />
                  </svg>
                </div>
                <div>
                  <div className="text-2xl font-bold text-white">DeynCare</div>
                  <div className="text-sm text-slate-400">Business Management Platform</div>
                </div>
              </div>
              
              <p className="text-slate-300 mb-6 max-w-md leading-relaxed">
                DeynCare empowers businesses across Somalia with intelligent debt management, 
                subscription automation, and comprehensive analytics. Trusted by 500+ businesses.
              </p>
              
              {/* Trust Badges */}
              <div className="flex flex-wrap gap-3 mb-8">
                <div className="flex items-center gap-2 bg-slate-800 px-4 py-2 rounded-full border border-slate-700">
                  <div className="w-4 h-4 bg-green-500 rounded-full"></div>
                  <span className="text-sm text-slate-300">EVC Plus Certified</span>
                </div>
                <div className="flex items-center gap-2 bg-slate-800 px-4 py-2 rounded-full border border-slate-700">
                  <div className="w-4 h-4 bg-blue-500 rounded-full"></div>
                  <span className="text-sm text-slate-300">Bank-Grade Security</span>
                </div>
                <div className="flex items-center gap-2 bg-slate-800 px-4 py-2 rounded-full border border-slate-700">
                  <div className="w-4 h-4 bg-purple-500 rounded-full"></div>
                  <span className="text-sm text-slate-300">99.9% Uptime</span>
                </div>
              </div>

              {/* Stats */}
              <div className="grid grid-cols-3 gap-6">
                <div>
                  <div className="text-2xl font-bold text-white">500+</div>
                  <div className="text-xs text-slate-400">Active Businesses</div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-white">18K+</div>
                  <div className="text-xs text-slate-400">Total Users</div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-white">94.7%</div>
                  <div className="text-xs text-slate-400">Success Rate</div>
                </div>
              </div>
            </div>
            
            {/* Product Links */}
            <div>
              <h3 className="mb-6 text-lg font-semibold text-white">Product</h3>
              <ul className="space-y-3 text-sm">
                <li>
                  <Link href="#features" className="text-slate-300 hover:text-white transition-colors flex items-center gap-2">
                    <span>Features Overview</span>
                  </Link>
                </li>
                <li>
                  <Link href="#analytics" className="text-slate-300 hover:text-white transition-colors">
                    Analytics & Reports
                  </Link>
                </li>
                <li>
                  <Link href="#testimonials" className="text-slate-300 hover:text-white transition-colors">
                    Success Stories
                  </Link>
                </li>
                <li>
                  <Link href="/login" className="text-blue-400 hover:text-blue-300 font-medium transition-colors">
                    Start Free Trial →
                  </Link>
                </li>
              </ul>
            </div>
            
            {/* Support & Contact */}
            <div>
              <h3 className="mb-6 text-lg font-semibold text-white">Support & Contact</h3>
              <ul className="space-y-3 text-sm">
                <li>
                  <Link href="#" className="text-slate-300 hover:text-white transition-colors">
                    Help Center
                  </Link>
                </li>
                <li>
                  <Link href="#" className="text-slate-300 hover:text-white transition-colors">
                    Documentation
                  </Link>
                </li>
                <li>
                  <Link href="#" className="text-slate-300 hover:text-white transition-colors">
                    API Reference
                  </Link>
                </li>
                <li>
                  <Link href="#" className="text-slate-300 hover:text-white transition-colors">
                    System Status
                  </Link>
                </li>
                <li className="pt-2">
                  <div className="text-slate-400 text-xs mb-1">Email Support:</div>
                  <a href="mailto:<EMAIL>" className="text-white font-medium hover:text-blue-400 transition-colors">
                    <EMAIL>
                  </a>
                </li>
                <li>
                  <div className="text-slate-400 text-xs mb-1">Phone Support:</div>
                  <a href="tel:+252-61-xxx-xxxx" className="text-white font-medium hover:text-blue-400 transition-colors">
                    +252 61 XXX XXXX
                  </a>
                </li>
              </ul>
            </div>
          </div>
          
          {/* Bottom Section */}
          <div className="mt-16 pt-8 border-t border-slate-700">
            <div className="flex flex-col lg:flex-row justify-between items-center gap-6">
              <div className="flex flex-col sm:flex-row items-center gap-4">
                <div className="text-sm text-slate-400">
                  © {new Date().getFullYear()} DeynCare Platform. All rights reserved.
                </div>
                <div className="flex items-center gap-2 text-sm text-slate-500">
                  <span>Made with</span>
                  <span className="text-red-500">❤️</span>
                  <span>for Somali businesses</span>
                </div>
              </div>
              
              <div className="flex flex-wrap gap-6 text-sm">
                <Link href="#" className="text-slate-400 hover:text-white transition-colors">
                  Privacy Policy
                </Link>
                <Link href="#" className="text-slate-400 hover:text-white transition-colors">
                  Terms of Service
                </Link>
                <Link href="#" className="text-slate-400 hover:text-white transition-colors">
                  Cookie Policy
                </Link>
                <Link href="#" className="text-slate-400 hover:text-white transition-colors">
                  Security
                </Link>
              </div>
            </div>
            
            {/* Additional Info */}
            <div className="mt-6 pt-6 border-t border-slate-800">
              <div className="flex flex-col sm:flex-row justify-between items-center gap-4 text-xs text-slate-500">
                <div>
                  DeynCare is a registered trademark. EVC Plus integration certified and secured.
                </div>
                <div className="flex items-center gap-4">
                  <span>Available in Somalia</span>
                  <span>•</span>
                  <span>Multi-language support</span>
                  <span>•</span>
                  <span>24/7 monitoring</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}
