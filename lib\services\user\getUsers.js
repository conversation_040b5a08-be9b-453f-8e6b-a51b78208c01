import apiBridge from '@/lib/api/bridge';
import { ENDPOINTS } from '@/lib/api/contract';
import { toast } from 'sonner';
import BaseService from '@/lib/services/baseService';

/**
 * Get users with optional filters and pagination
 * @param {Object} filters - Filter criteria
 * @param {number} page - Page number
 * @param {number} limit - Results per page
 * @returns {Promise<Array>} Array of users
 */
async function getUsers(filters = {}, page = 1, limit = 10) {
  try {
    // Combine filters with pagination
    const params = { ...filters, page, limit };
    
    // Make API request using the bridge
    const response = await apiBridge.get(ENDPOINTS.USERS.BASE, { params });
    
    // Process response
    if (response.data && response.data.success) {
      return response.data.data.users || [];
    }
    
    // Handle unexpected response
    console.error('Unexpected API response format:', response.data);
    toast.error('Failed to fetch users: Unexpected response format');
    return [];
  } catch (error) {
    console.error('Error fetching users:', error);
    // Use standardized error handling, but don't show toast since this might be a background operation
    BaseService.handleError(error, 'UserService.getUsers', false);
    return [];
  }
}

export default getUsers;
