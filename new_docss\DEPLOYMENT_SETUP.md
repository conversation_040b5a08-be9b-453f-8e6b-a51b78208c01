# 🚀 Deployment Setup Guide

## GitHub Actions Auto-Deploy Configuration

This guide explains how to set up automatic deployment to your VPS using GitHub Actions.

### 📋 Prerequisites

- GitHub repository for your backend code
- VPS server with SSH access
- Node.js and PM2 installed on your server
- Your backend code already deployed manually at least once

### 🔧 GitHub Repository Secrets Setup

Go to your GitHub repository → Settings → Secrets and variables → Actions → New repository secret

Add these 3 secrets:

#### 1. SSH_PRIVATE_KEY
```
Your private SSH key content (typically from ~/.ssh/id_rsa)
```

#### 2. SSH_HOST
```
your-server-ip-or-domain.com
```

#### 3. SSH_USER
```
your-ssh-username (usually 'root' or 'ubuntu')
```

### 🗂️ Server Directory Structure

Ensure your server has this structure:
```
/var/www/deyncare-backend.khanciye.com/
├── src/
├── package.json
├── ecosystem.config.js
└── ... (your backend files)
```

### 🔑 SSH Key Generation (if needed)

If you don't have SSH keys:

1. **On your local machine:**
```bash
ssh-keygen -t rsa -b 4096 -C "<EMAIL>"
```

2. **Copy public key to server:**
```bash
ssh-copy-id your-user@your-server-ip
```

3. **Copy private key content:**
```bash
cat ~/.ssh/id_rsa
```
(Paste this entire content into SSH_PRIVATE_KEY secret)

### 📋 Server Setup Checklist

On your VPS server:

1. **Install Node.js & PM2:**
```bash
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt install -y nodejs
sudo npm install -g pm2
```

2. **Clone your repository:**
```bash
cd /var/www/
git clone https://github.com/yourusername/deyncare-backend.git deyncare-backend.khanciye.com
cd deyncare-backend.khanciye.com
```

3. **Install dependencies:**
```bash
npm install --production
```

4. **Start with PM2:**
```bash
pm2 start ecosystem.config.js
pm2 save
pm2 startup
```

### 🚀 How Auto-Deploy Works

1. **Trigger:** Push to `main` branch
2. **Actions:** 
   - Connects to your VPS via SSH
   - Pulls latest code from GitHub
   - Installs/updates dependencies
   - Restarts PM2 processes

### 🔍 Monitoring Deployments

- **GitHub:** Go to Actions tab to see deployment status
- **Server:** Check PM2 logs: `pm2 logs`
- **Server:** Check process status: `pm2 status`

### 🛠️ Troubleshooting

**Common Issues:**

1. **SSH Permission Denied:**
   - Check SSH_PRIVATE_KEY format (include `-----BEGIN/END-----` lines)
   - Verify SSH_USER and SSH_HOST are correct

2. **Directory Not Found:**
   - Ensure `/var/www/deyncare-backend.khanciye.com` exists
   - Check path permissions

3. **PM2 Restart Failed:**
   - Run `pm2 status` on server
   - Check `pm2 logs` for errors

4. **Git Pull Failed:**
   - Ensure server has git access to your repository
   - Check if repository is public or setup deploy keys

### 🎯 Next Steps

1. Set up the 3 GitHub secrets
2. Push to main branch
3. Watch the deployment in GitHub Actions
4. Verify your app is running: `pm2 status`

---

**🎉 You're all set! Every push to main will now automatically deploy to your VPS.** 