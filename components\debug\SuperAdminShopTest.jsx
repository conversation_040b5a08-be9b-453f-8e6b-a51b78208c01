"use client";

import { useState } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { toast } from 'sonner';
import ShopService from '@/lib/services/shop';
import PlanService from '@/lib/services/plan';

/**
 * Debug component to test SuperAdmin shop creation workflow
 * This component helps identify and fix issues with the shop registration process
 */
export default function SuperAdminShopTest() {
  const [testResults, setTestResults] = useState({});
  const [isRunning, setIsRunning] = useState(false);

  const runTest = async (testName, testFn) => {
    console.log(`🧪 Running test: ${testName}`);
    setTestResults(prev => ({ ...prev, [testName]: { status: 'running', message: 'Running...' } }));
    
    try {
      const result = await testFn();
      console.log(`✅ Test passed: ${testName}`, result);
      setTestResults(prev => ({ 
        ...prev, 
        [testName]: { 
          status: 'success', 
          message: result.message || 'Test passed',
          data: result.data 
        } 
      }));
      return true;
    } catch (error) {
      console.error(`❌ Test failed: ${testName}`, error);
      setTestResults(prev => ({ 
        ...prev, 
        [testName]: { 
          status: 'error', 
          message: error.message || 'Test failed',
          error: error 
        } 
      }));
      return false;
    }
  };

  const testPlanAvailability = async () => {
    const response = await PlanService.getPlans({ includeInactive: false });
    
    if (!response.success) {
      throw new Error(`Failed to fetch plans: ${response.error}`);
    }
    
    const plans = response.data || [];
    if (plans.length === 0) {
      throw new Error('No active plans available');
    }
    
    return {
      message: `Found ${plans.length} active plans`,
      data: plans.map(p => ({
        planId: p.planId,
        name: p.name,
        type: p.type,
        displayName: p.displayName,
        isActive: p.isActive
      }))
    };
  };

  const testPlanInitialization = async () => {
    const response = await PlanService.initializeDefaultPlans();
    
    if (!response.success) {
      throw new Error(`Failed to initialize plans: ${response.error}`);
    }
    
    return {
      message: response.message || 'Plans initialized successfully',
      data: response.data
    };
  };

  const testShopCreation = async () => {
    // Get available plans first
    const plansResponse = await PlanService.getPlans({ includeInactive: false });
    if (!plansResponse.success || !plansResponse.data || plansResponse.data.length === 0) {
      throw new Error('No plans available for shop creation');
    }

    const testPlan = plansResponse.data[0];
    const testShopData = {
      fullName: 'Test User',
      email: `test-${Date.now()}@example.com`,
      phone: '+252612345678',
      shopName: `Test Shop ${Date.now()}`,
      shopAddress: 'Test Address, Mogadishu, Somalia',
      businessDetails: {
        type: 'retail',
        category: 'general_store'
        // Note: No customCategory field - this should work fine
      },
      planType: testPlan.planId || testPlan._id
    };

    console.log('🧪 Testing shop creation with data:', testShopData);

    const result = await ShopService.createShop(testShopData);

    if (!result || (!result.shopId && !result.id)) {
      throw new Error('Shop creation returned invalid result');
    }

    return {
      message: `Shop created successfully with ID: ${result.shopId || result.id}`,
      data: result
    };
  };

  const testShopCreationWithCustomCategory = async () => {
    // Get available plans first
    const plansResponse = await PlanService.getPlans({ includeInactive: false });
    if (!plansResponse.success || !plansResponse.data || plansResponse.data.length === 0) {
      throw new Error('No plans available for shop creation');
    }

    const testPlan = plansResponse.data[0];
    const testShopData = {
      fullName: 'Test User Custom',
      email: `test-custom-${Date.now()}@example.com`,
      phone: '+252612345679',
      shopName: `Test Custom Shop ${Date.now()}`,
      shopAddress: 'Test Custom Address, Mogadishu, Somalia',
      businessDetails: {
        type: 'retail',
        category: 'others',
        customCategory: 'Custom Business Type'
      },
      planType: testPlan.planId || testPlan._id
    };

    console.log('🧪 Testing shop creation with custom category:', testShopData);

    const result = await ShopService.createShop(testShopData);

    if (!result || (!result.shopId && !result.id)) {
      throw new Error('Shop creation with custom category returned invalid result');
    }

    return {
      message: `Shop with custom category created successfully with ID: ${result.shopId || result.id}`,
      data: result
    };
  };

  const runAllTests = async () => {
    setIsRunning(true);
    setTestResults({});
    
    try {
      // Test 1: Check plan availability
      await runTest('Plan Availability', testPlanAvailability);
      
      // Test 2: Initialize plans if needed
      await runTest('Plan Initialization', testPlanInitialization);
      
      // Test 3: Re-check plan availability after initialization
      await runTest('Plan Availability (After Init)', testPlanAvailability);
      
      // Test 4: Test shop creation
      await runTest('Shop Creation', testShopCreation);

      // Test 5: Test shop creation with custom category
      await runTest('Shop Creation (Custom Category)', testShopCreationWithCustomCategory);

      toast.success('All tests completed! Check results below.');
    } catch (error) {
      toast.error('Test suite failed');
    } finally {
      setIsRunning(false);
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'success': return 'text-green-600';
      case 'error': return 'text-red-600';
      case 'running': return 'text-blue-600';
      default: return 'text-gray-600';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'success': return '✅';
      case 'error': return '❌';
      case 'running': return '🔄';
      default: return '⏳';
    }
  };

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle>SuperAdmin Shop Creation Test Suite</CardTitle>
        <p className="text-sm text-muted-foreground">
          This tool tests the complete SuperAdmin shop registration workflow to identify and fix issues.
        </p>
      </CardHeader>
      <CardContent className="space-y-4">
        <Button 
          onClick={runAllTests} 
          disabled={isRunning}
          className="w-full"
        >
          {isRunning ? 'Running Tests...' : 'Run All Tests'}
        </Button>
        
        <div className="space-y-3">
          {Object.entries(testResults).map(([testName, result]) => (
            <div key={testName} className="border rounded-lg p-4">
              <div className="flex items-center justify-between mb-2">
                <h3 className="font-medium">{testName}</h3>
                <span className={`text-sm ${getStatusColor(result.status)}`}>
                  {getStatusIcon(result.status)} {result.status}
                </span>
              </div>
              <p className="text-sm text-muted-foreground mb-2">{result.message}</p>
              {result.data && (
                <details className="text-xs">
                  <summary className="cursor-pointer text-blue-600">View Data</summary>
                  <pre className="mt-2 p-2 bg-gray-100 rounded overflow-auto">
                    {JSON.stringify(result.data, null, 2)}
                  </pre>
                </details>
              )}
              {result.error && (
                <details className="text-xs">
                  <summary className="cursor-pointer text-red-600">View Error</summary>
                  <pre className="mt-2 p-2 bg-red-50 rounded overflow-auto">
                    {JSON.stringify(result.error, null, 2)}
                  </pre>
                </details>
              )}
            </div>
          ))}
        </div>
        
        {Object.keys(testResults).length === 0 && !isRunning && (
          <div className="text-center text-muted-foreground py-8">
            Click "Run All Tests" to start testing the SuperAdmin shop creation workflow.
          </div>
        )}
      </CardContent>
    </Card>
  );
}
