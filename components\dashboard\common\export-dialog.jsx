"use client";

import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/auth-context';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { toast } from 'sonner';
import { CalendarIcon, Download, FileText, Package, Users, Store, CreditCard, TrendingUp } from 'lucide-react';
import { format } from 'date-fns';
import { cn } from '@/lib/utils';
import ExportService from '@/lib/services/export/exportService';

// Form validation schema
const exportFormSchema = z.object({
  exportType: z.string().min(1, 'Please select an export type'),
  format: z.enum(['csv', 'excel']),
  filename: z.string().optional(),
  filters: z.record(z.any()).optional()
});

const iconMap = {
  Users,
  Store,
  Package,
  CreditCard,
  FileText,
  TrendingUp
};

/**
 * Export Dialog Component
 * Universal dialog for all export operations
 */
export function ExportDialog({ 
  open, 
  onOpenChange, 
  exportType, 
  title = "Export Data", 
  description = "Export data to CSV or Excel format" 
}) {
  const { user } = useAuth();
  const [isExporting, setIsExporting] = useState(false);
  const [availableExports, setAvailableExports] = useState([]);
  const [selectedExport, setSelectedExport] = useState(null);
  const [exportFilters, setExportFilters] = useState([]);
  
  // Set up form
  const form = useForm({
    resolver: zodResolver(exportFormSchema),
    defaultValues: {
      exportType: exportType || '',
      format: 'csv',
      filename: '',
      filters: {}
    },
  });

  // Load available exports based on user role
  useEffect(() => {
    if (user?.role) {
      const exports = ExportService.getAvailableExports(user.role);
      setAvailableExports(exports);
      
      // Auto-select export type if provided
      if (exportType) {
        const exportConfig = exports.find(e => e.id === exportType);
        if (exportConfig) {
          setSelectedExport(exportConfig);
          const filters = ExportService.getExportFilters(exportType);
          setExportFilters(filters);
          form.setValue('exportType', exportType);
        }
      }
    }
  }, [user, exportType, form]);

  // Reset form when dialog opens/closes
  useEffect(() => {
    if (open) {
      // Prevent body scroll when dialog is open to avoid stacking issues
      document.body.style.overflow = 'hidden';
      
      // Reset form when dialog opens
      form.reset({
        exportType: exportType || '',
        format: 'csv',
        filename: '',
        filters: {}
      });
      
      // If exportType is provided, auto-select it
      if (exportType && availableExports.length > 0) {
        const exportConfig = availableExports.find(e => e.id === exportType);
        if (exportConfig) {
          setSelectedExport(exportConfig);
          const filters = ExportService.getExportFilters(exportType);
          setExportFilters(filters);
          form.setValue('exportType', exportType);
        }
      }
    } else {
      // Reset state when dialog closes
      document.body.style.overflow = 'unset';
      setSelectedExport(null);
      setExportFilters([]);
      setIsExporting(false);
      form.reset();
    }
    
    // Cleanup function
    return () => {
      if (!open) {
        document.body.style.overflow = 'unset';
      }
    };
  }, [open, exportType, availableExports, form]);

  // Handle export type selection
  const handleExportTypeChange = (newExportType) => {
    const exportConfig = availableExports.find(e => e.id === newExportType);
    setSelectedExport(exportConfig);
    
    const filters = ExportService.getExportFilters(newExportType);
    setExportFilters(filters);
    
    // Reset form filters
    form.setValue('filters', {});
  };

  // Handle form submission
  const onSubmit = async (data) => {
    if (!selectedExport) return;

    setIsExporting(true);
    
    try {
      const { exportType: formExportType, format, filename, filters } = data;
      
      // Prepare export parameters
      const exportParams = {
        ...filters,
        filename: filename || undefined
      };
      
      // Call the appropriate export method
      const methodName = selectedExport.method;
      if (ExportService[methodName]) {
        await ExportService[methodName](exportParams, format);
        toast.success(`${selectedExport.name} exported successfully`);
        onOpenChange(false);
      } else {
        throw new Error(`Export method ${methodName} not found`);
      }
    } catch (error) {
      console.error('Export error:', error);
      toast.error(`Failed to export ${selectedExport.name}: ${error.message}`);
    } finally {
      setIsExporting(false);
    }
  };

  // Render filter field
  const renderFilterField = (filter, index) => {
    const fieldName = `filters.${filter.key}`;
    
    switch (filter.type) {
      case 'select':
        return (
          <FormField
            key={index}
            control={form.control}
            name={fieldName}
            render={({ field }) => (
              <FormItem>
                <FormLabel>{filter.label}</FormLabel>
                <Select onValueChange={field.onChange} defaultValue={field.value}>
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder={`Select ${filter.label}`} />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent className="z-[9999]">
                    {filter.options.map((option) => (
                      <SelectItem key={option} value={option}>
                        {option}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />
        );
      
      case 'boolean':
        return (
          <FormField
            key={index}
            control={form.control}
            name={fieldName}
            render={({ field }) => (
              <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                <FormControl>
                  <Checkbox
                    checked={field.value}
                    onCheckedChange={field.onChange}
                  />
                </FormControl>
                <div className="space-y-1 leading-none">
                  <FormLabel>{filter.label}</FormLabel>
                </div>
              </FormItem>
            )}
          />
        );
      
      case 'date':
        return (
          <FormField
            key={index}
            control={form.control}
            name={fieldName}
            render={({ field }) => (
              <FormItem className="flex flex-col">
                <FormLabel>{filter.label}</FormLabel>
                <Popover modal={true}>
                  <PopoverTrigger asChild>
                    <FormControl>
                      <Button
                        variant="outline"
                        className={cn(
                          "w-full pl-3 text-left font-normal",
                          !field.value && "text-muted-foreground"
                        )}
                      >
                        {field.value ? (
                          format(new Date(field.value), "PPP")
                        ) : (
                          <span>Pick a date</span>
                        )}
                        <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                      </Button>
                    </FormControl>
                  </PopoverTrigger>
                  <PopoverContent 
                    className="w-auto p-0 z-[9999]" 
                    align="start"
                    side="bottom"
                    sideOffset={5}
                    avoidCollisions={true}
                  >
                    <Calendar
                      mode="single"
                      selected={field.value ? new Date(field.value) : undefined}
                      onSelect={(date) => field.onChange(date?.toISOString())}
                      disabled={(date) =>
                        date > new Date() || date < new Date("1900-01-01")
                      }
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
                <FormMessage />
              </FormItem>
            )}
          />
        );
      
      case 'number':
        return (
          <FormField
            key={index}
            control={form.control}
            name={fieldName}
            render={({ field }) => (
              <FormItem>
                <FormLabel>{filter.label}</FormLabel>
                <FormControl>
                  <Input
                    type="number"
                    placeholder={filter.label}
                    min={filter.min}
                    max={filter.max}
                    {...field}
                    onChange={(e) => field.onChange(parseFloat(e.target.value) || '')}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        );
      
      default:
        return (
          <FormField
            key={index}
            control={form.control}
            name={fieldName}
            render={({ field }) => (
              <FormItem>
                <FormLabel>{filter.label}</FormLabel>
                <FormControl>
                  <Input placeholder={filter.label} {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        );
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange} modal={true}>
      <DialogContent className="dialog-content max-w-4xl z-[9998]">
        <DialogHeader className="dialog-header">
          <DialogTitle>{title}</DialogTitle>
          <DialogDescription>
            {description}
          </DialogDescription>
        </DialogHeader>

        <div className="dialog-body">
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            {/* Export Type Selection */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Select Export Type</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {availableExports.map((exportConfig) => {
                  const Icon = iconMap[exportConfig.icon] || FileText;
                  return (
                    <div
                      key={exportConfig.id}
                      className={cn(
                        "p-4 border rounded-lg cursor-pointer transition-all hover:shadow-md",
                        selectedExport?.id === exportConfig.id
                          ? "border-primary bg-primary/5"
                          : "border-border hover:border-primary/50"
                      )}
                      onClick={() => {
                        form.setValue('exportType', exportConfig.id);
                        handleExportTypeChange(exportConfig.id);
                      }}
                    >
                      <div className="flex items-start space-x-3">
                        <Icon className="h-5 w-5 text-primary mt-0.5" />
                        <div className="flex-1">
                          <h4 className="font-medium">{exportConfig.name}</h4>
                          <p className="text-sm text-muted-foreground mt-1">
                            {exportConfig.description}
                          </p>
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>

            {/* Export Format */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <FormField
                control={form.control}
                name="format"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Export Format</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select format" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent className="z-[9999]">
                        <SelectItem value="csv">CSV</SelectItem>
                        <SelectItem value="excel">Excel</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="filename"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Custom Filename (Optional)</FormLabel>
                    <FormControl>
                      <Input placeholder="Leave empty for auto-generated name" {...field} />
                    </FormControl>
                    <FormDescription>
                      Without file extension
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Export Filters */}
            {selectedExport && exportFilters.length > 0 && (
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Filter Options</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {exportFilters.map(renderFilterField)}
                </div>
              </div>
            )}

            <DialogFooter>
              <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
                Cancel
              </Button>
              <Button 
                type="submit" 
                disabled={isExporting || !selectedExport}
              >
                {isExporting ? (
                  <>
                    <Download className="h-4 w-4 mr-2 animate-spin" />
                    Exporting...
                  </>
                ) : (
                  <>
                    <Download className="h-4 w-4 mr-2" />
                    Export {selectedExport?.name}
                  </>
                )}
              </Button>
            </DialogFooter>
            </form>
          </Form>
        </div>
      </DialogContent>
    </Dialog>
  );
}

export default ExportDialog; 