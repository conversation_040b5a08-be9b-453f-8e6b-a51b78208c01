# DeynCare Mobile App Subscription Integration Guide

## Overview

This document provides comprehensive guidance for integrating the Flutter mobile application with DeynCare's subscription-based access control system. The backend restricts access to features when subscriptions expire, ensuring only active subscribers can use the full application.

## Table of Contents

1. [Architecture Overview](#architecture-overview)
2. [Backend API Endpoints](#backend-api-endpoints)
3. [Flutter Integration Guide](#flutter-integration-guide)
4. [Access Control Logic](#access-control-logic)
5. [User Experience Flow](#user-experience-flow)
6. [Error Handling](#error-handling)
7. [Testing Guide](#testing-guide)

## Architecture Overview

### Core Components

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Flutter App   │    │   Backend API   │    │   Database      │
│                 │    │                 │    │                 │
│ • User Login    │───▶│ • Auth Check    │───▶│ • Subscription  │
│ • Feature Use   │    │ • Subscription  │    │ • User Data     │
│ • Payment UI    │    │   Middleware    │    │ • Shop Data     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### Subscription States

| State | Description | Access Level | Action Required |
|-------|-------------|--------------|-----------------|
| `active` | Valid subscription | Full Access | None |
| `trial` | Trial period | Full Access | Upgrade before expiry |
| `expired` | Subscription expired | Restricted | Renew subscription |
| `grace_period` | Grace period active | Limited Access | Renew before final expiry |
| `canceled` | User canceled | Restricted | Reactivate |
| `none` | No subscription | Restricted | Subscribe |

## Backend API Endpoints

### 1. Check Subscription Status

**Endpoint:** `GET /api/subscription-status/check/current`

**Headers:**
```http
Authorization: Bearer <jwt_token>
Content-Type: application/json
```

**Response (Active Subscription):**
```json
{
  "success": true,
  "hasSubscription": true,
  "status": "active",
  "accessLevel": "full",
  "subscription": {
    "id": "64a7b8c9d1e2f3a4b5c6d7e8",
    "planType": "monthly",
    "startDate": "2024-01-01T00:00:00.000Z",
    "endDate": "2024-02-01T00:00:00.000Z",
    "daysRemaining": 15,
    "autoRenew": true,
    "isExpired": false
  },
  "actionRequired": null,
  "paymentOptions": {
    "renewUrl": "https://app.deyncare.com/subscription/renew",
    "evcPaymentUrl": "https://app.deyncare.com/subscription/renew?method=evc",
    "plansUrl": "https://app.deyncare.com/subscription/plans"
  },
  "restrictedFeatures": []
}
```

**Response (Expired Subscription):**
```json
{
  "success": true,
  "hasSubscription": true,
  "status": "expired",
  "accessLevel": "restricted",
  "subscription": {
    "id": "64a7b8c9d1e2f3a4b5c6d7e8",
    "planType": "monthly",
    "startDate": "2024-01-01T00:00:00.000Z",
    "endDate": "2024-02-01T00:00:00.000Z",
    "daysRemaining": 0,
    "autoRenew": false,
    "isExpired": true
  },
  "actionRequired": "renew",
  "paymentOptions": {
    "renewUrl": "https://app.deyncare.com/subscription/renew",
    "evcPaymentUrl": "https://app.deyncare.com/subscription/renew?method=evc",
    "plansUrl": "https://app.deyncare.com/subscription/plans"
  },
  "restrictedFeatures": [
    "customer_management",
    "debt_tracking", 
    "payment_processing",
    "risk_assessment",
    "reporting",
    "data_export"
  ]
}
```

### 2. Protected API Endpoints

All feature-related endpoints automatically check subscription status via middleware.

**Restricted Response Example:**
```json
{
  "success": false,
  "error": "Your subscription has expired. Please renew to continue using DeynCare.",
  "code": "SUBSCRIPTION_EXPIRED",
  "restrictedAccess": true,
  "subscriptionStatus": "expired",
  "subscriptionDetails": {
    "planType": "monthly",
    "expiredOn": "2024-02-01T00:00:00.000Z",
    "daysExpired": 5
  },
  "actionRequired": "renew",
  "showRenewalPopup": true,
  "popupConfig": {
    "title": "Subscription Expired",
    "message": "Your monthly plan expired 5 days ago",
    "urgent": false,
    "primaryAction": "renew_now",
    "secondaryAction": "view_plans"
  },
  "renewalEndpoint": "/api/subscriptions/renew",
  "paymentMethodsEndpoint": "/api/subscriptions/payment-methods",
  "restrictedFeatures": [
    "customer_management",
    "debt_tracking",
    "payment_processing",
    "risk_assessment", 
    "reporting",
    "data_export"
  ]
}
```

### 3. Get Available Payment Methods

**Endpoint:** `GET /api/subscriptions/payment-methods`

**Headers:**
```http
Authorization: Bearer <jwt_token>
Content-Type: application/json
```

**Response:**
```json
{
  "success": true,
  "message": "Available payment methods retrieved",
  "data": {
    "paymentMethods": ["EVC Plus", "Cash", "Bank Transfer"],
    "onlineEnabled": true,
    "offlineEnabled": true,
    "preferredMethod": "EVC Plus"
  }
}
```

### 4. Process Subscription Renewal

**Endpoint:** `POST /api/subscriptions/renew`

**Headers:**
```http
Authorization: Bearer <jwt_token>
Content-Type: application/json
```

**Request Body (EVC Plus):**
```json
{
  "planType": "monthly",
  "paymentMethod": "EVC Plus",
  "phoneNumber": "************"
}
```

**Request Body (Offline Payment):**
```json
{
  "planType": "monthly", 
  "paymentMethod": "Cash"
}
```

**Response (Successful EVC Payment):**
```json
{
  "success": true,
  "message": "Subscription renewed successfully",
  "data": {
    "paymentId": "PAY_123456789",
    "subscriptionId": "SUB_987654321",
    "planType": "monthly",
    "amount": 10,
    "currency": "USD",
    "paymentMethod": "EVC Plus",
    "status": "completed",
    "accessRestored": true,
    "newEndDate": "2024-03-01T00:00:00.000Z"
  }
}
```

**Response (Offline Payment):**
```json
{
  "success": true,
  "message": "Renewal payment submitted for approval",
  "data": {
    "paymentId": "PAY_123456789",
    "subscriptionId": "SUB_987654321",
    "planType": "monthly",
    "amount": 10,
    "currency": "USD",
    "paymentMethod": "Cash",
    "status": "pending_approval",
    "accessRestored": false
  }
}
```

### 5. Check Renewal Payment Status

**Endpoint:** `GET /api/subscriptions/renewal/:paymentId/status`

**Response:**
```json
{
  "success": true,
  "message": "Payment status retrieved",
  "data": {
    "paymentId": "PAY_123456789",
    "status": "confirmed",
    "amount": 10,
    "currency": "USD",
    "method": "evc_plus",
    "createdAt": "2024-01-15T10:30:00.000Z",
    "subscriptionId": "SUB_987654321",
    "accessRestored": true
  }
}
```

## Flutter Integration Guide

### 1. Subscription Service

Create a subscription service in your Flutter app:

```dart
class SubscriptionService {
  static const String baseUrl = 'YOUR_API_BASE_URL';
  
  // Check current subscription status
  static Future<SubscriptionStatus> checkSubscriptionStatus() async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/api/subscription-status/check/current'),
        headers: {
          'Authorization': 'Bearer ${await AuthService.getToken()}',
          'Content-Type': 'application/json',
        },
      );
      
      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return SubscriptionStatus.fromJson(data);
      } else if (response.statusCode == 403) {
        final data = json.decode(response.body);
        return SubscriptionStatus.fromRestrictedResponse(data);
      } else {
        throw Exception('Failed to check subscription status');
      }
    } catch (e) {
      throw Exception('Subscription check failed: $e');
    }
  }
  
  // Check if feature is accessible
  static Future<bool> isFeatureAccessible(String featureName) async {
    final status = await checkSubscriptionStatus();
    return !status.restrictedFeatures.contains(featureName);
  }
}
```

### 2. Subscription Status Model

```dart
class SubscriptionStatus {
  final bool hasSubscription;
  final String status;
  final String accessLevel;
  final Subscription? subscription;
  final String? actionRequired;
  final PaymentOptions paymentOptions;
  final List<String> restrictedFeatures;
  
  SubscriptionStatus({
    required this.hasSubscription,
    required this.status,
    required this.accessLevel,
    this.subscription,
    this.actionRequired,
    required this.paymentOptions,
    required this.restrictedFeatures,
  });
  
  factory SubscriptionStatus.fromJson(Map<String, dynamic> json) {
    return SubscriptionStatus(
      hasSubscription: json['hasSubscription'] ?? false,
      status: json['status'] ?? 'none',
      accessLevel: json['accessLevel'] ?? 'restricted',
      subscription: json['subscription'] != null 
        ? Subscription.fromJson(json['subscription']) 
        : null,
      actionRequired: json['actionRequired'],
      paymentOptions: PaymentOptions.fromJson(json['paymentOptions']),
      restrictedFeatures: List<String>.from(json['restrictedFeatures'] ?? []),
    );
  }
  
  bool get isActive => accessLevel == 'full' && status == 'active';
  bool get isRestricted => accessLevel == 'restricted';
  bool get needsRenewal => actionRequired == 'renew';
  bool get needsSubscription => actionRequired == 'subscribe';
}
```

### 3. Feature Access Widget

Create a widget that wraps restricted features:

```dart
class FeatureAccessWidget extends StatelessWidget {
  final String featureName;
  final Widget child;
  final Widget? restrictedWidget;
  
  const FeatureAccessWidget({
    Key? key,
    required this.featureName,
    required this.child,
    this.restrictedWidget,
  }) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    return FutureBuilder<bool>(
      future: SubscriptionService.isFeatureAccessible(featureName),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const CircularProgressIndicator();
        }
        
        if (snapshot.hasError) {
          return ErrorWidget(snapshot.error!);
        }
        
        if (snapshot.data == true) {
          return child;
        } else {
          return restrictedWidget ?? 
            const SubscriptionExpiredWidget();
        }
      },
    );
  }
}
```

### 4. Subscription Expired Widget

```dart
class SubscriptionExpiredWidget extends StatelessWidget {
  const SubscriptionExpiredWidget({Key? key}) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.lock_outline,
            size: 64,
            color: Colors.red[300],
          ),
          const SizedBox(height: 16),
          Text(
            'Subscription Required',
            style: Theme.of(context).textTheme.headlineSmall,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          Text(
            'Your subscription has expired. Renew now to continue using this feature.',
            style: Theme.of(context).textTheme.bodyMedium,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 20),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              ElevatedButton.icon(
                onPressed: () => _openEVCPayment(context),
                icon: const Icon(Icons.phone_android),
                label: const Text('Pay with EVC'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.green,
                ),
              ),
              ElevatedButton.icon(
                onPressed: () => _openWebPayment(context),
                icon: const Icon(Icons.credit_card),
                label: const Text('Other Methods'),
              ),
            ],
          ),
        ],
      ),
    );
  }
  
  void _openEVCPayment(BuildContext context) {
    // Implement EVC payment flow
    PaymentService.openEVCPayment();
  }
  
  void _openWebPayment(BuildContext context) {
    // Open web-based payment
    PaymentService.openWebPayment();
  }
}
```

## Access Control Logic

### 1. Route Guards

Implement route guards to check subscription status:

```dart
class SubscriptionGuard extends RouteGuard {
  @override
  Future<bool> canActivate(
    ExecutionContext context,
    Route route,
  ) async {
    try {
      final status = await SubscriptionService.checkSubscriptionStatus();
      
      // Allow access to payment and subscription pages
      if (route.name?.contains('payment') == true ||
          route.name?.contains('subscription') == true) {
        return true;
      }
      
      // Check if route requires active subscription
      if (route.settings.arguments != null) {
        final args = route.settings.arguments as Map<String, dynamic>;
        final requiresSubscription = args['requiresSubscription'] ?? false;
        
        if (requiresSubscription && !status.isActive) {
          // Redirect to subscription page
          NavigationService.pushReplacementNamed('/subscription-expired');
          return false;
        }
      }
      
      return true;
    } catch (e) {
      // On error, allow access but log issue
      print('Subscription check failed: $e');
      return true;
    }
  }
}
```

### 2. Feature-Level Restrictions

```dart
class CustomerManagementPage extends StatefulWidget {
  @override
  Widget build(BuildContext context) {
    return FeatureAccessWidget(
      featureName: 'customer_management',
      child: Scaffold(
        appBar: AppBar(title: Text('Customers')),
        body: CustomerList(),
        floatingActionButton: FloatingActionButton(
          onPressed: () => Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => FeatureAccessWidget(
                featureName: 'customer_management',
                child: AddCustomerPage(),
              ),
            ),
          ),
          child: Icon(Icons.add),
        ),
      ),
    );
  }
}
```

## User Experience Flow

### 1. App Launch Flow

```mermaid
graph TD
    A[App Launch] --> B[Check Authentication]
    B -->|Authenticated| C[Check Subscription Status]
    B -->|Not Authenticated| D[Login Screen]
    C -->|Active| E[Full App Access]
    C -->|Expired| F[Show Expiry Notice]
    C -->|Grace Period| G[Show Grace Period Warning]
    F --> H[Payment Options]
    G --> I[Continue with Warnings]
    H --> J[EVC Payment]
    H --> K[Web Payment]
    D --> L[Login Success]
    L --> C
```

### 2. Feature Access Flow

```mermaid
graph TD
    A[User Taps Feature] --> B[Check Feature Access]
    B -->|Allowed| C[Show Feature]
    B -->|Restricted| D[Show Restriction Notice]
    D --> E[Offer Payment Options]
    E --> F[EVC Payment]
    E --> G[Web Payment]
    F --> H[Payment Success]
    G --> H
    H --> I[Refresh Subscription]
    I --> C
```

## Error Handling

### 1. Network Errors

```dart
class SubscriptionErrorHandler {
  static void handleSubscriptionError(dynamic error) {
    if (error is SocketException) {
      // Network connectivity issue
      showDialog(
        context: navigatorKey.currentContext!,
        builder: (context) => AlertDialog(
          title: Text('Connection Error'),
          content: Text('Please check your internet connection and try again.'),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: Text('OK'),
            ),
          ],
        ),
      );
    } else if (error is TimeoutException) {
      // Request timeout
      _showRetryDialog('Request timed out. Please try again.');
    } else {
      // Generic error
      _showRetryDialog('An error occurred. Please try again.');
    }
  }
  
  static void _showRetryDialog(String message) {
    showDialog(
      context: navigatorKey.currentContext!,
      builder: (context) => AlertDialog(
        title: Text('Error'),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              // Retry the operation
              SubscriptionService.checkSubscriptionStatus();
            },
            child: Text('Retry'),
          ),
        ],
      ),
    );
  }
}
```

### 2. API Error Responses

```dart
class ApiErrorHandler {
  static void handleApiError(int statusCode, Map<String, dynamic> response) {
    switch (statusCode) {
      case 403:
        if (response['code'] == 'SUBSCRIPTION_EXPIRED') {
          _handleSubscriptionExpired(response);
        } else if (response['code'] == 'NO_SUBSCRIPTION') {
          _handleNoSubscription(response);
        }
        break;
      case 401:
        _handleUnauthorized();
        break;
      case 500:
        _handleServerError();
        break;
      default:
        _handleGenericError(response['error'] ?? 'Unknown error');
    }
  }
  
  static void _handleSubscriptionExpired(Map<String, dynamic> response) {
    final subscriptionDetails = response['subscriptionDetails'];
    final daysExpired = subscriptionDetails['daysExpired'];
    
    showDialog(
      context: navigatorKey.currentContext!,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: Text('Subscription Expired'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Your subscription expired $daysExpired days ago.'),
            SizedBox(height: 16),
            Text('Renew now to continue using all features.'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('Later'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              PaymentService.openEVCPayment();
            },
            child: Text('Renew Now'),
          ),
        ],
      ),
    );
  }
}
```

## Testing Guide

### 1. Testing Subscription States

```dart
void main() {
  group('Subscription Service Tests', () {
    testWidgets('Active subscription allows feature access', (tester) async {
      // Mock active subscription response
      when(mockHttpClient.get(any, headers: anyNamed('headers')))
        .thenAnswer((_) async => http.Response(
          json.encode({
            'success': true,
            'accessLevel': 'full',
            'status': 'active',
            'restrictedFeatures': []
          }),
          200,
        ));
      
      final isAccessible = await SubscriptionService.isFeatureAccessible('customer_management');
      expect(isAccessible, true);
    });
    
    testWidgets('Expired subscription restricts feature access', (tester) async {
      // Mock expired subscription response
      when(mockHttpClient.get(any, headers: anyNamed('headers')))
        .thenAnswer((_) async => http.Response(
          json.encode({
            'success': false,
            'code': 'SUBSCRIPTION_EXPIRED',
            'restrictedFeatures': ['customer_management']
          }),
          403,
        ));
      
      final isAccessible = await SubscriptionService.isFeatureAccessible('customer_management');
      expect(isAccessible, false);
    });
  });
}
```

### 2. Widget Testing

```dart
testWidgets('FeatureAccessWidget shows restriction for expired subscription', (tester) async {
  // Mock expired subscription
  when(mockSubscriptionService.isFeatureAccessible('customer_management'))
    .thenAnswer((_) async => false);
  
  await tester.pumpWidget(
    MaterialApp(
      home: FeatureAccessWidget(
        featureName: 'customer_management',
        child: Text('Feature Content'),
      ),
    ),
  );
  
  await tester.pumpAndSettle();
  
  expect(find.text('Subscription Required'), findsOneWidget);
  expect(find.text('Feature Content'), findsNothing);
});
```

## Best Practices

### 1. Caching Strategy

```dart
class SubscriptionCache {
  static SubscriptionStatus? _cachedStatus;
  static DateTime? _lastFetch;
  static const Duration _cacheTimeout = Duration(minutes: 5);
  
  static Future<SubscriptionStatus> getSubscriptionStatus() async {
    final now = DateTime.now();
    
    if (_cachedStatus != null && 
        _lastFetch != null &&
        now.difference(_lastFetch!) < _cacheTimeout) {
      return _cachedStatus!;
    }
    
    _cachedStatus = await SubscriptionService.checkSubscriptionStatus();
    _lastFetch = now;
    
    return _cachedStatus!;
  }
  
  static void clearCache() {
    _cachedStatus = null;
    _lastFetch = null;
  }
}
```

### 2. Offline Handling

```dart
class OfflineSubscriptionHandler {
  static const String _cacheKey = 'last_subscription_status';
  
  static Future<SubscriptionStatus?> getOfflineStatus() async {
    final prefs = await SharedPreferences.getInstance();
    final cached = prefs.getString(_cacheKey);
    
    if (cached != null) {
      final data = json.decode(cached);
      return SubscriptionStatus.fromJson(data);
    }
    
    return null;
  }
  
  static Future<void> cacheStatus(SubscriptionStatus status) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_cacheKey, json.encode(status.toJson()));
  }
}
```

### 3. Performance Optimization

- Cache subscription status for short periods (5 minutes)
- Use lazy loading for feature access checks
- Implement background refresh of subscription status
- Use efficient state management (Provider/Riverpod/Bloc)

## Security Considerations

1. **Token Security**: Always use secure storage for JWT tokens
2. **SSL Pinning**: Implement certificate pinning for API calls
3. **Local Validation**: Never rely solely on client-side checks
4. **Sensitive Data**: Don't store payment information locally
5. **Network Security**: Use HTTPS for all API communications

## Troubleshooting

### Common Issues

1. **Token Expiration**: Implement automatic token refresh
2. **Network Connectivity**: Handle offline scenarios gracefully
3. **Payment Failures**: Provide clear error messages and retry options
4. **Cache Issues**: Implement cache invalidation strategies
5. **State Synchronization**: Ensure UI updates after subscription changes

### Debug Tools

```dart
class SubscriptionDebugger {
  static void logSubscriptionState(SubscriptionStatus status) {
    print('=== Subscription Debug Info ===');
    print('Status: ${status.status}');
    print('Access Level: ${status.accessLevel}');
    print('Action Required: ${status.actionRequired}');
    print('Restricted Features: ${status.restrictedFeatures}');
    print('============================');
  }
}
```

This documentation provides a comprehensive guide for integrating the Flutter mobile application with DeynCare's subscription system, ensuring proper access control and a smooth user experience. 