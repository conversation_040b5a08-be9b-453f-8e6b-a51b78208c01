/**
 * Change Plan Service
 * Updates a subscription's plan with plan integration
 */
const { Subscription, Plan } = require('../../models');
const { AppError, logError, logSuccess } = require('../../utils');
const getSubscriptionById = require('./getSubscriptionById');

/**
 * Change subscription plan
 * @param {string} subscriptionId - ID of the subscription
 * @param {Object} changeData - Plan change details
 * @param {Object} options - Additional options
 * @returns {Promise<Object>} Updated subscription
 */
const changePlan = async (subscriptionId, changeData, options = {}) => {
  try {
    const { planId, planType } = changeData;
    
    // Get the subscription
    const subscription = await getSubscriptionById(subscriptionId);
    
    // Get plan from Plan model
    let plan;
    if (planId) {
      plan = await Plan.findOne({ planId, isDeleted: false });
      if (!plan) {
        throw new AppError('Plan not found', 404, 'plan_not_found');
      }
    } else if (planType) {
      plan = await Plan.findOne({ type: planType, isDefault: true, isDeleted: false });
      if (!plan) {
        throw new AppError(`No default ${planType} plan found`, 404, 'plan_not_found');
      }
    } else {
      throw new AppError('Either planId or planType must be provided', 400, 'validation_error');
    }
    
    // Record current plan details for history
    const previousPlan = {
      planId: subscription.planId,
      type: subscription.plan?.type,
      name: subscription.plan?.name
    };
    
    // Update subscription with new plan details
    subscription.planId = plan.planId;
    subscription.plan.name = plan.name;
    subscription.plan.type = plan.type;
    subscription.plan.features = { ...plan.features };
    subscription.plan.limits = { ...plan.limits };
    subscription.pricing.basePrice = plan.pricing.basePrice;
    subscription.pricing.currency = plan.pricing.currency;
    subscription.pricing.billingCycle = plan.type === 'yearly' ? 'yearly' : 'monthly';
    
    // Update status if needed
    if (plan.type === 'trial' && subscription.status !== 'trial') {
      subscription.status = 'trial';
    } else if (plan.type !== 'trial' && subscription.status === 'trial') {
      subscription.status = 'active';
    }
    
    // Add to history
    subscription.history.push({
      action: 'plan_changed',
      date: new Date(),
      performedBy: options.actorId || 'system',
      details: {
        previousPlan,
        newPlan: {
          planId: plan.planId,
          name: plan.name,
          type: plan.type
        },
        actorRole: options.actorRole || 'system'
      }
    });
    
    // Save changes
    const updatedSubscription = await subscription.save();
    logSuccess(`Changed plan for subscription: ${subscriptionId} to plan: ${plan.name} (${plan.type})`, 'SubscriptionService');
    
    return updatedSubscription;
  } catch (error) {
    logError(`Failed to change plan for subscription: ${subscriptionId}`, 'SubscriptionService', error);
    throw error;
  }
};

module.exports = changePlan;
