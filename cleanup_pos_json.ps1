# PowerShell script to remove POS features from JSON files

# Define files to clean
$files = @(
    "files/Deyncare.subscriptions.json",
    "files/Deyncare.subscriptionss.json"
)

foreach ($file in $files) {
    if (Test-Path $file) {
        Write-Host "Cleaning $file..."
        
        # Read the file content
        $content = Get-Content $file -Raw
        
        # Remove productSalesSystem and posAndReceipt lines
        $content = $content -replace '"productSalesSystem": true,\s*', ''
        $content = $content -replace '"posAndReceipt": true,\s*', ''
        
        # Write the cleaned content back
        Set-Content $file $content
        
        Write-Host "Cleaned $file"
    } else {
        Write-Host "File not found: $file"
    }
}

Write-Host "JSON cleanup completed!" 