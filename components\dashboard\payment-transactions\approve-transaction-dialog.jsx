import React, { useState } from 'react';
import { CheckCircle, AlertTriangle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogFooter, 
  DialogHeader, 
  DialogTitle 
} from '@/components/ui/dialog';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import PaymentTransactionsService from '@/lib/services/payment-transactions';

/**
 * ApproveTransactionDialog Component
 * Dialog for approving payment transactions with optional notes
 * 
 * @param {Object} props - Component props
 * @param {boolean} props.open - Dialog open state
 * @param {Function} props.onOpenChange - Dialog open state change callback
 * @param {Object} props.transaction - Transaction to approve
 * @param {Function} props.onConfirm - Approve confirmation callback
 * @param {boolean} props.loading - Loading state
 * @returns {JSX.Element} Rendered component
 */
const ApproveTransactionDialog = ({
  open = false,
  onOpenChange,
  transaction = null,
  onConfirm,
  loading = false
}) => {
  const [notes, setNotes] = useState('');

  const handleConfirm = () => {
    onConfirm?.(transaction, notes);
    setNotes(''); // Reset notes after confirmation
  };

  const handleClose = () => {
    setNotes(''); // Reset notes when closing
    onOpenChange?.(false);
  };

  if (!transaction) return null;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-green-700">
            <CheckCircle className="h-5 w-5" />
            Approve Payment Transaction
          </DialogTitle>
          <DialogDescription>
            You are about to approve this payment transaction. This action will:
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4 py-4">
          {/* Transaction Details */}
          <div className="bg-green-50 dark:bg-green-900/10 p-4 rounded-lg space-y-3">
            <div className="flex justify-between items-start">
              <span className="text-sm font-medium text-green-800 dark:text-green-200">
                Payment ID
              </span>
              <span className="text-sm text-green-700 dark:text-green-300">
                {transaction.paymentId}
              </span>
            </div>
            
            <div className="flex justify-between items-start">
              <span className="text-sm font-medium text-green-800 dark:text-green-200">
                Shop
              </span>
              <span className="text-sm text-green-700 dark:text-green-300">
                {transaction.shopName}
              </span>
            </div>
            
            <div className="flex justify-between items-start">
              <span className="text-sm font-medium text-green-800 dark:text-green-200">
                Amount
              </span>
              <span className="text-sm font-semibold text-green-700 dark:text-green-300">
                {PaymentTransactionsService.formatCurrency(transaction.amount)}
              </span>
            </div>

            <div className="flex justify-between items-start">
              <span className="text-sm font-medium text-green-800 dark:text-green-200">
                Payment Method
              </span>
              <Badge variant="outline" className="text-green-700 border-green-300">
                {PaymentTransactionsService.formatPaymentMethod(transaction.method)}
              </Badge>
            </div>
          </div>

          {/* Action Effects */}
          <div className="bg-blue-50 dark:bg-blue-900/10 p-4 rounded-lg">
            <div className="flex items-start gap-2">
              <AlertTriangle className="h-4 w-4 text-blue-600 mt-0.5" />
              <div className="text-sm text-blue-800 dark:text-blue-200">
                <p className="font-medium mb-1">This will:</p>
                <ul className="space-y-1 text-xs">
                  <li>• Activate the associated subscription</li>
                  <li>• Send confirmation email to the customer</li>
                  <li>• Update payment status to "Approved"</li>
                  <li>• Log this approval action</li>
                </ul>
              </div>
            </div>
          </div>

          {/* Approval Notes */}
          <div className="space-y-2">
            <Label htmlFor="approval-notes">
              Approval Notes (Optional)
            </Label>
            <Textarea
              id="approval-notes"
              placeholder="Add any notes about this approval..."
              value={notes}
              onChange={(e) => setNotes(e.target.value)}
              rows={3}
              maxLength={500}
            />
            <div className="text-xs text-muted-foreground text-right">
              {notes.length}/500 characters
            </div>
          </div>
        </div>

        <DialogFooter className="flex gap-2">
          <Button
            variant="outline"
            onClick={handleClose}
            disabled={loading}
          >
            Cancel
          </Button>
          <Button
            onClick={handleConfirm}
            disabled={loading}
            className="bg-green-600 hover:bg-green-700 text-white"
          >
            {loading ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent mr-2" />
                Approving...
              </>
            ) : (
              <>
                <CheckCircle className="h-4 w-4 mr-2" />
                Approve Payment
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default ApproveTransactionDialog; 