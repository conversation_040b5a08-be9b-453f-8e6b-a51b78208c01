/**
 * ML Risk Export Controller
 * Handles export requests for ML risk assessment data with debt and payment records (Admin role)
 */
const BaseExportController = require('./baseExportController');
const MLRiskService = require('../../services/mlRiskService');
const RiskScoreService = require('../../services/riskScoreService');
const { Customer, Debt, Payment } = require('../../models');
const { logError } = require('../../utils');

class MLRiskExportController extends BaseExportController {
  /**
   * Get ML risk export fields
   * @param {Object} req - Express request object
   * @returns {Array} Field configurations
   */
  static async getExportFields(req) {
    return [
      {
        label: 'Customer ID',
        key: 'customerId',
        type: 'string'
      },
      {
        label: 'Customer Name',
        key: 'customerName',
        type: 'string'
      },
      {
        label: 'Customer Phone',
        key: 'customerPhone',
        type: 'string'
      },
      {
        label: 'Customer Email',
        key: 'customerEmail',
        type: 'string'
      },
      {
        label: 'Risk Level',
        key: 'riskLevel',
        type: 'string'
      },
      {
        label: 'Risk Score',
        key: 'riskScore',
        type: 'number'
      },
      {
        label: 'ML Confidence',
        key: 'mlConfidence',
        type: 'number'
      },
      {
        label: 'Total Debts',
        key: 'totalDebts',
        type: 'number'
      },
      {
        label: 'Total Debt Amount',
        key: 'totalDebtAmount',
        type: 'currency',
        currency: 'USD'
      },
      {
        label: 'Outstanding Balance',
        key: 'outstandingBalance',
        type: 'currency',
        currency: 'USD'
      },
      {
        label: 'Overdue Debts',
        key: 'overdueDebts',
        type: 'number'
      },
      {
        label: 'Overdue Amount',
        key: 'overdueAmount',
        type: 'currency',
        currency: 'USD'
      },
      {
        label: 'Total Payments',
        key: 'totalPayments',
        type: 'number'
      },
      {
        label: 'Total Paid Amount',
        key: 'totalPaidAmount',
        type: 'currency',
        currency: 'USD'
      },
      {
        label: 'Payment Success Rate',
        key: 'paymentSuccessRate',
        type: 'number'
      },
      {
        label: 'Average Payment Days',
        key: 'averagePaymentDays',
        type: 'number'
      },
      {
        label: 'Last Payment Date',
        key: 'lastPaymentDate',
        type: 'date'
      },
      {
        label: 'Last Payment Amount',
        key: 'lastPaymentAmount',
        type: 'currency',
        currency: 'USD'
      },
      {
        label: 'Risk Factors',
        key: 'riskFactors',
        type: 'string'
      },
      {
        label: 'ML Last Assessment',
        key: 'lastMLAssessment',
        type: 'datetime'
      },
      {
        label: 'Risk Last Updated',
        key: 'riskLastUpdated',
        type: 'datetime'
      }
    ];
  }

  /**
   * Get ML risk data for export
   * @param {Object} req - Express request object
   * @returns {Array} ML risk data
   */
  static async getExportData(req) {
    try {
      const { 
        riskLevel,
        minRiskScore,
        maxRiskScore,
        customerId,
        startDate,
        endDate
      } = req.query;
      
      const { shopId } = req.user; // Admin can only export their shop's data
      
      // Build filters
      const filters = { shopId };
      if (customerId) filters.customerId = customerId;
      if (startDate || endDate) {
        filters.createdAt = {};
        if (startDate) filters.createdAt.$gte = new Date(startDate);
        if (endDate) filters.createdAt.$lte = new Date(endDate);
      }

      // Get customers for this shop
      const customers = await Customer.find(filters).lean();
      
      // Get risk scores for these customers
      const riskScores = await RiskScoreService.getShopRiskScores(shopId, {
        riskLevel,
        minRiskScore: minRiskScore ? parseInt(minRiskScore) : undefined,
        maxRiskScore: maxRiskScore ? parseInt(maxRiskScore) : undefined
      });

      // Get debts and payments for analysis
      const customerIds = customers.map(c => c.customerId);
      const debts = await Debt.find({ 
        shopId, 
        customerId: { $in: customerIds } 
      }).lean();
      
      const payments = await Payment.find({ 
        shopId, 
        customerId: { $in: customerIds } 
      }).lean();

      // Combine data for comprehensive risk export
      const exportData = [];
      
      for (const customer of customers) {
        const customerDebts = debts.filter(d => d.customerId === customer.customerId);
        const customerPayments = payments.filter(p => p.customerId === customer.customerId);
        const customerRisk = riskScores.find(r => r.customerId === customer.customerId);
        
        // Calculate debt metrics
        const totalDebts = customerDebts.length;
        const totalDebtAmount = customerDebts.reduce((sum, debt) => sum + debt.amount, 0);
        const outstandingBalance = customerDebts.reduce((sum, debt) => sum + debt.balance, 0);
        const overdueDebts = customerDebts.filter(d => d.status === 'overdue').length;
        const overdueAmount = customerDebts
          .filter(d => d.status === 'overdue')
          .reduce((sum, debt) => sum + debt.balance, 0);
        
        // Calculate payment metrics
        const totalPayments = customerPayments.length;
        const totalPaidAmount = customerPayments.reduce((sum, payment) => sum + payment.amount, 0);
        const successfulPayments = customerPayments.filter(p => p.status === 'completed').length;
        const paymentSuccessRate = totalPayments > 0 ? (successfulPayments / totalPayments) * 100 : 0;
        
        // Calculate average payment days
        const paymentDays = customerPayments
          .filter(p => p.status === 'completed')
          .map(p => {
            const debt = customerDebts.find(d => d.debtId === p.debtId);
            if (debt) {
              const daysDiff = Math.abs(new Date(p.createdAt) - new Date(debt.createdAt));
              return daysDiff / (1000 * 60 * 60 * 24);
            }
            return 0;
          });
        
        const averagePaymentDays = paymentDays.length > 0 
          ? paymentDays.reduce((sum, days) => sum + days, 0) / paymentDays.length 
          : 0;
        
        // Get last payment info
        const lastPayment = customerPayments
          .filter(p => p.status === 'completed')
          .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))[0];
        
        // Build export record
        const exportRecord = {
          customerId: customer.customerId,
          customerName: customer.fullName,
          customerPhone: customer.phone,
          customerEmail: customer.email,
          riskLevel: customerRisk?.riskLevel || 'Unknown',
          riskScore: customerRisk?.riskScore || 0,
          mlConfidence: customerRisk?.confidence || 0,
          totalDebts,
          totalDebtAmount,
          outstandingBalance,
          overdueDebts,
          overdueAmount,
          totalPayments,
          totalPaidAmount,
          paymentSuccessRate: Math.round(paymentSuccessRate),
          averagePaymentDays: Math.round(averagePaymentDays),
          lastPaymentDate: lastPayment?.createdAt || null,
          lastPaymentAmount: lastPayment?.amount || 0,
          riskFactors: customerRisk?.factors?.join(', ') || '',
          lastMLAssessment: customerRisk?.lastAssessment || null,
          riskLastUpdated: customerRisk?.updatedAt || null
        };
        
        exportData.push(exportRecord);
      }
      
      return exportData;
    } catch (error) {
      logError('Failed to get ML risk data for export', 'MLRiskExportController', error);
      throw error;
    }
  }

  /**
   * Export ML risk data to CSV
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next middleware function
   */
  static async exportToCSV(req, res, next) {
    req.exportConfig = {
      module: 'ml_risk',
      getFields: this.getExportFields,
      getData: this.getExportData,
      filename: req.query.filename || 'ml_risk_assessment_export'
    };

    await this.prepareExport(req, res, next);
    return super.exportToCSV(req, res, next);
  }

  /**
   * Export ML risk data to Excel
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next middleware function
   */
  static async exportToExcel(req, res, next) {
    req.exportConfig = {
      module: 'ml_risk',
      getFields: this.getExportFields,
      getData: this.getExportData,
      filename: req.query.filename || 'ml_risk_assessment_export',
      options: {
        sheetName: 'ML Risk Assessment',
        styling: {
          header: true,
          columns: {
            'customerName': { width: 20 },
            'customerPhone': { width: 15 },
            'customerEmail': { width: 25 },
            'riskLevel': { width: 12 },
            'riskScore': { width: 12 },
            'mlConfidence': { width: 12 },
            'totalDebtAmount': { width: 15, alignment: { horizontal: 'right' } },
            'outstandingBalance': { width: 15, alignment: { horizontal: 'right' } },
            'overdueAmount': { width: 15, alignment: { horizontal: 'right' } },
            'totalPaidAmount': { width: 15, alignment: { horizontal: 'right' } },
            'paymentSuccessRate': { width: 15, alignment: { horizontal: 'right' } },
            'averagePaymentDays': { width: 15, alignment: { horizontal: 'right' } },
            'lastPaymentAmount': { width: 15, alignment: { horizontal: 'right' } },
            'riskFactors': { width: 40 },
            'lastMLAssessment': { width: 20 },
            'riskLastUpdated': { width: 20 }
          }
        }
      }
    };

    await this.prepareExport(req, res, next);
    return super.exportToExcel(req, res, next);
  }
}

module.exports = MLRiskExportController; 