"use client";

import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { toast } from 'sonner';
import { Plus, Package, DollarSign, Settings, Info } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { cn } from '@/lib/utils';
import { usePlanMutations } from '@/hooks/usePlan-index';

// Form validation schema - UPDATED to match backend Joi validation exactly
const createPlanSchema = z.object({
  name: z.string().min(1, 'Plan name is required').max(50, 'Name too long'), // Backend limit: 50
  displayName: z.string().min(1, 'Display name is required').max(100, 'Display name too long'),
  type: z.enum(['trial', 'monthly', 'yearly'], { // Backend only supports these 3 types
    required_error: 'Plan type is required',
  }),
  description: z.string().max(500, 'Description too long').optional(), // Backend limit: 500
  basePrice: z.number().min(0, 'Price must be positive').max(99999, 'Price too high'),
  currency: z.string().max(3, 'Currency code too long').default('USD'), // Backend limit: 3
  billingCycle: z.enum(['one-time', 'monthly', 'yearly'], { // Backend exact enum values
    required_error: 'Billing cycle is required',
  }),
  trialDays: z.number().min(0, 'Trial days must be positive').default(0), // Backend field
  setupFee: z.number().min(0, 'Setup fee must be positive').default(0), // Backend field
  isActive: z.boolean().default(true),
  displayOrder: z.number().int().min(1, 'Display order must be positive').default(1), // Backend field
  // Individual Feature Controls - SuperAdmin can enable/disable each feature
  debtTracking: z.boolean().default(true),
  customerPayments: z.boolean().default(true),
  smsReminders: z.boolean().default(true),
  smartRiskScore: z.boolean().default(true),
  businessDashboard: z.boolean().default(true),
  exportReports: z.boolean().default(true),
  customerProfiles: z.boolean().default(true),
  offlineSupport: z.boolean().default(true),
  // Limits - using backend field names and defaults
  maxProducts: z.number().min(1, 'Invalid limit').max(99999, 'Limit too high').default(1000), // Backend default: 1000
  maxEmployees: z.number().min(1, 'Invalid limit').max(99999, 'Limit too high').default(10), // Backend default: 10
  maxStorageMB: z.number().min(1, 'Invalid limit').max(99999, 'Limit too high').default(500), // Backend field name and default
  maxCustomers: z.number().min(1, 'Invalid limit').max(99999, 'Limit too high').default(1000), // Backend default: 1000
  maxDailyTransactions: z.number().min(1, 'Invalid limit').max(999999, 'Limit too high').default(500), // Backend field name and default
  // Metadata
  isRecommended: z.boolean().default(false), // Backend metadata field
  tags: z.string().optional(),
  customFields: z.string().optional(),
});

export function CreatePlanDialog({ isOpen, onClose, onSuccess }) {
  const [loading, setLoading] = useState(false);
  // Enable toast messages to see success/error feedback
  const { createPlan } = usePlanMutations({ 
    showToastMessages: true,
    onPlanCreated: (plan) => {
      console.log('Plan created successfully:', plan);
      form.reset();
      onSuccess?.();
      onClose();
    },
    onError: (operation, error) => {
      console.error('Plan operation failed:', operation, error);
    }
  });

  const form = useForm({
    resolver: zodResolver(createPlanSchema),
    defaultValues: {
      name: '',
      displayName: '',
      type: 'monthly',
      description: '',
      basePrice: 0,
      currency: 'USD',
      billingCycle: 'monthly',
      trialDays: 0,
      setupFee: 0,
      isActive: true,
      displayOrder: 1,
      // Individual Feature Controls - SuperAdmin can enable/disable each feature
      debtTracking: true,
      customerPayments: true,
      smsReminders: true,
      smartRiskScore: true,
      businessDashboard: true,
      exportReports: true,
      customerProfiles: true,
      offlineSupport: true,
      maxProducts: 1000,
      maxEmployees: 10,
      maxStorageMB: 500,
      maxCustomers: 1000,
      maxDailyTransactions: 500,
      isRecommended: false,
      tags: '',
      customFields: '',
    },
  });

  // Auto-fill display name based on name
  const watchName = form.watch('name');
  useEffect(() => {
    if (watchName && !form.getValues('displayName')) {
      form.setValue('displayName', watchName);
    }
  }, [watchName, form]);

  // Auto-update billing cycle based on type
  const watchType = form.watch('type');
  useEffect(() => {
    if (watchType === 'monthly') {
      form.setValue('billingCycle', 'monthly');
    } else if (watchType === 'yearly') {
      form.setValue('billingCycle', 'yearly');
    }
  }, [watchType, form]);

  const onSubmit = async (data) => {
    try {
      setLoading(true);
      
      // Process tags and custom fields
      const tags = data.tags ? data.tags.split(',').map(tag => tag.trim()).filter(Boolean) : [];
      let customFields = {};
      
      if (data.customFields) {
        try {
          customFields = JSON.parse(data.customFields);
        } catch (error) {
          toast.error('Invalid JSON in custom fields');
          return;
        }
      }
      
      // FIXED: Create data structure that exactly matches backend validation schema
      const planData = {
        name: data.name,
        displayName: data.displayName,
        type: data.type,
        description: data.description || '',
        // Backend requires nested pricing object
        pricing: {
          basePrice: data.basePrice,
          currency: data.currency,
          billingCycle: data.billingCycle,
          trialDays: data.trialDays,
          setupFee: data.setupFee,
        },
        // Backend requires complete features object
        features: {
          debtTracking: data.debtTracking,
          customerPayments: data.customerPayments,
          smsReminders: data.smsReminders,
          smartRiskScore: data.smartRiskScore,
          businessDashboard: data.businessDashboard,
          exportReports: data.exportReports,
          customerProfiles: data.customerProfiles,
          offlineSupport: data.offlineSupport,
        },
        // Backend requires nested limits object
        limits: {
          maxProducts: data.maxProducts,
          maxEmployees: data.maxEmployees,
          maxStorageMB: data.maxStorageMB,
          maxCustomers: data.maxCustomers,
          maxDailyTransactions: data.maxDailyTransactions,
        },
        // Backend requires nested metadata object
        metadata: {
          isRecommended: data.isRecommended,
          tags,
          customFields,
        },
        isActive: data.isActive,
        displayOrder: data.displayOrder,
      };

      await createPlan(planData);
      // Success handling is done in the onPlanCreated callback
    } catch (error) {
      console.error('Error creating plan:', error);
      // Error toast is handled by the mutation hook
      // Additional error handling can be done here if needed
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    form.reset();
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="dialog-content max-w-2xl">
        <DialogHeader className="dialog-header">
          <DialogTitle>Create New Plan</DialogTitle>
          <DialogDescription>
            Add a new subscription plan to the system
          </DialogDescription>
        </DialogHeader>
        
        <div className="dialog-body">

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            {/* Basic Information */}
            <div className="space-y-4">
              <div className="flex items-center gap-2 text-sm font-medium">
                <Info className="h-4 w-4" />
                Basic Information
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Plan Name</FormLabel>
                      <FormControl>
                        <Input placeholder="e.g., basic-monthly" {...field} />
                      </FormControl>
                      <FormDescription>
                        Internal plan identifier (lowercase, no spaces)
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="displayName"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Display Name</FormLabel>
                      <FormControl>
                        <Input placeholder="e.g., Basic Monthly" {...field} />
                      </FormControl>
                      <FormDescription>
                        Name shown to customers
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="type"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="flex items-center gap-2">
                        <Package className="h-4 w-4" />
                        Plan Type
                      </FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select plan type" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="trial">Trial</SelectItem>
                          <SelectItem value="monthly">Monthly</SelectItem>
                          <SelectItem value="yearly">Yearly</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="billingCycle"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Billing Cycle</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select billing cycle" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="one-time">One Time</SelectItem>
                          <SelectItem value="monthly">Monthly</SelectItem>
                          <SelectItem value="yearly">Yearly</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Description</FormLabel>
                    <FormControl>
                      <Textarea 
                        placeholder="Describe this plan's features and benefits..."
                        className="resize-none"
                        rows={3}
                        {...field} 
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Pricing */}
            <div className="space-y-4">
              <div className="flex items-center gap-2 text-sm font-medium">
                <DollarSign className="h-4 w-4" />
                Pricing
              </div>
              
              <div className="grid grid-cols-3 gap-4">
                <div className="col-span-2">
                  <FormField
                    control={form.control}
                    name="basePrice"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Base Price</FormLabel>
                        <FormControl>
                          <Input 
                            type="number" 
                            step="0.01"
                            min="0"
                            placeholder="0.00"
                            {...field}
                            onChange={e => field.onChange(parseFloat(e.target.value) || 0)}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <FormField
                  control={form.control}
                  name="currency"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Currency</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="USD" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="USD">USD</SelectItem>
                          <SelectItem value="EUR">EUR</SelectItem>
                          <SelectItem value="GBP">GBP</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {/* Additional Pricing Fields */}
              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="trialDays"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Trial Days</FormLabel>
                      <FormControl>
                        <Input 
                          type="number" 
                          min="0"
                          placeholder="0"
                          {...field}
                          onChange={e => field.onChange(parseInt(e.target.value) || 0)}
                        />
                      </FormControl>
                      <FormDescription>
                        Number of free trial days
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="setupFee"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Setup Fee</FormLabel>
                      <FormControl>
                        <Input 
                          type="number" 
                          step="0.01"
                          min="0"
                          placeholder="0.00"
                          {...field}
                          onChange={e => field.onChange(parseFloat(e.target.value) || 0)}
                        />
                      </FormControl>
                      <FormDescription>
                        One-time setup fee
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {/* Plan Settings */}
              <div className="grid grid-cols-3 gap-4">
                <FormField
                  control={form.control}
                  name="displayOrder"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Display Order</FormLabel>
                      <FormControl>
                        <Input 
                          type="number" 
                          min="1"
                          placeholder="1"
                          {...field}
                          onChange={e => field.onChange(parseInt(e.target.value) || 1)}
                        />
                      </FormControl>
                      <FormDescription>
                        Sort order for plan listing
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="isRecommended"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                      <div className="space-y-0.5">
                        <FormLabel className="text-base">
                          Recommended
                        </FormLabel>
                        <FormDescription>
                          Mark as recommended plan
                        </FormDescription>
                      </div>
                      <FormControl>
                        <Switch
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="isActive"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                      <div className="space-y-0.5">
                        <FormLabel className="text-base">
                          Active
                        </FormLabel>
                        <FormDescription>
                          Plan is available for signup
                        </FormDescription>
                      </div>
                      <FormControl>
                        <Switch
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />
              </div>
            </div>

            {/* Resource Limits */}
            <div className="space-y-4">
              <div className="flex items-center gap-2 text-sm font-medium">
                <Settings className="h-4 w-4" />
                Resource Limits
              </div>
              <p className="text-xs text-muted-foreground">Set specific limits or use large numbers for effectively unlimited</p>
              
              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="maxProducts"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Max Products</FormLabel>
                      <FormControl>
                        <Input 
                          type="number" 
                          min="1"
                          placeholder="1000"
                          {...field}
                          onChange={e => field.onChange(parseInt(e.target.value) || 1000)}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="maxEmployees"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Max Employees</FormLabel>
                      <FormControl>
                        <Input 
                          type="number" 
                          min="1"
                          placeholder="10"
                          {...field}
                          onChange={e => field.onChange(parseInt(e.target.value) || 10)}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="maxStorageMB"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Max Storage (MB)</FormLabel>
                      <FormControl>
                        <Input 
                          type="number" 
                          min="1"
                          placeholder="500"
                          {...field}
                          onChange={e => field.onChange(parseInt(e.target.value) || 500)}
                        />
                      </FormControl>
                      <FormDescription>
                        Storage limit in megabytes
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="maxCustomers"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Max Customers</FormLabel>
                      <FormControl>
                        <Input 
                          type="number" 
                          min="1"
                          placeholder="1000"
                          {...field}
                          onChange={e => field.onChange(parseInt(e.target.value) || 1000)}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="maxDailyTransactions"
                  render={({ field }) => (
                    <FormItem className="col-span-2">
                      <FormLabel>Max Daily Transactions</FormLabel>
                      <FormControl>
                        <Input 
                          type="number" 
                          min="1"
                          placeholder="500"
                          {...field}
                          onChange={e => field.onChange(parseInt(e.target.value) || 500)}
                        />
                      </FormControl>
                      <FormDescription>
                        Maximum transactions allowed per day
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>

            {/* Plan Features - SuperAdmin can control each feature individually */}
            <div className="space-y-4">
              <div className="flex items-center gap-2 text-sm font-medium">
                <Package className="h-4 w-4" />
                Plan Features
              </div>
              <p className="text-xs text-muted-foreground">
                Enable or disable specific features for this plan. Only enabled features will be available to subscribers.
              </p>
              
              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="debtTracking"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                      <div className="space-y-0.5">
                        <FormLabel className="text-sm font-medium">
                          Debt Tracking
                        </FormLabel>
                        <FormDescription className="text-xs">
                          Track customer debts and payments
                        </FormDescription>
                      </div>
                      <FormControl>
                        <Switch
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="customerPayments"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                      <div className="space-y-0.5">
                        <FormLabel className="text-sm font-medium">
                          Customer Payments
                        </FormLabel>
                        <FormDescription className="text-xs">
                          Accept and process customer payments
                        </FormDescription>
                      </div>
                      <FormControl>
                        <Switch
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="smsReminders"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                      <div className="space-y-0.5">
                        <FormLabel className="text-sm font-medium">
                          SMS Reminders
                        </FormLabel>
                        <FormDescription className="text-xs">
                          Send SMS notifications and reminders
                        </FormDescription>
                      </div>
                      <FormControl>
                        <Switch
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="smartRiskScore"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                      <div className="space-y-0.5">
                        <FormLabel className="text-sm font-medium">
                          Smart Risk Score
                        </FormLabel>
                        <FormDescription className="text-xs">
                          AI-powered customer risk assessment
                        </FormDescription>
                      </div>
                      <FormControl>
                        <Switch
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />



                <FormField
                  control={form.control}
                  name="businessDashboard"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                      <div className="space-y-0.5">
                        <FormLabel className="text-sm font-medium">
                          Business Dashboard
                        </FormLabel>
                        <FormDescription className="text-xs">
                          Analytics and business insights
                        </FormDescription>
                      </div>
                      <FormControl>
                        <Switch
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="exportReports"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                      <div className="space-y-0.5">
                        <FormLabel className="text-sm font-medium">
                          Export Reports
                        </FormLabel>
                        <FormDescription className="text-xs">
                          Generate and export business reports
                        </FormDescription>
                      </div>
                      <FormControl>
                        <Switch
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="customerProfiles"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                      <div className="space-y-0.5">
                        <FormLabel className="text-sm font-medium">
                          Customer Profiles
                        </FormLabel>
                        <FormDescription className="text-xs">
                          Detailed customer information management
                        </FormDescription>
                      </div>
                      <FormControl>
                        <Switch
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />



                <FormField
                  control={form.control}
                  name="offlineSupport"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                      <div className="space-y-0.5">
                        <FormLabel className="text-sm font-medium">
                          Offline Support
                        </FormLabel>
                        <FormDescription className="text-xs">
                          Work without internet connection
                        </FormDescription>
                      </div>
                      <FormControl>
                        <Switch
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />
              </div>
            </div>

            {/* Settings */}
            <div className="space-y-4">
              <FormField
                control={form.control}
                name="tags"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Tags</FormLabel>
                    <FormControl>
                      <Input 
                        placeholder="popular, recommended"
                        {...field}
                      />
                    </FormControl>
                    <FormDescription>
                      Comma-separated tags
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="customFields"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Custom Fields (JSON)</FormLabel>
                    <FormControl>
                      <Input 
                        placeholder='{"key": "value"}'
                        {...field}
                      />
                    </FormControl>
                    <FormDescription>
                      Valid JSON object
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Submit Button */}
            <div className="flex justify-end gap-3">
              <Button
                type="button"
                variant="outline"
                onClick={handleClose}
                disabled={loading}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={loading}>
                {loading ? 'Creating...' : 'Create Plan'}
              </Button>
            </div>
          </form>
        </Form>
        </div>
      </DialogContent>
    </Dialog>
  );
} 