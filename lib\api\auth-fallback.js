/**
 * Auth Fallback Module
 * Provides fallback mechanisms for authentication failures
 */

// Track authentication failure states
let authFailureCount = 0;
const MAX_AUTH_FAILURES = 3;
const AUTH_FAILURE_RESET_TIME = 5 * 60 * 1000; // 5 minutes
let lastAuthFailureTime = 0;

// Track server 500 errors during auth
let serverErrorCount = 0;
const MAX_SERVER_ERRORS = 2;
let lastServerErrorTime = 0;

/**
 * Record an authentication failure
 * @param {number} status - HTTP status code
 * @returns {boolean} Whether we've reached failure threshold
 */
export function recordAuthFailure(status) {
  const now = Date.now();
  
  // Reset counters if it's been a while since last failure
  if (now - lastAuthFailureTime > AUTH_FAILURE_RESET_TIME) {
    authFailureCount = 0;
    serverErrorCount = 0;
  }
  
  // Track server errors separately
  if (status >= 500) {
    serverErrorCount++;
    lastServerErrorTime = now;
    console.warn(`[Auth Fallback] Server error count: ${serverErrorCount}`);
    return serverErrorCount >= MAX_SERVER_ERRORS;
  }
  
  // Track auth failures
  authFailureCount++;
  lastAuthFailureTime = now;
  console.warn(`[Auth Fallback] Auth failure count: ${authFailureCount}`);
  return authFailureCount >= MAX_AUTH_FAILURES;
}

/**
 * Check if we should use fallback auth
 * @returns {boolean} Whether to use fallback auth
 */
export function shouldUseFallbackAuth() {
  const now = Date.now();
  
  // If we've had server errors recently, use fallback
  if (serverErrorCount >= MAX_SERVER_ERRORS && 
      (now - lastServerErrorTime < AUTH_FAILURE_RESET_TIME)) {
    console.log('[Auth Fallback] Using fallback due to server errors');
    return true;
  }
  
  // If we've had too many auth failures, use fallback
  if (authFailureCount >= MAX_AUTH_FAILURES && 
      (now - lastAuthFailureTime < AUTH_FAILURE_RESET_TIME)) {
    console.log('[Auth Fallback] Using fallback due to auth failures');
    return true;
  }
  
  return false;
}

/**
 * Reset all counters
 */
export function resetAuthFallbackCounters() {
  authFailureCount = 0;
  serverErrorCount = 0;
  lastAuthFailureTime = 0;
  lastServerErrorTime = 0;
  console.log('[Auth Fallback] Counters reset');
}

/**
 * Get stored user data from localStorage as fallback
 * @returns {Object|null} User data or null
 */
export function getFallbackUserData() {
  try {
    const userData = localStorage.getItem('userData');
    if (userData) {
      return JSON.parse(userData);
    }
  } catch (e) {
    console.error('[Auth Fallback] Error getting fallback user data:', e);
  }
  return null;
}

/**
 * Store user data in localStorage for fallback
 * @param {Object} userData - User data to store
 */
export function storeFallbackUserData(userData) {
  if (!userData) return;
  
  try {
    localStorage.setItem('userData', JSON.stringify(userData));
    console.log('[Auth Fallback] User data stored for fallback');
  } catch (e) {
    console.error('[Auth Fallback] Error storing fallback user data:', e);
  }
}
