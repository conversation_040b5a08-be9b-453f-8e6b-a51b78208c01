"use client";

import { useState, useCallback } from 'react';
import { toast } from 'sonner';
import SubscriptionService from '@/lib/services/subscription';

export function useExport() {
  const [exportStatus, setExportStatus] = useState('idle'); // 'idle' | 'exporting' | 'completed' | 'error'
  const [exportProgress, setExportProgress] = useState(0);
  const [exportError, setExportError] = useState(null);

  const exportData = useCallback(async (type, options = {}) => {
    try {
      setExportStatus('exporting');
      setExportProgress(0);
      setExportError(null);

      const result = await SubscriptionService.exportSubscriptions({
        format: options.format || 'csv',
        filters: options.filters || {},
        onProgress: (progress) => {
          setExportProgress(progress);
        }
      });

      if (result.fileUrl) {
        // Create a temporary link and trigger download
        const link = document.createElement('a');
        link.href = result.fileUrl;
        link.download = result.fileName;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        setExportStatus('completed');
        toast.success('Export completed successfully');
      } else {
        throw new Error('No file URL received from export');
      }
    } catch (err) {
      console.error('Error exporting data:', err);
      setExportError(err.message || 'Failed to export data');
      setExportStatus('error');
      toast.error('Failed to export data');
    }
  }, []);

  const cancelExport = useCallback(() => {
    setExportStatus('idle');
    setExportProgress(0);
    setExportError(null);
    toast.info('Export cancelled');
  }, []);

  return {
    exportData,
    exportProgress,
    exportStatus,
    exportError,
    cancelExport
  };
} 