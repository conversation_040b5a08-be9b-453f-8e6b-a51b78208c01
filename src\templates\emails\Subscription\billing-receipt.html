<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Billing Receipt - DeynCare</title>
    <style>
        /* Inlined styles for email client compatibility */
        :root {
            --primary-color: #2e86de;
            --secondary-color: #0abde3;
            --accent-color: #10ac84;
            --success-color: #27ae60;
            --text-color: #333333;
            --text-muted: #666666;
            --bg-light: #f8f9fa;
            --bg-white: #ffffff;
            --border-color: #e9ecef;
        }
        
        body {
            font-family: 'Segoe UI', Arial, sans-serif;
            line-height: 1.6;
            color: #333333;
            margin: 0;
            padding: 0;
            background-color: #f8f9fa;
        }
        
        .receipt-container {
            max-width: 600px;
            margin: 20px auto;
            background-color: #ffffff;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
        
        .receipt-header {
            background: linear-gradient(135deg, #2e86de 0%, #0abde3 100%);
            color: white;
            padding: 30px 20px;
            text-align: center;
            position: relative;
        }
        
        .receipt-header::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 20px;
            background: linear-gradient(135deg, #2e86de 0%, #0abde3 100%);
            clip-path: polygon(0 0, 100% 0, 100% 100%, 0 80%);
        }
        
        .receipt-logo {
            font-size: 32px;
            font-weight: 700;
            margin-bottom: 10px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }
        
        .receipt-title {
            font-size: 24px;
            font-weight: 600;
            margin: 0;
        }
        
        .receipt-subtitle {
            font-size: 16px;
            opacity: 0.9;
            margin: 5px 0 0;
        }
        
        .receipt-content {
            padding: 40px 30px;
        }
        
        .success-badge {
            background-color: #27ae60;
            color: white;
            padding: 12px 24px;
            border-radius: 50px;
            text-align: center;
            font-weight: 600;
            margin-bottom: 30px;
            display: inline-block;
            width: 100%;
            box-sizing: border-box;
        }
        
        .receipt-details {
            background-color: #f8f9fa;
            border-radius: 8px;
            padding: 25px;
            margin-bottom: 30px;
            border-left: 4px solid #2e86de;
        }
        
        .details-section {
            margin-bottom: 25px;
        }
        
        .details-section:last-child {
            margin-bottom: 0;
        }
        
        .section-title {
            font-size: 18px;
            font-weight: 600;
            color: #2e86de;
            margin-bottom: 15px;
            border-bottom: 2px solid #e9ecef;
            padding-bottom: 8px;
        }
        
        .details-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 15px;
        }
        
        .details-table td {
            padding: 10px 0;
            border-bottom: 1px solid #e9ecef;
            vertical-align: top;
        }
        
        .details-table td:first-child {
            font-weight: 600;
            width: 45%;
            color: #666666;
        }
        
        .details-table td:last-child {
            color: #333333;
            font-weight: 500;
        }
        
        .amount-highlight {
            font-size: 24px;
            font-weight: 700;
            color: #27ae60;
        }
        
        .plan-features {
            background-color: #ffffff;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            margin: 25px 0;
        }
        
        .plan-features h3 {
            color: #2e86de;
            margin-top: 0;
            margin-bottom: 15px;
        }
        
        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 10px;
            margin-top: 15px;
        }
        
        .feature-item {
            display: flex;
            align-items: center;
            padding: 8px 0;
        }
        
        .feature-icon {
            width: 20px;
            height: 20px;
            background-color: #27ae60;
            border-radius: 50%;
            color: white;
            text-align: center;
            line-height: 20px;
            font-size: 12px;
            margin-right: 10px;
            flex-shrink: 0;
        }
        
        .feature-text {
            font-size: 14px;
            color: #333333;
        }
        
        .receipt-footer {
            background-color: #f8f9fa;
            padding: 25px 30px;
            text-align: center;
            border-top: 1px solid #e9ecef;
        }
        
        .support-section {
            margin: 20px 0;
            padding: 20px;
            background-color: #fff3cd;
            border-radius: 8px;
            border-left: 4px solid #ffc107;
        }
        
        .support-section h4 {
            margin-top: 0;
            color: #856404;
        }
        
        .support-section p {
            margin-bottom: 0;
            color: #856404;
        }
        
        .footer-links {
            margin: 15px 0;
        }
        
        .footer-links a {
            color: #2e86de;
            text-decoration: none;
            margin: 0 10px;
        }
        
        .footer-text {
            font-size: 12px;
            color: #666666;
            margin: 5px 0;
        }
        
        .receipt-number {
            font-family: 'Courier New', monospace;
            background-color: #e9ecef;
            padding: 5px 10px;
            border-radius: 4px;
            font-weight: bold;
        }
        
        .divider {
            height: 2px;
            background: linear-gradient(to right, #2e86de, #0abde3);
            margin: 30px 0;
            border-radius: 2px;
        }
        
        @media only screen and (max-width: 480px) {
            .receipt-container {
                margin: 10px;
                border-radius: 8px;
            }
            
            .receipt-content {
                padding: 25px 20px;
            }
            
            .receipt-details {
                padding: 20px;
            }
            
            .features-grid {
                grid-template-columns: 1fr;
            }
            
            .details-table td:first-child {
                width: 40%;
                font-size: 14px;
            }
        }
    </style>
</head>
<body>
    <div class="receipt-container">
        <!-- Header -->
        <div class="receipt-header">
            <div class="receipt-logo">DeynCare</div>
            <h1 class="receipt-title">Billing Receipt</h1>
            <p class="receipt-subtitle">Thank you for your subscription!</p>
        </div>
        
        <!-- Content -->
        <div class="receipt-content">
            <!-- Success Message -->
            <div class="success-badge">
                ✅ Payment Successfully Processed
            </div>
            
            <!-- Receipt Details -->
            <div class="receipt-details">
                <!-- Shop Information -->
                <div class="details-section">
                    <h3 class="section-title">Shop Information</h3>
                    <table class="details-table">
                        <tr>
                            <td>Shop Name:</td>
                            <td><strong>{{shopName}}</strong></td>
                        </tr>
                        <tr>
                            <td>Shop ID:</td>
                            <td><span class="receipt-number">{{shopId}}</span></td>
                        </tr>
                        <tr>
                            <td>Owner Name:</td>
                            <td>{{fullName}}</td>
                        </tr>
                        <tr>
                            <td>Email:</td>
                            <td>{{email}}</td>
                        </tr>
                        <tr>
                            <td>Phone:</td>
                            <td>{{phone}}</td>
                        </tr>
                    </table>
                </div>
                
                <!-- Payment Information -->
                <div class="details-section">
                    <h3 class="section-title">Payment Information</h3>
                    <table class="details-table">
                        <tr>
                            <td>Receipt Number:</td>
                            <td><span class="receipt-number">{{receiptNumber}}</span></td>
                        </tr>
                        <tr>
                            <td>Transaction ID:</td>
                            <td><span class="receipt-number">{{transactionId}}</span></td>
                        </tr>
                        <tr>
                            <td>Payment Date:</td>
                            <td>{{paymentDate}}</td>
                        </tr>
                        <tr>
                            <td>Payment Method:</td>
                            <td>{{paymentMethod}}</td>
                        </tr>
                        <tr>
                            <td>Amount Paid:</td>
                            <td><span class="amount-highlight">${{amount}} USD</span></td>
                        </tr>
                    </table>
                </div>
                
                <!-- Subscription Information -->
                <div class="details-section">
                    <h3 class="section-title">Subscription Details</h3>
                    <table class="details-table">
                        <tr>
                            <td>Plan Type:</td>
                            <td><strong>{{planType}} Plan</strong></td>
                        </tr>
                        <tr>
                            <td>Billing Cycle:</td>
                            <td>{{billingCycle}}</td>
                        </tr>
                        <tr>
                            <td>Subscription Start:</td>
                            <td>{{subscriptionStart}}</td>
                        </tr>
                        <tr>
                            <td>Next Billing Date:</td>
                            <td>{{nextBillingDate}}</td>
                        </tr>
                        <tr>
                            <td>Status:</td>
                            <td><strong style="color: #27ae60;">Active</strong></td>
                        </tr>
                    </table>
                </div>
            </div>
            
            <!-- Plan Features -->
            <div class="plan-features">
                <h3>Your {{planType}} Plan Includes:</h3>
                <div class="features-grid">
                    <div class="feature-item">
                        <div class="feature-icon">✓</div>
                        <div class="feature-text">Complete Debt Management System</div>
                    </div>
                    <div class="feature-item">
                        <div class="feature-icon">✓</div>
                        <div class="feature-text">Customer Profile Management</div>
                    </div>
                    <div class="feature-item">
                        <div class="feature-icon">✓</div>
                        <div class="feature-text">Automated SMS Reminders</div>
                    </div>
                    <div class="feature-item">
                        <div class="feature-icon">✓</div>
                        <div class="feature-text">ML-Powered Risk Assessment</div>
                    </div>
                    <div class="feature-item">
                        <div class="feature-icon">✓</div>
                        <div class="feature-text">Payment Tracking System</div>
                    </div>
                    <div class="feature-item">
                        <div class="feature-icon">✓</div>
                        <div class="feature-text">Business Analytics Dashboard</div>
                    </div>
                    <div class="feature-item">
                        <div class="feature-icon">✓</div>
                        <div class="feature-text">Export & Reporting Tools</div>
                    </div>
                    <div class="feature-item">
                        <div class="feature-icon">✓</div>
                        <div class="feature-text">Multi-Currency Support</div>
                    </div>
                    <div class="feature-item">
                        <div class="feature-icon">✓</div>
                        <div class="feature-text">Offline Mode Capability</div>
                    </div>
                    <div class="feature-item">
                        <div class="feature-icon">✓</div>
                        <div class="feature-text">24/7 Customer Support</div>
                    </div>
                </div>
            </div>
            
            <div class="divider"></div>
            
            <!-- Support Section -->
            <div class="support-section">
                <h4>📞 Need Help?</h4>
                <p>If you have any questions about this payment or need assistance with your account, our support team is here to help!</p>
                <p><strong>Email:</strong> <EMAIL> | <strong>Phone:</strong> +****************</p>
            </div>
            
            <div style="text-align: center; margin-top: 30px;">
                <p style="font-size: 16px; color: #666666;">This email serves as your official receipt for tax purposes.</p>
                <p style="font-size: 14px; color: #999999;">Keep this receipt for your records. You can also access your billing history anytime in your account dashboard.</p>
            </div>
        </div>
        
        <!-- Footer -->
        <div class="receipt-footer">
            <div class="footer-links">
                <a href="{{dashboardUrl}}">Dashboard</a> |
                <a href="{{billingUrl}}">Billing History</a> |
                <a href="{{supportUrl}}">Support</a> |
                <a href="{{privacyUrl}}">Privacy Policy</a>
            </div>
            <div class="footer-text">
                © {{year}} DeynCare. All rights reserved.
            </div>
            <div class="footer-text">
                You're receiving this receipt because you completed a payment for DeynCare services.
            </div>
        </div>
    </div>
</body>
</html> 