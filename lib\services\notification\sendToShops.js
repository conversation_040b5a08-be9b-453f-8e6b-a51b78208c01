import apiBridge from '@/lib/api/bridge';
import { ENDPOINTS } from '@/lib/api/contract';
import { handleError, processApiResponse, validateRequiredFields } from '../baseService';

/**
 * Send push notification to specific shops (SuperAdmin only)
 * @param {Object} notificationData - Notification data
 * @param {string[]} notificationData.shopIds - Array of shop IDs
 * @param {string} notificationData.title - Notification title (1-100 chars)
 * @param {string} notificationData.message - Notification message (1-500 chars)
 * @param {string} [notificationData.priority='normal'] - Priority: low, normal, high
 * @param {string} [notificationData.actionUrl] - Action URL
 * @param {string} [notificationData.actionLabel] - Action button label
 * @param {string} [notificationData.scheduledAt] - ISO 8601 date for scheduling
 * @returns {Promise<Object>} Send results with success/failure counts
 */
async function sendToShops(notificationData) {
  // Validate required fields
  const validation = validateRequiredFields(notificationData, ['shopIds', 'title', 'message']);
  if (!validation.isValid) {
    throw new Error(validation.message);
  }

  // Additional validation for shopIds array
  if (!Array.isArray(notificationData.shopIds) || notificationData.shopIds.length === 0) {
    throw new Error('At least one shop ID is required');
  }

  // Validate title and message length
  if (notificationData.title.length > 100) {
    throw new Error('Title cannot exceed 100 characters');
  }

  if (notificationData.message.length > 500) {
    throw new Error('Message cannot exceed 500 characters');
  }

  // Validate priority if provided
  if (notificationData.priority && !['low', 'normal', 'high'].includes(notificationData.priority)) {
    throw new Error('Priority must be low, normal, or high');
  }

    // Prepare notification data with backend-expected structure
    const backendData = {
      shopIds: notificationData.shopIds,
      title: notificationData.title,
      message: notificationData.message,
      priority: notificationData.priority || 'normal',
      ...(notificationData.actionUrl && { actionUrl: notificationData.actionUrl }),
      ...(notificationData.actionLabel && { actionLabel: notificationData.actionLabel }),
      ...(notificationData.scheduledAt && { scheduledAt: notificationData.scheduledAt })
    };

    // Make API request using the bridge
    const response = await apiBridge.post(ENDPOINTS.NOTIFICATIONS.PUSH.TO_SHOPS, backendData, {
      skipCache: true
    });

  // Process response using utility
  const result = processApiResponse(response, 'Notification sent to shops successfully');
  return result;
}

export default sendToShops; 