# Authentication System Upgrade Plan

## 1. AuthRemoteSource Updates

### New Endpoints
```dart
class AuthRemoteSource {
  // New registration flow endpoints
  Future<Map<String, dynamic>> initRegistration({
    required String fullName,
    required String email,
    required String phone,
    required String password,
    required String shopName,
    required String shopAddress,
    String planType = 'trial',
    String paymentMethod = 'offline',
    bool initialPaid = false,
    String? discountCode,
  });

  Future<Map<String, dynamic>> verifyEmail({
    required String email,
    required String verificationCode,
  });

  Future<Map<String, dynamic>> processPayment({
    required String userId,
    required String shopId,
    required String planId,
    required String paymentMethod,
    required Map<String, dynamic> paymentDetails,
    String? discountCode,
  });
}
```

### Response Handling
```dart
// Add new response validation
void _validateRegistrationResponse(dynamic response) {
  if (!response.containsKey('nextStep')) {
    throw ApiException(
      message: 'Missing next step in registration response',
      code: 'missing_next_step',
    );
  }
  
  if (!response.containsKey('status')) {
    throw ApiException(
      message: 'Missing status in registration response',
      code: 'missing_status',
    );
  }
}
```

## 2. AuthRepository Updates

### New Methods
```dart
class AuthRepositoryImpl {
  Future<(UserModel, AuthTokenModel)> initRegistration({
    required String fullName,
    required String email,
    required String phone,
    required String password,
    required String shopName,
    required String shopAddress,
    String planType = 'trial',
    String paymentMethod = 'offline',
    bool initialPaid = false,
    String? discountCode,
  });

  Future<(UserModel, AuthTokenModel)> verifyEmail({
    required String email,
    required String verificationCode,
  });

  Future<(UserModel, AuthTokenModel)> processPayment({
    required String userId,
    required String shopId,
    required String planId,
    required String paymentMethod,
    required Map<String, dynamic> paymentDetails,
    String? discountCode,
  });
}
```

### Status Tracking
```dart
enum RegistrationStatus {
  initial,
  pendingEmailVerification,
  emailVerifiedPendingPayment,
  paymentProcessing,
  registrationComplete,
  error
}

class RegistrationProgress {
  final RegistrationStatus status;
  final String? nextStep;
  final Map<String, dynamic>? data;
  final String? error;
}
```

## 3. AuthUtils Updates

### New Storage Keys
```dart
class AuthUtils {
  static const String _registrationStatusKey = 'registration_status';
  static const String _paymentStatusKey = 'payment_status';
  static const String _verificationCodeKey = 'verification_code';
  static const String _registrationProgressKey = 'registration_progress';
  
  // New methods
  Future<void> saveRegistrationStatus(RegistrationStatus status);
  Future<void> savePaymentStatus(String status);
  Future<void> saveVerificationCode(String code);
  Future<void> saveRegistrationProgress(RegistrationProgress progress);
  
  // Enhanced user data storage
  Future<void> saveUserData(UserModel user) async {
    final data = {
      ...user.toJson(),
      'registrationStatus': user.registrationStatus,
      'isEmailVerified': user.isEmailVerified,
      'isPaid': user.isPaid,
      'paymentStatus': user.paymentStatus,
    };
    await _secureStorage.write(
      key: _userDataKey,
      value: _serializeJson(data),
    );
  }
}
```

## 4. ApiClient Updates

### New Endpoint Support
```dart
class ApiClient {
  // New registration endpoints
  Future<dynamic> initRegistration(Map<String, dynamic> data);
  Future<dynamic> verifyEmail(Map<String, dynamic> data);
  Future<dynamic> processPayment(Map<String, dynamic> data);
  
  // Enhanced error handling
  ApiException _handleRegistrationError(DioException e) {
    if (e.response?.statusCode == 409) {
      return ApiException(
        message: 'Registration conflict',
        code: 'registration_conflict',
        statusCode: 409,
      );
    }
    return _handleDioError(e);
  }
}
```

## 5. AuthService Updates

### New Methods
```dart
class AuthService {
  // New registration flow methods
  Future<(UserModel, AuthTokenModel)> initRegistration({
    required String fullName,
    required String email,
    required String phone,
    required String password,
    required String shopName,
    required String shopAddress,
    String planType = 'trial',
    String paymentMethod = 'offline',
    bool initialPaid = false,
    String? discountCode,
  });

  Future<(UserModel, AuthTokenModel)> verifyEmail({
    required String email,
    required String verificationCode,
  });

  Future<(UserModel, AuthTokenModel)> processPayment({
    required String userId,
    required String shopId,
    required String planId,
    required String paymentMethod,
    required Map<String, dynamic> paymentDetails,
    String? discountCode,
  });
  
  // Status tracking methods
  Future<RegistrationStatus> getRegistrationStatus();
  Future<String> getNextStep();
  Future<bool> isRegistrationComplete();
}
```

## Implementation Steps

1. **Phase 1: Core Updates**
   - Update models with new fields
   - Add new endpoints to remote source
   - Implement new repository methods

2. **Phase 2: Storage Updates**
   - Add new storage keys
   - Implement status tracking
   - Update data serialization

3. **Phase 3: Service Layer**
   - Add new service methods
   - Implement status tracking
   - Add error handling

4. **Phase 4: Testing**
   - Unit tests for new methods
   - Integration tests for flow
   - Error handling tests

## Migration Strategy

1. **Backward Compatibility**
   - Keep old methods temporarily
   - Add new methods alongside
   - Gradually deprecate old methods

2. **Data Migration**
   - Update existing user records
   - Add new fields with defaults
   - Migrate payment records

3. **Error Handling**
   - Add new error types
   - Update error messages
   - Implement recovery flows 