/**
 * Subscription Controller
 * 
 * @module SubscriptionController
 * @description
 * This file serves as the main entry point for all subscription-related operations.
 * It has been refactored to import controllers from subdirectories for better code organization.
 * 
 * Each controller group is responsible for a specific set of related operations:
 * - Management: Current subscription, subscription history, subscription by ID
 * - Lifecycle: Creation, upgrades, plan changes, cancellation, renewal
 * - Payments: Recording payments, EVC integration, offline payments
 *
 * @version 2.0.0
 * <AUTHOR> Development Team
 * @since 2025-06-01
 *
 * @example
 * // Import the entire subscription controller
 * const SubscriptionController = require('./controllers/subscriptionController');
 *
 * // Or import specific controllers directly (recommended for new code)
 * const { getCurrentSubscription } = require('./controllers/subscription');
 */

// Import all controller modules from the subscription directory
const subscriptionControllers = require('./subscription');

/**
 * Re-export all controllers from the modular structure
 * This allows existing code to continue importing from subscriptionController.js
 * while we gradually migrate to the new modular structure
 */
const SubscriptionController = {
  // Admin/SuperAdmin subscription management
  getSubscriptionById: subscriptionControllers.getSubscriptionById,
  getCurrentSubscription: subscriptionControllers.getCurrentSubscription,
  getSubscriptionHistory: subscriptionControllers.getSubscriptionHistory,
  getAllSubscriptions: subscriptionControllers.getAllSubscriptions,

  // Subscription lifecycle (admin only)
  upgradeFromTrial: subscriptionControllers.upgradeFromTrial,
  changePlan: subscriptionControllers.changePlan,
  cancelSubscription: subscriptionControllers.cancelSubscription,
  renewSubscription: subscriptionControllers.renewSubscription,
  extendSubscription: subscriptionControllers.extendSubscription,
  updateAutoRenewal: subscriptionControllers.updateAutoRenewal,
  requestUpgrade: subscriptionControllers.requestUpgrade,

  // Payment handling (admin only)
  recordPayment: subscriptionControllers.recordPayment,
  getPaymentProofFile: subscriptionControllers.getPaymentProofFile,

  // SuperAdmin functions
  getSubscriptionStats: subscriptionControllers.getSubscriptionStats,
  bulkUpdateSubscriptions: subscriptionControllers.bulkUpdateSubscriptions,
  manageCronTasks: subscriptionControllers.manageCronTasks,
  
  // Payment retry management (SuperAdmin only)
  getPaymentRetryStatus: subscriptionControllers.getPaymentRetryStatus,
  triggerManualRetry: subscriptionControllers.triggerManualRetry,
  cancelScheduledRetries: subscriptionControllers.cancelScheduledRetries,
  processAllPendingRetries: subscriptionControllers.processAllPendingRetries,
  getRetryConfiguration: subscriptionControllers.getRetryConfiguration
};

module.exports = SubscriptionController;