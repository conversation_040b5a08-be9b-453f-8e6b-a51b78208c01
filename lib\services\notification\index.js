/**
 * Notification Service - Aggregates all notification-related service methods
 * 
 * Provides unified access to:
 * - Push notifications (SuperAdmin only)
 * - FCM token management  
 * - Notification statistics and testing
 * - Firebase connection testing
 */

// Import individual notification services
import sendToShops from './sendToShops';
import sendBroadcast from './sendBroadcast';
import sendDebtReminders from './sendDebtReminders';
import getNotificationStats from './getNotificationStats';
import testFirebaseConnection from './testFirebaseConnection';
import getNotificationTargets from './getNotificationTargets';
import { getNotificationHistory } from './getNotificationHistory';
import registerFCMToken from './registerFCMToken';
import sendTestNotification from './sendTestNotification';
// Note: unregisterFCMToken, getUserFCMTokens, cleanupExpiredTokens not available in backend

// Import API bridge and utilities
import apiBridge from '@/lib/api/bridge';
import { toast } from 'sonner';
import BaseService from '@/lib/services/baseService';

/**
 * NotificationService exports all notification-related API operations
 */
const NotificationService = {
  // Core notification services
  sendToShops,
  sendBroadcast,
  sendDebtReminders,
  getNotificationStats,
  getNotificationHistory,
  testFirebaseConnection,
  getNotificationTargets,
  
  // FCM token management services (Backend-matched only)
  registerFCMToken,
  sendTestNotification,
  // Note: unregisterFCMToken, getUserFCMTokens, cleanupExpiredTokens not available in backend

  /**
   * Get notification system status
   * Combines Firebase test and basic stats for system health check
   * @returns {Promise<Object>} Combined system status
   */
  async getSystemStatus() {
    try {
      const [firebaseTest, stats] = await Promise.allSettled([
        testFirebaseConnection(),
        getNotificationStats({ days: 1 })
      ]);

      return {
        success: true,
        data: {
          firebase: {
            connected: firebaseTest.status === 'fulfilled' && firebaseTest.value?.success,
            error: firebaseTest.status === 'rejected' ? firebaseTest.reason?.message : null
          },
          todayStats: stats.status === 'fulfilled' ? stats.value : null,
          timestamp: new Date().toISOString()
        }
      };
    } catch (error) {
      console.error('[NotificationService.getSystemStatus] Error:', error);
      BaseService.handleError(error, 'NotificationService.getSystemStatus', false);
      
      // Return default status to prevent UI breaking
      return {
        success: false,
        data: {
          firebase: { connected: false, error: error.message },
          todayStats: null,
          timestamp: new Date().toISOString()
        }
      };
    }
  },

  /**
   * Validate notification data before sending
   * @param {Object} data - Notification data to validate
   * @param {string} type - Type of notification: 'shops', 'broadcast', 'debt_reminders'
   * @returns {Object} Validation result
   */
  validateNotificationData(data, type) {
    const errors = [];

    try {
      // Type-specific validation
      switch (type) {
        case 'shops':
          if (!data.shopIds || !Array.isArray(data.shopIds) || data.shopIds.length === 0) {
            errors.push('At least one shop ID is required');
          }
          break;
        case 'broadcast':
          // No additional validation needed beyond common checks
          break;
        case 'debt_reminders':
          if (data.reminderType && !['7_days', '3_days', 'overdue'].includes(data.reminderType)) {
            errors.push('Reminder type must be 7_days, 3_days, or overdue');
          }
          // Note: customMessage not used in backend debt reminders
          break;
      }

      // Common validations
      if (type !== 'debt_reminders' || data.customMessage) {
        if (!data.title || data.title.length === 0 || data.title.length > 100) {
          errors.push('Title must be between 1 and 100 characters');
        }
        
        if (!data.message || data.message.length === 0 || data.message.length > 500) {
          errors.push('Message must be between 1 and 500 characters');
        }
      }

      if (data.priority && !['low', 'normal', 'high'].includes(data.priority)) {
        errors.push('Priority must be low, normal, or high');
      }

      if (data.actionUrl && !/^https?:\/\/.+/.test(data.actionUrl)) {
        errors.push('Action URL must be a valid HTTP/HTTPS URL');
      }

      if (data.actionLabel && data.actionLabel.length > 50) {
        errors.push('Action label cannot exceed 50 characters');
      }

      return {
        isValid: errors.length === 0,
        errors
      };
    } catch (error) {
      return {
        isValid: false,
        errors: ['Validation error: ' + error.message]
      };
    }
  },

  // Note: updateFCMTokenUsage method removed - backend endpoint doesn't exist

  /**
   * Send notification with validation
   * Helper method that validates data before sending
   * @param {string} type - Notification type
   * @param {Object} data - Notification data
   * @returns {Promise<Object>} Send result
   */
  async sendValidatedNotification(type, data) {
    try {
      // Validate data first
      const validation = this.validateNotificationData(data, type);
      if (!validation.isValid) {
        const errorMessage = `Validation failed: ${validation.errors.join(', ')}`;
        BaseService.handleError({ message: errorMessage }, 'NotificationService.sendValidatedNotification', true);
        throw new Error(errorMessage);
      }

      // Send based on type
      switch (type) {
        case 'shops':
          return await sendToShops(data);
        case 'broadcast':
          return await sendBroadcast(data);
        case 'debt_reminders':
          return await sendDebtReminders(data);
        default:
          throw new Error(`Unsupported notification type: ${type}`);
      }
    } catch (error) {
      BaseService.handleError(error, 'NotificationService.sendValidatedNotification', true);
      throw error;
    }
  }
};

export default NotificationService; 