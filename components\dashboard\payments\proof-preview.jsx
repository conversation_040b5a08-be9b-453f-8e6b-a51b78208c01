"use client";

import { But<PERSON> } from "@/components/ui/button";
import {
  AlertDialog,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { X, Check, ZoomIn, ZoomOut, RotateCw, Download } from "lucide-react";
import { useState } from "react";

export function ProofPreview({ 
  open, 
  onOpenChange, 
  imageUrl, 
  payment, 
  onVerify, 
  onReject,
  onDownload 
}) {
  const [scale, setScale] = useState(1);
  const [rotation, setRotation] = useState(0);

  const handleClose = () => {
    onOpenChange(false);
    // Reset zoom and rotation when closing
    setScale(1);
    setRotation(0);
  };

  const zoomIn = () => {
    setScale(prev => Math.min(prev + 0.25, 3));
  };

  const zoomOut = () => {
    setScale(prev => Math.max(prev - 0.25, 0.5));
  };

  const rotate = () => {
    setRotation(prev => (prev + 90) % 360);
  };

  return (
    <AlertDialog open={open} onOpenChange={handleClose}>
      <AlertDialogContent className="max-w-3xl max-h-[90vh] flex flex-col p-0 gap-0 overflow-hidden">
        <AlertDialogHeader className="p-4 border-b">
          <AlertDialogTitle className="flex items-center justify-between">
            <span>Payment Proof Document</span>
            <div className="flex items-center gap-2">
              <Button 
                size="sm" 
                variant="ghost" 
                className="h-8 w-8 p-0" 
                onClick={zoomIn}
                title="Zoom In"
              >
                <ZoomIn className="h-4 w-4" />
              </Button>
              <Button 
                size="sm" 
                variant="ghost" 
                className="h-8 w-8 p-0" 
                onClick={zoomOut}
                title="Zoom Out"
              >
                <ZoomOut className="h-4 w-4" />
              </Button>
              <Button 
                size="sm" 
                variant="ghost" 
                className="h-8 w-8 p-0" 
                onClick={rotate}
                title="Rotate"
              >
                <RotateCw className="h-4 w-4" />
              </Button>
              {payment && (
                <Button 
                  size="sm" 
                  variant="ghost" 
                  className="h-8 w-8 p-0" 
                  onClick={() => onDownload && onDownload(payment)}
                  title="Download"
                >
                  <Download className="h-4 w-4" />
                </Button>
              )}
            </div>
          </AlertDialogTitle>
        </AlertDialogHeader>
        
        <div className="flex-1 overflow-auto p-1 bg-muted/30 flex items-center justify-center" style={{ minHeight: '50vh' }}>
          {imageUrl && (
            <div className="relative overflow-auto flex items-center justify-center h-full w-full">
              <img
                src={imageUrl}
                alt="Payment Proof"
                className="object-contain transition-all"
                style={{ 
                  transform: `scale(${scale}) rotate(${rotation}deg)`,
                  maxHeight: '70vh',
                  transformOrigin: 'center'
                }}
              />
            </div>
          )}
        </div>
        
        <AlertDialogFooter className="p-4 border-t flex flex-col sm:flex-row gap-2 sm:gap-0 sm:justify-between">
          <AlertDialogCancel 
            onClick={handleClose}
            className="sm:mr-auto mt-0"
          >
            Close
          </AlertDialogCancel>
          
          {payment && payment.status === "pending" && (
            <div className="flex gap-2 w-full sm:w-auto">
              <Button
                variant="destructive"
                onClick={() => {
                  handleClose();
                  onReject(payment);
                }}
                className="flex-1 sm:flex-initial"
              >
                <X className="mr-2 h-4 w-4" />
                Reject
              </Button>
              <Button
                onClick={() => {
                  handleClose();
                  onVerify(payment);
                }}
                className="flex-1 sm:flex-initial"
              >
                <Check className="mr-2 h-4 w-4" />
                Verify
              </Button>
            </div>
          )}
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
