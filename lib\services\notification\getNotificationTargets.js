import apiBridge from '@/lib/api/bridge';
import { ENDPOINTS } from '@/lib/api/contract';
import { handleError, processApiResponse } from '../baseService';

/**
 * Get notification targets (shops and users) - SuperAdmin only
 * @returns {Promise<Object>} Available notification targets
 */
async function getNotificationTargets() {
  try {
    // Make API request using the bridge with caching
    const response = await apiBridge.get(ENDPOINTS.NOTIFICATIONS.PUSH.TARGETS, {
      cacheKey: 'notification-targets',
      cacheTTL: 600000 // 10 minutes cache
    });

    // Process response using utility
    const result = processApiResponse(response, null); // No toast for targets retrieval
    return result;
  } catch (error) {
    // Don't show toast for targets errors to avoid UI disruption
    handleError(error, 'NotificationService.getNotificationTargets', false);
    
    // Return default empty targets to prevent UI breaking
    return {
      shops: [],
      users: [],
      total: 0
    };
  }
}

export default getNotificationTargets; 