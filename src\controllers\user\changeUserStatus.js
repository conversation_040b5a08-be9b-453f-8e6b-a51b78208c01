const mongoose = require('mongoose');
const UserService = require('../../services/userService');
const NotificationService = require('../../services/notificationService');
const { 
  AppError, 
  <PERSON><PERSON><PERSON><PERSON>, 
  Log<PERSON><PERSON>per, 
  logError 
} = require('../../utils');
const _handleStatusChangeError = require('./_handleStatusChangeError');

/**
 * Validate permissions for status change
 * @param {Object} actor - The user performing the action
 * @param {Object} targetUser - The user being modified
 * @param {Function} next - Express next function
 * @returns {boolean} Whether permission is valid
 * @private
 */
const _validateStatusChangePermission = async (actor, targetUser, next) => {
  // Only superAdmin can change status for users from other shops
  if (actor.role !== 'superAdmin' && targetUser.shopId !== actor.shopId) {
    next(new AppError('You do not have permission to change status for this user', 403, 'forbidden'));
    return false;
  }
  
  // Only superAdmin can change status for an admin
  if (targetUser.role === 'admin' && actor.role !== 'superAdmin') {
    next(new AppError('Only superAdmins can change status for admin users', 403, 'forbidden'));
    return false;
  }
  
  // Prevent changing status of superAdmin by non-superAdmins
  if (targetUser.role === 'superAdmin' && actor.role !== 'superAdmin') {
    next(new AppError('Only superAdmins can change status for other superAdmins', 403, 'forbidden'));
    return false;
  }
  
  return true;
};

/**
 * Prepare data for status update
 * @param {string} status - New status
 * @param {string} reason - Reason for status change
 * @param {string} actorId - ID of the user making the change
 * @returns {Object} Update data object
 * @private
 */
const _prepareStatusUpdateData = (status, reason, actorId) => {
  const updateData = { status };
  
  // Handle suspension-specific updates
  if (status === 'suspended') {
    updateData.isSuspended = true;
    updateData.suspensionReason = reason;
    updateData.suspendedAt = new Date();
    updateData.suspendedBy = actorId;
  } else if (status === 'active') {
    // When reactivating, clear suspension flags
    updateData.isSuspended = false;
    updateData.suspensionReason = null;
    updateData.suspendedAt = null;
    updateData.suspendedBy = null;
  }
  
  return updateData;
};

/**
 * Change user status (SuperAdmin can change status for any user)
 * PATCH /api/users/:userId/status
 * Requires authentication and appropriate authorization
 */
const changeUserStatus = async (req, res, next) => {
  try {
    const { userId } = req.params;
    const { status, reason, sendEmail = true } = req.validatedData || req.body;
    
    console.log(`[UserController] Changing user ${userId} status to ${status} with sendEmail=${sendEmail}`);
    
    // Get the user to update
    const userToUpdate = await UserService.getUserById(userId, { sanitize: false });
    
    // Validate permissions - will call next(error) if permission denied
    const permissionValid = await _validateStatusChangePermission(req.user, userToUpdate, next);
    if (!permissionValid || res.headersSent) {
      return; // Permission validation failed and response was already sent
    }
    
    // For suspended status, require a reason
    if (status === 'suspended' && (!reason || reason.trim() === '')) {
      return next(new AppError('A reason must be provided when suspending a user', 400, 'reason_required'));
    }
    
    // Prepare update data
    const updateData = _prepareStatusUpdateData(status, reason, req.user.userId);
    
    // Set update options
    const options = {
      actorId: req.user.userId,
      actorRole: req.user.role
    };
    
    // Start transaction for cascading operations
    const session = await mongoose.startSession();
    let updatedUser;
    
    try {
      await session.withTransaction(async () => {
        // 1. Update the user
        updatedUser = await UserService.updateUser(userId, updateData, options);
        
        // 2. CASCADE: Handle shop impact if user is shop owner/admin
        if (userToUpdate.shopId && userToUpdate.role === 'admin') {
          const Shop = require('../../models/shop.model');
          
          let shopStatusUpdate = {};
          let cascadeMessage = '';
          
          if (status === 'suspended' || status === 'inactive') {
            // Shop owner suspended/inactive → Suspend shop
            shopStatusUpdate = {
              status: 'suspended',
              statusReason: `Shop owner ${status}: ${reason || 'No reason provided'}`,
              statusChangedAt: new Date(),
              statusChangedBy: req.user.userId
            };
            cascadeMessage = `${status} → shop suspended`;
          } else if (status === 'active') {
            // Shop owner reactivated → Check if shop should be reactivated
            shopStatusUpdate = {
              status: 'active',
              statusReason: `Shop owner reactivated: ${reason || 'No reason provided'}`,
              statusChangedAt: new Date(),
              statusChangedBy: req.user.userId
            };
            cascadeMessage = 'reactivated → shop reactivated';
          }
          
          if (Object.keys(shopStatusUpdate).length > 0) {
            await Shop.findOneAndUpdate(
              { shopId: userToUpdate.shopId },
              { $set: shopStatusUpdate },
              { session }
            );
            
            console.log(`🔄 REVERSE CASCADE: User ${userId} (shop owner) ${cascadeMessage}`);
          }
        }
      });
      
      await session.endSession();
    } catch (error) {
      await session.endSession();
      throw error;
    }
    
    // Send notification asynchronously if sendEmail is true (don't await)
    if (sendEmail !== false) {
      NotificationService.sendStatusChangeNotification(userToUpdate, status, reason)
        .catch(error => logError(`Notification error: ${error.message}`, 'UserController', error));
    } else {
      console.log(`Email notification skipped for user ${userId} status change to ${status}`);
    }
    
    // Log the status change
    await LogHelper.createSecurityLog('user_status_changed', {
      actorId: req.user.userId,
      actorRole: req.user.role,
      targetId: userId,
      details: {
        newStatus: status,
        reason: reason || 'No reason provided',
        cascadeAction: userToUpdate.role === 'admin' ? 'shop_status_updated' : 'none'
      }
    });
    
    // Return successful response
    const responseMessage = userToUpdate.role === 'admin' 
      ? `User status changed to ${status} successfully with shop impact` 
      : `User status changed to ${status} successfully`;
      
    return ResponseHelper.success(res, responseMessage, {
      user: UserService.sanitizeUserForResponse(updatedUser)
    });
  } catch (error) {
    return _handleStatusChangeError(error, next, 'Failed to change user status');
  }
};

module.exports = changeUserStatus;
