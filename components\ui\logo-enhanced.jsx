"use client"

import Link from "next/link"
import Image from "next/image"
import { useTheme } from "next-themes"
import { cn } from "@/lib/utils"
import { useState, useEffect } from "react"

export function LogoEnhanced({ 
  className = "", 
  variant = "full", // "full", "icon", "text"
  size = "md", // "xs", "sm", "md", "lg", "xl"
  href = "/",
  showText = true,
  ...props 
}) {
  const { theme, systemTheme } = useTheme()
  const [mounted, setMounted] = useState(false)
  const [imageError, setImageError] = useState(false)
  
  // Handle hydration
  useEffect(() => {
    setMounted(true)
  }, [])
  
  // Determine if we're in dark mode - fallback to light mode if not mounted
  const isDark = mounted && (theme === "dark" || (theme === "system" && systemTheme === "dark"))
  
  // Enhanced size configurations with larger minimum sizes
  const sizeConfig = {
    xs: {
      logo: "h-8 sm:h-10 w-auto min-w-[80px]",
      icon: "h-8 w-8 sm:h-10 sm:w-10",
      text: "text-sm sm:text-base font-semibold",
      container: "space-x-2"
    },
    sm: {
      logo: "h-10 sm:h-12 w-auto min-w-[100px]",
      icon: "h-10 w-10 sm:h-12 sm:w-12",
      text: "text-base sm:text-lg font-semibold",
      container: "space-x-2"
    },
    md: {
      logo: "h-12 sm:h-14 w-auto min-w-[120px]",
      icon: "h-12 w-12 sm:h-14 sm:w-14",
      text: "text-lg sm:text-xl font-bold",
      container: "space-x-2 sm:space-x-3"
    },
    lg: {
      logo: "h-14 sm:h-16 w-auto min-w-[140px]",
      icon: "h-14 w-14 sm:h-16 sm:w-16",
      text: "text-xl sm:text-2xl font-bold",
      container: "space-x-2 sm:space-x-3"
    },
    xl: {
      logo: "h-18 sm:h-20 w-auto min-w-[160px]",
      icon: "h-18 w-18 sm:h-20 sm:w-20",
      text: "text-2xl sm:text-3xl font-bold",
      container: "space-x-3"
    }
  }
  
  const config = sizeConfig[size] || sizeConfig.md
  
  // Logo source based on theme - with fallback
  const logoSrc = isDark ? "/images/deyncare_logo_white.png" : "/images/deyncare_logo_black.png"
  const iconSrc = "/images/deyncare_icon.png"
  
  // Enhanced SVG fallback with DeynCare branding
  const SVGLogo = ({ className: svgClassName, isIcon = false }) => (
    <div className={cn("flex items-center", svgClassName)}>
      <div 
        className={cn(
          isIcon ? config.icon : "h-10 w-auto flex items-center", 
          "flex items-center justify-center rounded-lg bg-primary text-primary-foreground font-bold text-white px-3 py-1"
        )}
      >
        {isIcon ? (
          <svg
            viewBox="0 0 375 375"
            className="w-full h-full p-1"
            fill="currentColor"
          >
            <path d="M147.945312 217.484375 L 174.574219 217.484375 C 187.773438 217.484375 198.726562 206.945312 198.851562 193.75 C 198.980469 180.421875 188.179688 169.539062 174.878906 169.539062 L 105.359375 169.539062 L 122.433594 202.066406 C 127.414062 211.546875 137.238281 217.484375 147.945312 217.484375" />
            <path d="M 174.125 73.648438 L 55.015625 73.648438 L 72.09375 106.171875 C 77.070312 115.65625 86.894531 121.59375 97.601562 121.59375 L 174.265625 121.59375 C 213.894531 121.59375 246.871094 154.015625 246.800781 193.644531 C 246.730469 233.304688 214.554688 265.433594 174.878906 265.433594 L 155.703125 265.433594 L 171.375 295.285156 C 177.515625 306.980469 190.832031 313.058594 203.65625 309.902344 C 255.902344 297.027344 294.664062 249.894531 294.746094 193.695312 C 294.84375 127.667969 240.152344 73.648438 174.125 73.648438" />
          </svg>
        ) : (
          <span className={cn(config.text, "text-white font-bold tracking-wide")}>
            DeynCare
          </span>
        )}
      </div>
    </div>
  )
  
  const LogoContent = () => {
    // If not mounted yet, return SVG fallback to prevent hydration mismatch
    if (!mounted) {
      return variant === "icon" ? (
        <SVGLogo isIcon={true} />
      ) : variant === "text" ? (
        <span className={cn(config.text, "text-primary font-bold")}>DeynCare</span>
      ) : (
        <SVGLogo />
      )
    }

    switch (variant) {
      case "icon":
        return imageError ? (
          <SVGLogo isIcon={true} />
        ) : (
          <div className="relative">
            <Image
              src={iconSrc}
              alt="DeynCare"
              width={64}
              height={64}
              className={cn(config.icon, "object-contain")}
              priority
              onError={() => setImageError(true)}
              unoptimized
            />
          </div>
        )
      
      case "text":
        return (
          <span className={cn(config.text, "text-primary font-bold")}>
            DeynCare
          </span>
        )
      
      case "full":
      default:
        return imageError ? (
          <div className={cn("flex items-center", config.container)}>
            <SVGLogo />
          </div>
        ) : (
          <div className={cn("flex items-center", config.container)}>
            <div className="relative flex-shrink-0">
              <Image
                src={logoSrc}
                alt="DeynCare"
                width={250}
                height={60}
                className={cn(config.logo, "object-contain")}
                priority
                onError={() => setImageError(true)}
                unoptimized
              />
            </div>
            {showText && size !== "xs" && (
              <span className={cn(config.text, "text-primary font-bold hidden sm:block ml-2")}>
                DeynCare
              </span>
            )}
          </div>
        )
    }
  }
  
  // If href is provided, wrap in Link
  if (href) {
    return (
      <Link 
        href={href} 
        className={cn("flex items-center transition-opacity hover:opacity-80", className)} 
        {...props}
      >
        <LogoContent />
      </Link>
    )
  }
  
  // Otherwise return just the logo content
  return (
    <div className={cn("flex items-center", className)} {...props}>
      <LogoContent />
    </div>
  )
}

export default LogoEnhanced 