import React, { useState } from 'react';
import { XCircle, AlertTriangle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogFooter, 
  DialogHeader, 
  DialogTitle 
} from '@/components/ui/dialog';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import PaymentTransactionsService from '@/lib/services/payment-transactions';

/**
 * RejectTransactionDialog Component
 * Dialog for rejecting payment transactions with required reason
 * 
 * @param {Object} props - Component props
 * @param {boolean} props.open - Dialog open state
 * @param {Function} props.onOpenChange - Dialog open state change callback
 * @param {Object} props.transaction - Transaction to reject
 * @param {Function} props.onConfirm - Reject confirmation callback
 * @param {boolean} props.loading - Loading state
 * @returns {JSX.Element} Rendered component
 */
const RejectTransactionDialog = ({
  open = false,
  onOpenChange,
  transaction = null,
  onConfirm,
  loading = false
}) => {
  const [reason, setReason] = useState('');

  const handleConfirm = () => {
    if (!reason.trim()) return; // Require reason
    onConfirm?.(transaction, reason.trim());
    setReason(''); // Reset reason after confirmation
  };

  const handleClose = () => {
    setReason(''); // Reset reason when closing
    onOpenChange?.(false);
  };

  const isValidReason = reason.trim().length >= 10; // Minimum 10 characters

  if (!transaction) return null;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-red-700">
            <XCircle className="h-5 w-5" />
            Reject Payment Transaction
          </DialogTitle>
          <DialogDescription>
            You are about to reject this payment transaction. Please provide a clear reason.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4 py-4">
          {/* Transaction Details */}
          <div className="bg-red-50 dark:bg-red-900/10 p-4 rounded-lg space-y-3">
            <div className="flex justify-between items-start">
              <span className="text-sm font-medium text-red-800 dark:text-red-200">
                Payment ID
              </span>
              <span className="text-sm text-red-700 dark:text-red-300">
                {transaction.paymentId}
              </span>
            </div>
            
            <div className="flex justify-between items-start">
              <span className="text-sm font-medium text-red-800 dark:text-red-200">
                Shop
              </span>
              <span className="text-sm text-red-700 dark:text-red-300">
                {transaction.shopName}
              </span>
            </div>
            
            <div className="flex justify-between items-start">
              <span className="text-sm font-medium text-red-800 dark:text-red-200">
                Amount
              </span>
              <span className="text-sm font-semibold text-red-700 dark:text-red-300">
                {PaymentTransactionsService.formatCurrency(transaction.amount)}
              </span>
            </div>

            <div className="flex justify-between items-start">
              <span className="text-sm font-medium text-red-800 dark:text-red-200">
                Payment Method
              </span>
              <Badge variant="outline" className="text-red-700 border-red-300">
                {PaymentTransactionsService.formatPaymentMethod(transaction.method)}
              </Badge>
            </div>
          </div>

          {/* Action Effects */}
          <div className="bg-orange-50 dark:bg-orange-900/10 p-4 rounded-lg">
            <div className="flex items-start gap-2">
              <AlertTriangle className="h-4 w-4 text-orange-600 mt-0.5" />
              <div className="text-sm text-orange-800 dark:text-orange-200">
                <p className="font-medium mb-1">This will:</p>
                <ul className="space-y-1 text-xs">
                  <li>• Mark payment as "Rejected"</li>
                  <li>• Send rejection notification email</li>
                  <li>• Keep subscription inactive</li>
                  <li>• Log this rejection with reason</li>
                </ul>
              </div>
            </div>
          </div>

          {/* Rejection Reason (Required) */}
          <div className="space-y-2">
            <Label htmlFor="rejection-reason" className="text-red-700">
              Rejection Reason <span className="text-red-500">*</span>
            </Label>
            <Textarea
              id="rejection-reason"
              placeholder="Please provide a clear and detailed reason for rejecting this payment (minimum 10 characters)..."
              value={reason}
              onChange={(e) => setReason(e.target.value)}
              rows={4}
              maxLength={1000}
              className={!isValidReason && reason.length > 0 ? 'border-red-300' : ''}
            />
            <div className="flex justify-between text-xs">
              <div className={!isValidReason && reason.length > 0 ? 'text-red-500' : 'text-muted-foreground'}>
                {!isValidReason && reason.length > 0 
                  ? 'Reason must be at least 10 characters'
                  : 'Minimum 10 characters required'
                }
              </div>
              <div className="text-muted-foreground">
                {reason.length}/1000 characters
              </div>
            </div>
          </div>
        </div>

        <DialogFooter className="flex gap-2">
          <Button
            variant="outline"
            onClick={handleClose}
            disabled={loading}
          >
            Cancel
          </Button>
          <Button
            onClick={handleConfirm}
            disabled={loading || !isValidReason}
            variant="destructive"
          >
            {loading ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent mr-2" />
                Rejecting...
              </>
            ) : (
              <>
                <XCircle className="h-4 w-4 mr-2" />
                Reject Payment
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default RejectTransactionDialog; 