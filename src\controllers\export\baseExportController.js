/**
 * Base Export Controller
 * Provides common export functionality for all modules
 */
const ExportService = require('../../services/Export/exportService');
const { logError } = require('../../utils');

class BaseExportController {
  /**
   * Export data to CSV
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next middleware function
   */
  static async exportToCSV(req, res, next) {
    try {
      const {
        data,
        fields,
        module,
        filename,
        options = {}
      } = req.exportConfig;

      const processedData = ExportService.processData(data, fields);
      const csv = await ExportService.toCSV(processedData, fields, options);
      
      const exportFilename = ExportService.generateFilename(
        module,
        'csv',
        filename
      );

      res.setHeader('Content-Type', 'text/csv');
      res.setHeader('Content-Disposition', `attachment; filename=${exportFilename}`);
      
      return res.send(csv);
    } catch (error) {
      logError('Failed to export data to CSV', 'BaseExportController', error);
      return next(error);
    }
  }

  /**
   * Export data to Excel
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next middleware function
   */
  static async exportToExcel(req, res, next) {
    try {
      const {
        data,
        fields,
        module,
        filename,
        options = {}
      } = req.exportConfig;

      const processedData = ExportService.processData(data, fields);
      const excel = await ExportService.toExcel(processedData, fields, {
        ...options,
        sheetName: options.sheetName || module
      });
      
      const exportFilename = ExportService.generateFilename(
        module,
        'xlsx',
        filename
      );

      res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
      res.setHeader('Content-Disposition', `attachment; filename=${exportFilename}`);
      
      return res.send(excel);
    } catch (error) {
      logError('Failed to export data to Excel', 'BaseExportController', error);
      return next(error);
    }
  }

  /**
   * Prepare export configuration
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next middleware function
   */
  static async prepareExport(req, res, next) {
    try {
      const { module, service, getFields, getData } = req.exportConfig;

      // Get field configurations
      const fields = await getFields(req);

      // Validate fields
      if (!fields || !Array.isArray(fields) || fields.length === 0) {
        throw new Error(`Invalid or empty fields configuration for ${module} export`);
      }

      // Get data
      const data = await getData(req);

      // Validate data
      if (!data) {
        logError(`No data returned from getData for ${module} export`, 'BaseExportController');
        // Return empty array instead of undefined to prevent map errors
        req.exportConfig = {
          ...req.exportConfig,
          data: [],
          fields
        };
      } else if (!Array.isArray(data)) {
        logError(`Data is not an array for ${module} export`, 'BaseExportController');
        throw new Error(`Expected array data for ${module} export, got ${typeof data}`);
      } else {
        // Set export configuration with valid data
        req.exportConfig = {
          ...req.exportConfig,
          data,
          fields
        };
      }

      // Don't call next() - just prepare the config and return
    } catch (error) {
      logError('Failed to prepare export', 'BaseExportController', error);
      throw error; // Re-throw instead of calling next(error)
    }
  }
}

module.exports = BaseExportController; 