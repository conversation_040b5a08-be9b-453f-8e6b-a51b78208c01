/**
 * Utility functions for subscription-related operations
 */

/**
 * Format subscription status for display
 * @param {string} status - Subscription status
 * @returns {Object} Formatted status object with label and variant
 */
export const formatSubscriptionStatus = (status) => {
    const statusMap = {
      active: { label: 'Active', variant: 'success' },
      pending: { label: 'Pending', variant: 'warning' },
      expired: { label: 'Expired', variant: 'destructive' },
      cancelled: { label: 'Cancelled', variant: 'secondary' },
      suspended: { label: 'Suspended', variant: 'warning' },
      trial: { label: 'Trial', variant: 'info' }
    };
  
    return statusMap[status] || { label: status, variant: 'default' };
  };
  
  /**
   * Calculate subscription metrics
   * @param {Array} subscriptions - Array of subscription objects
   * @returns {Object} Calculated metrics
   */
  export const calculateSubscriptionMetrics = (subscriptions) => {
    const metrics = {
      total: subscriptions.length,
      active: 0,
      revenue: 0,
      byPlan: {},
      byStatus: {},
      byPaymentMethod: {}
    };
  
    subscriptions.forEach(sub => {
      // Count active subscriptions
      if (sub.status === 'active') {
        metrics.active++;
      }
  
      // Calculate revenue
      if (sub.status === 'active' && sub.pricing?.amount) {
        metrics.revenue += sub.pricing.amount;
      }
  
      // Group by plan
      const planName = sub.plan?.name || 'Unknown';
      metrics.byPlan[planName] = (metrics.byPlan[planName] || 0) + 1;
  
      // Group by status
      metrics.byStatus[sub.status] = (metrics.byStatus[sub.status] || 0) + 1;
  
      // Group by payment method
      const paymentMethod = sub.paymentMethod || 'Unknown';
      metrics.byPaymentMethod[paymentMethod] = (metrics.byPaymentMethod[paymentMethod] || 0) + 1;
    });
  
    return metrics;
  };
  
  /**
   * Format currency amount
   * @param {number} amount - Amount to format
   * @param {string} currency - Currency code
   * @returns {string} Formatted amount
   */
  export const formatCurrency = (amount, currency = 'USD') => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency
    }).format(amount);
  };
  
  /**
   * Calculate subscription period
   * @param {Date} startDate - Start date
   * @param {Date} endDate - End date
   * @returns {Object} Period information
   */
  export const calculateSubscriptionPeriod = (startDate, endDate) => {
    const start = new Date(startDate);
    const end = new Date(endDate);
    const now = new Date();
  
    const totalDays = Math.ceil((end - start) / (1000 * 60 * 60 * 24));
    const remainingDays = Math.ceil((end - now) / (1000 * 60 * 60 * 24));
    const elapsedDays = Math.ceil((now - start) / (1000 * 60 * 60 * 24));
  
    return {
      totalDays,
      remainingDays: Math.max(0, remainingDays),
      elapsedDays: Math.max(0, elapsedDays),
      progress: Math.min(100, Math.max(0, (elapsedDays / totalDays) * 100))
    };
  };
  
  /**
   * Validate subscription data
   * @param {Object} data - Subscription data to validate
   * @returns {Object} Validation result
   */
  export const validateSubscriptionData = (data) => {
    const errors = {};
  
    if (!data.planId) {
      errors.planId = 'Plan ID is required';
    }
  
    if (!data.startDate) {
      errors.startDate = 'Start date is required';
    }
  
    if (!data.endDate) {
      errors.endDate = 'End date is required';
    }
  
    if (data.startDate && data.endDate) {
      const start = new Date(data.startDate);
      const end = new Date(data.endDate);
  
      if (start >= end) {
        errors.endDate = 'End date must be after start date';
      }
    }
  
    return {
      isValid: Object.keys(errors).length === 0,
      errors
    };
  };
  
  /**
   * Generate subscription export filename
   * @param {string} format - Export format (csv, xlsx)
   * @returns {string} Generated filename
   */
  export const generateExportFilename = (format = 'csv') => {
    const date = new Date().toISOString().split('T')[0];
    return `subscriptions_export_${date}.${format}`;
  };
  
  /**
   * Process subscription data for export
   * @param {Array} subscriptions - Array of subscription objects
   * @returns {Array} Processed data ready for export
   */
  export const processSubscriptionExport = (subscriptions) => {
    return subscriptions.map(sub => ({
      'Subscription ID': sub.id,
      'Plan': sub.plan?.name || 'N/A',
      'Status': formatSubscriptionStatus(sub.status).label,
      'Start Date': new Date(sub.startDate).toLocaleDateString(),
      'End Date': new Date(sub.endDate).toLocaleDateString(),
      'Amount': formatCurrency(sub.pricing?.amount || 0, sub.pricing?.currency),
      'Payment Method': sub.paymentMethod || 'N/A',
      'Auto Renewal': sub.autoRenewal ? 'Yes' : 'No',
      'Created At': new Date(sub.createdAt).toLocaleDateString(),
      'Last Updated': new Date(sub.updatedAt).toLocaleDateString()
    }));
  };

/**
 * Validate subscription extension data
 * @param {Object} data - Data for subscription extension (days, reason)
 * @returns {Object} Validation result
 */
export const validateSubscriptionExtension = (data) => {
  const errors = {};

  if (!data.days || data.days <= 0) {
    errors.days = 'Extension days must be a positive number.';
  }

  if (!data.reason || data.reason.trim() === '') {
    errors.reason = 'Reason for extension is required.';
  }

  return {
    isValid: Object.keys(errors).length === 0,
    errors
  };
};

/**
 * Get the variant for a status badge
 * @param {string} status - Subscription status
 * @returns {string} Badge variant string
 */
export const getStatusVariant = (status) => {
  const statusMap = {
    active: 'success',
    pending: 'warning',
    expired: 'destructive',
    canceled: 'outline',
    suspended: 'warning',
    trial: 'warning',
    past_due: 'destructive'
  };

  return statusMap[status] || 'default';
};

/**
 * Validate payment verification data
 * @param {Object} data - Payment verification data
 * @returns {Object} Validation result
 */
export const validatePaymentVerification = (data) => {
  const errors = {};

  if (!data.notes || data.notes.trim() === '') {
    errors.notes = 'Verification notes are required';
  }

  if (data.amount && (isNaN(data.amount) || data.amount <= 0)) {
    errors.amount = 'Amount must be a positive number';
  }

  if (data.transactionId && data.transactionId.trim() === '') {
    errors.transactionId = 'Transaction ID cannot be empty if provided';
  }

  return {
    isValid: Object.keys(errors).length === 0,
    errors
  };
};

/**
 * Format date for display (e.g., MMM dd, yyyy)
 * @param {string|Date} date - Date to format
 * @returns {string} Formatted date string
 */
export const formatDate = (date) => {
  if (!date) return 'N/A';
  try {
    return new Date(date).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: 'numeric',
      minute: 'numeric',
      hour12: true
    });
  } catch (error) {
    console.error('Error formatting date:', error);
    return 'Invalid date';
  }
};

/**
 * Format short date for display (e.g., MMM dd, yyyy)
 * @param {string|Date} date - Date to format
 * @returns {string} Formatted short date string
 */
export const formatShortDate = (date) => {
  if (!date) return 'N/A';
  try {
    return new Date(date).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  } catch (error) {
    console.error('Error formatting short date:', error);
    return 'Invalid date';
  }
};