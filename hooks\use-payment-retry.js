import { useState, useCallback } from 'react';
import SubscriptionService from '@/lib/services/subscription';
import { toast } from 'sonner';

/**
 * Custom hook for payment retry operations
 * Provides methods for managing payment retries and configurations
 */
export const usePaymentRetry = () => {
  const [loading, setLoading] = useState(false);
  const [retryConfig, setRetryConfig] = useState(null);
  const [retryStatus, setRetryStatus] = useState(null);

  /**
   * Get payment retry configuration
   */
  const getRetryConfig = useCallback(async () => {
    try {
      setLoading(true);
      const config = await SubscriptionService.getPaymentRetryConfig();
      setRetryConfig(config);
      return config;
    } catch (error) {
      console.error('Error fetching retry config:', error);
      toast.error('Failed to load retry configuration');
      return null;
    } finally {
      setLoading(false);
    }
  }, []);

  /**
   * Get payment retry status for a subscription
   */
  const getRetryStatus = useCallback(async (subscriptionId) => {
    try {
      setLoading(true);
      const status = await SubscriptionService.getPaymentRetryStatus(subscriptionId);
      setRetryStatus(status);
      return status;
    } catch (error) {
      console.error('Error fetching retry status:', error);
      toast.error('Failed to load retry status');
      return null;
    } finally {
      setLoading(false);
    }
  }, []);

  /**
   * Trigger manual payment retry for a subscription
   */
  const triggerManualRetry = useCallback(async (subscriptionId) => {
    try {
      setLoading(true);
      const result = await SubscriptionService.triggerManualPaymentRetry(subscriptionId);
      if (result?.success) {
        toast.success('Payment retry triggered successfully');
        return result;
      }
      toast.error('Failed to trigger payment retry');
      return null;
    } catch (error) {
      console.error('Error triggering manual retry:', error);
      toast.error('Failed to trigger payment retry');
      return null;
    } finally {
      setLoading(false);
    }
  }, []);

  /**
   * Cancel scheduled retries for a subscription
   */
  const cancelRetries = useCallback(async (subscriptionId) => {
    try {
      setLoading(true);
      const result = await SubscriptionService.cancelScheduledRetries(subscriptionId);
      if (result?.success) {
        toast.success('Payment retries cancelled successfully');
        return result;
      }
      toast.error('Failed to cancel payment retries');
      return null;
    } catch (error) {
      console.error('Error cancelling retries:', error);
      toast.error('Failed to cancel payment retries');
      return null;
    } finally {
      setLoading(false);
    }
  }, []);

  /**
   * Process all pending payment retries
   */
  const processAllPendingRetries = useCallback(async () => {
    try {
      setLoading(true);
      const result = await SubscriptionService.processAllPendingRetries();
      if (result?.success) {
        toast.success(`Processed ${result.processed || 0} pending retries`);
        return result;
      }
      toast.error('Failed to process pending retries');
      return null;
    } catch (error) {
      console.error('Error processing pending retries:', error);
      toast.error('Failed to process pending retries');
      return null;
    } finally {
      setLoading(false);
    }
  }, []);

  return {
    loading,
    retryConfig,
    retryStatus,
    getRetryConfig,
    getRetryStatus,
    triggerManualRetry,
    cancelRetries,
    processAllPendingRetries
  };
};

export default usePaymentRetry; 