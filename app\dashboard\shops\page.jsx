"use client";

import { useState, useEffect, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/auth-context';
import { useShopManagement } from '@/hooks/use-shop-management';
import { useRealTimeUpdates } from '@/hooks/use-real-time-updates';
import { toast } from 'sonner';
import { ResponsiveContainer } from '@/components/layout/responsive-container';
import { KpiCard } from '@/components/dashboard/common/kpi-card';
// Icons are handled by the KpiCard component internally

// Import modular components
import ShopsHeader from '@/components/dashboard/shops/ShopsHeader';
import ShopsFilters from '@/components/dashboard/shops/ShopsFilters';
import ShopsTable from '@/components/dashboard/shops/ShopsTable';
import ShopsDialogs from '@/components/dashboard/shops/ShopsDialogs';
import { ExportDialog } from '@/components/dashboard/common/export-dialog';

// Import service directly for direct API access if needed
import ShopService from '@/lib/services/shop';

export default function ShopsPage() {
  const router = useRouter();
  const { isAuthenticated, isLoading, isSuperAdmin } = useAuth();
  
  // Real-time updates integration available if needed
  
  // Enhanced shop management hook with integrated statistics and shop operations
  const {
    shops,
    isLoading: isLoadingShops, // Fix naming mismatch - hook returns isLoading
    error: shopError, // Fix naming mismatch - hook returns error
    pagination,
    filters,
    fetchShops,
    createShop,
    updateShop,
    changeShopStatus,
    deleteShop,
    changePage,
    changePageSize,
    updateFilters,
    resetFilters,
    refreshAll,
    // Statistics
    summary,
    activity,
    businessMetrics,
    statsLoading,
    statsError,
    refreshStats
  } = useShopManagement({
    includeStats: true,
    autoRefreshStats: false,
    statsRefreshInterval: 300000 // 5 minutes
  });

  // Debug what the hook is returning
  if (process.env.NODE_ENV === 'development') {
    console.log('[ShopsPage] Hook returned:', {
      shopsCount: shops?.length || 0,
      shopsType: typeof shops,
      isArray: Array.isArray(shops),
      isLoading: isLoadingShops,
      error: shopError,
      paginationTotal: pagination?.totalItems || 0
    });
  }
  
  // Local state for UI interactions
  const [isRefreshing, setIsRefreshing] = useState(false);
  
  // Export dialog state
  const [showExportDialog, setShowExportDialog] = useState(false);
  
  // Dialog state
  const [registrationDialogOpen, setRegistrationDialogOpen] = useState(false);
  const [suspensionDialogOpen, setSuspensionDialogOpen] = useState(false);
  const [detailsDialogOpen, setDetailsDialogOpen] = useState(false);
  const [formDialogOpen, setFormDialogOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [currentShop, setCurrentShop] = useState(null);
  const [shopToEdit, setShopToEdit] = useState(null);
  const [shopToDelete, setShopToDelete] = useState(null);
  const [suspensionMode, setSuspensionMode] = useState('suspend');

  // Subscribe to real-time updates for UI feedback
  useRealTimeUpdates('shops', useCallback((data, updateOptions) => {
    if (updateOptions?.action) {
      switch (updateOptions.action) {
        case 'created':
          toast.success('New shop registered successfully');
          break;
        case 'updated':
          toast.success('Shop updated successfully');
          break;
        case 'deleted':
          toast.success('Shop deleted successfully');
          break;
        case 'status_changed':
          toast.success(`Shop ${updateOptions.newStatus === 'active' ? 'reactivated' : 'suspended'} successfully`);
          break;
        case 'refresh':
          // Silent refresh, no toast needed
          break;
      }
    }
  }, []));
  
  // Authentication check
  useEffect(() => {
    if (isLoading) return;
    
    // If not authenticated or not a superadmin, redirect to login
    if (!isAuthenticated || !isSuperAdmin) {
      router.push('/unauthorized');
    }
  }, [isAuthenticated, isLoading, isSuperAdmin, router]);
  
  // Optimized refresh handler with debounce protection
  const handleRefresh = async () => {
    // Prevent multiple refresh operations
    if (isRefreshing) return;
    
    // Set local loading state
    setIsRefreshing(true);
    
    try {
      // Use the integrated refresh that handles both shops and statistics
      await refreshAll();
      
      toast.success("Shop data refreshed successfully");
      
    } catch (error) {
      // Only log detailed errors in development
      if (process.env.NODE_ENV !== 'production') {
        console.error('[ShopsPage] Refresh error:', error);
      }
      toast.error(`Failed to refresh shops: ${error.message}`);
    } finally {
      // Add a small delay to ensure loading indicator is visible for a minimum time
      // This prevents flickering if the data loads very quickly
      setTimeout(() => {
        setIsRefreshing(false);
      }, 300);
    }
  };
  
  // Function to handle export
  const handleExport = () => {
    setShowExportDialog(true);
  };
  
  // Handle shop suspension/reactivation with real-time updates
  const handleShopStatusChange = useCallback(async (shop, reason) => {
    try {
      // Determine the new status based on the suspension mode
      const newStatus = suspensionMode === 'activate' ? 'active' : 'suspended';
      
      // Get the correct shop ID (could be shopId, _id, or id)
      const shopId = shop.shopId || shop._id || shop.id;
      
      await changeShopStatus(shopId, newStatus, reason);
      // Real-time update is triggered automatically by the hook
      setSuspensionDialogOpen(false);
    } catch (error) {
      console.error('[ShopsPage] Status change error:', error);
      toast.error(`Failed to ${suspensionMode === 'activate' ? 'reactivate' : 'suspend'} shop`);
    }
  }, [changeShopStatus, suspensionMode]);
  
  // Open shop details dialog
  const handleViewShop = (shop) => {
    setCurrentShop(shop);
    setDetailsDialogOpen(true);
  };
  
  // Open suspension dialog
  const handleOpenSuspension = (shop, mode = 'suspend') => {
    setCurrentShop(shop);
    setSuspensionMode(mode);
    setSuspensionDialogOpen(true);
  };
  
  // Handle registration success with real-time updates
  const handleRegistrationSuccess = async () => {
    setRegistrationDialogOpen(false);
    // Real-time update is triggered automatically by the hook
  };
  
  // Open edit shop form
  const handleOpenEditForm = (shop) => {
    setShopToEdit(shop);
    setFormDialogOpen(true);
  };
  
  // Handle edit shop with real-time updates
  const handleEditShop = async (shopId, data) => {
    try {
      await updateShop(shopId, data);
      // Real-time update is triggered automatically by the hook
      setFormDialogOpen(false);
    } catch (error) {
      toast.error('Failed to update shop');
    }
  };
  
  // Open delete shop dialog
  const handleOpenDeleteDialog = (shop) => {
    setShopToDelete(shop);
    setDeleteDialogOpen(true);
  };
  
  // Handle shop deletion with real-time updates
  const handleDeleteShop = async (shopId, reason) => {
    try {
      await deleteShop(shopId, reason);
      // Real-time update is triggered automatically by the hook
      setDeleteDialogOpen(false);
    } catch (error) {
      toast.error('Failed to delete shop');
    }
  };
  
  // Format currency values
  const formatCurrency = (value) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(value);
  };



  return (
    <ResponsiveContainer>
      <div className="space-y-6">
        {/* Shop Header */}
        <ShopsHeader 
          onRegisterClick={() => setRegistrationDialogOpen(true)}
          onExportClick={handleExport}
        />

        {/* Shop Summary KPI Cards */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <KpiCard
            title="Total Shops"
            value={summary?.totalShops?.toLocaleString() || '0'}
            icon="cart"
            description="All registered shops"
            loading={statsLoading}
          />
          <KpiCard
            title="Active Shops"
            value={summary?.activeShops?.toLocaleString() || '0'}
            icon="activity"
            description="Currently active shops"
            trend="up"
            trendValue={summary?.totalShops > 0 ? `${((summary?.activeShops / summary?.totalShops) * 100).toFixed(1)}%` : '0%'}
            trendLabel="of total shops"
            loading={statsLoading}
          />
          <KpiCard
            title="Pending Shops"
            value={summary?.pendingShops?.toLocaleString() || '0'}
            icon="clock"
            description="Awaiting verification"
            trend={summary?.pendingShops > 0 ? "down" : "neutral"}
            trendValue={summary?.totalShops > 0 ? `${((summary?.pendingShops / summary?.totalShops) * 100).toFixed(1)}%` : '0%'}
            trendLabel="need attention"
            loading={statsLoading}
          />
          <KpiCard
            title="Problem Shops"
            value={((summary?.suspendedShops || 0) + (summary?.inactiveShops || 0)).toLocaleString()}
            icon="alert-triangle"
            description="Suspended + Inactive"
            trend={((summary?.suspendedShops || 0) + (summary?.inactiveShops || 0)) > 0 ? "down" : "neutral"}
            trendValue={summary?.totalShops > 0 ? `${((((summary?.suspendedShops || 0) + (summary?.inactiveShops || 0)) / summary?.totalShops) * 100).toFixed(1)}%` : '0%'}
            trendLabel="problem shops"
            loading={statsLoading}
          />
        </div>
        
        {/* Shop Filters */}
        <ShopsFilters
          searchQuery={(filters && filters.search) || ''}
          setSearchQuery={(search) => updateFilters && updateFilters({ search })}
          statusFilter={(filters && filters.status) || 'all'}
          setStatusFilter={(status) => updateFilters && updateFilters({ status })}
          onRefresh={handleRefresh}
          isRefreshing={isRefreshing}
        />
        
        {/* Shop Table */}
        {process.env.NODE_ENV === 'development' && (
          <div className="mb-4 p-4 bg-gray-100 rounded text-sm">
            <strong>Debug Info:</strong> shops={shops?.length || 0},
            loading={isLoadingShops || isRefreshing},
            error={shopError},
            pagination={JSON.stringify(pagination)}
            <br />
            <strong>Hook Debug:</strong> shopsArray={Array.isArray(shops)},
            shopsType={typeof shops},
            firstShop={shops?.[0]?.shopName || 'none'}
          </div>
        )}
        <ShopsTable
          shops={shops || []}
          isLoading={isLoadingShops || isRefreshing}
          onView={handleViewShop}
          onEdit={handleOpenEditForm}
          onDelete={handleOpenDeleteDialog}
          onSuspend={(shop) => handleOpenSuspension(shop, 'suspend')}
          onReactivate={(shop) => handleOpenSuspension(shop, 'activate')}
          currentPage={(pagination && pagination.currentPage) || 1}
          pageSize={(pagination && pagination.pageSize) || 20}
          totalShops={(pagination && pagination.totalItems) || 0}
          totalPages={(pagination && pagination.totalPages) || 1}
          onPageChange={changePage || (() => {})}
          onPageSizeChange={changePageSize || (() => {})}
        />
        
        {/* Dialogs */}
        <ShopsDialogs
          registrationDialogOpen={registrationDialogOpen}
          setRegistrationDialogOpen={setRegistrationDialogOpen}
          onRegistrationSuccess={handleRegistrationSuccess}
          
          suspensionDialogOpen={suspensionDialogOpen}
          setSuspensionDialogOpen={setSuspensionDialogOpen}
          currentShop={currentShop}
          onSuspensionConfirm={handleShopStatusChange}
          suspensionMode={suspensionMode}
          
          detailsDialogOpen={detailsDialogOpen}
          setDetailsDialogOpen={setDetailsDialogOpen}
          onEditShop={() => handleOpenEditForm(currentShop)}
          
          formDialogOpen={formDialogOpen}
          setFormDialogOpen={setFormDialogOpen}
          shopToEdit={shopToEdit}
          onShopUpdate={handleEditShop}
          
          deleteDialogOpen={deleteDialogOpen}
          setDeleteDialogOpen={setDeleteDialogOpen}
          shopToDelete={shopToDelete}
          onDeleteShop={handleDeleteShop}
        />
        
        {/* Export Dialog */}
        <ExportDialog
          open={showExportDialog}
          onOpenChange={setShowExportDialog}
          exportType="shops"
          title="Export Shops"
          description="Export shop data to CSV format"
        />
      </div>
    </ResponsiveContainer>
  );
}
