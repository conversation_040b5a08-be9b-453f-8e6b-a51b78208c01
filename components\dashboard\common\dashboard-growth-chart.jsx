"use client"

import { Cartesian<PERSON><PERSON>, <PERSON>, <PERSON><PERSON>hart, <PERSON><PERSON><PERSON><PERSON>, YAxis } from "recharts"
import {
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from "@/components/ui/chart"

/**
 * Dashboard Growth Chart Component
 * Displays growth trends for users and shops
 */
export function DashboardGrowthChart({ data, config }) {
  return (
    <ChartContainer 
      config={config}
      className="h-[300px] w-full"
    >
      <LineChart
        accessibilityLayer
        data={data}
        margin={{
          top: 20,
          left: 12,
          right: 12,
          bottom: 12,
        }}
      >
        <CartesianGrid 
          vertical={false} 
          strokeDasharray="3 3"
          className="stroke-border/50"
        />
        <XAxis
          dataKey="month"
          tickLine={false}
          axisLine={false}
          tickMargin={8}
          tickFormatter={(value) => value.slice(0, 3)}
          className="text-xs"
        />
        <YAxis
          tickLine={false}
          axisLine={false}
          tickMargin={8}
          className="text-xs"
        />
        <ChartTooltip
          cursor={false}
          content={<ChartTooltipContent indicator="line" />}
        />
        <Line
          dataKey="users"
          type="natural"
          stroke="var(--color-users)"
          strokeWidth={2}
          dot={{
            fill: "var(--color-users)",
            strokeWidth: 2,
            r: 4,
          }}
          activeDot={{
            r: 6,
            strokeWidth: 2,
          }}
        />
        <Line
          dataKey="shops"
          type="natural"
          stroke="var(--color-shops)"
          strokeWidth={2}
          dot={{
            fill: "var(--color-shops)",
            strokeWidth: 2,
            r: 4,
          }}
          activeDot={{
            r: 6,
            strokeWidth: 2,
          }}
        />
      </LineChart>
    </ChartContainer>
  )
}
