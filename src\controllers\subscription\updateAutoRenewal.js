/**
 * Update Auto-Renewal Controller
 * Handles updating auto-renewal settings for subscriptions
 */
const { SubscriptionService } = require('../../services');
const { logError } = require('../../utils');

/**
 * Update auto-renewal settings
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
const updateAutoRenewal = async (req, res, next) => {
  try {
    const { autoRenew } = req.body;
    const { shopId } = req.user;
    
    if (!shopId) {
      return res.status(400).json({
        success: false,
        message: 'No shop associated with this user',
        statusCode: 400,
        type: 'shop_not_found'
      });
    }
    
    // Get current subscription for the shop
    const subscription = await SubscriptionService.getCurrentSubscriptionByShop(shopId);
    
    if (!subscription) {
      return res.status(404).json({
        success: false,
        message: 'Subscription not found',
        statusCode: 404,
        type: 'subscription_not_found'
      });
    }
    
    // Update renewal settings
    subscription.renewalSettings.autoRenew = autoRenew;
    
    // Add to history
    subscription.history.push({
      action: autoRenew ? 'auto_renewal_enabled' : 'auto_renewal_disabled',
      date: new Date(),
      performedBy: req.user?.userId || 'system'
    });
    
    // Save changes
    await subscription.save();
    
    return res.status(200).json({
      success: true,
      message: `Auto-renewal ${autoRenew ? 'enabled' : 'disabled'} successfully`,
      data: {
        subscriptionId: subscription.subscriptionId,
        autoRenew
      }
    });
  } catch (error) {
    logError('Failed to update auto-renewal settings', 'SubscriptionController', error);
    return next(error);
  }
};

module.exports = updateAutoRenewal;
