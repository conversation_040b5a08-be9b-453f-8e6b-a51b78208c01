/**
 * Enhanced Console logger with color-coded outputs, emoji indicators, and timestamps
 * Optimized for performance with configurable log levels and reduced verbosity
 */

// ANSI color codes for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  dim: '\x1b[2m',
  
  // Text colors
  black: '\x1b[30m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  white: '\x1b[37m',
};

// Logger configuration with enhanced performance settings
const config = {
  // Current log level (higher numbers = more verbose)
  logLevel: parseInt(process.env.LOG_LEVEL) || (process.env.NODE_ENV === 'development' ? 3 : 2),
  
  // Log level hierarchy (0 = silent, 1 = error only, 2 = warn+error, 3 = info+warn+error, 4 = debug+all)
  levels: {
    error: 1,
    warning: 2,
    success: 2,
    info: 3,
    debug: 4,
    auth: 3,
    database: 3,
    api: 3,
    performance: 3,
    session: 4,
    validation: 4
  },
  
  // Log types with associated emoji and color
  logTypes: {
    info: { emoji: 'ℹ️', color: colors.blue },
    success: { emoji: '✅', color: colors.green },
    warning: { emoji: '⚠️', color: colors.yellow },
    error: { emoji: '❌', color: colors.red },
    debug: { emoji: '🔧', color: colors.magenta },
    auth: { emoji: '🔐', color: colors.cyan },
    database: { emoji: '📊', color: colors.cyan },
    api: { emoji: '🌐', color: colors.blue },
    performance: { emoji: '⚡', color: colors.yellow },
    session: { emoji: '👤', color: colors.magenta },
    validation: { emoji: '✅', color: colors.green }
  },

  // Cache for repeated messages to avoid spam
  messageCache: new Map(),
  cacheTimeout: 5000, // 5 seconds
  
  // Get the current time in a readable format
  getTimestamp() {
    const now = new Date();
    const hours = now.getHours().toString().padStart(2, '0');
    const minutes = now.getMinutes().toString().padStart(2, '0');
    const seconds = now.getSeconds().toString().padStart(2, '0');
    
    return `[${hours}:${minutes}:${seconds}]`;
  },
  
  // Format a string with colors and styling
  formatString(str, color, bold = false) {
    if (process.env.NODE_ENV === 'test') {
      return str; // No color in test environment
    }
    
    return bold 
      ? `${color}${colors.bright}${str}${colors.reset}`
      : `${color}${str}${colors.reset}`;
  },

  // Check if message should be logged based on caching
  shouldLog(message, type, location) {
    const cacheKey = `${type}|${location}|${message}`;
    const now = Date.now();
    
    if (this.messageCache.has(cacheKey)) {
      const lastLogged = this.messageCache.get(cacheKey);
      if (now - lastLogged < this.cacheTimeout) {
        return false; // Skip repeated message
      }
    }
    
    this.messageCache.set(cacheKey, now);
    return true;
  },
  
  // Base log function with enhanced performance
  log(message, type = 'info', location = '', data = null) {
    // Skip logs in test environment unless they're errors
    if (process.env.NODE_ENV === 'test' && type !== 'error') {
      return;
    }
    
    // Check log level
    const messageLevel = this.levels[type] || this.levels.info;
    if (messageLevel > this.logLevel) {
      return;
    }
    
    // Skip debug logs in production unless DEBUG=true is set
    if (type === 'debug' && process.env.NODE_ENV === 'production' && process.env.DEBUG !== 'true') {
      return;
    }

    // Check if this message should be logged (anti-spam)
    if (!this.shouldLog(message, type, location)) {
      return;
    }
    
    const logConfig = this.logTypes[type] || this.logTypes.info;
    const timestamp = this.formatString(this.getTimestamp(), colors.dim);
    const emoji = logConfig.emoji;
    
    // Simplified location formatting
    const locationStr = location ? `[${location}] ` : '';
    
    // Log the message with simplified format
    console.log(`${timestamp} ${emoji} ${locationStr}${message}`);
    
    // Log additional data if provided (only for errors in production)
    if (data && (process.env.NODE_ENV !== 'production' || type === 'error')) {
      if (data instanceof Error) {
        console.log(this.formatString('Error:', colors.red), data.message);
        if (process.env.NODE_ENV === 'development' && data.stack) {
          console.log(data.stack);
        }
      } else if (typeof data === 'object') {
        console.log(this.formatString('Data:', colors.dim), JSON.stringify(data, null, 2));
      } else {
        console.log(this.formatString('Data:', colors.dim), data);
      }
    }
  }
};

// Bind the log function to the config object
const log = config.log.bind(config);

// Export specific log types with corrected parameter order
const logInfo = (message, location = '', data = null) => log(message, 'info', location, data);
const logSuccess = (message, location = '', data = null) => log(message, 'success', location, data);
const logWarning = (message, location = '', data = null) => log(message, 'warning', location, data);
const logError = (message, location = '', data = null) => log(message, 'error', location, data);
const logDebug = (message, location = '', data = null) => log(message, 'debug', location, data);
const logAuth = (message, location = '', data = null) => log(message, 'auth', location, data);
const logDatabase = (message, location = '', data = null) => log(message, 'database', location, data);
const logApi = (message, location = '', data = null) => log(message, 'api', location, data);
const logPerformance = (message, location = '', data = null) => log(message, 'performance', location, data);
const logSession = (message, location = '', data = null) => log(message, 'session', location, data);
const logValidation = (message, location = '', data = null) => log(message, 'validation', location, data);

/**
 * Enhanced Performance timer with better formatting
 */
const timer = {
  start(label) {
    const startTime = process.hrtime.bigint();
    
    return () => {
      const endTime = process.hrtime.bigint();
      const duration = Number(endTime - startTime) / 1000000; // Convert to milliseconds
      
      logPerformance(`${label}: ${duration.toFixed(2)}ms`, 'Timer');
      return duration;
    };
  }
};

// Export the module
module.exports = {
  log,
  logInfo,
  logSuccess,
  logWarning,
  logError,
  logDebug,
  logAuth,
  logDatabase,
  logApi,
  logPerformance,
  logSession,
  logValidation,
  timer,
  colors,
  // Utility functions
  setLogLevel: (level) => { config.logLevel = level; },
  getLogLevel: () => config.logLevel,
  clearCache: () => { config.messageCache.clear(); }
};
