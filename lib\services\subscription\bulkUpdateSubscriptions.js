import apiBridge from '@/lib/api/bridge';
import { ENDPOINTS } from '@/lib/api/contract';;
import { handleError, processApiResponse, validateRequiredFields } from '../baseService';

/**
 * Perform bulk operations on subscriptions (SuperAdmin only)
 * @param {Object} bulkData - Bulk operation data
 * @returns {Promise<Object>} Bulk operation results
 */
async function bulkUpdateSubscriptions(bulkData) {
  try {
    // Validate required fields
    const requiredFields = ['operation', 'subscriptionIds'];
    const validation = validateRequiredFields(bulkData, requiredFields);
    
    if (!validation.isValid) {
      throw new Error(validation.message);
    }

    // Validate subscriptionIds is array
    if (!Array.isArray(bulkData.subscriptionIds) || bulkData.subscriptionIds.length === 0) {
      throw new Error('subscriptionIds must be a non-empty array');
    }

    const response = await apiBridge.post(`${ENDPOINTS.SUBSCRIPTIONS.BASE}/bulk`, bulkData, {
      clearCacheEndpoint: ENDPOINTS.SUBSCRIPTIONS.BASE
    });
    
    const result = processApiResponse(response, `Bulk operation completed successfully for ${bulkData.subscriptionIds.length} subscriptions`);
    return result;
  } catch (error) {
    handleError(error, 'SubscriptionService.bulkUpdateSubscriptions', true);
    throw error;
  }
}

export default bulkUpdateSubscriptions; 
