"use client"

import { <PERSON>ginForm } from "@/components/auth/login-form"
import { Logo } from "@/components/ui/logo"
import { ThemeToggleFixed } from "@/components/ui/theme-toggle-fixed"
import Link from "next/link"
import { useAuth } from "@/contexts/auth-context"
import { useEffect } from "react"
import { useRouter } from "next/navigation"
import { ResponsiveContainer } from "@/components/layout/responsive-container"
import { useResponsive } from "@/hooks/use-responsive"

export default function LoginPage() {
  const { isAuthenticated, user, isSuperAdmin } = useAuth()
  const router = useRouter()
  const { isMobile, isTablet } = useResponsive()
  
  // Redirect if already authenticated, checking role
  useEffect(() => {
    if (isAuthenticated) {
      if (isSuperAdmin()) {
        router.push("/dashboard")
      } else {
        // Non-superadmin user, redirect to unauthorized
        router.push("/unauthorized")
      }
    }
  }, [isAuthenticated, isSuperAdmin, router])
  
  return (
    <div className="flex min-h-screen bg-background items-center justify-center p-4 md:p-8">
      {/* Main card with split design */}
      <div className="w-full max-w-6xl overflow-hidden rounded-2xl border border-border shadow-sm relative">
        {/* Theme toggle in top right corner */}
        <div className="absolute top-4 right-4 z-10">
          <ThemeToggleFixed />
        </div>
        
        <div className="flex flex-col md:flex-row">
          {/* Left side - Login form */}
          <div className="w-full p-8 md:w-1/2 bg-background">
            <div className="mb-8 flex flex-col space-y-2">
              <Logo className="mx-auto h-12 w-12 mb-6" />
              <h1 className="text-center text-3xl font-bold">Log In</h1>
              <p className="text-center text-muted-foreground">
                Welcome back! Please enter your details
              </p>
            </div>
            
            <LoginForm />
            
            <div className="mt-8 text-center text-sm">
              <p className="text-muted-foreground">
                Don&apos;t have an account?{" "}
                <Link href="/contact" className="text-primary underline-offset-4 hover:underline">
                  Contact us
                </Link>
              </p>
            </div>
          </div>
          
          {/* Right side - Background with DeynCare brand blue */}
          <div className="hidden md:block md:w-1/2 bg-primary p-8">
            <div className="flex h-full flex-col items-center justify-center text-center">
              <div className="space-y-6 max-w-md">
                <h2 className="text-2xl md:text-3xl font-bold text-white">
                  DeynCare Superadmin Portal
                </h2>
                <p className="text-primary-foreground/90 text-base">
                   DeynCare empowers businesses with intelligent debt managemen.
                </p>
                
                <div className="mt-8 rounded-xl bg-white/10 p-6 backdrop-blur-sm text-left">
                  <p className="text-primary-foreground">
                    "DeynCare has transformed how we manage Debt care and Repayment RIsk."
                  </p>
                  <div className="mt-4 flex items-center">
                    <div className="h-10 w-10 rounded-full bg-primary-foreground flex items-center justify-center text-primary font-bold">
                      AD
                    </div>
                    <div className="ml-3">
                      <p className="text-sm font-medium text-primary-foreground">Tm. G-108</p>
                      <p className="text-xs text-primary-foreground/70">CEO Founder</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
