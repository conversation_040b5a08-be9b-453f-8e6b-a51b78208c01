const admin = require('firebase-admin');
const { logInfo, logSuccess, logError } = require('../utils/logger');
const SettingsHelper = require('../utils/helpers/settingsHelper');

/**
 * Firebase Cloud Messaging Service
 * Handles push notifications with proper token management
 */
class FirebaseService {
  constructor() {
    this.adminApp = null;
    this.isInitialized = false;
    // Lazy-loaded models to prevent circular dependencies
    this._models = {};
  }

  /**
   * Lazy load models to prevent circular dependencies
   * @param {string} modelName - Name of the model to load
   * @returns {Object} Model instance
   */
  getModel(modelName) {
    if (!this._models[modelName]) {
      try {
        this._models[modelName] = require(`../models/${modelName}.model`);
      } catch (error) {
        logError(`Failed to load model ${modelName}: ${error.message}`, 'FirebaseService');
        throw error;
      }
    }
    return this._models[modelName];
  }

  /**
   * Initialize Firebase Admin SDK with timeout
   * @returns {Promise<boolean>} Success status
   */
  async initialize() {
    try {
      if (this.isInitialized) {
        return true;
      }

      // Add timeout to Firebase initialization
      const initPromise = new Promise((resolve, reject) => {
        try {
          const credentials = SettingsHelper.getFirebaseCredentials();
          if (!credentials) {
            return reject(new Error('Firebase credentials not found or invalid'));
          }

          // Check if Firebase is already initialized
          try {
            if (admin.apps.length > 0) {
              this.adminApp = admin.apps[0];
              this.isInitialized = true;
              return resolve(true);
            }
          } catch (checkError) {
            // Continue with initialization
          }

          this.adminApp = admin.initializeApp({
            credential: admin.credential.cert(credentials.serviceAccountKey),
            projectId: credentials.projectId
          });

          this.isInitialized = true;
          resolve(true);
        } catch (error) {
          reject(error);
        }
      });

      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('Firebase initialization timeout after 5 seconds')), 5000);
      });

      await Promise.race([initPromise, timeoutPromise]);
      logSuccess('Firebase Admin SDK initialized successfully', 'FirebaseService');
      return true;
    } catch (error) {
      logError(`Firebase initialization failed: ${error.message}`, 'FirebaseService', error);
      this.isInitialized = false;
      return false;
    }
  }

  /**
   * Send push notification to Admin/Shop Owner
   * @param {Object} notificationData - Notification data
   * @returns {Promise<Object>} Send result
   */
  async sendPushNotification(notificationData) {
    try {
      if (!this.isInitialized) {
        const initSuccess = await this.initialize();
        if (!initSuccess) {
          throw new Error('Firebase not initialized');
        }
      }

      const {
        recipientTokens, // FCM tokens of Admin/Shop Owner devices
        title,
        message,
        data = {},
        priority = 'high',
        badge,
        sound = 'default',
        clickAction,
        imageUrl
      } = notificationData;

      // Validate recipient tokens
      if (!recipientTokens || recipientTokens.length === 0) {
        throw new Error('No recipient tokens provided');
      }

      // Prepare FCM message with better imageUrl validation
      const notification = {
        title: title,
        body: message
      };
      
      // Only add imageUrl if it's a valid URL string
      if (imageUrl && typeof imageUrl === 'string' && imageUrl.startsWith('http')) {
        notification.imageUrl = imageUrl;
      }
      
      const fcmMessage = {
        notification: notification,
        data: {
          ...data,
          notificationId: data.notificationId || `FCM_${Date.now()}`,
          timestamp: new Date().toISOString(),
          type: data.type || 'general'
        },
        android: {
          priority: this.mapToFirebasePriority(priority),
          notification: {
            channelId: 'deyncare_admin',
            priority: 'high',
            defaultSound: true,
            defaultVibrateTimings: true,
            ...(clickAction && { clickAction })
          }
        },
        apns: {
          payload: {
            aps: {
              alert: {
                title: title,
                body: message
              },
              badge: badge || 1,
              sound: sound,
              category: data.category || 'GENERAL'
            }
          }
        }
      };

      // Send to multiple tokens with fallback for older Firebase SDK versions
      const results = [];
      const batchSize = 500; // FCM batch limit

      for (let i = 0; i < recipientTokens.length; i += batchSize) {
        const batch = recipientTokens.slice(i, i + batchSize);
        
        try {
          let response;
          
          // 🔍 DEBUG: Check Firebase messaging methods available
          const messaging = admin.messaging();
          logInfo(`DEBUG - Firebase messaging methods: sendMulticast=${typeof messaging.sendMulticast}, send=${typeof messaging.send}`, 'FirebaseService');
          
          // Force fallback method for now since sendMulticast is not working
          logInfo(`Using individual send method for Firebase SDK compatibility`, 'FirebaseService');
          
          const individualResults = await Promise.allSettled(
            batch.map(token => 
              admin.messaging().send({
                token: token,
                ...fcmMessage
              })
            )
          );
          
          // Convert individual results to multicast format
          response = {
            successCount: individualResults.filter(r => r.status === 'fulfilled').length,
            failureCount: individualResults.filter(r => r.status === 'rejected').length,
            responses: individualResults.map((result, index) => ({
              success: result.status === 'fulfilled',
              messageId: result.status === 'fulfilled' ? result.value : null,
              error: result.status === 'rejected' ? { message: result.reason.message } : null
            }))
          };

          results.push({
            batchIndex: Math.floor(i / batchSize),
            successCount: response.successCount,
            failureCount: response.failureCount,
            responses: response.responses
          });

          logInfo(`FCM batch ${Math.floor(i / batchSize)} sent: ${response.successCount}/${batch.length} successful`, 'FirebaseService');
          
          // Handle failed tokens
          if (response.failureCount > 0) {
            await this.handleFailedTokens(batch, response.responses);
          }
        } catch (batchError) {
          logError(`FCM batch ${Math.floor(i / batchSize)} failed: ${batchError.message}`, 'FirebaseService');
          results.push({
            batchIndex: Math.floor(i / batchSize),
            successCount: 0,
            failureCount: batch.length,
            error: batchError.message
          });
        }
      }

      // Calculate totals
      const totalSuccess = results.reduce((sum, result) => sum + (result.successCount || 0), 0);
      const totalFailure = results.reduce((sum, result) => sum + (result.failureCount || 0), 0);

      logInfo(`Push notification sent: ${totalSuccess}/${recipientTokens.length} successful`, 'FirebaseService');

      return {
        success: totalSuccess > 0,
        totalSent: totalSuccess,
        totalFailed: totalFailure,
        totalRecipients: recipientTokens.length,
        results: results
      };
    } catch (error) {
      logError(`Failed to send push notification: ${error.message}`, 'FirebaseService', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Send targeted notification to specific shop owners
   * @param {Array} shopIds - Shop IDs to target
   * @param {Object} notificationData - Notification content
   * @returns {Promise<Object>} Send result
   */
  async sendToShopOwners(shopIds, notificationData) {
    try {
      // Get FCM tokens for shop owners using lazy-loaded model
      const User = this.getModel('user');
      const users = await User.find({
        shopId: { $in: shopIds },
        role: { $in: ['admin', 'employee'] },
        status: 'active',
        isSuspended: false,
        isDeleted: false,
        fcmTokens: { $exists: true, $ne: [] }
      }).select('fcmTokens shopId fullName role status');

      if (users.length === 0) {
        logInfo(`No active shop owners found with FCM tokens for shops: ${shopIds.join(', ')}`, 'FirebaseService');
        return {
          success: false,
          error: 'No recipients found'
        };
      }

      // Collect all FCM tokens
      const allTokens = [];
      users.forEach(user => {
        if (user.fcmTokens && user.fcmTokens.length > 0) {
          allTokens.push(...user.fcmTokens.map(tokenObj => tokenObj.token));
        }
      });

      logInfo(`Sending push notification to ${users.length} shop owners (${allTokens.length} devices)`, 'FirebaseService');

      // Send notification
      return await this.sendPushNotification({
        recipientTokens: allTokens,
        ...notificationData
      });
    } catch (error) {
      logError(`Failed to send notification to shop owners: ${error.message}`, 'FirebaseService', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Send broadcast notification to all admins
   * @param {Object} notificationData - Notification content
   * @returns {Promise<Object>} Send result
   */
  async sendBroadcastToAdmins(notificationData) {
    try {
      const User = this.getModel('user');
      const adminUsers = await User.find({
        role: { $in: ['superAdmin', 'admin', 'employee'] },
        status: 'active',
        isSuspended: false,
        isDeleted: false,
        fcmTokens: { $exists: true, $ne: [] }
      }).select('fcmTokens role fullName status');

      if (adminUsers.length === 0) {
        return {
          success: false,
          error: 'No admin users with FCM tokens found'
        };
      }

      const allTokens = [];
      adminUsers.forEach(user => {
        if (user.fcmTokens && user.fcmTokens.length > 0) {
          allTokens.push(...user.fcmTokens.map(tokenObj => tokenObj.token));
        }
      });

      logInfo(`Broadcasting to ${adminUsers.length} admin users (${allTokens.length} devices)`, 'FirebaseService');

      return await this.sendPushNotification({
        recipientTokens: allTokens,
        ...notificationData
      });
    } catch (error) {
      logError(`Failed to broadcast to admins: ${error.message}`, 'FirebaseService', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Handle failed FCM tokens (remove invalid tokens)
   * @param {Array} tokens - Batch of tokens
   * @param {Array} responses - FCM responses
   */
  async handleFailedTokens(tokens, responses) {
    try {
      const invalidTokens = [];
      
      responses.forEach((response, index) => {
        if (!response.success) {
          const error = response.error;
          if (error.code === 'messaging/registration-token-not-registered' ||
              error.code === 'messaging/invalid-registration-token') {
            invalidTokens.push(tokens[index]);
          }
        }
      });

      if (invalidTokens.length > 0) {
        // Remove invalid tokens from user records using lazy-loaded model
        const User = this.getModel('user');
        await User.updateMany(
          { 'fcmTokens.token': { $in: invalidTokens } },
          { $pull: { fcmTokens: { token: { $in: invalidTokens } } } }
        );
        
        logInfo(`Removed ${invalidTokens.length} invalid FCM tokens`, 'FirebaseService');
      }
    } catch (error) {
      logError(`Failed to handle failed tokens: ${error.message}`, 'FirebaseService');
    }
  }

  /**
   * Test Firebase connection
   * @returns {Promise<Object>} Test result
   */
  async testConnection() {
    try {
      const initSuccess = await this.initialize();
      if (!initSuccess) {
        return {
          success: false,
          message: 'Firebase initialization failed'
        };
      }

      return {
        success: true,
        message: 'Firebase connection successful'
      };
    } catch (error) {
      return {
        success: false,
        message: `Firebase connection failed: ${error.message}`
      };
    }
  }

  /**
   * Map internal priority to valid Firebase priority
   * @param {string} priority - Internal priority
   * @returns {string} Valid Firebase priority
   */
  mapToFirebasePriority(priority) {
    const priorityMap = {
      'low': 'NORMAL',
      'medium': 'NORMAL', 
      'normal': 'NORMAL',
      'high': 'HIGH',
      'urgent': 'HIGH'
    };
    return priorityMap[priority] || 'NORMAL';
  }
}

// Export singleton instance
module.exports = new FirebaseService(); 