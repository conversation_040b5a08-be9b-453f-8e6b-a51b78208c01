"use client";

import { useState, useCallback, useEffect, useRef } from 'react';
import { toast } from 'sonner';
import PlanService from '@/lib/services/plan';

/**
 * Centralized Plans Hook
 * 
 * Comprehensive hook for all plan management operations
 * 100% aligned with backend Plan API and business logic
 */
export function usePlans(options = {}) {
  // Extract options with defaults
  const {
    autoFetch = true,
    includeInactive = true, // SuperAdmin sees all plans by default
    enableCache = true,
    showToastMessages = true,
    fetchDelay = 300,
    onPlansChange,
    onError
  } = options;

  // Main state management
  const [plans, setPlans] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [lastUpdated, setLastUpdated] = useState(null);

  // Filtering and pagination state
  const [filters, setFilters] = useState({
    searchQuery: '',
    typeFilter: 'all', // all, trial, monthly, yearly
    statusFilter: 'all', // all, active, inactive
    sortBy: 'displayOrder',
    sortOrder: 'asc'
  });

  // Use refs to prevent duplicate requests and track state
  const isLoadingRef = useRef(false);
  const filtersRef = useRef(filters);
  const isMountedRef = useRef(true);

  // Update refs when state changes
  filtersRef.current = filters;

  /**
   * Fetch all plans with current filters
   * Matches backend getAllPlans logic exactly
   */
  const fetchPlans = useCallback(async (showLoadingToast = false) => {
    // Prevent duplicate requests
    if (isLoadingRef.current) {
      console.log('[usePlans] Request already in progress, skipping...');
      return;
    }

    try {
      isLoadingRef.current = true;
      setLoading(true);
      setError(null);

      console.log('🔍 [usePlans] Starting fetchPlans with options:', {
        includeInactive,
        autoFetch,
        showLoadingToast,
        currentFilters: filtersRef.current
      });

      if (showLoadingToast && showToastMessages) {
        toast.info('Loading plans...');
      }

      console.log('[usePlans] Fetching plans with filters:', filtersRef.current);

      // Call our plan service with current filters
      console.log('🚀 [usePlans] Calling PlanService.getPlans...');
      const response = await PlanService.getPlans({
        includeInactive,
        search: filtersRef.current.searchQuery || undefined,
        type: filtersRef.current.typeFilter !== 'all' ? filtersRef.current.typeFilter : undefined,
        sortBy: filtersRef.current.sortBy,
        sortOrder: filtersRef.current.sortOrder
      });

      console.log('📦 [usePlans] Raw response from PlanService.getPlans:', response);

      if (response && response.success) {
        console.log('✅ [usePlans] Response success is true');
        let processedPlans = response.data || [];
        console.log('📋 [usePlans] Processing plans data:', processedPlans);

        // Apply frontend filtering (backend might not support all filters yet)
        if (filtersRef.current.statusFilter !== 'all') {
          const isActiveFilter = filtersRef.current.statusFilter === 'active';
          const beforeFilter = processedPlans.length;
          processedPlans = processedPlans.filter(plan => plan.isActive === isActiveFilter);
          console.log(`🔧 [usePlans] Applied status filter: ${beforeFilter} -> ${processedPlans.length} plans`);
        }

        // Apply search filter if backend doesn't handle it
        if (filtersRef.current.searchQuery) {
          const query = filtersRef.current.searchQuery.toLowerCase();
          const beforeSearch = processedPlans.length;
          processedPlans = processedPlans.filter(plan => 
            plan.name?.toLowerCase().includes(query) ||
            plan.displayName?.toLowerCase().includes(query) ||
            plan.description?.toLowerCase().includes(query)
          );
          console.log(`🔍 [usePlans] Applied search filter: ${beforeSearch} -> ${processedPlans.length} plans`);
        }

        // Sort plans (backend default is by displayOrder)
        processedPlans.sort((a, b) => {
          const aValue = a[filtersRef.current.sortBy] || 0;
          const bValue = b[filtersRef.current.sortBy] || 0;
          
          if (filtersRef.current.sortOrder === 'desc') {
            return bValue - aValue;
          }
          return aValue - bValue;
        });

        console.log('🎯 [usePlans] Setting plans state with:', processedPlans);
        setPlans(processedPlans);
        setLastUpdated(new Date());
        
        // IMPORTANT: Set loading to false immediately after setting data
        setLoading(false);
        isLoadingRef.current = false;
        
        if (showLoadingToast && showToastMessages) {
          toast.success(`Loaded ${processedPlans.length} plans`);
        }

        // Trigger callback if provided
        if (onPlansChange) {
          onPlansChange(processedPlans);
        }

        console.log('✅ [usePlans] Plans loaded successfully:', processedPlans.length, 'Loading state set to false');
      } else {
        console.log('❌ [usePlans] Response success is false or missing:', response);
        throw new Error(response?.message || 'Failed to load plans');
      }
    } catch (err) {
      console.error('💥 [usePlans] Error fetching plans:', err);
      
      if (isMountedRef.current) {
        const errorMessage = err.message || 'Failed to load plans';
        setError(errorMessage);

        if (showToastMessages && !err.message?.includes('throttled')) {
          toast.error(errorMessage);
        }

        // Trigger error callback if provided
        if (onError) {
          onError(err);
        }
      }
    } finally {
      if (isMountedRef.current) {
        console.log('🏁 [usePlans] Finishing fetchPlans, setting loading to false');
        setLoading(false);
        isLoadingRef.current = false;
      }
    }
  }, [includeInactive, showToastMessages, onPlansChange, onError]);

  /**
   * Get plan by ID with caching
   */
  const getPlanById = useCallback(async (planId) => {
    try {
      console.log('[usePlans] Getting plan by ID:', planId);
      
      const response = await PlanService.getPlanById(planId);
      
      if (response.success) {
        return response.data;
      } else {
        throw new Error(response.message || 'Plan not found');
      }
    } catch (err) {
      console.error('[usePlans] Error getting plan by ID:', err);
      
      if (showToastMessages) {
        toast.error(err.message || 'Failed to load plan');
      }
      
      return null;
    }
  }, [showToastMessages]);

  /**
   * Get plan by type (trial, monthly, yearly)
   */
  const getPlanByType = useCallback(async (planType) => {
    try {
      console.log('[usePlans] Getting plan by type:', planType);
      
      const response = await PlanService.getPlanByType(planType);
      
      if (response.success) {
        return response.data;
      } else {
        throw new Error(response.message || `No plan found for type: ${planType}`);
      }
    } catch (err) {
      console.error('[usePlans] Error getting plan by type:', err);
      
      if (showToastMessages) {
        toast.error(err.message || 'Failed to load plan');
      }
      
      return null;
    }
  }, [showToastMessages]);

  /**
   * Create new plan (SuperAdmin only)
   * Backend forces all features to true automatically
   */
  const createPlan = useCallback(async (planData) => {
    try {
      setLoading(true);
      
      if (showToastMessages) {
        toast.info('Creating plan...');
      }

      console.log('[usePlans] Creating plan:', planData);

      const response = await PlanService.createPlan(planData);

      if (response.success) {
        // Refresh plans list after creation
        await fetchPlans(false);
        
        if (showToastMessages) {
          toast.success(`Plan "${planData.displayName}" created successfully`);
        }

        return response.data;
      } else {
        throw new Error(response.message || 'Failed to create plan');
      }
    } catch (err) {
      console.error('[usePlans] Error creating plan:', err);
      
      if (showToastMessages) {
        toast.error(err.message || 'Failed to create plan');
      }
      
      throw err;
    } finally {
      setLoading(false);
    }
  }, [fetchPlans, showToastMessages]);

  /**
   * Update existing plan (SuperAdmin only)
   * Backend forces all features to true automatically
   */
  const updatePlan = useCallback(async (planId, updateData) => {
    try {
      setLoading(true);
      
      if (showToastMessages) {
        toast.info('Updating plan...');
      }

      console.log('[usePlans] Updating plan:', planId, updateData);

      const response = await PlanService.updatePlan(planId, updateData);

      if (response.success) {
        // Update local state immediately for better UX
        setPlans(prev => prev.map(plan => 
          (plan.planId || plan._id) === planId 
            ? { ...plan, ...updateData, ...response.data }
            : plan
        ));

        // Refresh plans list to get latest backend state
        await fetchPlans(false);
        
        if (showToastMessages) {
          toast.success('Plan updated successfully');
        }

        return response.data;
      } else {
        throw new Error(response.message || 'Failed to update plan');
      }
    } catch (err) {
      console.error('[usePlans] Error updating plan:', err);
      
      if (showToastMessages) {
        toast.error(err.message || 'Failed to update plan');
      }
      
      throw err;
    } finally {
      setLoading(false);
    }
  }, [fetchPlans, showToastMessages]);

  /**
   * Toggle plan status (SuperAdmin only)
   * Activates or deactivates a plan
   */
  const togglePlanStatus = useCallback(async (planId, isActive) => {
    try {
      setLoading(true);
      
      const statusText = isActive ? 'activating' : 'deactivating';
      if (showToastMessages) {
        toast.info(`${statusText.charAt(0).toUpperCase() + statusText.slice(1)} plan...`);
      }

      console.log('[usePlans] Toggling plan status:', planId, isActive);

      const response = await PlanService.togglePlanStatus(planId, isActive);

      if (response.success) {
        // Update local state immediately
        setPlans(prev => prev.map(plan => 
          (plan.planId || plan._id) === planId 
            ? { ...plan, isActive }
            : plan
        ));

        // Refresh plans list to get latest backend state
        await fetchPlans(false);
        
        if (showToastMessages) {
          toast.success(`Plan ${isActive ? 'activated' : 'deactivated'} successfully`);
        }

        return response.data;
      } else {
        throw new Error(response.message || 'Failed to toggle plan status');
      }
    } catch (err) {
      console.error('[usePlans] Error toggling plan status:', err);
      
      if (showToastMessages) {
        toast.error(err.message || 'Failed to toggle plan status');
      }
      
      throw err;
    } finally {
      setLoading(false);
    }
  }, [fetchPlans, showToastMessages]);

  /**
   * Delete plan (SuperAdmin only)
   * Backend performs soft delete
   */
  const deletePlan = useCallback(async (planId) => {
    try {
      setLoading(true);
      
      if (showToastMessages) {
        toast.info('Deleting plan...');
      }

      console.log('[usePlans] Deleting plan:', planId);

      const response = await PlanService.deletePlan(planId);

      if (response.success) {
        // Remove from local state immediately
        setPlans(prev => prev.filter(plan => (plan.planId || plan._id) !== planId));

        // Refresh plans list to get latest backend state
        await fetchPlans(false);
        
        if (showToastMessages) {
          toast.success('Plan deleted successfully');
        }

        return true;
      } else {
        throw new Error(response.message || 'Failed to delete plan');
      }
    } catch (err) {
      console.error('[usePlans] Error deleting plan:', err);
      
      if (showToastMessages) {
        toast.error(err.message || 'Failed to delete plan');
      }
      
      throw err;
    } finally {
      setLoading(false);
    }
  }, [fetchPlans, showToastMessages]);

  /**
   * Duplicate plan (SuperAdmin only)
   */
  const duplicatePlan = useCallback(async (planId, newName) => {
    try {
      setLoading(true);
      
      if (showToastMessages) {
        toast.info('Duplicating plan...');
      }

      console.log('[usePlans] Duplicating plan:', planId, newName);

      const response = await PlanService.duplicatePlan(planId, newName);

      if (response.success) {
        // Refresh plans list after duplication
        await fetchPlans(false);
        
        if (showToastMessages) {
          toast.success(`Plan duplicated as "${newName}"`);
        }

        return response.data;
      } else {
        throw new Error(response.message || 'Failed to duplicate plan');
      }
    } catch (err) {
      console.error('[usePlans] Error duplicating plan:', err);
      
      if (showToastMessages) {
        toast.error(err.message || 'Failed to duplicate plan');
      }
      
      throw err;
    } finally {
      setLoading(false);
    }
  }, [fetchPlans, showToastMessages]);

  /**
   * Get active plans only
   */
  const getActivePlans = useCallback(() => {
    return plans.filter(plan => plan.isActive && !plan.isDeleted);
  }, [plans]);

  /**
   * Get plans formatted for selection dropdowns
   */
  const getPlansForSelection = useCallback(() => {
    return getActivePlans()
      .sort((a, b) => (a.displayOrder || 1) - (b.displayOrder || 1))
      .map(plan => ({
        value: plan.planId || plan._id,
        label: plan.displayName || plan.name,
        type: plan.type,
        price: plan.pricing?.basePrice || 0,
        currency: plan.pricing?.currency || 'USD',
        billingCycle: plan.pricing?.billingCycle || 'monthly',
        isRecommended: plan.metadata?.isRecommended || false,
        description: plan.description || '',
        features: plan.features || {},
        limits: plan.limits || {}
      }));
  }, [getActivePlans]);

  /**
   * Update filters and refresh data
   */
  const updateFilters = useCallback((newFilters) => {
    console.log('[usePlans] Updating filters:', newFilters);
    setFilters(prev => ({ ...prev, ...newFilters }));
  }, []);

  /**
   * Clear all filters
   */
  const clearFilters = useCallback(() => {
    console.log('[usePlans] Clearing filters');
    setFilters({
      searchQuery: '',
      typeFilter: 'all',
      statusFilter: 'all',
      sortBy: 'displayOrder',
      sortOrder: 'asc'
    });
  }, []);

  /**
   * Refresh plans data
   */
  const refreshPlans = useCallback((showToast = false) => {
    return fetchPlans(showToast);
  }, [fetchPlans]);

  /**
   * Initialize default plans if none exist
   */
  const initializeDefaultPlans = useCallback(async () => {
    try {
      setLoading(true);
      
      if (showToastMessages) {
        toast.info('Initializing default plans...');
      }

      console.log('[usePlans] Initializing default plans');

      const response = await PlanService.initializeDefaultPlans();

      if (response.success) {
        if (response.created) {
          // Refresh plans list after initialization
          await fetchPlans(false);
          
          if (showToastMessages) {
            toast.success(response.message || 'Default plans created successfully');
          }
        } else {
          if (showToastMessages) {
            toast.info(response.message || 'Default plans already exist');
          }
        }

        return response.data;
      } else {
        throw new Error(response.message || 'Failed to initialize default plans');
      }
    } catch (err) {
      console.error('[usePlans] Error initializing default plans:', err);
      
      if (showToastMessages) {
        toast.error(err.message || 'Failed to initialize default plans');
      }
      
      throw err;
    } finally {
      setLoading(false);
    }
  }, [fetchPlans, showToastMessages]);

  // Auto-fetch on mount with delay to prevent React Strict Mode double calls
  useEffect(() => {
    if (!autoFetch) return;

    const timer = setTimeout(() => {
      fetchPlans();
    }, fetchDelay);

    return () => clearTimeout(timer);
  }, [autoFetch, fetchDelay, fetchPlans]);

  // Re-fetch when filters change
  useEffect(() => {
    if (!autoFetch) return;

    const timer = setTimeout(() => {
      fetchPlans();
    }, 300);

    return () => clearTimeout(timer);
  }, [filters, fetchPlans, autoFetch]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      isMountedRef.current = false;
    };
  }, []);

  // Return comprehensive hook interface
  return {
    // Data
    plans,
    loading,
    error,
    lastUpdated,
    filters,

    // Computed data
    activePlans: getActivePlans(),
    plansForSelection: getPlansForSelection(),

    // Basic operations
    fetchPlans,
    refreshPlans,
    getPlanById,
    getPlanByType,

    // CRUD operations (SuperAdmin only)
    createPlan,
    updatePlan,
    deletePlan,
    togglePlanStatus,
    duplicatePlan,

    // Utility operations
    initializeDefaultPlans,
    getActivePlans,
    getPlansForSelection,

    // Filter management
    updateFilters,
    clearFilters,
    setFilters: updateFilters,

    // Aliases for compatibility
    refetch: refreshPlans,
    refresh: refreshPlans
  };
} 