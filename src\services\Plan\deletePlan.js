/**
 * Soft delete a plan
 * Marks a plan as deleted rather than removing it from the database
 */
const { logSuccess, logError } = require('../../utils');
const getPlanById = require('./getPlanById');

/**
 * Soft delete a plan
 * @param {string} planId - ID of the plan to delete
 * @param {Object} options - Additional options
 * @returns {Promise<Object>} Deleted plan
 */
const deletePlan = async (planId, options = {}) => {
  try {
    const plan = await getPlanById(planId);
    
    // Soft delete
    plan.isActive = false;
    plan.isDeleted = true;
    
    const deletedPlan = await plan.save();
    logSuccess(`Deleted plan: ${planId}`, 'PlanService');
    
    return deletedPlan;
  } catch (error) {
    logError(`Failed to delete plan: ${planId}`, 'PlanService', error);
    throw error;
  }
};

module.exports = deletePlan;
