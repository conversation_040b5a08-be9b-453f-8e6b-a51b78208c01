import apiBridge from '@/lib/api/bridge';
import { ENDPOINTS } from '@/lib/api/contract';
import { toast } from 'sonner';
import BaseService from '@/lib/services/baseService';

/**
 * Update an existing user
 * @param {string} userId - ID of the user to update
 * @param {Object} userData - Updated user data
 * @returns {Promise<Object>} Updated user object
 */
async function updateUser(userId, userData) {
  try {
    // Make API request using the bridge
    const response = await apiBridge.put(`${ENDPOINTS.USERS.BASE}/${userId}`, userData, {
      clearCacheEndpoint: ENDPOINTS.USERS.BASE
    });
    
    // Process response
    if (response.data && response.data.success) {
      const updatedUser = response.data.data.user;
      toast.success('User updated successfully');
      
      // Return updated user data
      return updatedUser;
    }
    
    // Handle unexpected response
    console.error('Unexpected API response format:', response.data);
    toast.error('Failed to update user: Unexpected response format');
    return null;
  } catch (error) {
    console.error(`Error updating user ${userId}:`, error);
    // Use standardized error handling
    BaseService.handleError(error, 'UserService.updateUser', true);
    throw error;
  }
}

export default updateUser;
