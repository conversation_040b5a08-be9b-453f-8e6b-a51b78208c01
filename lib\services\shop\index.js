/**
 * SuperAdmin Shop Service - Aggregates all SuperAdmin shop-related service methods
 * 
 * This service is specifically designed for SuperAdmin operations using the
 * /api/admin/shops endpoints with proper authentication and payload structure.
 */

import getShops from './getShops';
import getShopById from './getShopById';
import createShop from './createShop';
import updateShop from './updateShop';
import suspendShop from './suspendShop';
import reactivateShop from './reactivateShop';
import deleteShop from './deleteShop';
import getShopStats from './getShopStats';
import apiBridge from '@/lib/api/bridge';
import { ENDPOINTS } from '@/lib/api/contract';
import { toast } from 'sonner';
import BaseService from '@/lib/services/baseService';

/**
 * SuperAdminShopService exports all SuperAdmin shop-related API operations
 * All methods require SuperAdmin authentication and use /api/admin/shops endpoints
 */
const SuperAdminShopService = {
  // Core CRUD operations
  getShops,
  getShopById,
  createShop,
  updateShop,
  deleteShop,
  
  // Status management
  suspendShop,
  reactivateShop,
  
  // Analytics
  getShopStats,
  
  /**
   * Upload a logo for a shop (SuperAdmin)
   * @param {string} shopId - ID of the shop
   * @param {File} logoFile - Logo file to upload
   * @returns {Promise<Object>} Updated logo URL
   */
  async uploadLogo(shopId, logoFile) {
    try {
      // Validate file
      if (!logoFile || !(logoFile instanceof File)) {
        toast.error('Please provide a valid logo file');
        return null;
      }
      
      // Check file type
      const allowedTypes = ['image/jpeg', 'image/png', 'image/jpg', 'image/webp'];
      if (!allowedTypes.includes(logoFile.type)) {
        toast.error('Invalid file type. Only JPG, PNG, and WebP files are allowed.');
        return null;
      }
      
      // Check file size (5MB limit)
      if (logoFile.size > 5 * 1024 * 1024) {
        toast.error('File size too large. Maximum size is 5MB.');
        return null;
      }
      
      const formData = new FormData();
      formData.append('shopLogo', logoFile);
      
      const response = await apiBridge.put(`${ENDPOINTS.SHOPS.BASE}/${shopId}/logo`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        },
        clearCacheEndpoint: ENDPOINTS.SHOPS.BASE
      });
      
      if (response.data && response.data.success) {
        toast.success('Shop logo updated successfully');
        return {
          logoUrl: response.data.data.logoUrl,
          updatedAt: response.data.data.updatedAt || new Date().toISOString()
        };
      }
      
      console.error('[SuperAdminShopService] Unexpected API response format:', response.data);
      toast.error('Failed to update shop logo');
      return null;
    } catch (error) {
      console.error(`[SuperAdminShopService] Error updating shop logo:`, error);
      
      // Enhanced error handling for SuperAdmin operations
      if (error.response?.status === 400 && error.response?.data?.message) {
        BaseService.handleError({ message: `Validation error: ${error.response.data.message}` }, 'SuperAdminShopService.uploadLogo', true);
      } else if (error.response?.status === 401) {
        BaseService.handleError({ message: 'Unauthorized: SuperAdmin access required' }, 'SuperAdminShopService.uploadLogo', true);
      } else if (error.response?.status === 403) {
        BaseService.handleError({ message: 'Forbidden: Only SuperAdmins can update shop logos' }, 'SuperAdminShopService.uploadLogo', true);
      } else if (error.response?.status === 404) {
        BaseService.handleError({ message: 'Shop not found' }, 'SuperAdminShopService.uploadLogo', true);
      } else {
        BaseService.handleError(error, 'SuperAdminShopService.uploadLogo', true);
      }
      
      return null;
    }
  },
  
  /**
   * Change shop status (SuperAdmin) - Unified status management
   * @param {string} shopId - ID of the shop
   * @param {string} status - New status ('active', 'suspended', 'inactive')
   * @param {string} reason - Reason for status change (required for suspension/deactivation)
   * @param {boolean} sendEmail - Whether to send email notification
   * @param {number} duration - Duration in days for suspension (optional)
   * @returns {Promise<Object>} Updated shop status
   */
  async changeShopStatus(shopId, status, reason = '', sendEmail = true, duration = null) {
    try {
      // Validate required parameters
      if (!shopId || !status) {
        toast.error('Shop ID and status are required');
        return null;
      }
      
      // Validate reason for suspension/deactivation  
      if ((status === 'suspended' || status === 'inactive') && (!reason || reason.trim().length < 3)) {
        toast.error('Please provide a valid reason (minimum 3 characters) for suspension/deactivation');
        return null;
      }
      
      // Prepare request payload
      const payload = {
        status,
        reason,
        sendEmail
      };
      
      // Add duration for suspension
      if (status === 'suspended' && duration) {
        payload.duration = duration;
      }
      
      const response = await apiBridge.put(`${ENDPOINTS.SHOPS.BASE}/${shopId}/status`, payload, {
        clearCacheEndpoint: ENDPOINTS.SHOPS.BASE
      });
      
      if (response.data && response.data.success) {
        const updatedShop = response.data.data;
        toast.success(`Shop ${status === 'active' ? 'activated' : status} successfully`);
        
        return {
          id: updatedShop.shopId || updatedShop._id,
          shopId: updatedShop.shopId || updatedShop._id,
          shopName: updatedShop.shopName,
          status: updatedShop.status,
          statusDetails: {
            reason,
            changedAt: new Date().toISOString(),
            changedBy: 'superAdmin',
            duration
          },
          updatedAt: updatedShop.updatedAt || new Date().toISOString()
        };
      }
      
      console.error('[SuperAdminShopService] Unexpected API response format:', response.data);
      toast.error(`Failed to change shop status: Unexpected response format`);
      return null;
    } catch (error) {
      console.error(`[SuperAdminShopService] Error changing shop status:`, error);
      
      // Enhanced error handling
      if (error.response?.status === 400 && error.response?.data?.message) {
        BaseService.handleError({ message: `Validation error: ${error.response.data.message}` }, 'SuperAdminShopService.changeShopStatus', true);
      } else if (error.response?.status === 401) {
        BaseService.handleError({ message: 'Unauthorized: SuperAdmin access required' }, 'SuperAdminShopService.changeShopStatus', true);
      } else if (error.response?.status === 403) {
        BaseService.handleError({ message: 'Forbidden: Only SuperAdmins can change shop status' }, 'SuperAdminShopService.changeShopStatus', true);
      } else if (error.response?.status === 404) {
        BaseService.handleError({ message: 'Shop not found' }, 'SuperAdminShopService.changeShopStatus', true);
      } else {
        BaseService.handleError(error, 'SuperAdminShopService.changeShopStatus', true);
      }
      
      return null;
    }
  },
  
  // Helper methods for common operations
  /**
   * Activate a shop (SuperAdmin) - Helper method
   * @param {string} shopId - Shop ID
   * @param {string} reason - Activation reason
   * @param {boolean} sendEmail - Whether to send email notification
   * @returns {Promise<Object>} Updated shop status
   */
  async activateShop(shopId, reason = 'Activated by SuperAdmin', sendEmail = true) {
    return this.changeShopStatus(shopId, 'active', reason, sendEmail);
  },
  
  /**
   * Deactivate a shop (SuperAdmin) - Helper method
   * @param {string} shopId - Shop ID
   * @param {string} reason - Deactivation reason (required)
   * @param {boolean} sendEmail - Whether to send email notification
   * @returns {Promise<Object>} Updated shop status
   */
  async deactivateShop(shopId, reason, sendEmail = true) {
    return this.changeShopStatus(shopId, 'inactive', reason, sendEmail);
  },
  
  /**
   * Get shop counts by status (SuperAdmin) - Helper method
   * @returns {Promise<Object>} Shop counts by status
   */
  async getShopCounts() {
    try {
      const stats = await this.getShopStats();
      
      if (stats) {
        return {
          total: stats.total || 0,
          active: stats.active || 0,
          suspended: stats.suspended || 0,
          inactive: stats.inactive || 0,
          verified: stats.verified || 0,
          unverified: stats.unverified || 0
        };
      }
      
      return {
        total: 0,
        active: 0,
        suspended: 0,
        inactive: 0,
        verified: 0,
        unverified: 0
      };
    } catch (error) {
      console.error('[SuperAdminShopService] Error getting shop counts:', error);
      return {
        total: 0,
        active: 0,
        suspended: 0,
        inactive: 0,
        verified: 0,
        unverified: 0
      };
    }
  },
  
  // Deprecated methods (for backward compatibility - will be removed)
  /**
   * @deprecated Use createShop instead
   */
  async createNewShop(shopData) {
    console.warn('[SuperAdminShopService] createNewShop is deprecated, use createShop instead');
    return this.createShop(shopData);
  },
  
  /**
   * @deprecated Use uploadLogo instead
   */
  async updateShopLogo(shopId, logoFile) {
    console.warn('[SuperAdminShopService] updateShopLogo is deprecated, use uploadLogo instead');
    return this.uploadLogo(shopId, logoFile);
  }
};

export default SuperAdminShopService;
