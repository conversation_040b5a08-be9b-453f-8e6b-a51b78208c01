name: 🚀 Auto Deploy to VPS

on:
  push:
    branches:
      - main

jobs:
  deploy:
    runs-on: ubuntu-latest

    steps:
      - name: 📥 Checkout Code
        uses: actions/checkout@v3

      - name: 🧪 Print Debug Info
        run: |
          echo "HOST: ${{ secrets.SSH_HOST }}"
          echo "USER: ${{ secrets.SSH_USER }}"
          echo "Current directory: $(pwd)"
          ssh -V

      - name: 🔐 Setup SSH Agent
        uses: webfactory/ssh-agent@v0.9.0
        with:
          ssh-private-key: ${{ secrets.SSH_PRIVATE_KEY }}

      - name: 🚀 Deploy to Server
        run: |
          ssh-keyscan -H ${{ secrets.SSH_HOST }} >> ~/.ssh/known_hosts
          ssh -o StrictHostKeyChecking=no ${{ secrets.SSH_USER }}@${{ secrets.SSH_HOST }} "
            echo '=== Starting Deployment ==='
            echo 'Current user: '$(whoami)
            echo 'Current directory: '$(pwd)
            
            echo '=== Navigating to app directory ==='
            cd /var/www/deyncare-backend.khanciye.com
            pwd
            
            echo '=== Pulling latest code ==='
            git pull origin main
            
            echo '=== Installing dependencies ==='
            npm install --production
            
            echo '=== Restarting PM2 ==='
            pm2 restart all
            
            echo '=== Deployment completed ==='
            pm2 status
          " 