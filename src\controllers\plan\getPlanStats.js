/**
 * Get Plan Statistics for SuperAdmin Dashboard
 * Provides usage data for each plan, including:
 * - Number of active subscriptions
 * - Revenue metrics
 * - Usage trends
 * GET /api/plans/stats
 */
const { Plan } = require('../../models');
const { SubscriptionService } = require('../../services');
const { logInfo, logError } = require('../../utils');

const getPlanStats = async (req, res, next) => {
  try {
    logInfo('Fetching plan statistics for SuperAdmin dashboard', 'PlanController');
    
    // Only SuperAdmin can access this endpoint
    if (req.user.role !== 'superAdmin') {
      return res.status(403).json({
        success: false,
        message: 'Access denied. SuperAdmin privileges required.'
      });
    }
    
    // Get all plans excluding soft-deleted ones for accurate stats
    const plans = await Plan.find({ isDeleted: { $ne: true } });
    
    // For each plan, get usage statistics
    let mostUsedPlan = null;
    let maxActiveSubscriptions = 0;
    let activePlansCount = 0;
    let pendingPlansCount = 0;

    const planStats = await Promise.all(plans.map(async (plan) => {
      // Get active subscriptions count for this plan
      // Count both 'active' and 'trial' subscriptions for usage stats
      const activeSubscriptions = await SubscriptionService.countSubscriptionsByPlanId(plan.planId, { status: ['active', 'trial'] });
      
      // Track most used plan
      if (activeSubscriptions > maxActiveSubscriptions) {
        maxActiveSubscriptions = activeSubscriptions;
        mostUsedPlan = {
          planId: plan.planId,
          name: plan.name,
          displayName: plan.displayName,
          type: plan.type,
          activeSubscriptions
        };
      }

      // Count active and pending plans
      if (plan.isActive) {
        activePlansCount++;
      } else {
        pendingPlansCount++;
      }

      // Get revenue data (last 30 days)
      const revenueData = await SubscriptionService.getPlanRevenue(plan.planId, 30);
      
      // Get expiring soon subscriptions (next 7 days)
      const expiringSoon = await SubscriptionService.countSubscriptionsByPlanId(plan.planId, { 
        status: 'active',
        expiresWithin: 7 // days
      });
      
      return {
        planId: plan.planId,
        name: plan.name,
        displayName: plan.displayName,
        type: plan.type,
        isActive: plan.isActive,
        stats: {
          activeSubscriptions,
          revenue: revenueData.total,
          expiringSoon,
          conversionRate: revenueData.conversionRate,
          averageLifetime: revenueData.averageLifetime
        }
      };
    }));

    const totalPlans = plans.length;
    
    // Calculate totals for the frontend
    const totalSubscriptions = planStats.reduce((sum, plan) => sum + plan.stats.activeSubscriptions, 0);
    const totalRevenue = planStats.reduce((sum, plan) => sum + plan.stats.revenue, 0);

    return res.status(200).json({
      success: true,
      summary: {
        totalPlans,
        activePlans: activePlansCount,
        pendingPlans: pendingPlansCount,
        mostUsedPlan
      },
      totals: {
        totalSubscriptions,
        totalRevenue
      },
      data: planStats
    });
  } catch (error) {
    logError('Failed to fetch plan statistics', 'PlanController', error);
    return next(error);
  }
};

module.exports = getPlanStats;
