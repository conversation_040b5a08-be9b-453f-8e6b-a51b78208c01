const cron = require('node-cron');
const Debt = require('../models/debt.model');
const Customer = require('../models/customer.model');
const { Setting } = require('../models');
const mlService = require('../services/mlRiskService');
const smsService = require('../services/smsService');

/**
 * ML Risk Evaluation Cron Job
 * 
 * Runs every hour to:
 * - Step 2: Check if due dates have arrived
 * - Step 3-5: Trigger ML evaluation for overdue debts
 * - Step 6: Send SMS reminders
 */

class MLRiskEvaluationCron {
  constructor() {
    this.isRunning = false;
    this.settingsCache = {};
    this.lastSettingsUpdate = null;
    this.settingsCacheExpiry = 5 * 60 * 1000; // 5 minutes
  }

  /**
   * Load ML cron settings from database
   */
  async loadCronSettings() {
    try {
      const now = new Date();
      
      // Check if cache is still valid
      if (this.lastSettingsUpdate && 
          (now - this.lastSettingsUpdate) < this.settingsCacheExpiry) {
        return this.settingsCache;
      }

      // Fetch ML cron settings
      const mlSettings = await Setting.find({
        category: 'ml',
        key: { $in: ['ml_enabled', 'ml_cron_enabled', 'ml_data_retention_days'] },
        shopId: null // Global settings only
      }).lean();

      // Convert to cache object
      const settingsMap = {};
      mlSettings.forEach(setting => {
        settingsMap[setting.key] = setting.value;
      });

      this.settingsCache = settingsMap;
      this.lastSettingsUpdate = now;

      return this.settingsCache;

    } catch (error) {
      console.error('❌ Failed to load ML cron settings:', error.message);
      return {};
    }
  }

  /**
   * Check if ML cron is enabled
   */
  async isCronEnabled() {
    const settings = await this.loadCronSettings();
    return settings.ml_enabled && settings.ml_cron_enabled;
  }

  /**
   * Start the cron job - runs every hour
   */
  start() {
    // Only log startup message in development
    if (process.env.NODE_ENV === 'development') {
      console.log('🤖 ML Risk Evaluation Cron Job Started');
    }
    
    // Run every hour at minute 0
    cron.schedule('0 * * * *', async () => {
      if (this.isRunning) {
        return; // Skip silently if already running
      }

      // Check if ML cron is enabled
      const isEnabled = await this.isCronEnabled();
      if (!isEnabled) {
        return; // Skip silently if disabled
      }

      this.isRunning = true;
      console.log('🔄 Starting ML risk evaluation cycle...');
      
      try {
        await this.evaluateOverdueDebts();
        await this.sendReminderNotifications();
        console.log('✅ ML evaluation cycle completed');
      } catch (error) {
        console.error('❌ ML evaluation cycle failed:', error);
      } finally {
        this.isRunning = false;
      }
    });

    // Also run immediately on startup for testing
    if (process.env.NODE_ENV === 'development') {
      setTimeout(() => this.evaluateOverdueDebts(), 5000);
    }
  }

  /**
   * Step 2-5: Check due dates and trigger ML evaluation
   */
  async evaluateOverdueDebts() {
    try {
      // Check if ML is enabled before processing
      const isEnabled = await this.isCronEnabled();
      if (!isEnabled) {
        return { processed: 0, skipped: true, reason: 'ML disabled' };
      }

      const now = new Date();
      
      // Find debts where due date has passed but no ML evaluation yet
      const overdueDebts = await Debt.find({
        DueDate: { $lt: now },
        OutstandingDebt: { $gt: 0 },
        RiskLevel: 'Active Debt', // Not yet ML evaluated
        isDeleted: false
      }).populate('customerId', 'CustomerName phone CustomerType');

      console.log(`📊 Found ${overdueDebts.length} debts due for ML evaluation`);

      const evaluationResults = {
        processed: 0,
        highRisk: 0,
        mediumRisk: 0,
        lowRisk: 0,
        errors: 0
      };

      for (const debt of overdueDebts) {
        try {
          // Get customer data
          const customer = await Customer.findOne({ customerId: debt.customerId });
          if (!customer) {
            console.error(`❌ Customer not found for debt ${debt.debtId}`);
            evaluationResults.errors++;
            continue;
          }

          // Step 3-5: ML Risk Evaluation
          const evaluation = await mlService.evaluateRisk(debt, customer);
          
          // Update debt with ML results
          debt.RiskLevel = evaluation.riskLevel;
          await debt.save();

          // Update customer risk profile
          customer.riskProfile.currentRiskLevel = evaluation.riskLevel;
          customer.riskProfile.riskScore = evaluation.riskScore;
          customer.riskProfile.lastAssessment = new Date();
          await customer.save();

          // Count by risk level
          switch (evaluation.riskLevel) {
            case 'High Risk':
              evaluationResults.highRisk++;
              break;
            case 'Medium Risk':
              evaluationResults.mediumRisk++;
              break;
            case 'Low Risk':
              evaluationResults.lowRisk++;
              break;
          }

          evaluationResults.processed++;

          console.log(`✅ ${debt.CustomerName} (${debt.debtId}): ${evaluation.riskLevel} (Score: ${evaluation.riskScore})`);

        } catch (error) {
          console.error(`❌ Error evaluating debt ${debt.debtId}:`, error.message);
          evaluationResults.errors++;
        }
      }

      // Log summary
      console.log('📈 ML Evaluation Summary:', {
        processed: evaluationResults.processed,
        highRisk: evaluationResults.highRisk,
        mediumRisk: evaluationResults.mediumRisk,
        lowRisk: evaluationResults.lowRisk,
        errors: evaluationResults.errors
      });

      return evaluationResults;

    } catch (error) {
      console.error('❌ Batch ML evaluation error:', error);
      throw error;
    }
  }

  /**
   * Step 6: Send SMS reminders based on ML risk levels
   */
  async sendReminderNotifications() {
    try {
      const now = new Date();
      
      // Get overdue debts that need reminders
      const overdueDebts = await Debt.find({
        DueDate: { $lt: now },
        OutstandingDebt: { $gt: 0 },
        RiskLevel: { $in: ['High Risk', 'Medium Risk'] },
        isDeleted: false
      });

      console.log(`📱 Sending reminders for ${overdueDebts.length} overdue debts`);

      const reminderResults = {
        sent: 0,
        failed: 0
      };

      for (const debt of overdueDebts) {
        try {
          const customer = await Customer.findOne({ customerId: debt.customerId });
          if (!customer || !customer.phone) {
            console.error(`❌ No phone number for customer ${debt.CustomerName}`);
            reminderResults.failed++;
            continue;
          }

          // Calculate days overdue
          const daysOverdue = Math.ceil((now - new Date(debt.DueDate)) / (1000 * 60 * 60 * 24));
          
          // Generate appropriate message based on risk level
          let message;
          if (debt.RiskLevel === 'High Risk') {
            message = `URGENT: Dear ${debt.CustomerName}, your payment of $${debt.OutstandingDebt} is ${daysOverdue} days OVERDUE. Immediate payment is required! Contact us immediately.`;
          } else {
            message = `Dear ${debt.CustomerName}, your payment of $${debt.OutstandingDebt} is ${daysOverdue} days overdue. Please pay as soon as possible. Thank you!`;
          }

          // Send SMS (if SMS service is available)
          if (smsService && typeof smsService.sendSMS === 'function') {
            await smsService.sendSMS(customer.phone, message);
            reminderResults.sent++;
            console.log(`📱 SMS sent to ${debt.CustomerName}: ${debt.RiskLevel}`);
          } else {
            console.log(`📱 Would send SMS to ${debt.CustomerName} (${customer.phone}): ${message}`);
            reminderResults.sent++;
          }

        } catch (error) {
          console.error(`❌ Error sending reminder for debt ${debt.debtId}:`, error.message);
          reminderResults.failed++;
        }
      }

      console.log('📱 SMS Reminder Summary:', reminderResults);
      return reminderResults;

    } catch (error) {
      console.error('❌ SMS reminder error:', error);
      throw error;
    }
  }

  /**
   * Manual trigger for immediate evaluation (for testing)
   */
  async triggerManualEvaluation(shopId = null) {
    console.log('🔧 Manual ML evaluation triggered');
    
    if (this.isRunning) {
      throw new Error('Evaluation already in progress');
    }

    this.isRunning = true;
    
    try {
      const results = await this.evaluateOverdueDebts();
      return results;
    } finally {
      this.isRunning = false;
    }
  }

  /**
   * Get evaluation status
   */
  async getStatus() {
    const settings = await this.loadCronSettings();
    const isEnabled = await this.isCronEnabled();
    
    return {
      isRunning: this.isRunning,
      isEnabled: isEnabled,
      mlEnabled: settings.ml_enabled || false,
      cronEnabled: settings.ml_cron_enabled || false,
      nextRun: this.isRunning ? null : (isEnabled ? 'Next hour at :00 minutes' : 'Disabled'),
      lastSettingsUpdate: this.lastSettingsUpdate
    };
  }
}

// Create singleton instance
const mlRiskEvaluationCron = new MLRiskEvaluationCron();

module.exports = mlRiskEvaluationCron; 