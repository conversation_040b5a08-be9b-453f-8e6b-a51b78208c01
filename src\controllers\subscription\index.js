/**
 * Subscription Controller Index
 * Exports all subscription-related controller functions
 */

// Import individual controller functions
const getSubscriptionById = require('./getSubscriptionById');
const getCurrentSubscription = require('./getCurrentSubscription');
const getSubscriptionHistory = require('./getSubscriptionHistory');
const changePlan = require('./changePlan');
const upgradeFromTrial = require('./upgradeFromTrial');
const requestUpgrade = require('./requestUpgrade');
const recordPayment = require('./recordPayment');
const cancelSubscription = require('./cancelSubscription');
const updateAutoRenewal = require('./updateAutoRenewal');
const renewSubscription = require('./renewSubscription');
const extendSubscription = require('./extendSubscription');
const getAllSubscriptions = require('./getAllSubscriptions');

// Import SuperAdmin controllers
const getSubscriptionStats = require('./getSubscriptionStats');
const bulkUpdateSubscriptions = require('./bulkUpdateSubscriptions');
const manageCronTasks = require('./manageCronTasks');
const managePaymentRetries = require('./managePaymentRetries');

// Export all controller functions
module.exports = {
  getSubscriptionById,
  getCurrentSubscription,
  getSubscriptionHistory,
  changePlan,
  upgradeFromTrial,
  requestUpgrade,

  recordPayment,
  cancelSubscription,
  updateAutoRenewal,
  renewSubscription,
  extendSubscription,
  getAllSubscriptions,




  // SuperAdmin functions
  getSubscriptionStats,
  bulkUpdateSubscriptions,
  manageCronTasks,
  // Payment retry management
  getPaymentRetryStatus: managePaymentRetries.getPaymentRetryStatus,
  triggerManualRetry: managePaymentRetries.triggerManualRetry,
  cancelScheduledRetries: managePaymentRetries.cancelScheduledRetries,
  processAllPendingRetries: managePaymentRetries.processAllPendingRetries,
  getRetryConfiguration: managePaymentRetries.getRetryConfiguration
};
