import apiBridge from '@/lib/api/bridge';
import BaseService from '../baseService';

const resumeSubscription = async (subscriptionId, resumeData) => {
  try {
    const response = await apiBridge.patch(
      ENDPOINTS.subscription.resumeSubscription.replace(':subscriptionId', subscriptionId),
      resumeData
    );
    
    return BaseService.processApiResponse(response);
  } catch (error) {
    throw BaseService.handleApiError(error);
  }
};

export default resumeSubscription; 
