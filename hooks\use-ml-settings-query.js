/**
 * ML Settings Query Hook
 * Uses backend-matching services for admin-accessible ML settings
 * Maps exactly to backend settings: ml_enabled, ml_auto_trigger_on_due, etc.
 */

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { 
  fetchMLSettings,
  updateMLSettings
} from '../lib/services/settings';

/**
 * Hook for managing admin-accessible ML settings
 * @returns {Object} ML settings query result and mutation functions
 */
export const useMLSettingsQuery = () => {
  const queryClient = useQueryClient();

  // Query for ML settings
  const mlSettingsQuery = useQuery({
    queryKey: ['mlSettings'],
    queryFn: () => fetchMLSettings(),
    staleTime: 5 * 60 * 1000, // 5 minutes
    cacheTime: 10 * 60 * 1000, // 10 minutes
    refetchOnWindowFocus: false,
    retry: 2,
    onError: (error) => {
      console.error('[useMLSettingsQuery] ML settings fetch error:', error);
    }
  });

  // Mutation for updating ML settings
  const updateMLSettingsMutation = useMutation({
    mutationFn: updateMLSettings,
    onSuccess: (data) => {
      console.log('[useMLSettingsQuery] ML settings updated:', data);
      // Invalidate and refetch ML settings
      queryClient.invalidateQueries(['mlSettings']);
    },
    onError: (error) => {
      console.error('[useMLSettingsQuery] ML settings update error:', error);
    }
  });

  // Computed values
  const isLoading = mlSettingsQuery.isLoading;
  const isError = mlSettingsQuery.isError;
  const error = mlSettingsQuery.error;

  // ML settings data (exact backend format)
  const mlSettingsData = mlSettingsQuery.data || {
    mlEnabled: false,
    autoTriggerOnDue: false,
    autoTriggerOnPaymentUpdate: false,
    storeRiskScoreInDB: false
  };

  return {
    // ML Settings Data
    mlSettings: mlSettingsData,
    
    // Loading States
    isLoading,
    isLoadingMLSettings: mlSettingsQuery.isLoading,
    
    // Error States
    isError,
    error,
    mlSettingsError: mlSettingsQuery.error,
    
    // Mutation States
    isSavingMLSettings: updateMLSettingsMutation.isLoading,
    
    // Mutation Functions
    updateMLSettings: updateMLSettingsMutation.mutateAsync,
    
    // Refetch Functions
    refetchMLSettings: mlSettingsQuery.refetch
  };
}; 