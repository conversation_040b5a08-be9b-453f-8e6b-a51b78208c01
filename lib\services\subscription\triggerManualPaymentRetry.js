import apiBridge from '@/lib/api/bridge';
import { ENDPOINTS } from '@/lib/api/contract';;
import { handleError, processApiResponse } from '../baseService';

/**
 * Trigger manual payment retry (SuperAdmin only)
 * @param {string} subscriptionId - Subscription ID
 * @returns {Promise<Object>} Payment retry trigger result
 */
async function triggerManualPaymentRetry(subscriptionId) {
  try {
    if (!subscriptionId) {
      throw new Error('Subscription ID is required');
    }

    const response = await apiBridge.post(`${ENDPOINTS.SUBSCRIPTIONS.BASE}/payment-retry/${subscriptionId}/trigger`, {}, {
      clearCacheEndpoint: ENDPOINTS.SUBSCRIPTIONS.BASE
    });
    
    const result = processApiResponse(response, 'Manual payment retry triggered successfully');
    return result;
  } catch (error) {
    handleError(error, 'SubscriptionService.triggerManualPaymentRetry', true);
    throw error;
  }
}

export default triggerManualPaymentRetry; 
