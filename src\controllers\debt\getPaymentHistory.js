const { Payment, Debt } = require('../../models');
const { AppError, logError } = require('../../utils');

/**
 * Get Payment History for Debt
 * GET /api/debts/:debtId/payments
 */
const getPaymentHistory = async (req, res, next) => {
  try {
    const { debtId } = req.params;
    const shopId = req.user.shopId;
    
    // Query parameters
    const {
      page = 1,
      limit = 20,
      sortBy = 'paymentDate',
      sortOrder = 'desc'
    } = req.query;

    // Verify debt exists and belongs to shop
    const debt = await Debt.findOne({ 
      debtId, 
      shopId, 
      isDeleted: false 
    }).lean();

    if (!debt) {
      return res.status(404).json({
        success: false,
        message: 'Debt not found'
      });
    }

    // Sort options
    const sortOptions = {};
    sortOptions[sortBy] = sortOrder === 'desc' ? -1 : 1;

    // Pagination
    const skip = (parseInt(page) - 1) * parseInt(limit);
    const limitNum = parseInt(limit);

    // Get payment history
    const payments = await Payment.find({
      debtId,
      shopId,
      paymentContext: 'debt',
      isDeleted: false
    })
    .sort(sortOptions)
    .skip(skip)
    .limit(limitNum)
    .lean();

    // Get total count for pagination
    const totalPayments = await Payment.countDocuments({
      debtId,
      shopId,
      paymentContext: 'debt',
      isDeleted: false
    });

    const totalPages = Math.ceil(totalPayments / limitNum);

    // Calculate payment statistics
    const paymentStats = await Payment.aggregate([
      {
        $match: {
          debtId,
          shopId,
          paymentContext: 'debt',
          isDeleted: false
        }
      },
      {
        $group: {
          _id: null,
          totalPayments: { $sum: 1 },
          totalAmount: { $sum: '$amount' },
          averagePayment: { $avg: '$amount' },
          firstPaymentDate: { $min: '$paymentDate' },
          lastPaymentDate: { $max: '$paymentDate' },
          onTimePayments: {
            $sum: {
              $cond: [{ $eq: ['$IsOnTime', true] }, 1, 0]
            }
          }
        }
      }
    ]);

    // Format payment data
    const formattedPayments = payments.map(payment => ({
      paymentId: payment.paymentId,
      amount: payment.amount,
      paymentDate: payment.paymentDate,
      paymentMethod: payment.paymentMethod,
      
      // ML timing fields
      isOnTime: payment.IsOnTime,
      paymentDelay: payment.PaymentDelay || 0,
      
      // Reference and tracking
      receiptNumber: payment.receiptNumber,
      reference: payment.reference,
      notes: payment.notes,
      
      // System fields
      recordedBy: payment.recordedBy,
      createdAt: payment.createdAt
    }));

    const stats = paymentStats[0] || {
      totalPayments: 0,
      totalAmount: 0,
      averagePayment: 0,
      firstPaymentDate: null,
      lastPaymentDate: null,
      onTimePayments: 0
    };

    res.json({
      success: true,
      message: 'Payment history retrieved successfully',
      data: {
        debtInfo: {
          debtId: debt.debtId,
          customerName: debt.CustomerName,
          debtAmount: debt.DebtAmount,
          outstandingDebt: debt.OutstandingDebt,
          paidAmount: debt.PaidAmount || 0,
          dueDate: debt.DueDate,
          riskLevel: debt.RiskLevel
        },
        
        payments: formattedPayments,
        
        pagination: {
          currentPage: parseInt(page),
          totalPages,
          totalPayments,
          hasNextPage: parseInt(page) < totalPages,
          hasPrevPage: parseInt(page) > 1
        },
        
        statistics: {
          totalPayments: stats.totalPayments,
          totalAmount: stats.totalAmount,
          averagePayment: Math.round(stats.averagePayment || 0),
          firstPaymentDate: stats.firstPaymentDate,
          lastPaymentDate: stats.lastPaymentDate,
          onTimePaymentRate: stats.totalPayments > 0 
            ? Math.round((stats.onTimePayments / stats.totalPayments) * 100) 
            : 0,
          remainingBalance: debt.OutstandingDebt,
          paymentProgress: debt.DebtAmount > 0 
            ? Math.round(((debt.PaidAmount || 0) / debt.DebtAmount) * 100) 
            : 0
        }
      }
    });

  } catch (error) {
    logError('Failed to get payment history', 'GetPaymentHistory', error);
    return next(new AppError('Failed to retrieve payment history', 500));
  }
};

module.exports = getPaymentHistory; 