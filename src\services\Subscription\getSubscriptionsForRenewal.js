/**
 * Get Subscriptions For Renewal Service
 * Finds subscriptions that are ready for auto-renewal
 */
const { Subscription } = require('../../models');
const { logError } = require('../../utils');

/**
 * Get subscriptions ready for auto-renewal within specified days
 * @param {Number} daysAhead - Number of days to look ahead for renewal (default: 3)
 * @returns {Promise<Array>} Array of subscriptions ready for renewal
 */
const getSubscriptionsForRenewal = async (daysAhead = 3) => {
  try {
    const now = new Date();
    const futureDate = new Date();
    futureDate.setDate(now.getDate() + daysAhead);
    
    // Find subscriptions where:
    // 1. Status is 'active'
    // 2. Auto-renewal is enabled
    // 3. End date is between now and future date
    // 4. Not deleted
    // 5. Renewal not already processed for this period
    const renewalSubscriptions = await Subscription.find({
      status: 'active',
      'renewalSettings.autoRenew': true,
      'dates.endDate': {
        $gte: now,
        $lte: futureDate
      },
      isDeleted: false,
      $or: [
        { 'renewalSettings.renewalProcessed': { $exists: false } },
        { 'renewalSettings.renewalProcessed': false },
        // Also include if last renewal attempt was more than 24 hours ago
        { 'renewalSettings.lastRenewalAttempt': { $lt: new Date(Date.now() - 24 * 60 * 60 * 1000) } }
      ]
    }).lean();

    return renewalSubscriptions;
  } catch (error) {
    logError('Failed to get subscriptions for renewal', 'SubscriptionService', error);
    throw error;
  }
};

module.exports = getSubscriptionsForRenewal; 