import api from "@/lib/api";
import { toast } from "sonner";

/**
 * Service for handling payment-related API requests
 */
export const PaymentService = {
  /**
   * Fetch offline payments that need verification
   */
  fetchOfflinePayments: async () => {
    try {
      // In development, return mock data
      if (process.env.NODE_ENV === 'development') {
        // Simulate API delay
        await new Promise(resolve => setTimeout(resolve, 500));
        
        return {
          data: [
            {
              paymentId: "PAY-2025-001",
              subscriptionId: "SUB-2025-001",
              shopName: "Mogadishu Medical Center",
              amount: 99.99,
              paymentMethod: "Bank Transfer",
              transactionId: "TRX-********",
              paymentDate: "2025-05-01T12:00:00Z",
              status: "pending",
              plan: "Monthly",
              note: "Payment made via Dahabshiil Bank. Please verify and activate our subscription.",
              proofFileId: "proof-001",
            },
            {
              paymentId: "PAY-2025-002",
              subscriptionId: "SUB-2025-002",
              shopName: "Hargeisa Health Clinic",
              amount: 199.99,
              paymentMethod: "Mobile Money",
              transactionId: "MM-********",
              paymentDate: "2025-05-02T10:30:00Z",
              status: "pending",
              plan: "Yearly",
              note: "Payment made through EVC Plus. Transaction screenshot attached.",
              proofFileId: "proof-002",
            },
            {
              paymentId: "PAY-2025-003",
              subscriptionId: "SUB-2025-003",
              shopName: "Bosaso Pharmacy",
              amount: 99.99,
              paymentMethod: "Bank Transfer",
              transactionId: "TRX-********",
              paymentDate: "2025-05-02T09:15:00Z",
              status: "verified",
              plan: "Monthly",
              verificationNote: "Payment verified and subscription activated.",
              proofFileId: "proof-003",
            },
            {
              paymentId: "PAY-2025-004",
              subscriptionId: "SUB-2025-004",
              shopName: "Kismaayo Medical Supplies",
              amount: 99.99,
              paymentMethod: "Mobile Money",
              transactionId: "MM-********",
              paymentDate: "2025-05-01T14:20:00Z",
              status: "rejected",
              plan: "Monthly",
              verificationNote: "Transaction ID not found in the system. Please resubmit with correct details.",
              proofFileId: "proof-004",
            },
          ]
        };
      }
      
      // Call the real API endpoint in production
      const response = await api.get("/api/subscriptions/payments/offline");
      return response.data;
    } catch (error) {
      console.error("Error fetching payments:", error);
      throw error;
    }
  },
  
  /**
   * Verify or reject a payment
   */
  verifyPayment: async (paymentId, status, note) => {
    try {
      if (process.env.NODE_ENV === 'development') {
        // Simulate API delay
        await new Promise(resolve => setTimeout(resolve, 500));
        return { success: true };
      }
      
      // Call the real API endpoint in production
      const response = await api.post(`/api/subscriptions/verify-payment/${paymentId}`, {
        status,
        note
      });
      return response.data;
    } catch (error) {
      console.error("Error verifying payment:", error);
      throw error;
    }
  },
  
  /**
   * Get payment proof image
   */
  getPaymentProof: async (fileId) => {
    try {
      if (process.env.NODE_ENV === 'development') {
        // In dev mode, return a placeholder image
        await new Promise(resolve => setTimeout(resolve, 500));
        return new Blob([]);
      }
      
      // Call the real API endpoint
      const response = await api.get(`/api/subscriptions/payment-proof/${fileId}`, {
        responseType: 'blob'
      });
      return response.data;
    } catch (error) {
      console.error("Error fetching payment proof:", error);
      throw error;
    }
  },
  
  /**
   * Format currency
   */
  formatCurrency: (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  },
  
  /**
   * Format date
   */
  formatDate: (dateString) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    }).format(date);
  }
};
