import apiBridge from '@/lib/api/bridge';
import { ENDPOINTS } from '@/lib/api/contract';
import { handleError, processApiResponse, validateRequiredFields } from '../baseService';

/**
 * Send broadcast push notification to all admins (SuperAdmin only)
 * @param {Object} notificationData - Notification data
 * @param {string} notificationData.title - Notification title (1-100 chars)
 * @param {string} notificationData.message - Notification message (1-500 chars)
 * @param {string} [notificationData.priority='normal'] - Priority: low, normal, high
 * @param {string} [notificationData.actionUrl] - Action URL
 * @param {string} [notificationData.actionLabel] - Action button label
 * @param {string} [notificationData.scheduledAt] - ISO 8601 date for scheduling
 * @returns {Promise<Object>} Broadcast result
 */
async function sendBroadcast(notificationData) {
  // Validate required fields
  const validation = validateRequiredFields(notificationData, ['title', 'message']);
  if (!validation.isValid) {
    throw new Error(validation.message);
  }

  // Validate title and message length
  if (notificationData.title.length > 100) {
    throw new Error('Title cannot exceed 100 characters');
  }

  if (notificationData.message.length > 500) {
    throw new Error('Message cannot exceed 500 characters');
  }

  // Validate priority if provided
  if (notificationData.priority && !['low', 'normal', 'high'].includes(notificationData.priority)) {
    throw new Error('Priority must be low, normal, or high');
  }

    // Prepare notification data with backend-expected structure
    const backendData = {
      title: notificationData.title,
      message: notificationData.message,
      priority: notificationData.priority || 'normal',
      ...(notificationData.actionUrl && { actionUrl: notificationData.actionUrl }),
      ...(notificationData.actionLabel && { actionLabel: notificationData.actionLabel }),
      ...(notificationData.scheduledAt && { scheduledAt: notificationData.scheduledAt })
    };

    // Make API request using the bridge
    const response = await apiBridge.post(ENDPOINTS.NOTIFICATIONS.PUSH.BROADCAST, backendData, {
      skipCache: true
    });

  // Process response using utility
  const result = processApiResponse(response, 'Broadcast notification sent successfully');
  return result;
}

export default sendBroadcast; 