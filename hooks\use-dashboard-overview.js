/**
 * Custom hook for SuperAdmin dashboard overview data
 * Provides unified data fetching and state management for the main dashboard
 */

import { useState, useEffect, useCallback } from 'react';
import { useAuth } from '@/contexts/auth-context';
import { toast } from 'sonner';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'https://deyncare-backend.khanciye.com';

export const useDashboardOverview = (options = {}) => {
  const { user } = useAuth();
  const [loading, setLoading] = useState(true); // Start with loading true for initial load
  const [refreshing, setRefreshing] = useState(false);
  const [dashboardData, setDashboardData] = useState(null);
  const [error, setError] = useState(null);
  const [lastUpdated, setLastUpdated] = useState(null);

  const {
    autoRefresh = false,
    refreshInterval = 300000, // 5 minutes
    showToastMessages = true
  } = options;

  const isSuperAdmin = user?.role === 'superAdmin';

  // Get auth headers
  const getAuthHeaders = useCallback(() => {
    const token = localStorage.getItem('accessToken');
    return {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    };
  }, []);

  /**
   * Fetch dashboard overview data
   */
  const fetchDashboardOverview = useCallback(async () => {
    if (!isSuperAdmin) {
      setError('SuperAdmin privileges required');
      return null;
    }

    try {
      setLoading(true);
      setError(null);

      const response = await fetch(
        `${API_BASE_URL}/api/admin/dashboard/overview`,
        {
          method: 'GET',
          headers: getAuthHeaders()
        }
      );

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();

      if (result.success) {
        setDashboardData(result.data);
        setLastUpdated(new Date().toISOString());
        
        if (showToastMessages) {
          toast.success('Dashboard data updated successfully');
        }
      } else {
        throw new Error(result.message || 'Failed to fetch dashboard data');
      }

      return result.data;
    } catch (error) {
      console.error('Error fetching dashboard overview:', error);
      console.error('API URL:', `${API_BASE_URL}/api/admin/dashboard/overview`);
      console.error('Headers:', getAuthHeaders());
      setError(error.message);
      
      // More specific error messages
      if (error.message.includes('401')) {
        if (showToastMessages) {
          toast.error('Authentication failed. Please log in again.');
        }
      } else if (error.message.includes('403')) {
        if (showToastMessages) {
          toast.error('Access denied. SuperAdmin privileges required.');
        }
      } else if (error.message.includes('404')) {
        if (showToastMessages) {
          toast.error('Dashboard endpoint not found.');
        }
      } else if (error.message.includes('500')) {
        if (showToastMessages) {
          toast.error('Server error while loading dashboard data.');
        }
      } else {
        if (showToastMessages) {
          toast.error('Failed to load dashboard data');
        }
      }
      return null;
    } finally {
      setLoading(false);
    }
  }, [isSuperAdmin, getAuthHeaders, showToastMessages]);

  /**
   * Refresh dashboard data (separate from initial loading)
   */
  const refreshDashboard = useCallback(async () => {
    if (!isSuperAdmin) {
      setError('SuperAdmin privileges required');
      return null;
    }

    try {
      setRefreshing(true);
      setError(null);

      const response = await fetch(
        `${API_BASE_URL}/api/admin/dashboard/overview`,
        {
          method: 'GET',
          headers: getAuthHeaders()
        }
      );

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();

      if (result.success) {
        setDashboardData(result.data);
        setLastUpdated(new Date().toISOString());

        if (showToastMessages) {
          toast.success('Dashboard data updated successfully');
        }
      } else {
        throw new Error(result.message || 'Failed to fetch dashboard data');
      }

      return result.data;
    } catch (error) {
      console.error('Error fetching dashboard overview:', error);
      console.error('API URL:', `${API_BASE_URL}/api/admin/dashboard/overview`);
      console.error('Headers:', getAuthHeaders());
      setError(error.message);

      // More specific error messages
      if (error.message.includes('401')) {
        if (showToastMessages) {
          toast.error('Authentication failed. Please log in again.');
        }
      } else if (error.message.includes('403')) {
        if (showToastMessages) {
          toast.error('Access denied. SuperAdmin privileges required.');
        }
      } else if (error.message.includes('404')) {
        if (showToastMessages) {
          toast.error('Dashboard endpoint not found.');
        }
      } else if (error.message.includes('500')) {
        if (showToastMessages) {
          toast.error('Server error while loading dashboard data.');
        }
      } else {
        if (showToastMessages) {
          toast.error('Failed to load dashboard data');
        }
      }
      return null;
    } finally {
      setRefreshing(false);
    }
  }, [isSuperAdmin, getAuthHeaders, showToastMessages]);

  /**
   * Get formatted statistics for KPI cards
   */
  const getFormattedStats = useCallback(() => {
    if (!dashboardData) return null;

    return {
      users: {
        total: dashboardData.users?.total || 0,
        active: dashboardData.users?.active || 0,
        inactive: dashboardData.users?.inactive || 0,
        growthRate: dashboardData.users?.growthRate || 0,
        growthPositive: dashboardData.users?.growthPositive || true,
        thisMonth: dashboardData.users?.thisMonth || 0,
        lastMonth: dashboardData.users?.lastMonth || 0
      },
      shops: {
        total: dashboardData.shops?.total || 0,
        active: dashboardData.shops?.active || 0,
        pending: dashboardData.shops?.pending || 0,
        suspended: dashboardData.shops?.suspended || 0,
        inactive: dashboardData.shops?.inactive || 0,
        growthRate: dashboardData.shops?.growthRate || 0,
        growthPositive: dashboardData.shops?.growthPositive || true,
        thisMonth: dashboardData.shops?.thisMonth || 0,
        lastMonth: dashboardData.shops?.lastMonth || 0
      },
      subscriptions: {
        total: dashboardData.subscriptions?.total || 0,
        active: dashboardData.subscriptions?.active || 0,
        trial: dashboardData.subscriptions?.trial || 0,
        expired: dashboardData.subscriptions?.expired || 0,
        expiringSoon: dashboardData.subscriptions?.expiringSoon || 0
      },
      payments: {
        total: dashboardData.payments?.total || 0,
        pending: dashboardData.payments?.pending || 0,
        approved: dashboardData.payments?.approved || 0,
        rejected: dashboardData.payments?.rejected || 0,
        totalRevenue: dashboardData.payments?.totalRevenue || 0,
        thisMonthRevenue: dashboardData.payments?.thisMonthRevenue || 0,
        lastMonthRevenue: dashboardData.payments?.lastMonthRevenue || 0,
        revenueGrowth: dashboardData.payments?.revenueGrowth || 0,
        revenueGrowthPositive: dashboardData.payments?.revenueGrowthPositive || true
      },
      system: {
        totalPlans: dashboardData.system?.totalPlans || 0,
        activePlans: dashboardData.system?.activePlans || 0,
        inactivePlans: dashboardData.system?.inactivePlans || 0,
        systemStatus: dashboardData.system?.systemStatus || 'unknown',
        lastHealthCheck: dashboardData.system?.lastHealthCheck || null
      }
    };
  }, [dashboardData]);

  /**
   * Format currency values
   */
  const formatCurrency = useCallback((value, currency = 'USD') => {
    if (!value) return '$0.00';
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency
    }).format(value);
  }, []);

  /**
   * Format percentage values
   */
  const formatPercentage = useCallback((value) => {
    if (value === null || value === undefined) return '0%';
    const sign = value >= 0 ? '+' : '';
    return `${sign}${value}%`;
  }, []);

  /**
   * Get trend direction
   */
  const getTrendDirection = useCallback((value) => {
    if (value > 0) return 'up';
    if (value < 0) return 'down';
    return 'neutral';
  }, []);

  // Initial data fetch
  useEffect(() => {
    if (isSuperAdmin) {
      fetchDashboardOverview();
    } else {
      // If not SuperAdmin, stop loading immediately
      setLoading(false);
      setError('SuperAdmin privileges required');
    }
  }, [isSuperAdmin, fetchDashboardOverview]);

  // Auto-refresh setup
  useEffect(() => {
    if (!autoRefresh || !isSuperAdmin) return;

    const interval = setInterval(() => {
      fetchDashboardOverview();
    }, refreshInterval);

    return () => clearInterval(interval);
  }, [autoRefresh, refreshInterval, isSuperAdmin, fetchDashboardOverview]);

  return {
    // Data
    dashboardData,
    formattedStats: getFormattedStats(),
    loading,
    refreshing,
    error,
    lastUpdated,

    // Actions
    fetchDashboardOverview,
    refreshDashboard,

    // Utilities
    formatCurrency,
    formatPercentage,
    getTrendDirection,

    // Computed values
    isSuperAdmin,
    hasData: !!dashboardData,
    isStale: lastUpdated ? (Date.now() - new Date(lastUpdated).getTime()) > refreshInterval : false
  };
};
