const { Debt, Payment } = require('../../models');
const { AppError, logError, logInfo } = require('../../utils');

/**
 * Delete Debt Record (Soft Delete)
 * DELETE /api/debts/:debtId
 */
const deleteDebt = async (req, res, next) => {
  try {
    const { debtId } = req.params;
    const shopId = req.user.shopId;

    // Find existing debt
    const debt = await Debt.findOne({ 
      debtId, 
      shopId, 
      isDeleted: false 
    });

    if (!debt) {
      return res.status(404).json({
        success: false,
        message: 'Debt not found'
      });
    }

    // Check if debt has any payments
    const hasPayments = await Payment.findOne({ 
      debtId, 
      paymentContext: 'debt',
      isDeleted: false 
    });

    if (hasPayments) {
      return res.status(400).json({
        success: false,
        message: 'Cannot delete debt with existing payments. Please refund payments first.',
        details: {
          reason: 'debt_has_payments',
          suggestion: 'Refund all payments before deleting the debt record'
        }
      });
    }

    // Check if debt is fully paid
    if (debt.OutstandingDebt <= 0 && debt.PaidAmount > 0) {
      return res.status(400).json({
        success: false,
        message: 'Cannot delete a fully paid debt. This record should be kept for historical purposes.',
        details: {
          reason: 'debt_fully_paid',
          paidAmount: debt.PaidAmount,
          suggestion: 'Archive the debt instead of deleting it'
        }
      });
    }

    // Perform soft delete
    debt.isDeleted = true;
    debt.deletedAt = new Date();
    debt.deletedBy = req.user.userId;
    
    await debt.save();

    logInfo(`Debt deleted: ${debtId} by ${req.user.email}`, 'DeleteDebt');

    res.json({
      success: true,
      message: 'Debt deleted successfully',
      data: {
        debtId: debt.debtId,
        customerName: debt.CustomerName,
        deletedAt: debt.deletedAt,
        note: 'This is a soft delete - the record is preserved for audit purposes'
      }
    });

  } catch (error) {
    logError('Failed to delete debt', 'DeleteDebt', error);
    return next(new AppError('Failed to delete debt', 500));
  }
};

module.exports = deleteDebt; 