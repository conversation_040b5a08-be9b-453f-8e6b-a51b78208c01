import apiBridge from '@/lib/api/bridge';
import { ENDPOINTS } from '@/lib/api/contract';
import { toast } from 'sonner';
import BaseService from '@/lib/services/baseService';

/**
 * Get a specific shop by ID via SuperAdmin API
 * @param {string} shopId - ID of the shop to retrieve
 * @returns {Promise<Object>} Shop object
 */
async function getShopById(shopId) {
  try {
    // Make API request using SuperAdmin endpoint
    const response = await apiBridge.get(`${ENDPOINTS.SHOPS.BASE}/${shopId}`);
    
    // Process response
    if (response.data && response.data.success) {
      const shopData = response.data.data;
      
      // Map SuperAdmin backend response to frontend-expected format
      return {
        // Core identifiers
        id: shopData.shopId || shopData._id,
        _id: shopData._id || shopData.shopId,
        shopId: shopData.shopId || shopData._id,
        
        // Basic shop info
        shopName: shopData.shopName,
        shopAddress: shopData.address,
        ownerName: shopData.ownerName,
        email: shopData.email,
        phone: shopData.phone,
        
        // Status and verification
        status: shopData.status,
        verified: shopData.verified,
        registeredBy: shopData.registeredBy,
        
        // Logo
        logoUrl: shopData.logoUrl || '',
        
        // Dates
        createdAt: shopData.createdAt,
        updatedAt: shopData.updatedAt,
        
        // Access control (SuperAdmin shops use simple access model)
        access: {
          isPaid: shopData.access?.isPaid || false,
          isActivated: shopData.access?.isActivated || false
        },
        
        // Additional data with fallbacks
        features: shopData.features || {},
        notifications: shopData.notifications || {},
        statistics: shopData.statistics || {},
        businessDetails: shopData.businessDetails || {},
        location: shopData.location || {},
        verificationDetails: shopData.verificationDetails || {},
        
        // Owner information if available
        owner: shopData.owner || {
          userId: shopData.ownerId,
          fullName: shopData.ownerName,
          email: shopData.email,
          phone: shopData.phone
        }
      };
    }
    
    // Handle unexpected response format
    console.error('[SuperAdminShopService] Unexpected API response format:', response.data);
    toast.error('Failed to load shop details: Unexpected response format');
    return null;
  } catch (error) {
    console.error('[SuperAdminShopService] Error getting shop details:', error);
    
    // Enhanced error handling for SuperAdmin operations
    if (error.response?.status === 401) {
      BaseService.handleError({ message: 'Unauthorized: SuperAdmin access required' }, 'SuperAdminShopService.getShopById', true);
    } else if (error.response?.status === 403) {
      BaseService.handleError({ message: 'Forbidden: Only SuperAdmins can view shop details' }, 'SuperAdminShopService.getShopById', true);
    } else if (error.response?.status === 404) {
      BaseService.handleError({ message: 'Shop not found' }, 'SuperAdminShopService.getShopById', true);
    } else {
      BaseService.handleError(error, 'SuperAdminShopService.getShopById', true);
    }
    
    return null;
  }
}

export default getShopById;
