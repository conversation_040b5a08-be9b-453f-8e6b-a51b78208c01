import { 
  Crown, 
  User, 
  Check, 
  X, 
  Store, 
  Users, 
  BarChart3, 
  Settings, 
  Bell,
  CreditCard,
  FileText,
  Shield,
  ArrowRight
} from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle } from "../ui/card";
import { Badge } from "../ui/badge";
import { Button } from "../ui/button";
import Link from "next/link";

const roleFeatures = [
  {
    category: "User Management",
    icon: Users,
    features: [
      {
        name: "Create Users",
        admin: "Shop employees only",
        superAdmin: "All users, all shops"
      },
      {
        name: "Role Management",
        admin: "Employee role only",
        superAdmin: "All roles including SuperAdmin"
      },
      {
        name: "User Analytics",
        admin: "Shop-level only",
        superAdmin: "System-wide analytics"
      },
      {
        name: "Bulk Operations",
        admin: "Limited to shop",
        superAdmin: "Cross-shop operations"
      }
    ]
  },
  {
    category: "Shop Management",
    icon: Store,
    features: [
      {
        name: "Shop Creation",
        admin: false,
        superAdmin: "Full shop onboarding"
      },
      {
        name: "Shop Status Control",
        admin: false,
        superAdmin: "Suspend/activate shops"
      },
      {
        name: "Shop Analytics",
        admin: "Own shop only",
        superAdmin: "All shops comparison"
      },
      {
        name: "Shop Verification",
        admin: false,
        superAdmin: "Verification workflow"
      }
    ]
  },
  {
    category: "Plans & Subscriptions",
    icon: CreditCard,
    features: [
      {
        name: "Plan Management",
        admin: false,
        superAdmin: "Create/edit all plans"
      },
      {
        name: "Subscription Control",
        admin: "View own only",
        superAdmin: "Manage all subscriptions"
      },
      {
        name: "Payment Processing",
        admin: "Shop payments",
        superAdmin: "System-wide payments"
      },
      {
        name: "Retry Management",
        admin: false,
        superAdmin: "Payment retry control"
      }
    ]
  },
  {
    category: "Analytics & Reports",
    icon: BarChart3,
    features: [
      {
        name: "Dashboard Access",
        admin: "Shop dashboard",
        superAdmin: "System dashboard"
      },
      {
        name: "Advanced Analytics",
        admin: "Shop metrics",
        superAdmin: "Cross-shop insights"
      },
      {
        name: "Export Reports",
        admin: "Shop data only",
        superAdmin: "System-wide exports"
      },
      {
        name: "Real-time Monitoring",
        admin: "Shop activities",
        superAdmin: "Platform monitoring"
      }
    ]
  },
  {
    category: "System Controls",
    icon: Settings,
    features: [
      {
        name: "System Settings",
        admin: false,
        superAdmin: "Full system configuration"
      },
      {
        name: "Security Controls",
        admin: "Shop-level",
        superAdmin: "Platform security"
      },
      {
        name: "ML Configuration",
        admin: false,
        superAdmin: "AI model parameters"
      },
      {
        name: "System Logs",
        admin: false,
        superAdmin: "Complete audit trail"
      }
    ]
  },
  {
    category: "Notifications",
    icon: Bell,
    features: [
      {
        name: "Push Notifications",
        admin: "Shop customers",
        superAdmin: "System-wide broadcasts"
      },
      {
        name: "Debt Reminders",
        admin: "Shop debts only",
        superAdmin: "All shops management"
      },
      {
        name: "Notification Analytics",
        admin: "Shop metrics",
        superAdmin: "Platform metrics"
      },
      {
        name: "Broadcasting",
        admin: false,
        superAdmin: "Mass communication"
      }
    ]
  }
];

const FeatureRow = ({ feature }) => (
  <div className="grid grid-cols-3 gap-4 py-4 border-b border-slate-100 last:border-0">
    <div className="font-medium text-slate-900">{feature.name}</div>
    <div className="flex items-center gap-2">
      {feature.admin ? (
        <>
          <Check className="w-4 h-4 text-green-500" />
          <span className="text-sm text-slate-600">{feature.admin}</span>
        </>
      ) : (
        <>
          <X className="w-4 h-4 text-red-400" />
          <span className="text-sm text-slate-400">Not available</span>
        </>
      )}
    </div>
    <div className="flex items-center gap-2">
      <Check className="w-4 h-4 text-blue-500" />
      <span className="text-sm text-slate-600">{feature.superAdmin}</span>
    </div>
  </div>
);

export default function RoleComparison() {
  return (
    <section className="py-24 bg-gradient-to-b from-slate-50 to-white">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <div className="text-center mb-16">
          <Badge className="mb-4 bg-blue-100 text-blue-700 border-blue-200">
            Role Comparison
          </Badge>
          <h2 className="text-3xl md:text-4xl font-bold text-slate-900 mb-6">
            Choose the Right Access Level
          </h2>
          <p className="text-lg text-slate-600 max-w-3xl mx-auto">
            DeynCare provides different access levels to match your business needs. 
            Compare Admin and SuperAdmin capabilities to find the perfect fit.
          </p>
        </div>

        {/* Role Cards Overview */}
        <div className="grid md:grid-cols-2 gap-8 mb-16">
          {/* Admin Card */}
          <Card className="relative border-2 border-slate-200 hover:border-slate-300 transition-colors">
            <CardHeader className="text-center pb-4">
              <div className="mx-auto mb-4 p-4 bg-green-100 rounded-full w-fit">
                <User className="w-8 h-8 text-green-600" />
              </div>
              <CardTitle className="text-2xl text-slate-900">Admin</CardTitle>
              <p className="text-slate-600">Shop-level management and operations</p>
            </CardHeader>
            <CardContent>
              <div className="space-y-3 mb-6">
                <div className="flex items-center gap-2 text-sm">
                  <Check className="w-4 h-4 text-green-500" />
                  <span>Manage shop employees and operations</span>
                </div>
                <div className="flex items-center gap-2 text-sm">
                  <Check className="w-4 h-4 text-green-500" />
                  <span>Access to shop-specific analytics</span>
                </div>
                <div className="flex items-center gap-2 text-sm">
                  <Check className="w-4 h-4 text-green-500" />
                  <span>Customer and debt management</span>
                </div>
                <div className="flex items-center gap-2 text-sm">
                  <Check className="w-4 h-4 text-green-500" />
                  <span>Shop-level reporting and exports</span>
                </div>
              </div>
              <div className="text-center">
                <span className="text-2xl font-bold text-slate-900">Perfect for</span>
                <p className="text-slate-600 mt-1">Shop owners and managers</p>
              </div>
            </CardContent>
          </Card>

          {/* SuperAdmin Card */}
          <Card className="relative border-2 border-blue-300 bg-gradient-to-b from-blue-50 to-white">
            <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
              <Badge className="bg-blue-600 text-white border-0 px-4 py-1">
                Most Popular
              </Badge>
            </div>
            <CardHeader className="text-center pb-4">
              <div className="mx-auto mb-4 p-4 bg-blue-100 rounded-full w-fit">
                <Crown className="w-8 h-8 text-blue-600" />
              </div>
              <CardTitle className="text-2xl text-slate-900">SuperAdmin</CardTitle>
              <p className="text-slate-600">Complete platform control and management</p>
            </CardHeader>
            <CardContent>
              <div className="space-y-3 mb-6">
                <div className="flex items-center gap-2 text-sm">
                  <Check className="w-4 h-4 text-blue-500" />
                  <span>Manage all shops and users system-wide</span>
                </div>
                <div className="flex items-center gap-2 text-sm">
                  <Check className="w-4 h-4 text-blue-500" />
                  <span>Advanced analytics and cross-shop insights</span>
                </div>
                <div className="flex items-center gap-2 text-sm">
                  <Check className="w-4 h-4 text-blue-500" />
                  <span>System configuration and ML controls</span>
                </div>
                <div className="flex items-center gap-2 text-sm">
                  <Check className="w-4 h-4 text-blue-500" />
                  <span>Platform-wide notifications and broadcasting</span>
                </div>
              </div>
              <div className="text-center">
                <span className="text-2xl font-bold text-slate-900">Perfect for</span>
                <p className="text-slate-600 mt-1">Platform administrators and enterprises</p>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Detailed Comparison Table */}
        <div className="bg-white rounded-2xl shadow-lg border border-slate-200 overflow-hidden">
          <div className="bg-slate-50 px-6 py-4 border-b border-slate-200">
            <h3 className="text-xl font-bold text-slate-900">Detailed Feature Comparison</h3>
            <p className="text-slate-600 mt-1">Compare specific capabilities across user roles</p>
          </div>

          {/* Table Header */}
          <div className="grid grid-cols-3 gap-4 px-6 py-4 bg-slate-50 border-b border-slate-200 font-semibold text-slate-900">
            <div>Feature</div>
            <div className="flex items-center gap-2">
              <User className="w-4 h-4" />
              Admin
            </div>
            <div className="flex items-center gap-2">
              <Crown className="w-4 h-4" />
              SuperAdmin
            </div>
          </div>

          {/* Feature Categories */}
          {roleFeatures.map((category, categoryIndex) => (
            <div key={categoryIndex} className="px-6 py-4">
              {/* Category Header */}
              <div className="flex items-center gap-3 mb-4 pb-2 border-b-2 border-slate-100">
                <div className="p-2 bg-slate-100 rounded-lg">
                  <category.icon className="w-5 h-5 text-slate-600" />
                </div>
                <h4 className="text-lg font-semibold text-slate-900">{category.category}</h4>
              </div>

              {/* Features */}
              <div className="space-y-0">
                {category.features.map((feature, featureIndex) => (
                  <FeatureRow key={featureIndex} feature={feature} />
                ))}
              </div>
            </div>
          ))}
        </div>

        {/* Bottom CTA */}
        <div className="mt-16 text-center">
          <h3 className="text-2xl font-bold text-slate-900 mb-4">
            Ready to Get Started?
          </h3>
          <p className="text-lg text-slate-600 mb-8 max-w-2xl mx-auto">
            Choose the access level that fits your needs. You can always upgrade as your business grows.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button asChild size="lg" className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white">
              <Link href="/login" className="flex items-center gap-2">
                Start with SuperAdmin
                <ArrowRight className="w-4 h-4" />
              </Link>
            </Button>
            <Button asChild variant="outline" size="lg">
              <Link href="#contact">
                Contact Us for Custom Setup
              </Link>
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
} 