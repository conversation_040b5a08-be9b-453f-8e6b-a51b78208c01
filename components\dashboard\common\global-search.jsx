"use client"

import * as React from "react"
import { useState, useEffect, useCallback } from "react"
import { useRouter } from "next/navigation"
import { 
  Search, 
  Store, 
  Users, 
  CreditCard, 
  Package, 
  FileText, 
  Settings, 
  BarChart3,
  Clock,
  ArrowRight,
  Sparkles
} from "lucide-react"
import { cn } from "@/lib/utils"
import {
  CommandDialog,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
  CommandSeparator,
  CommandShortcut,
} from "@/components/ui/command"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"

// Mock data for search results - replace with actual API calls
const searchData = {
  navigation: [
    { id: 'shops', name: 'Shop Management', icon: Store, href: '/dashboard/shops', category: 'Navigation' },
    { id: 'users', name: 'User Management', icon: Users, href: '/dashboard/users', category: 'Navigation' },
    { id: 'payments', name: 'Payments', icon: CreditCard, href: '/dashboard/payments', category: 'Navigation' },
    { id: 'plans', name: 'Plans', icon: Package, href: '/dashboard/plans', category: 'Navigation' },
    { id: 'subscriptions', name: 'Subscriptions', icon: FileText, href: '/dashboard/subscriptions', category: 'Navigation' },
    { id: 'reports', name: 'Reports', icon: BarChart3, href: '/dashboard/reports', category: 'Navigation' },
    { id: 'settings', name: 'Settings', icon: Settings, href: '/dashboard/settings', category: 'Navigation' },
  ],
  shops: [
    { id: 'shop1', name: 'MIDNIMO', owner: 'Mohamud Muhidin abdullahi', status: 'Active', category: 'Shops' },
    { id: 'shop2', name: 'HAYAT', owner: 'MUHAMMAD', status: 'Active', category: 'Shops' },
    { id: 'shop3', name: 'HIREY SHOP', owner: 'HIREY', status: 'Active', category: 'Shops' },
    { id: 'shop4', name: 'HAYATMARKET', owner: 'FITAH', status: 'Active', category: 'Shops' },
    { id: 'shop5', name: 'AMAR SHOP', owner: 'Amar', status: 'Pending', category: 'Shops' },
  ],
  users: [
    { id: 'user1', name: 'Mohamud Muhidin', email: '<EMAIL>', role: 'Shop Owner', category: 'Users' },
    { id: 'user2', name: 'Muhammad', email: '<EMAIL>', role: 'Shop Owner', category: 'Users' },
    { id: 'user3', name: 'Hirey', email: '<EMAIL>', role: 'Shop Owner', category: 'Users' },
  ],
  quickActions: [
    { id: 'register-shop', name: 'Register New Shop', icon: Store, action: 'register-shop', category: 'Quick Actions' },
    { id: 'create-user', name: 'Create New User', icon: Users, action: 'create-user', category: 'Quick Actions' },
    { id: 'generate-report', name: 'Generate Report', icon: FileText, action: 'generate-report', category: 'Quick Actions' },
    { id: 'view-analytics', name: 'View Analytics', icon: BarChart3, action: 'view-analytics', category: 'Quick Actions' },
  ]
}

export function GlobalSearch({ className }) {
  const [open, setOpen] = useState(false)
  const [query, setQuery] = useState("")
  const [results, setResults] = useState([])
  const [recentSearches, setRecentSearches] = useState([])
  const router = useRouter()

  // Keyboard shortcut to open search
  useEffect(() => {
    const down = (e) => {
      if (e.key === "k" && (e.metaKey || e.ctrlKey)) {
        e.preventDefault()
        setOpen((open) => !open)
      }
    }
    document.addEventListener("keydown", down)
    return () => document.removeEventListener("keydown", down)
  }, [])

  // Search function
  const performSearch = useCallback((searchQuery) => {
    if (!searchQuery.trim()) {
      setResults([])
      return
    }

    const query = searchQuery.toLowerCase()
    const allResults = []

    // Search navigation
    searchData.navigation.forEach(item => {
      if (item.name.toLowerCase().includes(query)) {
        allResults.push(item)
      }
    })

    // Search shops
    searchData.shops.forEach(shop => {
      if (shop.name.toLowerCase().includes(query) || shop.owner.toLowerCase().includes(query)) {
        allResults.push(shop)
      }
    })

    // Search users
    searchData.users.forEach(user => {
      if (user.fullName.toLowerCase().includes(query) || user.email.toLowerCase().includes(query)) {
        allResults.push(user)
      }
    })

    // Add quick actions if query matches
    if (query.includes('register') || query.includes('create') || query.includes('new')) {
      allResults.push(...searchData.quickActions)
    }

    setResults(allResults)
  }, [])

  // Handle search input change
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      performSearch(query)
    }, 150) // Debounce search

    return () => clearTimeout(timeoutId)
  }, [query, performSearch])

  // Handle item selection
  const handleSelect = useCallback((item) => {
    // Add to recent searches
    setRecentSearches(prev => {
      const filtered = prev.filter(search => search.id !== item.id)
      return [item, ...filtered].slice(0, 5) // Keep only 5 recent searches
    })

    // Navigate or perform action
    if (item.href) {
      router.push(item.href)
    } else if (item.action) {
      // Handle quick actions
      switch (item.action) {
        case 'register-shop':
          router.push('/dashboard/shops?action=register')
          break
        case 'create-user':
          router.push('/dashboard/users?action=create')
          break
        case 'generate-report':
          router.push('/dashboard/reports?action=generate')
          break
        case 'view-analytics':
          router.push('/dashboard/analytics')
          break
        default:
          break
      }
    } else if (item.category === 'Shops') {
      router.push(`/dashboard/shops?search=${encodeURIComponent(item.name)}`)
    } else if (item.category === 'Users') {
      router.push(`/dashboard/users?search=${encodeURIComponent(item.name)}`)
    }

    setOpen(false)
    setQuery("")
  }, [router])

  // Get icon for category
  const getCategoryIcon = (category) => {
    switch (category) {
      case 'Navigation': return BarChart3
      case 'Shops': return Store
      case 'Users': return Users
      case 'Quick Actions': return Sparkles
      default: return Search
    }
  }

  // Get status color
  const getStatusColor = (status) => {
    switch (status) {
      case 'Active': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
      case 'Pending': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300'
      case 'Suspended': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300'
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300'
    }
  }

  // Group results by category
  const groupedResults = results.reduce((acc, item) => {
    const category = item.category || 'Other'
    if (!acc[category]) acc[category] = []
    acc[category].push(item)
    return acc
  }, {})

  return (
    <>
      {/* Search trigger button */}
      <Button
        variant="outline"
        className={cn(
          "relative h-9 w-full justify-start rounded-[0.5rem] bg-background text-sm font-normal text-muted-foreground shadow-none sm:pr-12 md:w-40 lg:w-64",
          className
        )}
        onClick={() => setOpen(true)}
      >
        <Search className="mr-2 h-4 w-4" />
        <span className="hidden lg:inline-flex">Search everything...</span>
        <span className="inline-flex lg:hidden">Search...</span>
        <kbd className="pointer-events-none absolute right-[0.3rem] top-[0.3rem] hidden h-5 select-none items-center gap-1 rounded border bg-muted px-1.5 font-mono text-[10px] font-medium opacity-100 sm:flex">
          <span className="text-xs">⌘</span>K
        </kbd>
      </Button>

      {/* Search dialog */}
      <CommandDialog open={open} onOpenChange={setOpen}>
        <CommandInput 
          placeholder="Search shops, users, navigation, or type a command..." 
          value={query}
          onValueChange={setQuery}
        />
        <CommandList>
          <CommandEmpty>
            <div className="flex flex-col items-center justify-center py-6 text-center">
              <Search className="h-8 w-8 text-muted-foreground mb-2" />
              <p className="text-sm text-muted-foreground">No results found.</p>
              <p className="text-xs text-muted-foreground mt-1">
                Try searching for shops, users, or navigation items.
              </p>
            </div>
          </CommandEmpty>

          {/* Show recent searches when no query */}
          {!query && recentSearches.length > 0 && (
            <CommandGroup heading="Recent Searches">
              {recentSearches.map((item) => {
                const Icon = item.icon || getCategoryIcon(item.category)
                return (
                  <CommandItem
                    key={`recent-${item.id}`}
                    value={item.name}
                    onSelect={() => handleSelect(item)}
                    className="flex items-center gap-2"
                  >
                    <Clock className="h-4 w-4 text-muted-foreground" />
                    <Icon className="h-4 w-4" />
                    <span>{item.name}</span>
                    {item.status && (
                      <Badge variant="secondary" className={cn("ml-auto text-xs", getStatusColor(item.status))}>
                        {item.status}
                      </Badge>
                    )}
                  </CommandItem>
                )
              })}
            </CommandGroup>
          )}

          {/* Show grouped results */}
          {Object.entries(groupedResults).map(([category, items], index) => {
            const CategoryIcon = getCategoryIcon(category)
            return (
              <React.Fragment key={category}>
                {index > 0 && <CommandSeparator />}
                <CommandGroup heading={category}>
                  {items.map((item) => {
                    const Icon = item.icon || CategoryIcon
                    return (
                      <CommandItem
                        key={item.id}
                        value={item.name}
                        onSelect={() => handleSelect(item)}
                        className="flex items-center gap-2"
                      >
                        <Icon className="h-4 w-4" />
                        <div className="flex-1">
                          <div className="font-medium">{item.name}</div>
                          {item.owner && (
                            <div className="text-xs text-muted-foreground">Owner: {item.owner}</div>
                          )}
                          {item.email && (
                            <div className="text-xs text-muted-foreground">{item.email}</div>
                          )}
                          {item.role && (
                            <div className="text-xs text-muted-foreground">{item.role}</div>
                          )}
                        </div>
                        {item.status && (
                          <Badge variant="secondary" className={cn("text-xs", getStatusColor(item.status))}>
                            {item.status}
                          </Badge>
                        )}
                        {item.href && (
                          <ArrowRight className="h-4 w-4 text-muted-foreground" />
                        )}
                        {item.action && (
                          <CommandShortcut>Action</CommandShortcut>
                        )}
                      </CommandItem>
                    )
                  })}
                </CommandGroup>
              </React.Fragment>
            )
          })}

          {/* Show suggestions when no query */}
          {!query && (
            <>
              <CommandSeparator />
              <CommandGroup heading="Suggestions">
                <CommandItem onSelect={() => setQuery("active shops")}>
                  <Store className="mr-2 h-4 w-4" />
                  <span>View active shops</span>
                </CommandItem>
                <CommandItem onSelect={() => setQuery("pending users")}>
                  <Users className="mr-2 h-4 w-4" />
                  <span>Find pending users</span>
                </CommandItem>
                <CommandItem onSelect={() => setQuery("register")}>
                  <Sparkles className="mr-2 h-4 w-4" />
                  <span>Register new shop</span>
                </CommandItem>
                <CommandItem onSelect={() => setQuery("reports")}>
                  <FileText className="mr-2 h-4 w-4" />
                  <span>Generate reports</span>
                </CommandItem>
              </CommandGroup>
            </>
          )}
        </CommandList>
      </CommandDialog>
    </>
  )
} 