const express = require('express');
const { body, query } = require('express-validator');
const PushNotificationController = require('../controllers/admin/pushNotificationController');
const { authenticate, authorize } = require('../middleware/authMiddleware');

const router = express.Router();

/**
 * Push Notification Routes for SuperAdmin
 * All routes require SuperAdmin authentication
 */

// Apply authentication and SuperAdmin role middleware to all routes
router.use(authenticate);
router.use(authorize(['superAdmin']));

/**
 * @route   POST /api/admin/notifications/push/shops
 * @desc    Send push notification to specific shop owners/admins
 * @access  SuperAdmin only
 */
router.post('/shops', [
  body('shopIds')
    .isArray({ min: 1 })
    .withMessage('shopIds must be a non-empty array'),
  body('shopIds.*')
    .isString()
    .trim()
    .notEmpty()
    .withMessage('Each shopId must be a non-empty string'),
  body('title')
    .isString()
    .trim()
    .isLength({ min: 1, max: 100 })
    .withMessage('Title must be between 1 and 100 characters'),
  body('message')
    .isString()
    .trim()
    .isLength({ min: 1, max: 500 })
    .withMessage('Message must be between 1 and 500 characters'),
  body('priority')
    .optional()
    .isIn(['low', 'normal', 'high'])
    .withMessage('Priority must be low, normal, or high'),
  body('actionUrl')
    .optional()
    .isString()
    .trim()
    .withMessage('Action URL must be a string'),
  body('actionLabel')
    .optional()
    .isString()
    .trim()
    .isLength({ max: 50 })
    .withMessage('Action label must be max 50 characters'),
  body('scheduledAt')
    .optional()
    .isISO8601()
    .withMessage('Scheduled date must be a valid ISO 8601 date')
], PushNotificationController.sendToShops);

/**
 * @route   POST /api/admin/notifications/push/broadcast
 * @desc    Send broadcast push notification to all admins
 * @access  SuperAdmin only
 */
router.post('/broadcast', [
  body('title')
    .isString()
    .trim()
    .isLength({ min: 1, max: 100 })
    .withMessage('Title must be between 1 and 100 characters'),
  body('message')
    .isString()
    .trim()
    .isLength({ min: 1, max: 500 })
    .withMessage('Message must be between 1 and 500 characters'),
  body('priority')
    .optional()
    .isIn(['low', 'normal', 'high'])
    .withMessage('Priority must be low, normal, or high'),
  body('actionUrl')
    .optional()
    .isString()
    .trim()
    .withMessage('Action URL must be a string'),
  body('actionLabel')
    .optional()
    .isString()
    .trim()
    .isLength({ max: 50 })
    .withMessage('Action label must be max 50 characters'),
  body('scheduledAt')
    .optional()
    .isISO8601()
    .withMessage('Scheduled date must be a valid ISO 8601 date')
], PushNotificationController.sendBroadcast);

/**
 * @route   POST /api/admin/notifications/push/debt-reminders
 * @desc    Send debt reminder notifications to shop owners
 * @access  SuperAdmin only
 */
router.post('/debt-reminders', [
  body('shopIds')
    .optional()
    .isArray()
    .withMessage('shopIds must be an array'),
  body('shopIds.*')
    .optional()
    .isString()
    .trim()
    .notEmpty()
    .withMessage('Each shopId must be a non-empty string'),
  body('reminderType')
    .optional()
    .isIn(['overdue', 'upcoming', 'custom'])
    .withMessage('Reminder type must be overdue, upcoming, or custom'),
  body('customMessage')
    .optional()
    .isString()
    .trim()
    .isLength({ min: 1, max: 500 })
    .withMessage('Custom message must be between 1 and 500 characters'),
  body('daysOverdue')
    .optional()
    .isInt({ min: 0 })
    .withMessage('Days overdue must be a non-negative integer'),
  body('priority')
    .optional()
    .isIn(['low', 'normal', 'high'])
    .withMessage('Priority must be low, normal, or high')
], PushNotificationController.sendDebtReminders);

/**
 * @route   GET /api/admin/notifications/push/stats
 * @desc    Get push notification statistics
 * @access  SuperAdmin only
 */
router.get('/stats', [
  query('shopId')
    .optional()
    .isString()
    .trim()
    .withMessage('Shop ID must be a string'),
  query('days')
    .optional()
    .isInt({ min: 1, max: 365 })
    .withMessage('Days must be between 1 and 365')
], PushNotificationController.getNotificationStats);

/**
 * @route   GET /api/admin/notifications/push/test
 * @desc    Test Firebase connection
 * @access  SuperAdmin only
 */
router.get('/test', PushNotificationController.testFirebaseConnection);

/**
 * @route   GET /api/admin/notifications/push/history
 * @desc    Get notification history with pagination and filtering
 * @access  SuperAdmin only
 */
router.get('/history', [
  query('shopId')
    .optional()
    .isString()
    .trim()
    .withMessage('Shop ID must be a string'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Limit must be between 1 and 100'),
  query('offset')
    .optional()
    .isInt({ min: 0 })
    .withMessage('Offset must be non-negative'),
  query('status')
    .optional()
    .isIn(['all', 'sent', 'delivered', 'failed', 'pending', 'expired'])
    .withMessage('Status must be all, sent, delivered, failed, pending, or expired'),
  query('type')
    .optional()
    .isIn(['all', 'Push', 'Email', 'SMS'])
    .withMessage('Type must be all, Push, Email, or SMS')
], PushNotificationController.getNotificationHistory);

/**
 * @route   GET /api/admin/notifications/push/targets
 * @desc    Get list of shops and admin users for targeting notifications
 * @access  SuperAdmin only
 */
router.get('/targets', PushNotificationController.getNotificationTargets);

module.exports = router; 