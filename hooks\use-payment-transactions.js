/**
 * Custom hook for payment transaction operations
 * Provides state management and API integration for SuperAdmin payment transaction management
 */

import { useState, useEffect, useCallback } from 'react';
import { useAuth } from '@/contexts/auth-context';
import { toast } from 'sonner';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'https://deyncare-backend.khanciye.com';

export const usePaymentTransactions = () => {
  const { user } = useAuth();
  const [loading, setLoading] = useState(false);
  const [transactions, setTransactions] = useState([]);
  const [transactionStats, setTransactionStats] = useState(null);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
    total: 0,
    totalPages: 0
  });
  const [filters, setFilters] = useState({
    status: 'all',
    paymentMethod: 'all',
    dateRange: 'all',
    search: ''
  });

  const isSuperAdmin = user?.role === 'superAdmin';

  // Get auth token
  const getAuthToken = useCallback(() => {
    return localStorage.getItem('accessToken');
  }, []);

  // Create headers with auth
  const getHeaders = useCallback(() => {
    const token = getAuthToken();
    return {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    };
  }, [getAuthToken]);

  /**
   * Fetch payment transactions with filters and pagination
   */
  const fetchTransactions = useCallback(async (params = {}) => {
    if (!isSuperAdmin || loading) return null;
    
    try {
      setLoading(true);
      
      const currentPagination = pagination || { page: 1, limit: 10 };
      const currentFilters = filters || {};
      
      const requestParams = {
        page: params.page || currentPagination.page || 1,
        limit: params.limit || currentPagination.limit || 10,
        status: params.status || currentFilters.status || 'all',
        paymentMethod: params.paymentMethod || currentFilters.paymentMethod || 'all',
        dateRange: params.dateRange || currentFilters.dateRange || 'all',
        search: params.search || currentFilters.search || '',
        paymentContext: 'subscription', // Only show subscription payments
        ...params
      };

      // Build query string
      const queryParams = new URLSearchParams();
      Object.keys(requestParams).forEach(key => {
        if (requestParams[key] && requestParams[key] !== 'all') {
          queryParams.append(key, requestParams[key]);
        }
      });

      const response = await fetch(
        `${API_BASE_URL}/api/superadmin/payment-transactions?${queryParams.toString()}`,
        {
          method: 'GET',
          headers: getHeaders()
        }
      );

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      
      if (result.success) {
        setTransactions(result.data.payments || []);
        setPagination({
          page: result.data.pagination?.currentPage || requestParams.page,
          limit: result.data.pagination?.itemsPerPage || requestParams.limit,
          total: result.data.pagination?.totalItems || 0,
          totalPages: result.data.pagination?.totalPages || 1,
          hasNextPage: result.data.pagination?.hasNextPage || false,
          hasPrevPage: result.data.pagination?.hasPrevPage || false
        });
        
        // Update filters state
        setFilters(prev => ({
          ...prev,
          status: requestParams.status,
          paymentMethod: requestParams.paymentMethod,
          dateRange: requestParams.dateRange,
          search: requestParams.search
        }));
      }
      
      return result;
    } catch (error) {
      console.error('Error fetching payment transactions:', error);
      toast.error('Failed to load payment transactions');
      return null;
    } finally {
      setLoading(false);
    }
  }, [isSuperAdmin, loading, pagination, filters, getHeaders]);

  /**
   * Fetch payment transaction statistics
   */
  const fetchTransactionStats = useCallback(async () => {
    if (!isSuperAdmin) return null;
    
    try {
      const response = await fetch(
        `${API_BASE_URL}/api/superadmin/payment-transactions/stats`,
        {
          method: 'GET',
          headers: getHeaders()
        }
      );

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();

      if (result.success) {
        // Transform backend response to match frontend expectations
        const backendData = result.data;
        const transformedStats = {
          summary: {
            totalTransactions: backendData.totalPayments || 0,
            pendingTransactions: 0, // Will be calculated from statusDistribution
            approvedTransactions: 0, // Will be calculated from statusDistribution
            rejectedTransactions: 0, // Will be calculated from statusDistribution
            totalValue: backendData.totalRevenue || 0,
            approvedValue: backendData.totalRevenue || 0, // Assuming approved = total revenue
            successRate: backendData.successRate || 0
          },
          statusDistribution: backendData.statusDistribution || [],
          methodDistribution: backendData.methodDistribution || []
        };

        // Calculate status-specific counts from statusDistribution
        if (backendData.statusDistribution) {
          backendData.statusDistribution.forEach(status => {
            switch (status._id) {
              case 'pending':
                transformedStats.summary.pendingTransactions = status.count;
                break;
              case 'approved':
              case 'success':
                transformedStats.summary.approvedTransactions += status.count;
                break;
              case 'rejected':
              case 'failed':
                transformedStats.summary.rejectedTransactions += status.count;
                break;
            }
          });
        }

        setTransactionStats(transformedStats);
      }
      
      return result;
    } catch (error) {
      console.error('Error fetching payment transaction stats:', error);
      console.error('API URL:', `${API_BASE_URL}/api/superadmin/payment-transactions/stats`);
      console.error('Headers:', getHeaders());

      // More specific error messages
      if (error.message.includes('401')) {
        toast.error('Authentication failed. Please log in again.');
      } else if (error.message.includes('403')) {
        toast.error('Access denied. SuperAdmin privileges required.');
      } else if (error.message.includes('404')) {
        toast.error('Payment statistics endpoint not found.');
      } else if (error.message.includes('500')) {
        toast.error('Server error while loading payment statistics.');
      } else {
        toast.error('Failed to load payment statistics');
      }
      return null;
    }
  }, [isSuperAdmin, getHeaders]);

  /**
   * Get single payment transaction details
   */
  const getTransactionById = useCallback(async (paymentId) => {
    if (!isSuperAdmin) return null;
    
    try {
      const response = await fetch(
        `${API_BASE_URL}/api/superadmin/payment-transactions/${paymentId}`,
        {
          method: 'GET',
          headers: getHeaders()
        }
      );

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      return result.success ? result.data : null;
    } catch (error) {
      console.error('Error fetching payment transaction details:', error);
      toast.error('Failed to load payment details');
      return null;
    }
  }, [isSuperAdmin, getHeaders]);

  /**
   * Approve payment transaction
   */
  const approveTransaction = useCallback(async (paymentId, notes = '') => {
    if (!isSuperAdmin) return null;
    
    try {
      const response = await fetch(
        `${API_BASE_URL}/api/superadmin/payment-transactions/${paymentId}/approve`,
        {
          method: 'POST',
          headers: getHeaders(),
          body: JSON.stringify({ notes })
        }
      );

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      
      if (result.success) {
        toast.success('Payment transaction approved successfully');
        // Update local state
        setTransactions(prev => 
          prev.map(transaction => 
            transaction.paymentId === paymentId 
              ? { ...transaction, status: 'approved', approvalNotes: notes }
              : transaction
          )
        );
        // Refresh stats
        fetchTransactionStats();
      }
      
      return result;
    } catch (error) {
      console.error('Error approving payment transaction:', error);
      toast.error('Failed to approve payment transaction');
      return null;
    }
  }, [isSuperAdmin, getHeaders, fetchTransactionStats]);

  /**
   * Reject payment transaction
   */
  const rejectTransaction = useCallback(async (paymentId, reason = '') => {
    if (!isSuperAdmin) return null;
    
    try {
      const response = await fetch(
        `${API_BASE_URL}/api/superadmin/payment-transactions/${paymentId}/reject`,
        {
          method: 'POST',
          headers: getHeaders(),
          body: JSON.stringify({ reason })
        }
      );

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      
      if (result.success) {
        toast.success('Payment transaction rejected successfully');
        // Update local state
        setTransactions(prev => 
          prev.map(transaction => 
            transaction.paymentId === paymentId 
              ? { ...transaction, status: 'rejected', rejectionReason: reason }
              : transaction
          )
        );
        // Refresh stats
        fetchTransactionStats();
      }
      
      return result;
    } catch (error) {
      console.error('Error rejecting payment transaction:', error);
      toast.error('Failed to reject payment transaction');
      return null;
    }
  }, [isSuperAdmin, getHeaders, fetchTransactionStats]);

  /**
   * Export payment transactions
   */
  const exportTransactions = useCallback(async (format = 'csv', filters = {}) => {
    if (!isSuperAdmin) return null;
    
    try {
      const queryParams = new URLSearchParams();
      queryParams.append('format', format);
      
      Object.keys(filters).forEach(key => {
        if (filters[key] && filters[key] !== 'all') {
          queryParams.append(key, filters[key]);
        }
      });

      const response = await fetch(
        `${API_BASE_URL}/api/superadmin/payment-transactions/export?${queryParams.toString()}`,
        {
          method: 'GET',
          headers: getHeaders()
        }
      );

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      // Handle file download
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      
      const timestamp = new Date().toISOString().split('T')[0];
      link.download = `payment-transactions-${timestamp}.${format}`;
      
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
      
      toast.success(`Payment transactions exported as ${format.toUpperCase()}`);
      
      return true;
    } catch (error) {
      console.error('Error exporting payment transactions:', error);
      toast.error('Failed to export payment transactions');
      return null;
    }
  }, [isSuperAdmin, getHeaders]);

  /**
   * Apply filters and fetch data
   */
  const applyFilters = useCallback((newFilters) => {
    const updatedFilters = { ...filters, ...newFilters };
    setFilters(updatedFilters);
    fetchTransactions({ ...updatedFilters, page: 1 });
  }, [filters, fetchTransactions]);

  /**
   * Change page
   */
  const changePage = useCallback((page) => {
    setPagination(prev => ({ ...prev, page }));
    fetchTransactions({ ...filters, page });
  }, [filters, fetchTransactions]);

  /**
   * Change page size
   */
  const changePageSize = useCallback((limit) => {
    setPagination(prev => ({ ...prev, limit, page: 1 }));
    fetchTransactions({ ...filters, limit, page: 1 });
  }, [filters, fetchTransactions]);

  /**
   * Refresh all data
   */
  const refreshData = useCallback(async () => {
    await Promise.all([
      fetchTransactions(filters),
      fetchTransactionStats()
    ]);
  }, [fetchTransactions, fetchTransactionStats, filters]);

  // Initial data fetch
  useEffect(() => {
    if (isSuperAdmin) {
      refreshData();
    }
  }, [isSuperAdmin]);

  return {
    // Data
    transactions,
    transactionStats,
    pagination,
    filters,
    loading,
    
    // Actions
    fetchTransactions,
    fetchTransactionStats,
    getTransactionById,
    approveTransaction,
    rejectTransaction,
    exportTransactions,
    applyFilters,
    changePage,
    changePageSize,
    refreshData,
    
    // Computed values
    isSuperAdmin
  };
}; 