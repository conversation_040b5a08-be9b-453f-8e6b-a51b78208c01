/**
 * SuperAdmin Payment Export Service
 * Handles payment data export functionality for SuperAdmin
 */
const { Payment } = require('../../models');
const { AppError, logInfo, logError } = require('../../utils');
const fs = require('fs').promises;
const path = require('path');

/**
 * Export payment data in various formats
 * @param {Object} options - Export options
 * @returns {Promise<Object>} Export result
 */
const exportPaymentData = async (options = {}) => {
  try {
    const {
      format = 'csv',
      status,
      method,
      startDate,
      endDate,
      search,
      fields = ['all'] // Default to all fields
    } = options;

    // Build query
    const query = {
      paymentContext: 'subscription',
      isDeleted: false
    };

    if (status) query.status = status;
    if (method) query.method = method;
    if (startDate && endDate) {
      query.createdAt = {
        $gte: new Date(startDate),
        $lte: new Date(endDate)
      };
    }
    if (search) {
      query.$or = [
        { customerName: { $regex: search, $options: 'i' } },
        { shopName: { $regex: search, $options: 'i' } },
        { paymentId: { $regex: search, $options: 'i' } }
      ];
    }

    // Get payments
    const payments = await Payment.find(query)
      .sort({ createdAt: -1 })
      .lean();

    if (payments.length === 0) {
      throw new AppError('No payment data found for export', 404, 'no_data_found');
    }

    let exportData;
    let contentType;
    let filename;

    switch (format.toLowerCase()) {
      case 'csv':
        exportData = await generateCSVExport(payments, fields);
        contentType = 'text/csv';
        filename = `payment-transactions-${new Date().toISOString().split('T')[0]}.csv`;
        break;
      
      case 'pdf':
        exportData = await generatePDFExport(payments, fields);
        contentType = 'application/pdf';
        filename = `payment-transactions-${new Date().toISOString().split('T')[0]}.pdf`;
        break;
      
      case 'json':
        exportData = await generateJSONExport(payments, fields);
        contentType = 'application/json';
        filename = `payment-transactions-${new Date().toISOString().split('T')[0]}.json`;
        break;
      
      default:
        throw new AppError('Unsupported export format', 400, 'unsupported_format');
    }

    logInfo(`Exported ${payments.length} payment records in ${format} format`, 'PaymentExportService');

    return {
      data: exportData,
      contentType,
      filename,
      recordCount: payments.length,
      exportDate: new Date().toISOString()
    };
  } catch (error) {
    logError(`Error exporting payment data: ${error.message}`, 'PaymentExportService', error);
    
    if (error instanceof AppError) {
      throw error;
    }
    
    throw new AppError('Failed to export payment data', 500, 'export_error');
  }
};

/**
 * Generate CSV export
 * @param {Array} payments - Payment data
 * @param {Array} fields - Fields to include
 * @returns {Promise<string>} CSV data
 */
const generateCSVExport = async (payments, fields) => {
  try {
    // Define available fields
    const availableFields = {
      paymentId: 'Payment ID',
      customerName: 'Customer Name',
      shopName: 'Shop Name',
      amount: 'Amount',
      originalAmount: 'Original Amount',
      discountAmount: 'Discount Amount',
      method: 'Payment Method',
      paymentType: 'Payment Type',
      status: 'Status',
      isConfirmed: 'Confirmed',
      referenceNumber: 'Reference Number',
      notes: 'Notes',
      createdAt: 'Created At',
      approvedAt: 'Approved At',
      approvedBy: 'Approved By',
      approvalNotes: 'Approval Notes'
    };

    // Determine which fields to include
    const selectedFields = fields.includes('all') 
      ? Object.keys(availableFields)
      : fields.filter(field => availableFields[field]);

    // Create headers
    const headers = selectedFields.map(field => availableFields[field]);

    // Create rows
    const rows = payments.map(payment => {
      return selectedFields.map(field => {
        let value = payment[field];
        
        // Format special fields
        if (field === 'amount' || field === 'originalAmount' || field === 'discountAmount') {
          value = value ? `$${value.toFixed(2)}` : '$0.00';
        } else if (field === 'isConfirmed') {
          value = value ? 'Yes' : 'No';
        } else if (field === 'createdAt' || field === 'approvedAt') {
          value = value ? new Date(value).toISOString() : 'N/A';
        } else if (value === null || value === undefined) {
          value = 'N/A';
        }
        
        // Escape CSV special characters
        if (typeof value === 'string' && (value.includes(',') || value.includes('"') || value.includes('\n'))) {
          value = `"${value.replace(/"/g, '""')}"`;
        }
        
        return value;
      });
    });

    // Combine headers and rows
    const csvContent = [headers, ...rows]
      .map(row => row.join(','))
      .join('\n');

    return csvContent;
  } catch (error) {
    logError(`Error generating CSV export: ${error.message}`, 'PaymentExportService', error);
    throw new AppError('Failed to generate CSV export', 500, 'csv_generation_error');
  }
};

/**
 * Generate PDF export
 * @param {Array} payments - Payment data
 * @param {Array} fields - Fields to include
 * @returns {Promise<Buffer>} PDF data
 */
const generatePDFExport = async (payments, fields) => {
  try {
    // This is a simplified PDF generation
    // In production, you would use a proper PDF library like PDFKit or Puppeteer
    
    const availableFields = {
      paymentId: 'Payment ID',
      customerName: 'Customer Name',
      shopName: 'Shop Name',
      amount: 'Amount',
      method: 'Payment Method',
      status: 'Status',
      createdAt: 'Created At'
    };

    const selectedFields = fields.includes('all') 
      ? Object.keys(availableFields)
      : fields.filter(field => availableFields[field]);

    // Create PDF content (simplified text-based PDF)
    let pdfContent = `Payment Transactions Report\n`;
    pdfContent += `Generated on: ${new Date().toISOString()}\n`;
    pdfContent += `Total Records: ${payments.length}\n\n`;

    // Add headers
    pdfContent += selectedFields.map(field => availableFields[field]).join(' | ') + '\n';
    pdfContent += '-'.repeat(selectedFields.length * 20) + '\n';

    // Add data rows
    payments.forEach((payment, index) => {
      const row = selectedFields.map(field => {
        let value = payment[field];
        
        if (field === 'amount') {
          value = value ? `$${value.toFixed(2)}` : '$0.00';
        } else if (field === 'createdAt') {
          value = value ? new Date(value).toISOString().split('T')[0] : 'N/A';
        } else if (value === null || value === undefined) {
          value = 'N/A';
        }
        
        return String(value).substring(0, 18).padEnd(18);
      });
      
      pdfContent += row.join(' | ') + '\n';
      
      // Add page break every 50 rows
      if ((index + 1) % 50 === 0) {
        pdfContent += '\n--- Page Break ---\n\n';
      }
    });

    // Add summary
    pdfContent += `\n--- Summary ---\n`;
    pdfContent += `Total Payments: ${payments.length}\n`;
    
    const totalAmount = payments.reduce((sum, payment) => sum + (payment.amount || 0), 0);
    pdfContent += `Total Amount: $${totalAmount.toFixed(2)}\n`;
    
    const successfulPayments = payments.filter(p => ['success', 'approved'].includes(p.status)).length;
    pdfContent += `Successful Payments: ${successfulPayments}\n`;
    pdfContent += `Success Rate: ${payments.length > 0 ? ((successfulPayments / payments.length) * 100).toFixed(2) : 0}%\n`;

    return Buffer.from(pdfContent, 'utf8');
  } catch (error) {
    logError(`Error generating PDF export: ${error.message}`, 'PaymentExportService', error);
    throw new AppError('Failed to generate PDF export', 500, 'pdf_generation_error');
  }
};

/**
 * Generate JSON export
 * @param {Array} payments - Payment data
 * @param {Array} fields - Fields to include
 * @returns {Promise<string>} JSON data
 */
const generateJSONExport = async (payments, fields) => {
  try {
    const availableFields = [
      'paymentId', 'customerName', 'shopName', 'amount', 'originalAmount', 
      'discountAmount', 'method', 'paymentType', 'status', 'isConfirmed',
      'referenceNumber', 'notes', 'createdAt', 'approvedAt', 'approvedBy', 'approvalNotes'
    ];

    const selectedFields = fields.includes('all') 
      ? availableFields
      : fields.filter(field => availableFields.includes(field));

    // Filter and format payments
    const formattedPayments = payments.map(payment => {
      const formatted = {};
      
      selectedFields.forEach(field => {
        let value = payment[field];
        
        // Format special fields
        if (field === 'amount' || field === 'originalAmount' || field === 'discountAmount') {
          value = value ? parseFloat(value.toFixed(2)) : 0;
        } else if (field === 'createdAt' || field === 'approvedAt') {
          value = value ? new Date(value).toISOString() : null;
        }
        
        formatted[field] = value;
      });
      
      return formatted;
    });

    const exportData = {
      metadata: {
        exportDate: new Date().toISOString(),
        recordCount: formattedPayments.length,
        fields: selectedFields,
        filters: {
          status: payments.length > 0 ? [...new Set(payments.map(p => p.status))] : [],
          methods: payments.length > 0 ? [...new Set(payments.map(p => p.method))] : []
        }
      },
      data: formattedPayments
    };

    return JSON.stringify(exportData, null, 2);
  } catch (error) {
    logError(`Error generating JSON export: ${error.message}`, 'PaymentExportService', error);
    throw new AppError('Failed to generate JSON export', 500, 'json_generation_error');
  }
};

/**
 * Get available export fields
 * @returns {Object} Available fields for export
 */
const getAvailableExportFields = () => {
  return {
    paymentId: {
      label: 'Payment ID',
      description: 'Unique payment identifier',
      type: 'string'
    },
    customerName: {
      label: 'Customer Name',
      description: 'Shop owner full name',
      type: 'string'
    },
    shopName: {
      label: 'Shop Name',
      description: 'Shop name',
      type: 'string'
    },
    amount: {
      label: 'Amount',
      description: 'Payment amount',
      type: 'number'
    },
    originalAmount: {
      label: 'Original Amount',
      description: 'Original amount before discounts',
      type: 'number'
    },
    discountAmount: {
      label: 'Discount Amount',
      description: 'Discount amount applied',
      type: 'number'
    },
    method: {
      label: 'Payment Method',
      description: 'Payment method used',
      type: 'string'
    },
    paymentType: {
      label: 'Payment Type',
      description: 'Online or offline payment',
      type: 'string'
    },
    status: {
      label: 'Status',
      description: 'Payment status',
      type: 'string'
    },
    isConfirmed: {
      label: 'Confirmed',
      description: 'Whether payment is confirmed',
      type: 'boolean'
    },
    referenceNumber: {
      label: 'Reference Number',
      description: 'Payment reference number',
      type: 'string'
    },
    notes: {
      label: 'Notes',
      description: 'Payment notes',
      type: 'string'
    },
    createdAt: {
      label: 'Created At',
      description: 'Payment creation date',
      type: 'date'
    },
    approvedAt: {
      label: 'Approved At',
      description: 'Payment approval date',
      type: 'date'
    },
    approvedBy: {
      label: 'Approved By',
      description: 'User who approved the payment',
      type: 'string'
    },
    approvalNotes: {
      label: 'Approval Notes',
      description: 'Notes from approval process',
      type: 'string'
    }
  };
};

/**
 * Get export templates
 * @returns {Object} Predefined export templates
 */
const getExportTemplates = () => {
  return {
    basic: {
      name: 'Basic Export',
      description: 'Essential payment information',
      fields: ['paymentId', 'customerName', 'shopName', 'amount', 'method', 'status', 'createdAt']
    },
    detailed: {
      name: 'Detailed Export',
      description: 'Complete payment information with approval details',
      fields: ['paymentId', 'customerName', 'shopName', 'amount', 'originalAmount', 'discountAmount', 'method', 'status', 'isConfirmed', 'createdAt', 'approvedAt', 'approvedBy']
    },
    financial: {
      name: 'Financial Export',
      description: 'Financial data for accounting purposes',
      fields: ['paymentId', 'customerName', 'shopName', 'amount', 'originalAmount', 'discountAmount', 'method', 'status', 'createdAt', 'approvedAt']
    },
    approval: {
      name: 'Approval Export',
      description: 'Payment approval workflow data',
      fields: ['paymentId', 'customerName', 'shopName', 'amount', 'status', 'createdAt', 'approvedAt', 'approvedBy', 'approvalNotes']
    }
  };
};

module.exports = {
  exportPaymentData,
  generateCSVExport,
  generatePDFExport,
  generateJSONExport,
  getAvailableExportFields,
  getExportTemplates
}; 