"use client";

import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON>er, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Eye, Download, X, Check, FileText } from "lucide-react";

export function PaymentCard({ 
  payment, 
  formatCurrency, 
  formatDate, 
  onViewProof, 
  onDownload, 
  onVerify, 
  onReject 
}) {
  // Status badge component with appropriate styling
  const StatusBadge = ({ status }) => {
    const variants = {
      pending: "outline",
      verified: "success",
      rejected: "destructive"
    };
    
    const labels = {
      pending: "Pending",
      verified: "Verified",
      rejected: "Rejected"
    };
    
    return (
      <Badge variant={variants[status]} className="ml-2">
        {labels[status]}
      </Badge>
    );
  };

  return (
    <Card className="overflow-hidden transition-all hover:shadow-md">
      <CardHeader className="pb-2 md:pb-4">
        {/* Mobile-friendly header with responsive layout */}
        <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between gap-2">
          <div className="space-y-1">
            <div className="flex items-center flex-wrap gap-2">
              <CardTitle className="text-base md:text-lg">
                {payment.shopName}
              </CardTitle>
              <StatusBadge status={payment.status} />
            </div>
            <CardDescription className="text-xs md:text-sm break-all">
              Payment ID: {payment.paymentId}
              <br className="sm:hidden" />
              <span className="hidden sm:inline"> | </span>
              Subscription: {payment.subscriptionId}
            </CardDescription>
          </div>
          <div className="flex items-baseline gap-2 sm:text-right mt-2 sm:mt-0">
            <div className="font-bold text-base md:text-lg">{formatCurrency(payment.amount)}</div>
            <div className="text-xs text-muted-foreground">
              {payment.plan} Plan
            </div>
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="pb-2">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* Payment details - optimized for small screens */}
          <div className="space-y-2">
            <h4 className="text-sm font-medium flex items-center gap-2">
              <FileText className="h-4 w-4 text-muted-foreground" /> 
              Payment Details
            </h4>
            <div className="text-sm space-y-2 bg-muted/30 rounded-md p-3">
              <div className="flex flex-col sm:flex-row justify-between gap-1">
                <span className="text-muted-foreground text-xs">Payment Method:</span>
                <span className="font-medium">{payment.paymentMethod}</span>
              </div>
              <div className="flex flex-col sm:flex-row justify-between gap-1">
                <span className="text-muted-foreground text-xs">Transaction ID:</span>
                <span className="font-medium">{payment.transactionId || "N/A"}</span>
              </div>
              <div className="flex flex-col sm:flex-row justify-between gap-1">
                <span className="text-muted-foreground text-xs">Date:</span>
                <span className="font-medium">{formatDate(payment.paymentDate)}</span>
              </div>
            </div>
          </div>
          
          {/* Notes section */}
          <div className="space-y-2">
            <h4 className="text-sm font-medium">Notes</h4>
            <div className="text-sm bg-muted/30 rounded-md p-3 h-full min-h-[80px]">
              <p className="text-sm">
                {payment.note || "No notes provided by the shop."}
              </p>
              {payment.verificationNote && (
                <div className="mt-3 pt-3 border-t border-border">
                  <span className="text-xs font-medium block mb-1 text-muted-foreground">
                    Verification Note:
                  </span>
                  <p className="text-sm font-medium">{payment.verificationNote}</p>
                </div>
              )}
            </div>
          </div>
        </div>
      </CardContent>
      
      {/* Actions footer - responsive layout */}
      <CardFooter className="flex flex-col sm:flex-row sm:justify-between gap-3 border-t bg-muted/50 px-4 py-3">
        <div className="flex gap-2 w-full sm:w-auto justify-center sm:justify-start">
          <Button
            size="sm"
            variant="outline"
            onClick={() => onViewProof(payment)}
            className="flex-1 sm:flex-none"
          >
            <Eye className="mr-2 h-4 w-4" />
            View Proof
          </Button>
          <Button
            size="sm"
            variant="outline"
            onClick={() => onDownload(payment)}
            className="flex-1 sm:flex-none"
          >
            <Download className="mr-2 h-4 w-4" />
            Download
          </Button>
        </div>
        
        {payment.status === "pending" && (
          <div className="flex gap-2 w-full sm:w-auto justify-center sm:justify-end">
            <Button
              size="sm"
              variant="destructive"
              onClick={() => onReject(payment)}
              className="flex-1 sm:flex-none"
            >
              <X className="mr-2 h-4 w-4" />
              Reject
            </Button>
            <Button
              size="sm"
              onClick={() => onVerify(payment)}
              className="flex-1 sm:flex-none"
            >
              <Check className="mr-2 h-4 w-4" />
              Verify
            </Button>
          </div>
        )}
      </CardFooter>
    </Card>
  );
}
