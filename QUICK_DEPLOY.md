# DeynCare Frontend - Quick Deployment Guide

Your DeynCare frontend is configured to deploy to:
- **Frontend URL**: https://deyncare.cajiibcreative.com/
- **Backend API**: https://deyncare-backend.khanciye.com/

## Quick Deployment Steps

### 1. Setup Environment (First Time Only)
```bash
# Make scripts executable
chmod +x setup-env.sh deploy.sh

# Setup environment variables (quick setup - URLs already configured)
./setup-env.sh
```

Or create manually (even simpler):
```bash
# Copy the pre-configured environment file
cp env.production.example .env.production
# That's it! Your URLs are already set correctly.
```

### 2. Deploy Application
```bash
# Full deployment (automated)
./deploy.sh
```

That's it! The deployment script will handle:
- Installing Node.js and PM2
- Installing dependencies
- Building the application
- Starting with PM2
- Configuring auto-start on boot

## Manual Steps (Alternative)

If you prefer manual deployment:

```bash
# 1. Install dependencies
npm ci --production=false

# 2. Create environment file
cp env.production.example .env.production
# Edit .env.production with your settings (URLs are already configured)

# 3. Build the application
npm run build

# 4. Start with PM2
pm2 start ecosystem.config.js --env production
pm2 save
sudo pm2 startup systemd -u $USER --hp $HOME
```

## Verification

Check if everything is working:

```bash
# Check PM2 status
pm2 status

# View logs
pm2 logs deyncare-frontend

# Test local response
curl http://localhost:3000

# Test public URL (should work with your nginx setup)
curl https://deyncare.cajiibcreative.com
```

## Management Commands

```bash
# Restart application
pm2 restart deyncare-frontend

# View real-time logs
pm2 logs deyncare-frontend

# Monitor performance
pm2 monit

# Update application
cd /var/www/deyncare-frontend
git pull origin main
npm ci --production=false
npm run build
pm2 restart deyncare-frontend
```

## Nginx Configuration

Your nginx should proxy to `http://localhost:3000`. Since you already have nginx and SSL configured, just ensure your configuration includes:

```nginx
location / {
    proxy_pass http://localhost:3000;
    proxy_http_version 1.1;
    proxy_set_header Upgrade $http_upgrade;
    proxy_set_header Connection 'upgrade';
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
    proxy_cache_bypass $http_upgrade;
}
```

## Troubleshooting

**Application won't start:**
```bash
pm2 logs deyncare-frontend --lines 50
```

**Port 3000 already in use:**
```bash
sudo netstat -tulpn | grep :3000
sudo kill -9 <PID>
```

**Environment issues:**
```bash
pm2 show deyncare-frontend
```

## Configuration Files

- `env.production.example` - Environment template with your URLs
- `ecosystem.config.js` - PM2 configuration
- `deploy.sh` - Automated deployment script
- `setup-env.sh` - Environment setup helper
- `DEPLOYMENT_GUIDE.md` - Complete deployment documentation

## Next Steps After Deployment

1. Verify frontend at: https://deyncare.cajiibcreative.com/
2. Test login functionality with your backend
3. Check all API integrations are working
4. Monitor application performance with `pm2 monit`
5. Set up log rotation if needed

Your application is now ready for production use! 