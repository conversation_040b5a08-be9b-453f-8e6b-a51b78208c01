/**
 * Update Plan Features Controller
 * Allows SuperAdmin to dynamically control features for each plan
 */
const { PlanService } = require('../../services');
const { validate } = require('../../middleware/validationMiddleware');
const { planSchemas } = require('../../validations');
const { logError, logSuccess } = require('../../utils');

/**
 * Update plan features
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
const updatePlanFeatures = async (req, res, next) => {
  try {
    const { planId } = req.params;
    const { features } = req.body;
    const { userId, role } = req.user;

    // Only SuperAdmin can update plan features
    if (role !== 'superAdmin') {
      return res.status(403).json({
        success: false,
        message: 'Only SuperAdmin can update plan features',
        statusCode: 403,
        type: 'permission_denied'
      });
    }

    // Validate features object
    const validFeatures = [
      'debtTracking',
      'customerPayments', 
      'smsReminders',
      'smartRiskScore',
      'businessDashboard',
      'exportReports',
      'customerProfiles',
      'offlineSupport'
    ];

    // Validate that all provided features are valid
    const providedFeatures = Object.keys(features || {});
    const invalidFeatures = providedFeatures.filter(feature => !validFeatures.includes(feature));
    
    if (invalidFeatures.length > 0) {
      return res.status(400).json({
        success: false,
        message: `Invalid features: ${invalidFeatures.join(', ')}. Valid features are: ${validFeatures.join(', ')}`,
        statusCode: 400,
        type: 'invalid_features'
      });
    }

    // Update plan features
    const updatedPlan = await PlanService.updatePlan(planId, { features }, { actorId: userId });

    logSuccess(`Plan ${planId} features updated by ${userId}`, 'PlanController');

    return res.status(200).json({
      success: true,
      message: 'Plan features updated successfully',
      data: {
        planId: updatedPlan.planId,
        name: updatedPlan.name,
        type: updatedPlan.type,
        features: updatedPlan.features,
        updatedAt: updatedPlan.updatedAt
      }
    });
  } catch (error) {
    logError('Failed to update plan features', 'PlanController', error);
    return next(error);
  }
};

/**
 * Get plan features
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
const getPlanFeatures = async (req, res, next) => {
  try {
    const { planId } = req.params;

    const plan = await PlanService.getPlanById(planId);

    if (!plan) {
      return res.status(404).json({
        success: false,
        message: 'Plan not found',
        statusCode: 404,
        type: 'plan_not_found'
      });
    }

    return res.status(200).json({
      success: true,
      message: 'Plan features retrieved successfully',
      data: {
        planId: plan.planId,
        name: plan.name,
        type: plan.type,
        features: plan.features,
        limits: plan.limits
      }
    });
  } catch (error) {
    logError('Failed to get plan features', 'PlanController', error);
    return next(error);
  }
};

/**
 * Get all available features
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
const getAllFeatures = async (req, res, next) => {
  try {
    const availableFeatures = [
      {
        key: 'debtTracking',
        name: 'Debt Tracking',
        description: 'Track and manage customer debts',
        category: 'core'
      },
      {
        key: 'customerPayments',
        name: 'Customer Payments',
        description: 'Process and track customer payments',
        category: 'core'
      },
      {
        key: 'smsReminders',
        name: 'SMS Reminders',
        description: 'Send automated SMS reminders to customers',
        category: 'communication'
      },
      {
        key: 'smartRiskScore',
        name: 'Smart Risk Score',
        description: 'AI-powered risk assessment for customers',
        category: 'analytics'
      },
      {
        key: 'businessDashboard',
        name: 'Business Dashboard',
        description: 'Comprehensive business analytics dashboard',
        category: 'analytics'
      },
      {
        key: 'exportReports',
        name: 'Export Reports',
        description: 'Export data and generate reports',
        category: 'data'
      },
      {
        key: 'customerProfiles',
        name: 'Customer Profiles',
        description: 'Detailed customer profile management',
        category: 'core'
      },
      {
        key: 'offlineSupport',
        name: 'Offline Support',
        description: 'Work offline and sync when online',
        category: 'core'
      }
    ];

    return res.status(200).json({
      success: true,
      message: 'Available features retrieved successfully',
      data: {
        features: availableFeatures,
        totalFeatures: availableFeatures.length
      }
    });
  } catch (error) {
    logError('Failed to get available features', 'PlanController', error);
    return next(error);
  }
};

module.exports = {
  updatePlanFeatures,
  getPlanFeatures,
  getAllFeatures
}; 