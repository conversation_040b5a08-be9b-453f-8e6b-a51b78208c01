/**
 * React hooks for authentication
 */
import { useContext } from 'react';
import { AuthContext } from './auth-provider';

/**
 * Custom hook to access authentication context
 * @returns {Object} - Authentication context value
 * @throws {Error} - If used outside of AuthProvider
 */
export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
};
