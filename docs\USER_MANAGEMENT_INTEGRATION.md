# DeynCare User Management Integration Guide

This document provides guidance on how to integrate the new user management services with your existing application components.

## Architecture Overview

The user management system follows a layered architecture:

1. **API Bridge Layer** (`lib/api/bridge.js`)
   - Centralized API client with enhanced functionality
   - Token management and standardized error handling

2. **Service Layer** (`lib/services/user/`)
   - Business logic implementation
   - Data transformation and validation

3. **Context Layer** (`contexts/user-context.jsx`)
   - Global state management for user data
   - Shared operations across components

4. **Hook Layer** (`hooks/use-users-enhanced.js`)
   - Component-level access to user operations
   - Compatible with existing component structure

## Role-Based Requirements

The user management system enforces the following role-based rules:

1. **SuperAdmin Users**:
   - Must be associated with an actual shop that exists in the database
   - "No Shop" option is not valid for superAdmin users
   - Can create any role (including other superAdmins)

2. **Admin Users**:
   - Can only create employees for their own shop
   - Must have a valid shopId

3. **Employee Users**:
   - Limited permissions
   - Must have a valid shopId

## Integration Options

### Option 1: Direct Use of UserService

For new components, you can directly use the UserService:

```jsx
import { useEffect, useState } from 'react';
import UserService from '@/lib/services/user';

function UsersList() {
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(true);
  
  useEffect(() => {
    const loadUsers = async () => {
      setLoading(true);
      try {
        const userData = await UserService.getUsers();
        setUsers(userData);
      } catch (error) {
        console.error('Error loading users:', error);
      } finally {
        setLoading(false);
      }
    };
    
    loadUsers();
  }, []);
  
  return (
    <div>
      <h2>Users</h2>
      {loading ? (
        <p>Loading...</p>
      ) : (
        <ul>
          {users.map(user => (
            <li key={user.userId}>{user.fullName}</li>
          ))}
        </ul>
      )}
    </div>
  );
}
```

### Option 2: Using UserContext

For components that need shared user state:

```jsx
import { useUserContext } from '@/contexts/user-context';

function UsersList() {
  const { users, loading, loadUsers } = useUserContext();
  
  useEffect(() => {
    loadUsers();
  }, [loadUsers]);
  
  return (
    <div>
      <h2>Users</h2>
      {loading ? (
        <p>Loading...</p>
      ) : (
        <ul>
          {users.map(user => (
            <li key={user.userId}>{user.fullName}</li>
          ))}
        </ul>
      )}
    </div>
  );
}
```

### Option 3: Using Enhanced Hook (Recommended)

For components that need user operations with a familiar API:

```jsx
import { useUsersEnhanced } from '@/hooks/use-users-enhanced';

function UsersListWithFilters() {
  const {
    users,
    isLoading,
    pagination,
    filters,
    changePage,
    applyFilters
  } = useUsersEnhanced();
  
  const handleFilterChange = (newFilters) => {
    applyFilters(newFilters);
  };
  
  const handlePageChange = (page) => {
    changePage(page);
  };
  
  return (
    <div>
      <h2>Users</h2>
      <UserFilters onFilterChange={handleFilterChange} />
      
      {isLoading ? (
        <p>Loading...</p>
      ) : (
        <>
          <UsersList users={users} />
          <Pagination 
            currentPage={pagination.currentPage}
            totalPages={pagination.totalPages}
            onPageChange={handlePageChange}
          />
        </>
      )}
    </div>
  );
}
```

### Option 4: Compatibility Layer (For Existing Components)

For existing components that use the old UserService:

```jsx
// The user-service.js in components/dashboard/users/ provides compatibility
// You can continue using it in existing components without changes
import UserService from './user-service';

// Existing code continues to work
const userService = new UserService();
const users = await userService.getUsers();
```

## Setting Up Providers

To use the UserContext and enhanced hooks, wrap your application with the UserProvider:

```jsx
// In your layout.js or _app.js
import { UserProvider } from '@/contexts/user-context';
import { AuthProvider } from '@/contexts/auth-context';

export default function AppLayout({ children }) {
  return (
    <AuthProvider>
      <UserProvider>
        {children}
      </UserProvider>
    </AuthProvider>
  );
}
```

## Common Operations

### Creating a User

```jsx
const { createUser } = useUserContext();

// Or directly
import UserService from '@/lib/services/user';

// Create user with role-based validation
const handleCreateUser = async (userData) => {
  try {
    // Role-based validation is handled in the service
    const newUser = await createUser(userData);
    // Handle success
  } catch (error) {
    // Error already handled by service with toast
  }
};
```

### Updating a User

```jsx
const { updateUser } = useUserContext();

const handleUpdateUser = async (userId, userData) => {
  try {
    const updatedUser = await updateUser(userId, userData);
    // Handle success
  } catch (error) {
    // Error already handled by service with toast
  }
};
```

### Changing User Status

```jsx
const { changeUserStatus } = useUserContext();

const handleSuspendUser = async (userId) => {
  try {
    // Suspension requires a reason
    const reason = "Violation of terms of service";
    const updatedUser = await changeUserStatus(userId, 'suspended', reason);
    // Handle success
  } catch (error) {
    // Error already handled by service with toast
  }
};
```

## Best Practices

1. **Use the Enhanced Hook** for most components that need user management
2. **Leverage UserContext** for shared state across components
3. **Direct Service Use** for simple one-off operations
4. **Compatibility Layer** for maintaining existing components

## Special Considerations

### SuperAdmin and Shop Association

Remember that superAdmin users must be associated with a shop:

```jsx
// Ensure shop selection for superAdmin
const handleRoleChange = (role) => {
  setRole(role);
  
  // If role is superAdmin and no shop is selected, select the first available shop
  if (role === 'superAdmin' && (!selectedShop || selectedShop === 'no-shop')) {
    if (shops.length > 0) {
      setSelectedShop(shops[0].id);
    } else {
      toast.error('SuperAdmin users must be associated with a shop');
    }
  }
};
```

### Admin Permissions

Admin users can only create employees for their own shop:

```jsx
// Ensure admin can only select their own shop
const { user: currentUser } = useAuth();

useEffect(() => {
  // If current user is admin, pre-select their shop and disable shop selection
  if (currentUser && currentUser.role === 'admin') {
    setSelectedShop(currentUser.shopId);
    setDisableShopSelection(true);
  }
}, [currentUser]);
```

---

By following this guide, you can seamlessly integrate the new user management system while maintaining compatibility with your existing components.
