import React, { useState } from 'react';
import { RegistrationDialog } from './registration-dialog';
import { SuspensionDialog } from './suspension-dialog';
import { ShopDetailsDialog } from './shop-details/ShopDetailsDialog';
import { ShopFormDialog } from './shop-form-dialog';
import { DeleteShopDialog } from './delete-shop-dialog';

export const ShopsDialogs = ({
  // Registration dialog props
  registrationDialogOpen,
  setRegistrationDialogOpen,
  onRegistrationSuccess,
  
  // Suspension dialog props
  suspensionDialogOpen,
  setSuspensionDialogOpen,
  currentShop,
  onSuspensionConfirm,
  suspensionMode,
  
  // Details dialog props
  detailsDialogOpen,
  setDetailsDialogOpen,
  onEditShop,
  
  // Shop form dialog props
  formDialogOpen = false,
  setFormDialogOpen = () => {},
  shopToEdit = null,
  onShopUpdate = () => {},
  
  // Delete shop dialog props
  deleteDialogOpen = false,
  setDeleteDialogOpen = () => {},
  shopToDelete = null,
  onDeleteShop = () => {}
}) => {
  // Local state for form dialog submission
  const [isSubmittingForm, setIsSubmittingForm] = useState(false);
  // Local state for delete dialog submission
  const [isDeleting, setIsDeleting] = useState(false);
  
  // Handle shop form submission
  const handleFormSubmit = async (data, isEdit) => {
    setIsSubmittingForm(true);
    try {
      console.log('[ShopsDialogs] Form submission data:', data);
      
      if (isEdit && shopToEdit) {
        // For editing existing shop
        await onShopUpdate(shopToEdit.shopId, data);
      } else {
        // For creating new shop - prepare data for backend
        // Note: The form data is already transformed in shop-form-dialog.jsx
        await onShopUpdate(null, data);
      }
      
      // Close form dialog on success
      setFormDialogOpen(false);
    } catch (error) {
      console.error('Shop form submission error:', error);
    } finally {
      setIsSubmittingForm(false);
    }
  };
  
  // Handle shop deletion
  const handleDelete = async (shopId, reason) => {
    setIsDeleting(true);
    try {
      await onDeleteShop(shopId, reason);
      setDeleteDialogOpen(false);
    } catch (error) {
      console.error('Shop deletion error:', error);
    } finally {
      setIsDeleting(false);
    }
  };
  return (
    <>
      {/* Registration Dialog */}
      <RegistrationDialog
        open={registrationDialogOpen}
        onOpenChange={setRegistrationDialogOpen}
        onSuccess={onRegistrationSuccess}
      />
      
      {/* Suspension Dialog */}
      <SuspensionDialog
        open={suspensionDialogOpen}
        onOpenChange={setSuspensionDialogOpen}
        shop={currentShop}
        onConfirm={onSuspensionConfirm}
        mode={suspensionMode}
      />
      
      {/* Shop Details Dialog */}
      <ShopDetailsDialog
        open={detailsDialogOpen}
        onOpenChange={setDetailsDialogOpen}
        shop={currentShop}
        onEdit={onEditShop}
      />
      
      {/* Shop Form Dialog - for creating and editing shops */}
      <ShopFormDialog
        isOpen={formDialogOpen}
        onClose={() => setFormDialogOpen(false)}
        initialData={shopToEdit}
        onSubmit={handleFormSubmit}
        isSubmitting={isSubmittingForm}
      />
      
      {/* Delete Shop Dialog */}
      <DeleteShopDialog
        isOpen={deleteDialogOpen}
        onClose={() => setDeleteDialogOpen(false)}
        shopToDelete={shopToDelete}
        onDelete={handleDelete}
        isDeleting={isDeleting}
      />
    </>
  );
};

export default ShopsDialogs;
