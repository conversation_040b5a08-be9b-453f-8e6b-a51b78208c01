/**
 * Get All Subscriptions Service
 * Handles retrieving all subscriptions with filtering, sorting, and pagination
 */
const { Subscription, Shop, PricingPlan } = require('../../models');
const { AppError, logError } = require('../../utils');

/**
 * Get all subscriptions with filtering, sorting, and pagination
 * @param {Object} filter - Filter criteria
 * @param {Object} options - Pagination and sorting options
 * @returns {Promise<Object>} Paginated subscriptions result
 */
const getAllSubscriptions = async (filter = {}, options = {}) => {
  try {
    // Default options
    const defaultOptions = {
      page: 1,
      limit: 10,
      sort: { createdAt: -1 }
    };

    // Merge options
    const queryOptions = { ...defaultOptions, ...options };

    // Add isDeleted filter to exclude deleted subscriptions
    const queryFilter = {
      ...filter,
      isDeleted: false
    };

    // Execute paginated query WITHOUT populate (since shopId is string, not ObjectId)
    const result = await Subscription.paginate(queryFilter, queryOptions);

    // Get unique shop IDs for batch fetching
    const shopIds = [...new Set(result.docs.map(sub => sub.shopId).filter(Boolean))];
    
    // Batch fetch shop data using shopId strings
    const shops = await Shop.find({ 
      shopId: { $in: shopIds } 
    }).select('shopId shopName businessName email phone status').lean();
    
    // Create a shop lookup map
    const shopLookup = shops.reduce((acc, shop) => {
      acc[shop.shopId] = {
        name: shop.shopName || shop.businessName,
        email: shop.email,
        phone: shop.phone,
        status: shop.status
      };
      return acc;
    }, {});

    // Transform and enrich the subscription data
    const enrichedSubscriptions = result.docs.map((subscription) => {
      const subData = subscription.toObject();
      
      // Get shop data from lookup
      const shopData = shopLookup[subData.shopId] || { name: 'Unknown Shop' };

      // Ensure proper data formatting
      return {
        ...subData,
        // Fix shop data
        shop: shopData,
        shopName: shopData.name,
        
        // Fix period data
        startDate: subData.dates?.startDate || subData.startDate,
        endDate: subData.dates?.endDate || subData.endDate,
        
        // Fix payment data
        pricing: {
          ...subData.pricing,
          amount: subData.pricing?.basePrice || subData.pricing?.amount || 0,
          currency: subData.pricing?.currency || 'USD',
          billingCycle: subData.pricing?.billingCycle || subData.plan?.type || 'monthly'
        },
        
        // Fix payment method
        paymentMethod: subData.payment?.method || 'offline',
        
        // Fix auto renewal
        autoRenewal: subData.renewalSettings?.autoRenew ?? true,
        
        // Calculate remaining days
        remainingDays: subData.dates?.endDate ? 
          Math.max(0, Math.ceil((new Date(subData.dates.endDate) - new Date()) / (1000 * 60 * 60 * 24))) : 
          null
      };
    });

    // Transform the result to match expected format
    return {
      subscriptions: enrichedSubscriptions,
      pagination: {
        currentPage: result.page,
        totalPages: result.totalPages,
        totalItems: result.totalDocs,
        itemsPerPage: result.limit,
        hasNextPage: result.hasNextPage,
        hasPrevPage: result.hasPrevPage
      },
      summary: {
        total: result.totalDocs,
        active: enrichedSubscriptions.filter(sub => sub.status === 'active').length,
        trial: enrichedSubscriptions.filter(sub => sub.status === 'trial').length,
        expired: enrichedSubscriptions.filter(sub => sub.status === 'expired').length,
        canceled: enrichedSubscriptions.filter(sub => sub.status === 'canceled').length
      }
    };
  } catch (error) {
    logError('Failed to get all subscriptions', 'SubscriptionService', error);
    throw error;
  }
};

module.exports = getAllSubscriptions; 