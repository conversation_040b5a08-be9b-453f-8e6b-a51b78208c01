# Transaction (Payment) API Documentation

This document outlines the API endpoints related to payments within the DeynCare backend, which represent the core transaction handling. It includes details on access levels for different user roles.

Support for both online (EVC Plus) and offline (Cash) payment methods is integrated into these APIs.

## Base Path

`/api/payments`

## Authentication

All payment routes require authentication. Users must include a valid authentication token in the request headers.

## Endpoints

### `GET /api/payments/unconfirmed`

Retrieves a list of unconfirmed payments.

- **Purpose**: Allows authorized users to view payments that are pending confirmation. This can include online payments awaiting confirmation or offline payments initially recorded as pending.
- **Query Parameters**:
  - `hours` (optional): Filter for payments older than a specified number of hours.
  - `limit` (optional): Limit the number of results returned.

- **Access Control**:
  - **SuperAdmin**: Can view **all** unconfirmed payments across the entire system. The `shopId` query parameter is **not required** for SuperAdmins; they have global visibility.
  - **Admin**: Can view unconfirmed payments **only for their assigned shop**. The system uses the `shopId` associated with the Admin user's account (`req.user.shopId`) to filter the results. If an Admin user is not assigned to a shop, they will receive an error.

- **Implementation Details (Reflects Recent Updates)**:
  - The controller (`paymentController.js`) retrieves the user's role (`req.user.role`) and assigned shop ID (`req.user.shopId`).
  - It passes this information to the service layer (`paymentService.js`).
  - The service constructs a database query: includes the user's `shopId` filter if the user is an Admin, or omits the `shopId` filter if the user is a SuperAdmin.
  - The model (`payment.model.js`) handles the actual data fetching based on the constructed query.

### `POST /api/payments`

Creates a new payment record.

- **Purpose**: To record a new payment initiated within the system (e.g., via POS, online checkout, or manually). This endpoint is used for both online payments (often creating a 'pending' record initially) and offline payments (which might be recorded directly as 'confirmed' depending on workflow).
- **Access Control**: Typically requires authentication. Specific roles allowed to create payments may vary based on the payment context (e.g., users for their own payments, Admins for shop-related payments).
- **Payload**: Includes details like `shopId`, `customerId`, `amount`, `method` ('Cash', 'EVC Plus', etc.), `paymentContext`, and other relevant information depending on the context.

### `POST /api/payments/evc`

Processes a payment using the EVC Plus (WaafiPay) integration.

- **Purpose**: Initiates an online payment through the WaafiPay API. This is specifically for the EVC method.
- **Access Control**: Requires authentication. The specific role might depend on the application flow (e.g., users making payments, Admins initiating test payments).
- **Payload**: Includes necessary details for the EVC transaction, such as phone number, amount, and references.

### `POST /api/payments/:paymentId/confirm`

Confirms a pending payment.

- **Purpose**: Marks a pending payment record as confirmed, typically after verifying the transaction through external means (e.g., checking gateway status for online payments or manual verification for offline payments).
- **Access Control**: Requires authorization. Likely limited to roles with administrative privileges over payments, such as SuperAdmin or Admin (restricted to confirming payments within their assigned shop).
- **URL Parameters**: `:paymentId` - The ID of the payment to confirm.

### `POST /api/payments/:paymentId/refund`

Records a refund for a confirmed payment.

- **Purpose**: Updates a payment record to reflect that a refund has been processed.
- **Note**: As discussed, the WaafiPay API itself does not support initiating refunds via the API. This endpoint likely records *that* a refund has occurred externally for both online and offline payments.
- **Access Control**: Requires authorization. Likely limited to roles with administrative privileges over payments, such as SuperAdmin or Admin (restricted to refunding payments within their assigned shop).
- **URL Parameters**: `:paymentId` - The ID of the payment to refund.
- **Payload**: Includes details like the `amount` refunded and the `reason`. 