const { Shop } = require('../../models');
const { User, Subscription, Customer, Debt, Payment, Notification, Log, FinancialSnapshot, File, DiscountCode, Report, Session, Setting, ShopSetting } = require('../../models');
const { 
  AppError,
  LogHelper,
  logSuccess,
  logError,
  logInfo
} = require('../../utils');

/**
 * Delete shop (HARD DELETE ONLY with cascade)
 * @param {string} shopId - Shop ID to delete
 * @param {Object} options - Additional options
 * @param {string} options.actorId - ID of the actor performing the deletion
 * @param {string} options.actorRole - Role of the actor performing the deletion
 * @param {string} options.reason - Reason for deletion
 * @returns {Object} Result with success status
 */
const deleteShop = async (shopId, options = {}) => {
  try {
    // Validate shop exists
    const shop = await Shop.findOne({ shopId });
    
    if (!shop) {
      throw new AppError(
        'Shop not found',
        404,
        'shop_not_found'
      );
    }
    
    // Extract options
    const {
      actorId = 'system',
      actorRole = 'system',
      reason = 'Unspecified'
    } = options;

    // HARD DELETE WITH CASCADE - ALWAYS
    logInfo(`Starting hard delete with cascade for shop ${shopId}`, 'ShopService');

    let deletionResult = {};

    // Delete all related records
    
    // 1. Delete all users associated with this shop
    const deletedUsers = await User.deleteMany({ shopId: shopId });
    logInfo(`Deleted ${deletedUsers.deletedCount} users for shop ${shopId}`, 'ShopService');
    
    // 2. Delete all subscriptions associated with this shop
    const deletedSubscriptions = await Subscription.deleteMany({ shopId: shopId });
    logInfo(`Deleted ${deletedSubscriptions.deletedCount} subscriptions for shop ${shopId}`, 'ShopService');
    
    // 3. Delete all customers associated with this shop
    const deletedCustomers = await Customer.deleteMany({ shopId: shopId });
    logInfo(`Deleted ${deletedCustomers.deletedCount} customers for shop ${shopId}`, 'ShopService');
    
    // 4. Delete all debts associated with this shop
    const deletedDebts = await Debt.deleteMany({ shopId: shopId });
    logInfo(`Deleted ${deletedDebts.deletedCount} debts for shop ${shopId}`, 'ShopService');
    
    // 5. Delete all payments associated with this shop
    const deletedPayments = await Payment.deleteMany({ shopId: shopId });
    logInfo(`Deleted ${deletedPayments.deletedCount} payments for shop ${shopId}`, 'ShopService');
    
    // 6. Delete all notifications associated with this shop
    const deletedNotifications = await Notification.deleteMany({ shopId: shopId });
    logInfo(`Deleted ${deletedNotifications.deletedCount} notifications for shop ${shopId}`, 'ShopService');
    
    // 7. Delete all logs associated with this shop
    const deletedLogs = await Log.deleteMany({ shopId: shopId });
    logInfo(`Deleted ${deletedLogs.deletedCount} logs for shop ${shopId}`, 'ShopService');
    
    // 8. Delete all financial snapshots associated with this shop
    const deletedFinancialSnapshots = await FinancialSnapshot.deleteMany({ shopId: shopId });
    logInfo(`Deleted ${deletedFinancialSnapshots.deletedCount} financial snapshots for shop ${shopId}`, 'ShopService');
    
    // 9. Delete all files associated with this shop
    const deletedFiles = await File.deleteMany({ shopId: shopId });
    logInfo(`Deleted ${deletedFiles.deletedCount} files for shop ${shopId}`, 'ShopService');
    
    // 10. Delete all discount codes associated with this shop
    const deletedDiscountCodes = await DiscountCode.deleteMany({ shopId: shopId });
    logInfo(`Deleted ${deletedDiscountCodes.deletedCount} discount codes for shop ${shopId}`, 'ShopService');
    
    // 11. Delete all reports associated with this shop
    const deletedReports = await Report.deleteMany({ shopId: shopId });
    logInfo(`Deleted ${deletedReports.deletedCount} reports for shop ${shopId}`, 'ShopService');
    
    // 12. Delete all sessions associated with this shop
    const deletedSessions = await Session.deleteMany({ shopId: shopId });
    logInfo(`Deleted ${deletedSessions.deletedCount} sessions for shop ${shopId}`, 'ShopService');
    
    // 13. Delete all settings associated with this shop
    const deletedSettings = await Setting.deleteMany({ shopId: shopId });
    logInfo(`Deleted ${deletedSettings.deletedCount} settings for shop ${shopId}`, 'ShopService');
    
    // 14. Delete all shop settings associated with this shop
    const deletedShopSettings = await ShopSetting.deleteMany({ shopId: shopId });
    logInfo(`Deleted ${deletedShopSettings.deletedCount} shop settings for shop ${shopId}`, 'ShopService');
    
    // 15. Delete the shop itself (hard delete)
    await Shop.deleteOne({ shopId });
    
    deletionResult = {
      success: true,
      shopId,
      deletedAt: new Date(),
      type: 'hard_delete',
      cascadeStats: {
        usersDeleted: deletedUsers.deletedCount,
        subscriptionsDeleted: deletedSubscriptions.deletedCount,
        customersDeleted: deletedCustomers.deletedCount,
        debtsDeleted: deletedDebts.deletedCount,
        paymentsDeleted: deletedPayments.deletedCount,
        notificationsDeleted: deletedNotifications.deletedCount,
        logsDeleted: deletedLogs.deletedCount,
        financialSnapshotsDeleted: deletedFinancialSnapshots.deletedCount,
        filesDeleted: deletedFiles.deletedCount,
        discountCodesDeleted: deletedDiscountCodes.deletedCount,
        reportsDeleted: deletedReports.deletedCount,
        sessionsDeleted: deletedSessions.deletedCount,
        settingsDeleted: deletedSettings.deletedCount,
        shopSettingsDeleted: deletedShopSettings.deletedCount
      }
    };

    logSuccess(`Hard delete with cascade completed for shop ${shopId}`, 'ShopService');

    // Log shop deletion
    await LogHelper.createShopLog(
      'shop_hard_deleted',
      shopId,
      {
        actorId,
        actorRole
      },
      {
        reason,
        shopName: shop.shopName,
        deletedAt: new Date(),
        deletionType: 'hard',
        cascadeStats: deletionResult.cascadeStats
      }
    );
    
    return deletionResult;
  } catch (error) {
    if (error instanceof AppError) {
      throw error;
    }
    
    logError(`Error deleting shop ${shopId}: ${error.message}`, 'ShopService', error);
    throw new AppError('Failed to delete shop', 500, 'shop_deletion_error');
  }
};

module.exports = deleteShop;