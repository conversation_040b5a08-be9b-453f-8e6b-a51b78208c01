/**
 * Change Plan Controller
 * Handles changing a subscription's plan
 */
const { SubscriptionService } = require('../../services');
const { successResponse, AppError } = require('../../utils');

/**
 * Change subscription plan
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
const changePlan = async (req, res, next) => {
  try {
    const { planId, planType } = req.body;
    const { shopId } = req.user;
    
    // Validate inputs
    if (!shopId) {
      throw new AppError('No shop associated with this user', 400, 'shop_not_found');
    }
    
    if (!planId && !planType) {
      throw new AppError('Either Plan ID or Plan Type is required', 400, 'validation_error');
    }
    
    // Get current subscription for the shop
    const currentSubscription = await SubscriptionService.getCurrentSubscriptionByShop(shopId);
    
    if (!currentSubscription) {
      throw new AppError('Subscription not found', 404, 'subscription_not_found');
    }
    
    // For security tracking, we pass actor information to service
    const options = {
      actorId: req.user?.userId || 'system',
      actorRole: req.user?.role || 'system'
    };
    
    // Change plan through service
    const updatedSubscription = await SubscriptionService.changePlan(
      currentSubscription.subscriptionId, 
      { planId, planType }, 
      options
    );
    
    // Return successful response
    return successResponse(res, {
      message: 'Subscription plan changed successfully',
      data: updatedSubscription,
    });
  } catch (error) {
    next(error);
  }
};

module.exports = changePlan;
