# DeynCare Frontend: SuperAdmin Subscriptions Dashboard Structure

## Overview
This document outlines the frontend structure for the SuperAdmin Subscriptions dashboard, following a modern, component-based architecture with advanced features.

## Directory Structure
```
src/
├── pages/
│   └── admin/
│       └── subscriptions/
│           ├── index.tsx                 # Main subscriptions page
│           ├── SubscriptionDetails.tsx   # Individual subscription view
│           └── SubscriptionAnalytics.tsx # Analytics view
├── components/
│   └── subscriptions/
│       ├── common/
│       │   ├── SubscriptionCard.tsx      # Reusable subscription card
│       │   ├── StatusBadge.tsx          # Status indicator component
│       │   ├── PlanBadge.tsx            # Plan type indicator
│       │   └── PaymentMethodBadge.tsx   # Payment method indicator
│       ├── dashboard/
│       │   ├── SubscriptionStats.tsx     # Statistics overview
│       │   ├── RevenueChart.tsx         # Revenue visualization
│       │   ├── SubscriptionTrends.tsx   # Subscription trends
│       │   └── PlanDistribution.tsx     # Plan type distribution
│       ├── table/
│       │   ├── SubscriptionTable.tsx    # Main data table
│       │   ├── TableFilters.tsx         # Advanced filtering
│       │   ├── TablePagination.tsx      # Pagination controls
│       │   └── BulkActions.tsx          # Bulk operations
│       ├── forms/
│       │   ├── ExtendSubscription.tsx   # Extension form
│       │   ├── VerifyPayment.tsx        # Payment verification
│       │   └── BulkUpdateForm.tsx       # Bulk update form
│       └── export/
│           ├── ExportOptions.tsx        # Export configuration
│           ├── ExportProgress.tsx       # Export progress
│           └── ExportHistory.tsx        # Export history
├── hooks/
│   └── subscriptions/
│       ├── useSubscriptions.ts          # Main data fetching
│       ├── useSubscriptionStats.ts      # Stats fetching
│       ├── useBulkOperations.ts         # Bulk operations
│       └── useExport.ts                 # Export functionality
├── services/
│   └── subscriptions/
│       ├── api.ts                       # API integration
│       ├── export.ts                    # Export service
│       └── analytics.ts                 # Analytics service
└── utils/
    └── subscriptions/
        ├── formatters.ts                # Data formatting
        ├── validators.ts                # Form validation
        └── constants.ts                 # Constants and enums
```

## Component Details

### 1. Pages

#### `index.tsx` (Main Subscriptions Page)
- Dashboard layout with statistics
- Main subscription table
- Quick action buttons
- Export controls
- Filter and search functionality

#### `SubscriptionDetails.tsx`
- Detailed subscription information
- Payment history
- Usage statistics
- Action buttons (extend, cancel, etc.)
- Related shop information

#### `SubscriptionAnalytics.tsx`
- Revenue analytics
- Subscription trends
- Plan distribution
- Custom date range selection
- Export analytics data

### 2. Components

#### Common Components
- **SubscriptionCard**: Reusable card for displaying subscription info
- **StatusBadge**: Visual status indicators
- **PlanBadge**: Plan type visualization
- **PaymentMethodBadge**: Payment method indicators

#### Dashboard Components
- **SubscriptionStats**: Key metrics and statistics
- **RevenueChart**: Revenue visualization
- **SubscriptionTrends**: Growth and trends
- **PlanDistribution**: Plan type distribution

#### Table Components
- **SubscriptionTable**: Main data table with features:
  - Sorting
  - Filtering
  - Pagination
  - Row selection
  - Quick actions
- **TableFilters**: Advanced filtering options
- **TablePagination**: Pagination controls
- **BulkActions**: Bulk operation controls

#### Form Components
- **ExtendSubscription**: Subscription extension form
- **VerifyPayment**: Payment verification form
- **BulkUpdateForm**: Bulk update operations

#### Export Components
- **ExportOptions**: Export configuration
- **ExportProgress**: Export progress tracking
- **ExportHistory**: Export history and management

### 3. Hooks

#### `useSubscriptions`
```typescript
interface UseSubscriptionsReturn {
  subscriptions: Subscription[];
  loading: boolean;
  error: Error | null;
  refetch: () => Promise<void>;
  filters: SubscriptionFilters;
  setFilters: (filters: SubscriptionFilters) => void;
  pagination: PaginationState;
  setPagination: (pagination: PaginationState) => void;
}
```

#### `useSubscriptionStats`
```typescript
interface UseSubscriptionStatsReturn {
  stats: SubscriptionStats;
  loading: boolean;
  error: Error | null;
  refetch: () => Promise<void>;
  dateRange: DateRange;
  setDateRange: (range: DateRange) => void;
}
```

#### `useBulkOperations`
```typescript
interface UseBulkOperationsReturn {
  selectedIds: string[];
  setSelectedIds: (ids: string[]) => void;
  performBulkAction: (action: BulkAction) => Promise<void>;
  loading: boolean;
  error: Error | null;
}
```

#### `useExport`
```typescript
interface UseExportReturn {
  exportData: (options: ExportOptions) => Promise<void>;
  exportProgress: number;
  exportStatus: ExportStatus;
  cancelExport: () => void;
}
```

### 4. Services

#### `api.ts`
- API integration functions
- Error handling
- Request/response interceptors
- Authentication handling

#### `export.ts`
- Export functionality
- File format handling
- Progress tracking
- Error handling

#### `analytics.ts`
- Analytics data fetching
- Data transformation
- Caching
- Real-time updates

### 5. Utils

#### `formatters.ts`
- Date formatting
- Currency formatting
- Status formatting
- Number formatting

#### `validators.ts`
- Form validation rules
- Custom validators
- Error messages

#### `constants.ts`
- Status enums
- Plan types
- Payment methods
- Action types

## State Management
- Global state for user preferences
- Local state for component-specific data
- Caching for performance optimization
- Real-time updates for critical data

## Styling
- Component-based styling
- Responsive design
- Theme support
- Dark/light mode
- Custom animations

## Performance Considerations
- Lazy loading for heavy components
- Virtual scrolling for large lists
- Data caching
- Optimistic updates
- Debounced search/filter

## Security
- Role-based access control
- Input sanitization
- XSS prevention
- CSRF protection
- Secure file downloads

## Error Handling
- Global error boundary
- Component-level error handling
- User-friendly error messages
- Retry mechanisms
- Fallback UI

## Testing
- Unit tests for components
- Integration tests for features
- E2E tests for critical paths
- Performance testing
- Accessibility testing

---

*Generated by Cascade AI — DeynCare Frontend Documentation (SuperAdmin Subscriptions)* 