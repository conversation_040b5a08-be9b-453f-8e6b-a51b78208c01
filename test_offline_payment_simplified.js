/**
 * Test script to verify simplified offline payment implementation
 * This script tests the updated backend system with only "offline" payment method
 */

const mongoose = require('mongoose');
const { registerSchemas } = require('./src/validations/schemas/registerSchemas');
const { paymentSchemas } = require('./src/validations/schemas/paymentSchemas');

// Test data for offline payment
const testOfflinePaymentData = {
  planType: 'monthly',
  paymentMethod: 'offline',
  payerName: '<PERSON>',
  payerPhone: '+************',
  notes: 'Offline payment to DeynCare account'
};

// Test data with invalid payment methods (should fail)
const testInvalidPaymentMethods = [
  'Cash',
  'Bank Transfer', 
  'Check',
  'Other'
];

console.log('🧪 Testing Simplified Offline Payment Implementation\n');

// Test 1: Validate correct offline payment data
console.log('Test 1: Validating correct offline payment data...');
try {
  const { error, value } = registerSchemas.processPayment.validate(testOfflinePaymentData);
  if (error) {
    console.log('❌ FAILED: Validation error for valid offline payment data');
    console.log('Error:', error.details);
  } else {
    console.log('✅ PASSED: Valid offline payment data accepted');
    console.log('Validated data:', value);
  }
} catch (err) {
  console.log('❌ ERROR:', err.message);
}

console.log('\n' + '='.repeat(50) + '\n');

// Test 2: Test invalid payment methods (should fail)
console.log('Test 2: Testing invalid payment methods (should fail)...');
testInvalidPaymentMethods.forEach((method, index) => {
  console.log(`\nTest 2.${index + 1}: Testing payment method "${method}"`);
  
  const testData = {
    ...testOfflinePaymentData,
    paymentMethod: method
  };
  
  try {
    const { error } = registerSchemas.processPayment.validate(testData);
    if (error) {
      console.log(`✅ PASSED: Payment method "${method}" correctly rejected`);
      console.log('Error message:', error.details[0].message);
    } else {
      console.log(`❌ FAILED: Payment method "${method}" should have been rejected`);
    }
  } catch (err) {
    console.log('❌ ERROR:', err.message);
  }
});

console.log('\n' + '='.repeat(50) + '\n');

// Test 3: Test payment schema validation
console.log('Test 3: Testing payment schema validation...');

const testPaymentData = {
  shopId: 'SHP_123456',
  customerId: 'USR_789012',
  customerName: 'Ahmed Hassan',
  paymentContext: 'subscription',
  subscriptionId: 'SUB_345678',
  amount: 50.00,
  method: 'offline',
  referenceNumber: 'REF_001',
  notes: 'Test offline payment'
};

try {
  const { error, value } = paymentSchemas.recordPayment.validate(testPaymentData);
  if (error) {
    console.log('❌ FAILED: Payment schema validation error');
    console.log('Error:', error.details);
  } else {
    console.log('✅ PASSED: Payment schema validation successful');
    console.log('Validated payment data:', value);
  }
} catch (err) {
  console.log('❌ ERROR:', err.message);
}

console.log('\n' + '='.repeat(50) + '\n');

// Test 4: Test invalid payment methods in payment schema
console.log('Test 4: Testing invalid payment methods in payment schema...');

const invalidPaymentMethods = ['cash', 'bank_transfer', 'check', 'other'];
invalidPaymentMethods.forEach((method, index) => {
  console.log(`\nTest 4.${index + 1}: Testing payment method "${method}"`);
  
  const testData = {
    ...testPaymentData,
    method: method
  };
  
  try {
    const { error } = paymentSchemas.recordPayment.validate(testData);
    if (error) {
      console.log(`✅ PASSED: Payment method "${method}" correctly rejected in payment schema`);
      console.log('Error message:', error.details[0].message);
    } else {
      console.log(`❌ FAILED: Payment method "${method}" should have been rejected in payment schema`);
    }
  } catch (err) {
    console.log('❌ ERROR:', err.message);
  }
});

console.log('\n' + '='.repeat(50) + '\n');

// Test 5: Test valid payment methods
console.log('Test 5: Testing valid payment methods...');

const validPaymentMethods = ['offline', 'mobile_money', 'card'];
validPaymentMethods.forEach((method, index) => {
  console.log(`\nTest 5.${index + 1}: Testing payment method "${method}"`);
  
  const testData = {
    ...testPaymentData,
    method: method
  };
  
  try {
    const { error } = paymentSchemas.recordPayment.validate(testData);
    if (error) {
      console.log(`❌ FAILED: Valid payment method "${method}" was rejected`);
      console.log('Error:', error.details);
    } else {
      console.log(`✅ PASSED: Payment method "${method}" correctly accepted`);
    }
  } catch (err) {
    console.log('❌ ERROR:', err.message);
  }
});

console.log('\n' + '='.repeat(50) + '\n');

// Summary
console.log('🎯 Test Summary:');
console.log('- Offline payment method validation: Updated to accept only "offline"');
console.log('- Removed payment methods: Cash, Bank Transfer, Check, Other');
console.log('- Required fields: planType, payerName, payerPhone, notes, paymentProof');
console.log('- Excluded fields: bankDetails, transferReference');
console.log('- Payment schema: Updated to use "offline" instead of "cash", "bank_transfer", etc.');
console.log('\n✅ Backend system successfully simplified for offline payments only!');
