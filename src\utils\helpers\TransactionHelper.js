const mongoose = require('mongoose');
const { logInfo, logError, logWarning } = require('../logger');

const MAX_RETRIES = 5; // Maximum number of retries for transient errors
const RETRY_DELAY_MS = 100; // Initial delay before retrying (will be exponential)

/**
 * Helper utility for managing MongoDB transactions across different layers of the application
 */
const TransactionHelper = {
  /**
   * Start a new MongoDB transaction
   * @returns {Promise<Object>} The transaction session
   */
  startTransaction: async () => {
    const session = await mongoose.startSession();
    session.startTransaction();
    logInfo('Started new database transaction', 'TransactionHelper');
    return session;
  },

  /**
   * Commit a transaction
   * @param {Object} session - The transaction session
   * @returns {Promise<void>}
   */
  commitTransaction: async (session) => {
    if (!session) {
      logWarning('No transaction session provided to commit', 'TransactionHelper');
      return;
    }
    
    try {
      await session.commitTransaction();
      logInfo('Transaction committed successfully', 'TransactionHelper');
    } catch (error) {
      logError(`Error committing transaction: ${error.message}`, 'TransactionHelper', error);
      throw error;
    } finally {
      session.endSession();
    }
  },

  /**
   * Abort a transaction
   * @param {Object} session - The transaction session
   * @returns {Promise<void>}
   */
  abortTransaction: async (session) => {
    if (!session) {
      logWarning('No transaction session provided to abort', 'TransactionHelper');
      return;
    }
    
    try {
      await session.abortTransaction();
      logInfo('Transaction aborted', 'TransactionHelper');
    } catch (error) {
      logError(`Error aborting transaction: ${error.message}`, 'TransactionHelper', error);
      // Don't rethrow this error as we're already in an error handling path
    } finally {
      session.endSession();
    }
  },

  /**
   * Execute a function within a transaction with retry logic for transient errors
   * Automatically commits on success or aborts on error
   * @param {Function} fn - Async function to execute within the transaction
   * @param {Object} options - Options for the transaction
   * @param {string} options.name - Name of the transaction operation for logging
   * @param {number} options.retries - Current retry attempt (internal)
   * @returns {Promise<any>} The result of the function
   */
  withTransaction: async (fn, options = {}) => {
    const { name = 'Unnamed Transaction', retries = 0 } = options;

    if (retries >= MAX_RETRIES) {
      logError(`Max retries (${MAX_RETRIES}) reached for transaction operation '${name}'`, 'TransactionHelper');
      throw new mongoose.Error('Max retries reached for transaction');
    }

    const session = await TransactionHelper.startTransaction();
    
    logInfo(`Starting transaction operation: ${name} (Attempt ${retries + 1}/${MAX_RETRIES})`, 'TransactionHelper');
    
    try {
      const result = await fn(session);
      await TransactionHelper.commitTransaction(session);
      return result;
    } catch (error) {
      await TransactionHelper.abortTransaction(session);

      // Check for transient transaction errors (e.g., WriteConflict)
      // Error code 112 is WriteConflict
      if (error.code === 112 || (error.errorLabels && typeof error.errorLabels.has === 'function' && error.errorLabels.has('TransientTransactionError'))) {
        logWarning(`Transient transaction error in '${name}' (Attempt ${retries + 1}/${MAX_RETRIES}). Retrying...`, 'TransactionHelper', error);
        // Exponential backoff delay
        const delay = RETRY_DELAY_MS * Math.pow(2, retries);
        await new Promise(resolve => setTimeout(resolve, delay));
        // Retry the transaction
        return TransactionHelper.withTransaction(fn, { name, retries: retries + 1 });
      } else {
        // For non-transient errors, re-throw immediately
        logError(`Error in transaction operation '${name}': ${error.message}`, 'TransactionHelper', error);
        throw error;
      }
    }
  }
};

module.exports = TransactionHelper;
