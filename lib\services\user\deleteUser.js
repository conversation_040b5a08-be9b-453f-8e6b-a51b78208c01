import apiBridge from '@/lib/api/bridge';
import { ENDPOINTS } from '@/lib/api/contract';
import { toast } from 'sonner';
import BaseService from '@/lib/services/baseService';

/**
 * Delete a user
 * @param {string} userId - ID of the user to delete
 * @param {string} reason - Reason for deletion (required by backend)
 * @returns {Promise<boolean>} Success status
 */
async function deleteUser(userId, reason) {
  try {
    // Ensure reason is provided as it's required by the backend
    if (!reason || reason.trim().length < 5) {
      toast.error('Please provide a valid reason for deletion (minimum 5 characters)');
      return false;
    }
    
    // Make API request using the bridge
    const response = await apiBridge.delete(`${ENDPOINTS.USERS.BASE}/${userId}`, { reason }, {
      clearCacheEndpoint: ENDPOINTS.USERS.BASE
    });
    
    // Process response
    if (response.data && response.data.success) {
      toast.success('User deleted successfully');
      return true;
    }
    
    // Handle unexpected response
    console.error('Unexpected API response format:', response.data);
    toast.error('Failed to delete user: Unexpected response format');
    return false;
  } catch (error) {
    console.error(`Error deleting user ${userId}:`, error);
    // Use standardized error handling
    BaseService.handleError(error, 'UserService.deleteUser', true);
    return false;
  }
}

export default deleteUser;
