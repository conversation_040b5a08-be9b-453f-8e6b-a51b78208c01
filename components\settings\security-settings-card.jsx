"use client";

import { useState, useEffect, useCallback, useMemo } from "react";
import { <PERSON>, Card<PERSON><PERSON><PERSON>, Card<PERSON>ontent, CardTitle, CardDescription } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Separator } from "@/components/ui/separator";
import { Shield, LockKeyhole, RefreshCcw, AlertTriangle } from "lucide-react";

/**
 * Security Settings Card Component
 * Displays and manages global security settings
 */
export function SecuritySettingsCard({ 
  initialSettings, 
  onSave, 
  loading 
}) {
  // Define default settings with useMemo to avoid recreating on each render
  const defaultSettings = useMemo(() => ({
    passwordPolicy: {
      minLength: 8,
      requireUppercase: true,
      requireLowercase: true,
      requireNumbers: true,
      requireSpecialChars: true,
      passwordExpiryDays: 90
    },
    sessionSettings: {
      sessionTimeout: 30, // minutes
      maxLoginAttempts: 5,
      lockoutDuration: 15 // minutes
    },
    securityHeaders: {
      enableCSP: true,
      enableHSTS: true,
      enableXFrameOptions: true,
      enableXSSProtection: true
    }
  }), []);
  
  // Initialize with default settings
  const [settings, setSettings] = useState(defaultSettings);

  // Update local state when props change - only on initial mount or if initialSettings changes
  useEffect(() => {
    if (initialSettings && Object.keys(initialSettings).length > 0) {
      // Use functional update to avoid stale closures
      setSettings(() => ({
        // First apply defaults
        ...defaultSettings,
        // Then override with initialSettings
        ...initialSettings
      }));
    }
  }, []); // Empty dependency array to run only on mount

  // Handle toggle changes - memoized to prevent unnecessary recreations
  const handleToggleChange = useCallback((field, section = null) => {
    if (section) {
      setSettings(prev => ({
        ...prev,
        [section]: {
          ...prev[section],
          [field]: !prev[section][field]
        }
      }));
    } else {
      setSettings(prev => ({
        ...prev,
        [field]: !prev[field]
      }));
    }
  }, []);

  // Handle input changes - memoized to prevent unnecessary recreations
  const handleInputChange = useCallback((e, section = null) => {
    const { name, value } = e.target;
    const numericValue = e.target.type === 'number' ? parseInt(value, 10) || 0 : value;
    
    if (section) {
      setSettings(prev => ({
        ...prev,
        [section]: {
          ...prev[section],
          [name]: numericValue
        }
      }));
    } else {
      setSettings(prev => ({
        ...prev,
        [name]: numericValue
      }));
    }
  }, []);
  
  // Handle select changes - memoized to prevent unnecessary recreations
  const handleSelectChange = useCallback((value, field, section = null) => {
    const numericValue = parseInt(value, 10) || 0;
    
    if (section) {
      setSettings(prev => ({
        ...prev,
        [section]: {
          ...prev[section],
          [field]: numericValue
        }
      }));
    } else {
      setSettings(prev => ({
        ...prev,
        [field]: numericValue
      }));
    }
  }, []);

  // Save settings - memoized to prevent unnecessary recreations
  const handleSave = useCallback(() => {
    onSave(settings);
  }, [onSave, settings]);

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <Shield className="mr-2 h-5 w-5" />
          Security Settings
        </CardTitle>
        <CardDescription>
          Configure global security policies and settings
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Authentication Settings */}
        <div className="space-y-4">
          <h3 className="text-lg font-medium">Authentication Settings</h3>
          
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor="sessionTimeout">Session Timeout</Label>
              <p className="text-sm text-muted-foreground">
                Automatically log out inactive users after specified minutes
              </p>
            </div>
            <div className="w-24">
              <Select 
                value={settings.sessionSettings.sessionTimeout.toString()} 
                onValueChange={(value) => handleSelectChange(value, 'sessionTimeout', 'sessionSettings')}
              >
                <SelectTrigger id="sessionTimeout">
                  <SelectValue placeholder="Select" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="15">15 min</SelectItem>
                  <SelectItem value="30">30 min</SelectItem>
                  <SelectItem value="60">60 min</SelectItem>
                  <SelectItem value="120">120 min</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>
        
        <Separator />
        
        {/* Password Policy */}
        <div className="space-y-4">
          <h3 className="text-lg font-medium">Password Policy</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="minLength">Minimum Password Length</Label>
              <Input
                id="minLength"
                name="minLength"
                type="number"
                min={6}
                max={32}
                value={settings.passwordPolicy.minLength}
                onChange={(e) => handleInputChange(e, 'passwordPolicy')}
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="passwordExpiryDays">Password Expiry (Days)</Label>
              <Input
                id="passwordExpiryDays"
                name="passwordExpiryDays"
                type="number"
                min={0}
                max={365}
                value={settings.passwordPolicy.passwordExpiryDays}
                onChange={(e) => handleInputChange(e, 'passwordPolicy')}
              />
              <p className="text-xs text-muted-foreground">
                Set to 0 for no expiry
              </p>
            </div>
          </div>
          
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <div className="flex items-center justify-between">
              <Label htmlFor="requireUppercase">Require Uppercase</Label>
              <Switch
                id="requireUppercase"
                checked={settings.passwordPolicy.requireUppercase}
                onCheckedChange={() => handleToggleChange('requireUppercase', 'passwordPolicy')}
              />
            </div>
            
            <div className="flex items-center justify-between">
              <Label htmlFor="requireLowercase">Require Lowercase</Label>
              <Switch
                id="requireLowercase"
                checked={settings.passwordPolicy.requireLowercase}
                onCheckedChange={() => handleToggleChange('requireLowercase', 'passwordPolicy')}
              />
            </div>
            
            <div className="flex items-center justify-between">
              <Label htmlFor="requireNumbers">Require Numbers</Label>
              <Switch
                id="requireNumbers"
                checked={settings.passwordPolicy.requireNumbers}
                onCheckedChange={() => handleToggleChange('requireNumbers', 'passwordPolicy')}
              />
            </div>
            
            <div className="flex items-center justify-between">
              <Label htmlFor="requireSpecialChars">Require Special Characters</Label>
              <Switch
                id="requireSpecialChars"
                checked={settings.passwordPolicy.requireSpecialChars}
                onCheckedChange={() => handleToggleChange('requireSpecialChars', 'passwordPolicy')}
              />
            </div>
          </div>
        </div>
        
        <Separator />
        
        {/* Login Protection */}
        <div className="space-y-4">
          <h3 className="text-lg font-medium">Login Protection</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="maxLoginAttempts">Max Failed Login Attempts</Label>
              <Input
                id="maxLoginAttempts"
                name="maxLoginAttempts"
                type="number"
                min={1}
                max={10}
                value={settings.sessionSettings.maxLoginAttempts}
                onChange={(e) => handleInputChange(e, 'sessionSettings')}
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="lockoutDuration">Account Lockout Duration (Minutes)</Label>
              <Input
                id="lockoutDuration"
                name="lockoutDuration"
                type="number"
                min={5}
                max={1440}
                value={settings.sessionSettings.lockoutDuration}
                onChange={(e) => handleInputChange(e, 'sessionSettings')}
              />
            </div>
          </div>
        </div>
        
        <Separator />
        
        {/* Security Headers */}
        <div className="space-y-4">
          <h3 className="text-lg font-medium">Web Security</h3>
          
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="enableCSP">Content Security Policy</Label>
                <p className="text-xs text-muted-foreground">Protects against XSS attacks</p>
              </div>
              <Switch
                id="enableCSP"
                checked={settings.securityHeaders.enableCSP}
                onCheckedChange={() => handleToggleChange('enableCSP', 'securityHeaders')}
              />
            </div>
            
            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="enableHSTS">HTTP Strict Transport Security</Label>
                <p className="text-xs text-muted-foreground">Enforces HTTPS connections</p>
              </div>
              <Switch
                id="enableHSTS"
                checked={settings.securityHeaders.enableHSTS}
                onCheckedChange={() => handleToggleChange('enableHSTS', 'securityHeaders')}
              />
            </div>
            
            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="enableXFrameOptions">X-Frame-Options</Label>
                <p className="text-xs text-muted-foreground">Prevents clickjacking</p>
              </div>
              <Switch
                id="enableXFrameOptions"
                checked={settings.securityHeaders.enableXFrameOptions}
                onCheckedChange={() => handleToggleChange('enableXFrameOptions', 'securityHeaders')}
              />
            </div>
            
            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="enableXSSProtection">X-XSS-Protection</Label>
                <p className="text-xs text-muted-foreground">Additional XSS protection</p>
              </div>
              <Switch
                id="enableXSSProtection"
                checked={settings.securityHeaders.enableXSSProtection}
                onCheckedChange={() => handleToggleChange('enableXSSProtection', 'securityHeaders')}
              />
            </div>
          </div>
        </div>
        
        <div className="p-4 bg-yellow-50 rounded-md flex items-start space-x-3 text-yellow-800">
          <AlertTriangle className="h-5 w-5 mt-0.5 text-yellow-500" />
          <div>
            <p className="font-medium">Security Settings Warning</p>
            <p className="text-sm">
              Changing these settings affects the entire application's security posture.
              Make sure you understand the implications before making changes.
            </p>
          </div>
        </div>
        
        <Button 
          onClick={handleSave}
          disabled={loading}
          className="w-full"
        >
          {loading ? "Saving..." : "Save Security Settings"}
        </Button>
      </CardContent>
    </Card>
  );
}
