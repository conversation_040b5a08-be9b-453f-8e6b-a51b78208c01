# Enhanced Payment System - Single Endpoint

## 🎯 Offline Payment Test

**Endpoint**: `POST http://localhost:5000/api/register/pay`

### Required Headers:
```
Authorization: Bearer YOUR_JWT_TOKEN
Content-Type: multipart/form-data
```

### Test Payload (Multipart Form Data):
```
planType: monthly
paymentMethod: Cash
paymentProof: [FILE] (JPG/PNG/PDF, max 5MB)
```

### Expected Response:
```json
{
  "success": true,
  "message": "Payment skipped. Complete payment later.",
  "data": {
    "nextStep": "registration_complete_offline_payment_pending"
  }
}
```

## ✅ Status Fixed
- ✅ Fixed subscription validation error for offline payments
- ✅ Now uses valid 'trial' status for offline payments until verified
- ✅ No more `'pending_payment'` enum validation errors

**Ready for testing!** 🚀 