const { Shop } = require('../../models');
const { 
  App<PERSON><PERSON>r, 
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  logInfo, 
  logError 
} = require('../../utils');

/**
 * Get all shops with optional filtering and pagination
 * @param {Object} filters - Filter criteria
 * @param {string} filters.status - Filter by status
 * @param {boolean} filters.isActive - Filter by active status
 * @param {string} filters.registrationStatus - Filter by registration status
 * @param {string} filters.paymentStatus - Filter by payment status
 * @param {string} filters.businessType - Filter by business type
 * @param {string} filters.city - Filter by city
 * @param {string} filters.region - Filter by region
 * @param {string} filters.country - Filter by country
 * @param {Object} filters.createdAt - Date range filter
 * @param {number} page - Page number (default 1)
 * @param {number} limit - Items per page (default 10, set to 0 for no limit)
 * @returns {Promise<Object>} Shops with pagination
 */
const getAllShops = async (filters = {}, page = 1, limit = 10) => {
  try {
    // Build MongoDB filter
    const mongoFilter = { isDeleted: false };
    
    // Apply filters
    if (filters.status) mongoFilter.status = filters.status;
    if (filters.isActive !== undefined) mongoFilter.isActive = filters.isActive;
    if (filters.registrationStatus) mongoFilter.registrationStatus = filters.registrationStatus;
    if (filters.paymentStatus) mongoFilter.paymentStatus = filters.paymentStatus;
    if (filters.businessType) mongoFilter.businessType = filters.businessType;
    if (filters.city) mongoFilter['address.city'] = filters.city;
    if (filters.region) mongoFilter['address.region'] = filters.region;
    if (filters.country) mongoFilter['address.country'] = filters.country;
    
    // Handle date range filter
    if (filters.createdAt) {
      mongoFilter.createdAt = filters.createdAt;
    }
    
    // Prepare pagination options
    const paginationOptions = {
      page: parseInt(page) || 1,
      limit: parseInt(limit) || 10,
      sort: { createdAt: -1 },
      select: '-sensitiveField' // Exclude any sensitive fields if needed
    };
    
    // If limit is 0, get all records (for export)
    if (paginationOptions.limit === 0) {
      const shops = await Shop.find(mongoFilter)
        .select(paginationOptions.select)
        .sort(paginationOptions.sort)
        .lean();
      
      logInfo(`Retrieved ${shops.length} shops (no pagination)`, 'ShopService');
      
      return {
        docs: shops,
        totalDocs: shops.length,
        limit: 0,
        totalPages: 1,
        page: 1,
        pagingCounter: 1,
        hasPrevPage: false,
        hasNextPage: false,
        prevPage: null,
        nextPage: null
      };
    }
    
    // Use PaginationHelper for consistent pagination
    const result = await PaginationHelper.paginate(Shop, mongoFilter, paginationOptions);
    
    // Transform to match expected format
    const response = {
      docs: result.items,
      totalDocs: result.pagination.total,
      limit: result.pagination.limit,
      totalPages: result.pagination.totalPages,
      page: result.pagination.page,
      pagingCounter: (result.pagination.page - 1) * result.pagination.limit + 1,
      hasPrevPage: result.pagination.page > 1,
      hasNextPage: result.pagination.page < result.pagination.totalPages,
      prevPage: result.pagination.page > 1 ? result.pagination.page - 1 : null,
      nextPage: result.pagination.page < result.pagination.totalPages ? result.pagination.page + 1 : null
    };
    
    logInfo(`Retrieved ${response.docs.length} shops (page ${response.page}/${response.totalPages})`, 'ShopService');
    
    return response;
  } catch (error) {
    if (error instanceof AppError) {
      throw error;
    }
    
    logError(`Error getting all shops: ${error.message}`, 'ShopService', error);
    throw new AppError('Failed to get shops', 500, 'shop_get_error');
  }
};

module.exports = getAllShops; 