/**
 * Validate Discount Module
 * Handles discount validation and calculation
 */
const { AppError, logError } = require('../../utils');

// Import getDiscount to avoid circular dependencies
const { getDiscountByCode } = require('./getDiscount');

/**
 * Validate and calculate discount for a purchase
 * @param {String} code - Discount code
 * @param {Number} amount - Purchase amount
 * @param {String} context - Context (subscription, debt)
 * @param {String} userId - User ID
 * @param {String} shopId - Shop ID
 * @returns {Promise<Object>} Discount details and amount
 */
const validateAndCalculateDiscount = async (code, amount, context = 'subscription', userId, shopId = null) => {
  try {
    // Get the discount code
    const discountCode = await getDiscountByCode(code);
    
    // Check if valid
    if (!discountCode.isValid()) {
      throw new AppError('Discount code is expired or inactive', 400, 'invalid_discount');
    }
    
    // Special handling for debt context - we want to be extra careful about this
    if (context === 'debt') {
      // Check if explicitly allowed for debt context
      if (!discountCode.applicableFor.includes('debt') && !discountCode.applicableFor.includes('all')) {
        throw new AppError('Discount codes cannot be applied to debt payments by default', 400, 'invalid_context_debt');
      }
    }
    
    // Check if applicable for this context
    if (!discountCode.applicableFor.includes('all') && !discountCode.applicableFor.includes(context)) {
      throw new AppError(`Discount code is not applicable for ${context}`, 400, 'invalid_context');
    }
    
    // Check if shop-specific discount matches
    if (discountCode.shopId && discountCode.shopId !== shopId) {
      throw new AppError('Discount code is not valid for this shop', 400, 'invalid_shop');
    }
    
    // Check if user has already used this code
    if (discountCode.perUserLimit > 0) {
      // This would ideally check against a usage tracking table
      // For now, we'll assume the limit is enforced elsewhere
    }
    
    // Calculate discount amount
    const discountAmount = discountCode.calculateDiscount(amount);
    
    // If minimum purchase not met
    if (discountAmount === 0 && amount > 0) {
      throw new AppError(
        `Minimum purchase amount of ${discountCode.minimumPurchase} not met`,
        400, 
        'minimum_purchase_not_met'
      );
    }
    
    // Format response
    return {
      discountId: discountCode.discountId,
      code: discountCode.code,
      type: discountCode.type,
      value: discountCode.value,
      discountAmount,
      finalAmount: amount - discountAmount,
      description: discountCode.description
    };
  } catch (error) {
    if (error instanceof AppError) {
      throw error;
    }
    
    logError(`Failed to validate discount code: ${error.message}`, 'DiscountService', error);
    throw new AppError('Failed to validate discount code', 500, 'discount_validation_error');
  }
};

module.exports = validateAndCalculateDiscount;
