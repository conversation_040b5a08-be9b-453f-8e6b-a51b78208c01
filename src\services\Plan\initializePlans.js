/**
 * Initialize plans
 * Creates default plans if none exist
 */
const { Plan } = require('../../models');
const { logInfo, logError, logSuccess } = require('../../utils');

/**
 * Initializes default plans if none exist
 * Ensures all features are enabled across all plan types
 */
const initializePlans = async () => {
  try {
    logInfo('Initializing plans...', 'PlanService');
    await Plan.createDefaultPlans();
    logSuccess('Plans initialized', 'PlanService');
  } catch (error) {
    logError('Failed to initialize plans', 'PlanService', error);
    throw error;
  }
};

module.exports = initializePlans;
