/**
 * Renew Subscription Controller
 * Handles renewal of subscriptions
 */
const { SubscriptionService } = require('../../services');
const { logError } = require('../../utils');

/**
 * Renew a subscription
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
const renewSubscription = async (req, res, next) => {
  try {
    const { subscriptionId, paymentMethod } = req.body;
    
    // Get subscription
    const subscription = await SubscriptionService.getSubscriptionById(subscriptionId);
    
    // Calculate new end date
    const currentEndDate = new Date(subscription.dates.endDate);
    const newEndDate = new Date(currentEndDate);
    
    if (subscription.pricing.billingCycle === 'monthly') {
      newEndDate.setMonth(newEndDate.getMonth() + 1);
    } else if (subscription.pricing.billingCycle === 'yearly') {
      newEndDate.setFullYear(newEndDate.getFullYear() + 1);
    }
    
    // Update subscription
    subscription.dates.endDate = newEndDate;
    subscription.payment.method = paymentMethod || subscription.payment.method;
    subscription.payment.nextPaymentDate = newEndDate;
    subscription.status = 'active';
    
    // Add to history
    subscription.history.push({
      action: 'renewed',
      date: new Date(),
      performedBy: req.user?.userId || 'system',
      details: {
        previousEndDate: currentEndDate,
        newEndDate,
        method: paymentMethod
      }
    });
    
    // Save changes
    await subscription.save();
    
    return res.status(200).json({
      success: true,
      message: 'Subscription renewed successfully',
      data: {
        subscriptionId,
        status: subscription.status,
        endDate: newEndDate
      }
    });
  } catch (error) {
    logError('Failed to renew subscription', 'SubscriptionController', error);
    return next(error);
  }
};

module.exports = renewSubscription;
