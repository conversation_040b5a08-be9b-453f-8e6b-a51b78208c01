const mongoose = require('mongoose');
require('dotenv').config();

// Import models
const { Shop, Subscription } = require('../models');
const createSubscription = require('../services/Subscription/createSubscription');

/**
 * Script to fix shops that don't have corresponding subscription records
 * This creates subscription records for shops that only have embedded subscription data
 */
const fixShopSubscriptions = async () => {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    
    console.log('Connected to MongoDB');
    
    // Find all active shops
    const shops = await Shop.find({ 
      isDeleted: { $ne: true },
      status: { $in: ['active', 'pending'] }
    }).lean();
    
    console.log(`Found ${shops.length} shops to check`);
    
    let fixedCount = 0;
    let skippedCount = 0;
    let errorCount = 0;
    
    for (const shop of shops) {
      try {
        console.log(`\nProcessing shop: ${shop.shopId} - ${shop.shopName}`);
        
        // Check if subscription record already exists
        const existingSubscription = await Subscription.findOne({ shopId: shop.shopId });
        
        if (existingSubscription) {
          console.log(`  ✓ Subscription already exists: ${existingSubscription.subscriptionId}`);
          skippedCount++;
          continue;
        }
        
        // Extract subscription data from shop
        const shopSubscription = shop.subscription || {};
        const planType = shopSubscription.plan?.type || shopSubscription.planType || 'trial';
        const planId = shopSubscription.planId || null;
        
        console.log(`  → Creating subscription with plan type: ${planType}`);
        
        // Prepare subscription data
        const subscriptionData = {
          shopId: shop.shopId,
          planType: planType,
          planId: planId,
          pricing: {
            basePrice: shopSubscription.pricing?.price || shopSubscription.pricing?.basePrice || 0,
            currency: shopSubscription.pricing?.currency || 'USD'
          },
          paymentMethod: shopSubscription.paymentMethod || (planType === 'trial' ? 'free' : 'offline'),
          paymentDetails: shopSubscription.paymentDetails || null,
          discountDetails: shopSubscription.pricing?.discount || null
        };
        
        // Create subscription record
        const newSubscription = await createSubscription(subscriptionData, {
          actorId: 'migration_script',
          actorRole: 'system'
        });
        
        console.log(`  ✓ Created subscription: ${newSubscription.subscriptionId}`);
        fixedCount++;
        
      } catch (error) {
        console.error(`  ✗ Error processing shop ${shop.shopId}: ${error.message}`);
        errorCount++;
      }
    }
    
    // Summary
    console.log('\n' + '='.repeat(50));
    console.log('MIGRATION SUMMARY');
    console.log('='.repeat(50));
    console.log(`Total shops processed: ${shops.length}`);
    console.log(`Subscriptions created: ${fixedCount}`);
    console.log(`Shops skipped (already had subscription): ${skippedCount}`);
    console.log(`Errors: ${errorCount}`);
    console.log('='.repeat(50));
    
    if (fixedCount > 0) {
      console.log(`\n✓ Successfully created ${fixedCount} missing subscription records`);
    }
    
    if (errorCount > 0) {
      console.log(`\n⚠ ${errorCount} shops had errors and need manual review`);
    }
    
  } catch (error) {
    console.error('Migration failed:', error);
    process.exit(1);
  } finally {
    // Close MongoDB connection
    await mongoose.connection.close();
    console.log('\nMongoDB connection closed');
    process.exit(0);
  }
};

// Run the migration if this script is executed directly
if (require.main === module) {
  console.log('Starting shop subscription migration...');
  fixShopSubscriptions();
}

module.exports = fixShopSubscriptions; 