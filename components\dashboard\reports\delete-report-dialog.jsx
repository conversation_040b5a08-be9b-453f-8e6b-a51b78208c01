"use client";

import { useState } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { toast } from 'sonner';
import { AlertTriangle, FileText } from 'lucide-react';
import ReportService from './report-service';

/**
 * Dialog to confirm report deletion
 */
export function DeleteReportDialog({ isOpen, onClose, report }) {
  const [isDeleting, setIsDeleting] = useState(false);
  const reportService = new ReportService();

  /**
   * Handle report deletion
   */
  const handleDelete = async () => {
    if (!report) {
      toast.error('No report selected');
      return;
    }
    
    setIsDeleting(true);
    
    try {
      await reportService.deleteReport(report.reportId);
      toast.success('Report deleted successfully');
      onClose(true); // Refresh report list
    } catch (error) {
      console.error('Error deleting report:', error);
      toast.error('Failed to delete report');
    } finally {
      setIsDeleting(false);
    }
  };

  /**
   * Get report type badge
   */
  const getReportTypeBadge = (type) => {
    const colors = {
      'debt': 'bg-blue-50 text-blue-600 hover:bg-blue-100',
      'sales': 'bg-green-50 text-green-600 hover:bg-green-100',
      'ml-risk': 'bg-purple-50 text-purple-600 hover:bg-purple-100',
      'pos-profit': 'bg-amber-50 text-amber-600 hover:bg-amber-100',
    };

    const labels = {
      'debt': 'Debt Report',
      'sales': 'Sales Report',
      'ml-risk': 'Risk Analysis',
      'pos-profit': 'POS Profit'
    };

    return (
      <Badge variant="outline" className={colors[type] || ''}>
        {labels[type] || type}
      </Badge>
    );
  };

  if (!isOpen || !report) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle className="flex items-center text-destructive">
            <AlertTriangle className="h-5 w-5 mr-2" />
            Delete Report
          </DialogTitle>
          <DialogDescription>
            Are you sure you want to delete this report? This action cannot be undone.
          </DialogDescription>
        </DialogHeader>

        <div className="py-4">
          <div className="bg-muted/50 p-4 rounded-md mb-4">
            <div className="flex items-start mb-2">
              <FileText className="h-5 w-5 mr-2 mt-0.5 text-muted-foreground" />
              <div className="flex-1">
                <h3 className="font-medium text-sm">{report.title}</h3>
                <div className="flex items-center mt-1">
                  {getReportTypeBadge(report.type)}
                </div>
              </div>
            </div>
            <p className="text-sm text-muted-foreground mt-2">
              Generated on {new Date(report.generatedAt).toLocaleDateString()}
            </p>
          </div>
          
          <p className="text-sm text-destructive">
            Deleting this report will remove it permanently from the system.
          </p>
        </div>

        <DialogFooter>
          <Button
            variant="outline"
            onClick={() => onClose(false)}
            disabled={isDeleting}
          >
            Cancel
          </Button>
          <Button 
            variant="destructive" 
            onClick={handleDelete}
            disabled={isDeleting}
          >
            {isDeleting ? 'Deleting...' : 'Delete Report'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
