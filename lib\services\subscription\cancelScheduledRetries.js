import apiBridge from '@/lib/api/bridge';
import { ENDPOINTS } from '@/lib/api/contract';;
import { handleError, processApiResponse } from '../baseService';

/**
 * Cancel scheduled payment retries (SuperAdmin only)
 * @param {string} subscriptionId - Subscription ID
 * @returns {Promise<Object>} Cancellation result
 */
async function cancelScheduledRetries(subscriptionId) {
  try {
    if (!subscriptionId) {
      throw new Error('Subscription ID is required');
    }

    const response = await apiBridge.post(`${ENDPOINTS.SUBSCRIPTIONS.BASE}/payment-retry/${subscriptionId}/cancel`, {}, {
      clearCacheEndpoint: ENDPOINTS.SUBSCRIPTIONS.BASE
    });
    
    const result = processApiResponse(response, 'Scheduled payment retries cancelled successfully');
    return result;
  } catch (error) {
    handleError(error, 'SubscriptionService.cancelScheduledRetries', true);
    throw error;
  }
}

export default cancelScheduledRetries; 
