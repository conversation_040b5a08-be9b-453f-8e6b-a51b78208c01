"use client";

import { useEffect, useState, useCallback, useMemo } from "react";
import { useRouter } from "next/navigation";
import { toast } from "sonner";

// Import optimized hooks
import useSettingsPage from "@/hooks/use-settings-page";

// Import modular components
import { SettingsPageHeader } from "@/components/settings/settings-page-header";
import { GeneralSettingsCard } from "@/components/settings/general-settings-card";

/**
 * General Settings Page
 * Allows SuperAdmin to configure global application settings
 */
export default function GeneralSettingsPage() {
  const router = useRouter();
  
  // Use the optimized settings hook with category='general'
  const { 
    settings, 
    loading, 
    error, 
    updateSettings,
    isReady,
    isSuperAdmin
  } = useSettingsPage({
    category: 'general',
    pageTitle: 'General Settings',
    requireSuperAdmin: true
  });
  
  // No need for a separate useEffect for access control or fetching
  // The useSettingsPage hook handles this internally

  // Save general settings - memoized to prevent unnecessary recreations
  const handleSaveSettings = useCallback(async (updatedSettings) => {
    try {
      const result = await updateSettings(updatedSettings);
      
      if (result.success) {
        toast.success("Settings updated successfully");
      }
      return result;
    } catch (err) {
      toast.error("Failed to update settings");
      return { success: false, error: err.message };
    }
  }, [updateSettings]);

  // Only render when ready and for SuperAdmin
  if (!isReady) {
    return null;
  }

  return (
    <div className="container py-8">
      <SettingsPageHeader 
        title="General Settings" 
        description="Configure global application settings"
      />
      
      <div className="mt-8 max-w-4xl mx-auto">
        <GeneralSettingsCard
          initialSettings={settings}
          onSave={handleSaveSettings}
          loading={loading}
          readOnly={!isSuperAdmin}
        />
      </div>
    </div>
  );
}
