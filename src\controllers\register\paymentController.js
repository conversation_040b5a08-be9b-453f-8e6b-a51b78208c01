const mongoose = require('mongoose');
const UserService = require('../../services/userService');
const ShopService = require('../../services/shopService');
const DiscountService = require('../../services/discountService');
const EVCPaymentService = require('../../services/evcPaymentService');
const FileUploadService = require('../../services/fileUploadService');

const { Plan, Subscription } = require('../../models');
const { 
  AppError,
  ResponseHelper,
  UserHelper,
  SubscriptionHelper,
  LogHelper,
  logInfo,
  logSuccess,
  logWarning,
  logError,
  TransactionHelper
} = require('../../utils');
const idGenerator = require('../../utils/generators/idGenerator');

// Track in-progress payments to prevent duplicates
const processingPayments = new Set();

/**
 * Process payment for user subscription.
 * POST /api/register/pay
 * Requires user to be authenticated and email verified.
 */
const processPayment = async (req, res, next) => {
  const session = await mongoose.startSession();
  session.startTransaction();

  try {
    // The authenticated user is available via req.user due to auth middleware
    const userId = req.user.userId;
    const userEmail = req.user.email;

    // Check if payment is already in progress for this user
    if (processingPayments.has(userId)) {
      throw new AppError('Payment already in progress for this user', 409, 'payment_in_progress');
    }

    // Add user to processing set
    processingPayments.add(userId);

    try {
      const {
        planType,
        paymentMethod,
        paymentDetails: rawPaymentDetails,
        discountCode,
        // Enhanced offline payment fields
        payerName,
        payerPhone,
        notes
      } = req.validatedData || req.body;

      console.log('=== PAYMENT REQUEST DEBUG ===');
      console.log('Raw req.body:', JSON.stringify(req.body, null, 2));
      console.log('Raw req.validatedData:', JSON.stringify(req.validatedData, null, 2));
      console.log('Extracted planType:', planType);
      console.log('Extracted paymentMethod:', paymentMethod);
      console.log('=== END DEBUG ===');
      
      console.log('Received payment request:', {
        planType,
        paymentMethod,
        rawPaymentDetails,
        discountCode,
        payerName,
        payerPhone,
        notes,
        hasFile: !!req.file
      });

      // Auto-prepare payment details for EVC Plus payments using the user's phone number
      let paymentDetails = rawPaymentDetails || {};
      let initialPaid = false; // Assume false unless payment succeeds

      // Use a transaction for atomic updates
      const result = await TransactionHelper.withTransaction(async (session) => {
        console.log('Starting TransactionHelper.withTransaction callback...');
        // Find the user to ensure they are email verified and to get shopId
        const user = await UserService.getUserById(userId, { session });

        if (!user) {
          throw new AppError('User not found', 404, 'user_not_found');
        }

        // Ensure user's email is verified before proceeding to payment
        if (!user.emailVerified) {
          throw new AppError('Email not verified. Please verify your email before making a payment.', 403, 'email_not_verified');
        }

        // If already paid, return as complete
        if (user.isPaid) {
          logWarning(`User ${userId} attempted to pay for an already paid subscription.`, 'paymentController');
          return {
            user: UserHelper.sanitizeUser(user),
            nextStep: 'registration_complete'
          };
        }

        const shop = await ShopService.getShopById(user.shopId, { session, includeInactive: true });
        if (!shop) {
          throw new AppError('Associated shop not found', 404, 'shop_not_found');
        }

        // Check if shop has a subscription record, create one if missing (recovery from failed registration)
        let hasSubscription = false;
        if (shop.currentSubscriptionId) {
          const { Subscription } = require('../../models');
          const existingSubscription = await Subscription.findOne({ 
            subscriptionId: shop.currentSubscriptionId 
          }).session(session);
          hasSubscription = !!existingSubscription;
        }

        // If no subscription exists (from failed registration), create one now
        if (!hasSubscription) {
          logWarning(`Shop ${shop.shopId} has no subscription record, creating during payment`, 'paymentController');
          try {
            const createSubscription = require('../../services/Subscription/createSubscription');
            const subscriptionData = {
              shopId: shop.shopId,
              planType: planType,
              paymentMethod: paymentMethod === 'EVC Plus' ? 'evc_plus' : paymentMethod.toLowerCase(),
              paymentDetails: paymentDetails,
              session: session,
              createdBy: 'payment_recovery'
            };

            const recoverySubscription = await createSubscription(subscriptionData, { 
              actorId: user.userId, 
              actorRole: user.role 
            });
            
            // Update shop with subscription reference
            shop.currentSubscriptionId = recoverySubscription.subscriptionId;
            await shop.save({ session });
            
            logSuccess(`Recovery subscription ${recoverySubscription.subscriptionId} created for shop ${shop.shopId}`, 'paymentController');
          } catch (recoveryError) {
            logError(`Failed to create recovery subscription for shop ${shop.shopId}: ${recoveryError.message}`, 'paymentController');
            // Continue with payment anyway - subscription isn't strictly required for payment processing
          }
        }

        // Check if the selected payment method is enabled
        const { Setting } = require('../../models');

        // Enhanced debugging for payment method validation
        console.log(`[PaymentController] 🔍 PAYMENT METHOD VALIDATION DEBUG:`);
        console.log(`[PaymentController] - User: ${userId}`);
        console.log(`[PaymentController] - Requested Payment Method: "${paymentMethod}"`);
        console.log(`[PaymentController] - Plan Type: "${planType}"`);

        const allowedPaymentMethods = await Setting.findOne({
          key: 'payment_methods_available',
          shopId: null // Global setting
        });

        console.log(`[PaymentController] - Settings Query Result:`, {
          found: !!allowedPaymentMethods,
          key: allowedPaymentMethods?.key,
          value: allowedPaymentMethods?.value,
          dataType: allowedPaymentMethods?.dataType,
          shopId: allowedPaymentMethods?.shopId
        });

        if (allowedPaymentMethods && allowedPaymentMethods.value) {
          const isMethodAllowed = allowedPaymentMethods.value.includes(paymentMethod);
          console.log(`[PaymentController] - Payment Method Check:`, {
            requestedMethod: paymentMethod,
            allowedMethods: allowedPaymentMethods.value,
            isAllowed: isMethodAllowed
          });

          if (!isMethodAllowed) {
            console.log(`[PaymentController] ❌ PAYMENT METHOD REJECTED: "${paymentMethod}" not in allowed methods`);
            throw new AppError(
              `Payment method "${paymentMethod}" is not currently available. Allowed methods: ${allowedPaymentMethods.value.join(', ')}`,
              400,
              'invalid_payment_method'
            );
          }

          console.log(`[PaymentController] ✅ Payment method "${paymentMethod}" is allowed`);
        } else {
          console.log(`[PaymentController] ⚠️ No payment methods setting found - allowing all methods`);
        }

        // Also check if online/offline payment is enabled
        const isOnlineMethod = ['EVC Plus', 'Card', 'Mobile Money'].includes(paymentMethod);
        const isOfflineMethod = ['offline'].includes(paymentMethod);

        if (isOnlineMethod) {
          const onlineEnabled = await Setting.findOne({
            key: 'enable_online_payment',
            shopId: null
          });

          if (onlineEnabled && onlineEnabled.value === false) {
            throw new AppError(
              'Online payment methods are currently disabled',
              400,
              'payment_method_disabled'
            );
          }
        } else if (isOfflineMethod) {
          const offlineEnabled = await Setting.findOne({
            key: 'enable_offline_payment',
            shopId: null
          });

          if (offlineEnabled && offlineEnabled.value === false) {
            throw new AppError(
              'Offline payment methods are currently disabled',
              400,
              'payment_method_disabled'
            );
          }
        }

        // Apply discount if provided
        let discountDetails = null;
        if (discountCode) {
          discountDetails = await DiscountService.verifyDiscountCode(discountCode);

          if (!discountDetails.valid) {
            throw new AppError(
              discountDetails.message || 'Invalid discount code',
              400,
              'invalid_discount_code'
            );
          }
          logInfo(`Discount code ${discountCode} applied to payment for user ${userId}`, 'paymentController');
        }

        // Get plan price and details from database
        console.log('=== PLAN FETCH DEBUG ===');
        console.log('Looking for plan with type:', planType);
        
        const plan = await Plan.findOne({ type: planType });
        if (!plan) {
          throw new AppError(`Subscription plan '${planType}' not found.`, 404, 'plan_not_found');
        }
        console.log('Found plan:', {
          id: plan._id,
          planId: plan.planId,
          name: plan.name,
          type: plan.type,
          basePrice: plan.pricing.basePrice,
          currency: plan.pricing.currency,
          billingCycle: plan.pricing.billingCycle
        });

        let planPrice = plan.pricing.basePrice;
        console.log(`=== PRICE CALCULATION ===`);
        console.log(`Plan type requested: ${planType}`);
        console.log(`Plan type found: ${plan.type}`);
        console.log(`Base price: $${planPrice}`);
        console.log(`=== END PLAN DEBUG ===`);

        if (discountDetails?.valid && discountDetails.discountDetails) {
          // Apply discount to the base price
          planPrice -= discountDetails.discountDetails.discountAmount;
          if (planPrice < 0) planPrice = 0; // Ensure price doesn't go negative
          console.log(`planPrice after discount: $${planPrice}`);
        }

        // Critical: Ensure price is not zero for EVC Plus payments unless explicitly a free plan
        if (planPrice === 0 && paymentMethod === 'EVC Plus' && plan.type !== 'trial') {
          throw new AppError('Payment amount cannot be zero for EVC Plus payments unless it\'s a trial plan.', 400, 'invalid_payment_amount');
        }

        // Handle EVC Plus payment
        if (paymentMethod === 'EVC Plus' && user.phone) {
          if (!paymentDetails.phoneNumber) {
            paymentDetails.phoneNumber = user.phone; // Default to user's registered phone
          }
          logInfo(`Initiating EVC Plus payment of $${planPrice} for ${paymentDetails.phoneNumber}`, 'paymentController');

          const paymentData = {
            phone: paymentDetails.phoneNumber,
            amount: planPrice,
            reference: `sub_${Date.now()}_${userId.substring(0, 5)}`, // Unique reference
            description: `${shop.shopName} subscription: ${planType} plan`,
            shopName: shop.shopName,
            shopId: shop.shopId
          };
          console.log('=== EVC PAYMENT DEBUG ===');
          console.log('Plan type being paid:', planType);
          console.log('Final amount being charged:', planPrice);
          console.log('Payment data sent to EVC Plus:', JSON.stringify(paymentData, null, 2));
          console.log('=== END EVC DEBUG ===');

          try {
            const evcResponse = await EVCPaymentService.payByWaafiPay(paymentData); // No auto-retry here

            if (evcResponse && evcResponse.success) {
              initialPaid = true;
              paymentDetails.transactionId = evcResponse.transactionId;
              // Add other payment details from evcResponse if available
              if (evcResponse.receiptUrl) paymentDetails.receiptUrl = evcResponse.receiptUrl;
              if (evcResponse.payerName) paymentDetails.payerName = evcResponse.payerName;
              if (evcResponse.payerPhone) paymentDetails.payerPhone = evcResponse.payerPhone;
              if (evcResponse.payerEmail) paymentDetails.payerEmail = evcResponse.payerEmail;

              logSuccess(`EVC payment successful for user ${userId}`, 'paymentController');
            } else {
              // Payment failed at gateway, log and continue as not paid
              logError(`EVC payment failed for user ${userId}: ${evcResponse?.responseMessage || 'Unknown error'}`, 'paymentController', evcResponse);
              throw new AppError(
                evcResponse?.responseMessage || 'EVC Plus payment failed',
                402, // Payment Required
                'evc_payment_declined'
              ); 
            }
          } catch (paymentError) {
            logError(`Error processing EVC payment for user ${userId}: ${paymentError.message}`, 'paymentController', paymentError);
            // Re-throw payment specific AppErrors, or wrap others
            if (paymentError instanceof AppError) {
              throw paymentError;
            }
            throw new AppError(
              `Payment processing failed: ${paymentError.message}`,
              502,
              'evc_payment_error'
            );
          }
        } else if (paymentMethod === 'offline') {
          // Enhanced offline payment handling with optional proof and details
          initialPaid = false;
          
          // Handle file upload if provided
          let paymentProofFile = null;
          if (req.file) {
            try {
              paymentProofFile = await FileUploadService.savePaymentProof(req.file);
              logInfo(`Payment proof file uploaded: ${paymentProofFile.filename}`, 'paymentController');
            } catch (fileError) {
              logError(`Failed to save payment proof file: ${fileError.message}`, 'paymentController', fileError);
              // Continue without failing the payment process
            }
          }
          
          // Enhance payment details with offline payment information
          paymentDetails = {
            ...paymentDetails,
            payerName: payerName || user.fullName, // Default to user's name
            payerPhone: payerPhone || user.phone,   // Default to user's phone
            notes: notes || '',
            paymentProof: paymentProofFile ? {
              filename: paymentProofFile.filename,
              originalName: paymentProofFile.originalname,
              path: paymentProofFile.path,
              size: paymentProofFile.size,
              mimetype: paymentProofFile.mimetype,
              uploadedAt: new Date()
            } : null,
            submissionMethod: req.file || payerName || payerPhone || notes ? 'enhanced_offline' : 'basic_offline'
          };
          
          console.log(`Enhanced offline payment details for user ${userId}:`, paymentDetails);
          logInfo(`Enhanced offline payment submission for user ${userId}. Method: ${paymentDetails.submissionMethod}`, 'paymentController');
        }
        // No other payment methods are handled for initial registration payment at this step

        // Create subscription using the proper service to ensure consistency
        const createSubscription = require('../../services/Subscription/createSubscription');

        const subscriptionData = {
          shopId: shop.shopId,
          planType: plan.type,
          planId: plan.planId,
          planName: plan.name,
          pricing: {
            basePrice: plan.pricing.basePrice,
            currency: plan.pricing.currency,
            billingCycle: plan.pricing.billingCycle
          },
          paymentMethod: paymentMethod === 'EVC Plus' ? 'evc_plus' : paymentMethod.toLowerCase(),
          paymentDetails: {
            ...paymentDetails,
            verified: initialPaid,
            lastPaymentDate: initialPaid ? new Date() : undefined,
            nextPaymentDate: initialPaid ? SubscriptionHelper.calculateEndDate(plan.type, new Date()) : undefined
          },
          discountDetails: discountDetails?.valid ? discountDetails.discountDetails : null,
          session
        };

        const createdSubscription = await createSubscription(subscriptionData, {
          actorId: userId,
          actorRole: user.role || 'user'
        });

        logInfo(`Created new subscription ${createdSubscription.subscriptionId} for shop ${shop.shopId}`, 'paymentController');

        // Create Payment record for enhanced offline payments
        if (!initialPaid && (paymentDetails.submissionMethod === 'enhanced_offline' || paymentDetails.paymentProof)) {
          const { Payment } = require('../../models');
          const paymentId = await idGenerator.generatePaymentId(Payment);
          
          const paymentData = {
            paymentId,
            shopId: shop.shopId,
            customerId: user.userId,
            customerName: user.fullName,
            paymentContext: 'subscription',
            subscriptionId: createdSubscription.subscriptionId,
            amount: planPrice,
            originalAmount: planPrice,
            paymentDate: new Date(),
            method: paymentMethod.toLowerCase(),
            paymentType: 'offline',
            referenceNumber: paymentId,
            status: 'pending',
            notes: paymentDetails.notes || `${plan.type} subscription payment - Registration`,
            proofFileId: paymentDetails.paymentProof ? paymentDetails.paymentProof.filename : null,
            recordedBy: user.userId,
            recordedFromIp: req.ip || 'unknown',
            sessionType: 'online'
          };
          
          const paymentRecord = new Payment(paymentData);
          await paymentRecord.save({ session });
          logInfo(`Created payment record ${paymentId} for enhanced offline payment submission`, 'paymentController');
        }

        // Update user and shop status based on payment outcome
        console.log(`Before updating user/shop: user.isPaid=${user.isPaid}, shop.access.isPaid=${shop.access.isPaid}, initialPaid=${initialPaid}`);
        user.isPaid = initialPaid; // Set user's payment status
        shop.access.isPaid = initialPaid; // Set shop's payment status
        console.log(`After updating user/shop: user.isPaid=${user.isPaid}, shop.access.isPaid=${shop.access.isPaid}`);

        // Check for full activation after payment
        if (user.emailVerified === true && user.isPaid === true) {
          const wasAlreadyActivated = user.isActivated; // Check if already activated
          user.isActivated = true;
          user.status = 'active'; // Set user status to active
          shop.access.isActivated = true;
          shop.status = 'active'; // Set shop status to active
          
          // Enable email notifications for system emails (welcome, receipts, etc.)
          if (!shop.notifications.emailEnabled) {
            shop.notifications.emailEnabled = true;
            logInfo(`Email notifications enabled for shop ${shop.shopId} during activation`, 'paymentController');
          }
          
          logInfo(`User ${user.userId} and Shop ${shop.shopId} activated successfully.`, 'paymentController');
          
          // Send welcome and billing receipt emails if user just became activated
          if (!wasAlreadyActivated && initialPaid) {
            try {
              // Import email service
              const AuthEmailService = require('../../services/email/authEmailService');
              
              // Send welcome email
              await AuthEmailService.sendWelcomeEmail(user, shop, createdSubscription);
              logSuccess(`Welcome email sent to ${user.email} after payment completion`, 'paymentController');
              
              // Send billing receipt email
              const paymentData = {
                transactionId: paymentDetails.transactionId || `TXN-${Date.now()}`,
                amount: planPrice.toString(),
                method: paymentMethod === 'EVC Plus' ? 'EVC Plus' : paymentMethod
              };
              
              await AuthEmailService.sendRegistrationReceiptEmail(user, shop, paymentData, createdSubscription);
              logSuccess(`Registration receipt email sent to ${user.email} after payment completion`, 'paymentController');
            } catch (emailError) {
              logError(`Failed to send welcome/receipt emails to ${user.email} after payment`, 'paymentController', emailError);
              // Do not throw; payment should not fail if email fails
            }
          }
        }

        // Link the new subscription to the shop and remove embedded subscription
        shop.currentSubscriptionId = createdSubscription.subscriptionId; // Reference the new subscription
        shop.subscription = undefined; // Remove the old embedded subscription field

        await user.save({ session }); // Save user changes
        await shop.save({ session }); // Save shop changes

        // Log the payment event
        await LogHelper.createPaymentLog(
          user.userId,
          shop.shopId,
          initialPaid ? 'payment_successful' : 'payment_skipped_offline',
          {
            planType,
            paymentMethod: paymentMethod === 'EVC Plus' ? 'evc_plus' : paymentMethod.toLowerCase(),
            amount: planPrice,
            transactionId: paymentDetails.transactionId || 'N/A',
            initialPaid
          },
          session
        );

        logSuccess(`Payment process completed for user ${userId}. Initial Paid: ${initialPaid}`, 'paymentController');
        console.log('TransactionHelper.withTransaction callback returning:', { user: UserHelper.sanitizeUser(user), shop, initialPaid, createdSubscription });

        return { user: UserHelper.sanitizeUser(user), shop, initialPaid, createdSubscription, isOfflinePayment: !initialPaid };
      }, { name: 'ProcessPayment' });

      console.log('TransactionHelper.withTransaction returned:', result);

      // CRITICAL: If result is undefined (e.g., due to an error handled by the catch block),
      // we must not proceed. The error should have already been passed to next(error).
      if (!result) {
        logError('TransactionHelper.withTransaction returned undefined result. Aborting further processing.', 'paymentController');
        return; // Stop further execution in this controller
      }

      // Send SuperAdmin notification for offline payment orders (outside transaction)
      if (result.isOfflinePayment && result.shop) {
        try {
          // Get SuperAdmin email from environment or default
          const superAdminEmail = process.env.SUPER_ADMIN_EMAIL || '<EMAIL>';
          
          // Get enhanced payment details from the subscription
          const paymentDetails = result.createdSubscription?.payment?.paymentDetails || {};
          
          // Prepare comprehensive notification data
          const notificationData = {
            shopName: result.shop.shopName,
            shopId: result.shop.shopId,
            subscriptionId: result.createdSubscription?.subscriptionId || 'N/A',
            amount: result.createdSubscription?.pricing?.basePrice || 10,
            currency: 'USD',
            paymentMethod: paymentMethod,
            customerName: result.user.fullName,
            customerEmail: result.user.email,
            customerPhone: result.user.phone,
            referenceNumber: `DEYN-${new Date().getFullYear()}-${result.user.userId.slice(-6).toUpperCase()}`,
            
            // Enhanced offline payment details
            payerName: paymentDetails.payerName || result.user.fullName,
            payerPhone: paymentDetails.payerPhone || result.user.phone,
            notes: paymentDetails.notes || `Customer selected ${paymentMethod} payment method and is waiting for approval`,
            bankDetails: paymentDetails.bankDetails || '',
            transferReference: paymentDetails.transferReference || '',
            
            // File attachment information
            hasPaymentProof: !!paymentDetails.paymentProof,
            paymentProofFile: paymentDetails.paymentProof ? {
              filename: paymentDetails.paymentProof.filename,
              originalName: paymentDetails.paymentProof.originalName,
              size: paymentDetails.paymentProof.size,
              uploadedAt: paymentDetails.paymentProof.uploadedAt
            } : null,
            
            // Submission method for context
            submissionMethod: paymentDetails.submissionMethod || 'basic_offline',
            submissionTime: new Date().toISOString()
          };
          
          // Import EmailService correctly
          const EmailService = require('../../services/email');
          await EmailService.admin.sendOfflinePaymentOrderNotification(superAdminEmail, notificationData);
          logSuccess(`Enhanced SuperAdmin notification sent for offline payment order: ${result.shop.shopName} (${paymentDetails.submissionMethod})`, 'paymentController');
        } catch (notificationError) {
          logError(`Failed to send SuperAdmin notification for offline payment`, 'paymentController', notificationError);
          // Don't fail the payment process if notification fails
        }
      }

      // Determine next step based on the outcome
      let nextStep = 'payment_failed';
      if (result.initialPaid) {
        nextStep = 'registration_complete';
      } else {
        // Even if offline, if everything else was fine, registration is complete for now
        nextStep = 'registration_complete_offline_payment_pending';
      }

      // Return success response with user status and next step
      return ResponseHelper.success(
        res,
        result.initialPaid ? 'Payment successful and account activated!' : 'Payment skipped. Complete payment later.',
        {
          user: result.user,
          shop: result.shop,
          subscription: result.createdSubscription,
          nextStep: nextStep
        }
      );

    } finally {
      // Always remove the user from processing set, even if there's an error
      processingPayments.delete(userId);
    }

  } catch (error) {
    logError(`Payment processing failed: ${error.message}`, 'paymentController', error);

    // Handle specific errors
    if (error instanceof AppError) {
      return next(error);
    } else if (error.name === 'MongoServerError' && error.code === 11000) {
      return next(new AppError('Database conflict during payment processing', 409, 'database_conflict'));
    }

    return next(new AppError(
      error.message || 'Error during payment processing',
      error.statusCode || 500,
      error.type || 'payment_processing_error'
    ));
  }
};

module.exports = {
  processPayment
}; 