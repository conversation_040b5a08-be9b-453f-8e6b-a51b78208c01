/**
 * Settings validation schemas
 * Handles validation for settings-related operations
 */
const Joi = require('joi');
const { validationPatterns } = require('../validationPatterns');

// Schema for EVC credentials
const evcCredentialsSchema = Joi.object({
  merchantUId: Joi.string().trim().min(5).max(100).required()
    .messages({
      'string.empty': 'Merchant UID is required',
      'string.min': 'Merchant UID must be at least {#limit} characters',
      'string.max': 'Merchant UID cannot exceed {#limit} characters'
    }),
  apiUserId: Joi.string().trim().min(5).max(100).required()
    .messages({
      'string.empty': 'API User ID is required',
      'string.min': 'API User ID must be at least {#limit} characters',
      'string.max': 'API User ID cannot exceed {#limit} characters'
    }),
  apiKey: Joi.string().trim().min(8).max(500).required()
    .messages({
      'string.empty': 'API Key is required',
      'string.min': 'API Key must be at least {#limit} characters',
      'string.max': 'API Key cannot exceed {#limit} characters'
    }),
  merchantNo: Joi.string().trim().min(4).max(30).required()
    .messages({
      'string.empty': 'Merchant Number is required',
      'string.min': 'Merchant Number must be at least {#limit} characters',
      'string.max': 'Merchant Number cannot exceed {#limit} characters'
    }),
  url: Joi.string().trim().uri().default('https://api.waafi.app/asm')
    .messages({
      'string.uri': 'URL must be a valid URI'
    }),
  shopId: Joi.string().trim().allow(null, '')
});

// Schema for testing EVC credentials
const testEVCCredentialsSchema = Joi.object({
  shopId: Joi.string().trim().allow(null, '')
});

// Schema for updating payment methods
const updatePaymentMethodsSchema = Joi.object({
  enableOnline: Joi.boolean(),
  enableOffline: Joi.boolean(),
      context: Joi.string().valid('general', 'subscription'),
  methods: Joi.array().items(Joi.string()),
  shopId: Joi.string().trim().allow(null, '')
});

// Schema for updating a setting by key
const updateSettingByKeySchema = Joi.object({
  // The value can be of mixed types, so we allow anything and rely on model validation
  value: Joi.alternatives().try(Joi.string(), Joi.number(), Joi.boolean(), Joi.object(), Joi.array(), Joi.date(), null).required()
    .messages({
      'any.required': 'Setting value is required',
    }),
});

module.exports = {
  evcCredentialsSchema,
  testEVCCredentialsSchema,
  updatePaymentMethodsSchema,
  updateSettingByKeySchema,
};
