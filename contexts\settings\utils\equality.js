/**
 * Settings Context - Equality Utility
 * Provides deep equality checks for state comparisons
 */

/**
 * Deep equality check between two values
 * Used to prevent unnecessary state updates when values haven't changed
 * 
 * @param {*} a - First value
 * @param {*} b - Second value
 * @returns {boolean} Whether values are deeply equal
 */
export function isEqual(a, b) {
  // Same reference check
  if (a === b) return true;
  
  // Object comparison
  if (a && b && typeof a === 'object' && typeof b === 'object') {
    // Array comparison
    if (Array.isArray(a) && Array.isArray(b)) {
      if (a.length !== b.length) return false;
      
      for (let i = 0; i < a.length; i++) {
        if (!isEqual(a[i], b[i])) return false;
      }
      
      return true;
    }
    
    // Object key comparison
    const keysA = Object.keys(a);
    const keysB = Object.keys(b);
    
    if (keysA.length !== keysB.length) return false;
    
    for (const key of keysA) {
      if (!keysB.includes(key)) return false;
      if (!isEqual(a[key], b[key])) return false;
    }
    
    return true;
  }
  
  // Primitive comparison
  return false;
}
