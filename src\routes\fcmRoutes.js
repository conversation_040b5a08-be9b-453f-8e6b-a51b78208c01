const express = require('express');
const { body } = require('express-validator');
const FCMTokenController = require('../controllers/fcmTokenController');
const { authenticate, authorize } = require('../middleware/authMiddleware');

const router = express.Router();

/**
 * FCM Token Management Routes
 * For Admin/Shop Owner mobile app users only
 */

// Apply authentication middleware to all routes
router.use(authenticate);

// Apply role middleware - only admin roles can manage FCM tokens
router.use(authorize(['superAdmin', 'admin', 'employee']));

/**
 * @route   POST /api/fcm/register
 * @desc    Register FCM token for push notifications
 * @access  Admin/Shop Owner only
 */
router.post('/register', [
  body('token')
    .isString()
    .trim()
    .notEmpty()
    .withMessage('FCM token is required'),
  body('deviceInfo')
    .optional()
    .isObject()
    .withMessage('Device info must be an object'),
  body('deviceInfo.platform')
    .optional()
    .isIn(['android', 'ios'])
    .withMessage('Platform must be android or ios'),
  body('deviceInfo.deviceId')
    .optional()
    .isString()
    .trim()
    .withMessage('Device ID must be a string'),
  body('deviceInfo.appVersion')
    .optional()
    .isString()
    .trim()
    .withMessage('App version must be a string'),
  body('deviceInfo.osVersion')
    .optional()
    .isString()
    .trim()
    .withMessage('OS version must be a string')
], FCMTokenController.registerToken);

/**
 * @route   POST /api/fcm/test
 * @desc    Send test push notification to user's devices
 * @access  Admin/Shop Owner only
 */
router.post('/test', FCMTokenController.testNotification);

module.exports = router; 