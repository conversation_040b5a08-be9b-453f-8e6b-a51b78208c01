# 🎯 Simple RiskScore API Documentation

## **Overview**
Simple risk score tracking system for DeynCare. Allows shops to view their customers' risk scores and SuperAdmin to view all risk scores across the platform.

---

## **📊 Admin Endpoints (Shop Owners)**

### **Get Shop Customer Risk Scores**
```http
GET /api/risk/shop
```

**Access:** Admin only (shop-specific)  
**Description:** Get risk scores for all customers in your shop

#### **Query Parameters:**
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `riskLevel` | string | No | Filter by risk level: `Low Risk`, `Medium Risk`, `High Risk`, `Critical Risk` |
| `sortBy` | string | No | Sort by: `riskScore`, `lastAssessment` (default: `riskScore`) |
| `sortOrder` | string | No | Sort order: `asc`, `desc` (default: `desc`) |
| `page` | number | No | Page number (default: 1) |
| `limit` | number | No | Results per page (default: 50) |

#### **Response:**
```json
{
  "success": true,
  "message": "Shop risk scores retrieved successfully",
  "data": [
    {
      "customerId": "CUST001",
      "customerName": "Ahmed Hassan",
      "phone": "+252617123456",
      "riskScore": 75,
      "riskLevel": "High Risk",
      "lastAssessment": "2025-01-20T14:30:00.000Z",
      "outstandingBalance": 1500
    }
  ],
  "pagination": {
    "currentPage": 1,
    "totalPages": 3,
    "totalCount": 125
  },
  "statusCode": 200
}
```

---

## **👑 SuperAdmin Endpoints**

### **Get All Risk Scores (Platform-wide)**
```http
GET /api/risk/all
```

**Access:** SuperAdmin only  
**Description:** View risk scores across all shops in the platform

#### **Response:**
```json
{
  "success": true,
  "message": "All risk scores retrieved successfully",
  "data": [
    {
      "customerId": "CUST001",
      "customerName": "Ahmed Hassan",
      "phone": "+252617123456",
      "shopId": "SHOP001",
      "riskScore": 85,
      "riskLevel": "High Risk",
      "lastAssessment": "2025-01-20T14:30:00.000Z",
      "outstandingBalance": 2000
    }
  ],
  "pagination": {
    "currentPage": 1,
    "totalPages": 15,
    "totalCount": 1456
  },
  "statusCode": 200
}
```

---

## **🔐 Authentication**

All endpoints require authentication with JWT token:

```bash
# Header format
Authorization: Bearer YOUR_JWT_TOKEN
```

**Role Access:**
- **Admin**: Can only access `/api/risk/shop` (their shop's data)
- **SuperAdmin**: Can access all endpoints

---

## **📈 Risk Score System**

### **Risk Levels:**
- **Low Risk** (0-25): Good payment behavior
- **Medium Risk** (26-50): Moderate risk factors  
- **High Risk** (51-75): Significant risk indicators
- **Critical Risk** (76-100): High probability of default

### **Risk Score Sources:**
- `ml_api`: Generated by ML FastAPI service
- `manual`: Manually set by SuperAdmin
- `system`: Default/initial assessment

This simple system provides the **essential risk tracking functionality** you requested! 🚀 