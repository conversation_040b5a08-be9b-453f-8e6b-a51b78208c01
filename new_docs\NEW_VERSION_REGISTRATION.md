# New Version Registration System Documentation

## Overview
The new version of the registration system implements a modular, step-by-step approach to user and shop registration. The system is designed to be robust, secure, and flexible, supporting multiple payment methods and verification processes.

## Registration Flow

### 1. Initial Registration
**Endpoint:** `POST /api/register/init`

#### Request Body
```json
{
  "fullName": "string",
  "email": "string",
  "phone": "string",
  "password": "string",
  "shopName": "string",
  "shopAddress": "string",
  "planType": "trial" | "basic" | "premium",
  "registeredBy": "self" | "admin",
  "paymentMethod": "offline" | "EVC Plus",
  "initialPaid": boolean,
  "paymentDetails": object,
  "discountCode": "string"
}
```

#### Response
```json
{
  "success": true,
  "message": "Registration initiated successfully. Please verify your email.",
  "data": {
    "user": {
      "userId": "string",
      "fullName": "string",
      "email": "string",
      "status": "pending_email_verification"
    },
    "shop": {
      "id": "string",
      "name": "string"
    },
    "nextStep": "verify_email_required"
  }
}
```

### 2. Email Verification
**Endpoint:** `POST /api/register/verify-email`

#### Request Body
```json
{
  "email": "string",
  "verificationCode": "string"
}
```

#### Response
```json
{
  "success": true,
  "message": "Email verified successfully",
  "data": {
    "user": {
      "userId": "string",
      "fullName": "string",
      "email": "string",
      "status": "email_verified_pending_payment" | "active"
    },
    "nextStep": "payment_required" | "registration_complete",
    "accessToken": "string",
    "refreshToken": "string"
  }
}
```

### 3. Payment Processing
**Endpoint:** `POST /api/register/pay`

#### Request Body
```json
{
  "planType": "string",
  "paymentMethod": "EVC Plus" | "offline",
  "paymentDetails": {
    "phoneNumber": "string",
    "paymentReference": "string",
    "paymentDate": "string",
    "paymentNotes": "string"
  },
  "discountCode": "string"
}
```

#### Response
```json
{
  "success": true,
  "message": "Payment successful and account activated!",
  "data": {
    "user": {
      "userId": "string",
      "fullName": "string",
      "email": "string",
      "status": "active" | "email_verified_pending_payment"
    },
    "shop": {
      "shopId": "string",
      "name": "string",
      "status": "active" | "pending"
    },
    "subscription": {
      "subscriptionId": "string",
      "planType": "string",
      "status": "active" | "pending_payment"
    },
    "nextStep": "registration_complete" | "registration_complete_offline_payment_pending"
  }
}
```

## Status Transitions

### User Status Flow
```
pending_email_verification
  ↓ (after email verification)
email_verified_pending_payment
  ↓ (after payment)
active
```

### Shop Status Flow
```
pending
  ↓ (after email verification + payment)
active
```

### Subscription Status Flow
```
pending_payment
  ↓ (after payment)
active
```

### Next Steps Flow
```
verify_email_required
  ↓ (after email verification)
payment_required
  ↓ (after payment)
registration_complete
```

## Error Handling

### Initial Registration Errors
- `409`: Duplicate email/shop
- `400`: Invalid input data
- `500`: Server error

### Email Verification Errors
- `400`: Invalid verification code
- `400`: Verification code expired
- `404`: User not found
- `409`: Database conflict

### Payment Processing Errors
- `400`: Invalid payment method
- `400`: Invalid discount code
- `402`: Payment declined
- `404`: Plan not found
- `409`: Payment in progress
- `502`: Payment gateway error

## Features

### 1. Discount Code Handling
- Applied during initial registration
- Can be applied during payment
- Validates discount code
- Applies discount to plan price

### 2. Payment Methods
- EVC Plus: Immediate verification
- Offline: Manual verification required
- Supports multiple payment methods
- Configurable through settings

### 3. Security Features
- Transaction management
- Duplicate payment prevention
- Email verification
- Token-based authentication

## Implementation Details

### Transaction Management
- Uses `TransactionHelper` for atomic operations
- Ensures data consistency across user and shop creation
- Handles rollback on failures

### Email Verification
- Generates unique verification codes
- Implements code expiry
- Sends verification emails asynchronously

### Payment Processing
- Supports multiple payment gateways
- Implements payment status tracking
- Handles offline payment verification
- Creates subscription records

### Status Management
- Tracks user, shop, and subscription status
- Implements proper status transitions
- Ensures data consistency across status changes

## Best Practices

1. **Error Handling**
   - Use specific error types
   - Implement proper error logging
   - Return meaningful error messages

2. **Security**
   - Validate all input data
   - Implement rate limiting
   - Use secure password hashing
   - Implement proper token management

3. **Performance**
   - Use transactions for atomic operations
   - Implement proper indexing
   - Handle asynchronous operations properly

4. **Maintainability**
   - Follow consistent coding standards
   - Implement proper logging
   - Use meaningful variable names
   - Document code properly

## Future Improvements

1. **Additional Payment Methods**
   - Support for more payment gateways
   - Integration with popular payment providers

2. **Enhanced Security**
   - Two-factor authentication
   - IP-based verification
   - Enhanced fraud detection

3. **User Experience**
   - Improved error messages
   - Better status tracking
   - Enhanced notification system

4. **Analytics**
   - Registration success rate tracking
   - Payment success rate tracking
   - User behavior analysis 