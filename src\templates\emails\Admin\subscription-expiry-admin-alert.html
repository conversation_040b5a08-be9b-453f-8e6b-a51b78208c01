<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Subscription Expiry Alert - DeynCare Admin</title>
    <link rel="stylesheet" href="../css/styles.css">
</head>
<body>
    <div class="email-container admin-alert">
        <div class="header">
            <img src="https://yourwebsite.com/logo.png" alt="DeynCare Logo" class="logo">
            <div class="alert-badge">ADMIN ALERT</div>
        </div>
        
        <div class="content">
            <h1>🚨 Subscription Expiry Alert</h1>
            
            <div class="summary-stats">
                <h2>Daily Summary - {{currentDate}}</h2>
                
                <div class="stats-grid">
                    <div class="stat-card critical">
                        <div class="stat-number">{{totalExpired}}</div>
                        <div class="stat-label">Total Expired</div>
                    </div>
                    
                    <div class="stat-card revenue">
                        <div class="stat-number">${{totalRevenueLoss}}</div>
                        <div class="stat-label">Revenue Impact</div>
                    </div>
                    
                    <div class="stat-card today">
                        <div class="stat-number">{{expiredToday}}</div>
                        <div class="stat-label">Expired Today</div>
                    </div>
                </div>
            </div>

            <div class="plan-breakdown">
                <h3>📊 Breakdown by Plan Type</h3>
                <table class="breakdown-table">
                    {{#each planBreakdown}}
                    <tr>
                        <td class="plan-type">{{@key}} Plan</td>
                        <td class="plan-count">{{this}} expired</td>
                    </tr>
                    {{/each}}
                </table>
            </div>

            <div class="expired-subscriptions">
                <h3>📋 Expired Subscriptions Details</h3>
                <div class="subscription-list">
                    {{#each subscriptions}}
                    <div class="subscription-item">
                        <div class="shop-info">
                            <strong>{{shopName}}</strong>
                            <span class="shop-id">(ID: {{shopId}})</span>
                        </div>
                        <div class="subscription-details">
                            <span class="plan-badge {{planType}}">{{planType}} Plan</span>
                            <span class="expired-date">Expired: {{expiredOn}}</span>
                        </div>
                    </div>
                    {{/each}}
                </div>
            </div>

            <div class="action-section">
                <h3>🎯 Recommended Actions</h3>
                <ul class="action-list">
                    <li>Review subscription metrics and trends</li>
                    <li>Contact high-value customers for renewal discussions</li>
                    <li>Analyze churn reasons and improve retention strategies</li>
                    <li>Consider targeted re-engagement campaigns</li>
                    <li>Update pricing or feature offerings if needed</li>
                </ul>
            </div>

            <div class="quick-links">
                <h3>🔗 Quick Administrative Links</h3>
                <div class="link-buttons">
                    <a href="{{dashboardUrl}}" class="admin-button primary">
                        📊 View Subscription Dashboard
                    </a>
                    <a href="{{reportsUrl}}" class="admin-button secondary">
                        📈 Generate Reports
                    </a>
                </div>
            </div>

            <div class="alert-info">
                <h4>ℹ️ About This Alert</h4>
                <p>This automated alert is sent daily to notify administrators about subscription expirations. It helps you stay informed about customer churn and take proactive retention measures.</p>
                <p><strong>Next Alert:</strong> Tomorrow at the same time</p>
            </div>
        </div>
        
        <div class="footer admin-footer">
            <p>&copy; 2024 DeynCare Admin System. All rights reserved.</p>
            <p>This is an automated system notification.</p>
        </div>
    </div>

    <style>
        .admin-alert {
            max-width: 700px;
            margin: 0 auto;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .alert-badge {
            background: #dc3545;
            color: white;
            padding: 5px 15px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: bold;
            text-align: center;
            margin-top: 10px;
        }
        .summary-stats {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 15px;
            margin-top: 15px;
        }
        .stat-card {
            text-align: center;
            padding: 15px;
            border-radius: 8px;
            color: white;
        }
        .stat-card.critical {
            background: #dc3545;
        }
        .stat-card.revenue {
            background: #fd7e14;
        }
        .stat-card.today {
            background: #6f42c1;
        }
        .stat-number {
            font-size: 32px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .stat-label {
            font-size: 14px;
            opacity: 0.9;
        }
        .plan-breakdown {
            margin: 25px 0;
        }
        .breakdown-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }
        .breakdown-table td {
            padding: 10px;
            border-bottom: 1px solid #eee;
        }
        .plan-type {
            font-weight: bold;
        }
        .plan-count {
            text-align: right;
            color: #dc3545;
            font-weight: bold;
        }
        .subscription-list {
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .subscription-item {
            padding: 15px;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: between;
            align-items: center;
        }
        .shop-info {
            flex: 1;
        }
        .shop-id {
            color: #666;
            font-size: 12px;
        }
        .subscription-details {
            text-align: right;
        }
        .plan-badge {
            padding: 3px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: bold;
            margin-right: 8px;
        }
        .plan-badge.monthly {
            background: #e3f2fd;
            color: #1976d2;
        }
        .plan-badge.yearly {
            background: #f3e5f5;
            color: #7b1fa2;
        }
        .expired-date {
            color: #dc3545;
            font-size: 12px;
        }
        .action-list {
            list-style: none;
            padding: 0;
        }
        .action-list li {
            padding: 8px 0;
            border-left: 3px solid #007bff;
            padding-left: 15px;
            margin: 5px 0;
        }
        .link-buttons {
            display: flex;
            gap: 15px;
            margin: 15px 0;
        }
        .admin-button {
            flex: 1;
            text-align: center;
            padding: 12px;
            text-decoration: none;
            border-radius: 5px;
            font-weight: bold;
            color: white;
        }
        .admin-button.primary {
            background: #007bff;
        }
        .admin-button.secondary {
            background: #6c757d;
        }
        .alert-info {
            background: #e7f3ff;
            padding: 15px;
            border-radius: 5px;
            border-left: 4px solid #007bff;
            margin: 20px 0;
        }
        .admin-footer {
            background: #343a40;
            color: white;
            text-align: center;
            padding: 15px;
            font-size: 12px;
        }
    </style>
</body>
</html> 