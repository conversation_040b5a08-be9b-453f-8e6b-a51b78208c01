"use client"

import Image from "next/image"
import { useTheme } from "next-themes"
import { useEffect, useState } from "react"
import { cn } from "@/lib/utils"

export function AuthLogo({ 
  className = "",
  size = "2xl" // Default to extra large for auth pages
}) {
  const { theme, systemTheme, resolvedTheme } = useTheme()
  const [mounted, setMounted] = useState(false)
  
  useEffect(() => {
    setMounted(true)
  }, [])
  
  // Enhanced theme detection - force detection even if not mounted
  const isDark = mounted && (
    resolvedTheme === "dark" || 
    theme === "dark" || 
    (theme === "system" && systemTheme === "dark") ||
    (typeof document !== 'undefined' && document.documentElement.classList.contains('dark'))
  )
  
  // Size configurations optimized for auth pages - very large sizes
  const sizeClasses = {
    lg: "h-16 w-auto",
    xl: "h-20 w-auto", 
    "2xl": "h-24 w-auto",
    "3xl": "h-28 w-auto"
  }
  
  const logoClass = sizeClasses[size] || sizeClasses["2xl"]
  
  // Choose the correct logo based on theme with fallback to black for SSR
  const logoSrc = mounted 
    ? (isDark ? "/images/deyncare_logo_white.png" : "/images/deyncare_logo_black.png")
    : "/images/deyncare_logo_black.png"
  
  // SVG fallback for when images don't load
  const LogoFallback = () => (
    <div className="flex items-center space-x-3 bg-primary/10 rounded-lg px-6 py-3 border">
      <div className="w-12 h-12 bg-primary rounded-lg flex items-center justify-center flex-shrink-0">
        <svg viewBox="0 0 375 375" className="w-8 h-8 text-white" fill="currentColor">
          <path d="M147.945312 217.484375 L 174.574219 217.484375 C 187.773438 217.484375 198.726562 206.945312 198.851562 193.75 C 198.980469 180.421875 188.179688 169.539062 174.878906 169.539062 L 105.359375 169.539062 L 122.433594 202.066406 C 127.414062 211.546875 137.238281 217.484375 147.945312 217.484375" />
          <path d="M 174.125 73.648438 L 55.015625 73.648438 L 72.09375 106.171875 C 77.070312 115.65625 86.894531 121.59375 97.601562 121.59375 L 174.265625 121.59375 C 213.894531 121.59375 246.871094 154.015625 246.800781 193.644531 C 246.730469 233.304688 214.554688 265.433594 174.878906 265.433594 L 155.703125 265.433594 L 171.375 295.285156 C 177.515625 306.980469 190.832031 313.058594 203.65625 309.902344 C 255.902344 297.027344 294.664062 249.894531 294.746094 193.695312 C 294.84375 127.667969 240.152344 73.648438 174.125 73.648438" />
        </svg>
      </div>
      <div className="text-2xl font-bold text-primary">DeynCare</div>
    </div>
  )
  
  return (
    <div className={cn("flex items-center justify-center", className)}>
      <div className="relative">
        <Image
          src={logoSrc}
          alt="DeynCare Business Management Platform"
          width={400}
          height={100}
          className={cn(logoClass, "object-contain max-w-full")}
          priority
          onError={(e) => {
            console.warn(`Failed to load logo: ${logoSrc}`)
            // Hide the broken image and show fallback
            e.target.style.display = 'none'
            e.target.nextSibling?.classList.remove('hidden')
          }}
          onLoad={() => {
            console.log(`Logo loaded successfully: ${logoSrc}`)
          }}
        />
        {/* Fallback shown when image fails */}
        <div className="hidden">
          <LogoFallback />
        </div>
      </div>
    </div>
  )
}

export default AuthLogo 