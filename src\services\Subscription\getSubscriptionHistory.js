/**
 * Get Subscription History Service
 */
const { Subscription } = require('../../models');
const { logError } = require('../../utils');

/**
 * Get subscription history for a shop
 * @param {string} shopId - ID of the shop
 * @returns {Promise<Array>} Subscription history
 */
const getSubscriptionHistory = async (shopId) => {
  try {
    const subscriptions = await Subscription.find({
      shopId,
      isDeleted: false
    }).sort({ 'dates.startDate': -1 })
      .populate('planId', 'name type pricing features limits'); // Populate the plan data
    
    return subscriptions;
  } catch (error) {
    logError(`Failed to get subscription history for shop: ${shopId}`, 'SubscriptionService', error);
    throw error;
  }
};

module.exports = getSubscriptionHistory;
