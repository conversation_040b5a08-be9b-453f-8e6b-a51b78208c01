/**
 * List Discounts Module
 * Handles retrieving multiple discount codes with filtering and pagination
 */
const { DiscountCode } = require('../../models');
const { AppError, logError } = require('../../utils');

/**
 * Get discount codes with filtering and pagination
 * @param {Object} filter - Filter criteria
 * @param {Object} options - Pagination and sorting options
 * @returns {Promise<Object>} Discount codes with pagination info
 */
const getDiscountCodes = async (filter = {}, options = {}) => {
  try {
    // Default options
    const defaultOptions = {
      page: 1,
      limit: 20,
      sort: { createdAt: -1 }
    };
    
    // Merge with provided options
    const mergedOptions = { ...defaultOptions, ...options };
    
    // Build query
    const query = { isDeleted: false };
    
    // Apply filters
    if (filter.isActive !== undefined) {
      query.isActive = filter.isActive;
    }
    
    if (filter.shopId) {
      query.shopId = filter.shopId;
    }
    
    if (filter.applicableFor) {
      query.applicableFor = { $in: [filter.applicableFor, 'all'] };
    }
    
    if (filter.code) {
      query.code = { $regex: new RegExp(filter.code, 'i') };
    }
    
    if (filter.type) {
      query.type = filter.type;
    }
    
    // Handle date range filters
    if (filter.startDateFrom || filter.startDateTo) {
      query.startDate = {};
      
      if (filter.startDateFrom) {
        query.startDate.$gte = new Date(filter.startDateFrom);
      }
      
      if (filter.startDateTo) {
        query.startDate.$lte = new Date(filter.startDateTo);
      }
    }
    
    if (filter.expiryDateFrom || filter.expiryDateTo) {
      query.expiryDate = {};
      
      if (filter.expiryDateFrom) {
        query.expiryDate.$gte = new Date(filter.expiryDateFrom);
      }
      
      if (filter.expiryDateTo) {
        query.expiryDate.$lte = new Date(filter.expiryDateTo);
      }
    }
    
    // Handle active/expired filter
    if (filter.status === 'active') {
      query.isActive = true;
      query.expiryDate = { $gt: new Date() };
    } else if (filter.status === 'expired') {
      query.expiryDate = { $lte: new Date() };
    }
    
    // Get total count
    const totalCount = await DiscountCode.countDocuments(query);
    
    // Calculate pagination
    const totalPages = Math.ceil(totalCount / mergedOptions.limit);
    const skip = (mergedOptions.page - 1) * mergedOptions.limit;
    
    // Get discount codes
    const discountCodes = await DiscountCode.find(query)
      .sort(mergedOptions.sort)
      .skip(skip)
      .limit(mergedOptions.limit);
    
    return {
      discountCodes,
      pagination: {
        total: totalCount,
        page: mergedOptions.page,
        limit: mergedOptions.limit,
        pages: totalPages
      }
    };
  } catch (error) {
    logError(`Failed to get discount codes: ${error.message}`, 'DiscountService', error);
    throw new AppError('Failed to retrieve discount codes', 500, 'discount_retrieval_error');
  }
};

module.exports = getDiscountCodes;
