"use client";

import { useEffect, useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Clock, CheckCircle, XCircle, Building2, Users, MessageSquare, Loader2 } from "lucide-react";
import { format, formatDistanceToNow } from "date-fns";
import NotificationService from "@/lib/services/notification/index";

/**
 * Recent Activity Component
 * Shows the most recent notification activities in the overview tab
 */
export function RecentActivity() {
  const [recentNotifications, setRecentNotifications] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    loadRecentNotifications();
  }, []);

  const loadRecentNotifications = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await NotificationService.getNotificationHistory({
        limit: 5, // Only show last 5 notifications
        offset: 0
      });

      if (response && response.success) {
        setRecentNotifications(response.data?.notifications || []);
      } else {
        setError(response?.message || 'Failed to load recent activity');
      }
    } catch (err) {
      console.error('[RecentActivity] Error loading notifications:', err);
      setError(err.message || 'An error occurred while loading recent activity');
    } finally {
      setIsLoading(false);
    }
  };

  const getTypeIcon = (type) => {
    const icons = {
      shops: Building2,
      broadcast: Users,
      debt_reminders: MessageSquare,
      push: MessageSquare,
      notification: MessageSquare
    };
    return icons[type] || MessageSquare;
  };

  const getStatusIcon = (status) => {
    const icons = {
      delivered: CheckCircle,
      sent: CheckCircle,
      failed: XCircle,
      pending: Clock,
      expired: XCircle
    };
    return icons[status] || Clock;
  };

  const getStatusColor = (status) => {
    const colors = {
      delivered: "text-green-600",
      sent: "text-blue-600",
      failed: "text-red-600",
      pending: "text-yellow-600",
      expired: "text-gray-600"
    };
    return colors[status] || "text-gray-600";
  };

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Clock className="h-5 w-5" />
            Recent Activity
          </CardTitle>
          <CardDescription>
            Latest notification events and activities
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <Loader2 className="h-8 w-8 animate-spin text-muted-foreground mx-auto mb-4" />
            <p className="text-sm text-muted-foreground">Loading recent activity...</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Clock className="h-5 w-5" />
            Recent Activity
          </CardTitle>
          <CardDescription>
            Latest notification events and activities
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <XCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <p className="text-sm text-muted-foreground">{error}</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (recentNotifications.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Clock className="h-5 w-5" />
            Recent Activity
          </CardTitle>
          <CardDescription>
            Latest notification events and activities
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <Clock className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="font-medium text-muted-foreground">No Recent Activity</h3>
            <p className="text-sm text-muted-foreground mt-2">
              Notification activities will appear here once you start sending notifications
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Clock className="h-5 w-5" />
          Recent Activity
        </CardTitle>
        <CardDescription>
          Latest notification events and activities
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {recentNotifications.map((notification) => {
            const TypeIcon = getTypeIcon(notification.type);
            const StatusIcon = getStatusIcon(notification.status);
            const statusColor = getStatusColor(notification.status);

            return (
              <div
                key={notification.id}
                className="flex items-start space-x-4 p-3 border rounded-lg hover:bg-muted/50 transition-colors"
              >
                <div className="flex-shrink-0">
                  <div className="p-2 bg-primary/10 rounded-md">
                    <TypeIcon className="h-4 w-4 text-primary" />
                  </div>
                </div>
                
                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between">
                    <h4 className="text-sm font-medium truncate">
                      {notification.title}
                    </h4>
                    <div className="flex items-center space-x-2 ml-2">
                      <Badge variant="secondary" className="text-xs">
                        {notification.type}
                      </Badge>
                      <StatusIcon className={`h-3 w-3 ${statusColor}`} />
                    </div>
                  </div>
                  
                  <p className="text-sm text-muted-foreground truncate mt-1">
                    {notification.message}
                  </p>
                  
                  <div className="flex items-center justify-between mt-2 text-xs text-muted-foreground">
                    <span>
                      {notification.recipientType === 'admin' ? 'Shop Admin' : notification.recipientType}
                      {notification.shopId && ` • Shop ${notification.shopId}`}
                    </span>
                    <span>
                      {formatDistanceToNow(new Date(notification.sentAt), { addSuffix: true })}
                    </span>
                  </div>
                </div>
              </div>
            );
          })}
          
          {recentNotifications.length > 0 && (
            <div className="text-center pt-4 border-t">
              <p className="text-xs text-muted-foreground">
                Showing {recentNotifications.length} most recent notifications
              </p>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
} 