/**
 * Get Subscription by ID Service
 */
const { Subscription } = require('../../models');
const { AppError, logError } = require('../../utils');

/**
 * Get subscription by ID
 * @param {string} subscriptionId - ID of the subscription
 * @returns {Promise<Object>} Found subscription
 */
const getSubscriptionById = async (subscriptionId) => {
  try {
    const subscription = await Subscription.findOne({ 
      subscriptionId, 
      isDeleted: false 
    });
    
    if (!subscription) {
      throw new AppError('Subscription not found', 404, 'subscription_not_found');
    }
    
    return subscription;
  } catch (error) {
    logError(`Failed to get subscription: ${subscriptionId}`, 'SubscriptionService', error);
    throw error;
  }
};

module.exports = getSubscriptionById;
