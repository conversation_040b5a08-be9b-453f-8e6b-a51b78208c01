import apiBridge from '@/lib/api/bridge';
import { ENDPOINTS } from '@/lib/api/contract';;
import { handleError, processApiResponse, validateRequiredFields } from '../baseService';

/**
 * Upgrade subscription from trial to paid plan
 * @param {Object} upgradeData - Upgrade data (planId/planType, paymentMethod, paymentDetails)
 * @returns {Promise<Object>} Updated subscription object
 */
async function upgradeFromTrial(upgradeData) {
  try {
    // Validate required fields
    const requiredFields = ['paymentMethod'];
    const validation = validateRequiredFields(upgradeData, requiredFields);
    
    if (!validation.isValid) {
      throw new Error(validation.message);
    }

    // Validate either planId or planType is provided
    if (!upgradeData.planId && !upgradeData.planType) {
      throw new Error('Either planId or planType is required');
    }

    const response = await apiBridge.post(`${ENDPOINTS.SUBSCRIPTIONS.BASE}/upgrade`, upgradeData, {
      clearCacheEndpoint: ENDPOINTS.SUBSCRIPTIONS.BASE
    });
    
    const result = processApiResponse(response, 'Subscription upgraded successfully');
    return result.subscription || result;
  } catch (error) {
    handleError(error, 'SubscriptionService.upgradeFromTrial', true);
    throw error;
  }
}

export default upgradeFromTrial; 
