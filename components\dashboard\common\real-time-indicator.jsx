import React, { useState, useEffect } from 'react';
import { Wifi, WifiOff, Refresh<PERSON><PERSON>, CheckCircle, AlertCircle } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { useRealTimeUpdates, useRealTimeUpdater } from '@/hooks/use-real-time-updates';

/**
 * Real-Time Status Indicator Component
 * Shows the current status of real-time updates and data freshness
 */
export function RealTimeIndicator({ dataType = 'general', className = '' }) {
  const [status, setStatus] = useState('connected');
  const [lastUpdate, setLastUpdate] = useState(null);
  const [updateCount, setUpdateCount] = useState(0);
  const { isDataStale } = useRealTimeUpdater();

  // Subscribe to real-time updates for this data type
  useRealTimeUpdates(dataType, (data, updateOptions) => {
    setLastUpdate(new Date());
    setUpdateCount(prev => prev + 1);
    setStatus('updated');
    
    // Reset status after animation
    setTimeout(() => {
      setStatus('connected');
    }, 2000);
  });

  // Check if data is stale
  useEffect(() => {
    const checkStaleData = () => {
      if (isDataStale(dataType, 300000)) { // 5 minutes
        setStatus('stale');
      }
    };

    const interval = setInterval(checkStaleData, 30000); // Check every 30 seconds
    return () => clearInterval(interval);
  }, [dataType, isDataStale]);

  const getStatusInfo = () => {
    switch (status) {
      case 'connected':
        return {
          icon: Wifi,
          color: 'bg-green-500',
          text: 'Real-time updates active',
          variant: 'default'
        };
      case 'updated':
        return {
          icon: RefreshCw,
          color: 'bg-blue-500',
          text: 'Data updated',
          variant: 'secondary'
        };
      case 'stale':
        return {
          icon: AlertCircle,
          color: 'bg-yellow-500',
          text: 'Data may be outdated',
          variant: 'outline'
        };
      case 'disconnected':
        return {
          icon: WifiOff,
          color: 'bg-red-500',
          text: 'Real-time updates unavailable',
          variant: 'destructive'
        };
      default:
        return {
          icon: Wifi,
          color: 'bg-gray-500',
          text: 'Unknown status',
          variant: 'outline'
        };
    }
  };

  const statusInfo = getStatusInfo();
  const Icon = statusInfo.icon;

  const formatLastUpdate = () => {
    if (!lastUpdate) return 'No recent updates';
    
    const now = new Date();
    const diff = now - lastUpdate;
    const minutes = Math.floor(diff / 60000);
    const seconds = Math.floor((diff % 60000) / 1000);
    
    if (minutes > 0) {
      return `Updated ${minutes}m ago`;
    } else {
      return `Updated ${seconds}s ago`;
    }
  };

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <div className={`flex items-center space-x-2 ${className}`}>
            <div className="relative">
              <Icon 
                className={`h-4 w-4 ${status === 'updated' ? 'animate-spin' : ''}`} 
              />
              <div 
                className={`absolute -top-1 -right-1 h-2 w-2 rounded-full ${statusInfo.color} ${
                  status === 'updated' ? 'animate-pulse' : ''
                }`}
              />
            </div>
            <Badge variant={statusInfo.variant} className="text-xs">
              Live
            </Badge>
            {updateCount > 0 && (
              <span className="text-xs text-muted-foreground">
                ({updateCount})
              </span>
            )}
          </div>
        </TooltipTrigger>
        <TooltipContent>
          <div className="text-sm">
            <p className="font-medium">{statusInfo.text}</p>
            <p className="text-muted-foreground">{formatLastUpdate()}</p>
            {updateCount > 0 && (
              <p className="text-muted-foreground">
                {updateCount} update{updateCount !== 1 ? 's' : ''} received
              </p>
            )}
          </div>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}

/**
 * Real-Time Status Panel Component
 * Shows detailed real-time update information for multiple data types
 */
export function RealTimeStatusPanel({ dataTypes = ['shops', 'users', 'plans'], className = '' }) {
  const [globalStats, setGlobalStats] = useState({
    totalUpdates: 0,
    lastGlobalUpdate: null,
    activeConnections: 0
  });

  // Subscribe to all data types
  dataTypes.forEach(dataType => {
    useRealTimeUpdates(dataType, () => {
      setGlobalStats(prev => ({
        ...prev,
        totalUpdates: prev.totalUpdates + 1,
        lastGlobalUpdate: new Date(),
        activeConnections: dataTypes.length
      }));
    });
  });

  return (
    <div className={`p-4 bg-card border rounded-lg ${className}`}>
      <div className="flex items-center justify-between mb-3">
        <h3 className="text-sm font-medium">Real-Time Status</h3>
        <CheckCircle className="h-4 w-4 text-green-500" />
      </div>
      
      <div className="space-y-2">
        {dataTypes.map(dataType => (
          <div key={dataType} className="flex items-center justify-between">
            <span className="text-sm capitalize">{dataType}</span>
            <RealTimeIndicator dataType={dataType} />
          </div>
        ))}
      </div>
      
      <div className="mt-3 pt-3 border-t">
        <div className="flex items-center justify-between text-xs text-muted-foreground">
          <span>Total Updates: {globalStats.totalUpdates}</span>
          <span>
            {globalStats.lastGlobalUpdate 
              ? `Last: ${globalStats.lastGlobalUpdate.toLocaleTimeString()}`
              : 'No updates yet'
            }
          </span>
        </div>
      </div>
    </div>
  );
}

export default RealTimeIndicator; 