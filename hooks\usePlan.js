"use client";

import { useState, useCallback, useEffect, useRef } from 'react';
import { toast } from 'sonner';
import PlanService from '@/lib/services/plan';

/**
 * Single Plan Hook
 * 
 * Specialized hook for individual plan operations
 * Handles plan by ID, plan by type, and real-time updates
 */
export function usePlan(planId = null, options = {}) {
  const {
    autoFetch = true,
    showToastMessages = true,
    fetchDelay = 300,
    enableCache = true,
    onPlanChange,
    onError
  } = options;

  // Main plan state
  const [plan, setPlan] = useState(null);
  const [loading, setLoading] = useState(!!planId);
  const [error, setError] = useState(null);
  const [lastUpdated, setLastUpdated] = useState(null);

  // Use refs to prevent duplicate requests and track state
  const isLoadingRef = useRef(false);
  const isMountedRef = useRef(true);
  const currentPlanIdRef = useRef(planId);

  // Update current plan ID ref
  currentPlanIdRef.current = planId;

  /**
   * Fetch plan by ID
   */
  const fetchPlanById = useCallback(async (id, showLoadingToast = false) => {
    if (!id) {
      setPlan(null);
      setError(null);
      setLoading(false);
      return null;
    }

    // Prevent duplicate requests for the same plan
    if (isLoadingRef.current && currentPlanIdRef.current === id) {
      console.log('[usePlan] Request already in progress for plan:', id);
      return;
    }

    try {
      isLoadingRef.current = true;
      setLoading(true);
      setError(null);

      if (showLoadingToast && showToastMessages) {
        toast.info('Loading plan...');
      }

      console.log('[usePlan] Fetching plan by ID:', id);

      // FIXED: Force fresh backend request by skipping cache
      const response = await PlanService.getPlanById(id, { skipCache: true });

      if (!isMountedRef.current) return null;

      if (response.success) {
        const planData = response.data;
        setPlan(planData);
        setLastUpdated(new Date());
        
        if (showLoadingToast && showToastMessages) {
          toast.success('Plan loaded successfully');
        }

        // Trigger callback if provided
        if (onPlanChange) {
          onPlanChange(planData);
        }

        console.log('[usePlan] Plan loaded successfully:', planData);
        return planData;
      } else {
        throw new Error(response.message || 'Plan not found');
      }
    } catch (err) {
      console.error('[usePlan] Error fetching plan by ID:', err);
      
      if (!isMountedRef.current) return null;

      const errorMessage = err.message || 'Failed to load plan';
      setError(errorMessage);

      if (showToastMessages && !err.message?.includes('throttled')) {
        toast.error(errorMessage);
      }

      // Trigger error callback if provided
      if (onError) {
        onError(err);
      }

      return null;
    } finally {
      if (isMountedRef.current) {
        setLoading(false);
        isLoadingRef.current = false;
      }
    }
  }, [showToastMessages, onPlanChange, onError]);

  /**
   * Fetch plan by type (trial, monthly, yearly)
   */
  const fetchPlanByType = useCallback(async (planType, showLoadingToast = false) => {
    if (!planType) {
      throw new Error('Plan type is required');
    }

    try {
      setLoading(true);
      setError(null);

      if (showLoadingToast && showToastMessages) {
        toast.info(`Loading ${planType} plan...`);
      }

      console.log('[usePlan] Fetching plan by type:', planType);

      const response = await PlanService.getPlanByType(planType);

      if (!isMountedRef.current) return null;

      if (response.success) {
        const planData = response.data;
        setPlan(planData);
        setLastUpdated(new Date());
        
        if (showLoadingToast && showToastMessages) {
          toast.success(`${planType} plan loaded successfully`);
        }

        // Trigger callback if provided
        if (onPlanChange) {
          onPlanChange(planData);
        }

        console.log('[usePlan] Plan by type loaded successfully:', planData);
        return planData;
      } else {
        throw new Error(response.message || `No ${planType} plan found`);
      }
    } catch (err) {
      console.error('[usePlan] Error fetching plan by type:', err);
      
      if (!isMountedRef.current) return null;

      const errorMessage = err.message || `Failed to load ${planType} plan`;
      setError(errorMessage);

      if (showToastMessages && !err.message?.includes('throttled')) {
        toast.error(errorMessage);
      }

      // Trigger error callback if provided
      if (onError) {
        onError(err);
      }

      return null;
    } finally {
      if (isMountedRef.current) {
        setLoading(false);
      }
    }
  }, [showToastMessages, onPlanChange, onError]);

  /**
   * Update current plan
   */
  const updatePlan = useCallback(async (updateData) => {
    if (!plan?.planId) {
      throw new Error('No plan loaded to update or plan missing planId');
    }

    try {
      setLoading(true);
      
      if (showToastMessages) {
        toast.info('Updating plan...');
      }

      // FIXED: Always use planId field (custom ID), never _id (MongoDB ID)
      const planId = plan.planId;
      console.log('[usePlan] Updating plan:', planId, updateData);

      const response = await PlanService.updatePlan(planId, updateData);

      if (response.success) {
        // Update local state with new data
        const updatedPlan = { ...plan, ...updateData, ...response.data };
        setPlan(updatedPlan);
        setLastUpdated(new Date());
        
        if (showToastMessages) {
          toast.success('Plan updated successfully');
        }

        // Trigger callback if provided
        if (onPlanChange) {
          onPlanChange(updatedPlan);
        }

        return updatedPlan;
      } else {
        throw new Error(response.message || 'Failed to update plan');
      }
    } catch (err) {
      console.error('[usePlan] Error updating plan:', err);
      
      if (showToastMessages) {
        toast.error(err.message || 'Failed to update plan');
      }
      
      throw err;
    } finally {
      setLoading(false);
    }
  }, [plan, showToastMessages, onPlanChange]);

  /**
   * Toggle current plan status
   */
  const toggleStatus = useCallback(async (isActive) => {
    if (!plan?.planId && !plan?._id) {
      throw new Error('No plan loaded to toggle');
    }

    try {
      setLoading(true);
      
      const statusText = isActive ? 'activating' : 'deactivating';
      if (showToastMessages) {
        toast.info(`${statusText.charAt(0).toUpperCase() + statusText.slice(1)} plan...`);
      }

      const planId = plan.planId || plan._id;
      console.log('[usePlan] Toggling plan status:', planId, isActive);

      const response = await PlanService.togglePlanStatus(planId, isActive);

      if (response.success) {
        // Update local state
        const updatedPlan = { ...plan, isActive };
        setPlan(updatedPlan);
        setLastUpdated(new Date());
        
        if (showToastMessages) {
          toast.success(`Plan ${isActive ? 'activated' : 'deactivated'} successfully`);
        }

        // Trigger callback if provided
        if (onPlanChange) {
          onPlanChange(updatedPlan);
        }

        return updatedPlan;
      } else {
        throw new Error(response.message || 'Failed to toggle plan status');
      }
    } catch (err) {
      console.error('[usePlan] Error toggling plan status:', err);
      
      if (showToastMessages) {
        toast.error(err.message || 'Failed to toggle plan status');
      }
      
      throw err;
    } finally {
      setLoading(false);
    }
  }, [plan, showToastMessages, onPlanChange]);

  /**
   * Delete current plan
   */
  const deletePlan = useCallback(async () => {
    if (!plan?.planId && !plan?._id) {
      throw new Error('No plan loaded to delete');
    }

    try {
      setLoading(true);
      
      if (showToastMessages) {
        toast.info('Deleting plan...');
      }

      const planId = plan.planId || plan._id;
      console.log('[usePlan] Deleting plan:', planId);

      const response = await PlanService.deletePlan(planId);

      if (response.success) {
        // Clear local state after deletion
        setPlan(null);
        setLastUpdated(new Date());
        
        if (showToastMessages) {
          toast.success('Plan deleted successfully');
        }

        // Trigger callback if provided
        if (onPlanChange) {
          onPlanChange(null);
        }

        return true;
      } else {
        throw new Error(response.message || 'Failed to delete plan');
      }
    } catch (err) {
      console.error('[usePlan] Error deleting plan:', err);
      
      if (showToastMessages) {
        toast.error(err.message || 'Failed to delete plan');
      }
      
      throw err;
    } finally {
      setLoading(false);
    }
  }, [plan, showToastMessages, onPlanChange]);

  /**
   * Duplicate current plan
   */
  const duplicatePlan = useCallback(async (newName) => {
    if (!plan?.planId && !plan?._id) {
      throw new Error('No plan loaded to duplicate');
    }

    if (!newName) {
      throw new Error('New plan name is required');
    }

    try {
      setLoading(true);
      
      if (showToastMessages) {
        toast.info('Duplicating plan...');
      }

      const planId = plan.planId || plan._id;
      console.log('[usePlan] Duplicating plan:', planId, newName);

      const response = await PlanService.duplicatePlan(planId, newName);

      if (response.success) {
        if (showToastMessages) {
          toast.success(`Plan duplicated as "${newName}"`);
        }

        return response.data;
      } else {
        throw new Error(response.message || 'Failed to duplicate plan');
      }
    } catch (err) {
      console.error('[usePlan] Error duplicating plan:', err);
      
      if (showToastMessages) {
        toast.error(err.message || 'Failed to duplicate plan');
      }
      
      throw err;
    } finally {
      setLoading(false);
    }
  }, [plan, showToastMessages]);

  /**
   * Refresh current plan data
   */
  const refreshPlan = useCallback((showToast = false) => {
    if (planId) {
      return fetchPlanById(planId, showToast);
    }
    return Promise.resolve(null);
  }, [planId, fetchPlanById]);

  /**
   * Load a different plan by ID
   */
  const loadPlan = useCallback((newPlanId, showToast = false) => {
    return fetchPlanById(newPlanId, showToast);
  }, [fetchPlanById]);

  /**
   * Clear current plan
   */
  const clearPlan = useCallback(() => {
    setPlan(null);
    setError(null);
    setLastUpdated(null);
  }, []);

  // Auto-fetch plan by ID on mount or when planId changes
  useEffect(() => {
    if (!autoFetch || !planId) return;

    const timer = setTimeout(() => {
      fetchPlanById(planId);
    }, fetchDelay);

    return () => clearTimeout(timer);
  }, [planId, autoFetch, fetchDelay, fetchPlanById]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      isMountedRef.current = false;
    };
  }, []);

  return {
    // Current plan data
    plan,
    loading,
    error,
    lastUpdated,

    // Computed properties
    isActive: plan?.isActive || false,
    planType: plan?.type || null,
    planName: plan?.displayName || plan?.name || null,

    // Fetch operations
    fetchPlanById,
    fetchPlanByType,
    loadPlan,
    refreshPlan,

    // Mutation operations
    updatePlan,
    toggleStatus,
    deletePlan,
    duplicatePlan,

    // Utility operations
    clearPlan,

    // Aliases for compatibility
    refetch: refreshPlan,
    refresh: refreshPlan
  };
} 