/**
 * Create a new plan
 * Allows SuperAdmin to dynamically control features
 */
const { Plan } = require('../../models');
const { logSuccess, logError } = require('../../utils');

/**
 * Create a new plan
 * Respects SuperAdmin's feature choices instead of forcing all features
 * @param {Object} planData - Plan details
 * @param {Object} options - Additional options
 * @returns {Promise<Object>} Created plan
 */
const createPlan = async (planData, options = {}) => {
  try {
    // Only apply default features if none provided by SuperAdmin
    let finalFeatures = planData.features;
    
    if (!finalFeatures) {
      // Default features only used when SuperAdmin doesn't specify
      finalFeatures = {
        debtTracking: true,
        customerPayments: true,
        smsReminders: true,
        smartRiskScore: true,
        businessDashboard: true,
        exportReports: true,
        customerProfiles: true,
        offlineSupport: true
      };
      logSuccess('Applied default features (all enabled)', 'PlanService');
    } else {
      logSuccess(`Using SuperAdmin defined features: ${Object.keys(finalFeatures).filter(key => finalFeatures[key]).join(', ')}`, 'PlanService');
    }

    // Create the plan with SuperAdmin's feature choices
    const plan = new Plan({
      ...planData,
      features: finalFeatures,
      createdBy: options.actorId || 'system'
    });
    
    const savedPlan = await plan.save();
    logSuccess(`Created plan: ${savedPlan.planId}`, 'PlanService');
    
    return savedPlan;
  } catch (error) {
    logError('Failed to create plan', 'PlanService', error);
    throw error;
  }
};

module.exports = createPlan;
