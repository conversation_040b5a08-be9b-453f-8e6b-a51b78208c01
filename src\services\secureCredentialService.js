/**
 * Secure Credential Service
 * Handles encryption and decryption of sensitive credentials
 */
const crypto = require('crypto');
const { AppError, logError } = require('../utils');

/**
 * Service for securely handling sensitive credentials like API keys
 */
const SecureCredentialService = {
  /**
   * Encrypt a sensitive value
   * @param {string} plaintext - Value to encrypt
   * @param {string} shopId - Shop ID for shop-specific encryption (null for global)
   * @returns {string} Encrypted value (IV:EncryptedData format)
   */
  encrypt: (plaintext, shopId = null) => {
    try {
      if (!plaintext) return null;
      
      const masterKey = process.env.CREDENTIAL_ENCRYPTION_KEY;
      if (!masterKey) throw new AppError('Credential encryption key not configured', 500);
      
      // Create shop-specific derived key by combining master key with shopId
      let shopSpecificData = masterKey;
      if (shopId) {
        // Include shop salt for additional security
        const shopSalt = process.env.SHOP_KEY_SALT || 'deyncare_shop_salt';
        shopSpecificData = `${masterKey}_${shopId}_${shopSalt}`;
      }
      
      // Derive a proper-length key using SHA-256 with shop-specific input
      const derivedKey = crypto.createHash('sha256').update(shopSpecificData).digest();
      const iv = crypto.randomBytes(16); // 16 bytes for AES
      const cipher = crypto.createCipheriv('aes-256-cbc', derivedKey, iv);
      
      let encrypted = cipher.update(plaintext, 'utf8', 'base64');
      encrypted += cipher.final('base64');
      
      // Return IV:EncryptedData format
      return `${iv.toString('hex')}:${encrypted}`;
    } catch (error) {
      logError(`Failed to encrypt value: ${error.message}`, 'SecureCredentialService', error);
      throw new AppError('Failed to encrypt value', 500);
    }
  },
  
  /**
   * Decrypt an encrypted value
   * @param {string} encryptedValue - Encrypted value (IV:EncryptedData format)
   * @param {string} shopId - Shop ID for shop-specific decryption (null for global)
   * @returns {string} Decrypted plaintext
   */
  decrypt: (encryptedValue, shopId = null) => {
    try {
      if (!encryptedValue || !encryptedValue.includes(':')) return null;
      
      const masterKey = process.env.CREDENTIAL_ENCRYPTION_KEY;
      if (!masterKey) throw new AppError('Credential encryption key not configured', 500);
      
      // Create shop-specific derived key by combining master key with shopId
      let shopSpecificData = masterKey;
      if (shopId) {
        // Include shop salt for additional security
        const shopSalt = process.env.SHOP_KEY_SALT || 'deyncare_shop_salt';
        shopSpecificData = `${masterKey}_${shopId}_${shopSalt}`;
      }
      
      // Derive key consistently with encryption
      const derivedKey = crypto.createHash('sha256').update(shopSpecificData).digest();
      
      const [ivHex, encryptedData] = encryptedValue.split(':');
      const iv = Buffer.from(ivHex, 'hex');
      const decipher = crypto.createDecipheriv('aes-256-cbc', derivedKey, iv);
      
      let decrypted = decipher.update(encryptedData, 'base64', 'utf8');
      decrypted += decipher.final('utf8');
      
      return decrypted;
    } catch (error) {
      logError(`Failed to decrypt value: ${error.message}`, 'SecureCredentialService', error);
      throw new AppError('Failed to decrypt value', 500);
    }
  },
  
  /**
   * Encrypt all credential fields in an object
   * @param {Object} credentials - Object with credential fields
   * @param {string} shopId - Shop ID for shop-specific encryption (null for global)
   * @returns {Object} Object with encrypted credentials
   */
  encryptCredentials: (credentials, shopId = null) => {
    if (!credentials || typeof credentials !== 'object') return null;
    
    const encrypted = {};
    
    Object.keys(credentials).forEach(key => {
      if (credentials[key]) {
        encrypted[key] = SecureCredentialService.encrypt(credentials[key], shopId);
      }
    });
    
    return encrypted;
  },
  
  /**
   * Decrypt all credential fields in an object
   * @param {Object} encryptedCredentials - Object with encrypted credential fields
   * @param {string} shopId - Shop ID for shop-specific decryption (null for global)
   * @returns {Object} Object with decrypted credentials
   */
  decryptCredentials: (encryptedCredentials, shopId = null) => {
    if (!encryptedCredentials || typeof encryptedCredentials !== 'object') return null;
    
    const decrypted = {};
    
    Object.keys(encryptedCredentials).forEach(key => {
      if (encryptedCredentials[key]) {
        decrypted[key] = SecureCredentialService.decrypt(encryptedCredentials[key], shopId);
      }
    });
    
    return decrypted;
  }
};

module.exports = SecureCredentialService;
