import React from 'react';
import { Users, Plus, Download } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';

export const UsersHeader = ({ onAddUserClick, onExportClick }) => {
  return (
    <div className="flex items-center justify-between mb-6">
      <div className="flex items-center gap-2">
        <Users className="h-6 w-6" />
        <div>
          <h1 className="text-2xl font-bold tracking-tight">User Management</h1>
          <p className="text-muted-foreground">
            Create and manage users in the DeynCare system
          </p>
        </div>
      </div>
      
      <div className="flex items-center gap-2">
        <Button variant="outline" onClick={onExportClick}>
          <Download className="mr-2 h-4 w-4" />
          Export Users
        </Button>
        <Button onClick={onAddUserClick}>
          <Plus className="mr-2 h-4 w-4" />
          Add New User
        </Button>
      </div>
    </div>
  );
};

export default UsersHeader;
