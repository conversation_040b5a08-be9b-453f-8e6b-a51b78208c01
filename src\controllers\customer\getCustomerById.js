const { Customer, Debt, Payment } = require('../../models');
const { AppError, logError } = require('../../utils');

/**
 * Get Single Customer with Full Profile
 * GET /api/customers/:customerId
 */
const getCustomerById = async (req, res, next) => {
  try {
    const { customerId } = req.params;
    const shopId = req.user.shopId;
    
    // Check if this is a mobile app request (for enhanced risk display)
    const isMobileRequest = req.headers['user-agent']?.includes('Flutter') || 
                           req.query.mobile === 'true' ||
                           req.headers['x-platform'] === 'mobile';

    // Find customer with comprehensive data
    const customer = await Customer.findOne({ 
      customerId, 
      shopId, 
      isDeleted: false 
    }).lean();

    if (!customer) {
      return res.status(404).json({
        success: false,
        message: 'Customer not found'
      });
    }

    // Get customer's debt history with detailed information
    const debts = await Debt.find({ 
      customerId, 
      shopId, 
      isDeleted: false 
    }).sort({ createdAt: -1 }).lean();

    // Get customer's payment history
    const payments = await Payment.find({
      customerId,
      shopId,
      paymentContext: 'debt',
      isDeleted: false
    }).sort({ paymentDate: -1 }).lean();

    // Calculate comprehensive statistics
    const stats = {
      totalDebts: debts.length,
      activeDebts: debts.filter(d => d.OutstandingDebt > 0).length,
      paidDebts: debts.filter(d => d.OutstandingDebt <= 0 && d.PaidAmount > 0).length,
      
      financials: {
        totalBorrowed: debts.reduce((sum, d) => sum + d.DebtAmount, 0),
        totalOutstanding: debts.reduce((sum, d) => sum + d.OutstandingDebt, 0),
        totalPaid: debts.reduce((sum, d) => sum + (d.PaidAmount || 0), 0),
        averageDebtAmount: debts.length > 0 ? debts.reduce((sum, d) => sum + d.DebtAmount, 0) / debts.length : 0,
        collectionRate: 0
      },
      
      paymentBehavior: {
        totalPayments: payments.length,
        averagePaymentAmount: payments.length > 0 ? payments.reduce((sum, p) => sum + p.amount, 0) / payments.length : 0,
        onTimePayments: debts.filter(d => d.IsOnTime).length,
        latePayments: debts.filter(d => !d.IsOnTime && d.PaymentDelay > 0).length,
        averagePaymentDelay: 0,
        maxPaymentDelay: Math.max(...debts.map(d => d.PaymentDelay || 0), 0)
      },
      
      riskAnalysis: {
        currentRiskLevel: customer.riskProfile?.currentRiskLevel || 'Not Assessed',
        riskScore: customer.riskProfile?.riskScore || 0,
        lastAssessment: customer.riskProfile?.lastAssessment,
        assessmentHistory: customer.riskProfile?.assessmentCount || 0,
        riskFactors: []
      }
    };

    // Calculate collection rate
    if (stats.financials.totalBorrowed > 0) {
      stats.financials.collectionRate = Math.round((stats.financials.totalPaid / stats.financials.totalBorrowed) * 100);
    }

    // Calculate average payment delay
    const delayedDebts = debts.filter(d => d.PaymentDelay > 0);
    if (delayedDebts.length > 0) {
      stats.paymentBehavior.averagePaymentDelay = Math.round(
        delayedDebts.reduce((sum, d) => sum + d.PaymentDelay, 0) / delayedDebts.length
      );
    }

    // Identify risk factors
    if (customer.CustomerType === 'New') {
      stats.riskAnalysis.riskFactors.push('New customer');
    }
    if (stats.paymentBehavior.averagePaymentDelay > 7) {
      stats.riskAnalysis.riskFactors.push('History of late payments');
    }
    if (stats.financials.collectionRate < 80) {
      stats.riskAnalysis.riskFactors.push('Poor payment history');
    }
    if (stats.financials.totalOutstanding > 1000) {
      stats.riskAnalysis.riskFactors.push('High outstanding balance');
    }

    // 📱 MOBILE-FRIENDLY RISK INTERPRETATION
    const riskLevel = customer.riskProfile?.currentRiskLevel || 'Active Debt';
    const riskScore = customer.riskProfile?.riskScore || 0;
    
    let riskInterpretation = null;
    if (isMobileRequest) {
      switch (riskLevel) {
        case 'Low Risk':
          riskInterpretation = {
            status: 'Excellent',
            emoji: '✅',
            color: '#10B981', // Green
            backgroundColor: '#ECFDF5',
            message: 'Great payment history! Keep it up.',
            description: 'This customer has shown excellent payment behavior and is very likely to pay on time.',
            actionable: 'Consider offering credit limit increases or loyalty rewards.',
            trustLevel: 'High Trust',
            recommendation: 'Approve new debts up to higher limits'
          };
          break;
        
        case 'Medium Risk':
          riskInterpretation = {
            status: 'Good',
            emoji: '⚠️',
            color: '#F59E0B', // Yellow
            backgroundColor: '#FFFBEB',
            message: 'Generally reliable with room for improvement.',
            description: 'This customer has moderate payment behavior. Monitor closely.',
            actionable: 'Send friendly payment reminders and offer payment plans.',
            trustLevel: 'Moderate Trust',
            recommendation: 'Approve with standard terms and monitoring'
          };
          break;
        
        case 'High Risk':
          riskInterpretation = {
            status: 'Needs Attention',
            emoji: '🚨',
            color: '#EF4444', // Red
            backgroundColor: '#FEF2F2',
            message: 'Payment delays detected. Follow up required.',
            description: 'This customer has shown concerning payment patterns. Immediate attention needed.',
            actionable: 'Contact customer immediately. Consider payment restrictions.',
            trustLevel: 'Low Trust',
            recommendation: 'Require approval for new debts or deny'
          };
          break;
        
        default: // Active Debt
          riskInterpretation = {
            status: 'Monitoring',
            emoji: '📊',
            color: '#6B7280', // Gray
            backgroundColor: '#F9FAFB',
            message: 'New debt created. Evaluation in progress.',
            description: 'Customer has active debt. ML evaluation will begin after due date.',
            actionable: 'Monitor payment behavior. No action needed yet.',
            trustLevel: 'Under Review',
            recommendation: 'Continue monitoring until first evaluation'
          };
      }
    }

    // Format debt history
    const debtHistory = debts.map(debt => ({
      debtId: debt.debtId,
      amount: debt.DebtAmount,
      outstandingAmount: debt.OutstandingDebt,
      paidAmount: debt.PaidAmount || 0,
      paymentRatio: Math.round(debt.DebtPaidRatio * 100),
      dueDate: debt.DueDate,
      riskLevel: debt.RiskLevel,
      paymentDelay: debt.PaymentDelay,
      isOnTime: debt.IsOnTime,
      status: debt.OutstandingDebt <= 0 ? 'Paid' : (new Date() > debt.DueDate ? 'Overdue' : 'Active'),
      createdAt: debt.createdAt
    }));

    // Format payment history
    const paymentHistory = payments.slice(0, 10).map(payment => ({
      paymentId: payment.paymentId,
      debtId: payment.debtId,
      amount: payment.amount,
      paymentDate: payment.paymentDate,
      paymentMethod: payment.paymentMethod,
      isOnTime: payment.IsOnTime,
      paymentDelay: payment.PaymentDelay,
      notes: payment.notes
    }));

    // Recent activity (last 30 days)
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
    
    const recentPayments = payments.filter(p => new Date(p.paymentDate) >= thirtyDaysAgo);
    const recentDebts = debts.filter(d => new Date(d.createdAt) >= thirtyDaysAgo);

    const recentActivity = {
      newDebtsLast30Days: recentDebts.length,
      paymentsLast30Days: recentPayments.length,
      amountPaidLast30Days: recentPayments.reduce((sum, p) => sum + p.amount, 0),
      lastPaymentDate: payments.length > 0 ? payments[0].paymentDate : null,
      lastDebtDate: debts.length > 0 ? debts[0].createdAt : null
    };

    // 📱 MOBILE APP UI HINTS
    let uiHints = null;
    if (isMobileRequest) {
      uiHints = {
        showRiskBadge: riskLevel !== 'Active Debt',
        allowNewDebt: riskLevel !== 'High Risk',
        requireApproval: riskLevel === 'High Risk',
        suggestedCreditLimit: riskLevel === 'Low Risk' ? 'Increase' : 
                             riskLevel === 'Medium Risk' ? 'Maintain' : 'Reduce',
        notificationPriority: riskLevel === 'High Risk' ? 'high' : 'normal',
        displayWarning: riskLevel === 'High Risk',
        enableAutoApproval: riskLevel === 'Low Risk',
        maxRecommendedDebt: riskLevel === 'Low Risk' ? stats.financials.averageDebtAmount * 1.5 :
                           riskLevel === 'Medium Risk' ? stats.financials.averageDebtAmount :
                           stats.financials.averageDebtAmount * 0.5
      };
    }

    // Build response based on request type
    const baseResponse = {
      success: true,
      message: 'Customer profile retrieved successfully',
      data: {
        customer: {
          customerId: customer.customerId,
          customerName: customer.CustomerName,
          phone: customer.phone,
          customerType: customer.CustomerType,
          createdAt: customer.createdAt,
          updatedAt: customer.updatedAt
        },
        
        statistics: stats,
        
        debtHistory: {
          total: debtHistory.length,
          debts: debtHistory
        },
        
        paymentHistory: {
          total: payments.length,
          recent: paymentHistory,
          note: payments.length > 10 ? `Showing 10 most recent payments out of ${payments.length} total` : null
        },
        
        recentActivity,
        
        riskProfile: customer.riskProfile || {
          currentRiskLevel: 'Not Assessed',
          riskScore: 0,
          lastAssessment: null,
          assessmentCount: 0
        }
      }
    };

    // Add mobile-specific enhancements
    if (isMobileRequest && riskInterpretation) {
      baseResponse.data.riskInterpretation = riskInterpretation;
      baseResponse.data.uiHints = uiHints;
      baseResponse.data.mobileOptimized = true;
      
      // Add quick summary for mobile
      baseResponse.data.quickSummary = {
        riskLevel: riskLevel,
        riskScore: riskScore,
        trustLevel: riskInterpretation.trustLevel,
        recommendation: riskInterpretation.recommendation,
        totalOutstanding: stats.financials.totalOutstanding,
        paymentBehaviorScore: stats.financials.collectionRate,
        activeDebts: stats.activeDebts
      };
    }

    res.json(baseResponse);

  } catch (error) {
    logError('Failed to get customer profile', 'GetCustomerById', error);
    return next(new AppError('Failed to retrieve customer profile', 500));
  }
};

module.exports = getCustomerById; 