/**
 * Update Subscription Service
 * Updates specific fields of a subscription
 */
const { Subscription } = require('../../models');
const { logError, logSuccess } = require('../../utils');

/**
 * Update subscription with provided data
 * @param {String} subscriptionId - The subscription ID to update
 * @param {Object} updateData - The data to update
 * @param {Object} actor - Actor performing the action (optional)
 * @returns {Promise<Object>} Updated subscription
 */
const updateSubscription = async (subscriptionId, updateData, actor = {}) => {
  try {
    const { actorId = 'system', actorRole = 'system' } = actor;
    
    // Add meta information to update
    const finalUpdateData = {
      ...updateData,
      'meta.lastModifiedBy': actorId,
      'meta.lastModifiedAt': new Date()
    };
    
    // Update the subscription
    const updatedSubscription = await Subscription.findByIdAndUpdate(
      subscriptionId,
      { $set: finalUpdateData },
      { new: true, runValidators: true }
    );
    
    if (!updatedSubscription) {
      throw new Error(`Subscription not found: ${subscriptionId}`);
    }
    
    logSuccess(`Successfully updated subscription: ${subscriptionId}`, 'SubscriptionService');
    
    return updatedSubscription;
  } catch (error) {
    logError('Failed to update subscription', 'SubscriptionService', error);
    throw error;
  }
};

module.exports = updateSubscription; 