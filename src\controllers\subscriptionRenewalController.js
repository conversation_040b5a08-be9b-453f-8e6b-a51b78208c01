/**
 * Subscription Renewal Controller
 * Handles subscription renewal using existing payment methods
 */
const mongoose = require('mongoose');
const EVCPaymentService = require('../services/evcPaymentService');
const SubscriptionService = require('../services/SubscriptionService');
const ShopService = require('../services/shopService');
const { Plan, Subscription, Payment } = require('../models');
const { 
  AppError,
  logInfo,
  logSuccess,
  logError,
  idGenerator
} = require('../utils');

/**
 * Process subscription renewal payment
 * POST /api/subscriptions/renew
 */
const processRenewalPayment = async (req, res, next) => {
  const session = await mongoose.startSession();
  session.startTransaction();

  try {
    const userId = req.user.userId;
    const { planType, paymentMethod, phoneNumber } = req.body;

    // Validate required fields
    if (!planType || !paymentMethod) {
      throw new AppError('Plan type and payment method are required', 400, 'missing_parameters');
    }

    // Get current subscription
    const shopId = req.user.shopId;
    const currentSubscription = await SubscriptionService.getCurrentSubscriptionByShop(shopId);
    
    if (!currentSubscription) {
      throw new AppError('No subscription found for renewal', 404, 'subscription_not_found');
    }

    // Get shop details
    const shop = await ShopService.getShopById(shopId, { session });
    if (!shop) {
      throw new AppError('Shop not found', 404, 'shop_not_found');
    }

    // Get plan details and pricing
    const plan = await Plan.findOne({ type: planType });
    if (!plan) {
      throw new AppError(`Subscription plan '${planType}' not found`, 404, 'plan_not_found');
    }

    const planPrice = plan.pricing.basePrice;
    let paymentSuccess = false;
    let paymentDetails = {};

    // Process payment based on method
    if (paymentMethod === 'EVC Plus') {
      // Use user's registered phone or provided phone
      const evcPhoneNumber = phoneNumber || req.user.phone;
      
      if (!evcPhoneNumber) {
        throw new AppError('Phone number is required for EVC Plus payments', 400, 'missing_phone');
      }

      const paymentData = {
        phone: evcPhoneNumber,
        amount: planPrice,
        reference: `renewal_${Date.now()}_${userId.substring(0, 5)}`,
        description: `${shop.shopName} subscription renewal: ${planType} plan`,
        shopName: shop.shopName,
        shopId: shop.shopId
      };

      logInfo(`Initiating EVC Plus renewal payment of $${planPrice} for ${evcPhoneNumber}`, 'SubscriptionRenewalController');

      try {
        const evcResponse = await EVCPaymentService.payByWaafiPay(paymentData);

        if (evcResponse && evcResponse.success) {
          paymentSuccess = true;
          paymentDetails = {
            transactionId: evcResponse.transactionId,
            phoneNumber: evcPhoneNumber,
            method: 'evc_plus'
          };
          logSuccess(`EVC Plus renewal payment successful for user ${userId}`, 'SubscriptionRenewalController');
        } else {
          throw new AppError(
            evcResponse?.responseMessage || 'EVC Plus payment failed',
            402,
            'evc_payment_declined'
          );
        }
      } catch (paymentError) {
        logError(`EVC Plus renewal payment failed for user ${userId}: ${paymentError.message}`, 'SubscriptionRenewalController');
        throw paymentError;
      }
    } else if (['offline'].includes(paymentMethod)) {
      // For offline payments, mark as pending approval
      paymentSuccess = false; // Will require admin approval
      paymentDetails = {
        method: paymentMethod.toLowerCase(),
        status: 'pending_approval',
        notes: 'Renewal payment submitted for approval'
      };
      logInfo(`Offline renewal payment submitted for approval: ${paymentMethod}`, 'SubscriptionRenewalController');
    } else {
      throw new AppError(`Payment method '${paymentMethod}' is not supported`, 400, 'unsupported_payment_method');
    }

    // Create payment record
    const paymentId = await idGenerator.generatePaymentId();
    const paymentRecord = new Payment({
      paymentId,
      shopId: shop.shopId,
      customerId: userId,
      customerName: req.user.fullName,
      paymentContext: 'subscription_renewal',
      subscriptionId: currentSubscription._id,
      amount: planPrice,
      originalAmount: planPrice,
      paymentDate: new Date(),
      method: paymentDetails.method,
      paymentType: paymentMethod === 'EVC Plus' ? 'online' : 'offline',
      referenceNumber: paymentDetails.transactionId || paymentId,
      status: paymentSuccess ? 'confirmed' : 'pending',
      notes: `${planType} subscription renewal payment`,
      recordedBy: userId,
      recordedFromIp: req.ip,
      sessionType: 'online'
    });

    await paymentRecord.save({ session });

    // If payment successful, immediately renew subscription
    if (paymentSuccess) {
      await SubscriptionService.renewSubscription(
        currentSubscription._id,
        {
          paymentMethod: paymentDetails.method,
          transactionId: paymentRecord._id,
          amount: planPrice,
          currency: 'USD'
        },
        { actorId: userId, actorRole: req.user.role }
      );

      // Remove access restrictions
      await ShopService.updateShopAccess(shopId, {
        'access.isActive': true,
        'access.restrictedAt': null,
        'access.restrictedBy': null
      });

      logSuccess(`Subscription renewed successfully for shop: ${shopId}`, 'SubscriptionRenewalController');
    }

    await session.commitTransaction();

    res.json({
      success: true,
      message: paymentSuccess ? 'Subscription renewed successfully' : 'Renewal payment submitted for approval',
      data: {
        paymentId: paymentRecord.paymentId,
        subscriptionId: currentSubscription._id,
        planType,
        amount: planPrice,
        currency: 'USD',
        paymentMethod,
        status: paymentSuccess ? 'completed' : 'pending_approval',
        accessRestored: paymentSuccess,
        ...(paymentSuccess && { 
          newEndDate: new Date(Date.now() + (planType === 'yearly' ? 365 : 30) * 24 * 60 * 60 * 1000) 
        })
      }
    });

  } catch (error) {
    await session.abortTransaction();
    logError('Failed to process subscription renewal payment', 'SubscriptionRenewalController', error);
    next(error);
  } finally {
    session.endSession();
  }
};

/**
 * Get available payment methods for renewal
 * GET /api/subscriptions/payment-methods
 */
const getAvailablePaymentMethods = async (req, res, next) => {
  try {
    const { Setting } = require('../models');
    
    // Get allowed payment methods from settings
    const allowedMethods = await Setting.findOne({
      key: 'payment_methods_available',
      shopId: null // Global setting
    });

    // Check if online/offline payments are enabled
    const onlineEnabled = await Setting.findOne({
      key: 'enable_online_payment',
      shopId: null
    });

    const offlineEnabled = await Setting.findOne({
      key: 'enable_offline_payment',
      shopId: null
    });

    let availableMethods = [];

    if (allowedMethods && allowedMethods.value) {
      availableMethods = allowedMethods.value.filter(method => {
        const isOnlineMethod = ['EVC Plus', 'Card', 'Mobile Money'].includes(method);
        const isOfflineMethod = ['Cash', 'Bank Transfer', 'Check', 'Other', 'offline'].includes(method);

        if (isOnlineMethod && onlineEnabled && onlineEnabled.value === false) {
          return false;
        }
        if (isOfflineMethod && offlineEnabled && offlineEnabled.value === false) {
          return false;
        }
        return true;
      });
    } else {
      // Default methods if no settings found
      availableMethods = ['EVC Plus', 'Cash', 'Bank Transfer'];
    }

    res.json({
      success: true,
      message: 'Available payment methods retrieved',
      data: {
        paymentMethods: availableMethods,
        onlineEnabled: onlineEnabled ? onlineEnabled.value : true,
        offlineEnabled: offlineEnabled ? offlineEnabled.value : true,
        preferredMethod: 'EVC Plus' // Default recommendation
      }
    });
  } catch (error) {
    logError('Failed to get available payment methods', 'SubscriptionRenewalController', error);
    next(error);
  }
};

/**
 * Check renewal payment status
 * GET /api/subscriptions/renewal/:paymentId/status
 */
const checkRenewalPaymentStatus = async (req, res, next) => {
  try {
    const { paymentId } = req.params;

    const payment = await Payment.findOne({ paymentId });
    if (!payment) {
      throw new AppError('Payment not found', 404, 'payment_not_found');
    }

    // Check if payment belongs to current user's shop
    if (payment.shopId !== req.user.shopId) {
      throw new AppError('Unauthorized to view this payment', 403, 'unauthorized');
    }

    res.json({
      success: true,
      message: 'Payment status retrieved',
      data: {
        paymentId: payment.paymentId,
        status: payment.status,
        amount: payment.amount,
        currency: 'USD',
        method: payment.method,
        createdAt: payment.paymentDate,
        subscriptionId: payment.subscriptionId,
        accessRestored: payment.status === 'confirmed'
      }
    });
  } catch (error) {
    logError('Failed to check renewal payment status', 'SubscriptionRenewalController', error);
    next(error);
  }
};

module.exports = {
  processRenewalPayment,
  getAvailablePaymentMethods,
  checkRenewalPaymentStatus
}; 