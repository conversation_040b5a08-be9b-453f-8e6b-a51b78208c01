"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { useAuth } from "@/contexts/auth-context";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { toast } from "sonner";
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { KpiCard } from "@/components/dashboard/common/kpi-card";
import { Badge } from "@/components/ui/badge";
import {
  Bell,
  Send,
  Users,
  Building2,
  MessageSquare,
  Activity,
  Clock,
  CheckCircle,
  AlertTriangle,
  TrendingUp,
  RefreshCw,
  Settings2,
  Play,
  BarChart3
} from "lucide-react";

// Import our notification hooks
import { useNotifications } from "@/hooks/use-notifications";
import { useNotificationStats } from "@/hooks/use-notification-stats";

// Import notification components
import { NotificationSender } from "@/components/dashboard/notifications/notification-sender";
import { NotificationHistory } from "@/components/dashboard/notifications/notification-history";
import { RecentActivity } from "@/components/dashboard/notifications/recent-activity";

/**
 * Notification Settings Main Page
 * Comprehensive notification management dashboard for SuperAdmin
 */
export default function NotificationSettingsPage() {
  const router = useRouter();
  const { isSuperAdmin } = useAuth();
  const [activeTab, setActiveTab] = useState("overview");

  // Hooks for notification operations
  const {
    stats,
    targets,
    systemStatus,
    loadStats,
    loadTargets,
    testFirebase,
    refreshAll,
    isLoading
  } = useNotificationStats({
    autoRefresh: true,
    refreshInterval: 30000,
    showToastMessages: true
  });

  const {
    sendToShops,
    sendBroadcast,
    sendDebtReminders,
    testFirebaseConnection,
    isLoading: isOperationLoading
  } = useNotifications({
    showToastMessages: true,
    onSuccess: (operation, result) => {
      console.log(`${operation} completed successfully:`, result);
      refreshAll();
    }
  });



  // Redirect non-SuperAdmin users
  useEffect(() => {
    if (!isSuperAdmin()) {
      toast.error("You don't have permission to access this page");
      router.push("/dashboard");
    }
  }, [isSuperAdmin, router]);

  // Load initial data
  useEffect(() => {
    if (isSuperAdmin()) {
      loadStats();
      loadTargets();
    }
  }, [isSuperAdmin, loadStats, loadTargets]);

  // Handle refresh
  const handleRefresh = async () => {
    try {
      await refreshAll();
      toast.success("Data refreshed successfully");
    } catch (error) {
      toast.error("Failed to refresh data");
    }
  };

  // Handle Firebase test
  const handleFirebaseTest = async () => {
    try {
      await testFirebase();
    } catch (error) {
      // Error handling is done in the hook
    }
  };

  // Only render for SuperAdmin users
  if (!isSuperAdmin()) {
    return null;
  }

  // Calculate KPI values from stats
  const kpiData = {
    totalSent: stats?.stats?.totalSent || 0,
    totalDelivered: stats?.stats?.totalDelivered || 0,
    totalFailed: stats?.stats?.totalFailed || 0,
    deliveryRate: stats?.stats?.deliveryRate || 0,
    totalTargets: targets?.total || 0,
    activeShops: targets?.shops?.length || 0,
    activeUsers: targets?.users?.length || 0,
    firebaseStatus: systemStatus?.firebase?.connected || false
  };

  return (
    <div className="py-8 space-y-8">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold flex items-center gap-3">
            <div className="p-2 bg-primary/10 rounded-lg">
              <Bell className="h-6 w-6 text-primary" />
            </div>
            Notification Management
          </h1>
          <p className="text-muted-foreground mt-2">
            Manage push notifications, FCM tokens, and system communications
          </p>
        </div>
        <div className="flex items-center gap-3">
          <Button
            variant="outline"
            size="sm"
            onClick={handleRefresh}
            disabled={isLoading}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={handleFirebaseTest}
            disabled={isOperationLoading}
          >
            <Play className="h-4 w-4 mr-2" />
            Test Firebase
          </Button>
        </div>
      </div>

      {/* KPI Cards Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <KpiCard
          title="Total Notifications Sent"
          value={kpiData.totalSent.toLocaleString()}
          description="All time total"
          icon="activity"
          trend={kpiData.totalSent > 0 ? "up" : "neutral"}
          trendValue={`${kpiData.deliveryRate.toFixed(1)}%`}
          trendLabel="delivery rate"
          loading={isLoading}
        />
        
        <KpiCard
          title="Successfully Delivered"
          value={kpiData.totalDelivered.toLocaleString()}
          description="Confirmed deliveries"
          icon="credit"
          trend="up"
          trendValue={`${((kpiData.totalDelivered / Math.max(kpiData.totalSent, 1)) * 100).toFixed(1)}%`}
          trendLabel="success rate"
          loading={isLoading}
        />
        
        <KpiCard
          title="Active Notification Targets"
          value={kpiData.totalTargets.toLocaleString()}
          description={`${kpiData.activeShops} shops, ${kpiData.activeUsers} users`}
          icon="users"
          trend="up"
          trendValue={`${kpiData.activeShops}`}
          trendLabel="active shops"
          loading={isLoading}
        />
        
        <KpiCard
          title="Firebase Connection"
          value={kpiData.firebaseStatus ? "Connected" : "Disconnected"}
          description="FCM service status"
          icon="activity"
          trend={kpiData.firebaseStatus ? "up" : "down"}
          trendValue={kpiData.firebaseStatus ? "Online" : "Offline"}
          trendLabel="current status"
          loading={isLoading}
        />
      </div>

      {/* Firebase Status Alert */}
      {systemStatus && !systemStatus.firebase?.connected && (
        <Card className="border-amber-200 bg-amber-50">
          <CardContent className="pt-6">
            <div className="flex items-start space-x-4">
              <div className="p-2 bg-amber-100 rounded-md">
                <AlertTriangle className="h-6 w-6 text-amber-600" />
              </div>
              <div className="flex-1">
                <h3 className="font-medium text-amber-800">Firebase Connection Issue</h3>
                <p className="text-sm text-amber-700 mt-1">
                  Firebase connection is not working properly. Push notifications may not be delivered.
                  Error: {systemStatus.firebase?.error || 'Unknown error'}
                </p>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleFirebaseTest}
                  className="mt-3 border-amber-300 text-amber-700 hover:bg-amber-100"
                >
                  <Play className="h-4 w-4 mr-2" />
                  Test Connection
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Main Content Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="overview" className="flex items-center gap-2">
            <BarChart3 className="h-4 w-4" />
            Overview
          </TabsTrigger>
          <TabsTrigger value="send" className="flex items-center gap-2">
            <Send className="h-4 w-4" />
            Send Notifications
          </TabsTrigger>
          <TabsTrigger value="history" className="flex items-center gap-2">
            <Clock className="h-4 w-4" />
            History
          </TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* System Status Card */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Activity className="h-5 w-5" />
                  System Status
                </CardTitle>
                <CardDescription>
                  Current notification system health
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Firebase Connection</span>
                  <Badge variant={kpiData.firebaseStatus ? "default" : "destructive"}>
                    {kpiData.firebaseStatus ? "Connected" : "Disconnected"}
                  </Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Active Targets</span>
                  <Badge variant="secondary">
                    {kpiData.totalTargets} total
                  </Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Delivery Rate</span>
                  <Badge variant={kpiData.deliveryRate > 90 ? "default" : "secondary"}>
                    {kpiData.deliveryRate.toFixed(1)}%
                  </Badge>
                </div>
              </CardContent>
            </Card>

            {/* Quick Actions Card */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Send className="h-5 w-5" />
                  Quick Actions
                </CardTitle>
                <CardDescription>
                  Common notification operations
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-3">
                <Button
                  variant="outline"
                  className="w-full justify-start"
                  onClick={() => setActiveTab("send")}
                >
                  <Building2 className="h-4 w-4 mr-2" />
                  Send to Shops
                </Button>
                <Button
                  variant="outline"
                  className="w-full justify-start"
                  onClick={() => setActiveTab("send")}
                >
                  <Users className="h-4 w-4 mr-2" />
                  Broadcast Message
                </Button>
                <Button
                  variant="outline"
                  className="w-full justify-start"
                  onClick={() => setActiveTab("send")}
                >
                  <MessageSquare className="h-4 w-4 mr-2" />
                  Debt Reminders
                </Button>
              </CardContent>
            </Card>
          </div>

          {/* Recent Activity */}
          <RecentActivity />
        </TabsContent>

        {/* Send Notifications Tab */}
        <TabsContent value="send" className="space-y-6">
          <NotificationSender
            sendToShops={sendToShops}
            sendBroadcast={sendBroadcast}
            sendDebtReminders={sendDebtReminders}
            targets={targets}
            isLoading={isOperationLoading}
          />
        </TabsContent>

        {/* History Tab */}
        <TabsContent value="history" className="space-y-6">
          <NotificationHistory
            stats={stats}
            loadStats={loadStats}
            isLoading={isLoading}
          />
        </TabsContent>


      </Tabs>
    </div>
  );
} 