/**
 * Get User Statistics Controller
 * Handles the retrieval of user statistics for dashboard analytics
 */
const UserService = require('../../services/userService');
const { AppError, logInfo, logError } = require('../../utils');

/**
 * Get user statistics
 * @route GET /api/users/stats
 * @access Private (SuperAdmin only)
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object  
 * @param {Function} next - Express next middleware function
 * @returns {Object} User statistics data
 */
const getUserStats = async (req, res, next) => {
  try {
    const { startDate, endDate, groupBy } = req.query;
    
    // Parse date parameters if provided
    const options = {};
    
    if (startDate) {
      options.startDate = new Date(startDate);
      if (isNaN(options.startDate.getTime())) {
        return res.status(400).json({
          success: false,
          message: 'Invalid startDate format. Use ISO date format (YYYY-MM-DD)',
          statusCode: 400,
          type: 'validation_error'
        });
      }
    }
    
    if (endDate) {
      options.endDate = new Date(endDate);
      if (isNaN(options.endDate.getTime())) {
        return res.status(400).json({
          success: false,
          message: 'Invalid endDate format. Use ISO date format (YYYY-MM-DD)',
          statusCode: 400,
          type: 'validation_error'
        });
      }
    }
    
    // Validate groupBy parameter
    if (groupBy && !['day', 'week', 'month'].includes(groupBy)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid groupBy value. Must be one of: day, week, month',
        statusCode: 400,
        type: 'validation_error'
      });
    }
    
    if (groupBy) {
      options.groupBy = groupBy;
    }
    
    // Validate date range
    if (options.startDate && options.endDate && options.startDate >= options.endDate) {
      return res.status(400).json({
        success: false,
        message: 'startDate must be before endDate',
        statusCode: 400,
        type: 'validation_error'
      });
    }
    
    logInfo(`Fetching user statistics with options: ${JSON.stringify(options)}`, 'getUserStatsController');
    
    // Get user statistics from service
    const userStats = await UserService.getUserStats(options);
    
    logInfo('User statistics retrieved successfully', 'getUserStatsController');
    
    return res.status(200).json({
      success: true,
      message: 'User statistics retrieved successfully',
      data: userStats,
      statusCode: 200
    });
    
  } catch (error) {
    logError('Error retrieving user statistics', 'getUserStatsController', error);
    
    if (error instanceof AppError) {
      return res.status(error.statusCode).json({
        success: false,
        message: error.message,
        statusCode: error.statusCode,
        type: error.type || 'error'
      });
    }
    
    return res.status(500).json({
      success: false,
      message: 'Internal server error while retrieving user statistics',
      statusCode: 500,
      type: 'server_error'
    });
  }
};

module.exports = getUserStats; 