/**
 * User Controller
 * 
 * This controller has been refactored into a modular structure for better maintainability.
 * Each operation is now in its own file under the src/controllers/user/ directory.
 * 
 * This file re-exports all controller functions for backward compatibility while providing
 * the benefits of a more modular codebase.
 */

// Import required models and utilities
const User = require('../models/user.model');
const { AppError } = require('../utils');

// Re-export all user controller functions from the modular structure
const UserController = require('./user');

// For debugging purposes - display all exported functions
// console.log('UserController exports:', Object.keys(UserController)); // Debug output disabled

// Add employee management methods to the UserController object

// Get all employees for the shop
UserController.getEmployees = async (req, res, next) => {
  try {
    console.log(`[getEmployees] Controller reached! User: ${req.user?.userId}, Role: ${req.user?.role}, ShopId: ${req.user?.shopId}`);
    const { page = 1, limit = 10, search } = req.query;

    const query = {
      shopId: req.user.shopId,
      role: 'employee',
      isDeleted: false
    };
    
    if (search) {
      query.$or = [
        { fullName: { $regex: search, $options: 'i' } },
        { email: { $regex: search, $options: 'i' } }
      ];
    }
    
    const employees = await User.find(query)
      .select('-password')
      .limit(limit * 1)
      .skip((page - 1) * limit)
      .sort({ createdAt: -1 });
    
    const total = await User.countDocuments(query);
    
    // Always return success, even with empty results
    res.status(200).json({
      success: true,
      message: `Found ${total} employee(s) for shop ${req.user.shopId}`,
      data: {
        employees,
        pagination: {
          current: parseInt(page),
          pages: Math.ceil(total / parseInt(limit)),
          total
        }
      }
    });
  } catch (error) {
    console.error('Error in getEmployees:', error);
    next(error);
  }
};

// Get employee by ID
UserController.getEmployeeById = async (req, res, next) => {
  try {
    const { employeeId } = req.params;
    
    const employee = await User.findOne({
      userId: employeeId,
      shopId: req.user.shopId,
      role: 'employee'
    }).select('-password');
    
    if (!employee) {
      return next(new AppError('Employee not found', 404, 'employee_not_found'));
    }
    
    res.status(200).json({
      success: true,
      data: { employee }
    });
  } catch (error) {
    next(error);
  }
};

// Update employee permissions
UserController.updateEmployeePermissions = async (req, res, next) => {
  try {
    const { employeeId } = req.params;
    const { visibility } = req.body;
    
    const employee = await User.findOneAndUpdate(
      { 
        userId: employeeId, 
        shopId: req.user.shopId, 
        role: 'employee' 
      },
      { visibility },
      { new: true }
    ).select('-password');
    
    if (!employee) {
      return next(new AppError('Employee not found', 404, 'employee_not_found'));
    }
    
    res.status(200).json({
      success: true,
      message: 'Employee permissions updated successfully',
      data: { employee }
    });
  } catch (error) {
    next(error);
  }
};

// Delete employee
UserController.deleteEmployee = async (req, res, next) => {
  try {
    const { employeeId } = req.params;
    
    const employee = await User.findOneAndDelete({
      userId: employeeId,
      shopId: req.user.shopId,
      role: 'employee'
    });
    
    if (!employee) {
      return next(new AppError('Employee not found', 404, 'employee_not_found'));
    }
    
    res.status(200).json({
      success: true,
      message: 'Employee deleted successfully'
    });
  } catch (error) {
    next(error);
  }
};

// Reset employee password (Admin only)
UserController.resetEmployeePassword = async (req, res, next) => {
  try {
    const { employeeId } = req.params;
    const { sendEmail = true } = req.body;

    // Find the employee
    const employee = await User.findOne({
      userId: employeeId,
      shopId: req.user.shopId,
      role: 'employee',
      isDeleted: false
    });

    if (!employee) {
      return next(new AppError('Employee not found', 404, 'employee_not_found'));
    }

    // Generate reset token (same as existing forgot password system)
    const crypto = require('crypto');
    const resetToken = crypto.randomBytes(32).toString('hex');
    const resetTokenExpiry = new Date(Date.now() + 60 * 60 * 1000); // 1 hour (same as forgot password)

    // Hash the token for storage (same as existing system)
    const hashedToken = crypto
      .createHash('sha256')
      .update(resetToken)
      .digest('hex');

    // Save hashed token to employee (using existing fields)
    employee.passwordResetToken = hashedToken;
    employee.passwordResetExpires = resetTokenExpiry;
    await employee.save();

    // Send reset email if requested
    if (sendEmail) {
      try {
        const EmailService = require('../services/emailService');
        await EmailService.auth.sendPasswordResetEmail(employee, resetToken, {
          resetByAdmin: true,
          adminName: req.user.fullName
        });
      } catch (emailError) {
        console.error('Failed to send password reset email:', emailError);
        // Don't fail the request if email fails
      }
    }

    res.status(200).json({
      success: true,
      message: sendEmail
        ? 'Password reset email sent to employee successfully'
        : 'Password reset token generated successfully (check email for reset link)',
      data: {
        expiresAt: resetTokenExpiry,
        employeeEmail: employee.email,
        employeeName: employee.fullName,
        employeeTitle: employee.userTitle
      }
    });
  } catch (error) {
    console.error('Error in resetEmployeePassword:', error);
    next(error);
  }
};



// Export the complete controller with all methods
module.exports = UserController;

