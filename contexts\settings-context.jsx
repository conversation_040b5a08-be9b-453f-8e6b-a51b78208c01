"use client";

/**
 * Settings Context - DEPRECATED
 * 
 * ⚠️ DEPRECATED: This file is being maintained for backward compatibility only.
 * Please use the modular implementation from '@/contexts/settings' instead.
 * 
 * This file re-exports the components from the modular implementation to
 * ensure backward compatibility during the transition period.
 */

import { 
  SettingsContext as ModularSettingsContext,
  SettingsProvider as ModularSettingsProvider,
  useSettings as modularUseSettings
} from './settings/index';

// Console warning about deprecation
const showDeprecationWarning = () => {
  // Only show in development to avoid console noise in production
  if (process.env.NODE_ENV === 'development') {
    console.warn(
      '%c[DEPRECATED] settings-context.jsx is deprecated', 
      'background: #FFF3CD; color: #856404; padding: 2px 4px; border-radius: 2px;',
      'Please import from @/contexts/settings instead.'
    );
  }
};

// Re-export the context
export const SettingsContext = ModularSettingsContext;

// Re-export the provider with a warning
export const SettingsProvider = ({ children, ...props }) => {
  showDeprecationWarning();
  return <ModularSettingsProvider {...props}>{children}</ModularSettingsProvider>;
};

// Re-export the hook with a warning
export function useSettings() {
  showDeprecationWarning();
  return modularUseSettings();
}

/**
 * Migration Guide:
 * 
 * 1. Change imports from:
 *    import { SettingsProvider, useSettings } from '@/contexts/settings-context';
 *    
 *    To:
 *    import { SettingsProvider, useSettings } from '@/contexts/settings';
 * 
 * 2. No other code changes needed - the API is identical
 * 
 * 3. Once all imports have been updated, this file can be safely removed
 */