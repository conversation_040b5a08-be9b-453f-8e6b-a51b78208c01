"use client";

import { useEffect } from "react";
import { useRouter } from "next/navigation";
import { useAuth } from "@/contexts/auth-context";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { toast } from "sonner";
import {
  Settings2,
  Shield,
  Building,
  FileText,
} from "lucide-react";

/**
 * System Settings main page
 * Acts as a dashboard/hub for all settings categories
 * Only accessible by SuperAdmin users
 */
export default function SettingsPage() {
  const router = useRouter();
  const { isSuperAdmin } = useAuth();

  // Redirect non-SuperAdmin users
  useEffect(() => {
    if (!isSuperAdmin()) {
      toast.error("You don't have permission to access this page");
      router.push("/dashboard");
    }
  }, [isSuperAdmin, router]);

  // Settings categories available to SuperAdmin
  const settingsCategories = [
    {
      title: "General Settings",
      description: "Manage system-wide configuration and defaults",
      icon: <Settings2 className="h-6 w-6" />,
      href: "/dashboard/settings/general",
    },
    {
      title: "Security Settings",
      description: "Manage security policies and access controls",
      icon: <Shield className="h-6 w-6" />,
      href: "/dashboard/settings/security",
    },
    // Shop configuration removed - business rules are now managed by individual shops
    // while platform-level limits are controlled via subscription plans
    {
      title: "System Logs",
      description: "View system activity and audit logs",
      icon: <FileText className="h-6 w-6" />,
      href: "/dashboard/settings/system-logs",
    },
  ];

  // Navigate to the selected settings category
  const navigateToCategory = (href) => {
    router.push(href);
  };

  // Only render for SuperAdmin users
  if (!isSuperAdmin()) {
    return null;
  }

  return (
    <div className="container py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">System Settings</h1>
        <p className="text-muted-foreground">
          Manage global system settings and configuration
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {settingsCategories.map((category) => (
          <Card key={category.title} className="overflow-hidden">
            <CardHeader className="pb-2">
              <div className="flex items-start justify-between">
                <div className="p-2 bg-primary/10 rounded-md">
                  {category.icon}
                </div>
              </div>
              <CardTitle className="mt-4">{category.title}</CardTitle>
              <CardDescription>{category.description}</CardDescription>
            </CardHeader>
            <CardContent>
              <Button
                variant="default"
                className="w-full mt-2"
                onClick={() => navigateToCategory(category.href)}
              >
                Manage
              </Button>
            </CardContent>
          </Card>
        ))}
      </div>
      
      <div className="mt-8 p-4 bg-amber-50 border border-amber-200 rounded-md">
        <div className="flex items-start space-x-4">
          <div className="p-2 bg-amber-100 rounded-md">
            <Shield className="h-6 w-6 text-amber-600" />
          </div>
          <div>
            <h3 className="font-medium text-amber-800">SuperAdmin Only</h3>
            <p className="text-sm text-amber-700 mt-1">
              These settings affect the entire system and are only accessible to SuperAdmin users.
              Changes made here will impact all shops and users on the platform.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
