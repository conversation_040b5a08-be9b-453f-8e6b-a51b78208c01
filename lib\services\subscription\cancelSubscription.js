import apiBridge from '@/lib/api/bridge';
import BaseService from '../baseService';

/**
 * Cancel active subscription
 * @param {Object} cancelData - Cancellation data (reason, feedback, immediateEffect)
 * @returns {Promise<Object>} Cancellation result
 */
const cancelSubscription = async (subscriptionId, cancellationData) => {
  try {
    const response = await apiBridge.patch(
      ENDPOINTS.subscription.cancelSubscription.replace(':subscriptionId', subscriptionId),
      cancellationData
    );
    
    return BaseService.processApiResponse(response);
  } catch (error) {
    throw BaseService.handleApiError(error);
  }
};

export default cancelSubscription; 
