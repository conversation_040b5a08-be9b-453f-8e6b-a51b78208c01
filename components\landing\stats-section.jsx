"use client";

import { 
  TrendingUp, 
  Users, 
  Store, 
  DollarSign, 
  BarChart3, 
  Activity,
  ArrowUpRight,
  ArrowDownRight,
  Eye,
  Zap
} from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle } from "../ui/card";
import { Badge } from "../ui/badge";

const platformStats = [
  {
    label: "Active Shops",
    value: "2,547",
    change: "+12.3%",
    trend: "up",
    icon: Store,
    color: "text-blue-600 dark:text-blue-400",
    bgColor: "bg-blue-50 dark:bg-blue-900/20"
  },
  {
    label: "Total Users",
    value: "18,492",
    change: "+8.7%",
    trend: "up",
    icon: Users,
    color: "text-green-600 dark:text-green-400",
    bgColor: "bg-green-50 dark:bg-green-900/20"
  },
  {
    label: "Monthly Revenue",
    value: "$89.2K",
    change: "+15.2%",
    trend: "up",
    icon: DollarSign,
    color: "text-purple-600 dark:text-purple-400",
    bgColor: "bg-purple-50 dark:bg-purple-900/20"
  },
  {
    label: "Debt Recovery Rate",
    value: "94.7%",
    change: "+3.1%",
    trend: "up",
    icon: TrendingUp,
    color: "text-orange-600 dark:text-orange-400",
    bgColor: "bg-orange-50 dark:bg-orange-900/20"
  }
];

const realtimeMetrics = [
  {
    title: "Live Transactions",
    value: "1,247",
    unit: "today",
    icon: Activity,
    pulse: true
  },
  {
    title: "Risk Assessments",
    value: "89",
    unit: "this hour",
    icon: BarChart3,
    pulse: true
  },
  {
    title: "Notifications Sent",
    value: "3,521",
    unit: "today",
    icon: Zap,
    pulse: true
  }
];

const analyticsFeatures = [
  {
    title: "Real-time Dashboard",
    description: "Monitor your business performance with live data updates and interactive charts.",
    metrics: ["Revenue tracking", "Customer analytics", "Debt monitoring"],
    icon: BarChart3
  },
  {
    title: "Predictive Analytics",
    description: "AI-powered insights help predict customer behavior and optimize debt collection strategies.",
    metrics: ["Risk scoring", "Payment predictions", "Trend forecasting"],
    icon: TrendingUp
  },
  {
    title: "Custom Reports",
    description: "Generate detailed reports tailored to your business needs with automated scheduling.",
    metrics: ["Automated exports", "Custom metrics", "Email delivery"],
    icon: Eye
  }
];

export default function StatsSection() {
  return (
    <section className="py-24 bg-gradient-to-b from-white to-slate-50 dark:from-slate-900 dark:to-slate-800">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <div className="text-center mb-16">
          <Badge className="mb-4 bg-purple-100 dark:bg-purple-900/50 text-purple-700 dark:text-purple-300 border-purple-200 dark:border-purple-700">
            Powered by Data
          </Badge>
          <h2 className="text-3xl md:text-4xl font-bold text-slate-900 dark:text-white mb-6">
            Real-time Analytics That Drive Results
          </h2>
          <p className="text-lg text-slate-600 dark:text-slate-300 max-w-3xl mx-auto">
            Make data-driven decisions with comprehensive analytics, real-time monitoring, 
            and AI-powered insights that help you understand and grow your business.
          </p>
        </div>

        {/* Platform Statistics */}
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-6 mb-16">
          {platformStats.map((stat, index) => (
            <Card key={index} className="relative overflow-hidden border border-slate-200 dark:border-slate-700 bg-white dark:bg-slate-800 hover:shadow-lg dark:hover:shadow-slate-700/25 transition-all duration-300">
              <CardContent className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <div className={`p-3 rounded-lg ${stat.bgColor}`}>
                    <stat.icon className={`w-6 h-6 ${stat.color}`} />
                  </div>
                  <div className={`flex items-center gap-1 text-sm font-medium ${
                    stat.trend === 'up' ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'
                  }`}>
                    {stat.trend === 'up' ? (
                      <ArrowUpRight className="w-4 h-4" />
                    ) : (
                      <ArrowDownRight className="w-4 h-4" />
                    )}
                    {stat.change}
                  </div>
                </div>
                <div>
                  <div className="text-2xl md:text-3xl font-bold text-slate-900 dark:text-white mb-1">
                    {stat.value}
                  </div>
                  <div className="text-sm text-slate-600 dark:text-slate-400">{stat.label}</div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Real-time Metrics */}
        <div className="mb-16">
          <div className="text-center mb-8">
            <h3 className="text-2xl font-bold text-slate-900 dark:text-white mb-2">Live System Activity</h3>
            <p className="text-slate-600 dark:text-slate-300">Watch your platform in action with real-time data</p>
          </div>
          
          <div className="grid md:grid-cols-3 gap-6">
            {realtimeMetrics.map((metric, index) => (
              <Card key={index} className="relative border border-slate-200 dark:border-slate-700 bg-gradient-to-br from-white to-slate-50 dark:from-slate-800 dark:to-slate-700">
                <CardContent className="p-6 text-center">
                  <div className="relative mb-4">
                    <div className="mx-auto p-4 bg-blue-100 dark:bg-blue-900/30 rounded-full w-fit">
                      <metric.icon className="w-8 h-8 text-blue-600 dark:text-blue-400" />
                    </div>
                    {metric.pulse && (
                      <div className="absolute inset-0 flex items-center justify-center">
                        <div className="w-16 h-16 bg-blue-400 dark:bg-blue-500 rounded-full animate-ping opacity-20"></div>
                      </div>
                    )}
                  </div>
                  <div className="text-3xl font-bold text-slate-900 dark:text-white mb-1">{metric.value}</div>
                  <div className="text-sm text-slate-600 dark:text-slate-400 mb-2">{metric.title}</div>
                  <Badge variant="outline" className="text-xs border-slate-200 dark:border-slate-600 text-slate-600 dark:text-slate-400">
                    {metric.unit}
                  </Badge>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Analytics Features */}
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          {/* Left side - Features */}
          <div>
            <h3 className="text-2xl md:text-3xl font-bold text-slate-900 dark:text-white mb-6">
              Advanced Analytics for Smart Decisions
            </h3>
            <div className="space-y-8">
              {analyticsFeatures.map((feature, index) => (
                <div key={index} className="flex gap-4">
                  <div className="flex-shrink-0">
                    <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-500 rounded-lg flex items-center justify-center">
                      <feature.icon className="w-6 h-6 text-white" />
                    </div>
                  </div>
                  <div>
                    <h4 className="text-lg font-semibold text-slate-900 dark:text-white mb-2">{feature.title}</h4>
                    <p className="text-slate-600 dark:text-slate-300 mb-3 leading-relaxed">{feature.description}</p>
                    <div className="flex flex-wrap gap-2">
                      {feature.metrics.map((metric, idx) => (
                        <Badge key={idx} variant="secondary" className="bg-slate-100 dark:bg-slate-700 text-slate-700 dark:text-slate-300 border-0">
                          {metric}
                        </Badge>
                      ))}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Right side - Analytics Dashboard Preview */}
          <div className="relative">
            <Card className="p-6 bg-gradient-to-br from-slate-900 to-slate-800 dark:from-slate-800 dark:to-slate-900 border-slate-700 dark:border-slate-600 text-white">
              <div className="flex items-center justify-between mb-6">
                <div>
                  <h4 className="text-lg font-semibold mb-1">Real-time business insights</h4>
                  <div className="flex items-center gap-2 text-sm text-slate-300">
                    <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                    Live
                  </div>
                </div>
                <Badge className="bg-green-500/20 text-green-400 border-green-500/30">
                  Real-time
                </Badge>
              </div>

              {/* Chart representation */}
              <div className="relative h-48 bg-slate-800 dark:bg-slate-700 rounded-lg p-4 mb-6">
                <div className="flex items-end justify-between h-full">
                  {[65, 45, 78, 52, 85, 67, 92, 74, 58, 89, 76, 95].map((height, index) => (
                    <div key={index} className="relative group">
                      <div 
                        className="bg-gradient-to-t from-blue-500 to-purple-500 rounded-sm w-4 transition-all duration-300 group-hover:from-blue-400 group-hover:to-purple-400"
                        style={{ height: `${height}%` }}
                      ></div>
                    </div>
                  ))}
                </div>
                <div className="absolute bottom-1 left-4 right-4">
                  <div className="text-xs text-slate-400 dark:text-slate-500 flex justify-center">
                    Revenue Growth (12 months)
                  </div>
                </div>
              </div>

              {/* Key metrics */}
              <div className="grid grid-cols-2 gap-4">
                <div className="bg-slate-800 dark:bg-slate-700 rounded-lg p-4 text-center">
                  <div className="text-2xl font-bold text-green-400">98.2%</div>
                  <div className="text-xs text-slate-400">Uptime</div>
                </div>
                <div className="bg-slate-800 dark:bg-slate-700 rounded-lg p-4 text-center">
                  <div className="text-2xl font-bold text-blue-400 flex items-center justify-center gap-1">
                    4.9
                    <span className="text-yellow-400 text-lg">★</span>
                  </div>
                  <div className="text-xs text-slate-400">Satisfaction</div>
                </div>
              </div>
            </Card>

            {/* Background decoration */}
            <div className="absolute -top-4 -right-4 w-24 h-24 bg-gradient-to-r from-blue-400 to-purple-400 dark:from-blue-500 dark:to-purple-500 rounded-full opacity-20 blur-2xl"></div>
          </div>
        </div>

        {/* Bottom CTA section */}
        <div className="mt-16 text-center p-8 bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 rounded-2xl border border-blue-100 dark:border-blue-800">
          <div className="flex items-center justify-center gap-2 text-blue-600 dark:text-blue-400 mb-2">
            <Activity className="w-5 h-5" />
            <span className="text-sm font-medium">Processing over 10,000+ transactions daily</span>
          </div>
        </div>
      </div>
    </section>
  );
} 