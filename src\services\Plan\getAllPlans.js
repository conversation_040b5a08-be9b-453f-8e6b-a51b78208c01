/**
 * Get all plans
 * Retrieves active plans or all plans based on parameters
 */
const { Plan } = require('../../models');
const { SubscriptionService } = require('../');
const { logError } = require('../../utils');

/**
 * Get all plans
 * @param {boolean} includeInactive - Whether to include inactive plans (for SuperAdmin)
 * @returns {Promise<Array>} List of plans with subscriber counts
 */
const getAllPlans = async (includeInactive = false) => {
  try {
    let query = { isDeleted: false };
    
    // Only filter by active status if not including inactive plans
    if (!includeInactive) {
      query.isActive = true;
    }
    
    const plans = await Plan.find(query).sort({ displayOrder: 1 });
    
    // Add subscriber count for each plan
    const plansWithStats = await Promise.all(plans.map(async (plan) => {
      const planObj = plan.toObject();
      
      // Get active subscription count for this plan
      try {
        const subscriberCount = await SubscriptionService.countSubscriptionsByPlanId(
          plan.planId, 
          { status: ['active', 'trial'] }
        );
        planObj.subscriberCount = subscriberCount;
      } catch (error) {
        logError(`Failed to get subscriber count for plan ${plan.planId}`, 'PlanService', error);
        planObj.subscriberCount = 0;
      }
      
      return planObj;
    }));
    
    return plansWithStats;
  } catch (error) {
    logError('Failed to get plans', 'PlanService', error);
    throw error;
  }
};

module.exports = getAllPlans;
