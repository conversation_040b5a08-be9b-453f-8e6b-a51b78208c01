/**
 * Fix Orphaned Debt Records
 * 
 * Problem: Some debt records may have null or missing debtId
 * Solution: Generate proper debtIds for these records
 */

const mongoose = require('mongoose');
const { generateDebtId } = require('../generators/idGenerator');
const Debt = require('../../models/debt.model');
require('dotenv').config();

async function fixOrphanedDebts() {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI || process.env.DATABASE_URL);
    console.log('✅ Connected to MongoDB');

    // Find debts with null or missing debtId
    const orphanedDebts = await Debt.find({
      $or: [
        { debtId: null },
        { debtId: { $exists: false } },
        { debtId: '' }
      ]
    });

    console.log(`\n📊 Found ${orphanedDebts.length} orphaned debt records`);

    if (orphanedDebts.length === 0) {
      console.log('✅ No orphaned debts found. Database is clean!');
      return;
    }

    // Fix each orphaned debt
    for (let i = 0; i < orphanedDebts.length; i++) {
      const debt = orphanedDebts[i];
      console.log(`\n🔧 Fixing debt ${i + 1}/${orphanedDebts.length}`);
      console.log(`   - MongoDB _id: ${debt._id}`);
      console.log(`   - Customer: ${debt.CustomerName || 'Unknown'}`);
      console.log(`   - Amount: $${debt.DebtAmount || 0}`);

      try {
        // Generate new debtId
        const newDebtId = await generateDebtId(Debt);
        console.log(`   - Generated debtId: ${newDebtId}`);

        // Update the debt record
        debt.debtId = newDebtId;
        await debt.save();
        
        console.log(`   ✅ Fixed successfully`);
      } catch (error) {
        console.error(`   ❌ Error fixing debt ${debt._id}:`, error.message);
        
        // If there's still a duplicate error, try with a timestamp suffix
        if (error.code === 11000) {
          try {
            const timestampSuffix = Date.now().toString().slice(-4);
            const fallbackId = `DEBT${timestampSuffix}`;
            debt.debtId = fallbackId;
            await debt.save();
            console.log(`   ✅ Fixed with fallback ID: ${fallbackId}`);
          } catch (fallbackError) {
            console.error(`   ❌ Fallback also failed:`, fallbackError.message);
          }
        }
      }
    }

    // Verify the fix
    const remainingOrphaned = await Debt.find({
      $or: [
        { debtId: null },
        { debtId: { $exists: false } },
        { debtId: '' }
      ]
    }).countDocuments();

    console.log(`\n📊 Verification:`);
    console.log(`   - Remaining orphaned debts: ${remainingOrphaned}`);
    
    if (remainingOrphaned === 0) {
      console.log('🎉 All orphaned debts have been fixed!');
    } else {
      console.log('⚠️  Some orphaned debts still remain. Manual intervention may be required.');
    }

  } catch (error) {
    console.error('❌ Error fixing orphaned debts:', error);
  } finally {
    await mongoose.disconnect();
    console.log('📤 Disconnected from MongoDB');
  }
}

// Run the fix
if (require.main === module) {
  fixOrphanedDebts();
}

module.exports = fixOrphanedDebts; 