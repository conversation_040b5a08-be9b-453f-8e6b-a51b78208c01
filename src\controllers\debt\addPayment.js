const Debt = require('../../models/debt.model');
const Payment = require('../../models/payment.model');
const Customer = require('../../models/customer.model');
const { generatePaymentId } = require('../../utils/generators/idGenerator');
const AppError = require('../../utils/core/AppError');
const mlService = require('../../services/mlRiskService');
const FirebaseService = require('../../services/firebaseService');

/**
 * Add Payment Controller (Optimized for Speed)
 * - Fast payment recording with minimal blocking operations
 * - Background ML evaluation for performance
 * - Single notification system (no duplicates)
 * - Parallel database operations where possible
 */
const addPayment = async (req, res, next) => {
  try {
    const { debtId } = req.params;
    const {
      amount,
      paymentDate = new Date(),
      paidAtReal,
      paymentMethod = 'cash',
      notes
    } = req.body;

    const shopId = req.user.shopId;

    // Parallel fetch debt and customer (faster)
    const [debt, customer] = await Promise.all([
      Debt.findOne({ debtId, shopId }),
      Debt.findOne({ debtId, shopId }).then(d => 
        d ? Customer.findOne({ customerId: d.customerId }) : null
      )
    ]);

    if (!debt) {
      return next(new AppError('Debt record not found', 404));
    }
    if (!customer) {
      return next(new AppError('Customer not found', 404));
    }

    // Generate payment ID and create payment record
    const paymentId = await generatePaymentId(Payment);
    const payment = new Payment({
      paymentId,
      paymentContext: 'debt',
      debtId: debt.debtId,
      customerId: customer.customerId,
      shopId,
      amount: parseFloat(amount),
      paymentDate: new Date(),
      paidAtReal: paidAtReal ? new Date(paidAtReal) : new Date(),
      paymentMethod,
      notes,
    });

    // Calculate payment timing and update debt
    await payment.calculatePaymentTiming(debt.DueDate);
    await debt.addPayment(parseFloat(amount), payment.paidAtReal);

    // Determine payment status quickly
    const now = new Date();
    const dueDate = new Date(debt.DueDate);
    const isDueDatePassed = now > dueDate;
    const isFullPayment = debt.DebtPaidRatio >= 1.0;
    const isSignificantPartialPayment = debt.DebtPaidRatio >= 0.5 && debt.DebtPaidRatio < 1.0;

    // Update debt status based on payment
    if (debt.OutstandingDebt === 0) {
      debt.status = 'paid';
    } else if (debt.DebtPaidRatio >= 0.5) {
      debt.status = 'partially_paid';
    } else {
      debt.status = 'partially_paid';
    }

    // Fast parallel save (payment + debt)
    await Promise.all([
      payment.save(),
      debt.save()
    ]);

    // Prepare response data immediately (don't wait for ML/notifications)
    const paymentTiming = payment.IsOnTime 
      ? (payment.PaymentDelay === 0 ? 'On Time ✅' : `${Math.abs(payment.PaymentDelay)} days early ⚡`)
      : `${payment.PaymentDelay} days late ⏰`;

    const paymentStatus = debt.OutstandingDebt === 0 
      ? 'Full Payment ✅'
      : debt.DebtPaidRatio >= 0.5 
        ? 'Significant Partial Payment ⚠️'
        : 'Minimal Payment 💰';

    const responseData = {
      success: true,
      message: 'Payment recorded successfully',
      data: {
        payment: {
          paymentId: payment.paymentId,
          amount: payment.amount,
          officialDate: payment.paymentDate,
          realPaymentDate: payment.paidAtReal,
          method: payment.paymentMethod,
          timing: paymentTiming,
          status: paymentStatus,
          notes: payment.notes
        },
        debt: {
          debtId: debt.debtId,
          originalAmount: debt.DebtAmount,
          outstandingDebt: debt.OutstandingDebt,
          paidAmount: debt.PaidAmount,
          debtPaidRatio: Math.round(debt.DebtPaidRatio * 100) / 100,
          status: debt.status,
          riskLevel: debt.RiskLevel
        },
        customer: {
          name: customer.CustomerName,
          type: customer.CustomerType,
          currentRiskLevel: customer.riskProfile.currentRiskLevel,
          riskScore: customer.riskProfile.riskScore
        },
        mlEvaluation: {
          status: 'processing',
          message: 'Risk evaluation running in background'
        }
      }
    };

    // Send response immediately (fast!)
    res.status(200).json(responseData);

    // Background processing (non-blocking)
    setImmediate(async () => {
      try {
        let finalCustomerStatus = customer.riskProfile.currentRiskLevel || 'Active Debt';
        let mlEvaluationResult = null;

        // ML evaluation in background
        const shouldEvaluateML = isFullPayment || isSignificantPartialPayment || isDueDatePassed;
        
        if (shouldEvaluateML) {
          mlEvaluationResult = await mlService.evaluateRisk(debt, customer);
          
          // Update debt and customer with ML results
          await Promise.all([
            Debt.updateOne(
              { debtId: debt.debtId },
              { RiskLevel: mlEvaluationResult.riskLevel }
            ),
            Customer.updateOne(
              { customerId: customer.customerId },
              {
                'riskProfile.currentRiskLevel': mlEvaluationResult.riskLevel,
                'riskProfile.riskScore': mlEvaluationResult.riskScore,
                'riskProfile.lastAssessment': new Date(),
                'riskProfile.mlSource': 'ml_api'
              }
            )
          ]);

          // Use the updated ML risk status for notification
          finalCustomerStatus = mlEvaluationResult.riskLevel;
        }

        // Enhanced notification with ML risk status
        const statusEmoji = {
          'Low Risk': '✅',
          'Medium Risk': '⚠️', 
          'High Risk': '🚨',
          'Active Debt': '📝'
        }[finalCustomerStatus] || '📋';

        const paymentAmountFormatted = new Intl.NumberFormat('en-US', {
          style: 'currency',
          currency: 'USD',
          minimumFractionDigits: 0
        }).format(payment.amount);

        // Create detailed notification message
        let notificationMessage = `${customer.CustomerName} paid ${paymentAmountFormatted}`;
        
        if (mlEvaluationResult) {
          // Include ML evaluation details in notification
          const riskScore = Math.round(mlEvaluationResult.riskScore);
          notificationMessage += ` | Risk: ${statusEmoji} ${finalCustomerStatus} (${riskScore}%)`;
          
          if (mlEvaluationResult.factors && mlEvaluationResult.factors.length > 0) {
            const mainFactor = mlEvaluationResult.factors[0];
            notificationMessage += ` | ${mainFactor}`;
          }
        } else {
          notificationMessage += ` | Status: ${statusEmoji} ${finalCustomerStatus}`;
        }

        // Add payment status context
        if (debt.OutstandingDebt === 0) {
          notificationMessage += ' | ✅ PAID IN FULL';
        } else if (debt.DebtPaidRatio >= 0.5) {
          const remainingFormatted = new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD',
            minimumFractionDigits: 0
          }).format(debt.OutstandingDebt);
          notificationMessage += ` | ${remainingFormatted} remaining`;
        }

        // Send enhanced push notification with ML risk status
        await FirebaseService.sendToShopOwners(
          [debt.shopId],
          {
            title: '💰 Payment Recorded',
            body: notificationMessage,
            data: {
              type: 'payment',
              paymentId: payment.paymentId,
              debtId: debt.debtId,
              customerId: customer.customerId,
              customerName: customer.CustomerName,
              paymentAmount: payment.amount.toString(),
              customerStatus: finalCustomerStatus,
              riskScore: mlEvaluationResult ? mlEvaluationResult.riskScore.toString() : customer.riskProfile.riskScore.toString(),
              mlEvaluated: mlEvaluationResult ? 'true' : 'false',
              paymentStatus: debt.OutstandingDebt === 0 ? 'full' : 'partial',
              outstandingAmount: debt.OutstandingDebt.toString(),
              actionUrl: `deyncare://debt/${debt.debtId}`
            },
            priority: mlEvaluationResult && (mlEvaluationResult.riskLevel === 'High Risk') ? 'high' : 'normal',
            badge: 1,
            sound: 'default'
          }
        );

        console.log(`✅ Enhanced payment notification sent with ML status: ${finalCustomerStatus}`);

      } catch (backgroundError) {
        console.error('Background processing error:', backgroundError);
        
        // Fallback notification if ML fails
        try {
          const fallbackStatus = customer.riskProfile.currentRiskLevel || 'Active Debt';
          const fallbackEmoji = {
            'Low Risk': '✅',
            'Medium Risk': '⚠️', 
            'High Risk': '🚨',
            'Active Debt': '📝'
          }[fallbackStatus] || '📋';

          const paymentAmountFormatted = new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD',
            minimumFractionDigits: 0
          }).format(payment.amount);

          await FirebaseService.sendToShopOwners(
            [debt.shopId],
            {
              title: '💰 Payment Recorded',
              body: `${customer.CustomerName} paid ${paymentAmountFormatted} | Status: ${fallbackEmoji} ${fallbackStatus}`,
              data: {
                type: 'payment',
                paymentId: payment.paymentId,
                debtId: debt.debtId,
                customerId: customer.customerId,
                customerName: customer.CustomerName,
                paymentAmount: payment.amount.toString(),
                customerStatus: fallbackStatus,
                actionUrl: `deyncare://debt/${debt.debtId}`
              },
              priority: 'normal',
              badge: 1,
              sound: 'default'
            }
          );

          console.log(`✅ Fallback payment notification sent: ${fallbackStatus}`);
        } catch (fallbackError) {
          console.error('Fallback notification also failed:', fallbackError);
        }
      }
    });

  } catch (error) {
    console.error('Add payment error:', error);
    return next(new AppError('Failed to process payment', 500));
  }
};

module.exports = addPayment;