/**
 * Export Service
 * Handles all export operations for different data types
 */
import { getAccessToken } from '@/lib/api/token';

// Ensure API_BASE_URL includes /api prefix
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'https://deyncare-backend.khanciye.com/api';

class ExportService {
  constructor() {
    // Ensure baseUrl is correctly constructed
    const baseApiUrl = API_BASE_URL.endsWith('/api') ? API_BASE_URL : `${API_BASE_URL}/api`;
    this.baseUrl = `${baseApiUrl}/export`;
  }

  /**
   * Get auth headers
   */
  getAuthHeaders() {
    const token = getAccessToken();
    return {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    };
  }

  /**
   * Download file helper
   */
  async downloadFile(url, filename) {
    try {
      const response = await fetch(url, {
        headers: this.getAuthHeaders()
      });

      if (!response.ok) {
        throw new Error(`Export failed: ${response.statusText}`);
      }

      const blob = await response.blob();
      const downloadUrl = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = downloadUrl;
      link.download = filename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(downloadUrl);

      return { success: true };
    } catch (error) {
      console.error('Export error:', error);
      throw error;
    }
  }

  /**
   * Build query string from filters (including format parameter)
   */
  buildQueryString(filters) {
    const params = new URLSearchParams();
    
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        params.append(key, value);
      }
    });

    return params.toString();
  }

  // =============================================================================
  // SUPERADMIN EXPORTS
  // =============================================================================

  /**
   * Export user management data (SuperAdmin only)
   */
  async exportUsers(filters = {}, format = 'csv') {
    const queryString = this.buildQueryString({ ...filters, format });
    const url = `${this.baseUrl}/users?${queryString}`;
    const filename = filters.filename || `user_management_export_${new Date().toISOString().split('T')[0]}.${format}`;
    
    return this.downloadFile(url, filename);
  }

  /**
   * Export shop management data (SuperAdmin only)
   */
  async exportShops(filters = {}, format = 'csv') {
    const queryString = this.buildQueryString({ ...filters, format });
    const url = `${this.baseUrl}/shops?${queryString}`;
    const filename = filters.filename || `shop_management_export_${new Date().toISOString().split('T')[0]}.${format}`;
    
    return this.downloadFile(url, filename);
  }

  /**
   * Export plan management data (SuperAdmin only)
   */
  async exportPlans(filters = {}, format = 'csv') {
    const queryString = this.buildQueryString({ ...filters, format });
    const url = `${this.baseUrl}/plans?${queryString}`;
    const filename = filters.filename || `plan_management_export_${new Date().toISOString().split('T')[0]}.${format}`;
    
    return this.downloadFile(url, filename);
  }

  /**
   * Export subscription management data (SuperAdmin only)
   */
  async exportSubscriptions(filters = {}, format = 'csv') {
    const queryString = this.buildQueryString({ ...filters, format });
    const url = `${this.baseUrl}/subscriptions?${queryString}`;
    const filename = filters.filename || `subscription_management_export_${new Date().toISOString().split('T')[0]}.${format}`;
    
    return this.downloadFile(url, filename);
  }

  // =============================================================================
  // ADMIN EXPORTS
  // =============================================================================

  /**
   * Export customer data (Admin only)
   */
  async exportCustomers(filters = {}, format = 'csv') {
    const queryString = this.buildQueryString({ ...filters, format });
    const url = `${this.baseUrl}/customers?${queryString}`;
    const filename = filters.filename || `customer_export_${new Date().toISOString().split('T')[0]}.${format}`;
    
    return this.downloadFile(url, filename);
  }

  /**
   * Export debt management data (Admin only)
   */
  async exportDebts(filters = {}, format = 'csv') {
    const queryString = this.buildQueryString({ ...filters, format });
    const url = `${this.baseUrl}/debts?${queryString}`;
    const filename = filters.filename || `debt_management_export_${new Date().toISOString().split('T')[0]}.${format}`;
    
    return this.downloadFile(url, filename);
  }

  /**
   * Export ML risk assessment data (Admin only)
   */
  async exportMLRisk(filters = {}, format = 'csv') {
    const queryString = this.buildQueryString({ ...filters, format });
    const url = `${this.baseUrl}/ml-risk?${queryString}`;
    const filename = filters.filename || `ml_risk_assessment_export_${new Date().toISOString().split('T')[0]}.${format}`;
    
    return this.downloadFile(url, filename);
  }

  // =============================================================================
  // UTILITY METHODS
  // =============================================================================

  /**
   * Get available export types based on user role
   */
  getAvailableExports(userRole) {
    // Normalize role names to handle different formats
    const normalizedRole = userRole?.toLowerCase?.() || '';
    
    const exports = {
      superadmin: [
        {
          id: 'users',
          name: 'User Management',
          description: 'Export all users with roles, status, and shop information',
          icon: 'Users',
          method: 'exportUsers'
        },
        {
          id: 'shops',
          name: 'Shop Management',
          description: 'Export all shops with owner details and business information',
          icon: 'Store',
          method: 'exportShops'
        },
        {
          id: 'plans',
          name: 'Plan Management',
          description: 'Export all pricing plans with details and configurations',
          icon: 'Package',
          method: 'exportPlans'
        },
        {
          id: 'subscriptions',
          name: 'Subscription Management',
          description: 'Export all subscriptions with payment and status information',
          icon: 'CreditCard',
          method: 'exportSubscriptions'
        }
      ],
      admin: [
        {
          id: 'customers',
          name: 'Customer Reports',
          description: 'Export customer data with debt and risk information',
          icon: 'Users',
          method: 'exportCustomers'
        },
        {
          id: 'debts',
          name: 'Debt Management',
          description: 'Export debt records with payment history and status',
          icon: 'FileText',
          method: 'exportDebts'
        },
        {
          id: 'ml-risk',
          name: 'ML Risk Assessment',
          description: 'Export ML risk scores with debt and payment analysis',
          icon: 'TrendingUp',
          method: 'exportMLRisk'
        }
      ]
    };

    // Handle different role formats
    if (normalizedRole.includes('superadmin') || normalizedRole === 'superadmin') {
      return exports.superadmin;
    } else if (normalizedRole.includes('admin') || normalizedRole === 'admin') {
      return exports.admin;
    }
    
    // Fallback - return empty array
    return [];
  }

  /**
   * Get export filters for a specific export type
   */
  getExportFilters(exportType) {
    const filters = {
      users: [
        { key: 'role', label: 'Role', type: 'select', options: ['superAdmin', 'admin', 'employee'] },
        { key: 'status', label: 'Status', type: 'select', options: ['active', 'inactive', 'suspended'] },
        { key: 'emailVerified', label: 'Email Verified', type: 'boolean' },
        { key: 'isPaid', label: 'Is Paid', type: 'boolean' },
        { key: 'startDate', label: 'Start Date', type: 'date' },
        { key: 'endDate', label: 'End Date', type: 'date' }
      ],
      shops: [
        { key: 'status', label: 'Status', type: 'select', options: ['pending', 'active', 'suspended', 'inactive', 'deleted'] },
        { key: 'paymentStatus', label: 'Payment Status', type: 'select', options: ['pending', 'paid', 'failed', 'refunded'] },
        { key: 'businessType', label: 'Business Type', type: 'select', options: ['retail', 'wholesale', 'service', 'manufacturing', 'restaurant', 'other'] },
        { key: 'isActive', label: 'Is Active', type: 'boolean' },
        { key: 'startDate', label: 'Start Date', type: 'date' },
        { key: 'endDate', label: 'End Date', type: 'date' }
      ],
      plans: [
        { key: 'planType', label: 'Plan Type', type: 'select', options: ['trial', 'monthly', 'yearly'] },
        { key: 'includeInactive', label: 'Include Inactive', type: 'boolean' },
        { key: 'minPrice', label: 'Min Price', type: 'number' },
        { key: 'maxPrice', label: 'Max Price', type: 'number' }
      ],
      subscriptions: [
        { key: 'status', label: 'Status', type: 'select', options: ['active', 'expired', 'cancelled', 'pending'] },
        { key: 'planType', label: 'Plan Type', type: 'select', options: ['trial', 'monthly', 'yearly'] },
        { key: 'paymentMethod', label: 'Payment Method', type: 'select', options: ['offline', 'evc_plus', 'free'] },
        { key: 'autoRenew', label: 'Auto Renew', type: 'boolean' },
        { key: 'startDate', label: 'Start Date', type: 'date' },
        { key: 'endDate', label: 'End Date', type: 'date' }
      ],
      customers: [
        { key: 'status', label: 'Status', type: 'select', options: ['active', 'inactive', 'suspended'] },
        { key: 'riskLevel', label: 'Risk Level', type: 'select', options: ['Low Risk', 'Medium Risk', 'High Risk'] },
        { key: 'city', label: 'City', type: 'text' },
        { key: 'region', label: 'Region', type: 'text' },
        { key: 'startDate', label: 'Start Date', type: 'date' },
        { key: 'endDate', label: 'End Date', type: 'date' }
      ],
      debts: [
        { key: 'status', label: 'Status', type: 'select', options: ['active', 'paid', 'overdue', 'cancelled'] },
        { key: 'priority', label: 'Priority', type: 'select', options: ['low', 'medium', 'high', 'urgent'] },
        { key: 'riskLevel', label: 'Risk Level', type: 'select', options: ['Low Risk', 'Medium Risk', 'High Risk'] },
        { key: 'customerId', label: 'Customer ID', type: 'text' },
        { key: 'startDate', label: 'Start Date', type: 'date' },
        { key: 'endDate', label: 'End Date', type: 'date' },
        { key: 'dueStartDate', label: 'Due Start Date', type: 'date' },
        { key: 'dueEndDate', label: 'Due End Date', type: 'date' }
      ],
      'ml-risk': [
        { key: 'riskLevel', label: 'Risk Level', type: 'select', options: ['Low Risk', 'Medium Risk', 'High Risk'] },
        { key: 'minRiskScore', label: 'Min Risk Score', type: 'number', min: 0, max: 100 },
        { key: 'maxRiskScore', label: 'Max Risk Score', type: 'number', min: 0, max: 100 },
        { key: 'customerId', label: 'Customer ID', type: 'text' },
        { key: 'startDate', label: 'Start Date', type: 'date' },
        { key: 'endDate', label: 'End Date', type: 'date' }
      ]
    };

    return filters[exportType] || [];
  }
}

export default new ExportService(); 