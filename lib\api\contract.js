/**
 * DeynCare API Contract
 * 
 * This file serves as the single source of truth for all API endpoints,
 * request/response formats, and error handling between frontend and backend.
 * 
 * It provides:
 * 1. Standardized endpoint definitions
 * 2. Request schema validation that matches backend requirements
 * 3. Response format normalization
 * 4. Consistent error handling
 * 
 * UPDATED: Cleaned up non-existent endpoints and added missing payment transactions
 */
import { API_BASE_URL } from './config';

/**
 * Complete API endpoint definitions
 * Every endpoint used by the application should be defined here
 */
const ENDPOINTS = {
  // Auth endpoints
  AUTH: {
    LOGIN: '/api/auth/login',
    LOGOUT: '/api/auth/logout',
    LOGOUT_ALL: '/api/auth/logout-all',
    FORGOT_PASSWORD: '/api/auth/forgot-password',
    RESET_PASSWORD: '/api/auth/reset-password',
    CHANGE_PASSWORD: '/api/auth/change-password',
    PROFILE: '/api/auth/me',
  },
  
  // Shop endpoints
  SHOPS: {
    // SuperAdmin Shop CRUD endpoints - aligned with backend
    BASE: '/api/admin/shops',
    DETAIL: (id) => `/api/admin/shops/${id}`,
    STATS: '/api/admin/shops/stats',
    LOGO: (id) => `/api/admin/shops/${id}/logo`,
    STATUS: (id) => `/api/admin/shops/${id}/status`,
    
    // Legacy endpoints for backward compatibility (if needed)
    LEGACY_BASE: '/api/shops',
    LEGACY_DETAIL: (id) => `/api/shops/${id}`,
    LEGACY_REACTIVATE: (id) => `/api/shops/${id}/reactivate`,
    LEGACY_SUSPEND: (id) => `/api/shops/${id}/suspend`,
    LEGACY_LOGO: (id) => `/api/shops/${id}/logo`,
    LEGACY_SUBSCRIPTION_PLANS: '/api/shops/subscription-plans',
    LEGACY_STATS: '/api/shops/stats',
  },
  
  // User endpoints
  USERS: {
    BASE: '/api/users',
    DETAIL: (id) => `/api/users/${id}`,
    STATUS: (id) => `/api/users/${id}/status`,
    PROFILE: '/api/users/profile',
    STATS: '/api/users/stats',
  },
  
  // Plans endpoints (SuperAdmin only)
  PLANS: {
    BASE: '/api/plans',
    DETAIL: (id) => `/api/plans/${id}`,
    STATS: '/api/plans/stats',
    TOGGLE_STATUS: (id) => `/api/plans/${id}/toggle-status`,
    // Features management (SuperAdmin only)
    FEATURES_ALL: '/api/plans/features/all',
    FEATURES: (id) => `/api/plans/${id}/features`,
  },

  // Subscriptions endpoints
  SUBSCRIPTIONS: {
    BASE: '/api/subscriptions',
    DETAIL: (id) => `/api/subscriptions/${id}`,
    CURRENT: '/api/subscriptions/current',
    HISTORY: '/api/subscriptions/history',
    CHANGE_PLAN: '/api/subscriptions/change-plan',
    CANCEL: '/api/subscriptions/cancel',
    PAYMENT: '/api/subscriptions/payment',
    AUTO_RENEWAL: '/api/subscriptions/auto-renewal',
    RENEW: '/api/subscriptions/renew',
    REQUEST_UPGRADE: '/api/subscriptions/request-upgrade',
    // ANALYTICS: '/api/subscriptions/analytics', // ❌ Removed - not implemented in backend
    BULK: '/api/subscriptions/bulk',
    CRON_RUN: '/api/subscriptions/cron/run',
    EXTEND: (id) => `/api/subscriptions/${id}/extend`,
    STATS: '/api/subscriptions/stats',
    // Payment retry endpoints
    PAYMENT_RETRY: {
      STATUS: (id) => `/api/subscriptions/payment-retry/${id}/status`,
      TRIGGER: (id) => `/api/subscriptions/payment-retry/${id}/trigger`,
      CANCEL: (id) => `/api/subscriptions/payment-retry/${id}/cancel`,
      PROCESS_ALL: '/api/subscriptions/payment-retry/process-all',
      CONFIG: '/api/subscriptions/payment-retry/config',
    },
  },
  
  // Payment Transactions (SuperAdmin only - subscription context)
  PAYMENT_TRANSACTIONS: {
    BASE: '/api/admin/payment-transactions',
    DETAIL: (id) => `/api/admin/payment-transactions/${id}`,
    STATS: '/api/admin/payment-transactions/stats',
    EXPORT: '/api/admin/payment-transactions/export',
    APPROVE: (id) => `/api/admin/payment-transactions/${id}/approve`,
    REJECT: (id) => `/api/admin/payment-transactions/${id}/reject`,
  },
  
  // Discount endpoints
  DISCOUNTS: {
    BASE: '/api/discounts',
    DETAIL: (id) => `/api/discounts/${id}`,
    VALIDATE: '/api/discounts/validate',
    CONTEXTS: ['subscription', 'debt'],
  },
  
  // Settings endpoints
  SETTINGS: {
    BASE: '/api/settings',
    DETAIL: (key) => `/api/settings/${key}`,
    PAYMENT_METHODS: '/api/settings/payment-methods',
    EVC_CREDENTIALS: '/api/settings/evc-credentials',
    TEST_EVC_CREDENTIALS: '/api/settings/test-evc-credentials',
  },
  
  // Notification endpoints (Backend-matched)
  NOTIFICATIONS: {
    // Push notifications (SuperAdmin only)
    PUSH: {
      BASE: '/api/admin/notifications/push',
      TO_SHOPS: '/api/admin/notifications/push/shops',
      BROADCAST: '/api/admin/notifications/push/broadcast',
      DEBT_REMINDERS: '/api/admin/notifications/push/debt-reminders',
      STATS: '/api/admin/notifications/push/stats',
      TEST: '/api/admin/notifications/push/test',
      TARGETS: '/api/admin/notifications/push/targets',
      HISTORY: '/api/admin/notifications/push/history',
    },
    // FCM token management (ONLY existing backend routes)
    FCM: {
      BASE: '/api/fcm',
      REGISTER: '/api/fcm/register',
      TEST: '/api/fcm/test',
    },
  },
  
  // Reports endpoints
  REPORTS: {
    BASE: '/api/reports',
    USERS: '/api/reports/users',
    SUBSCRIPTIONS: '/api/reports/subscriptions',
  },
  
  // Payments endpoints
  PAYMENTS: {
    BASE: '/api/payments',
    DETAIL: (id) => `/api/payments/${id}`,
    VALIDATE: '/api/payments/validate',
  },
};

/**
 * Request validation schemas that match backend requirements
 * Use these to validate frontend form data before submission
 */
const REQUEST_SCHEMAS = {
  // User schemas
  USERS: {
    CREATE: {
      required: ['fullName', 'email', 'phone', 'password', 'role'],
      conditional: {
        // SuperAdmin users must have a shopId - no "No Shop" option
        role: {
          'superAdmin': ['shopId'] 
        }
      },
      properties: {
        fullName: { type: 'string', minLength: 3, maxLength: 100 },
        email: { type: 'string', format: 'email' },
        phone: { type: 'string', pattern: '^[+]?[0-9]{10,15}$' },
        password: { type: 'string', minLength: 8 },
        role: { type: 'string', enum: ['superAdmin', 'admin', 'employee'] },
        shopId: { type: 'string' },
        isActive: { type: 'boolean', default: true }
      }
    },
    UPDATE: {
      properties: {
        fullName: { type: 'string', minLength: 3, maxLength: 100 },
        phone: { type: 'string', pattern: '^[+]?[0-9]{10,15}$' },
        role: { type: 'string', enum: ['superAdmin', 'admin', 'employee'] },
        isActive: { type: 'boolean' }
      }
    },
    CHANGE_STATUS: {
      required: ['status'],
      conditional: {
        status: {
          'suspended': ['reason']
        }
      },
      properties: {
        status: { type: 'string', enum: ['active', 'inactive', 'suspended'] },
        reason: { type: 'string', minLength: 5, maxLength: 200 }
      }
    },
    DELETE: {
      required: ['reason'],
      properties: {
        reason: { type: 'string', minLength: 5, maxLength: 200 }
      }
    }
  },
  
  // Shop schemas
  SHOPS: {
    CREATE: {
      required: ['name', 'email', 'phone', 'address'],
      properties: {
        name: { type: 'string', minLength: 3, maxLength: 100 },
        email: { type: 'string', format: 'email' },
        phone: { type: 'string', pattern: '^[+]?[0-9]{10,15}$' },
        address: { type: 'string', minLength: 5, maxLength: 200 },
        logo: { type: 'string' },
        isActive: { type: 'boolean', default: true }
      }
    },
    UPDATE: {
      properties: {
        name: { type: 'string', minLength: 3, maxLength: 100 },
        email: { type: 'string', format: 'email' },
        phone: { type: 'string', pattern: '^[+]?[0-9]{10,15}$' },
        address: { type: 'string', minLength: 5, maxLength: 200 },
        logo: { type: 'string' }
      }
    }
  },
  
  // Settings schemas
  SETTINGS: {
    UPDATE_GENERAL: {
      required: ['settings'],
      properties: {
        settings: { type: 'object' }
      }
    },
    UPDATE_SECURITY: {
      properties: {
        passwordPolicy: { type: 'object' },
        sessionSettings: { type: 'object' },
        securityHeaders: { type: 'object' }
      }
    },
    UPDATE_BY_KEY: {
      required: ['value'],
      properties: {
        value: { type: 'any' },
        shopId: { type: 'string' }
      }
    },
    EVC_CREDENTIALS: {
      required: ['merchantUId', 'apiUserId', 'apiKey', 'merchantNo'],
      properties: {
        merchantUId: { type: 'string', minLength: 1 },
        apiUserId: { type: 'string', minLength: 1 },
        apiKey: { type: 'string', minLength: 1 },
        merchantNo: { type: 'string', minLength: 1 },
        url: { type: 'string', format: 'url' },
        shopId: { type: 'string' }
      }
    },
    TEST_EVC_CREDENTIALS: {
      properties: {
        shopId: { type: 'string' },
        phone: { type: 'string', pattern: '^[+]?[0-9]{10,15}$' },
        amount: { type: 'number', minimum: 0.1 }
      }
    }
  },
  
  // Discount schemas
  DISCOUNTS: {
    CREATE: {
      required: ['code', 'discountType', 'value', 'applicableFor'],
      properties: {
        code: { type: 'string', minLength: 3, maxLength: 20 },
        discountType: { type: 'string', enum: ['fixed', 'percentage'] },
        value: { type: 'number', minimum: 0 },
        maxUsage: { type: 'number', minimum: 1 },
        maxUsagePerUser: { type: 'number', minimum: 1 },
        expiryDate: { type: 'string', format: 'date-time' },
        startDate: { type: 'string', format: 'date-time' },
        minimumPurchase: { type: 'number', minimum: 0 },
        applicableFor: { 
          type: 'array', 
          items: { type: 'string', enum: ['subscription', 'debt'] },
          default: ['subscription', 'debt']
        },
        shopId: { type: 'string' }
      }
    }
  },
  
  // Plans schemas (SuperAdmin only)
  PLANS: {
    CREATE: {
      required: ['name', 'type', 'displayName', 'pricing'],
      properties: {
        name: { type: 'string', maxLength: 50, trim: true },
        type: { type: 'string', enum: ['trial', 'monthly', 'yearly'] },
        displayName: { type: 'string', maxLength: 100, trim: true },
        description: { type: 'string', maxLength: 500, allowEmpty: true },
        pricing: {
          type: 'object',
          required: ['basePrice', 'billingCycle'],
          properties: {
            basePrice: { type: 'number', minimum: 0 },
            currency: { type: 'string', maxLength: 3, default: 'USD' },
            billingCycle: { type: 'string', enum: ['one-time', 'monthly', 'yearly'] },
            trialDays: { type: 'number', minimum: 0, default: 0 },
            setupFee: { type: 'number', minimum: 0, default: 0 }
          }
        },
        features: {
          type: 'object',
          properties: {
            debtTracking: { type: 'boolean', default: true },
            customerPayments: { type: 'boolean', default: true },
            smsReminders: { type: 'boolean', default: true },
            smartRiskScore: { type: 'boolean', default: true },
            businessDashboard: { type: 'boolean', default: true },
            exportReports: { type: 'boolean', default: true },
            customerProfiles: { type: 'boolean', default: true },
            offlineSupport: { type: 'boolean', default: true }
          }
        },
        limits: {
          type: 'object',
          properties: {
            maxProducts: { type: 'number', minimum: 1, integer: true, default: 1000 },
            maxEmployees: { type: 'number', minimum: 1, integer: true, default: 10 },
            maxStorageMB: { type: 'number', minimum: 1, integer: true, default: 500 },
            maxCustomers: { type: 'number', minimum: 1, integer: true, default: 1000 },
            maxDailyTransactions: { type: 'number', minimum: 1, integer: true, default: 500 }
          }
        },
        isActive: { type: 'boolean', default: true },
        displayOrder: { type: 'number', integer: true, default: 1 },
        metadata: {
          type: 'object',
          properties: {
            isRecommended: { type: 'boolean', default: false },
            tags: { type: 'array', items: { type: 'string' } },
            customFields: { type: 'object' }
          },
          default: {}
        }
      }
    },
    UPDATE: {
      properties: {
        name: { type: 'string', maxLength: 50, trim: true },
        displayName: { type: 'string', maxLength: 100, trim: true },
        description: { type: 'string', maxLength: 500, allowEmpty: true },
        pricing: {
          type: 'object',
          properties: {
            basePrice: { type: 'number', minimum: 0 },
            currency: { type: 'string', maxLength: 3 },
            billingCycle: { type: 'string', enum: ['one-time', 'monthly', 'yearly'] },
            trialDays: { type: 'number', minimum: 0 },
            setupFee: { type: 'number', minimum: 0 }
          }
        },
        features: {
          type: 'object',
          properties: {
            debtTracking: { type: 'boolean' },
            customerPayments: { type: 'boolean' },
            smsReminders: { type: 'boolean' },
            smartRiskScore: { type: 'boolean' },
            businessDashboard: { type: 'boolean' },
            exportReports: { type: 'boolean' },
            customerProfiles: { type: 'boolean' },
            offlineSupport: { type: 'boolean' }
          }
        },
        limits: {
          type: 'object',
          properties: {
            maxProducts: { type: 'number', minimum: 1, integer: true },
            maxEmployees: { type: 'number', minimum: 1, integer: true },
            maxStorageMB: { type: 'number', minimum: 1, integer: true },
            maxCustomers: { type: 'number', minimum: 1, integer: true },
            maxDailyTransactions: { type: 'number', minimum: 1, integer: true }
          }
        },
        isActive: { type: 'boolean' },
        displayOrder: { type: 'number', integer: true },
        metadata: {
          type: 'object',
          properties: {
            isRecommended: { type: 'boolean' },
            tags: { type: 'array', items: { type: 'string' } },
            customFields: { type: 'object' }
          }
        }
      }
    }
  },

  // Notification schemas
  NOTIFICATIONS: {
    SEND_TO_SHOPS: {
      required: ['shopIds', 'title', 'message'],
      properties: {
        shopIds: { type: 'array', items: { type: 'string' }, minItems: 1 },
        title: { type: 'string', minLength: 1, maxLength: 100 },
        message: { type: 'string', minLength: 1, maxLength: 500 },
        priority: { type: 'string', enum: ['low', 'normal', 'high'], default: 'normal' },
        actionUrl: { type: 'string', format: 'uri' },
        actionLabel: { type: 'string', maxLength: 50 },
        scheduledAt: { type: 'string', format: 'date-time' }
      }
    },
    BROADCAST: {
      required: ['title', 'message'],
      properties: {
        title: { type: 'string', minLength: 1, maxLength: 100 },
        message: { type: 'string', minLength: 1, maxLength: 500 },
        priority: { type: 'string', enum: ['low', 'normal', 'high'], default: 'normal' },
        actionUrl: { type: 'string', format: 'uri' },
        actionLabel: { type: 'string', maxLength: 50 },
        scheduledAt: { type: 'string', format: 'date-time' }
      }
    },
    DEBT_REMINDERS: {
      properties: {
        shopIds: { type: 'array', items: { type: 'string' } },
        reminderType: { type: 'string', enum: ['7_days', '3_days', 'overdue'], default: 'overdue' },
        daysOverdue: { type: 'number', minimum: 0 },
        priority: { type: 'string', enum: ['low', 'normal', 'high'], default: 'normal' }
      }
    },
    FCM_REGISTER: {
      required: ['token'],
      properties: {
        token: { type: 'string', minLength: 1 },
        deviceInfo: {
          type: 'object',
          properties: {
            deviceId: { type: 'string' },
            platform: { type: 'string', enum: ['android', 'ios'] },
            appVersion: { type: 'string' },
            osVersion: { type: 'string' }
          }
        }
      }
    },
    FCM_TEST: {
      properties: {}
    }
  },

  // Enhanced Subscriptions schemas
  SUBSCRIPTIONS: {
    CREATE: {
      required: ['shopId', 'planType', 'paymentMethod'],
      conditional: {
        paymentMethod: {
          'offline': ['paymentDetails'],
          'evc_plus': ['paymentDetails']
        }
      },
      properties: {
        shopId: { type: 'string' },
        planId: { type: 'string' },
        planType: { type: 'string', enum: ['trial', 'monthly', 'yearly'] },
        paymentMethod: { type: 'string', enum: ['offline', 'evc_plus', 'free'] },
        paymentDetails: {
          type: 'object',
          properties: {
            transactionId: { type: 'string' },
            amount: { type: 'number', minimum: 0.01 },
            receiptUrl: { type: 'string', format: 'uri' },
            payerName: { type: 'string' },
            payerPhone: { type: 'string', pattern: '^[+]?[0-9]{10,15}$' },
            payerEmail: { type: 'string', format: 'email' },
            notes: { type: 'string', maxLength: 500 }
          }
        }
      }
    },
    CHANGE_PLAN: {
      required: ['planType'],
      properties: {
        planId: { type: 'string' },
        planType: { type: 'string', enum: ['monthly', 'yearly'] },
        prorated: { type: 'boolean', default: true },
        paymentMethod: { type: 'string', enum: ['offline', 'evc_plus'] },
        paymentDetails: {
          type: 'object',
          properties: {
            transactionId: { type: 'string' },
            amount: { type: 'number', minimum: 0.01 },
            notes: { type: 'string', maxLength: 500 }
          }
        }
      }
    },
    CANCEL: {
      properties: {
        reason: { type: 'string', maxLength: 500 },
        feedback: { type: 'string', maxLength: 1000 },
        immediateEffect: { type: 'boolean', default: false }
      }
    },
    PAYMENT: {
      required: ['amount', 'transactionId', 'paymentMethod'],
      properties: {
        amount: { type: 'number', minimum: 0.01 },
        transactionId: { type: 'string' },
        paymentMethod: { type: 'string', enum: ['offline', 'evc_plus'] },
        currency: { type: 'string', default: 'USD' },
        notes: { type: 'string', maxLength: 500 }
      }
    },
    AUTO_RENEWAL: {
      required: ['autoRenew'],
      properties: {
        autoRenew: { type: 'boolean' }
      }
    },
    RENEW: {
      required: ['paymentMethod', 'transactionId'],
      properties: {
        paymentMethod: { type: 'string', enum: ['offline', 'evc_plus'] },
        transactionId: { type: 'string' },
        amount: { type: 'number', minimum: 0.01 },
        currency: { type: 'string', default: 'USD' },
        notes: { type: 'string', maxLength: 500 }
      }
    },
    EXTEND: {
      required: ['days'],
      properties: {
        days: { type: 'number', minimum: 1 },
        reason: { type: 'string', maxLength: 500 }
      }
    }
  },

  // Payment Transaction schemas (SuperAdmin only)
  PAYMENT_TRANSACTIONS: {
    GET_ALL: {
      properties: {
        page: { type: 'number', minimum: 1 },
        limit: { type: 'number', minimum: 1, maximum: 100 },
        status: { type: 'string', enum: ['pending', 'approved', 'rejected'] },
        shopId: { type: 'string' },
        startDate: { type: 'string', format: 'date' },
        endDate: { type: 'string', format: 'date' }
      }
    },
    APPROVE: {
      required: ['adminNote'],
      properties: {
        adminNote: { type: 'string', minLength: 1, maxLength: 500 }
      }
    },
    REJECT: {
      required: ['adminNote'],
      properties: {
        adminNote: { type: 'string', minLength: 1, maxLength: 500 }
      }
    }
  }
};

/**
 * Response formatting utilities
 */
const responseFormatters = {
  formatShopResponse: (response) => {
    if (!response?.data?.success) return null;
    
    const data = response.data.data;
    
    if (data.shops) {
      return {
        shops: data.shops,
        pagination: data.pagination || {
          currentPage: 1,
          totalPages: 1,
          total: data.shops.length
        }
      };
    }
    
    if (Array.isArray(data)) {
      return {
        shops: data,
        pagination: {
          currentPage: 1,
          totalPages: 1,
          total: data.length
        }
      };
    }
    
    if (data.shop) {
      return { shop: data.shop };
    }
    
    if (data && typeof data === 'object' && !Array.isArray(data)) {
      return { shop: data };
    }
    
    return null;
  },
  
  formatUserResponse: (response) => {
    if (!response?.data?.success) return null;
    
    const data = response.data.data;
    
    if (data.users) {
      return {
        users: data.users,
        pagination: data.pagination || {
          currentPage: 1,
          totalPages: 1,
          total: data.users.length
        }
      };
    }
    
    if (Array.isArray(data)) {
      return {
        users: data,
        pagination: {
          currentPage: 1,
          totalPages: 1,
          total: data.length
        }
      };
    }
    
    if (data.user) {
      return { user: data.user };
    }
    
    if (data && typeof data === 'object' && !Array.isArray(data)) {
      return { user: data };
    }
    
    return null;
  },
  
  formatGenericResponse: (response, entityType) => {
    if (!response?.data?.success) return null;
    
    const data = response.data.data;
    const pluralKey = entityType.endsWith('s') ? entityType : `${entityType}s`;
    const singularKey = entityType.endsWith('s') ? entityType.slice(0, -1) : entityType;
    
    if (data[pluralKey]) {
      return {
        [pluralKey]: data[pluralKey],
        pagination: data.pagination || {
          currentPage: 1,
          totalPages: 1,
          total: data[pluralKey].length
        }
      };
    }
    
    if (Array.isArray(data)) {
      return {
        [pluralKey]: data,
        pagination: {
          currentPage: 1,
          totalPages: 1,
          total: data.length
        }
      };
    }
    
    if (data[singularKey]) {
      return { [singularKey]: data[singularKey] };
    }
    
    if (data && typeof data === 'object' && !Array.isArray(data)) {
      return { [singularKey]: data };
    }
    
    return null;
  }
};

/**
 * Error handling utilities
 */
const errorHandlers = {
  getErrorMessage: (error) => {
    if (error.response?.data?.message) {
      return error.response.data.message;
    }
    
    if (error.response?.data?.error) {
      return error.response.data.error;
    }
    
    if (!error.response && error.request) {
      return 'Network error. Please check your internet connection.';
    }
    
    if (error.message) {
      return error.message;
    }
    
    return 'An unknown error occurred';
  },
  
  isNetworkError: (error) => !error.response && error.request,
  
  isAuthError: (error) => error.response?.status === 401 || error.response?.status === 403,
  
  isValidationError: (error) => error.response?.status === 400 || error.response?.status === 422,
  
  isPermissionError: (error) => {
    return error.response?.status === 403 && 
           (error.response?.data?.message?.includes('SuperAdmin') ||
            error.response?.data?.message?.includes('permission'));
  },
  
  formatValidationErrors: (error) => {
    if (error.response?.data?.errors) {
      return error.response.data.errors;
    }
    
    if (error.response?.data?.validationErrors) {
      return error.response.data.validationErrors;
    }
    
    return null;
  }
};

export {
  API_BASE_URL,
  ENDPOINTS,
  REQUEST_SCHEMAS,
  responseFormatters,
  errorHandlers
};
