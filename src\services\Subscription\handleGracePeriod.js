/**
 * Handle Grace Period Service
 * Manages grace period logic for expired subscriptions
 */
const { Subscription } = require('../../models');
const { logError, logSuccess, logInfo } = require('../../utils');

/**
 * Check if subscription is in grace period
 * @param {Object} subscription - The subscription object
 * @param {Number} gracePeriodDays - Number of grace period days (default: 30)
 * @returns {Object} Grace period status
 */
const getGracePeriodStatus = (subscription, gracePeriodDays = 30) => {
  try {
    const now = new Date();
    const endDate = new Date(subscription.dates?.endDate || subscription.endDate);
    const gracePeriodEnd = new Date(endDate.getTime() + (gracePeriodDays * 24 * 60 * 60 * 1000));
    
    const isExpired = endDate < now;
    const isInGracePeriod = isExpired && now <= gracePeriodEnd;
    const gracePeriodDaysLeft = isInGracePeriod ? 
      Math.ceil((gracePeriodEnd - now) / (1000 * 60 * 60 * 24)) : 0;
    
    return {
      isExpired,
      isInGracePeriod,
      gracePeriodEnd,
      gracePeriodDaysLeft,
      shouldDeactivate: isExpired && !isInGracePeriod
    };
  } catch (error) {
    logError('Error calculating grace period status', 'SubscriptionService', error);
    return {
      isExpired: true,
      isInGracePeriod: false,
      gracePeriodEnd: new Date(),
      gracePeriodDaysLeft: 0,
      shouldDeactivate: true
    };
  }
};

/**
 * Apply grace period to expired subscription
 * @param {String} subscriptionId - The subscription ID
 * @param {Number} gracePeriodDays - Number of grace period days
 * @param {Object} actor - Actor performing the action
 * @returns {Promise<Object>} Updated subscription
 */
const applyGracePeriod = async (subscriptionId, gracePeriodDays = 30, actor = {}) => {
  try {
    const { actorId = 'system', actorRole = 'system' } = actor;
    
    const subscription = await Subscription.findById(subscriptionId);
    
    if (!subscription) {
      throw new Error(`Subscription not found: ${subscriptionId}`);
    }
    
    const gracePeriodStatus = getGracePeriodStatus(subscription, gracePeriodDays);
    
    if (!gracePeriodStatus.isExpired) {
      logInfo(`Subscription ${subscriptionId} has not expired yet`, 'SubscriptionService');
      return subscription;
    }
    
    // Update subscription with grace period information
    const updatedSubscription = await Subscription.findByIdAndUpdate(
      subscriptionId,
      {
        $set: {
          status: 'grace_period',
          'gracePeriod.isActive': true,
          'gracePeriod.startDate': new Date(subscription.dates?.endDate || subscription.endDate),
          'gracePeriod.endDate': gracePeriodStatus.gracePeriodEnd,
          'gracePeriod.daysRemaining': gracePeriodStatus.gracePeriodDaysLeft,
          'gracePeriod.appliedBy': actorId,
          'gracePeriod.appliedAt': new Date(),
          'access.isRestricted': true,
          'access.restrictionLevel': 'grace_period',
          'access.restrictedAt': new Date(),
          'meta.lastModifiedBy': actorId,
          'meta.lastModifiedAt': new Date()
        },
        $push: {
          'meta.statusHistory': {
            status: 'grace_period',
            changedAt: new Date(),
            changedBy: actorId,
            reason: `Grace period applied for ${gracePeriodDays} days`
          }
        }
      },
      { new: true, runValidators: true }
    );
    
    logSuccess(`Applied grace period to subscription: ${subscriptionId}`, 'SubscriptionService');
    
    return updatedSubscription;
  } catch (error) {
    logError('Failed to apply grace period', 'SubscriptionService', error);
    throw error;
  }
};

/**
 * Get subscriptions in grace period that are about to expire
 * @param {Number} daysAhead - Number of days to look ahead (default: 2)
 * @returns {Promise<Array>} Array of subscriptions in grace period ending soon
 */
const getGracePeriodEndingSoon = async (daysAhead = 2) => {
  try {
    const now = new Date();
    const futureDate = new Date();
    futureDate.setDate(now.getDate() + daysAhead);
    
    const gracePeriodEnding = await Subscription.find({
      status: 'grace_period',
      'gracePeriod.isActive': true,
      'gracePeriod.endDate': {
        $gte: now,
        $lte: futureDate
      },
      isDeleted: false
    }).lean();
    
    return gracePeriodEnding;
  } catch (error) {
    logError('Failed to get grace period ending soon', 'SubscriptionService', error);
    throw error;
  }
};

/**
 * Finalize expired grace periods (permanently deactivate)
 * @returns {Promise<Array>} Array of finalized subscriptions
 */
const finalizeExpiredGracePeriods = async () => {
  try {
    const now = new Date();
    
    const expiredGracePeriods = await Subscription.find({
      status: 'grace_period',
      'gracePeriod.isActive': true,
      'gracePeriod.endDate': { $lt: now },
      isDeleted: false
    });
    
    const finalizedSubscriptions = [];
    
    for (const subscription of expiredGracePeriods) {
      try {
        const finalized = await Subscription.findByIdAndUpdate(
          subscription._id,
          {
            $set: {
              status: 'expired',
              'gracePeriod.isActive': false,
              'gracePeriod.finalizedAt': new Date(),
              'access.isActive': false,
              'access.restrictionLevel': 'full',
              'meta.lastModifiedBy': 'system',
              'meta.lastModifiedAt': new Date()
            },
            $push: {
              'meta.statusHistory': {
                status: 'expired',
                changedAt: new Date(),
                changedBy: 'system',
                reason: 'Grace period expired - final deactivation'
              }
            }
          },
          { new: true, runValidators: true }
        );
        
        finalizedSubscriptions.push(finalized);
        logSuccess(`Finalized expired grace period for subscription: ${subscription._id}`, 'SubscriptionService');
      } catch (error) {
        logError(`Failed to finalize grace period for subscription: ${subscription._id}`, 'SubscriptionService', error);
      }
    }
    
    return finalizedSubscriptions;
  } catch (error) {
    logError('Failed to finalize expired grace periods', 'SubscriptionService', error);
    throw error;
  }
};

module.exports = {
  getGracePeriodStatus,
  applyGracePeriod,
  getGracePeriodEndingSoon,
  finalizeExpiredGracePeriods
}; 