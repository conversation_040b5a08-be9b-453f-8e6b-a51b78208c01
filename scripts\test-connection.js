/**
 * Test script to verify API connection
 * Run with: node scripts/test-connection.js
 */

const readline = require('readline');

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// Test API connection
async function testAPIConnection() {
  console.log('🔍 Testing API Connection...\n');
  
  // Test development environment
  const devURL = 'http://localhost:5000';
  console.log(`Testing Development URL: ${devURL}`);
  
  try {
    const response = await fetch(`${devURL}/api/health`);
    if (response.ok) {
      console.log('✅ Development API is accessible');
    } else {
      console.log('❌ Development API is not responding correctly');
    }
  } catch (error) {
    console.log('❌ Development API is not accessible:', error.message);
  }
  
  // Test production environment
  const prodURL = 'https://deyncare-backend.khanciye.com';
  console.log(`\nTesting Production URL: ${prodURL}`);
  
  try {
    const response = await fetch(`${prodURL}/api/health`);
    if (response.ok) {
      console.log('✅ Production API is accessible');
    } else {
      console.log('❌ Production API is not responding correctly');
    }
  } catch (error) {
    console.log('❌ Production API is not accessible:', error.message);
  }
  
  console.log('\n📋 Environment Configuration:');
  console.log(`NODE_ENV: ${process.env.NODE_ENV || 'development'}`);
  console.log(`NEXT_PUBLIC_API_URL: ${process.env.NEXT_PUBLIC_API_URL || 'Not set'}`);
  
  console.log('\n💡 Recommendations:');
  console.log('For development: Set NEXT_PUBLIC_API_URL=http://localhost:5000');
  console.log('For production: Set NEXT_PUBLIC_API_URL=https://deyncare-backend.khanciye.com');
}

// Check if fetch is available (Node.js 18+)
if (typeof fetch === 'undefined') {
  console.log('❌ fetch is not available. Please use Node.js 18+ or install node-fetch');
  process.exit(1);
}

testAPIConnection().then(() => {
  rl.close();
}).catch(error => {
  console.error('Error testing connection:', error);
  rl.close();
}); 