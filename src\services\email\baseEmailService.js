const nodemailer = require('nodemailer');
const path = require('path');
const fs = require('fs');
const { logInfo, logError, logSuccess, logDebug } = require('../../utils/logger');
const AppError = require('../../utils/core/AppError');

/**
 * Base Email Service class for handling transactional emails and template rendering
 * Modified singleton pattern to allow inheritance by specialized services
 */
class BaseEmailService {
  constructor() {
    // Allow inheritance - only prevent multiple instances of the same class
    const className = this.constructor.name;
    
    // Check if we already have an instance of this specific class
    if (BaseEmailService.instances && BaseEmailService.instances[className]) {
      return BaseEmailService.instances[className];
    }
    
    // Initialize instances object if it doesn't exist
    if (!BaseEmailService.instances) {
      BaseEmailService.instances = {};
    }
    
    // Email transport settings
    this.transporter = null;
    this.initialized = false;
    this.sender = '';
    this.initPromise = null; // Track initialization promise
    
    // Template settings
    this.templatesDir = path.join(__dirname, '../../templates/emails');
    this.templates = {};
    this.templatesLoaded = false;
    
    // Store this instance
    BaseEmailService.instances[className] = this;
    
    // Initialize asynchronously to avoid blocking
    this.initialize();
  }

  /**
   * Initialize the email service (async to prevent blocking)
   */
  async initialize() {
    if (this.initPromise) {
      return this.initPromise;
    }
    
    this.initPromise = this._performInitialization();
    return this.initPromise;
  }

  /**
   * Perform the actual initialization
   */
  async _performInitialization() {
    try {
      await this.init();
      await this.loadTemplates();
      logSuccess('Email service initialized successfully', 'EmailService');
    } catch (error) {
      logError('Failed to initialize email service', 'EmailService', error);
    }
  }

  /**
   * Initialize the email transporter with optimized settings
   */
  init() {
    try {
      // Only log once during startup and only in development
      if (!this.initialized && process.env.NODE_ENV === 'development') {
        logDebug(`Initializing email service with ${process.env.EMAIL_HOST}:${process.env.EMAIL_PORT}`, 'EmailService');
      }
      
      // Create a transporter with optimized email settings
      this.transporter = nodemailer.createTransport({
        host: process.env.EMAIL_HOST,
        port: process.env.EMAIL_PORT,
        secure: parseInt(process.env.EMAIL_PORT) === 465, // Secure if port is 465
        auth: {
          user: process.env.EMAIL_USER,
          pass: process.env.EMAIL_PASS
        },
        // Optimized connection settings
        pool: true,                    // Use connection pooling
        maxConnections: 5,             // Max concurrent connections
        maxMessages: 100,              // Max messages per connection
        rateLimit: 10,                 // Max messages per second
        connectionTimeout: 5000,       // 5 second connection timeout
        greetingTimeout: 5000,         // 5 second greeting timeout
        socketTimeout: 10000,          // 10 second socket timeout
        // Reduce debug output
        debug: false,
        logger: false
      });

      this.sender = process.env.EMAIL_FROM || '<EMAIL>';
      this.initialized = true;
    } catch (error) {
      logError('Failed to initialize email transporter', 'EmailService', error);
      this.initialized = false;
    }
  }

  /**
   * Send an email with timeout handling
   * @param {string} to - Recipient email
   * @param {string} subject - Email subject
   * @param {string} html - Email HTML content
   * @returns {Promise<boolean>} - Success status
   */
  async sendMail(to, subject, html) {
    // Ensure service is initialized
    await this.initialize();
    
    if (!this.initialized) {
      throw new AppError('Email service is not available', 500, 'service_unavailable');
    }

    try {
      const mailOptions = {
        from: this.sender,
        to,
        subject,
        html
      };

      // Add timeout to email sending
      const emailPromise = this.transporter.sendMail(mailOptions);
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('Email sending timeout')), 15000); // 15 second timeout
      });

      await Promise.race([emailPromise, timeoutPromise]);
      return true;
    } catch (error) {
      logError(`Failed to send email to ${to}: ${error.message}`, 'EmailService');
      throw new AppError('Failed to send email', 500, 'email_error');
    }
  }

  /**
   * Load template files from the templates directory
   */
  async loadTemplates() {
    if (this.templatesLoaded) {
      return;
    }
    
    try {
      // Check if templates directory exists
      if (!fs.existsSync(this.templatesDir)) {
        logError('Templates directory does not exist', 'EmailService');
        return;
      }

      // Find all directory structure recursively
      const count = this.scanTemplateDirectory(this.templatesDir, '');
      this.templatesLoaded = true;
      
      // Only log template count in development
      if (process.env.NODE_ENV === 'development') {
        logDebug(`Loaded ${count} email templates`, 'EmailService');
      }
    } catch (error) {
      logError('Failed to load email templates', 'EmailService', error);
    }
  }

  /**
   * Recursively scan template directory structure
   * @param {string} dir - Directory to scan
   * @param {string} prefix - Prefix for template keys
   * @returns {number} - Number of templates loaded
   */
  scanTemplateDirectory(dir, prefix) {
    let count = 0;
    
    try {
      const items = fs.readdirSync(dir);
      
      for (const item of items) {
        const fullPath = path.join(dir, item);
        const stat = fs.statSync(fullPath);
        
        if (stat.isDirectory()) {
          // Recursively scan subdirectories
          const subPrefix = prefix ? `${prefix}/${item}` : item;
          count += this.scanTemplateDirectory(fullPath, subPrefix);
        } else if (item.endsWith('.html')) {
          // Load template file
          const templateName = item.replace('.html', '');
          const templateKey = prefix ? `${prefix}/${templateName}` : templateName;
          
          try {
            const templateContent = fs.readFileSync(fullPath, 'utf8');
            this.templates[templateKey] = templateContent;
            count++;
          } catch (readError) {
            logError(`Failed to read template: ${templateKey}`, 'EmailService', readError);
          }
        }
      }
    } catch (error) {
      logError(`Failed to scan directory: ${dir}`, 'EmailService', error);
    }
    
    return count;
  }

  /**
   * Render a template with provided data
   * @param {string} templateKey - Template key (e.g., 'Auth/verification')
   * @param {Object} data - Data to inject into the template
   * @returns {string} - Rendered HTML content
   */
  renderTemplate(templateKey, data = {}) {
    try {
      // Ensure templates are loaded
      if (!this.templatesLoaded) {
        logError('Templates not loaded yet', 'EmailService');
        throw new AppError('Email templates not available', 500, 'template_error');
      }
      
      // Check if template exists
      if (!this.templates[templateKey]) {
        logError(`Template '${templateKey}' not found. Available: ${Object.keys(this.templates).join(', ')}`, 'EmailService');
        throw new AppError(`Email template '${templateKey}' not found`, 404, 'template_not_found');
      }
      
      let html = this.templates[templateKey];
      
      // Simple template rendering (replace {{key}} with data values)
      for (const [key, value] of Object.entries(data)) {
        const placeholder = new RegExp(`{{${key}}}`, 'g');
        html = html.replace(placeholder, value || '');
      }
      
      return html;
    } catch (error) {
      logError(`Template rendering failed for '${templateKey}'`, 'EmailService', error);
      throw error;
    }
  }
  
  /**
   * Verify SMTP connection
   * @returns {Promise<boolean>} - Connection status
   */
  async verifyConnection() {
    await this.initialize();
    
    if (!this.initialized) {
      return false;
    }

    try {
      await this.transporter.verify();
      
      // Only log connection verification in development
      if (process.env.NODE_ENV === 'development') {
        logDebug('Email service connection verified', 'EmailService');
      }
      return true;
    } catch (error) {
      logError('Email service connection verification failed', 'EmailService', error);
      return false;
    }
  }

  /**
   * Send an email with support for templates, multiple recipients, and attachments
   * @param {Object} options - Email options
   * @param {string|Array<string>} options.to - Recipient email(s)
   * @param {string} options.subject - Email subject
   * @param {string} options.template - Template key (e.g., 'Report/report-delivery')
   * @param {Object} options.data - Data to inject into the template
   * @param {Array<Object>} options.attachments - Array of attachment objects
   * @returns {Promise<boolean>} - Success status
   */
  async sendEmail(options) {
    // Ensure service is initialized
    await this.initialize();
    
    if (!this.initialized) {
      throw new AppError('Email service is not available', 500, 'service_unavailable');
    }

    try {
      const { to, subject, template, data = {}, attachments = [] } = options;
      
      // Convert recipients to array if string
      const recipients = Array.isArray(to) ? to : [to];
      
      // Render HTML content from template
      const html = this.renderTemplate(template, data);
      
      // Prepare mail options
      const mailOptions = {
        from: this.sender,
        to: recipients.join(', '),
        subject,
        html
      };
      
      // Add attachments if provided
      if (attachments && attachments.length > 0) {
        mailOptions.attachments = attachments;
      }

      // Send the email
      await this.transporter.sendMail(mailOptions);
      
      logSuccess(`Email sent to ${mailOptions.to}`, 'EmailService');
      return true;
    } catch (error) {
      logError(`Failed to send email: ${error.message}`, 'EmailService', error);
      throw new AppError('Failed to send email', 500, 'email_error');
    }
  }
}

// Create and export singleton instance
module.exports = BaseEmailService;
