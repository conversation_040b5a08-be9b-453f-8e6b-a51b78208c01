# System Workflows - Updated with Actual Implementation

## Admin Dashboard Workflow

The administrative interface provides secure access and comprehensive system management through Next.js web application.

### Admin Dashboard Functional Areas:

#### 1. Authentication & Authorization
- **JWT Token Authentication**: Secure login with access/refresh token pattern
- **Role-Based Access Control**: SuperAdmin and Admin role validation
- **Session Management**: Automatic token refresh and session validation
- **Multi-tenant Security**: Shop-based data isolation and access control

#### 2. Dashboard Overview
- **Real-time Statistics**: User, shop, subscription, and payment metrics
- **System Health Monitoring**: Performance and status indicators  
- **Parallel Data Fetching**: Optimized API calls for dashboard components
- **Auto-refresh Capability**: 5-minute interval updates with manual refresh option

#### 3. User Management
- **User Statistics**: Total users, active users, monthly growth tracking
- **Role Management**: Admin, employee, and customer role assignments
- **Permission Control**: Module-based access permissions (customer management, debt access, reports)
- **User Status Tracking**: Active, suspended, pending verification states

#### 4. Shop Management
- **Shop CRUD Operations**: Create, read, update, delete shop records
- **Shop Statistics**: Registration trends, active shops, subscription status
- **Logo Upload**: Multipart file upload for shop branding
- **Status Management**: Pending, active, suspended shop states

#### 5. Subscription Management
- **Subscription Overview**: Plan types, payment status, renewal tracking
- **Payment Processing**: Online and offline payment verification
- **Plan Statistics**: Usage analytics and subscription trends
- **Renewal Management**: Automated and manual subscription renewals

#### 6. Risk Assessment Dashboard
- **Customer Risk Profiles**: ML-generated risk levels and scores
- **Risk Heatmaps**: Visual representation of customer risk distribution
- **Debt Overview**: Outstanding debts, payment delays, risk classifications
- **Alert Management**: High-risk customer notifications and actions

#### 7. Secure Logout
- **Session Termination**: Complete token invalidation
- **Security Logging**: Logout events and session tracking
- **Multi-device Logout**: Option to terminate all active sessions

---

## Mobile Application User Journey

The Flutter mobile application emphasizes offline-first functionality and seamless user experience for SME debt management.

### User Journey Steps:

#### 1. App Launch & Authentication
- **Splash Screen**: App initialization and connectivity check
- **Login Screen**: JWT-based authentication with form validation
- **Token Management**: Automatic token refresh and session validation
- **Offline Authentication**: Cached credentials for offline access

#### 2. Dashboard Navigation
- **Bottom Navigation**: Customer, Debt, Payment, Profile tabs
- **Permission-based UI**: Dynamic interface based on user permissions
- **Quick Actions**: Add customer, collect payment, view reports shortcuts
- **Real-time Updates**: Live data synchronization when online

#### 3. Customer Management
- **Customer List**: Paginated customer display with search and filters
- **Customer Creation**: Form-based customer registration with validation
- **Customer Profiles**: Detailed view with risk assessment and debt history
- **Risk Level Display**: Visual risk badges (Low, Medium, High Risk)

#### 4. Debt Entry & Management
- **Debt Creation**: Form-based debt entry with due date selection
- **Payment Recording**: Multiple payment method support (Cash, EVC Plus, Bank Transfer)
- **Debt Status Tracking**: Active, overdue, paid status management
- **ML Risk Integration**: Automatic risk assessment for overdue debts

#### 5. Offline Functionality
- **Local Data Storage**: SQLite database for offline data persistence
- **Connectivity Awareness**: Automatic online/offline mode detection
- **Data Synchronization**: Background sync when internet becomes available
- **Offline Payment Recording**: Local storage with sync queue management

#### 6. Payment Processing
- **Multiple Payment Methods**: EVC Plus, cash, bank transfer support
- **Payment History**: Comprehensive payment tracking and receipts
- **Offline Payment Support**: Local recording with server synchronization
- **Receipt Generation**: Digital receipts for payment confirmations

#### 7. Notification System
- **Firebase Cloud Messaging**: Real-time push notifications
- **SMS Integration**: Hormuud API for SMS alerts to customers
- **Risk-based Alerts**: Automated notifications for high-risk customers
- **Payment Reminders**: Scheduled notifications for due payments

---

## System Features (Actual Implementation)

### Backend System Features (Node.js/Express):
- **JWT Authentication System**: Access/refresh token pattern with 15-minute access tokens and 7-day refresh tokens
- **Role-Based Access Control**: SuperAdmin, Admin, Employee roles with granular permissions
- **Multi-tenant Architecture**: Shop-based data isolation with shopId references
- **EVC Plus Payment Integration**: WaafiPay API for Somali mobile money payments
- **Email Service**: Nodemailer with automated verification and notification emails
- **SMS Service**: Hormuud API integration for SMS notifications in Somalia
- **File Upload System**: Multer middleware for payment proofs and shop logos
- **Security Logging**: Comprehensive audit trails for authentication and security events
- **Rate Limiting**: API request throttling and protection mechanisms
- **Session Management**: Device tracking and multi-session support

### Mobile App Features (Flutter):
- **Offline-First Architecture**: ConnectivityAwareService with automatic online/offline detection
- **Local Data Storage**: Flutter Secure Storage for tokens and sensitive data
- **BLoC State Management**: Clean Architecture with separation of concerns
- **Customer Management**: Full CRUD operations with search and filtering
- **Debt Management**: Create, track, and manage customer debts with due dates
- **Payment Recording**: Multiple payment methods (Cash, EVC Plus, Bank Transfer)
- **Firebase Push Notifications**: FCM integration for real-time notifications
- **Theme Management**: Light/dark mode with SharedPreferences persistence
- **Permission-Based UI**: Dynamic interface based on user role permissions
- **Offline Payment Support**: File upload for payment proofs with multipart form data

### Web Dashboard Features (Next.js):
- **SuperAdmin Dashboard**: Real-time statistics with user, shop, subscription, and payment metrics
- **User Management**: Create, update, delete users with role assignments
- **Shop Management**: CRUD operations for shops with logo upload and status management
- **Subscription Management**: Plan management and payment verification
- **Dashboard Analytics**: KPI cards with growth trends and percentage calculations
- **Auto-refresh System**: 5-minute interval updates with manual refresh capability
- **Responsive Design**: TailwindCSS with mobile-first responsive components
- **Error Handling**: Comprehensive error states and loading indicators

### Machine Learning Features (FastAPI):
- **Risk Assessment Model**: Logistic Regression with 100% accuracy on synthetic data
- **Feature Engineering**: 5 key features (DebtPaidRatio, PaymentDelay, OutstandingDebt, DebtAmount, CustomerType)
- **Automated ML Evaluation**: Cron job triggers ML assessment after debt due dates
- **Risk Classification**: Three-tier system (Low ≤0.3, Medium 0.3-0.6, High >0.6)
- **Model Serialization**: Joblib for model persistence and deployment
- **Bulk Prediction Support**: Single and batch prediction endpoints
- **Keep-alive Mechanism**: Render cloud hosting with uptime monitoring

### Database & Storage Features:
- **MongoDB Atlas**: Cloud NoSQL database with document-oriented storage
- **Data Models**: 16 core entities including User, Shop, Customer, Debt, Payment, Subscription
- **Automatic Calculations**: Pre-save hooks for debt ratios and payment delays
- **Index Optimization**: Compound indexes for efficient querying
- **Data Validation**: Mongoose schemas with comprehensive validation rules

### Communication & Integration Features:
- **Firebase Cloud Messaging**: Push notifications for mobile app users
- **Email Automation**: Welcome emails, verification emails, payment notifications
- **SMS Notifications**: Hormuud API for debt reminders and payment alerts
- **Payment Gateway**: EVC Plus integration for mobile money transactions
- **File Management**: Upload system for receipts, logos, and payment proofs

---

## Tools and Technologies (Actual Stack)

### Frontend Technologies:
- **Flutter**: Cross-platform mobile app development with Dart
- **Next.js**: React-based admin dashboard with server-side rendering
- **BLoC Pattern**: State management for Flutter application
- **Clean Architecture**: Layered architecture for maintainable code

### Backend Technologies:
- **Node.js + Express**: RESTful API server with middleware support
- **MongoDB + Mongoose**: NoSQL database with ODM for data modeling
- **JWT**: JSON Web Tokens for secure authentication
- **Multer**: File upload middleware for document handling

### Machine Learning:
- **FastAPI**: Python-based ML service for real-time predictions
- **Logistic Regression**: Binary classification for risk assessment
- **Joblib**: Model serialization and deployment

### Communication Services:
- **Firebase Cloud Messaging**: Push notification service
- **Hormuud EVC plus payment integration  API**: Local payment  service for Somali market
- **Nodemailer**: Email service for notifications and verification

### Database & Storage:
- **MongoDB Atlas**: Cloud database hosting

### Security & Authentication:
- **JWT Tokens**: Access and refresh token authentication
- **bcrypt**: Password hashing and validation
- **CORS**: Cross-origin resource sharing configuration
- **Rate Limiting**: API request throttling and protection

### Development & Deployment:
- **Git**: Version control and collaboration
- **Docker**: Containerization for deployment
- **PM2**: Process management for Node.js applications
- **Render/Heroku**: Cloud hosting platforms
