/**
 * Bootstrap Performance Test Script
 * Tests the optimized bootstrap process to verify performance improvements
 */
require('dotenv').config();
const mongoose = require('mongoose');
const { logInfo, logSuccess, logError, timer } = require('../utils/logger');
const { bootstrap } = require('../config/bootstrap');

// MongoDB Connection Options - Same as server (corrected)
const mongoOptions = {
  useNewUrlParser: true,
  useUnifiedTopology: true,
  autoIndex: true,
  connectTimeoutMS: 6000, // 6 seconds for connection timeout
  socketTimeoutMS: 25000, // 25 seconds for socket timeout
  family: 4, // Use IPv4, skip trying IPv6
  maxPoolSize: 10, // Maintain up to 10 socket connections
  serverSelectionTimeoutMS: 8000, // 8 seconds for server selection
  heartbeatFrequencyMS: 10000, // Check connection health every 10 seconds
  // Removed bufferMaxEntries as it's not supported in current MongoDB driver versions
};

/**
 * Test bootstrap performance
 */
async function testBootstrapPerformance() {
  const testTimer = timer.start('Bootstrap Performance Test');
  
  try {
    logInfo('🧪 Starting Bootstrap Performance Test...', 'Test');
    
    // Connect to MongoDB
    const dbTimer = timer.start('Database Connection');
    await mongoose.connect(process.env.MONGODB_URI, mongoOptions);
    const dbTime = dbTimer();
    logSuccess(`Database connected in ${dbTime.toFixed(2)}ms`, 'Test');
    
    // Test optimized bootstrap
    const bootstrapTimer = timer.start('Optimized Bootstrap');
    const result = await bootstrap();
    const bootstrapTime = bootstrapTimer();
    
    if (result.success) {
      logSuccess(`✅ Optimized bootstrap completed in ${bootstrapTime.toFixed(2)}ms`, 'Test');
      logInfo('📊 Performance Summary:', 'Test');
      console.log(`  • Database Connection: ${dbTime.toFixed(2)}ms`);
      console.log(`  • Bootstrap Process: ${bootstrapTime.toFixed(2)}ms`);
      console.log(`  • Total Startup Time: ${(dbTime + bootstrapTime).toFixed(2)}ms`);
      
      if (result.criticalResults) {
        console.log('\n📋 Critical Tasks Results:');
        Object.entries(result.criticalResults).forEach(([task, taskResult]) => {
          const status = taskResult.success ? '✅' : '❌';
          console.log(`  ${status} ${task}: ${taskResult.success ? 'SUCCESS' : taskResult.error}`);
        });
      }
      
      // Performance benchmarks
      const totalTime = dbTime + bootstrapTime;
      console.log('\n🎯 Performance Benchmarks:');
      if (totalTime < 5000) {
        console.log('  🟢 EXCELLENT: Under 5 seconds (target achieved!)');
      } else if (totalTime < 10000) {
        console.log('  🟡 GOOD: Under 10 seconds (acceptable)');
      } else if (totalTime < 30000) {
        console.log('  🟠 FAIR: Under 30 seconds (improvement needed)');
      } else {
        console.log('  🔴 POOR: Over 30 seconds (significant improvement needed)');
      }
      
    } else {
      logError(`❌ Bootstrap failed: ${result.error}`, 'Test');
    }
    
    const totalTestTime = testTimer();
    logSuccess(`Test completed in ${totalTestTime.toFixed(2)}ms`, 'Test');
    
  } catch (error) {
    testTimer();
    logError(`Test failed: ${error.message}`, 'Test', error);
  } finally {
    // Close database connection
    await mongoose.connection.close();
    logInfo('Database connection closed', 'Test');
  }
}

/**
 * Run multiple bootstrap tests to get average performance
 */
async function runPerformanceTests(iterations = 3) {
  logInfo(`🔄 Running ${iterations} bootstrap performance tests...`, 'Test');
  
  const times = [];
  
  for (let i = 1; i <= iterations; i++) {
    logInfo(`\n--- Test Run ${i}/${iterations} ---`, 'Test');
    
    try {
      const startTime = Date.now();
      await testBootstrapPerformance();
      const endTime = Date.now();
      const totalTime = endTime - startTime;
      times.push(totalTime);
      
      // Wait a bit between tests to prevent resource conflicts
      if (i < iterations) {
        await new Promise(resolve => setTimeout(resolve, 2000));
      }
    } catch (error) {
      logError(`Test run ${i} failed: ${error.message}`, 'Test');
    }
  }
  
  if (times.length > 0) {
    const avgTime = times.reduce((a, b) => a + b, 0) / times.length;
    const minTime = Math.min(...times);
    const maxTime = Math.max(...times);
    
    console.log('\n📈 Performance Statistics:');
    console.log(`  • Average Time: ${avgTime.toFixed(2)}ms`);
    console.log(`  • Fastest Time: ${minTime.toFixed(2)}ms`);
    console.log(`  • Slowest Time: ${maxTime.toFixed(2)}ms`);
    console.log(`  • Improvement vs 54s baseline: ${((54000 - avgTime) / 54000 * 100).toFixed(1)}%`);
  }
}

// Run the test
if (require.main === module) {
  const args = process.argv.slice(2);
  const iterations = args[0] ? parseInt(args[0]) : 1;
  
  if (iterations > 1) {
    runPerformanceTests(iterations)
      .then(() => process.exit(0))
      .catch(() => process.exit(1));
  } else {
    testBootstrapPerformance()
      .then(() => process.exit(0))
      .catch(() => process.exit(1));
  }
}

module.exports = { testBootstrapPerformance, runPerformanceTests }; 