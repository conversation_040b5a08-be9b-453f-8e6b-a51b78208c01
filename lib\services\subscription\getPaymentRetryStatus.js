import apiBridge from '@/lib/api/bridge';
import { ENDPOINTS } from '@/lib/api/contract';;
import { handleError, processApiResponse } from '../baseService';

/**
 * Get payment retry status for subscription (SuperAdmin only)
 * @param {string} subscriptionId - Subscription ID
 * @returns {Promise<Object>} Payment retry status information
 */
async function getPaymentRetryStatus(subscriptionId) {
  try {
    if (!subscriptionId) {
      throw new Error('Subscription ID is required');
    }

    const response = await apiBridge.get(`${ENDPOINTS.SUBSCRIPTIONS.BASE}/payment-retry/${subscriptionId}/status`);
    
    const result = processApiResponse(response);
    return result.retryStatus || result;
  } catch (error) {
    handleError(error, 'SubscriptionService.getPaymentRetryStatus', true);
    throw error;
  }
}

export default getPaymentRetryStatus; 
