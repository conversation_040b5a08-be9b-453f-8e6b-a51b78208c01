import React from 'react';
import UserFormDialog from '@/components/dashboard/users/user-form-dialog';
import { UserDetailsDialog } from '@/components/dashboard/users/user-details';
import { ChangeStatusDialog } from '@/components/dashboard/users/change-status-dialog';
import { DeleteUserDialog } from '@/components/dashboard/users/delete-user-dialog';

export const UsersDialogs = ({
  // Form dialog props
  isFormOpen,
  selectedUser,
  shops,
  onCloseForm,
  onUserCreated,
  onUserUpdated,
  
  // Details dialog props
  showDetailsDialog,
  onCloseDetailsDialog,
  
  // Status change dialog props
  showChangeStatusDialog,
  statusAction,
  onCloseStatusDialog,
  onStatusChanged,
  
  // Delete dialog props
  showDeleteDialog,
  onCloseDeleteDialog,
  onUserDeleted
}) => {
  return (
    <>
      {/* User Form Dialog */}
      {isFormOpen && (
        <UserFormDialog
          isOpen={isFormOpen}
          onClose={onCloseForm}
          user={selectedUser}
          shops={shops}
          onUserCreated={onUserCreated}
          onUserUpdated={onUserUpdated}
        />
      )}
      
      {/* User Details Dialog */}
      {showDetailsDialog && selectedUser && (
        <UserDetailsDialog
          isOpen={showDetailsDialog}
          onClose={onCloseDetailsDialog}
          user={selectedUser}
        />
      )}
      
      {/* Change Status Dialog */}
      {showChangeStatusDialog && selectedUser && (
        <ChangeStatusDialog
          isOpen={showChangeStatusDialog}
          onClose={onCloseStatusDialog}
          user={selectedUser}
          action={statusAction}
          onStatusChanged={onStatusChanged}
        />
      )}
      
      {/* Delete User Dialog */}
      {showDeleteDialog && selectedUser && (
        <DeleteUserDialog
          isOpen={showDeleteDialog}
          onClose={onCloseDeleteDialog}
          user={selectedUser}
          onUserDeleted={onUserDeleted}
        />
      )}
    </>
  );
};

export default UsersDialogs;
