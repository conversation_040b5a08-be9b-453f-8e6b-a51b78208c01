"use client";

import { useState, useCallback, useEffect } from 'react';
import { toast } from 'sonner';
import NotificationService from '@/lib/services/notification';

/**
 * Notification Statistics Hook
 * 
 * Backend-matched implementation for notification statistics and system status
 * Provides methods for:
 * - Loading notification statistics
 * - Testing Firebase connection
 * - Getting notification targets
 * - System status overview
 */
export function useNotificationStats(options = {}) {
  const {
    showToastMessages = true,
    onSuccess,
    onError,
    autoRefresh = false,
    refreshInterval = 30000 // 30 seconds
  } = options;

  // State management
  const [stats, setStats] = useState(null);
  const [targets, setTargets] = useState(null);
  const [systemStatus, setSystemStatus] = useState(null);
  const [isLoadingStats, setIsLoadingStats] = useState(false);
  const [isLoadingTargets, setIsLoadingTargets] = useState(false);
  const [isTestingFirebase, setIsTestingFirebase] = useState(false);
  const [error, setError] = useState(null);

  /**
   * Load notification statistics
   * @param {Object} [options] - Query options
   * @param {string} [options.shopId] - Filter by specific shop
   * @param {number} [options.days=30] - Number of days to query
   */
  const loadStats = useCallback(async (queryOptions = {}) => {
    setIsLoadingStats(true);
    setError(null);

    try {
      const response = await NotificationService.getNotificationStats(queryOptions);
      
      if (response.success || response.stats) {
        const statsData = response.data || response;
        setStats(statsData);
        
        if (onSuccess) onSuccess('loadStats', statsData);
        return statsData;
      } else {
        throw new Error(response.message || 'Failed to load notification stats');
      }
    } catch (err) {
      const errorMessage = err.message || 'Failed to load notification stats';
      setError(errorMessage);
      
      if (showToastMessages) {
        toast.error(errorMessage);
      }
      
      if (onError) onError('loadStats', err);
      
      // Return empty stats to prevent UI breaking
      const emptyStats = { stats: {}, details: [] };
      setStats(emptyStats);
      return emptyStats;
    } finally {
      setIsLoadingStats(false);
    }
  }, [showToastMessages, onSuccess, onError]);

  /**
   * Load notification targets
   */
  const loadTargets = useCallback(async () => {
    setIsLoadingTargets(true);
    setError(null);

    try {
      const response = await NotificationService.getNotificationTargets();
      
      if (response.success || response.shops || response.users) {
        const targetsData = response.data || response;
        setTargets(targetsData);
        
        if (onSuccess) onSuccess('loadTargets', targetsData);
        return targetsData;
      } else {
        throw new Error(response.message || 'Failed to load notification targets');
      }
    } catch (err) {
      const errorMessage = err.message || 'Failed to load notification targets';
      setError(errorMessage);
      
      if (showToastMessages) {
        toast.error(errorMessage);
      }
      
      if (onError) onError('loadTargets', err);
      
      // Return empty targets to prevent UI breaking
      const emptyTargets = { shops: [], users: [], total: 0 };
      setTargets(emptyTargets);
      return emptyTargets;
    } finally {
      setIsLoadingTargets(false);
    }
  }, [showToastMessages, onSuccess, onError]);

  /**
   * Test Firebase connection
   */
  const testFirebase = useCallback(async () => {
    setIsTestingFirebase(true);
    setError(null);

    try {
      const response = await NotificationService.testFirebaseConnection();
      
      // The response is already processed by processApiResponse
      // Show success message only once here
      if (showToastMessages) {
        toast.success('Firebase connection test successful');
      }
      
      if (onSuccess) onSuccess('testFirebase', response);
      return response;
    } catch (err) {
      const errorMessage = err.message || 'Firebase connection test failed';
      setError(errorMessage);
      
      if (showToastMessages) {
        toast.error(errorMessage);
      }
      
      if (onError) onError('testFirebase', err);
      throw err;
    } finally {
      setIsTestingFirebase(false);
    }
  }, [showToastMessages, onSuccess, onError]);

  /**
   * Get comprehensive system status
   */
  const getSystemStatus = useCallback(async () => {
    try {
      const response = await NotificationService.getSystemStatus();
      setSystemStatus(response.data);
      return response.data;
    } catch (err) {
      console.error('[useNotificationStats] Error getting system status:', err);
      const defaultStatus = {
        firebase: { connected: false, error: err.message },
        todayStats: null,
        timestamp: new Date().toISOString()
      };
      setSystemStatus(defaultStatus);
      return defaultStatus;
    }
  }, []);

  /**
   * Refresh all data
   */
  const refreshAll = useCallback(async () => {
    try {
      const [statsResult, targetsResult] = await Promise.allSettled([
        loadStats(),
        loadTargets()
      ]);

      // Update system status as well
      await getSystemStatus();

      return {
        stats: statsResult.status === 'fulfilled' ? statsResult.value : null,
        targets: targetsResult.status === 'fulfilled' ? targetsResult.value : null
      };
    } catch (err) {
      console.error('[useNotificationStats] Error refreshing data:', err);
      throw err;
    }
  }, [loadStats, loadTargets, getSystemStatus]);

  /**
   * Clear error state
   */
  const clearError = useCallback(() => {
    setError(null);
  }, []);

  /**
   * Clear all cached data
   */
  const clearCache = useCallback(() => {
    setStats(null);
    setTargets(null);
    setSystemStatus(null);
    setError(null);
  }, []);

  // Auto-refresh effect
  useEffect(() => {
    if (autoRefresh && refreshInterval > 0) {
      const interval = setInterval(() => {
        refreshAll();
      }, refreshInterval);

      return () => clearInterval(interval);
    }
  }, [autoRefresh, refreshInterval, refreshAll]);

  // Computed loading state
  const isLoading = isLoadingStats || isLoadingTargets || isTestingFirebase;

  return {
    // Data
    stats,
    targets,
    systemStatus,
    
    // Loading states
    isLoadingStats,
    isLoadingTargets,
    isTestingFirebase,
    isLoading,
    error,
    
    // Operations
    loadStats,
    loadTargets,
    testFirebase,
    getSystemStatus,
    refreshAll,
    
    // Utility methods
    clearError,
    clearCache
  };
} 