const mongoose = require('mongoose');
const Customer = require('../models/customer.model');

/**
 * Fix Customer Collection Indexes
 * - Drops old CustomerID index
 * - Recreates proper customerId index
 */
async function fixCustomerIndexes() {
  try {
    console.log('🔄 Connecting to database...');
    
    // Use environment variable or default connection
    const mongoURI = process.env.MONGODB_URI || process.env.DB_URI || 'mongodb+srv://Karshe:<EMAIL>/Deyncare?retryWrites=true&w=majority';
    await mongoose.connect(mongoURI);
    
    console.log('✅ Connected to database');
    
    // Get the collection
    const collection = Customer.collection;
    
    console.log('🔍 Checking existing indexes...');
    const existingIndexes = await collection.getIndexes();
    console.log('Current indexes:', Object.keys(existingIndexes));
    
    // Drop the old CustomerID index if it exists
    if (existingIndexes.CustomerID_1) {
      console.log('🗑️ Dropping old CustomerID index...');
      await collection.dropIndex('CustomerID_1');
      console.log('✅ Old CustomerID index dropped');
    }
    
    // Drop any other problematic indexes
    const indexesToDrop = ['CustomerID_1', 'CustomerName_1', 'phone_1'];
    for (const indexName of indexesToDrop) {
      try {
        if (existingIndexes[indexName]) {
          console.log(`🗑️ Dropping index: ${indexName}`);
          await collection.dropIndex(indexName);
          console.log(`✅ Dropped index: ${indexName}`);
        }
      } catch (error) {
        console.log(`⚠️ Could not drop index ${indexName}:`, error.message);
      }
    }
    
    console.log('🔄 Creating new indexes...');
    
    // Create proper indexes as defined in the model, but check if they exist first
    const finalIndexes = await collection.getIndexes();
    
    // Only create shopId + phone index if it doesn't exist
    if (!finalIndexes['shopId_1_phone_1']) {
      await collection.createIndex({ shopId: 1, phone: 1 });
      console.log('✅ Created index: shopId_1_phone_1');
    } else {
      console.log('ℹ️ Index shopId_1_phone_1 already exists');
    }
    
    // Only create customerId index if it doesn't exist, or drop and recreate if properties differ
    if (finalIndexes['customerId_1']) {
      const existingIndex = finalIndexes['customerId_1'];
      if (existingIndex.unique) {
        console.log('ℹ️ Index customerId_1 already exists and is unique - keeping it');
      }
    } else {
      await collection.createIndex({ customerId: 1 }, { unique: true });
      console.log('✅ Created unique index: customerId_1');
    }
    
    // Only create shopId + outstandingBalance index if it doesn't exist
    if (!finalIndexes['shopId_1_outstandingBalance_-1']) {
      await collection.createIndex({ shopId: 1, outstandingBalance: -1 });
      console.log('✅ Created index: shopId_1_outstandingBalance_-1');
    } else {
      console.log('ℹ️ Index shopId_1_outstandingBalance_-1 already exists');
    }
    
    console.log('🔍 Final index check...');
    const finalIndexesAfter = await collection.getIndexes();
    console.log('Final indexes:', Object.keys(finalIndexesAfter));
    
    console.log('✅ Customer indexes fixed successfully!');
    
  } catch (error) {
    console.error('❌ Error fixing customer indexes:', error);
    throw error;
  } finally {
    await mongoose.disconnect();
    console.log('🔌 Database connection closed');
  }
}

// Run the script
if (require.main === module) {
  fixCustomerIndexes()
    .then(() => {
      console.log('🎉 Script completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Script failed:', error);
      process.exit(1);
    });
}

module.exports = { fixCustomerIndexes }; 