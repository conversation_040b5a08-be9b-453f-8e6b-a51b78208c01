import apiBridge from '@/lib/api/bridge';
import { ENDPOINTS } from '@/lib/api/contract';;
import { handleError, processApiResponse } from '../baseService';

/**
 * Extend subscription period
 * @param {string} subscriptionId - Subscription ID to extend
 * @param {Object} extensionData - Extension data (days, reason, etc.)
 * @returns {Promise<Object>} Extended subscription
 */
async function extendSubscription(subscriptionId, extensionData) {
  try {
    const response = await apiBridge.post(
      `${ENDPOINTS.SUBSCRIPTIONS.BASE}/${subscriptionId}/extend`,
      extensionData
    );
    
    return processApiResponse(response);
  } catch (error) {
    handleError(error, 'SubscriptionService.extendSubscription', true);
    throw error;
  }
}

export default extendSubscription; 
