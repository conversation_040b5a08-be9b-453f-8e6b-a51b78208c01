/**
 * Request Subscription Upgrade Controller
 * Handles upgrade requests by sending email to SuperAdmin
 */
const { SubscriptionService } = require('../../services');
const SubscriptionEmailService = require('../../services/email/subscriptionEmailService');
const { Shop } = require('../../models');
const { logError, logSuccess, successResponse } = require('../../utils');

/**
 * Request subscription upgrade
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
const requestUpgrade = async (req, res, next) => {
  try {
    const { planType, message } = req.body;
    const { shopId, userId, email: userEmail } = req.user;
    
    if (!shopId) {
      return res.status(400).json({
        success: false,
        message: 'No shop associated with this user',
        statusCode: 400,
        type: 'shop_not_found'
      });
    }
    
    // Get current subscription for the shop
    const currentSubscription = await SubscriptionService.getCurrentSubscriptionByShop(shopId);
    
    if (!currentSubscription) {
      return res.status(404).json({
        success: false,
        message: 'Subscription not found',
        statusCode: 404,
        type: 'subscription_not_found'
      });
    }

    // Get shop details
    const shop = await Shop.findOne({ shopId });
    if (!shop) {
      return res.status(404).json({
        success: false,
        message: 'Shop not found',
        statusCode: 404,
        type: 'shop_not_found'
      });
    }

    // Send upgrade request email to SuperAdmin
    try {
      await SubscriptionEmailService.sendUpgradeRequestEmail({
        shopId,
        shopName: shop.shopName,
        shopEmail: shop.email,
        ownerName: shop.ownerName,
        currentPlan: currentSubscription.plan?.type || 'unknown',
        requestedPlan: planType,
        requestMessage: message,
        requestedBy: userId,
        requestedAt: new Date()
      });

      logSuccess(`Upgrade request sent for shop ${shopId} to plan ${planType}`, 'SubscriptionController');

      return successResponse(res, {
        message: 'Upgrade request sent successfully. Our team will contact you within 24 hours.',
        data: {
          shopId,
          currentPlan: currentSubscription.plan?.type,
          requestedPlan: planType,
          status: 'request_sent'
        }
      });
    } catch (emailError) {
      logError('Failed to send upgrade request email', 'SubscriptionController', emailError);
      
      return res.status(500).json({
        success: false,
        message: 'Failed to send upgrade request. Please try again or contact support.',
        statusCode: 500,
        type: 'email_failed'
      });
    }
  } catch (error) {
    logError('Failed to process upgrade request', 'SubscriptionController', error);
    return next(error);
  }
};

module.exports = requestUpgrade; 