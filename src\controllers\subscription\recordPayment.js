/**
 * Record Payment Controller
 * Handles recording payments for subscriptions
 */
const { SubscriptionService } = require('../../services');
const { logError } = require('../../utils');

/**
 * Record payment for subscription
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
const recordPayment = async (req, res, next) => {
  try {
    const { subscriptionId, paymentDetails } = req.body;
    
    // For security tracking, we pass actor information to service
    const options = {
      actorId: req.user?.userId || 'system',
      actorRole: req.user?.role || 'system'
    };
    
    // Record payment through service
    const updatedSubscription = await SubscriptionService.recordPayment(
      subscriptionId, 
      paymentDetails, 
      options
    );
    
    return res.status(200).json({
      success: true,
      message: 'Payment recorded successfully',
      data: {
        subscriptionId: updatedSubscription.subscriptionId,
        status: updatedSubscription.status,
        endDate: updatedSubscription.dates.endDate
      }
    });
  } catch (error) {
    logError('Failed to record payment', 'SubscriptionController', error);
    return next(error);
  }
};

module.exports = recordPayment;
