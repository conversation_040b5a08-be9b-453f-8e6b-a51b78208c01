const { Customer, Debt, Payment } = require('../../models');
const { AppError, logError, logInfo } = require('../../utils');

/**
 * Delete Customer Record (Soft Delete)
 * DELETE /api/customers/:customerId
 */
const deleteCustomer = async (req, res, next) => {
  try {
    const { customerId } = req.params;
    const shopId = req.user.shopId;

    // Find existing customer
    const customer = await Customer.findOne({ 
      customerId, 
      shopId, 
      isDeleted: false 
    });

    if (!customer) {
      return res.status(404).json({
        success: false,
        message: 'Customer not found'
      });
    }

    // Check if customer has any active debts
    const activeDebts = await Debt.find({ 
      customerId, 
      shopId, 
      OutstandingDebt: { $gt: 0 },
      isDeleted: false 
    });

    if (activeDebts.length > 0) {
      return res.status(400).json({
        success: false,
        message: 'Cannot delete customer with active debts. Please resolve all outstanding debts first.',
        details: {
          reason: 'customer_has_active_debts',
          activeDebts: activeDebts.length,
          totalOutstanding: activeDebts.reduce((sum, debt) => sum + debt.OutstandingDebt, 0),
          suggestion: 'Collect or write off all outstanding debts before deleting the customer'
        }
      });
    }

    // Check if customer has any recent activity (last 30 days)
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    const recentPayments = await Payment.find({
      customerId,
      shopId,
      paymentContext: 'debt',
      paymentDate: { $gte: thirtyDaysAgo },
      isDeleted: false
    });

    const recentDebts = await Debt.find({
      customerId,
      shopId,
      createdAt: { $gte: thirtyDaysAgo },
      isDeleted: false
    });

    if (recentPayments.length > 0 || recentDebts.length > 0) {
      return res.status(400).json({
        success: false,
        message: 'Cannot delete customer with recent activity. Customer has transactions within the last 30 days.',
        details: {
          reason: 'recent_activity_exists',
          recentPayments: recentPayments.length,
          recentDebts: recentDebts.length,
          suggestion: 'Wait 30 days after last activity or archive the customer instead'
        }
      });
    }

    // Check if customer has any debt history (paid debts)
    const debtHistory = await Debt.find({
      customerId,
      shopId,
      isDeleted: false
    });

    if (debtHistory.length > 0) {
      // If customer has debt history, warn but allow deletion with confirmation
      const { force } = req.query;
      
      if (!force || force !== 'true') {
        return res.status(400).json({
          success: false,
          message: 'Customer has debt history. This will soft-delete the customer but preserve debt records for audit purposes.',
          details: {
            reason: 'customer_has_debt_history',
            totalDebts: debtHistory.length,
            paidDebts: debtHistory.filter(d => d.OutstandingDebt <= 0 && d.PaidAmount > 0).length,
            suggestion: 'Add ?force=true to the request to proceed with deletion',
            note: 'Debt records will be preserved for audit purposes'
          }
        });
      }
    }

    // Perform soft delete
    customer.isDeleted = true;
    customer.deletedAt = new Date();
    customer.deletedBy = req.user.userId;
    
    await customer.save();

    // Optionally soft delete associated debt records as well
    if (debtHistory.length > 0) {
      await Debt.updateMany(
        { customerId, shopId, isDeleted: false },
        { 
          $set: { 
            isDeleted: true, 
            deletedAt: new Date(),
            deletedBy: req.user.userId
          } 
        }
      );
    }

    logInfo(`Customer deleted: ${customerId} by ${req.user.email}`, 'DeleteCustomer');

    res.json({
      success: true,
      message: 'Customer deleted successfully',
      data: {
        customerId: customer.customerId,
        customerName: customer.CustomerName,
        deletedAt: customer.deletedAt,
        debtRecordsAffected: debtHistory.length,
        note: 'This is a soft delete - all records are preserved for audit purposes'
      }
    });

  } catch (error) {
    logError('Failed to delete customer', 'DeleteCustomer', error);
    return next(new AppError('Failed to delete customer', 500));
  }
};

module.exports = deleteCustomer; 