/**
 * Toggle Plan Status Controller
 * Allows SuperAdmin to activate or deactivate a plan
 * PATCH /api/plans/:planId/toggle-status
 */
const { Plan } = require('../../models');
const { logInfo, logSuccess, logError } = require('../../utils');

const togglePlanStatus = async (req, res, next) => {
  try {
    const { planId } = req.params;
    
    // Only SuperAdmin can toggle plan status
    if (req.user.role !== 'superAdmin') {
      return res.status(403).json({
        success: false,
        message: 'Access denied. SuperAdmin privileges required.'
      });
    }
    
    logInfo(`Toggling status for plan: ${planId}`, 'PlanController');
    
    // Find the plan
    const plan = await Plan.findOne({ planId });
    
    if (!plan) {
      return res.status(404).json({
        success: false,
        message: 'Plan not found'
      });
    }
    
    // Toggle the status
    plan.isActive = !plan.isActive;
    
    // Add audit information
    plan.metadata = plan.metadata || {};
    plan.metadata.lastUpdatedBy = req.user.userId;
    plan.metadata.lastStatusChange = new Date();
    
    await plan.save();
    
    const statusText = plan.isActive ? 'activated' : 'deactivated';
    logSuccess(`Plan ${planId} has been ${statusText}`, 'PlanController');
    
    return res.status(200).json({
      success: true,
      message: `Plan has been ${statusText}`,
      data: {
        planId: plan.planId,
        name: plan.name,
        isActive: plan.isActive
      }
    });
  } catch (error) {
    logError(`Failed to toggle plan status: ${error.message}`, 'PlanController', error);
    return next(error);
  }
};

module.exports = togglePlanStatus;
