import apiBridge from '@/lib/api/bridge';
import { ENDPOINTS } from '@/lib/api/contract';;
import { handleError, processApiResponse } from '../baseService';

/**
 * Manually trigger subscription cron tasks (SuperAdmin only)
 * @returns {Promise<Object>} Cron execution results
 */
async function triggerCronTasks() {
  try {
    const response = await apiBridge.post(`${ENDPOINTS.SUBSCRIPTIONS.BASE}/cron/run`, {}, {
      clearCacheEndpoint: ENDPOINTS.SUBSCRIPTIONS.BASE
    });
    
    const result = processApiResponse(response, 'Subscription cron tasks triggered successfully');
    return result;
  } catch (error) {
    handleError(error, 'SubscriptionService.triggerCronTasks', true);
    throw error;
  }
}

export default triggerCronTasks; 
