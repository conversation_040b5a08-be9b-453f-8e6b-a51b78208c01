#!/bin/bash

# ==============================================
# DEYNCARE FRONTEND DEPLOYMENT SCRIPT
# ==============================================
# This script deploys the DeynCare frontend to your VPS
# Make sure to run this script with appropriate permissions
# chmod +x deploy.sh

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration variables
APP_NAME="deyncare"
NODE_VERSION="18"
PM2_APP_NAME="deyncare"
DEPLOY_DIR="/var/www/deyncare.cajiibcreative.com"
BACKUP_DIR="/var/backups/deyncare"
BUILD_DIR="$DEPLOY_DIR/build"
LOG_FILE="/var/log/deyncare-deploy.log"

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to log messages
log_message() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" >> "$LOG_FILE"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to install Node.js if not present
install_nodejs() {
    if ! command_exists node; then
        print_status "Installing Node.js v$NODE_VERSION..."
        curl -fsSL https://deb.nodesource.com/setup_${NODE_VERSION}.x | sudo -E bash -
        sudo apt-get install -y nodejs
        print_success "Node.js installed successfully"
    else
        print_success "Node.js is already installed: $(node --version)"
    fi
}

# Function to install PM2 if not present
install_pm2() {
    if ! command_exists pm2; then
        print_status "Installing PM2..."
        sudo npm install -g pm2
        print_success "PM2 installed successfully"
    else
        print_success "PM2 is already installed: $(pm2 --version)"
    fi
}

# Function to create necessary directories
create_directories() {
    print_status "Creating deployment directories..."
    sudo mkdir -p "$DEPLOY_DIR"
    sudo mkdir -p "$BACKUP_DIR"
    sudo mkdir -p "$(dirname "$LOG_FILE")"
    sudo chown -R $USER:$USER "$DEPLOY_DIR"
    print_success "Directories created successfully"
}

# Function to backup existing deployment
backup_existing() {
    if [ -d "$DEPLOY_DIR" ] && [ "$(ls -A $DEPLOY_DIR)" ]; then
        print_status "Backing up existing deployment..."
        local backup_name="backup-$(date +%Y%m%d-%H%M%S)"
        sudo cp -r "$DEPLOY_DIR" "$BACKUP_DIR/$backup_name"
        print_success "Backup created: $BACKUP_DIR/$backup_name"
    fi
}

# Function to stop existing PM2 process
stop_pm2_process() {
    if pm2 list | grep -q "$PM2_APP_NAME"; then
        print_status "Stopping existing PM2 process..."
        pm2 stop "$PM2_APP_NAME" || true
        pm2 delete "$PM2_APP_NAME" || true
        print_success "PM2 process stopped"
    fi
}

# Function to install dependencies
install_dependencies() {
    print_status "Installing dependencies..."
    cd "$DEPLOY_DIR"
    npm ci --production=false
    print_success "Dependencies installed successfully"
}

# Function to build the application
build_application() {
    print_status "Building the application..."
    cd "$DEPLOY_DIR"
    
    # Check if .env.production exists
    if [ ! -f ".env.production" ]; then
        print_warning ".env.production not found. Please create it from env.production.example"
        exit 1
    fi
    
    # Build the Next.js application
    npm run build
    print_success "Application built successfully"
}

# Function to start the application with PM2
start_application() {
    print_status "Starting application with PM2..."
    cd "$DEPLOY_DIR"
    
    # Start with PM2 using ecosystem file if exists, otherwise use simple start
    if [ -f "ecosystem.config.js" ]; then
        pm2 start ecosystem.config.js
    else
        pm2 start npm --name "$PM2_APP_NAME" -- start
    fi
    
    # Save PM2 process list
    pm2 save
    
    # Set up PM2 to start on boot
    sudo pm2 startup systemd -u $USER --hp $HOME
    
    print_success "Application started successfully"
}

# Function to check application health
check_health() {
    print_status "Checking application health..."
    sleep 10  # Wait for app to start
    
    if pm2 list | grep -q "$PM2_APP_NAME.*online"; then
        print_success "Application is running successfully"
        pm2 show "$PM2_APP_NAME"
    else
        print_error "Application failed to start"
        pm2 logs "$PM2_APP_NAME" --lines 20
        exit 1
    fi
}

# Function to check nginx configuration (optional verification)
check_nginx() {
    if command_exists nginx; then
        print_status "Verifying nginx is running..."
        if systemctl is-active --quiet nginx; then
            print_success "Nginx is running successfully"
            print_status "Make sure your nginx is configured to proxy to http://localhost:3000"
        else
            print_warning "Nginx is not running. You may need to start it manually."
        fi
    else
        print_warning "Nginx not found. Make sure your web server is properly configured."
    fi
}

# Main deployment function
main() {
    print_status "Starting deployment of $APP_NAME..."
    log_message "Deployment started"
    
    # Pre-deployment checks
    if [ "$EUID" -eq 0 ]; then
        print_error "Do not run this script as root"
        exit 1
    fi
    
    # Create log file if it doesn't exist
    sudo touch "$LOG_FILE"
    sudo chown $USER:$USER "$LOG_FILE"
    
    # Install dependencies
    install_nodejs
    install_pm2
    
    # Prepare deployment
    create_directories
    backup_existing
    stop_pm2_process
    
    # Copy files to deployment directory
    print_status "Copying application files..."
    rsync -av --exclude node_modules --exclude .git --exclude .next . "$DEPLOY_DIR/"
    
    # Deploy application
    install_dependencies
    build_application
    start_application
    check_health
    
    # Verify nginx configuration
    check_nginx
    
    print_success "Deployment completed successfully!"
    print_status "Application is running at http://localhost:3000"
    print_status "Check PM2 status: pm2 status"
    print_status "View logs: pm2 logs $PM2_APP_NAME"
    
    log_message "Deployment completed successfully"
}

# Help function
show_help() {
    echo "DeynCare Frontend Deployment Script"
    echo ""
    echo "Usage: $0 [OPTION]"
    echo ""
    echo "Options:"
    echo "  -h, --help     Show this help message"
    echo "  -b, --backup   Create backup only"
    echo "  -s, --stop     Stop application only"
    echo "  -r, --restart  Restart application only"
    echo ""
    echo "Examples:"
    echo "  $0              # Full deployment"
    echo "  $0 --backup    # Create backup only"
    echo "  $0 --restart   # Restart application"
}

# Parse command line arguments
case "${1:-}" in
    -h|--help)
        show_help
        exit 0
        ;;
    -b|--backup)
        backup_existing
        exit 0
        ;;
    -s|--stop)
        stop_pm2_process
        exit 0
        ;;
    -r|--restart)
        stop_pm2_process
        start_application
        check_health
        exit 0
        ;;
    "")
        main
        ;;
    *)
        print_error "Unknown option: $1"
        show_help
        exit 1
        ;;
esac 