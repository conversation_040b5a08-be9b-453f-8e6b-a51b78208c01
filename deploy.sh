#!/bin/bash

# 🚀 DeynCare Backend Deployment Script
# Usage: ./deploy.sh [OPTIONS]

set -e  # Exit on any error

# Configuration
APP_DIR="/var/www/deyncare-backend.khanciye.com"
BRANCH="main"
BACKUP_DIR="/var/backups/deyncare-backend"
LOG_FILE="/var/log/deyncare-deploy.log"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Enhanced logging functions
log() {
    echo -e "${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a "$LOG_FILE"
}

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1" | tee -a "$LOG_FILE"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1" | tee -a "$LOG_FILE"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1" | tee -a "$LOG_FILE"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1" | tee -a "$LOG_FILE"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1" | tee -a "$LOG_FILE"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1" | tee -a "$LOG_FILE"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" | tee -a "$LOG_FILE"
}

# Check if running as root or with sudo
check_permissions() {
    if [ "$EUID" -eq 0 ]; then
        warning "Running as root. Consider using a dedicated user for deployments."
    fi
}

# Check if required tools are installed
check_requirements() {
    log "Checking system requirements..."
    
    # Check Git
    if ! command -v git &> /dev/null; then
        error "Git is not installed. Please install git first."
        exit 1
    fi
    
    # Check Node.js
    if ! command -v node &> /dev/null; then
        error "Node.js is not installed. Please install Node.js first."
        exit 1
    fi
    
    # Check NPM
    if ! command -v npm &> /dev/null; then
        error "NPM is not installed. Please install NPM first."
        exit 1
    fi
    
    # Check PM2
    if ! command -v pm2 &> /dev/null; then
        error "PM2 is not installed. Please install PM2 first."
        echo "To install PM2: npm install -g pm2"
        exit 1
    fi
    
    success "All requirements are met"
}

# Display system information
show_system_info() {
    log "System Information:"
    echo "  OS: $(lsb_release -d 2>/dev/null | cut -f2 || uname -s)"
    echo "  Node.js: $(node --version)"
    echo "  NPM: $(npm --version)"
    echo "  PM2: $(pm2 --version)"
    echo "  Git: $(git --version)"
    echo "  Current User: $(whoami)"
    echo "  Current Directory: $(pwd)"
}

# Create backup of current deployment
create_backup() {
    log "Creating backup of current deployment..."
    
    if [ -d "node_modules" ]; then
        mkdir -p "$BACKUP_DIR"
        BACKUP_NAME="backup-$(date '+%Y%m%d-%H%M%S')"
        tar -czf "$BACKUP_DIR/$BACKUP_NAME.tar.gz" --exclude='node_modules' --exclude='.git' --exclude='logs' . || warning "Backup failed, continuing..."
        success "Backup created: $BACKUP_DIR/$BACKUP_NAME.tar.gz"
    else
        warning "No existing deployment found to backup"
    fi
}

# Cleanup old backups (keep last 5)
cleanup_backups() {
    log "Cleaning up old backups..."
    
    if [ -d "$BACKUP_DIR" ]; then
        cd "$BACKUP_DIR"
        ls -t *.tar.gz 2>/dev/null | tail -n +6 | xargs -r rm -f
        success "Old backups cleaned up"
    fi
}

# Pull latest code from repository
pull_code() {
    log "Pulling latest changes from git..."
    
    # Check if it's a git repository
    if [ ! -d ".git" ]; then
        error "Not a git repository. Please run this script from the git repository directory."
        exit 1
    fi
    
    # Check current branch
    CURRENT_BRANCH=$(git branch --show-current)
    log "Current branch: $CURRENT_BRANCH"
    
    # Fetch and pull
    git fetch origin
    git reset --hard origin/$BRANCH
    
    # Show latest commit
    log "Latest commit:"
    git log --oneline -1
    
    success "Git pull completed"
}

# Function to install dependencies
install_dependencies() {
    print_status "Installing dependencies..."
    cd "$DEPLOY_DIR"
    
    # Use --legacy-peer-deps to resolve ESLint version conflicts
    npm ci --production=false --legacy-peer-deps
    print_success "Dependencies installed successfully"
}

# Create necessary directories
create_directories() {
    log "Creating necessary directories..."
    mkdir -p logs uploads
    chmod 755 logs uploads
    success "Directories created"
}

# Restart PM2 processes
restart_pm2() {
    log "Reloading PM2 application..."
    
    # Check if ecosystem.config.js exists
    if [ ! -f "ecosystem.config.js" ]; then
        error "ecosystem.config.js not found! PM2 configuration missing."
        exit 1
    fi
    
    # Reload or start PM2 application
    if pm2 list | grep -q "deyncare-backend"; then
        log "Reloading existing PM2 process..."
        pm2 reload ecosystem.config.js --env production
    else
        log "Starting new PM2 process..."
        pm2 start ecosystem.config.js --env production
    fi
    
    # Save PM2 configuration
    log "Saving PM2 configuration..."
    pm2 save
    
    success "PM2 processes restarted successfully"
}

# Check application status
check_application() {
    log "Checking application status..."
    sleep 3
    pm2 show deyncare-backend
}

# Perform health check
health_check() {
    log "Performing health check..."
    sleep 5
    if curl -f http://localhost:5000/api/health >/dev/null 2>&1; then
        success "Health check passed!"
    else
        warning "Health check failed - check logs with: pm2 logs deyncare-backend"
    fi
}

# Show deployment summary
show_summary() {
    log "=== Deployment Summary ==="
    echo "  Branch: $BRANCH"
    echo "  Deployment Time: $(date '+%Y-%m-%d %H:%M:%S')"
    echo "  Log File: $LOG_FILE"
    
    log "Latest commit info:"
    git log --oneline -1
    
    echo ""
    success "🎉 Deployment completed successfully!"
    echo ""
    echo "📊 Application Status:"
    pm2 list
    echo ""
    echo "📝 Useful commands:"
    echo "  View logs:    pm2 logs deyncare-backend"
    echo "  Restart app:  pm2 restart deyncare-backend"
    echo "  Stop app:     pm2 stop deyncare-backend"
    echo "  App status:   pm2 show deyncare-backend"
    echo "  Monitor:      pm2 monit"
    echo ""
    echo "🌐 API Endpoints:"
    echo "  Health: http://localhost:5000/api/health"
    echo "  Domain: https://deyncare-backend.khanciye.com/api/health"
    echo ""
    echo "Deployment completed at: $(date)"
}

# Main deployment function
main() {
    log "🚀 Starting DeynCare Backend Deployment..."
    
    check_permissions
    check_requirements
    show_system_info
    create_backup
    pull_code
    install_dependencies
    create_directories
    restart_pm2
    cleanup_backups
    check_application
    health_check
    show_summary
}

# Handle script arguments
case "$1" in
    --help|-h)
        echo "DeynCare Backend Deployment Script"
        echo ""
        echo "Usage: $0 [OPTIONS]"
        echo ""
        echo "Options:"
        echo "  --help, -h     Show this help message"
        echo "  --check        Check system requirements only"
        echo "  --backup       Create backup only"
        echo "  --pull         Pull code only"
        echo "  --restart      Restart PM2 only"
        echo "  --status       Show current status"
        echo "  --health       Perform health check only"
        echo ""
        echo "Examples:"
        echo "  $0              # Full deployment"
        echo "  $0 --check     # Check requirements"
        echo "  $0 --status    # Show status"
        echo "  $0 --health    # Health check"
        ;;
    --check)
        check_requirements
        show_system_info
        ;;
    --backup)
        create_backup
        ;;
    --pull)
        pull_code
        ;;
    --restart)
        restart_pm2
        ;;
    --status)
        show_system_info
        log "PM2 Status:"
        pm2 status
        log "Latest commit:"
        git log --oneline -1
        ;;
    --health)
        health_check
        ;;
    *)
        main
        ;;
esac 