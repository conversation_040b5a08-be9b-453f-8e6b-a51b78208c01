"use client"

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import { 
  Home, 
  Search, 
  ArrowLeft, 
  RefreshCw, 
  AlertTriangle,
  Compass,
  FileQuestion,
  Sparkles
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { cn } from '@/lib/utils'

export function NotFound({ 
  title = "Page Not Found",
  description = "Sorry, we couldn't find the page you're looking for.",
  showSearch = true,
  showSuggestions = true,
  customActions = null
}) {
  const router = useRouter()
  const [searchQuery, setSearchQuery] = useState('')
  const [isLoading, setIsLoading] = useState(false)

  // Animation state
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
  }, [])

  // Handle search
  const handleSearch = (e) => {
    e.preventDefault()
    if (searchQuery.trim()) {
      setIsLoading(true)
      // Simulate search delay
      setTimeout(() => {
        router.push(`/dashboard/shops?search=${encodeURIComponent(searchQuery)}`)
      }, 500)
    }
  }

  // Quick navigation suggestions
  const suggestions = [
    { name: 'Shop Management', href: '/dashboard/shops', icon: Home, description: 'Manage all shops and registrations' },
    { name: 'User Management', href: '/dashboard/users', icon: Compass, description: 'View and manage system users' },
    { name: 'Reports', href: '/dashboard/reports', icon: FileQuestion, description: 'Generate and view reports' },
    { name: 'Settings', href: '/dashboard/settings', icon: Sparkles, description: 'Configure system settings' },
  ]

  return (
    <div className="min-h-[70vh] flex items-center justify-center p-4">
      <div className="max-w-2xl w-full text-center space-y-8">
        {/* 404 Illustration */}
        <div className={cn(
          "relative transition-all duration-1000 ease-out",
          mounted ? "opacity-100 translate-y-0" : "opacity-0 translate-y-8"
        )}>
          <div className="relative">
            {/* Background gradient */}
            <div className="absolute inset-0 bg-gradient-to-r from-primary/20 to-secondary/20 rounded-full blur-3xl scale-150 opacity-30" />
            
            {/* 404 Text */}
            <div className="relative bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent">
              <h1 className="text-8xl md:text-9xl font-bold font-mono tracking-tight">
                404
              </h1>
            </div>
            
            {/* Floating elements */}
            <div className="absolute -top-4 -right-4 animate-bounce">
              <AlertTriangle className="h-8 w-8 text-yellow-500" />
            </div>
            <div className="absolute -bottom-4 -left-4 animate-pulse">
              <Search className="h-6 w-6 text-blue-500" />
            </div>
          </div>
        </div>

        {/* Content */}
        <div className={cn(
          "space-y-4 transition-all duration-1000 ease-out delay-300",
          mounted ? "opacity-100 translate-y-0" : "opacity-0 translate-y-8"
        )}>
          <h2 className="text-3xl md:text-4xl font-bold text-foreground">
            {title}
          </h2>
          <p className="text-lg text-muted-foreground max-w-md mx-auto">
            {description}
          </p>
          <Badge variant="secondary" className="text-xs">
            Error Code: 404 - Resource Not Found
          </Badge>
        </div>

        {/* Search Section */}
        {showSearch && (
          <div className={cn(
            "space-y-4 transition-all duration-1000 ease-out delay-500",
            mounted ? "opacity-100 translate-y-0" : "opacity-0 translate-y-8"
          )}>
            <h3 className="text-xl font-semibold text-foreground">
              Try searching for what you need
            </h3>
            <form onSubmit={handleSearch} className="max-w-md mx-auto">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  type="text"
                  placeholder="Search shops, users, or navigation..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10 pr-4 h-12 text-center bg-background border-input focus:border-primary focus:ring-1 focus:ring-primary"
                />
              </div>
              <Button 
                type="submit" 
                className="w-full mt-3 h-10"
                disabled={isLoading || !searchQuery.trim()}
              >
                {isLoading ? (
                  <>
                    <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                    Searching...
                  </>
                ) : (
                  <>
                    <Search className="h-4 w-4 mr-2" />
                    Search
                  </>
                )}
              </Button>
            </form>
          </div>
        )}

        {/* Quick Actions */}
        <div className={cn(
          "space-y-4 transition-all duration-1000 ease-out delay-700",
          mounted ? "opacity-100 translate-y-0" : "opacity-0 translate-y-8"
        )}>
          <div className="flex flex-wrap justify-center gap-3">
            <Button 
              onClick={() => router.back()} 
              variant="outline"
              className="h-10"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Go Back
            </Button>
            <Button 
              onClick={() => window.location.reload()} 
              variant="outline"
              className="h-10"
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh
            </Button>
          </div>

          {/* Custom Actions */}
          {customActions && (
            <div className="flex flex-wrap justify-center gap-3 mt-4">
              {customActions}
            </div>
          )}
        </div>

        {/* Suggestions */}
        {showSuggestions && (
          <div className={cn(
            "space-y-6 transition-all duration-1000 ease-out delay-900",
            mounted ? "opacity-100 translate-y-0" : "opacity-0 translate-y-8"
          )}>
            <h3 className="text-xl font-semibold text-foreground">
              Popular destinations
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 max-w-2xl mx-auto">
              {suggestions.map((suggestion, index) => {
                const Icon = suggestion.icon
                return (
                  <Link
                    key={suggestion.name}
                    href={suggestion.href}
                    className={cn(
                      "group p-4 rounded-lg border border-border bg-background hover:bg-muted/50 transition-all duration-200 hover:scale-105 hover:shadow-md",
                      "animate-fade-in-up"
                    )}
                    style={{ animationDelay: `${1200 + index * 100}ms` }}
                  >
                    <div className="flex items-start space-x-3">
                      <div className="flex-shrink-0">
                        <div className="p-2 rounded-md bg-primary/10 group-hover:bg-primary/20 transition-colors">
                          <Icon className="h-5 w-5 text-primary" />
                        </div>
                      </div>
                      <div className="flex-1 text-left">
                        <h4 className="font-medium text-foreground group-hover:text-primary transition-colors">
                          {suggestion.name}
                        </h4>
                        <p className="text-sm text-muted-foreground mt-1">
                          {suggestion.description}
                        </p>
                      </div>
                    </div>
                  </Link>
                )
              })}
            </div>
          </div>
        )}

        {/* Footer */}
        <div className={cn(
          "text-sm text-muted-foreground transition-all duration-1000 ease-out delay-1100",
          mounted ? "opacity-100 translate-y-0" : "opacity-0 translate-y-8"
        )}>
          <p>
            Need help? Contact our{' '}
            <Link href="/dashboard/settings" className="text-primary hover:underline">
              support team
            </Link>
          </p>
        </div>
      </div>
    </div>
  )
}

export default NotFound 