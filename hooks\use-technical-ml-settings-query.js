/**
 * Technical ML Settings Query Hook
 * Uses backend-matching services for SuperAdmin technical ML settings
 * Maps exactly to backend settings: ml_api_base_url, ml_api_key, ml_predict_endpoint, etc.
 */

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { 
  fetchTechnicalMLSettings,
  updateTechnicalMLSettings
} from '../lib/services/settings';

/**
 * Hook for managing SuperAdmin technical ML settings
 * @returns {Object} Technical ML settings query result and mutation functions
 */
export const useTechnicalMLSettingsQuery = () => {
  const queryClient = useQueryClient();

  // Query for technical ML settings
  const technicalMLSettingsQuery = useQuery({
    queryKey: ['technicalMLSettings'],
    queryFn: () => fetchTechnicalMLSettings(),
    staleTime: 5 * 60 * 1000, // 5 minutes
    cacheTime: 10 * 60 * 1000, // 10 minutes
    refetchOnWindowFocus: false,
    retry: 2,
    onError: (error) => {
      console.error('[useTechnicalMLSettingsQuery] Technical ML settings fetch error:', error);
    }
  });

  // Mutation for updating technical ML settings
  const updateTechnicalMLSettingsMutation = useMutation({
    mutationFn: updateTechnicalMLSettings,
    onSuccess: (data) => {
      console.log('[useTechnicalMLSettingsQuery] Technical ML settings updated:', data);
      // Invalidate and refetch technical ML settings
      queryClient.invalidateQueries(['technicalMLSettings']);
    },
    onError: (error) => {
      console.error('[useTechnicalMLSettingsQuery] Technical ML settings update error:', error);
    }
  });

  // Computed values
  const isLoading = technicalMLSettingsQuery.isLoading;
  const isError = technicalMLSettingsQuery.isError;
  const error = technicalMLSettingsQuery.error;

  // Technical ML settings data (exact backend format)
  const technicalMLSettingsData = technicalMLSettingsQuery.data || {
    mlApiBaseUrl: '',
    mlApiKey: '',
    mlPredictEndpoint: '/predict_single/',
    mlPredictionTimeout: 10,
    riskDataRetentionDays: 365
  };

  return {
    // Technical ML Settings Data
    technicalMLSettings: technicalMLSettingsData,
    
    // Loading States
    isLoading,
    isLoadingTechnicalMLSettings: technicalMLSettingsQuery.isLoading,
    
    // Error States
    isError,
    error,
    technicalMLSettingsError: technicalMLSettingsQuery.error,
    
    // Mutation States
    isSavingTechnicalMLSettings: updateTechnicalMLSettingsMutation.isLoading,
    
    // Mutation Functions
    updateTechnicalMLSettings: updateTechnicalMLSettingsMutation.mutateAsync,
    
    // Refetch Functions
    refetchTechnicalMLSettings: technicalMLSettingsQuery.refetch
  };
}; 