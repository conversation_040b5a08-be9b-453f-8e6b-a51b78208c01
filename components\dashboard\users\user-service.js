/**
 * User Service for Dashboard Components
 * This is a compatibility layer that connects the existing dashboard components
 * to our new centralized UserService implementation.
 */

import UserService from '@/lib/services/user';

/**
 * UserService class that maintains the existing API while using
 * our new centralized implementation under the hood
 */
class DashboardUserService {
  /**
   * Get users with pagination and filters
   * @param {Object} filters - Filter criteria
   * @param {number} page - Page number
   * @param {number} limit - Results per page
   * @returns {Promise<Object>} API response with users and pagination data
   */
  async getUsers(filters = {}, page = 1, limit = 10) {
    try {
      // Use our new centralized UserService
      const users = await UserService.getUsers(filters, page, limit);
      return {
        users,
        pagination: {
          currentPage: page,
          pageSize: limit,
          totalItems: users.length, // This will be improved when backend returns proper pagination
          totalPages: Math.ceil(users.length / limit)
        }
      };
    } catch (error) {
      console.error('Error fetching users:', error);
      // Error is already handled by the service
      return { users: [], pagination: { currentPage: 1, pageSize: 10, totalItems: 0, totalPages: 0 } };
    }
  }

  /**
   * Get a specific user by ID
   * @param {string} userId - User ID
   * @returns {Promise<Object>} User data
   */
  async getUserById(userId) {
    return UserService.getUserById(userId);
  }

  /**
   * Create a new user
   * @param {Object} userData - User data
   * @returns {Promise<Object>} Created user
   */
  async createUser(userData) {
    return UserService.createUser(userData);
  }

  /**
   * Update a user
   * @param {string} userId - User ID
   * @param {Object} userData - Updated user data
   * @returns {Promise<Object>} Updated user
   */
  async updateUser(userId, userData) {
    return UserService.updateUser(userId, userData);
  }

  /**
   * Delete a user
   * @param {string} userId - User ID
   * @param {string} reason - Reason for deletion
   * @returns {Promise<boolean>} Success status
   */
  async deleteUser(userId, reason) {
    return UserService.deleteUser(userId, reason);
  }

  /**
   * Change a user's status
   * @param {string} userId - User ID
   * @param {string} status - New status
   * @param {string} reason - Reason for status change
   * @returns {Promise<Object>} Updated user
   */
  async changeUserStatus(userId, status, reason) {
    return UserService.changeUserStatus(userId, status, reason);
  }

  /**
   * Update user profile
   * @param {Object} profileData - Profile data
   * @returns {Promise<Object>} Updated profile
   */
  async updateProfile(profileData) {
    return UserService.updateProfile(profileData);
  }

  /**
   * Change user password
   * @param {string} currentPassword - Current password
   * @param {string} newPassword - New password
   * @returns {Promise<boolean>} Success status
   */
  async changePassword(currentPassword, newPassword) {
    return UserService.changePassword(currentPassword, newPassword);
  }
}

// Export singleton instance to maintain compatibility with existing code
export default DashboardUserService;
