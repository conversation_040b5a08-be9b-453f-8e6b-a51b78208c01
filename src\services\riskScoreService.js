const { Customer } = require('../models');
const { logInfo, logError, AppError } = require('../utils');

/**
 * Simple Risk Score Service
 * Handles updating and retrieving customer risk scores
 */
class RiskScoreService {
  
  /**
   * Update customer risk score (from ML API)
   * @param {string} customerId - Customer ID
   * @param {Object} riskData - Risk assessment data
   * @returns {Object} Updated customer risk profile
   */
  async updateCustomerRiskScore(customerId, riskData) {
    try {
      const { riskScore, riskLevel, source = 'ml_api' } = riskData;
      
      // Find and update customer
      const updatedCustomer = await Customer.findOneAndUpdate(
        { customerId, isDeleted: false },
        {
          'riskProfile.riskScore': riskScore,
          'riskProfile.currentRiskLevel': riskLevel,
          'riskProfile.lastAssessment': new Date(),
          'riskProfile.mlSource': source,
          $inc: { 'riskProfile.assessmentCount': 1 }
        },
        { new: true, runValidators: true }
      );
      
      if (!updatedCustomer) {
        throw new AppError('Customer not found', 404, 'customer_not_found');
      }
      
      logInfo(`Risk score updated for customer ${customerId}: ${riskScore} (${riskLevel})`, 'RiskScoreService');
      
      return {
        success: true,
        customerId: updatedCustomer.customerId,
        customerName: updatedCustomer.CustomerName,
        riskProfile: updatedCustomer.riskProfile
      };
      
    } catch (error) {
      logError(`Failed to update risk score for customer ${customerId}`, 'RiskScoreService', error);
      throw error;
    }
  }
  
  /**
   * Get risk score list for a shop (Admin use)
   * @param {string} shopId - Shop ID
   * @param {Object} options - Query options
   * @returns {Array} List of customers with risk scores
   */
  async getShopRiskScores(shopId, options = {}) {
    try {
      const { 
        riskLevel, 
        sortBy = 'riskScore',
        sortOrder = 'desc',
        page = 1,
        limit = 50
      } = options;
      
      // Build query
      const query = { shopId, isDeleted: false };
      
      if (riskLevel) {
        query['riskProfile.currentRiskLevel'] = riskLevel;
      }
      
      // Calculate pagination
      const skip = (page - 1) * limit;
      
      // Build sort
      const sort = {};
      sort[`riskProfile.${sortBy}`] = sortOrder === 'asc' ? 1 : -1;
      
      // Get customers
      const customers = await Customer.find(query)
        .select('customerId CustomerName phone riskProfile outstandingBalance')
        .sort(sort)
        .skip(skip)
        .limit(Number(limit))
        .lean();
      
      const totalCount = await Customer.countDocuments(query);
      
      // Format response
      const riskScoreList = customers.map(customer => ({
        customerId: customer.customerId,
        customerName: customer.CustomerName,
        phone: customer.phone,
        riskScore: customer.riskProfile?.riskScore || 0,
        riskLevel: customer.riskProfile?.currentRiskLevel || 'Low Risk',
        lastAssessment: customer.riskProfile?.lastAssessment,
        outstandingBalance: customer.outstandingBalance || 0
      }));
      
      return {
        success: true,
        data: riskScoreList,
        pagination: {
          currentPage: Number(page),
          totalPages: Math.ceil(totalCount / limit),
          totalCount
        }
      };
      
    } catch (error) {
      logError(`Failed to get risk scores for shop ${shopId}`, 'RiskScoreService', error);
      throw error;
    }
  }
  
  /**
   * Get all risk scores across platform (SuperAdmin use)
   * @param {Object} options - Query options
   * @returns {Array} List of all customers with risk scores
   */
  async getAllRiskScores(options = {}) {
    try {
      const { 
        shopId,
        riskLevel, 
        sortBy = 'riskScore',
        sortOrder = 'desc',
        page = 1,
        limit = 100
      } = options;
      
      // Build query
      const query = { isDeleted: false };
      
      if (shopId) query.shopId = shopId;
      if (riskLevel) query['riskProfile.currentRiskLevel'] = riskLevel;
      
      // Calculate pagination
      const skip = (page - 1) * limit;
      
      // Build sort
      const sort = {};
      sort[`riskProfile.${sortBy}`] = sortOrder === 'asc' ? 1 : -1;
      
      // Get customers
      const customers = await Customer.find(query)
        .select('customerId CustomerName phone shopId riskProfile outstandingBalance')
        .sort(sort)
        .skip(skip)
        .limit(Number(limit))
        .lean();
      
      const totalCount = await Customer.countDocuments(query);
      
      // Format response
      const riskScoreList = customers.map(customer => ({
        customerId: customer.customerId,
        customerName: customer.CustomerName,
        phone: customer.phone,
        shopId: customer.shopId,
        riskScore: customer.riskProfile?.riskScore || 0,
        riskLevel: customer.riskProfile?.currentRiskLevel || 'Low Risk',
        lastAssessment: customer.riskProfile?.lastAssessment,
        outstandingBalance: customer.outstandingBalance || 0
      }));
      
      return {
        success: true,
        data: riskScoreList,
        pagination: {
          currentPage: Number(page),
          totalPages: Math.ceil(totalCount / limit),
          totalCount
        }
      };
      
    } catch (error) {
      logError('SuperAdmin failed to get all risk scores', 'RiskScoreService', error);
      throw error;
    }
  }
  
  /**
   * Get risk distribution summary for a shop
   * @param {string} shopId - Shop ID
   * @returns {Object} Risk distribution statistics
   */
  async getShopRiskSummary(shopId) {
    try {
      const summary = await Customer.aggregate([
        {
          $match: {
            shopId,
            isDeleted: false
          }
        },
        {
          $group: {
            _id: '$riskProfile.currentRiskLevel',
            count: { $sum: 1 },
            averageScore: { $avg: '$riskProfile.riskScore' },
            totalOutstanding: { $sum: '$outstandingBalance' }
          }
        }
      ]);
      
      // Get total customers for percentages
      const totalCustomers = await Customer.countDocuments({ shopId, isDeleted: false });
      
      // Format summary
      const riskDistribution = {};
      let totalAssessed = 0;
      
      summary.forEach(item => {
        const riskLevel = item._id || 'Unassessed';
        riskDistribution[riskLevel] = {
          count: item.count,
          percentage: totalCustomers > 0 ? Math.round((item.count / totalCustomers) * 100) : 0,
          averageScore: Math.round(item.averageScore || 0),
          totalOutstanding: item.totalOutstanding || 0
        };
        if (riskLevel !== 'Unassessed') totalAssessed += item.count;
      });
      
      logInfo(`Generated risk summary for shop ${shopId}`, 'RiskScoreService');
      
      return {
        success: true,
        shopId,
        totalCustomers,
        totalAssessed,
        assessmentCoverage: totalCustomers > 0 ? Math.round((totalAssessed / totalCustomers) * 100) : 0,
        riskDistribution
      };
      
    } catch (error) {
      logError(`Failed to get risk summary for shop ${shopId}`, 'RiskScoreService', error);
      throw error;
    }
  }
}

module.exports = new RiskScoreService(); 