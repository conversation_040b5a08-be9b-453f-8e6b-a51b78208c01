/**
 * Debug and Fix Payment Methods Configuration
 * This script checks and fixes the payment methods settings in the database
 */

const mongoose = require('mongoose');
require('dotenv').config();

// Import models
const Setting = require('./src/models/setting.model');

async function debugPaymentMethods() {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('✅ Connected to MongoDB');

    console.log('\n🔍 PAYMENT METHODS CONFIGURATION DEBUG\n');
    console.log('=' .repeat(60));

    // 1. Check current payment_methods_available setting
    console.log('\n1. Checking payment_methods_available setting:');
    const paymentMethodsSetting = await Setting.findOne({
      key: 'payment_methods_available',
      shopId: null
    });

    if (paymentMethodsSetting) {
      console.log('✅ Setting found:');
      console.log('  Key:', paymentMethodsSetting.key);
      console.log('  Value:', paymentMethodsSetting.value);
      console.log('  Data Type:', paymentMethodsSetting.dataType);
      console.log('  Category:', paymentMethodsSetting.category);
      console.log('  Updated By:', paymentMethodsSetting.updatedBy);
      console.log('  Created At:', paymentMethodsSetting.createdAt);
      console.log('  Updated At:', paymentMethodsSetting.updatedAt);
    } else {
      console.log('❌ payment_methods_available setting NOT FOUND');
    }

    // 2. Check enable_offline_payment setting
    console.log('\n2. Checking enable_offline_payment setting:');
    const offlinePaymentSetting = await Setting.findOne({
      key: 'enable_offline_payment',
      shopId: null
    });

    if (offlinePaymentSetting) {
      console.log('✅ Setting found:');
      console.log('  Key:', offlinePaymentSetting.key);
      console.log('  Value:', offlinePaymentSetting.value);
      console.log('  Data Type:', offlinePaymentSetting.dataType);
    } else {
      console.log('❌ enable_offline_payment setting NOT FOUND');
    }

    // 3. Check enable_online_payment setting
    console.log('\n3. Checking enable_online_payment setting:');
    const onlinePaymentSetting = await Setting.findOne({
      key: 'enable_online_payment',
      shopId: null
    });

    if (onlinePaymentSetting) {
      console.log('✅ Setting found:');
      console.log('  Key:', onlinePaymentSetting.key);
      console.log('  Value:', onlinePaymentSetting.value);
      console.log('  Data Type:', onlinePaymentSetting.dataType);
    } else {
      console.log('❌ enable_online_payment setting NOT FOUND');
    }

    // 4. List all payment-related settings
    console.log('\n4. All payment-related settings:');
    const allPaymentSettings = await Setting.find({
      $or: [
        { category: 'payment' },
        { key: { $regex: /payment/i } }
      ]
    }).sort({ key: 1 });

    if (allPaymentSettings.length > 0) {
      console.log(`✅ Found ${allPaymentSettings.length} payment-related settings:`);
      allPaymentSettings.forEach((setting, index) => {
        console.log(`  ${index + 1}. ${setting.key}:`);
        console.log(`     Value: ${JSON.stringify(setting.value)}`);
        console.log(`     Category: ${setting.category}`);
        console.log(`     Shop ID: ${setting.shopId || 'Global'}`);
      });
    } else {
      console.log('❌ No payment-related settings found');
    }

    // 5. Check if we need to create/fix the settings
    console.log('\n5. Fixing payment methods configuration:');
    
    let needsUpdate = false;
    
    // Fix payment_methods_available
    if (!paymentMethodsSetting) {
      console.log('🔧 Creating payment_methods_available setting...');
      await Setting.create({
        key: 'payment_methods_available',
        category: 'payment',
        displayName: 'Available Payment Methods',
        description: 'List of all payment methods available in the system',
        value: ['EVC Plus', 'Mobile Money', 'Card', 'offline'],
        dataType: 'array',
        defaultValue: ['EVC Plus', 'offline'],
        accessLevel: 'superAdmin',
        isEditable: true,
        isVisible: true,
        shopId: null,
        updatedBy: 'debug_script'
      });
      console.log('✅ Created payment_methods_available setting');
      needsUpdate = true;
    } else if (!paymentMethodsSetting.value.includes('offline')) {
      console.log('🔧 Adding "offline" to payment_methods_available...');
      const updatedMethods = [...paymentMethodsSetting.value];
      if (!updatedMethods.includes('offline')) {
        updatedMethods.push('offline');
      }
      
      await Setting.updateOne(
        { _id: paymentMethodsSetting._id },
        { 
          value: updatedMethods,
          updatedBy: 'debug_script',
          updatedAt: new Date()
        }
      );
      console.log('✅ Added "offline" to payment methods');
      needsUpdate = true;
    }

    // Fix enable_offline_payment
    if (!offlinePaymentSetting) {
      console.log('🔧 Creating enable_offline_payment setting...');
      await Setting.create({
        key: 'enable_offline_payment',
        category: 'payment',
        displayName: 'Enable Offline Payment',
        description: 'Whether offline payment methods are enabled',
        value: true,
        dataType: 'boolean',
        defaultValue: true,
        accessLevel: 'superAdmin',
        isEditable: true,
        isVisible: true,
        shopId: null,
        updatedBy: 'debug_script'
      });
      console.log('✅ Created enable_offline_payment setting');
      needsUpdate = true;
    } else if (offlinePaymentSetting.value !== true) {
      console.log('🔧 Enabling offline payment...');
      await Setting.updateOne(
        { _id: offlinePaymentSetting._id },
        { 
          value: true,
          updatedBy: 'debug_script',
          updatedAt: new Date()
        }
      );
      console.log('✅ Enabled offline payment');
      needsUpdate = true;
    }

    // Fix enable_online_payment
    if (!onlinePaymentSetting) {
      console.log('🔧 Creating enable_online_payment setting...');
      await Setting.create({
        key: 'enable_online_payment',
        category: 'payment',
        displayName: 'Enable Online Payment',
        description: 'Whether online payment methods are enabled',
        value: true,
        dataType: 'boolean',
        defaultValue: true,
        accessLevel: 'superAdmin',
        isEditable: true,
        isVisible: true,
        shopId: null,
        updatedBy: 'debug_script'
      });
      console.log('✅ Created enable_online_payment setting');
      needsUpdate = true;
    }

    if (needsUpdate) {
      console.log('\n🎉 Payment methods configuration has been updated!');
    } else {
      console.log('\n✅ Payment methods configuration is already correct');
    }

    // 6. Final verification
    console.log('\n6. Final verification:');
    const finalPaymentMethods = await Setting.findOne({
      key: 'payment_methods_available',
      shopId: null
    });

    if (finalPaymentMethods) {
      console.log('✅ Final payment_methods_available:');
      console.log('  Value:', finalPaymentMethods.value);
      console.log('  Includes "offline":', finalPaymentMethods.value.includes('offline'));
    }

    const finalOfflineEnabled = await Setting.findOne({
      key: 'enable_offline_payment',
      shopId: null
    });

    if (finalOfflineEnabled) {
      console.log('✅ Final enable_offline_payment:');
      console.log('  Value:', finalOfflineEnabled.value);
    }

    console.log('\n' + '=' .repeat(60));
    console.log('🎯 SUMMARY:');
    console.log('- Payment methods should now include "offline"');
    console.log('- Offline payments should be enabled');
    console.log('- Backend should accept offline payment requests');
    console.log('=' .repeat(60));

  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await mongoose.disconnect();
    console.log('\n✅ Disconnected from MongoDB');
  }
}

// Run the debug script
debugPaymentMethods();
