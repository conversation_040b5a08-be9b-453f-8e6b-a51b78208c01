const { User } = require('../../models');
const { 
  <PERSON><PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  logInfo, 
  logError 
} = require('../../utils');

/**
 * Get all users with optional filtering and pagination
 * @param {Object} filters - Filter criteria
 * @param {string} filters.role - Filter by role
 * @param {string} filters.status - Filter by status
 * @param {string} filters.shopId - Filter by shop ID
 * @param {boolean} filters.emailVerified - Filter by email verification status
 * @param {boolean} filters.isPaid - Filter by payment status
 * @param {boolean} filters.isSuspended - Filter by suspension status
 * @param {Object} filters.createdAt - Date range filter
 * @param {number} page - Page number (default 1)
 * @param {number} limit - Items per page (default 10, set to 0 for no limit)
 * @param {boolean} sanitize - Whether to sanitize user data (default true)
 * @returns {Promise<Object>} Users with pagination
 */
const getAllUsers = async (filters = {}, page = 1, limit = 10, sanitize = true) => {
  try {
    // Build MongoDB filter
    const mongoFilter = { isDeleted: false };
    
    // Apply filters
    if (filters.role) mongoFilter.role = filters.role;
    if (filters.status) mongoFilter.status = filters.status;
    if (filters.shopId) mongoFilter.shopId = filters.shopId;
    if (filters.emailVerified !== undefined) mongoFilter.emailVerified = filters.emailVerified;
    if (filters.isPaid !== undefined) mongoFilter.isPaid = filters.isPaid;
    if (filters.isSuspended !== undefined) mongoFilter.isSuspended = filters.isSuspended;
    
    // Handle date range filter
    if (filters.createdAt) {
      mongoFilter.createdAt = filters.createdAt;
    }
    
    // Prepare pagination options
    const paginationOptions = {
      page: parseInt(page) || 1,
      limit: parseInt(limit) || 10,
      sort: { createdAt: -1 },
      select: '-password -resetPasswordToken -resetPasswordExpires -verificationCode -verificationCodeExpires'
    };
    
    // If limit is 0, get all records (for export)
    if (paginationOptions.limit === 0) {
      const users = await User.find(mongoFilter)
        .select(paginationOptions.select)
        .sort(paginationOptions.sort)
        .lean();
      
      // Sanitize user data if requested
      const processedUsers = sanitize ? users.map(user => UserHelper.sanitizeUser(user)) : users;
      
      logInfo(`Retrieved ${processedUsers.length} users (no pagination)`, 'UserService');
      
      return {
        docs: processedUsers,
        totalDocs: processedUsers.length,
        limit: 0,
        totalPages: 1,
        page: 1,
        pagingCounter: 1,
        hasPrevPage: false,
        hasNextPage: false,
        prevPage: null,
        nextPage: null
      };
    }
    
    // Use PaginationHelper for consistent pagination
    const result = await PaginationHelper.paginate(User, mongoFilter, paginationOptions);
    
    // Sanitize user data if requested
    if (sanitize) {
      result.items = result.items.map(user => UserHelper.sanitizeUser(user));
    }
    
    // Transform to match expected format
    const response = {
      docs: result.items,
      totalDocs: result.pagination.total,
      limit: result.pagination.limit,
      totalPages: result.pagination.totalPages,
      page: result.pagination.page,
      pagingCounter: (result.pagination.page - 1) * result.pagination.limit + 1,
      hasPrevPage: result.pagination.page > 1,
      hasNextPage: result.pagination.page < result.pagination.totalPages,
      prevPage: result.pagination.page > 1 ? result.pagination.page - 1 : null,
      nextPage: result.pagination.page < result.pagination.totalPages ? result.pagination.page + 1 : null
    };
    
    logInfo(`Retrieved ${response.docs.length} users (page ${response.page}/${response.totalPages})`, 'UserService');
    
    return response;
  } catch (error) {
    if (error instanceof AppError) {
      throw error;
    }
    
    logError(`Error getting all users: ${error.message}`, 'UserService', error);
    throw new AppError('Failed to get users', 500, 'user_get_error');
  }
};

module.exports = getAllUsers; 