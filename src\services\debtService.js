/**
 * Debt Service
 * Service wrapper for debt-related operations
 */
const { getAllDebts } = require('../controllers/debt/getAllDebts');
const { getDebtById } = require('../controllers/debt/getDebtById');
const { createDebt } = require('../controllers/debt/createDebt');
const { updateDebt } = require('../controllers/debt/updateDebt');
const { deleteDebt } = require('../controllers/debt/deleteDebt');
const { getDebtStats } = require('../controllers/debt/getDebtStats');
const { addPayment } = require('../controllers/debt/addPayment');
const { getPaymentHistory } = require('../controllers/debt/getPaymentHistory');

class DebtService {
  /**
   * Get all debts
   * @param {Object} filters - Filter options
   * @param {number} page - Page number  
   * @param {number} limit - Items per page
   * @returns {Promise<Object>} Debts with pagination
   */
  static async getAllDebts(filters = {}, page = 1, limit = 10) {
    // Create mock request object
    const req = {
      query: { ...filters, page, limit },
      user: { shopId: filters.shopId }
    };
    
    // Create mock response object
    let result = {};
    const res = {
      json: (data) => {
        result = data;
      },
      status: () => res
    };
    
    // Call the controller
    await getAllDebts(req, res);
    
    return result.data || { docs: [], pagination: {} };
  }

  /**
   * Get debt by ID
   * @param {string} debtId - Debt ID
   * @returns {Promise<Object>} Debt data
   */
  static async getDebtById(debtId) {
    const req = {
      params: { debtId }
    };
    
    let result = {};
    const res = {
      json: (data) => {
        result = data;
      },
      status: () => res
    };
    
    await getDebtById(req, res);
    
    return result.data?.debt;
  }

  /**
   * Create a new debt
   * @param {Object} debtData - Debt data
   * @returns {Promise<Object>} Created debt
   */
  static async createDebt(debtData) {
    const req = {
      body: debtData,
      user: { shopId: debtData.shopId }
    };
    
    let result = {};
    const res = {
      json: (data) => {
        result = data;
      },
      status: () => res
    };
    
    await createDebt(req, res);
    
    return result.data?.debt;
  }

  /**
   * Update debt
   * @param {string} debtId - Debt ID
   * @param {Object} updateData - Update data
   * @returns {Promise<Object>} Updated debt
   */
  static async updateDebt(debtId, updateData) {
    const req = {
      params: { debtId },
      body: updateData
    };
    
    let result = {};
    const res = {
      json: (data) => {
        result = data;
      },
      status: () => res
    };
    
    await updateDebt(req, res);
    
    return result.data?.debt;
  }

  /**
   * Delete debt
   * @param {string} debtId - Debt ID
   * @returns {Promise<boolean>} Success status
   */
  static async deleteDebt(debtId) {
    const req = {
      params: { debtId }
    };
    
    let result = {};
    const res = {
      json: (data) => {
        result = data;
      },
      status: () => res
    };
    
    await deleteDebt(req, res);
    
    return result.success;
  }

  /**
   * Get debt statistics
   * @param {Object} filters - Filter options
   * @returns {Promise<Object>} Debt statistics
   */
  static async getDebtStats(filters = {}) {
    const req = {
      query: filters,
      user: { shopId: filters.shopId }
    };
    
    let result = {};
    const res = {
      json: (data) => {
        result = data;
      },
      status: () => res
    };
    
    await getDebtStats(req, res);
    
    return result.data?.stats;
  }

  /**
   * Add payment to debt
   * @param {string} debtId - Debt ID
   * @param {Object} paymentData - Payment data
   * @returns {Promise<Object>} Payment result
   */
  static async addPayment(debtId, paymentData) {
    const req = {
      params: { debtId },
      body: paymentData
    };
    
    let result = {};
    const res = {
      json: (data) => {
        result = data;
      },
      status: () => res
    };
    
    await addPayment(req, res);
    
    return result.data?.payment;
  }

  /**
   * Get payment history for debt
   * @param {string} debtId - Debt ID
   * @param {Object} filters - Filter options
   * @returns {Promise<Object>} Payment history
   */
  static async getPaymentHistory(debtId, filters = {}) {
    const req = {
      params: { debtId },
      query: filters
    };
    
    let result = {};
    const res = {
      json: (data) => {
        result = data;
      },
      status: () => res
    };
    
    await getPaymentHistory(req, res);
    
    return result.data?.payments || [];
  }
}

module.exports = DebtService; 