/**
 * Discount validation schemas
 */
const Joi = require('joi');

/**
 * Main discount validation schemas
 */
const discountSchemas = {
  /**
   * Schema for creating a new discount
   * POST /api/discounts
   */
  createDiscount: {
    body: Joi.object({
      code: Joi.string().uppercase().min(3).max(20).required()
        .messages({
          'string.empty': 'Discount code is required',
          'string.min': 'Discount code must be at least 3 characters',
          'string.max': 'Discount code cannot exceed 20 characters',
          'any.required': 'Discount code is required'
        }),
      
      description: Joi.string().max(200).optional()
        .messages({
          'string.max': 'Description cannot exceed 200 characters'
        }),
      
      type: Joi.string().valid('percentage', 'fixed').required()
        .messages({
          'any.only': 'Discount type must be either percentage or fixed',
          'any.required': 'Discount type is required'
        }),
      
      value: Joi.number().positive().required()
        .messages({
          'number.base': 'Discount value must be a number',
          'number.positive': 'Discount value must be positive',
          'any.required': 'Discount value is required'
        }),
      
      maxDiscountAmount: Joi.number().positive().allow(null).default(null)
        .messages({
          'number.base': 'Maximum discount amount must be a number',
          'number.positive': 'Maximum discount amount must be positive'
        }),
      
      isActive: Joi.boolean().default(true)
        .messages({
          'boolean.base': 'Active status must be a boolean'
        }),
      
      expiryDate: Joi.date().iso().min('now').optional()
        .messages({
          'date.base': 'Expiry date must be a valid date',
          'date.format': 'Expiry date must be in ISO format',
          'date.min': 'Expiry date cannot be in the past'
        }),
      
      usageLimit: Joi.number().integer().positive().allow(null).default(null)
        .messages({
          'number.base': 'Usage limit must be a number',
          'number.integer': 'Usage limit must be an integer',
          'number.positive': 'Usage limit must be positive'
        }),
      
      minPurchaseAmount: Joi.number().positive().allow(null).default(null)
        .messages({
          'number.base': 'Minimum purchase amount must be a number',
          'number.positive': 'Minimum purchase amount must be positive'
        }),
      
      applicableFor: Joi.array().items(
        Joi.string().valid('subscription', 'debt', 'all')
      ).default(['subscription'])
        .messages({
          'array.base': 'Applicable for must be an array',
          'any.only': 'Applicable for must contain valid options (subscription, debt, all)'
        }),
      
      shopId: Joi.string().when('$role', {
        is: 'superAdmin',
        then: Joi.string().optional(),
        otherwise: Joi.forbidden()
      }).messages({
        'any.required': 'Shop ID is required for discount creation'
      })
    })
  },
  
  /**
   * Schema for updating an existing discount
   * PUT /api/discounts/:discountId
   */
  updateDiscount: {
    params: Joi.object({
      discountId: Joi.string().required()
        .messages({
          'string.empty': 'Discount ID is required',
          'any.required': 'Discount ID is required'
        })
    }),
    body: Joi.object({
      code: Joi.string().uppercase().min(3).max(20).optional()
        .messages({
          'string.min': 'Discount code must be at least 3 characters',
          'string.max': 'Discount code cannot exceed 20 characters'
        }),
      
      description: Joi.string().max(200).optional()
        .messages({
          'string.max': 'Description cannot exceed 200 characters'
        }),
      
      type: Joi.string().valid('percentage', 'fixed').optional()
        .messages({
          'any.only': 'Discount type must be either percentage or fixed'
        }),
      
      value: Joi.number().positive()
        .messages({
          'number.base': 'Discount value must be a number',
          'number.positive': 'Discount value must be positive'
        }),
      
      maxDiscountAmount: Joi.number().positive().allow(null)
        .messages({
          'number.base': 'Maximum discount amount must be a number',
          'number.positive': 'Maximum discount amount must be positive'
        }),
      
      isActive: Joi.boolean().optional()
        .messages({
          'boolean.base': 'Active status must be a boolean'
        }),
      
      expiryDate: Joi.date().iso().min('now').optional()
        .messages({
          'date.base': 'Expiry date must be a valid date',
          'date.format': 'Expiry date must be in ISO format',
          'date.min': 'Expiry date cannot be in the past'
        }),
      
      usageLimit: Joi.number().integer().positive().allow(null).optional()
        .messages({
          'number.base': 'Usage limit must be a number',
          'number.integer': 'Usage limit must be an integer',
          'number.positive': 'Usage limit must be positive'
        }),
      
      minPurchaseAmount: Joi.number().positive().allow(null).optional()
        .messages({
          'number.base': 'Minimum purchase amount must be a number',
          'number.positive': 'Minimum purchase amount must be positive'
        }),
      
      applicableFor: Joi.array().items(
        Joi.string().valid('subscription', 'debt', 'all')
      ).optional()
        .messages({
          'array.base': 'Applicable for must be an array',
          'any.only': 'Applicable for must contain valid options (subscription, debt, all)'
        }),
      
      shopId: Joi.string().when('$role', {
        is: 'superAdmin',
        then: Joi.string().optional(),
        otherwise: Joi.forbidden()
      }).messages({
        'any.required': 'Shop ID is required for discount update'
      })
    }).min(1)
  },
  
  /**
   * Schema for validating a discount code
   * POST /api/discounts/validate
   */
  validateDiscount: {
    body: Joi.object({
      code: Joi.string().required()
        .messages({
          'string.empty': 'Discount code is required',
          'any.required': 'Discount code is required'
        }),
      
      amount: Joi.number().positive().required()
        .messages({
          'number.base': 'Purchase amount must be a number',
          'number.positive': 'Purchase amount must be positive',
          'any.required': 'Purchase amount is required'
        }),
      
      context: Joi.string().valid('subscription', 'debt').default('subscription')
        .messages({
          'any.only': 'Context must be one of: subscription, debt'
        }),
      
      shopId: Joi.string().when('$role', {
        is: 'superAdmin',
        then: Joi.string().optional(),
        otherwise: Joi.forbidden()
      }).messages({
        'any.required': 'Shop ID is required for discount validation'
      })
    })
  },
  
  /**
   * Schema for applying a discount
   * POST /api/discounts/apply
   */
  applyDiscount: {
    body: Joi.object({
      code: Joi.string().required()
        .messages({
          'string.empty': 'Discount code is required',
          'any.required': 'Discount code is required'
        }),
      
      amount: Joi.number().positive().required()
        .messages({
          'number.base': 'Purchase amount must be a number',
          'number.positive': 'Purchase amount must be positive',
          'any.required': 'Purchase amount is required'
        }),
      
      context: Joi.string().valid('subscription', 'debt').default('subscription')
        .messages({
          'any.only': 'Context must be one of: subscription, debt'
        }),
      
      shopId: Joi.string().when('$role', {
        is: 'superAdmin',
        then: Joi.string().optional(),
        otherwise: Joi.forbidden()
      }).messages({
        'any.required': 'Shop ID is required for discount application'
      })
    })
  },
  
  /**
   * Schema for getting all discounts
   * GET /api/discounts
   */
  getAllDiscounts: {
    query: Joi.object({
      page: Joi.number().integer().min(1).default(1),
      limit: Joi.number().integer().min(1).max(100).default(10),
      isActive: Joi.boolean().optional(),
      applicableFor: Joi.string().valid('subscription', 'debt', 'all').optional(),
      shopId: Joi.string().when('$role', {
        is: 'superAdmin',
        then: Joi.string().optional(),
        otherwise: Joi.forbidden()
      }).messages({
        'any.required': 'Shop ID is required for discount retrieval'
      })
    })
  },
  
  /**
   * Schema for getting a discount by ID
   * GET /api/discounts/:discountId
   */
  getDiscount: {
    params: Joi.object({
      discountId: Joi.string().required()
        .messages({
          'string.empty': 'Discount ID is required',
          'any.required': 'Discount ID is required'
        })
    })
  },
  
  /**
   * Schema for deleting a discount
   * DELETE /api/discounts/:discountId
   */
  deleteDiscount: {
    params: Joi.object({
      discountId: Joi.string().required()
        .messages({
          'string.empty': 'Discount ID is required',
          'any.required': 'Discount ID is required'
        })
    })
  }
};

module.exports = discountSchemas;
