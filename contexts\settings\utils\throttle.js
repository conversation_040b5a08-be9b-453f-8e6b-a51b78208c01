/**
 * Settings Context - Throttle Utility
 * Provides request throttling to prevent API hammering
 */

/**
 * Throttle API requests to prevent hammering
 * Improves performance by limiting request frequency and preventing redundant calls
 * 
 * @param {Object} requestTimeouts - Object to track timeouts
 * @param {Object} requestInProgress - Ref to track if a request is in progress
 * @param {string} key - Unique identifier for the request
 * @param {Function} fn - The async function to execute
 * @param {number} delay - Delay in milliseconds
 * @returns {Promise} - Promise that resolves with the function result
 */
export const throttleRequest = (requestTimeouts, requestInProgress, key, fn, delay = 2000) => {
  // Clear any existing timeout for this key
  if (requestTimeouts[key]) {
    clearTimeout(requestTimeouts[key]);
  }
  
  return new Promise((resolve) => {
    requestTimeouts[key] = setTimeout(async () => {
      try {
        // If there's already a request in progress, return a rejected promise
        if (requestInProgress.current) {
          resolve({ success: false, error: 'Request already in progress' });
          return;
        }
        
        // Set the request in progress flag
        requestInProgress.current = true;
        
        // Execute the function
        const result = await fn();
        resolve(result);
      } catch (error) {
        console.error(`Error in throttled request (${key}):`, error);
        resolve({ success: false, error: error.message });
      } finally {
        // Clear the request in progress flag
        requestInProgress.current = false;
        delete requestTimeouts[key];
      }
    }, delay);
  });
};
