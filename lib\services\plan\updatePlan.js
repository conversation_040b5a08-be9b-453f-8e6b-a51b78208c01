/**
 * Update Plan Service
 * 
 * Updates an existing pricing plan
 * UPDATED: Now matches backend implementation exactly - all features forced to true
 */

import apiBridge from '@/lib/api/bridge';
import { ENDPOINTS } from '@/lib/api/contract';
import { handleError, processApiResponse, logApiCall } from '../baseService';

/**
 * Update an existing plan (SuperAdmin only)
 * Backend ensures all features remain true even if input tries to set them false
 * @param {string} planId - Plan ID to update
 * @param {Object} updateData - Data to update
 * @returns {Promise<Object>} API response with updated plan
 */
async function updatePlan(planId, updateData) {
  try {
    if (!planId) {
      throw new Error('Plan ID is required');
    }

    if (!updateData || Object.keys(updateData).length === 0) {
      throw new Error('Update data is required');
    }

    // Process update data to match backend expectations
    const processedData = { ...updateData };

    // If features are being updated, ensure they're all set to true (backend enforces this)
    if (processedData.features) {
      Object.keys(processedData.features).forEach(feature => {
        processedData.features[feature] = true;
      });
    }

    // Validate enum values if provided
    if (processedData.type && !['trial', 'monthly', 'yearly'].includes(processedData.type)) {
      throw new Error('type must be one of: trial, monthly, yearly');
    }

    if (processedData.pricing?.billingCycle && 
        !['one-time', 'monthly', 'yearly'].includes(processedData.pricing.billingCycle)) {
      throw new Error('pricing.billingCycle must be one of: one-time, monthly, yearly');
    }

    // Validate numeric values
    if (processedData.pricing?.basePrice !== undefined && processedData.pricing.basePrice < 0) {
      throw new Error('pricing.basePrice must be >= 0');
    }

    if (processedData.pricing?.trialDays !== undefined && processedData.pricing.trialDays < 0) {
      throw new Error('pricing.trialDays must be >= 0');
    }

    if (processedData.pricing?.setupFee !== undefined && processedData.pricing.setupFee < 0) {
      throw new Error('pricing.setupFee must be >= 0');
    }

    // Validate string lengths
    if (processedData.name && processedData.name.length > 50) {
      throw new Error('name must be 50 characters or less');
    }

    if (processedData.displayName && processedData.displayName.length > 100) {
      throw new Error('displayName must be 100 characters or less');
    }

    if (processedData.description && processedData.description.length > 500) {
      throw new Error('description must be 500 characters or less');
    }

    logApiCall('PlanService.updatePlan', ENDPOINTS.PLANS.DETAIL(planId), processedData);

    const response = await apiBridge.put(ENDPOINTS.PLANS.DETAIL(planId), processedData, {
      skipCache: true
    });

    const result = processApiResponse(response, 'Plan updated successfully');

    // Clear related caches
    if (typeof window !== 'undefined') {
      localStorage.removeItem('cache_plans-list');
      localStorage.removeItem(`cache_plan-${planId}`);
      localStorage.removeItem('cache_plan-stats');
    }

    return result;
  } catch (error) {
    handleError(error, 'PlanService.updatePlan', true);
    throw error;
  }
}

export default updatePlan;