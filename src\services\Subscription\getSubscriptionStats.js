/**
 * Get Subscription Stats Service
 * Provides stats data for SuperAdmin dashboard
 */
const { Subscription, Shop } = require('../../models');
const mongoose = require('mongoose');

/**
 * Get subscription stats data
 * @param {Object} options - Stats options
 * @param {Date} options.startDate - Start date for stats
 * @param {Date} options.endDate - End date for stats
 * @param {String} options.groupBy - Group by (day, week, month)
 * @returns {Object} - Stats data
 */
const getSubscriptionStats = async (options) => {
  const { startDate, endDate, groupBy = 'day' } = options;
  
  // Get total subscriptions count
  const totalSubscriptions = await Subscription.countDocuments({});
  
  // Get active subscriptions (include both 'active' and 'trial' statuses)
  const activeSubscriptions = await Subscription.countDocuments({
    status: { $in: ['active', 'trial'] }
  });
  
  // Get trial subscriptions (status is exactly 'trial')
  const trialSubscriptions = await Subscription.countDocuments({
    status: 'trial'
  });
  
  // Get expiring soon subscriptions (next 7 days, both 'active' and 'trial')
  const nextWeek = new Date();
  nextWeek.setDate(nextWeek.getDate() + 7);
  
  const expiringSubscriptions = await Subscription.countDocuments({
    status: { $in: ['active', 'trial'] },
    $or: [
      { 'dates.endDate': { $lte: nextWeek, $gte: new Date() } },
      { 'dates.trialEndsAt': { $lte: nextWeek, $gte: new Date() } }
    ]
  });
  
  // Calculate average subscription lifetime
  const subscriptionLifetimeData = await Subscription.aggregate([
    {
      $match: {
        status: { $in: ['active', 'expired', 'cancelled'] },
        createdAt: { $exists: true }
      }
    },
    {
      $project: {
        lifetimeDays: {
          $divide: [
            { $subtract: [{ $ifNull: ['$expiresAt', new Date()] }, '$createdAt'] },
            1000 * 60 * 60 * 24
          ]
        }
      }
    },
    {
      $group: {
        _id: null,
        averageLifetime: { $avg: '$lifetimeDays' }
      }
    }
  ]);
  
  const averageLifetime = subscriptionLifetimeData.length > 0 
    ? Math.round(subscriptionLifetimeData[0].averageLifetime) 
    : 0;
  
  // Calculate total revenue from subscription pricing data
  const revenueData = await Subscription.aggregate([
    {
      $match: {
        status: { $in: ['active', 'expired'] },
        isDeleted: false,
        'plan.type': { $ne: 'trial' }
      }
    },
    {
      $addFields: {
        finalPrice: {
          $cond: {
            if: { $and: [
              { $eq: ['$pricing.discount.active', true] },
              { $eq: ['$pricing.discount.type', 'percentage'] }
            ]},
            then: {
              $subtract: [
                '$pricing.basePrice',
                { $multiply: ['$pricing.basePrice', { $divide: ['$pricing.discount.value', 100] }] }
              ]
            },
            else: {
              $cond: {
                if: { $and: [
                  { $eq: ['$pricing.discount.active', true] },
                  { $eq: ['$pricing.discount.type', 'fixed'] }
                ]},
                then: { $subtract: ['$pricing.basePrice', '$pricing.discount.amount'] },
                else: '$pricing.basePrice'
              }
            }
          }
        }
      }
    },
    {
      $group: {
        _id: '$plan.type',
        totalAmount: { $sum: '$finalPrice' },
        count: { $sum: 1 }
      }
    }
  ]);
  
  // Format revenue by plan type
  const revenueByPlanType = {};
  let totalRevenue = 0;
  
  revenueData.forEach(item => {
    revenueByPlanType[item._id] = {
      totalAmount: Math.round(item.totalAmount * 100) / 100,
      count: item.count
    };
    totalRevenue += item.totalAmount;
  });
  
  totalRevenue = Math.round(totalRevenue * 100) / 100;
  
  // Get subscription trends
  let dateFormat;
  let groupField;
  
  switch(groupBy) {
    case 'week':
      dateFormat = '%Y-%U';
      groupField = { year: '$year', week: '$week' };
      break;
    case 'month':
      dateFormat = '%Y-%m';
      groupField = { year: '$year', month: '$month' };
      break;
    default:
      dateFormat = '%Y-%m-%d';
      groupField = { year: '$year', month: '$month', day: '$day' };
  }
  
  const trends = await Subscription.aggregate([
    {
      $match: {
        createdAt: { $gte: startDate, $lte: endDate }
      }
    },
    {
      $project: {
        date: { $dateToString: { format: dateFormat, date: '$createdAt' } },
        planType: '$plan.type',
        year: { $year: '$createdAt' },
        month: { $month: '$createdAt' },
        week: { $week: '$createdAt' },
        day: { $dayOfMonth: '$createdAt' }
      }
    },
    {
      $group: {
        _id: {
          date: '$date',
          planType: '$planType',
          ...groupField
        },
        count: { $sum: 1 }
      }
    },
    {
      $sort: { '_id.year': 1, '_id.month': 1, '_id.week': 1, '_id.day': 1 }
    }
  ]);
  
  // Get plan distribution
  const planDistribution = await Subscription.aggregate([
    {
      $match: {
        status: 'active'
      }
    },
    {
      $group: {
        _id: '$plan.type',
        count: { $sum: 1 }
      }
    },
    {
      $sort: { count: -1 }
    }
  ]);
  
  return {
    overview: {
      totalSubscriptions,
      activeSubscriptions,
      trialSubscriptions,
      expiringSubscriptions,
      averageLifetime,
      totalRevenue
    },
    revenue: {
      total: totalRevenue,
      byPlanType: revenueByPlanType
    },
    trends,
    planDistribution
  };
};

module.exports = getSubscriptionStats; 