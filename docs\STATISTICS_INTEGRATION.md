# Statistics Integration Documentation

This document provides comprehensive documentation for the statistics integration in the DeynCare frontend dashboard, including the new API endpoints, components, and usage patterns.

## Overview

The statistics integration provides comprehensive analytics for both **User Management** and **Shop Management** modules, following the established patterns of services, hooks, and reusable components.

## Architecture

### Backend Integration
- **User Statistics API**: `GET /api/users/stats`
- **Shop Statistics API**: `GET /api/shops/stats`
- Both endpoints require **SuperAdmin** authentication
- Support query parameters: `startDate`, `endDate`, `groupBy`

### Frontend Architecture
```
lib/services/
├── user/
│   ├── getUserStats.js          # User statistics service
│   └── index.js                 # Updated with getUserStats
└── shop/
    ├── getShopStats.js          # Shop statistics service
    └── index.js                 # Updated with getShopStats

hooks/
├── use-user-stats.js            # User statistics hook
└── use-shop-stats.js            # Shop statistics hook

components/dashboard/common/
├── user-stats-cards.jsx         # User statistics cards
├── shop-stats-cards.jsx         # Shop statistics cards
└── kpi-card.jsx                 # Reusable KPI card component

app/dashboard/
├── analytics/page.jsx           # Main analytics dashboard
└── users/UsersOverview.jsx      # User overview component
```

## API Endpoints

### User Statistics
```javascript
GET /api/users/stats?startDate=2024-01-01&endDate=2024-12-31&groupBy=month
```

**Response Structure:**
```javascript
{
  "success": true,
  "data": {
    "summary": {
      "totalUsers": 1250,
      "activeUsers": 1100,
      "inactiveUsers": 125,
      "suspendedUsers": 25
    },
    "activity": {
      "recentRegistrations": 45,
      "activeInLastWeek": 890,
      "verifiedUsers": 1180,
      "paidUsers": 950
    },
    "distribution": {
      "byRole": [...],
      "byStatus": [...]
    },
    "trends": [...]
  }
}
```

### Shop Statistics
```javascript
GET /api/shops/stats?startDate=2024-01-01&endDate=2024-12-31&groupBy=month
```

**Response Structure:**
```javascript
{
  "success": true,
  "data": {
    "summary": {
      "totalShops": 450,
      "activeShops": 420,
      "pendingVerificationShops": 15,
      "revenueGeneratingShops": 380
    },
    "activity": {
      "recentRegistrations": 12,
      "highActivityShops": 85,
      "shopsWithSubscriptions": 380,
      "averageRevenuePerShop": 2450.50
    },
    "aggregatedBusinessMetrics": {
      "totalRevenue": 1250000,
      "totalCustomers": 15000,
      "totalProducts": 45000
    },
    "distribution": {
      "byStatus": [...],
      "byBusinessType": [...],
      "byLocation": [...]
    },
    "trends": [...]
  }
}
```

## Services

### UserService.getUserStats()
```javascript
import UserService from '@/lib/services/user';

const stats = await UserService.getUserStats({
  startDate: '2024-01-01',
  endDate: '2024-12-31',
  groupBy: 'month'
});
```

### ShopService.getShopStats()
```javascript
import ShopService from '@/lib/services/shop';

const stats = await ShopService.getShopStats({
  startDate: '2024-01-01', 
  endDate: '2024-12-31',
  groupBy: 'month'
});
```

## Hooks

### useUserStats Hook
```javascript
import { useUserStats } from '@/hooks/use-user-stats';

function UserDashboard() {
  const {
    summary,           // { totalUsers, activeUsers, inactiveUsers, suspendedUsers }
    activity,          // { recentRegistrations, activeInLastWeek, verifiedUsers, paidUsers }
    distribution,      // { byRole, byStatus }
    trends,           // Time-series data
    loading,          // Loading state
    error,            // Error state
    refreshStats,     // Manual refresh function
    hasData,          // Whether data is available
    isStale           // Whether data is stale (>5 minutes old)
  } = useUserStats({
    defaultFilters: { startDate: '2024-01-01', endDate: '2024-12-31' },
    autoFetch: true,
    refreshInterval: 300000 // 5 minutes
  });

  return (
    <div>
      <h2>Total Users: {summary.totalUsers}</h2>
      <h3>Active Users: {summary.activeUsers}</h3>
      <button onClick={() => refreshStats(true)}>Refresh</button>
    </div>
  );
}
```

### useShopStats Hook
```javascript
import { useShopStats } from '@/hooks/use-shop-stats';

function ShopDashboard() {
  const {
    summary,           // { totalShops, activeShops, pendingVerificationShops, revenueGeneratingShops }
    activity,          // { recentRegistrations, highActivityShops, shopsWithSubscriptions, averageRevenuePerShop }
    businessMetrics,   // { totalRevenue, totalCustomers, totalProducts }
    distribution,      // { byStatus, byBusinessType, byLocation }
    trends,           // Time-series data
    loading,          // Loading state
    error,            // Error state
    refreshStats,     // Manual refresh function
    hasData,          // Whether data is available
    isStale           // Whether data is stale (>5 minutes old)
  } = useShopStats({
    defaultFilters: { startDate: '2024-01-01', endDate: '2024-12-31' },
    autoFetch: true,
    refreshInterval: 300000 // 5 minutes
  });

  return (
    <div>
      <h2>Total Shops: {summary.totalShops}</h2>
      <h3>Active Shops: {summary.activeShops}</h3>
      <h3>Total Revenue: ${businessMetrics.totalRevenue}</h3>
      <button onClick={() => refreshStats(true)}>Refresh</button>
    </div>
  );
}
```

## Components

### UserStatsCards Component
```javascript
import { UserStatsCards } from '@/components/dashboard/common/user-stats-cards';

<UserStatsCards 
  filters={{ 
    startDate: '2024-01-01', 
    endDate: '2024-12-31',
    groupBy: 'month'
  }}
  autoRefresh={true}
  refreshInterval={300000}
  className="my-4"
  onRefresh={() => console.log('Stats refreshed')}
/>
```

**Features:**
- Displays 8 KPI cards with user metrics
- Auto-refresh capability
- Loading and error states
- Manual refresh button
- Responsive grid layout

### ShopStatsCards Component
```javascript
import { ShopStatsCards } from '@/components/dashboard/common/shop-stats-cards';

<ShopStatsCards 
  filters={{ 
    startDate: '2024-01-01', 
    endDate: '2024-12-31',
    groupBy: 'month'
  }}
  autoRefresh={true}
  refreshInterval={300000}
  className="my-4"
  onRefresh={() => console.log('Stats refreshed')}
/>
```

**Features:**
- Displays 12 KPI cards with shop metrics
- Currency formatting for revenue data
- Large number formatting (K, M units)
- Auto-refresh capability
- Loading and error states

### KpiCard Component (Reusable)
```javascript
import { KpiCard } from '@/components/dashboard/common/kpi-card';

<KpiCard
  title="Total Users"
  value="1,250"
  description="All registered users"
  icon="users"
  trend="up"
  trendValue="12.5%"
  trendLabel="from last month"
  loading={false}
/>
```

## Analytics Dashboard

### Main Analytics Page
Located at `/app/dashboard/analytics/page.jsx`

**Features:**
- **Overview Tab**: Both user and shop statistics
- **Users Tab**: Detailed user analytics
- **Shops Tab**: Detailed shop analytics
- Date range selection (7d, 30d, 90d, 1y)
- Grouping options (day, week, month)
- Export functionality (placeholder)
- SuperAdmin access control

**Usage:**
```javascript
// Access the analytics dashboard
window.location.href = '/dashboard/analytics';

// Direct to specific tab
window.location.href = '/dashboard/analytics?tab=users';
window.location.href = '/dashboard/analytics?tab=shops';
```

### Users Overview Component
Located at `/components/dashboard/users/UsersOverview.jsx`

**Integration Example:**
```javascript
import { UsersOverview } from '@/components/dashboard/users/UsersOverview';

function UsersPage() {
  return (
    <div>
      {/* Show user statistics at the top of users page */}
      <UsersOverview showQuickActions={true} />
      
      {/* Rest of users page content */}
      <UsersTable ... />
    </div>
  );
}
```

## Access Control

All statistics endpoints and components require **SuperAdmin** role:

```javascript
// Backend: Both endpoints have middleware
router.get('/stats', authenticate, authorize(['superAdmin']), getUserStats);
router.get('/stats', authenticate, authorize(['superAdmin']), getShopStats);

// Frontend: Analytics page checks user role
if (user?.role !== 'superAdmin') {
  return <AccessDenied />;
}
```

## Error Handling

### Service Level
```javascript
// Services use standardized error handling
try {
  const stats = await UserService.getUserStats(options);
  return stats;
} catch (error) {
  console.error('Error fetching stats:', error);
  BaseService.handleError(error, 'UserStatsService.getUserStats', true);
  throw error;
}
```

### Hook Level
```javascript
// Hooks provide error state and recovery
const { error, refreshStats } = useUserStats();

if (error) {
  return (
    <div>
      <p>Error: {error}</p>
      <button onClick={() => refreshStats(true)}>Retry</button>
    </div>
  );
}
```

### Component Level
```javascript
// Components show error states with retry options
if (error && !hasData) {
  return (
    <div className="p-6 text-center border rounded-lg bg-card">
      <p className="text-muted-foreground mb-4">Failed to load statistics</p>
      <Button onClick={handleRefresh} variant="outline" size="sm">
        <RefreshCw className="h-4 w-4 mr-2" />
        Retry
      </Button>
    </div>
  );
}
```

## Performance Considerations

### Caching
- API responses are cached by the bridge system
- 5-minute staleness detection for auto-refresh
- Manual refresh clears cache and fetches fresh data

### Throttling
- Hooks include request throttling (2-second minimum between requests)
- Abort controllers cancel pending requests
- Rate limit retry with exponential backoff

### Memory Management
- Cleanup functions in hooks prevent memory leaks
- Component unmount detection prevents state updates
- Interval cleanup on component unmount

## Integration Examples

### Add User Stats to Existing Dashboard
```javascript
// In any dashboard page
import { UserStatsCards } from '@/components/dashboard/common/user-stats-cards';

function Dashboard() {
  return (
    <div>
      <h1>Dashboard</h1>
      
      {/* Add user statistics section */}
      <section className="mb-8">
        <UserStatsCards 
          filters={{ startDate: '2024-01-01', endDate: '2024-12-31' }}
          autoRefresh={true}
          refreshInterval={300000}
        />
      </section>
      
      {/* Rest of dashboard content */}
    </div>
  );
}
```

### Add Shop Stats to Existing Page
```javascript
// In shop management page
import { ShopStatsCards } from '@/components/dashboard/common/shop-stats-cards';

function ShopsPage() {
  return (
    <div>
      <h1>Shop Management</h1>
      
      {/* Add shop statistics overview */}
      <section className="mb-8">
        <ShopStatsCards 
          filters={{ startDate: '2024-01-01', endDate: '2024-12-31' }}
          autoRefresh={false}
        />
      </section>
      
      {/* Shop management table */}
      <ShopsTable ... />
    </div>
  );
}
```

### Custom Statistics Integration
```javascript
// Custom component using the hooks directly
import { useUserStats, useShopStats } from '@/hooks';

function CustomAnalytics() {
  const userStats = useUserStats({ autoFetch: true });
  const shopStats = useShopStats({ autoFetch: true });
  
  const totalPlatformUsers = userStats.summary.totalUsers;
  const totalPlatformRevenue = shopStats.businessMetrics.totalRevenue;
  
  return (
    <div>
      <h2>Platform Overview</h2>
      <p>Total Users: {totalPlatformUsers}</p>
      <p>Total Revenue: ${totalPlatformRevenue}</p>
    </div>
  );
}
```

## Navigation Integration

The analytics dashboard can be accessed through:

1. **Direct URL**: `/dashboard/analytics`
2. **User Dashboard**: Links to detailed analytics
3. **Navigation Menu**: Add analytics link to sidebar
4. **Quick Actions**: From user/shop management pages

```javascript
// Example navigation integration
<Link href="/dashboard/analytics">
  <Button variant="outline">
    <BarChart3 className="h-4 w-4 mr-2" />
    View Analytics
  </Button>
</Link>
```

## Future Enhancements

### Planned Features
1. **Charts Integration**: Add trend charts using the existing chart components
2. **Export Functionality**: CSV/PDF export of statistics
3. **Scheduled Reports**: Email reports to SuperAdmins
4. **Real-time Updates**: WebSocket integration for live updates
5. **Comparative Analytics**: Period-over-period comparisons
6. **Advanced Filtering**: More granular filter options

### Extension Points
1. **Custom KPI Cards**: Create specialized cards for specific metrics
2. **Dashboard Layouts**: Customizable dashboard layouts
3. **Notification Integration**: Alerts for threshold breaches
4. **API Extensions**: Additional statistical endpoints

## Troubleshooting

### Common Issues

1. **Access Denied**: Ensure user has SuperAdmin role
2. **API Errors**: Check backend logs for endpoint issues
3. **Loading States**: Verify network connection and API availability
4. **Stale Data**: Use manual refresh or check auto-refresh settings

### Debug Mode
```javascript
// Enable debug logging
localStorage.setItem('debug', 'true');

// Check service calls
console.log('[UserStats] Fetching with filters:', filters);
```

This integration provides a comprehensive, scalable foundation for analytics in the DeynCare platform while maintaining consistency with existing patterns and best practices. 