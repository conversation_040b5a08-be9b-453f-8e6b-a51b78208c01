"use client";

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';

import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { AlertTriangle } from "lucide-react";

// Form schema for deletion
const deleteFormSchema = z.object({
  reason: z.string()
    .min(3, { message: "Reason must be at least 3 characters" })
    .max(200, { message: "Reason must not exceed 200 characters" })
});

export function DeleteShopDialog({ isOpen, onClose, shopToDelete, onDelete, isDeleting = false }) {
  // Initialize form with react-hook-form
  const form = useForm({
    resolver: zodResolver(deleteFormSchema),
    defaultValues: {
      reason: ""
    }
  });

  // Handle form submission
  const handleSubmit = async (data) => {
    try {
      await onDelete(shopToDelete.shopId, data.reason);
      form.reset();
    } catch (error) {
      console.error("Error deleting shop:", error);
    }
  };

  if (!shopToDelete) return null;

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !isDeleting && onClose()}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <div className="flex items-center gap-2 text-destructive">
            <AlertTriangle className="h-5 w-5" />
            <DialogTitle>Delete Shop</DialogTitle>
          </div>
          <DialogDescription className="pt-2">
            Are you sure you want to delete <span className="font-semibold">{shopToDelete.shopName}</span>? This action cannot be undone.
            All data associated with this shop will be permanently removed.
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="reason"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Reason for deletion</FormLabel>
                  <FormControl>
                    <Textarea 
                      placeholder="Please provide a reason for deleting this shop" 
                      className="min-h-[100px]"
                      {...field} 
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <DialogFooter className="gap-2 sm:gap-0">
              <Button 
                type="button" 
                variant="outline" 
                onClick={onClose}
                disabled={isDeleting}
              >
                Cancel
              </Button>
              <Button 
                type="submit" 
                variant="destructive"
                disabled={isDeleting}
              >
                {isDeleting ? "Deleting..." : "Delete Shop"}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
