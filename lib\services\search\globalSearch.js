import { baseService } from '../baseService';

class GlobalSearchService {
  constructor() {
    this.baseUrl = '/api/search';
  }

  /**
   * Search across all entities (shops, users, etc.)
   * @param {string} query - Search query
   * @param {Object} options - Search options
   * @returns {Promise<Object>} Search results
   */
  async searchAll(query, options = {}) {
    try {
      const params = new URLSearchParams({
        q: query,
        limit: options.limit || 10,
        categories: options.categories ? options.categories.join(',') : '',
        ...options
      });

      const response = await baseService.get(`${this.baseUrl}/all?${params}`);
      return response.data;
    } catch (error) {
      console.error('[GlobalSearchService] Search all error:', error);
      
      // Return mock data for development
      return this.getMockSearchResults(query);
    }
  }

  /**
   * Get search suggestions
   * @param {string} query - Search query
   * @param {string} type - Type of suggestions (shops, users, etc.)
   * @returns {Promise<Array>} Suggestions array
   */
  async getSuggestions(query, type = 'all') {
    try {
      const params = new URLSearchParams({
        q: query,
        type,
        limit: 5
      });

      const response = await baseService.get(`${this.baseUrl}/suggestions?${params}`);
      return response.data;
    } catch (error) {
      console.error('[GlobalSearchService] Get suggestions error:', error);
      
      // Return mock suggestions for development
      return this.getMockSuggestions(query, type);
    }
  }

  /**
   * Search shops specifically
   * @param {string} query - Search query
   * @param {Object} filters - Additional filters
   * @returns {Promise<Object>} Shop search results
   */
  async searchShops(query, filters = {}) {
    try {
      const params = new URLSearchParams({
        q: query,
        status: filters.status || '',
        subscription: filters.subscription || '',
        limit: filters.limit || 20,
        page: filters.page || 1
      });

      const response = await baseService.get(`${this.baseUrl}/shops?${params}`);
      return response.data;
    } catch (error) {
      console.error('[GlobalSearchService] Search shops error:', error);
      throw error;
    }
  }

  /**
   * Search users specifically
   * @param {string} query - Search query
   * @param {Object} filters - Additional filters
   * @returns {Promise<Object>} User search results
   */
  async searchUsers(query, filters = {}) {
    try {
      const params = new URLSearchParams({
        q: query,
        role: filters.role || '',
        status: filters.status || '',
        limit: filters.limit || 20,
        page: filters.page || 1
      });

      const response = await baseService.get(`${this.baseUrl}/users?${params}`);
      return response.data;
    } catch (error) {
      console.error('[GlobalSearchService] Search users error:', error);
      throw error;
    }
  }

  /**
   * Get recent searches for user
   * @returns {Promise<Array>} Recent searches
   */
  async getRecentSearches() {
    try {
      // For now, get from localStorage
      const recent = localStorage.getItem('deyncare_recent_searches');
      return recent ? JSON.parse(recent) : [];
    } catch (error) {
      console.error('[GlobalSearchService] Get recent searches error:', error);
      return [];
    }
  }

  /**
   * Save search to recent searches
   * @param {Object} searchItem - Search item to save
   */
  async saveRecentSearch(searchItem) {
    try {
      const recent = await this.getRecentSearches();
      const filtered = recent.filter(item => item.id !== searchItem.id);
      const updated = [searchItem, ...filtered].slice(0, 5); // Keep only 5 recent
      
      localStorage.setItem('deyncare_recent_searches', JSON.stringify(updated));
    } catch (error) {
      console.error('[GlobalSearchService] Save recent search error:', error);
    }
  }

  /**
   * Clear recent searches
   */
  async clearRecentSearches() {
    try {
      localStorage.removeItem('deyncare_recent_searches');
    } catch (error) {
      console.error('[GlobalSearchService] Clear recent searches error:', error);
    }
  }

  /**
   * Mock search results for development
   * @private
   */
  getMockSearchResults(query) {
    const mockData = {
      navigation: [
        { id: 'shops', name: 'Shop Management', icon: 'Store', href: '/dashboard/shops', category: 'Navigation' },
        { id: 'users', name: 'User Management', icon: 'Users', href: '/dashboard/users', category: 'Navigation' },
        { id: 'payments', name: 'Payments', icon: 'CreditCard', href: '/dashboard/payments', category: 'Navigation' },
        { id: 'plans', name: 'Plans', icon: 'Package', href: '/dashboard/plans', category: 'Navigation' },
        { id: 'subscriptions', name: 'Subscriptions', icon: 'FileText', href: '/dashboard/subscriptions', category: 'Navigation' },
        { id: 'reports', name: 'Reports', icon: 'BarChart3', href: '/dashboard/reports', category: 'Navigation' },
        { id: 'settings', name: 'Settings', icon: 'Settings', href: '/dashboard/settings', category: 'Navigation' },
      ],
      shops: [
        { id: 'shop1', name: 'MIDNIMO', owner: 'Mohamud Muhidin abdullahi', status: 'Active', category: 'Shops' },
        { id: 'shop2', name: 'HAYAT', owner: 'MUHAMMAD', status: 'Active', category: 'Shops' },
        { id: 'shop3', name: 'HIREY SHOP', owner: 'HIREY', status: 'Active', category: 'Shops' },
        { id: 'shop4', name: 'HAYATMARKET', owner: 'FITAH', status: 'Active', category: 'Shops' },
        { id: 'shop5', name: 'AMAR SHOP', owner: 'Amar', status: 'Pending', category: 'Shops' },
      ],
      users: [
        { id: 'user1', name: 'Mohamud Muhidin', email: '<EMAIL>', role: 'Shop Owner', category: 'Users' },
        { id: 'user2', name: 'Muhammad', email: '<EMAIL>', role: 'Shop Owner', category: 'Users' },
        { id: 'user3', name: 'Hirey', email: '<EMAIL>', role: 'Shop Owner', category: 'Users' },
      ],
      quickActions: [
        { id: 'register-shop', name: 'Register New Shop', icon: 'Store', action: 'register-shop', category: 'Quick Actions' },
        { id: 'create-user', name: 'Create New User', icon: 'Users', action: 'create-user', category: 'Quick Actions' },
        { id: 'generate-report', name: 'Generate Report', icon: 'FileText', action: 'generate-report', category: 'Quick Actions' },
        { id: 'view-analytics', name: 'View Analytics', icon: 'BarChart3', action: 'view-analytics', category: 'Quick Actions' },
      ]
    };

    const queryLower = query.toLowerCase();
    const results = [];

    // Search all categories
    Object.values(mockData).flat().forEach(item => {
      if (
        item.name.toLowerCase().includes(queryLower) ||
        (item.owner && item.owner.toLowerCase().includes(queryLower)) ||
        (item.email && item.email.toLowerCase().includes(queryLower))
      ) {
        results.push(item);
      }
    });

    return {
      results,
      total: results.length,
      query,
      categories: {
        navigation: results.filter(r => r.category === 'Navigation'),
        shops: results.filter(r => r.category === 'Shops'),
        users: results.filter(r => r.category === 'Users'),
        quickActions: results.filter(r => r.category === 'Quick Actions')
      }
    };
  }

  /**
   * Mock suggestions for development
   * @private
   */
  getMockSuggestions(query, type) {
    const suggestions = [
      { type: 'shop', value: 'MIDNIMO', label: 'MIDNIMO - Main Street, Hargeisa' },
      { type: 'shop', value: 'HAYAT', label: 'HAYAT - Wadajir' },
      { type: 'shop', value: 'HIREY SHOP', label: 'HIREY SHOP - Furayasha' },
      { type: 'shop', value: 'HAYATMARKET', label: 'HAYATMARKET - Hodan, Mogadisho' },
      { type: 'shop', value: 'AMAR SHOP', label: 'AMAR SHOP' },
      { type: 'owner', value: 'Mohamud Muhidin', label: 'Mohamud Muhidin abdullahi' },
      { type: 'owner', value: 'MUHAMMAD', label: 'MUHAMMAD - <EMAIL>' },
      { type: 'owner', value: 'HIREY', label: 'HIREY - <EMAIL>' },
      { type: 'owner', value: 'FITAH', label: 'FITAH - <EMAIL>' },
      { type: 'owner', value: 'Amar', label: 'Amar' },
    ];

    const queryLower = query.toLowerCase();
    return suggestions.filter(suggestion => {
      const matchesQuery = suggestion.label.toLowerCase().includes(queryLower) ||
                          suggestion.value.toLowerCase().includes(queryLower);
      const matchesType = type === 'all' || suggestion.type === type;
      return matchesQuery && matchesType;
    });
  }
}

// Export singleton instance
export const globalSearchService = new GlobalSearchService();
export default globalSearchService; 