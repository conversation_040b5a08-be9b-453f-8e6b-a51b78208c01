import apiBridge from '@/lib/api/bridge';
import { ENDPOINTS } from '@/lib/api/contract';
import { handleError, processApiResponse, validateRequiredFields, clearCache } from '../baseService';

/**
 * Register FCM token for current user
 * @param {Object} tokenData - Token registration data
 * @param {string} tokenData.token - FCM token string
 * @param {Object} [tokenData.deviceInfo] - Device information
 * @param {string} [tokenData.deviceInfo.deviceId] - Unique device identifier
 * @param {string} [tokenData.deviceInfo.platform] - Platform: ios, android, web
 * @param {string} [tokenData.deviceInfo.appVersion] - App version
 * @param {string} [tokenData.deviceInfo.osVersion] - OS version
 * @param {string} [tokenData.deviceInfo.deviceModel] - Device model
 * @returns {Promise<Object>} Registration result
 */
async function registerFCMToken(tokenData) {
  try {
    // Validate required fields
    const validation = validateRequiredFields(tokenData, ['token']);
    if (!validation.isValid) {
      handleError({ message: validation.message }, 'NotificationService.registerFCMToken', true);
      throw new Error(validation.message);
    }

    // Validate token is not empty string
    if (!tokenData.token || tokenData.token.trim() === '') {
      handleError({ message: 'FCM token cannot be empty' }, 'NotificationService.registerFCMToken', true);
      throw new Error('FCM token cannot be empty');
    }

    // Validate platform if provided
    if (tokenData.deviceInfo?.platform && !['ios', 'android', 'web'].includes(tokenData.deviceInfo.platform)) {
      handleError({ message: 'Platform must be ios, android, or web' }, 'NotificationService.registerFCMToken', true);
      throw new Error('Platform must be ios, android, or web');
    }

    // Make API request using the bridge
    const response = await apiBridge.post(ENDPOINTS.NOTIFICATIONS.FCM.REGISTER, tokenData, {
      skipCache: true
    });

    // Clear FCM tokens cache after registration
    clearCache('fcm-tokens');

    // Process response using utility
    const result = processApiResponse(response, 'FCM token registered successfully');
    return result;
  } catch (error) {
    handleError(error, 'NotificationService.registerFCMToken', true);
    throw error;
  }
}

export default registerFCMToken; 