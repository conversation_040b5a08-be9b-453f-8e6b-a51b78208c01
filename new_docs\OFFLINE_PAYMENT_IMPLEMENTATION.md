# 🛠️ Technical Implementation: Offline Payment Registration

## Overview
Technical implementation details for supporting offline payment registration journey.

## 🔧 **REQUIRED CODE CHANGES**

### **1. Enhanced Registration Controller**
**File**: `src/controllers/register/registrationController.js`

```javascript
// Enhanced email verification response for offline payments
const verifyEmail = async (req, res, next) => {
  try {
    // ... existing verification logic ...
    
    // After successful verification
    if (user.paymentMethod === 'offline') {
      // Generate payment instructions
      const paymentInstructions = await PaymentInstructionService.generateOfflineInstructions({
        userId: user.userId,
        shopId: user.shopId,
        planType: subscription.plan.type,
        amount: subscription.pricing.basePrice,
        referenceNumber: `DEYN-${new Date().getFullYear()}-${user.userId.slice(-3)}`
      });
      
      return ResponseHelper.success(res, 'Email verified successfully', {
        user: { status: user.status },
        paymentInstructions,
        nextStep: 'payment_confirmation'
      });
    }
    
    // ... existing response for online payments ...
  } catch (error) {
    next(error);
  }
};
```

### **2. Payment Instructions Service**
**File**: `src/services/paymentInstructionService.js` (NEW)

```javascript
const PaymentInstructionService = {
  /**
   * Generate offline payment instructions
   */
  generateOfflineInstructions: async (data) => {
    try {
      const { userId, shopId, planType, amount, referenceNumber } = data;
      
      // Get bank details from settings
      const bankDetails = await SettingsHelper.getBankDetails();
      
      // Store reference number in user record
      await UserService.updateUser(userId, {
        'payment.referenceNumber': referenceNumber,
        'payment.instructionsGeneratedAt': new Date()
      });
      
      return {
        method: 'offline',
        amount,
        currency: 'USD',
        referenceNumber,
        bankDetails: {
          bankName: bankDetails.bankName || 'Amal Bank',
          accountNumber: bankDetails.accountNumber || '**********',
          accountName: bankDetails.accountName || 'DeynCare Ltd',
          branch: bankDetails.branch || 'Main Branch'
        },
        mobileMoneyDetails: {
          provider: 'EVC Plus',
          number: '+************',
          accountName: 'DeynCare Ltd'
        },
        instructions: [
          `Make payment using reference number: ${referenceNumber}`,
          'Include your shop name in payment description',
          'Keep receipt for verification',
          'Contact support after payment: +************'
        ],
        supportContact: '+************',
        timeLimit: '48 hours',
        nextSteps: [
          'Make payment using provided details',
          'Keep your receipt/confirmation',
          'Wait for approval (usually within 24 hours)',
          'You will receive email confirmation once approved'
        ]
      };
    } catch (error) {
      throw new AppError('Failed to generate payment instructions', 500, 'payment_instructions_error');
    }
  }
};

module.exports = PaymentInstructionService;
```

### **3. Enhanced SuperAdmin Controller**
**File**: `src/controllers/register/superAdminController.js` (UPDATE)

```javascript
// Add route to list pending offline payments
const getPendingOfflinePayments = async (req, res, next) => {
  try {
    if (req.user.role !== 'superAdmin') {
      return next(new AppError('Only SuperAdmins can view pending payments', 403, 'access_denied'));
    }
    
    const { page = 1, limit = 20, sortBy = 'registeredAt', sortOrder = 'desc' } = req.query;
    
    // Find shops with pending payment status
    const query = {
      status: 'pending',
      'subscription.payment.method': { $in: ['offline', 'cash', 'bank'] },
      'subscription.status': 'pending_payment'
    };
    
    const pendingShops = await ShopService.findShopsWithFilters(query, {
      page: parseInt(page),
      limit: parseInt(limit),
      sort: { [sortBy]: sortOrder === 'desc' ? -1 : 1 },
      populate: ['owner', 'subscription']
    });
    
    const formattedData = pendingShops.data.map(shop => ({
      shopId: shop.shopId,
      shopName: shop.name,
      ownerName: shop.owner.fullName,
      email: shop.owner.email,
      phone: shop.owner.phone,
      planType: shop.subscription.plan.type,
      amount: shop.subscription.pricing.basePrice,
      paymentMethod: shop.subscription.payment.method,
      referenceNumber: shop.owner.payment?.referenceNumber,
      registeredAt: shop.createdAt,
      daysWaiting: Math.floor((new Date() - new Date(shop.createdAt)) / (1000 * 60 * 60 * 24)),
      status: 'pending_payment'
    }));
    
    return ResponseHelper.success(res, 'Pending offline payments retrieved', {
      pendingPayments: formattedData,
      pagination: pendingShops.pagination,
      summary: {
        totalPending: pendingShops.pagination.total,
        totalAmount: formattedData.reduce((sum, item) => sum + item.amount, 0)
      }
    });
  } catch (error) {
    logError('Failed to get pending offline payments', 'superAdminController', error);
    next(error);
  }
};

// Enhanced approval with offline payment confirmation
const approveShopRegistration = async (req, res, next) => {
  try {
    // ... existing validation ...
    
    const { shopId } = req.params;
    const { 
      approvalNotes, 
      activateImmediately = true,
      confirmOfflinePayment = true,
      offlinePaymentDetails 
    } = req.validatedData || req.body;
    
    const result = await TransactionHelper.withTransaction(async (session) => {
      // Get shop and subscription
      const shop = await ShopService.getShopById(shopId, { session, includeInactive: true });
      const subscription = await SubscriptionService.getSubscriptionByShopId(shopId, { session });
      
      if (!shop) {
        throw new AppError('Shop not found', 404, 'shop_not_found');
      }
      
      // Check if it's an offline payment
      const isOfflinePayment = subscription && 
        (subscription.payment.method === 'offline' || 
         subscription.payment.method === 'cash' ||
         subscription.payment.method === 'bank');
      
      if (isOfflinePayment && confirmOfflinePayment) {
        // Create payment record for offline payment
        const paymentData = {
          paymentId: `pay_${Date.now()}_${shopId.slice(-4)}`,
          shopId: shopId,
          customerId: shop.owner.userId,
          customerName: shop.owner.fullName,
          paymentContext: 'subscription',
          subscriptionId: subscription.subscriptionId,
          amount: offlinePaymentDetails.verifiedAmount || subscription.pricing.basePrice,
          method: offlinePaymentDetails.paymentMethod || 'Cash',
          paymentType: 'offline',
          referenceNumber: offlinePaymentDetails.receiptNumber,
          receiptNumber: offlinePaymentDetails.receiptNumber,
          paymentDate: offlinePaymentDetails.paymentDate || new Date(),
          isConfirmed: true,
          confirmedAt: new Date(),
          confirmedBy: req.user.userId,
          status: 'confirmed',
          notes: offlinePaymentDetails.verificationNotes,
          recordedBy: req.user.userId
        };
        
        await PaymentService.createPayment(paymentData);
        
        // Update subscription with payment confirmation
        subscription.payment.verified = true;
        subscription.payment.lastPaymentDate = offlinePaymentDetails.paymentDate || new Date();
        subscription.payment.paymentDetails = {
          transactionId: offlinePaymentDetails.receiptNumber,
          payerName: shop.owner.fullName,
          payerPhone: shop.owner.phone,
          notes: offlinePaymentDetails.verificationNotes
        };
        subscription.status = 'active';
        
        await subscription.save({ session });
        
        logInfo(`Offline payment confirmed for shop ${shopId}: ${offlinePaymentDetails.receiptNumber}`, 'superAdminController');
      }
      
      // Update shop status
      shop.status = 'active';
      shop.approvedBy = req.user.userId;
      shop.approvedAt = new Date();
      shop.access.isActivated = activateImmediately;
      
      await shop.save({ session });
      
      // Update user status  
      const shopOwner = await UserService.getUserById(shop.owner.userId, { session });
      if (shopOwner) {
        shopOwner.status = 'active';
        shopOwner.access.canLogin = true;
        shopOwner.access.lastStatusUpdate = new Date();
        await shopOwner.save({ session });
      }
      
      return { shop, shopOwner, subscription };
    });
    
    // Send approval email
    if (result.shopOwner) {
      try {
        await EmailService.auth.sendShopApprovalEmail(result.shopOwner, result.shop, {
          approvedBy: req.user.fullName,
          approvalNotes: approvalNotes,
          isOfflinePayment: confirmOfflinePayment,
          paymentConfirmed: confirmOfflinePayment
        });
      } catch (emailError) {
        logError(`Failed to send approval email`, 'superAdminController', emailError);
      }
    }
    
    const responseMessage = confirmOfflinePayment 
      ? 'Shop registration approved and payment confirmed successfully'
      : 'Shop registration approved successfully';
    
    return ResponseHelper.success(res, responseMessage, {
      shop: {
        id: shopId,
        name: result.shop.name,
        status: result.shop.status,
        approvedAt: result.shop.approvedAt,
        isActivated: result.shop.access.isActivated
      },
      owner: result.shopOwner ? UserHelper.sanitizeUser(result.shopOwner) : null,
      subscription: {
        status: result.subscription?.status,
        isPaid: result.subscription?.payment?.verified || false,
        method: result.subscription?.payment?.method
      },
      paymentConfirmed: confirmOfflinePayment,
      offlinePaymentDetails: confirmOfflinePayment ? offlinePaymentDetails : null
    });
  } catch (error) {
    logError('Failed to approve shop registration', 'superAdminController', error);
    next(error);
  }
};

module.exports = {
  // ... existing exports ...
  getPendingOfflinePayments,
  approveShopRegistration
};
```

### **4. Enhanced Routes**
**File**: `src/routes/registerRoutes.js` (UPDATE)

```javascript
// Add new route for pending offline payments
router.get('/admin/pending-payments', 
  authenticate, 
  authorize(['superAdmin']),
  superAdminController.getPendingOfflinePayments
);

// Update existing approve route with new validation
router.post('/admin/approve-shop/:shopId', 
  authenticate, 
  authorize(['superAdmin']),
  validate(registerSchemas.superAdminApproveShop), // Updated schema
  superAdminController.approveShopRegistration
);
```

### **5. Enhanced Email Templates**
**File**: `src/services/email/templates/offlinePaymentInstructions.js` (NEW)

```javascript
const offlinePaymentInstructionsTemplate = (data) => {
  const { 
    shopName, 
    ownerName, 
    amount, 
    currency, 
    referenceNumber, 
    bankDetails, 
    mobileMoneyDetails,
    instructions,
    supportContact 
  } = data;
  
  return {
    subject: `Payment Instructions - ${shopName} Registration`,
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #2c3e50;">Payment Instructions</h2>
        
        <p>Dear ${ownerName},</p>
        
        <p>Thank you for registering <strong>${shopName}</strong> with DeynCare! Your email has been verified successfully.</p>
        
        <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3 style="color: #28a745; margin-top: 0;">💰 Payment Details</h3>
          <p><strong>Amount:</strong> ${currency} ${amount}</p>
          <p><strong>Reference Number:</strong> <code style="background: #e9ecef; padding: 4px 8px; border-radius: 4px;">${referenceNumber}</code></p>
        </div>
        
        <div style="background: #e3f2fd; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3 style="color: #1976d2; margin-top: 0;">🏛️ Bank Transfer Details</h3>
          <p><strong>Bank:</strong> ${bankDetails.bankName}</p>
          <p><strong>Account Number:</strong> ${bankDetails.accountNumber}</p>
          <p><strong>Account Name:</strong> ${bankDetails.accountName}</p>
          <p><strong>Branch:</strong> ${bankDetails.branch}</p>
        </div>
        
        <div style="background: #e8f5e8; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3 style="color: #388e3c; margin-top: 0;">📱 Mobile Money Details</h3>
          <p><strong>Provider:</strong> ${mobileMoneyDetails.provider}</p>
          <p><strong>Number:</strong> ${mobileMoneyDetails.number}</p>
          <p><strong>Account Name:</strong> ${mobileMoneyDetails.accountName}</p>
        </div>
        
        <div style="background: #fff3cd; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3 style="color: #856404; margin-top: 0;">📋 Important Instructions</h3>
          <ul>
            ${instructions.map(instruction => `<li>${instruction}</li>`).join('')}
          </ul>
        </div>
        
        <div style="background: #f8d7da; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3 style="color: #721c24; margin-top: 0;">⏰ Next Steps</h3>
          <ol>
            <li>Make payment using the details above</li>
            <li>Keep your receipt/confirmation message</li>
            <li>Your account will be activated within 24-48 hours</li>
            <li>You'll receive a welcome email once approved</li>
          </ol>
        </div>
        
        <p><strong>Need Help?</strong><br>
        Contact our support team: <a href="tel:${supportContact}">${supportContact}</a><br>
        Email: <EMAIL></p>
        
        <p>Best regards,<br>
        The DeynCare Team</p>
      </div>
    `
  };
};

module.exports = offlinePaymentInstructionsTemplate;
```

## 🗂️ **NEW FILES TO CREATE**

1. **`src/services/paymentInstructionService.js`** - Payment instruction generation
2. **`src/services/email/templates/offlinePaymentInstructions.js`** - Email template
3. **`OFFLINE_PAYMENT_REGISTRATION_JOURNEY.md`** - User journey documentation
4. **`OFFLINE_PAYMENT_IMPLEMENTATION.md`** - This technical guide

## 🔄 **DATABASE UPDATES**

### **User Model Enhancement**
```javascript
// Add to user schema
payment: {
  referenceNumber: String,
  instructionsGeneratedAt: Date,
  paymentMethodPreference: String
}
```

### **Shop Model Enhancement**  
```javascript
// Add to shop schema
offlinePaymentTracking: {
  referenceNumber: String,
  instructionsSent: Boolean,
  customerContacted: Boolean,
  paymentVerified: Boolean,
  verificationNotes: String
}
```

## 📧 **EMAIL FLOW UPDATES**

### **New Email Types**
1. **Payment Instructions** - Sent after email verification for offline payments
2. **Payment Reminder** - Sent if no payment after 24 hours
3. **Approval Confirmation** - Enhanced welcome email for approved registrations

### **Email Service Updates**
```javascript
// Add to EmailService.auth
sendOfflinePaymentInstructions: async (userData, paymentInstructions) => {
  // Implementation for payment instruction email
},

sendPaymentReminder: async (userData, daysSinceInstructions) => {
  // Implementation for payment reminder
},

sendShopApprovalEmail: async (userData, shopData, approvalDetails) => {
  // Enhanced implementation with payment confirmation
}
```

## 🧪 **TESTING SCENARIOS**

### **Unit Tests**
- Payment instruction generation
- Offline payment approval logic
- Email template rendering
- Validation schemas

### **Integration Tests**
- Complete registration flow
- SuperAdmin approval process
- Email delivery
- Database state transitions

### **End-to-End Tests**
- Customer registration → approval → login
- Error handling scenarios
- Payment verification workflows

This implementation provides a complete offline payment registration system that's user-friendly, secure, and maintainable. 