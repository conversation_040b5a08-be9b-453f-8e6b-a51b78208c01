# DeynCare ML Risk Assessment System 🎯

## 📋 Table of Contents
- [System Overview](#system-overview)
- [Architecture Diagram](#architecture-diagram)
- [FairRisk Algorithm](#fairrisk-algorithm)
- [Four Core Features](#four-core-features)
- [Business Flow](#business-flow)
- [Calculation Examples](#calculation-examples)
- [ML Model Performance](#ml-model-performance)
- [API Integration](#api-integration)
- [Mobile App Integration](#mobile-app-integration)
- [Technical Implementation](#technical-implementation)

---

## 📌 System Overview

DeynCare ML Risk Assessment System is an intelligent **debt risk evaluation platform** that combines machine learning with real-world mobile money operations. The system evaluates customer creditworthiness using a **FairRisk Algorithm** and predicts repayment behavior through **Logistic Regression ML Model**.

### Key Features:
- ✅ **Real-time Risk Assessment** - Instant evaluation via FastAPI
- ✅ **Fair Algorithm** - Transparent risk scoring methodology  
- ✅ **Mobile App Integration** - Customer profile risk display
- ✅ **Automated Evaluation** - Cron-based ML processing
- ✅ **95% Accuracy** - Validated on 500+ synthetic customer records

---

## 🏗️ Architecture Diagram

```mermaid
graph TB
    subgraph "Mobile App Layer"
        A[Customer Creates Account] --> B[Apply for Loan]
        B --> C[Debt Created - Status: Active]
        C --> D[Customer Profile Shows: Active Debt]
    end
    
    subgraph "Business Logic Layer"
        E[Payment Recording] --> F[Due Date Check]
        F --> G{Past Due Date?}
        G -->|No| H[Status: Active Debt]
        G -->|Yes| I[Trigger ML Evaluation]
    end
    
    subgraph "ML Processing Layer"
        I --> J[Calculate 4 Core Features]
        J --> K[FastAPI ML Service]
        K --> L[Logistic Regression Model]
        L --> M[Risk Score & Level]
    end
    
    subgraph "Data Update Layer"
        M --> N[Update Debt Risk Level]
        N --> O[Update Customer Profile]
        O --> P[Mobile App Shows Risk Badge]
    end
    
    subgraph "Monitoring Layer"
        Q[Hourly Cron Job] --> F
        R[SMS Notifications] --> P
    end
    
    style A fill:#e1f5fe
    style K fill:#f3e5f5
    style M fill:#e8f5e8
    style P fill:#fff3e0
```

---

## 🧮 FairRisk Algorithm

### Mathematical Formula:
```
Risk_Score = (1 - DebtPaidRatio) + (PaymentDelay ÷ 10) + (OutstandingDebt ÷ DebtAmount)
```

### Risk Classification:
- **Low Risk**: `Risk_Score ≤ 0.3`
- **Medium Risk**: `0.3 < Risk_Score ≤ 0.6`  
- **High Risk**: `Risk_Score > 0.6`

### Algorithm Components:

#### 1. **Unpaid Debt Ratio**: `(1 - DebtPaidRatio)`
- Measures percentage of debt NOT yet paid
- Range: 0.0 to 1.0
- **Lower is better** (0.0 = fully paid)

#### 2. **Payment Delay Penalty**: `(PaymentDelay ÷ 10)`
- Normalizes payment delays to 0.1 scale per day
- Range: -∞ to +∞ (negative = early payment)
- **Lower is better** (negative values reduce risk)

#### 3. **Outstanding Debt Ratio**: `(OutstandingDebt ÷ DebtAmount)`
- Measures remaining debt as percentage of original
- Range: 0.0 to 1.0
- **Lower is better** (0.0 = fully paid)

---

## 🔧 Four Core Features

### Feature Calculation Flow:

```mermaid
graph LR
    subgraph "Input Data"
        A[Original Loan: $1000]
        B[Amount Paid: $750]
        C[Due Date: 2024-03-15]
        D[Payment Date: 2024-03-20]
    end
    
    subgraph "Calculations"
        E[DebtPaidRatio = 750/1000 = 0.75]
        F[OutstandingDebt = 1000-750 = $250]
        G[PaymentDelay = 20-15 = 5 days]
        H[DebtAmount = $1000 original]
    end
    
    subgraph "ML Features"
        I[Feature 1: 0.75]
        J[Feature 2: 5]
        K[Feature 3: $250]
        L[Feature 4: $1000]
    end
    
    A --> E
    B --> E
    A --> F
    B --> F
    C --> G
    D --> G
    A --> H
    
    E --> I
    G --> J
    F --> K
    H --> L
    
    style E fill:#e8f5e8
    style F fill:#e8f5e8
    style G fill:#e8f5e8
    style H fill:#e8f5e8
```

### 1. **DebtPaidRatio** (0.0 - 1.0)
```javascript
DebtPaidRatio = PaidAmount / DebtAmount
```
**Examples:**
- `$750 paid / $1000 debt = 0.75 (75% paid - Good)`
- `$200 paid / $1000 debt = 0.20 (20% paid - Concerning)`
- `$1000 paid / $1000 debt = 1.00 (100% paid - Excellent)`

### 2. **PaymentDelay** (Days: negative = early, positive = late)
```javascript
PaymentDelay = Math.ceil((PaymentDate - DueDate) / (1000 * 60 * 60 * 24))
```
**Examples:**
- `Due: Mar-15, Paid: Mar-10 = -5 days (5 days early - Excellent)`
- `Due: Mar-15, Paid: Mar-15 = 0 days (On time - Good)`
- `Due: Mar-15, Paid: Mar-25 = +10 days (10 days late - Bad)`

### 3. **OutstandingDebt** (Currency amount)
```javascript
OutstandingDebt = DebtAmount - PaidAmount
```
**Examples:**
- `$1000 debt - $750 paid = $250 remaining`
- `$1000 debt - $1000 paid = $0 remaining (Fully paid)`
- `$1000 debt - $100 paid = $900 remaining (High risk)`

### 4. **DebtAmount** (Original loan amount)
```javascript
DebtAmount = Original_Loan_Amount
```
**Context for risk evaluation:**
- `$100 debt = Small loan (Lower risk impact)`
- `$1000 debt = Medium loan (Moderate risk)`
- `$5000 debt = Large loan (Higher risk impact)`

---

## 📱 Business Flow

### Complete Process Diagram:

```mermaid
sequenceDiagram
    participant C as Customer (Mobile App)
    participant B as Backend API
    participant D as Database
    participant M as ML FastAPI
    participant CR as Cron Job
    
    Note over C,CR: Step 1: Customer Onboarding
    C->>B: Create Customer Account
    B->>D: Save Customer Profile
    D-->>B: Customer Created
    B-->>C: Profile: Risk Level = "Low Risk"
    
    Note over C,CR: Step 2: Loan Application
    C->>B: Apply for Loan ($1000)
    B->>D: Create Debt Record
    D-->>B: Debt Status = "Active"
    B-->>C: Loan Approved: "Active Debt"
    
    Note over C,CR: Step 3: Payment Recording
    C->>B: Record Payment ($750)
    B->>D: Update Debt + Calculate Features
    Note over D: DebtPaidRatio = 0.75<br/>PaymentDelay = +5 days<br/>OutstandingDebt = $250
    
    Note over C,CR: Step 4: ML Evaluation (Triggered by Cron)
    CR->>D: Check Overdue Debts
    CR->>M: Send ML Features
    M-->>CR: Risk Score = 0.45, Level = "Medium Risk"
    CR->>D: Update Customer Risk Profile
    
    Note over C,CR: Step 5: Mobile App Update
    C->>B: Refresh Customer Profile
    B->>D: Get Updated Risk Profile
    D-->>B: Risk Level = "Medium Risk"
    B-->>C: Display Risk Badge
```

---

## 🧮 Calculation Examples

### Example 1: **Excellent Customer**
```json
{
  "scenario": "New customer, paid early and in full",
  "input": {
    "DebtAmount": 500,
    "PaidAmount": 500,
    "DueDate": "2024-03-15",
    "PaymentDate": "2024-03-10"
  },
  "calculations": {
    "DebtPaidRatio": "500 / 500 = 1.0",
    "PaymentDelay": "(Mar-10 - Mar-15) = -5 days",
    "OutstandingDebt": "500 - 500 = $0",
    "DebtAmount": 500
  },
  "riskScore": {
    "unpaidDebtRatio": "(1 - 1.0) = 0.0",
    "delayPenalty": "(-5 / 10) = -0.5",
    "outstandingRatio": "(0 / 500) = 0.0",
    "totalRiskScore": "0.0 + (-0.5) + 0.0 = -0.5"
  },
  "result": {
    "normalizedScore": 0.0,
    "riskLevel": "Low Risk",
    "reasoning": "Paid early and in full - excellent customer"
  }
}
```

### Example 2: **Medium Risk Customer**
```json
{
  "scenario": "Returning customer, partial payment, slightly late",
  "input": {
    "DebtAmount": 1000,
    "PaidAmount": 750,
    "DueDate": "2024-03-15",
    "PaymentDate": "2024-03-20"
  },
  "calculations": {
    "DebtPaidRatio": "750 / 1000 = 0.75",
    "PaymentDelay": "(Mar-20 - Mar-15) = +5 days",
    "OutstandingDebt": "1000 - 750 = $250",
    "DebtAmount": 1000
  },
  "riskScore": {
    "unpaidDebtRatio": "(1 - 0.75) = 0.25",
    "delayPenalty": "(5 / 10) = 0.5",
    "outstandingRatio": "(250 / 1000) = 0.25",
    "totalRiskScore": "0.25 + 0.5 + 0.25 = 1.0"
  },
  "result": {
    "normalizedScore": 0.45,
    "riskLevel": "Medium Risk",
    "reasoning": "Good payment ratio but concerning delay"
  }
}
```

### Example 3: **High Risk Customer**
```json
{
  "scenario": "Returning customer, minimal payment, very late",
  "input": {
    "DebtAmount": 2000,
    "PaidAmount": 400,
    "DueDate": "2024-03-01",
    "PaymentDate": "2024-03-20"
  },
  "calculations": {
    "DebtPaidRatio": "400 / 2000 = 0.20",
    "PaymentDelay": "(Mar-20 - Mar-01) = +19 days",
    "OutstandingDebt": "2000 - 400 = $1600",
    "DebtAmount": 2000
  },
  "riskScore": {
    "unpaidDebtRatio": "(1 - 0.20) = 0.80",
    "delayPenalty": "(19 / 10) = 1.9",
    "outstandingRatio": "(1600 / 2000) = 0.80",
    "totalRiskScore": "0.80 + 1.9 + 0.80 = 3.5"
  },
  "result": {
    "normalizedScore": 0.85,
    "riskLevel": "High Risk",
    "reasoning": "Poor payment ratio + significant delay"
  }
}
```

---

## 📊 ML Model Performance

### Dataset Information:
- **Total Records**: 500 synthetic customers
- **Features Used**: 4 core financial metrics
- **Training Split**: 80% train, 20% test
- **Data Range**: 2024-01-01 to 2024-08-31

### Model Comparison:
```mermaid
graph TB
    subgraph "Model Performance"
        A[Random Forest: 94% Accuracy]
        B[Logistic Regression: 95% Accuracy ⭐]
        C[Selected Model: Logistic Regression]
    end
    
    subgraph "Performance Metrics"
        D[Low Risk: 90% Precision]
        E[Medium Risk: 100% Precision]
        F[High Risk: 95% Precision]
        G[Overall F1-Score: 0.92]
    end
    
    B --> C
    C --> D
    C --> E
    C --> F
    C --> G
```

### Classification Report:
```
              precision    recall  f1-score   support
    Low Risk       0.90      1.00      0.95         9
 Medium Risk       1.00      0.71      0.83        17
   High Risk       0.95      1.00      0.97        74

    accuracy                           0.95       100
   macro avg       0.95      0.90      0.92       100
weighted avg       0.95      0.95      0.95       100
```

---

## 🔌 API Integration

### FastAPI Endpoint Structure:

```mermaid
graph LR
    subgraph "API Request"
        A[Mobile App/Backend]
        B[POST /predict_single/]
    end
    
    subgraph "ML Processing"
        C[Load Model & Scaler]
        D[Preprocess Features]
        E[Generate Prediction]
        F[Calculate Risk Score]
    end
    
    subgraph "API Response"
        G[Risk Level]
        H[Risk Score]
        I[Confidence]
    end
    
    A --> B
    B --> C
    C --> D
    D --> E
    E --> F
    F --> G
    F --> H
    F --> I
```

### Request Format:
```json
{
  "DebtPaidRatio": 0.75,
  "PaymentDelay": 5,
  "OutstandingDebt": 250,
  "DebtAmount": 1000
}
```

### Response Format:
```json
{
  "PredictedRiskLevel": "Medium Risk",
  "RiskScore": 0.45,
  "confidence": 0.85,
  "factors": ["partial_payment", "payment_delay"],
  "recommendation": "Monitor closely"
}
```

---

## 📱 Mobile App Integration

### Customer Profile Display:

```mermaid
graph TB
    subgraph "Customer Profile Screen"
        A[Customer Name & Photo]
        B[Risk Level Badge]
        C[Risk Score Percentage]
        D[Last Assessment Date]
        E[Payment History]
    end
    
    subgraph "Risk Badge Colors"
        F[Low Risk = Green 🟢]
        G[Medium Risk = Yellow 🟡]
        H[High Risk = Red 🔴]
        I[Active Debt = Blue 🔵]
    end
    
    B --> F
    B --> G
    B --> H
    B --> I
```

### Risk Level States:
1. **"Active Debt"** - No ML evaluation yet (before due date)
2. **"Low Risk"** - Reliable customer (score ≤ 0.3)
3. **"Medium Risk"** - Monitor closely (score 0.3-0.6)
4. **"High Risk"** - Requires attention (score > 0.6)

---

## ⚙️ Technical Implementation

### System Components:

```mermaid
graph TB
    subgraph "Training Environment"
        A[Jupyter Notebook]
        B[Synthetic Dataset]
        C[Model Training]
        D[Model Export .pkl]
    end
    
    subgraph "Production Environment"
        E[FastAPI Service]
        F[Node.js Backend]
        G[MongoDB Database]
        H[Mobile App Frontend]
    end
    
    subgraph "Automation"
        I[Cron Jobs]
        J[ML Risk Evaluation]
        K[SMS Notifications]
    end
    
    A --> B
    B --> C
    C --> D
    D --> E
    
    E --> F
    F --> G
    G --> H
    
    I --> J
    J --> F
    J --> K
```

### Database Schema:

#### Customer Model:
```javascript
riskProfile: {
  currentRiskLevel: String,     // "Low Risk", "Medium Risk", "High Risk"
  riskScore: Number,            // 0-100 percentage
  lastAssessment: Date,         // When ML last evaluated
  assessmentCount: Number,      // Total evaluations
  mlSource: String              // "ml_api", "manual", "system"
}
```

#### Debt Model:
```javascript
// ML Features (auto-calculated)
DebtPaidRatio: Number,          // 0.0 - 1.0
PaymentDelay: Number,           // Days (negative = early)
OutstandingDebt: Number,        // Currency amount
DebtAmount: Number,             // Original loan amount

// ML Results
RiskLevel: String,              // "Low Risk", "Medium Risk", "High Risk", "Active Debt"
```

### Cron Job Schedule:
- **Frequency**: Every hour (`0 * * * *`)
- **Purpose**: Check overdue debts and trigger ML evaluation
- **Process**: Find debts with `RiskLevel: "Active Debt"` past due date

---

## 🎯 Key Success Factors

### 1. **Fair & Transparent Algorithm**
- Mathematical formula is explainable
- No bias against customer demographics
- Clear risk factor identification

### 2. **Real-World Business Logic**
- Respects due dates and payment patterns
- Integrates with mobile money workflows
- Supports Somali customer behaviors

### 3. **High Accuracy & Performance**
- 95% prediction accuracy
- Real-time API responses (<1 second)
- Scalable FastAPI architecture

### 4. **User-Friendly Integration**
- Clean mobile app display
- Automated background processing
- SMS notification support

---

**🚀 DeynCare ML Risk Assessment System successfully combines cutting-edge machine learning with practical mobile money operations, delivering accurate, fair, and actionable credit risk insights for Somali customers.** 