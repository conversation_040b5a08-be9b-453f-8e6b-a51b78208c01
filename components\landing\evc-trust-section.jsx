import { 
  Shield, 
  CheckCircle, 
  Smartphone, 
  CreditCard, 
  Users, 
  Clock,
  Lock,
  Zap,
  Star,
  UserPlus,
  ArrowRight,
  Eye,
  FileCheck,
  Globe
} from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle } from "../ui/card";
import { Badge } from "../ui/badge";
import { Button } from "../ui/button";
import Link from "next/link";

const trustFeatures = [
  {
    icon: Shield,
    title: "Bank-Grade Security",
    description: "EVC Plus certified payment processing with end-to-end encryption",
    color: "text-green-600",
    bgColor: "bg-green-50"
  },
  {
    icon: CheckCircle,
    title: "Instant Verification",
    description: "Real-time payment confirmation and receipt generation",
    color: "text-blue-600",
    bgColor: "bg-blue-50"
  },
  {
    icon: Users,
    title: "500K+ Users Trust",
    description: "Join thousands of businesses using EVC Plus payments",
    color: "text-purple-600",
    bgColor: "bg-purple-50"
  },
  {
    icon: Clock,
    title: "24/7 Processing",
    description: "Round-the-clock payment processing and support",
    color: "text-orange-600",
    bgColor: "bg-orange-50"
  }
];

const registrationSteps = [
  {
    step: 1,
    icon: UserPlus,
    title: "Quick Registration",
    description: "Sign up with your business details in under 2 minutes",
    features: ["Business information", "Contact verification", "Role selection"],
    color: "bg-blue-500"
  },
  {
    step: 2,
    icon: FileCheck,
    title: "Account Verification",
    description: "Automated verification process with instant approval",
    features: ["Document upload", "Identity verification", "Business validation"],
    color: "bg-green-500"
  },
  {
    step: 3,
    icon: CreditCard,
    title: "EVC Plus Activated",
    description: "EVC Plus payment acceptance is automatically enabled for your account",
    features: ["Auto-enabled payments", "Customer payment options", "Instant processing"],
    color: "bg-purple-500"
  },
  {
    step: 4,
    icon: Zap,
    title: "Start Managing",
    description: "Begin using all DeynCare features immediately",
    features: ["Dashboard access", "Feature activation", "Support onboarding"],
    color: "bg-orange-500"
  }
];

const evcBenefits = [
  "Zero setup fees for EVC Plus integration",
  "Instant payment confirmation and receipts",
  "Automatic reconciliation and reporting",
  "Fraud protection and secure transactions",
  "Multi-currency support (USD, SOS)",
  "Real-time transaction monitoring"
];

export default function EVCTrustSection() {
  return (
    <section id="evc-integration" className="py-24 bg-gradient-to-b from-blue-50 to-white dark:from-slate-800 dark:to-slate-900">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <div className="text-center mb-16">
          <Badge className="mb-4 bg-green-100 dark:bg-green-900/50 text-green-700 dark:text-green-300 border-green-200 dark:border-green-700">
            <Shield className="w-3 h-3 mr-1" />
            EVC Plus Certified
          </Badge>
          <h2 className="text-3xl md:text-4xl font-bold text-slate-900 dark:text-white mb-6">
            Register to Accept EVC Plus Payments
          </h2>
          <p className="text-lg text-slate-600 dark:text-slate-300 max-w-3xl mx-auto">
            Register your DeynCare account to instantly start accepting EVC Plus payments. 
            Our platform integrates seamlessly with Somalia's leading mobile payment system for secure, instant transactions.
          </p>
        </div>

        {/* Trust Features */}
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-16">
          {trustFeatures.map((feature, index) => (
            <Card key={index} className="text-center border border-slate-200 dark:border-slate-700 hover:shadow-lg transition-all duration-300 bg-white dark:bg-slate-800">
              <CardContent className="p-6">
                <div className={`mx-auto mb-4 p-4 rounded-full w-fit ${feature.bgColor} dark:${feature.bgColor.replace('bg-', 'bg-').replace('-50', '-900/30')}`}>
                  <feature.icon className={`w-8 h-8 ${feature.color} dark:${feature.color.replace('600', '400')}`} />
                </div>
                <h3 className="text-lg font-semibold text-slate-900 dark:text-white mb-2">{feature.title}</h3>
                <p className="text-sm text-slate-600 dark:text-slate-300 leading-relaxed">{feature.description}</p>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* EVC Plus Integration Showcase */}
        <div className="grid lg:grid-cols-2 gap-12 items-center mb-16">
          {/* Left Side - Benefits */}
          <div>
            <div className="flex items-center gap-3 mb-6">
              <div className="p-3 bg-green-100 dark:bg-green-900/30 rounded-lg">
                <Smartphone className="w-8 h-8 text-green-600 dark:text-green-400" />
              </div>
              <div>
                <h3 className="text-2xl md:text-3xl font-bold text-slate-900 dark:text-white">
                  Seamless EVC Plus Integration
                </h3>
                <p className="text-slate-600 dark:text-slate-300">Somalia's most trusted payment solution</p>
              </div>
            </div>
            
            <p className="text-slate-600 dark:text-slate-300 mb-6 leading-relaxed">
              When you register with DeynCare, you automatically gain access to EVC Plus payment processing. 
              Your customers can pay using their familiar EVC Plus accounts while you manage everything through DeynCare.
            </p>

            <div className="space-y-3 mb-8">
              {evcBenefits.map((benefit, index) => (
                <div key={index} className="flex items-center gap-3">
                  <CheckCircle className="w-5 h-5 text-green-500 dark:text-green-400 flex-shrink-0" />
                  <span className="text-slate-700 dark:text-slate-300">{benefit}</span>
                </div>
              ))}
            </div>

            <div className="flex items-center gap-3 p-4 bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-200 dark:border-green-800">
              <Star className="w-5 h-5 text-green-600 dark:text-green-400" />
              <div>
                <div className="font-semibold text-green-800 dark:text-green-300">99.9% Uptime Guaranteed</div>
                <div className="text-sm text-green-700 dark:text-green-400">EVC Plus infrastructure ensures maximum reliability</div>
              </div>
            </div>
          </div>

          {/* Right Side - Visual */}
          <div className="relative">
            <Card className="p-8 border border-slate-200 dark:border-slate-700 shadow-xl bg-gradient-to-br from-white to-green-50 dark:from-slate-800 dark:to-green-900/20">
              {/* Mock EVC Plus Interface */}
              <div className="text-center mb-6">
                <div className="inline-flex items-center gap-2 bg-green-600 dark:bg-green-500 text-white px-4 py-2 rounded-full mb-4">
                  <Smartphone className="w-4 h-4" />
                  <span className="font-medium">EVC Plus</span>
                </div>
                <h4 className="text-lg font-semibold text-slate-900 dark:text-white">Payment Processing</h4>
              </div>

              {/* Payment Flow */}
              <div className="space-y-4">
                <div className="flex items-center justify-between p-3 bg-white dark:bg-slate-700 rounded-lg border border-slate-200 dark:border-slate-600">
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 bg-blue-100 dark:bg-blue-900/50 rounded-full flex items-center justify-center">
                      <span className="text-blue-600 dark:text-blue-400 font-semibold text-sm">1</span>
                    </div>
                    <span className="text-slate-700 dark:text-slate-300">Customer Payment</span>
                  </div>
                  <div className="text-green-600 dark:text-green-400 font-semibold">$25.00</div>
                </div>

                <div className="flex items-center justify-between p-3 bg-white dark:bg-slate-700 rounded-lg border border-slate-200 dark:border-slate-600">
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 bg-green-100 dark:bg-green-900/50 rounded-full flex items-center justify-center">
                      <CheckCircle className="w-4 h-4 text-green-600 dark:text-green-400" />
                    </div>
                    <span className="text-slate-700 dark:text-slate-300">Instant Verification</span>
                  </div>
                  <Badge className="bg-green-100 dark:bg-green-900/50 text-green-700 dark:text-green-300">Confirmed</Badge>
                </div>

                <div className="flex items-center justify-between p-3 bg-white dark:bg-slate-700 rounded-lg border border-slate-200 dark:border-slate-600">
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 bg-purple-100 dark:bg-purple-900/50 rounded-full flex items-center justify-center">
                      <Eye className="w-4 h-4 text-purple-600 dark:text-purple-400" />
                    </div>
                    <span className="text-slate-700 dark:text-slate-300">Real-time Update</span>
                  </div>
                  <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                </div>
              </div>
            </Card>

            {/* Floating badges */}
            <div className="absolute -top-4 -right-4 bg-green-500 dark:bg-green-600 text-white px-3 py-1 rounded-full text-xs font-medium shadow-lg">
              Certified Partner
            </div>
            <div className="absolute -bottom-4 -left-4 bg-blue-500 dark:bg-blue-600 text-white px-3 py-1 rounded-full text-xs font-medium shadow-lg">
              Instant Processing
            </div>
          </div>
        </div>

        {/* Registration Process */}
        <div className="mb-16">
          <div className="text-center mb-12">
            <h3 className="text-2xl md:text-3xl font-bold text-slate-900 dark:text-white mb-4">
              Register & Start Accepting EVC Plus Payments
            </h3>
            <p className="text-lg text-slate-600 dark:text-slate-300 max-w-2xl mx-auto">
              Register your DeynCare business account today and automatically unlock EVC Plus payment acceptance. 
              Start processing payments in minutes with our simple registration process.
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {registrationSteps.map((step, index) => (
              <div key={index} className="relative">
                <Card className="h-full border border-slate-200 dark:border-slate-700 hover:shadow-lg transition-all duration-300 bg-white dark:bg-slate-800">
                  <CardHeader className="text-center pb-4">
                    <div className={`mx-auto mb-4 p-4 rounded-full w-fit ${step.color} text-white`}>
                      <step.icon className="w-8 h-8" />
                    </div>
                    <div className="mb-2">
                      <Badge className="bg-slate-100 dark:bg-slate-700 text-slate-700 dark:text-slate-300 text-xs">
                        Step {step.step}
                      </Badge>
                    </div>
                    <CardTitle className="text-lg text-slate-900 dark:text-white">{step.title}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-slate-600 dark:text-slate-300 mb-4 leading-relaxed">{step.description}</p>
                    <div className="space-y-2">
                      {step.features.map((feature, featureIndex) => (
                        <div key={featureIndex} className="flex items-center gap-2 text-sm">
                          <div className="w-1.5 h-1.5 bg-green-500 rounded-full"></div>
                          <span className="text-slate-700 dark:text-slate-300">{feature}</span>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
                
                {/* Arrow connector (except for last item) */}
                {index < registrationSteps.length - 1 && (
                  <div className="hidden lg:block absolute top-1/2 -right-4 transform -translate-y-1/2 z-10">
                    <ArrowRight className="w-6 h-6 text-slate-400 dark:text-slate-500" />
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>

        {/* CTA Section */}
        <div className="text-center">
          <div className="bg-gradient-to-r from-green-50 to-blue-50 dark:from-green-900/20 dark:to-blue-900/20 rounded-2xl p-8 md:p-12 border border-slate-200 dark:border-slate-700">
            <div className="max-w-3xl mx-auto">
              <div className="flex justify-center gap-4 mb-6">
                <div className="flex items-center gap-2 bg-white dark:bg-slate-800 px-4 py-2 rounded-full shadow-sm border border-slate-200 dark:border-slate-600">
                  <Shield className="w-4 h-4 text-green-600 dark:text-green-400" />
                  <span className="text-sm font-medium text-slate-700 dark:text-slate-300">Secure</span>
                </div>
                <div className="flex items-center gap-2 bg-white dark:bg-slate-800 px-4 py-2 rounded-full shadow-sm border border-slate-200 dark:border-slate-600">
                  <Zap className="w-4 h-4 text-blue-600 dark:text-blue-400" />
                  <span className="text-sm font-medium text-slate-700 dark:text-slate-300">Instant</span>
                </div>
                <div className="flex items-center gap-2 bg-white dark:bg-slate-800 px-4 py-2 rounded-full shadow-sm border border-slate-200 dark:border-slate-600">
                  <Globe className="w-4 h-4 text-purple-600 dark:text-purple-400" />
                  <span className="text-sm font-medium text-slate-700 dark:text-slate-300">Trusted</span>
                </div>
              </div>
              
              <h3 className="text-2xl md:text-3xl font-bold text-slate-900 dark:text-white mb-4">
                Register Now & Accept EVC Plus Payments
              </h3>
              <p className="text-lg text-slate-600 dark:text-slate-300 mb-8">
                Join thousands of Somali businesses who registered with DeynCare and now accept EVC Plus payments seamlessly. 
                Register today and start processing payments immediately.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button asChild size="lg" className="bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700 text-white">
                  <Link href="/login" className="flex items-center gap-2">
                    Start Free Trial
                    <ArrowRight className="w-4 h-4" />
                  </Link>
                </Button>
                <Button asChild variant="outline" size="lg" className="border-slate-300 dark:border-slate-600 dark:text-slate-300 dark:hover:bg-slate-800">
                  <Link href="#contact">
                    Contact EVC Plus Team
                  </Link>
                </Button>
              </div>
              <div className="mt-6 text-sm text-slate-500 dark:text-slate-400">
                Free 14-day trial • No setup fees • EVC Plus integration included
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
} 