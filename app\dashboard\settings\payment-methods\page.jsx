/**
 * Payment Methods Settings Page
 * Provides unified interface for EVC credentials and payment method configuration
 * Matches backend API structure exactly
 */

'use client';

import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '../../../../components/ui/card';
import { Alert, AlertDescription } from '../../../../components/ui/alert';
import { CreditCard, Settings, Info } from 'lucide-react';
import EVCCredentialsForm from '../../../../components/settings/evc-credentials-form';
import PaymentMethodsForm from '../../../../components/settings/payment-methods-form';
import { useAuth } from '../../../../contexts/auth-context';

/**
 * Payment Methods Settings Page Component
 */
export default function PaymentMethodsPage() {
  const { user } = useAuth();
  
  // Determine if user can manage global settings (SuperAdmin only)
  const canManageGlobal = user?.role === 'superAdmin';
  
  // For shop admins, use their assigned shopId
  const shopId = user?.role === 'admin' ? user?.shopId : null;

  const handleCredentialsSaved = (result) => {
    console.log('[PaymentMethodsPage] EVC credentials saved:', result);
    // Could show notification or trigger refetch
  };

  const handlePaymentMethodsSaved = (result) => {
    console.log('[PaymentMethodsPage] Payment methods saved:', result);
    // Could show notification or trigger refetch
  };

  const handleCredentialsTested = (result) => {
    console.log('[PaymentMethodsPage] EVC credentials tested:', result);
    // Could show notification with test results
  };

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center gap-3">
        <CreditCard className="w-6 h-6" />
        <div>
          <h1 className="text-2xl font-semibold">Payment Methods</h1>
          <p className="text-gray-600">
            Configure global EVC payment credentials and available payment methods
          </p>
        </div>
      </div>

      {/* Access Restriction Notice */}
      {!canManageGlobal && (
        <Alert>
          <Info className="h-4 w-4" />
          <AlertDescription>
            Payment methods configuration is restricted to SuperAdmin users only. 
            Shop admins can manage payments through the mobile application.
          </AlertDescription>
        </Alert>
      )}

      {/* SuperAdmin Global Settings */}
      {canManageGlobal && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Settings className="w-5 h-5" />
              Global Payment Settings
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              {/* Global EVC Credentials */}
              <div>
                <h3 className="text-lg font-medium mb-4">Global EVC Credentials</h3>
                <EVCCredentialsForm
                  shopId={null}
                  onSave={handleCredentialsSaved}
                  onTest={handleCredentialsTested}
                />
              </div>

              {/* Global Payment Methods */}
              <div>
                <h3 className="text-lg font-medium mb-4">Global Payment Methods</h3>
                <PaymentMethodsForm
                  context="general"
                  shopId={null}
                  onSave={handlePaymentMethodsSaved}
                />
              </div>
            </div>
          </CardContent>
        </Card>
      )}





      {/* Development Debug Info */}
      {process.env.NODE_ENV === 'development' && (
        <Card>
          <CardHeader>
            <CardTitle className="text-sm">Debug Information</CardTitle>
          </CardHeader>
          <CardContent>
            <pre className="text-xs bg-gray-100 p-3 rounded overflow-x-auto">
              {JSON.stringify({
                user: {
                  role: user?.role,
                  shopId: user?.shopId,
                  email: user?.email
                },
                permissions: {
                  canManageGlobal,
                  effectiveShopId: shopId
                }
              }, null, 2)}
            </pre>
          </CardContent>
        </Card>
      )}
    </div>
  );
} 