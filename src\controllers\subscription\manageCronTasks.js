/**
 * Subscription Cron Tasks Management Controller
 * Allows SuperAdmin to view, trigger, and configure automated subscription tasks
 * POST /api/subscriptions/cron/run
 */
const { spawn } = require('child_process');
const path = require('path');
const { logInfo, logSuccess, logError } = require('../../utils');

/**
 * Run subscription cron tasks on demand
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
const manageCronTasks = async (req, res, next) => {
  try {
    // Only SuperAdmin can run cron tasks on demand
    if (req.user.role !== 'superAdmin') {
      return res.status(403).json({
        success: false,
        message: 'Access denied. SuperAdmin privileges required.'
      });
    }
    
    // Get task type from request body
    const { taskType = 'all' } = req.body;
    
    // Validate task type
    const validTaskTypes = ['trialReminders', 'expiryReminders', 'autoRenewals', 'deactivateExpired', 'all'];
    
    if (!validTaskTypes.includes(taskType)) {
      return res.status(400).json({
        success: false,
        message: `Invalid task type. Must be one of: ${validTaskTypes.join(', ')}`
      });
    }
    
    logInfo(`SuperAdmin triggered subscription cron task: ${taskType}`, 'SubscriptionController');
    
    // Run the task as a separate process
    const cronScriptPath = path.join(process.cwd(), 'src', 'cron', 'subscriptionTasks.js');
    
    // Spawn the process
    const cronProcess = spawn('node', [cronScriptPath, taskType]);
    
    let output = '';
    let errorOutput = '';
    
    // Collect output
    cronProcess.stdout.on('data', (data) => {
      output += data.toString();
    });
    
    cronProcess.stderr.on('data', (data) => {
      errorOutput += data.toString();
    });
    
    // Send immediate response - task is running in background
    res.status(202).json({
      success: true,
      message: `Subscription cron task '${taskType}' has been triggered and is running in the background.`,
      taskType
    });
    
    // Handle process completion (for logging purposes)
    cronProcess.on('close', (code) => {
      if (code === 0) {
        logSuccess(`Subscription cron task '${taskType}' completed successfully`, 'SubscriptionController');
        logInfo(`Task output: ${output}`, 'SubscriptionController');
      } else {
        logError(`Subscription cron task '${taskType}' failed with code ${code}`, 'SubscriptionController');
        logError(`Task error: ${errorOutput}`, 'SubscriptionController');
      }
    });
    
  } catch (error) {
    logError('Failed to run subscription cron task', 'SubscriptionController', error);
    return next(error);
  }
};

module.exports = manageCronTasks;
