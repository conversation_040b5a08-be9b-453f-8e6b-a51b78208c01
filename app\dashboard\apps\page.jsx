"use client";

import { useState, useEffect } from 'react';
import { 
  Smartphone,
  Upload,
  CheckCircle,
  Shield,
  Download,
  Trash2,
  Star,
  Calendar,
  FileText,
  AlertTriangle,
  RefreshCw
} from 'lucide-react';
import { useAuth } from '@/contexts/auth-context';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { toast } from 'sonner';
import AppUploadForm from '@/components/dashboard/apps/app-upload-form';
import { appUploadAPI } from '@/lib/api/modules/appUpload';

export default function AppManagementPage() {
  const { user, isSuperAdmin } = useAuth();
  const [apps, setApps] = useState([]);
  const [loading, setLoading] = useState(true);
  const [deleting, setDeleting] = useState(null);
  const [refreshing, setRefreshing] = useState(false);

  // Only SuperAdmin can access this page
  if (!user || !isSuperAdmin()) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-16">
            <Shield className="h-16 w-16 text-gray-400 mb-4" />
            <h2 className="text-2xl font-semibold mb-2">Access Denied</h2>
            <p className="text-gray-600 text-center">
              Only SuperAdmin users can access App Management.
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  const fetchApps = async () => {
    try {
      setLoading(true);
      const response = await appUploadAPI.getApps({
        page: 1,
        limit: 50,
        sortBy: 'createdAt',
        sortOrder: 'desc'
      });
      setApps(response.data.apps || []);
    } catch (error) {
      console.error('Error fetching apps:', error);
      toast.error('Failed to load apps');
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (uploadId, appName) => {
    try {
      setDeleting(uploadId);
      await appUploadAPI.deleteApp(uploadId);
      toast.success(`${appName} deleted successfully`);
      fetchApps(); // Refresh the list
    } catch (error) {
      console.error('Error deleting app:', error);
      toast.error('Failed to delete app');
    } finally {
      setDeleting(null);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await fetchApps();
    setRefreshing(false);
    toast.success('Apps list refreshed');
  };

  const handleUploadSuccess = () => {
    fetchApps(); // Refresh the list after successful upload
    toast.success('App uploaded successfully');
  };

  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  useEffect(() => {
    fetchApps();
  }, []);

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-4 mb-6">
        <div className="w-12 h-12 bg-blue-600 rounded-lg flex items-center justify-center">
          <Smartphone className="h-6 w-6 text-white" />
        </div>
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
            App Management
          </h1>
          <p className="text-gray-600 dark:text-gray-300">
            Upload and manage mobile applications
          </p>
        </div>
      </div>

      {/* Upload Section */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Upload className="h-5 w-5" />
            Upload New App
          </CardTitle>
        </CardHeader>
        <CardContent>
          <AppUploadForm onUploadSuccess={handleUploadSuccess} />
        </CardContent>
      </Card>

      {/* Apps List Section */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Uploaded Apps
          </CardTitle>
          <Button
            onClick={handleRefresh}
            disabled={refreshing}
            variant="outline"
            size="sm"
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex items-center justify-center py-12">
              <RefreshCw className="h-8 w-8 animate-spin text-blue-600" />
              <span className="ml-2 text-gray-600">Loading apps...</span>
            </div>
          ) : apps.length === 0 ? (
            <div className="text-center py-12">
              <Smartphone className="h-16 w-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-600 mb-2">No Apps Uploaded Yet</h3>
              <p className="text-gray-500">Upload your first app using the form above.</p>
            </div>
          ) : (
            <div className="space-y-4">
              {apps.map((app) => (
                <Card key={app.uploadId} className="p-4">
                  <div className="flex items-start justify-between">
                    <div className="flex items-start gap-4">
                      <div className="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center">
                        <Smartphone className="h-6 w-6 text-gray-600" />
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-1">
                          <h3 className="font-semibold text-lg">{app.appName}</h3>
                          {app.isLatest && (
                            <Badge variant="default" className="text-xs">
                              <Star className="h-3 w-3 mr-1" />
                              Latest
                            </Badge>
                          )}
                          <Badge variant="outline" className="text-xs">
                            {app.platform.toUpperCase()}
                          </Badge>
                        </div>
                        <p className="text-sm text-gray-600 mb-2">
                          Version {app.version} (Build {app.buildNumber})
                        </p>
                        <div className="flex items-center gap-4 text-sm text-gray-500">
                          <span>Size: {formatFileSize(app.fileSize)}</span>
                          <span>Downloads: {app.downloadCount || 0}</span>
                          <span className="flex items-center gap-1">
                            <Calendar className="h-3 w-3" />
                            {formatDate(app.createdAt)}
                          </span>
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex gap-2">
                      <Button
                        onClick={() => {
                          const link = document.createElement('a');
                          link.href = `${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000'}/api/app/download/${app.uploadId}`;
                          link.download = app.fileName;
                          link.click();
                        }}
                        variant="outline"
                        size="sm"
                      >
                        <Download className="h-4 w-4 mr-1" />
                        Download
                      </Button>
                      
                      <AlertDialog>
                        <AlertDialogTrigger asChild>
                          <Button
                            variant="destructive"
                            size="sm"
                            disabled={deleting === app.uploadId}
                          >
                            {deleting === app.uploadId ? (
                              <RefreshCw className="h-4 w-4 mr-1 animate-spin" />
                            ) : (
                              <Trash2 className="h-4 w-4 mr-1" />
                            )}
                            Delete
                          </Button>
                        </AlertDialogTrigger>
                        <AlertDialogContent>
                          <AlertDialogHeader>
                            <AlertDialogTitle>Delete App</AlertDialogTitle>
                            <AlertDialogDescription>
                              Are you sure you want to delete <strong>{app.appName}</strong> version {app.version}?
                              This action cannot be undone.
                            </AlertDialogDescription>
                          </AlertDialogHeader>
                          <AlertDialogFooter>
                            <AlertDialogCancel>Cancel</AlertDialogCancel>
                            <AlertDialogAction
                              onClick={() => handleDelete(app.uploadId, app.appName)}
                              className="bg-red-600 hover:bg-red-700"
                            >
                              Delete App
                            </AlertDialogAction>
                          </AlertDialogFooter>
                        </AlertDialogContent>
                      </AlertDialog>
                    </div>
                  </div>
                </Card>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Info Section */}
      <Alert>
        <CheckCircle className="h-4 w-4" />
        <AlertDescription>
          <strong>App Management:</strong> Upload new versions, download existing apps, and delete old versions. 
          The latest version will be available for public download.
        </AlertDescription>
      </Alert>
    </div>
  );
} 